(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[932],{5826:t=>{t.exports={style:{fontFamily:"'GeistSans', 'GeistSans Fallback'"},className:"__className_fb8f2c",variable:"__variable_fb8f2c"}},9658:t=>{t.exports={style:{fontFamily:"'GeistMono', ui-monospace, SFMono-Regular, Roboto Mono, Menlo, Monaco, Liberation Mono, DejaVu Sans Mono, Courier New, monospace"},className:"__className_f910ec",variable:"__variable_f910ec"}},44444:(t,e,s)=>{"use strict";s.d(e,{Cp:()=>w});let i="9.22.0",n=globalThis;function r(t){let e=t.__SENTRY__=t.__SENTRY__||{};return e.version=e.version||i,e[i]=e[i]||{}}function o(t,e,s=n){let r=s.__SENTRY__=s.__SENTRY__||{},a=r[i]=r[i]||{};return a[t]||(a[t]=e())}function a(t=n.crypto||n.msCrypto){let e=()=>16*Math.random();try{if(t?.randomUUID)return t.randomUUID().replace(/-/g,"");t?.getRandomValues&&(e=()=>{let e=new Uint8Array(1);return t.getRandomValues(e),e[0]})}catch(t){}return"10000000100040008000100000000000".replace(/[018]/g,t=>(t^(15&e())>>t/4).toString(16))}function c(){return Date.now()/1e3}let h=function(){let{performance:t}=n;if(!t?.now)return c;let e=Date.now()-t.now(),s=void 0==t.timeOrigin?e:t.timeOrigin;return()=>(s+t.now())/1e3}(),_="undefined"==typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__,u=["debug","info","warn","error","log","assert","trace"],l={},p=o("logger",function(){let t=!1,e={enable:()=>{t=!0},disable:()=>{t=!1},isEnabled:()=>t};return _?u.forEach(s=>{e[s]=(...e)=>{t&&function(t){if(!("console"in n))return t();let e=n.console,s={},i=Object.keys(l);i.forEach(t=>{let i=l[t];s[t]=e[t],e[t]=i});try{return t()}finally{i.forEach(t=>{e[t]=s[t]})}}(()=>{n.console[s](`Sentry Logger [${s}]:`,...e)})}}):u.forEach(t=>{e[t]=()=>void 0}),e}),d="_sentrySpan";function g(t,e){e?function(t,e,s){try{Object.defineProperty(t,e,{value:s,writable:!0,configurable:!0})}catch(s){_&&p.log(`Failed to add non-enumerable property "${e}" to object`,t)}}(t,d,e):delete t[d]}let f=Object.prototype.toString;class m{constructor(){this._notifyingListeners=!1,this._scopeListeners=[],this._eventProcessors=[],this._breadcrumbs=[],this._attachments=[],this._user={},this._tags={},this._extra={},this._contexts={},this._sdkProcessingMetadata={},this._propagationContext={traceId:a(),sampleRand:Math.random()}}clone(){let t=new m;return t._breadcrumbs=[...this._breadcrumbs],t._tags={...this._tags},t._extra={...this._extra},t._contexts={...this._contexts},this._contexts.flags&&(t._contexts.flags={values:[...this._contexts.flags.values]}),t._user=this._user,t._level=this._level,t._session=this._session,t._transactionName=this._transactionName,t._fingerprint=this._fingerprint,t._eventProcessors=[...this._eventProcessors],t._attachments=[...this._attachments],t._sdkProcessingMetadata={...this._sdkProcessingMetadata},t._propagationContext={...this._propagationContext},t._client=this._client,t._lastEventId=this._lastEventId,g(t,this[d]),t}setClient(t){this._client=t}setLastEventId(t){this._lastEventId=t}getClient(){return this._client}lastEventId(){return this._lastEventId}addScopeListener(t){this._scopeListeners.push(t)}addEventProcessor(t){return this._eventProcessors.push(t),this}setUser(t){return this._user=t||{email:void 0,id:void 0,ip_address:void 0,username:void 0},this._session&&function(t,e={}){if(e.user&&(!t.ipAddress&&e.user.ip_address&&(t.ipAddress=e.user.ip_address),t.did||e.did||(t.did=e.user.id||e.user.email||e.user.username)),t.timestamp=e.timestamp||h(),e.abnormal_mechanism&&(t.abnormal_mechanism=e.abnormal_mechanism),e.ignoreDuration&&(t.ignoreDuration=e.ignoreDuration),e.sid&&(t.sid=32===e.sid.length?e.sid:a()),void 0!==e.init&&(t.init=e.init),!t.did&&e.did&&(t.did=`${e.did}`),"number"==typeof e.started&&(t.started=e.started),t.ignoreDuration)t.duration=void 0;else if("number"==typeof e.duration)t.duration=e.duration;else{let e=t.timestamp-t.started;t.duration=e>=0?e:0}e.release&&(t.release=e.release),e.environment&&(t.environment=e.environment),!t.ipAddress&&e.ipAddress&&(t.ipAddress=e.ipAddress),!t.userAgent&&e.userAgent&&(t.userAgent=e.userAgent),"number"==typeof e.errors&&(t.errors=e.errors),e.status&&(t.status=e.status)}(this._session,{user:t}),this._notifyScopeListeners(),this}getUser(){return this._user}setTags(t){return this._tags={...this._tags,...t},this._notifyScopeListeners(),this}setTag(t,e){return this._tags={...this._tags,[t]:e},this._notifyScopeListeners(),this}setExtras(t){return this._extra={...this._extra,...t},this._notifyScopeListeners(),this}setExtra(t,e){return this._extra={...this._extra,[t]:e},this._notifyScopeListeners(),this}setFingerprint(t){return this._fingerprint=t,this._notifyScopeListeners(),this}setLevel(t){return this._level=t,this._notifyScopeListeners(),this}setTransactionName(t){return this._transactionName=t,this._notifyScopeListeners(),this}setContext(t,e){return null===e?delete this._contexts[t]:this._contexts[t]=e,this._notifyScopeListeners(),this}setSession(t){return t?this._session=t:delete this._session,this._notifyScopeListeners(),this}getSession(){return this._session}update(t){var e;if(!t)return this;let s="function"==typeof t?t(this):t,{tags:i,extra:n,user:r,contexts:o,level:a,fingerprint:c=[],propagationContext:h}=(s instanceof m?s.getScopeData():(e=s,"[object Object]"===f.call(e))?t:void 0)||{};return this._tags={...this._tags,...i},this._extra={...this._extra,...n},this._contexts={...this._contexts,...o},r&&Object.keys(r).length&&(this._user=r),a&&(this._level=a),c.length&&(this._fingerprint=c),h&&(this._propagationContext=h),this}clear(){return this._breadcrumbs=[],this._tags={},this._extra={},this._user={},this._contexts={},this._level=void 0,this._transactionName=void 0,this._fingerprint=void 0,this._session=void 0,g(this,void 0),this._attachments=[],this.setPropagationContext({traceId:a(),sampleRand:Math.random()}),this._notifyScopeListeners(),this}addBreadcrumb(t,e){let s="number"==typeof e?e:100;if(s<=0)return this;let i={timestamp:c(),...t,message:t.message?function(t,e=0){return"string"!=typeof t||0===e||t.length<=e?t:`${t.slice(0,e)}...`}(t.message,2048):t.message};return this._breadcrumbs.push(i),this._breadcrumbs.length>s&&(this._breadcrumbs=this._breadcrumbs.slice(-s),this._client?.recordDroppedEvent("buffer_overflow","log_item")),this._notifyScopeListeners(),this}getLastBreadcrumb(){return this._breadcrumbs[this._breadcrumbs.length-1]}clearBreadcrumbs(){return this._breadcrumbs=[],this._notifyScopeListeners(),this}addAttachment(t){return this._attachments.push(t),this}clearAttachments(){return this._attachments=[],this}getScopeData(){return{breadcrumbs:this._breadcrumbs,attachments:this._attachments,contexts:this._contexts,tags:this._tags,extra:this._extra,user:this._user,level:this._level,fingerprint:this._fingerprint||[],eventProcessors:this._eventProcessors,propagationContext:this._propagationContext,sdkProcessingMetadata:this._sdkProcessingMetadata,transactionName:this._transactionName,span:this[d]}}setSDKProcessingMetadata(t){return this._sdkProcessingMetadata=function t(e,s,i=2){if(!s||"object"!=typeof s||i<=0)return s;if(e&&0===Object.keys(s).length)return e;let n={...e};for(let e in s)Object.prototype.hasOwnProperty.call(s,e)&&(n[e]=t(n[e],s[e],i-1));return n}(this._sdkProcessingMetadata,t,2),this}setPropagationContext(t){return this._propagationContext=t,this}getPropagationContext(){return this._propagationContext}captureException(t,e){let s=e?.event_id||a();if(!this._client)return p.warn("No client configured on scope - will not capture exception!"),s;let i=Error("Sentry syntheticException");return this._client.captureException(t,{originalException:t,syntheticException:i,...e,event_id:s},this),s}captureMessage(t,e,s){let i=s?.event_id||a();if(!this._client)return p.warn("No client configured on scope - will not capture message!"),i;let n=Error(t);return this._client.captureMessage(t,e,{originalException:t,syntheticException:n,...s,event_id:i},this),i}captureEvent(t,e){let s=e?.event_id||a();return this._client?this._client.captureEvent(t,{...e,event_id:s},this):p.warn("No client configured on scope - will not capture event!"),s}_notifyScopeListeners(){this._notifyingListeners||(this._notifyingListeners=!0,this._scopeListeners.forEach(t=>{t(this)}),this._notifyingListeners=!1)}}class b{constructor(t,e){let s,i;s=t||new m,i=e||new m,this._stack=[{scope:s}],this._isolationScope=i}withScope(t){var e;let s,i=this._pushScope();try{s=t(i)}catch(t){throw this._popScope(),t}return(e=s,e?.then&&"function"==typeof e.then)?s.then(t=>(this._popScope(),t),t=>{throw this._popScope(),t}):(this._popScope(),s)}getClient(){return this.getStackTop().client}getScope(){return this.getStackTop().scope}getIsolationScope(){return this._isolationScope}getStackTop(){return this._stack[this._stack.length-1]}_pushScope(){let t=this.getScope().clone();return this._stack.push({client:this.getClient(),scope:t}),t}_popScope(){return!(this._stack.length<=1)&&!!this._stack.pop()}}function S(){let t=(r(n),n),e=r(t);return e.stack=e.stack||new b(o("defaultCurrentScope",()=>new m),o("defaultIsolationScope",()=>new m))}function v(t){return S().withScope(t)}function y(t,e){let s=S();return s.withScope(()=>(s.getStackTop().scope=t,e(t)))}function x(t){return S().withScope(()=>t(S().getIsolationScope()))}let E=["user","level","extra","contexts","tags","fingerprint","propagationContext"];function w(t,e){return(function(t){let e=r(t);return e.acs?e.acs:{withIsolationScope:x,withScope:v,withSetScope:y,withSetIsolationScope:(t,e)=>x(e),getCurrentScope:()=>S().getScope(),getIsolationScope:()=>S().getIsolationScope()}})((r(n),n)).getCurrentScope().captureException(t,function(t){if(t){var e;return(e=t)instanceof m||"function"==typeof e||Object.keys(t).some(t=>E.includes(t))?{captureContext:t}:t}}(e))}}}]);