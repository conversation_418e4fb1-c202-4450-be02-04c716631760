{"version": 3, "middleware": {"/": {"files": ["server/edge-instrumentation.js", "server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|images|ingest|favicon.ico).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|images|ingest|favicon.ico).*)"}], "wasm": [{"name": "wasm_b588c0a55cd623de777dec94364d042f0b24ce2e", "filePath": "server/edge-chunks/wasm_b588c0a55cd623de777dec94364d042f0b24ce2e.wasm"}, {"name": "wasm_dfcb969881c33fe5ce4843150e020f53d9929c38", "filePath": "server/edge-chunks/wasm_dfcb969881c33fe5ce4843150e020f53d9929c38.wasm"}, {"name": "wasm_29af6355eac3f6c4172b8923030fe40be96f02f2", "filePath": "server/edge-chunks/wasm_29af6355eac3f6c4172b8923030fe40be96f02f2.wasm"}], "assets": [], "env": {"__NEXT_BUILD_ID": "OZKey0xO2SaNdmXHUUo8-", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "QCRWns5SM85b4FnujfvT4L9FX8j3qpuvF4CN+mGhfW0=", "__NEXT_PREVIEW_MODE_ID": "87d9d3082a8f42cdf2c2d416e523cd09", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "83e8ea6aeb745ce8e9bfb143ae513abaae90050f88b020fd5f8c895b69695560", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "cf44a60699ce7bdff458172dd94d62c7b646449df185130dd885187480fb29db"}}}, "functions": {}, "sortedMiddleware": ["/"]}