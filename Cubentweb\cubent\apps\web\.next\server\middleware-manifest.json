{"version": 3, "middleware": {"/": {"files": ["server/edge-instrumentation.js", "server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|images|ingest|favicon.ico).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|images|ingest|favicon.ico).*)"}], "wasm": [{"name": "wasm_b588c0a55cd623de777dec94364d042f0b24ce2e", "filePath": "server/edge-chunks/wasm_b588c0a55cd623de777dec94364d042f0b24ce2e.wasm"}, {"name": "wasm_dfcb969881c33fe5ce4843150e020f53d9929c38", "filePath": "server/edge-chunks/wasm_dfcb969881c33fe5ce4843150e020f53d9929c38.wasm"}, {"name": "wasm_29af6355eac3f6c4172b8923030fe40be96f02f2", "filePath": "server/edge-chunks/wasm_29af6355eac3f6c4172b8923030fe40be96f02f2.wasm"}], "assets": [], "env": {"__NEXT_BUILD_ID": "8hXVjGpWYKiYKyn1N6QzG", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "QCRWns5SM85b4FnujfvT4L9FX8j3qpuvF4CN+mGhfW0=", "__NEXT_PREVIEW_MODE_ID": "a377f72ac4d8894c3c2e7b5b59249b3f", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "f1e00cd017e6c75aa79ab6f4c99793d9dc9ee72b9aee72c5e7df24ed2f7bca4e", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "ed9fb163f9802c9de9dfbca518ff4a94635391f16a05189624a50aacb852c9ac"}}}, "functions": {}, "sortedMiddleware": ["/"]}