{"version": 3, "middleware": {"/": {"files": ["server/edge-instrumentation.js", "server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|images|ingest|favicon.ico).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|images|ingest|favicon.ico).*)"}], "wasm": [{"name": "wasm_b588c0a55cd623de777dec94364d042f0b24ce2e", "filePath": "server/edge-chunks/wasm_b588c0a55cd623de777dec94364d042f0b24ce2e.wasm"}, {"name": "wasm_dfcb969881c33fe5ce4843150e020f53d9929c38", "filePath": "server/edge-chunks/wasm_dfcb969881c33fe5ce4843150e020f53d9929c38.wasm"}, {"name": "wasm_29af6355eac3f6c4172b8923030fe40be96f02f2", "filePath": "server/edge-chunks/wasm_29af6355eac3f6c4172b8923030fe40be96f02f2.wasm"}], "assets": [], "env": {"__NEXT_BUILD_ID": "nQ96gHfbmzI2fJoSfsMz8", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "QCRWns5SM85b4FnujfvT4L9FX8j3qpuvF4CN+mGhfW0=", "__NEXT_PREVIEW_MODE_ID": "3c3f259114005a5ff6a3f89d6cfc695c", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "0d8c8e771cf631bfc71b05b66bf0d331cb7779b44b8b52650b993c88fb69879b", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "a2b4dd3f3e448429e0bf8732cafaf298b55202526443691ee14815cd4eff27a7"}}}, "functions": {}, "sortedMiddleware": ["/"]}