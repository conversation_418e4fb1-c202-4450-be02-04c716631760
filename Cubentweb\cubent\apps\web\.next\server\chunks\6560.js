"use strict";exports.id=6560,exports.ids=[6560],exports.modules={56560:(e,r,t)=>{t.r(r),t.d(r,{hasSrcAppDir:()=>n,suggestMiddlewareLocation:()=>c});var s=Object.getOwnPropertyNames;let o=((e,r)=>function(){return r||(0,e[s(e)[0]])((r={exports:{}}).exports,r),r.exports})({"src/runtime/node/safe-node-apis.js"(e,r){let{existsSync:s,writeFileSync:o,readFileSync:i,appendFileSync:a,mkdirSync:p,rmSync:d}=t(73024);r.exports={fs:{existsSync:s,writeFileSync:o,readFileSync:i,appendFileSync:a,mkdirSync:p,rmSync:d},path:t(76760),cwd:()=>process.cwd()}}})(),i=e=>{throw Error(`Clerk: ${e} is missing. This is an internal error. Please contact Clerk's support.`)},a=()=>(o.fs||i("fs"),o.fs),p=()=>(o.path||i("path"),o.path),d=()=>(o.cwd||i("cwd"),o.cwd);function n(){let{existsSync:e}=a(),r=p(),t=d();return!!e(r.join(t(),"src","app"))}function c(){let e=["ts","js"],r=(e,r,t)=>`Clerk: clerkMiddleware() was not run, your middleware file might be misplaced. Move your middleware file to ./${r}middleware.${e}. Currently located at ./${t}middleware.${e}`,{existsSync:t}=a(),s=p(),o=d(),i=s.join(o(),"src","app"),n=s.join(o(),"app"),c=(o,i,a)=>{for(let p of e)if(t(s.join(o,`middleware.${p}`)))return r(p,i,a)};return t(i)?c(i,"src/","src/app/")||c(o(),"src/",""):t(n)?c(n,"","app/"):void 0}}};