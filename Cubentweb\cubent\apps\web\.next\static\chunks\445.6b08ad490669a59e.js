"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[445],{14445:(e,l,s)=>{s.r(l),s.d(l,{KeylessCreatorOrReader:()=>r});var a=s(35062),n=s(50628),t=s(70988);let i=(0,t.createServerReference)("7fafdab18e4336438fc499c2c4e643a9436e2a9e02",t.callServer,void 0,t.findSourceMapURL,"createOrReadKeylessAction"),r=e=>{var l;let{children:s}=e,t=(null==(l=(0,a.useSelectedLayoutSegments)()[0])?void 0:l.startsWith("/_not-found"))||!1,[r,c]=n.useActionState(i,null);return((0,n.useEffect)(()=>{t||n.startTransition(()=>{c()})},[t]),n.isValidElement(s))?n.cloneElement(s,{key:null==r?void 0:r.publishableKey,publishableKey:null==r?void 0:r.publishableKey,__internal_keyless_claimKeylessApplicationUrl:null==r?void 0:r.claimUrl,__internal_keyless_copyInstanceKeysUrl:null==r?void 0:r.apiKeysUrl,__internal_bypassMissingPublishableKey:!0}):s}}}]);