"use strict";exports.id=2454,exports.ids=[2454],exports.modules={52454:(e,l,s)=>{s.r(l),s.d(l,{KeylessCreatorOrReader:()=>o});var t=s(53172),a=s(35371),r=s.n(a),i=s(96242);let n=(0,i.createServerReference)("7fafdab18e4336438fc499c2c4e643a9436e2a9e02",i.callServer,void 0,i.findSourceMapURL,"createOrReadKeylessAction"),o=e=>{var l;let{children:s}=e,i=(null==(l=(0,t.useSelectedLayoutSegments)()[0])?void 0:l.startsWith("/_not-found"))||!1,[o,c]=r().useActionState(n,null);return((0,a.useEffect)(()=>{i||r().startTransition(()=>{c()})},[i]),r().isValidElement(s))?r().cloneElement(s,{key:null==o?void 0:o.publishableKey,publishableKey:null==o?void 0:o.publishableKey,__internal_keyless_claimKeylessApplicationUrl:null==o?void 0:o.claimUrl,__internal_keyless_copyInstanceKeysUrl:null==o?void 0:o.apiKeysUrl,__internal_bypassMissingPublishableKey:!0}):s}}};