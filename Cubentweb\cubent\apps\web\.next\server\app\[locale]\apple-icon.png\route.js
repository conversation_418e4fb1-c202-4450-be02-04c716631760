"use strict";(()=>{var e={};e.id=5303,e.ids=[5303],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},26142:(e,r,t)=>{e.exports=t(44870)},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64636:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>U,routeModule:()=>l,serverHooks:()=>x,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>d});var n={};t.r(n),t.d(n,{GET:()=>i,dynamic:()=>u});var a=t(26142),o=t(94327),s=t(34862),p=t(26239);let A=Buffer.from("iVBORw0KGgoAAAANSUhEUgAAAMAAAADABAMAAACg8nE0AAAAGFBMVEX6+vrq6uqcnJx9fX0AAAAvLy/b29sgICAt9ZYuAAAAe0lEQVR4Ae3PQQnDQBAAwIVC36UOegr6iINsBERFDMQ/ERBuIex3xsEEMwAAwHfUPtH0ytoeXUuWNgUFBQUFBQWFhxQUFBQUFBQUFBQUFBQUFI6s/aPpfWZpjQ6FOwUFBQUFBQUFBQUFBQUFBQUFBQUFBYVR+8UEAADABUvDSqyHOPdDAAAAAElFTkSuQmCC","base64");function i(){return new p.NextResponse(A,{headers:{"Content-Type":"image/png","Cache-Control":"public, immutable, no-transform, max-age=31536000"}})}let u="force-static",l=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/[locale]/apple-icon.png/route",pathname:"/[locale]/apple-icon.png",filename:"apple-icon",bundlePath:"app/[locale]/apple-icon.png/route"},resolvedPagePath:"next-metadata-route-loader?filePath=C%3A%5CUsers%5Canwar%5CDocuments%5C2%20FOLDERS%20FOR%20CUBENT%5CCubentweb%5Ccubent%5Capps%5Cweb%5Capp%5C%5Blocale%5D%5Capple-icon.png&isDynamicRouteExtension=0!?__next_metadata_route__",nextConfigOutput:"",userland:n}),{workAsyncStorage:c,workUnitAsyncStorage:d,serverHooks:x}=l;function U(){return(0,s.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:d})}}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[5319,6239],()=>t(64636));module.exports=n})();