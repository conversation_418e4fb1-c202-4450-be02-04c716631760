(()=>{var e={};e.id=2285,e.ids=[2285],e.modules={1646:(e,r,t)=>{"use strict";t.d(r,{n:()=>s});let s=t(14251).sD},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3835:(e,r,t)=>{"use strict";t.d(r,{B:()=>a});var s=t(94752),i=t(48384);let a=async({date:e,readingTime:r,tags:t,toc:a})=>(0,s.jsxs)("div",{className:"col-span-4 flex w-72 flex-col items-start gap-8 border-foreground/10 border-l px-6 lg:col-span-2",children:[(0,s.jsxs)("div",{className:"grid gap-2",children:[(0,s.jsx)("p",{className:"text-muted-foreground text-sm",children:"Published"}),(0,s.jsx)("p",{className:"rounded-sm text-foreground text-sm",children:new Intl.DateTimeFormat("en-US",{month:"short",day:"numeric",year:"numeric",timeZone:"America/New_York"}).format(e)})]}),(0,s.jsxs)("div",{className:"grid gap-2",children:[(0,s.jsx)("p",{className:"text-muted-foreground text-sm",children:"Reading Time"}),(0,s.jsx)("p",{className:"rounded-sm text-foreground text-sm",children:r})]}),t&&(0,s.jsxs)("div",{className:"grid gap-2",children:[(0,s.jsx)("p",{className:"text-muted-foreground text-sm",children:"Tags"}),(0,s.jsx)("p",{className:"rounded-sm text-foreground text-sm",children:t.map(i.Z).join(", ")})]}),a?(0,s.jsx)("div",{className:"-mx-2",children:(0,s.jsxs)("div",{className:"grid gap-2 p-2",children:[(0,s.jsx)("p",{className:"text-muted-foreground text-sm",children:"Sections"}),a]})}):void 0]})},8086:e=>{"use strict";e.exports=require("module")},8924:(e,r,t)=>{"use strict";t.r(r),t.d(r,{$$RSC_SERVER_ACTION_0:()=>g,default:()=>f,generateMetadata:()=>h,generateStaticParams:()=>b});var s=t(94752),i=t(52661);t(37091);var a=t(3835),n=t(71844),o=t(23055),l=t(1646),d=t(17218),c=t(34962),u=t(56450),p=t(49499),x=t.n(p),m=t(62923);let h=async({params:e})=>{let{slug:r}=await e,t=await o.a.getPost(r);return t?(0,u.w)({title:t._title,description:t.description}):{}},b=async()=>(await o.a.getPosts()).map(({_slug:e})=>({slug:e})),g=async function([e]){let r=e.legalPages.item;return r||(0,m.notFound)(),(0,s.jsxs)("div",{className:"container max-w-5xl py-16",children:[(0,s.jsxs)(x(),{className:"mb-4 inline-flex items-center gap-1 text-muted-foreground text-sm focus:underline focus:outline-none",href:"/",children:[(0,s.jsx)(n.A60,{className:"h-4 w-4"}),"Back to Home"]}),(0,s.jsx)("h1",{className:"scroll-m-20 text-balance font-extrabold text-4xl tracking-tight lg:text-5xl",children:r._title}),(0,s.jsx)("p",{className:"text-balance leading-7 [&:not(:first-child)]:mt-6",children:r.description}),(0,s.jsxs)("div",{className:"mt-16 flex flex-col items-start gap-8 sm:flex-row",children:[(0,s.jsx)("div",{className:"sm:flex-1",children:(0,s.jsx)("div",{className:"prose prose-neutral dark:prose-invert",children:(0,s.jsx)(l.n,{content:r.body.json.content})})}),(0,s.jsx)("div",{className:"sticky top-24 hidden shrink-0 md:block",children:(0,s.jsx)(a.B,{toc:(0,s.jsx)(c.M,{data:r.body.json.toc}),readingTime:`${r.body.readingTime} min read`,date:new Date})})]})]})},f=async({params:e})=>{let{slug:r}=await e;return(0,s.jsx)(d.J,{queries:[o.a.postQuery(r)],children:(0,i.A)(g,"40014fd0d2df04cb5e33a1ee84d7eb881e8078a057",null)})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10970:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=t(57864),i=t(94327),a=t(73391),n=t.n(a),o=t(17984),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(r,l);let d={children:["",{children:["[locale]",{children:["legal",{children:["[slug]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,8924)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\web\\app\\[locale]\\legal\\[slug]\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,13141)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\web\\app\\[locale]\\legal\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,15190))).default(e)],apple:[async e=>(await Promise.resolve().then(t.bind(t,7820))).default(e)],openGraph:[async e=>(await Promise.resolve().then(t.bind(t,39440))).default(e)],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,37919)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\web\\app\\[locale]\\layout.tsx"],"global-error":[()=>Promise.resolve().then(t.bind(t,84641)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\web\\app\\[locale]\\global-error.tsx"]}]},{"not-found":[()=>Promise.resolve().then(t.t.bind(t,47837,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,47168,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,52945,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\web\\app\\[locale]\\legal\\[slug]\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/[locale]/legal/[slug]/page",pathname:"/[locale]/legal/[slug]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},13141:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(94752),i=t(77021);let a=({children:e})=>(0,s.jsxs)(s.Fragment,{children:[e,(0,s.jsx)(i.M,{})]})},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{"use strict";e.exports=require("node:child_process")},33873:e=>{"use strict";e.exports=require("path")},34962:(e,r,t)=>{"use strict";t.d(r,{M:()=>a});var s=t(94752),i=t(14251);let a=({data:e,...r})=>(0,s.jsx)("div",{children:(0,s.jsx)(i.sD,{components:{ol:({children:e})=>(0,s.jsx)("ol",{className:"flex list-none flex-col gap-2 text-sm",children:e}),ul:({children:e})=>(0,s.jsx)("ul",{className:"flex list-none flex-col gap-2 text-sm",children:e}),li:({children:e})=>(0,s.jsx)("li",{className:"pl-3",children:e}),a:({children:e,href:r})=>(0,s.jsx)("a",{className:"line-clamp-3 flex rounded-sm text-foreground text-sm underline decoration-foreground/0 transition-colors hover:decoration-foreground/50",href:`#${r?.split("#").at(1)}`,children:e})},...r,children:e})})},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},37067:e=>{"use strict";e.exports=require("node:http")},38522:e=>{"use strict";e.exports=require("node:zlib")},40635:(e,r,t)=>{Promise.resolve().then(t.bind(t,86332)),Promise.resolve().then(t.t.bind(t,41265,23)),Promise.resolve().then(t.bind(t,22683)),Promise.resolve().then(t.bind(t,38327))},41692:e=>{"use strict";e.exports=require("node:tls")},42152:e=>{"use strict";e.exports=require("process")},43291:(e,r,t)=>{Promise.resolve().then(t.bind(t,79047))},44708:e=>{"use strict";e.exports=require("node:https")},48161:e=>{"use strict";e.exports=require("node:os")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},56450:(e,r,t)=>{"use strict";t.d(r,{w:()=>l});var s=t(81121),i=t.n(s);let a="next-forge",n={name:"Vercel",url:"https://vercel.com/"},o=process.env.VERCEL_PROJECT_PRODUCTION_URL,l=({title:e,description:r,image:t,...s})=>{let l=`${e} | ${a}`,d={title:l,description:r,applicationName:a,metadataBase:o?new URL(`https://${o}`):void 0,authors:[n],creator:n.name,formatDetection:{telephone:!1},appleWebApp:{capable:!0,statusBarStyle:"default",title:l},openGraph:{title:l,description:r,type:"website",siteName:a,locale:"en_US"},publisher:"Vercel",twitter:{card:"summary_large_image",creator:"@vercel"}},c=i()(d,s);return t&&c.openGraph&&(c.openGraph.images=[{url:t,width:1200,height:630,alt:e}]),c}},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{"use strict";e.exports=require("node:fs")},73566:e=>{"use strict";e.exports=require("worker_threads")},74075:e=>{"use strict";e.exports=require("zlib")},74979:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,21034,23)),Promise.resolve().then(t.t.bind(t,49499,23)),Promise.resolve().then(t.bind(t,93665)),Promise.resolve().then(t.bind(t,40356))},74998:e=>{"use strict";e.exports=require("perf_hooks")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},77635:(e,r,t)=>{Promise.resolve().then(t.bind(t,43401))},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},80481:e=>{"use strict";e.exports=require("node:readline")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},86592:e=>{"use strict";e.exports=require("node:inspector")},92327:(e,r,t)=>{"use strict";t.r(r),t.d(r,{"00e578e70edcb55dedc8be97f84cdf9c4ce9b9fbb2":()=>s.z2,"40014fd0d2df04cb5e33a1ee84d7eb881e8078a057":()=>a.$$RSC_SERVER_ACTION_0,"405efdbdcc87c20941759ae1bb1e4f3629f6226400":()=>i.x,"6040ce2583939275b9b79a23c761210186958f33ca":()=>s.qr,"60ac4068834bf9e1b32327478a0425a5daded1d553":()=>s.xK,"60d7e646f4b8f4f2b31c38fcbc9963402a4f1620ad":()=>s.q2});var s=t(22589),i=t(18362),a=t(8924)},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[5319,3396,415,2644,6784,1121,5954,9752,4788],()=>t(10970));module.exports=s})();