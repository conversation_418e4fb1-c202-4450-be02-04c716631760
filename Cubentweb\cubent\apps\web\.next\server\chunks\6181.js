"use strict";exports.id=6181,exports.ids=[6181],exports.modules={86181:(e,t,n)=>{n.r(t),n.d(t,{default:()=>a});var a=[Object.freeze({displayName:"TSX",name:"tsx",patterns:[{include:"#directives"},{include:"#statements"},{include:"#shebang"}],repository:{"access-modifier":{match:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(abstract|declare|override|public|protected|private|readonly|static)(?![_$0-9A-Za-z])(?:(?=\\.\\.\\.)|(?!\\.))",name:"storage.modifier.tsx"},"after-operator-block-as-object-literal":{begin:"(?<!\\+\\+|--)(?<=[:=(,\\[?+!>]|^await|[^\\._$0-9A-Za-z]await|^return|[^\\._$0-9A-Za-z]return|^yield|[^\\._$0-9A-Za-z]yield|^throw|[^\\._$0-9A-Za-z]throw|^in|[^\\._$0-9A-Za-z]in|^of|[^\\._$0-9A-Za-z]of|^typeof|[^\\._$0-9A-Za-z]typeof|&&|\\|\\||\\*)\\s*(\\{)",beginCaptures:{1:{name:"punctuation.definition.block.tsx"}},end:"\\}",endCaptures:{0:{name:"punctuation.definition.block.tsx"}},name:"meta.objectliteral.tsx",patterns:[{include:"#object-member"}]},"array-binding-pattern":{begin:"(?:(\\.\\.\\.)\\s*)?(\\[)",beginCaptures:{1:{name:"keyword.operator.rest.tsx"},2:{name:"punctuation.definition.binding-pattern.array.tsx"}},end:"\\]",endCaptures:{0:{name:"punctuation.definition.binding-pattern.array.tsx"}},patterns:[{include:"#binding-element"},{include:"#punctuation-comma"}]},"array-binding-pattern-const":{begin:"(?:(\\.\\.\\.)\\s*)?(\\[)",beginCaptures:{1:{name:"keyword.operator.rest.tsx"},2:{name:"punctuation.definition.binding-pattern.array.tsx"}},end:"\\]",endCaptures:{0:{name:"punctuation.definition.binding-pattern.array.tsx"}},patterns:[{include:"#binding-element-const"},{include:"#punctuation-comma"}]},"array-literal":{begin:"\\s*(\\[)",beginCaptures:{1:{name:"meta.brace.square.tsx"}},end:"\\]",endCaptures:{0:{name:"meta.brace.square.tsx"}},name:"meta.array.literal.tsx",patterns:[{include:"#expression"},{include:"#punctuation-comma"}]},"arrow-function":{patterns:[{captures:{1:{name:"storage.modifier.async.tsx"},2:{name:"variable.parameter.tsx"}},match:"(?:(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(\\basync)\\s+)?([_$A-Za-z][_$0-9A-Za-z]*)\\s*(?==>)",name:"meta.arrow.tsx"},{begin:"(?:(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(\\basync))?((?<![})!\\]])\\s*(?=((<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<]|<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<]|<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<])*>)*>)*>\\s*)?[(]\\s*(\\/\\*([^\\*]|(\\*[^\\/]))*\\*\\/\\s*)*(([)]\\s*:)|((\\.\\.\\.\\s*)?[_$A-Za-z][_$0-9A-Za-z]*\\s*:)))|([<]\\s*[_$A-Za-z][_$0-9A-Za-z]*\\s+extends\\s*[^=>])|((<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<]|<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<]|<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<])*>)*>)*>\\s*)?\\(\\s*(\\/\\*([^\\*]|(\\*[^\\/]))*\\*\\/\\s*)*(([_$A-Za-z]|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\])|(\\.\\.\\.\\s*[_$A-Za-z]))([^()\\'\\\"\\`]|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\'([^\\'\\\\]|\\\\.)*\\')|(\\\"([^\\\"\\\\]|\\\\.)*\\\")|(\\`([^\\`\\\\]|\\\\.)*\\`))*)?\\)(\\s*:\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\([^()]+\\)|\\{[^{}]+\\})+)?\\s*=>)))",beginCaptures:{1:{name:"storage.modifier.async.tsx"}},end:"(?==>|\\{|(^\\s*(export|function|class|interface|let|var|(?:\\busing(?=\\s+(?!in\\b|of\\b(?!\\s*(?:of\\b|=)))[_$A-Za-z])\\b)|(?:\\bawait\\s+(?:\\busing(?=\\s+(?!in\\b|of\\b(?!\\s*(?:of\\b|=)))[_$A-Za-z])\\b)\\b)|const|import|enum|namespace|module|type|abstract|declare)\\s+))",name:"meta.arrow.tsx",patterns:[{include:"#comment"},{include:"#type-parameters"},{include:"#function-parameters"},{include:"#arrow-return-type"},{include:"#possibly-arrow-return-type"}]},{begin:"=>",beginCaptures:{0:{name:"storage.type.function.arrow.tsx"}},end:"((?<=\\}|\\S)(?<!=>)|((?!\\{)(?=\\S)))(?!\\/[\\/\\*])",name:"meta.arrow.tsx",patterns:[{include:"#single-line-comment-consuming-line-ending"},{include:"#decl-block"},{include:"#expression"}]}]},"arrow-return-type":{begin:"(?<=\\))\\s*(:)",beginCaptures:{1:{name:"keyword.operator.type.annotation.tsx"}},end:"(?==>|\\{|(^\\s*(export|function|class|interface|let|var|(?:\\busing(?=\\s+(?!in\\b|of\\b(?!\\s*(?:of\\b|=)))[_$A-Za-z])\\b)|(?:\\bawait\\s+(?:\\busing(?=\\s+(?!in\\b|of\\b(?!\\s*(?:of\\b|=)))[_$A-Za-z])\\b)\\b)|const|import|enum|namespace|module|type|abstract|declare)\\s+))",name:"meta.return.type.arrow.tsx",patterns:[{include:"#arrow-return-type-body"}]},"arrow-return-type-body":{patterns:[{begin:"(?<=[:])(?=\\s*\\{)",end:"(?<=\\})",patterns:[{include:"#type-object"}]},{include:"#type-predicate-operator"},{include:"#type"}]},"async-modifier":{match:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(async)(?![_$0-9A-Za-z])(?:(?=\\.\\.\\.)|(?!\\.))",name:"storage.modifier.async.tsx"},"binding-element":{patterns:[{include:"#comment"},{include:"#string"},{include:"#numeric-literal"},{include:"#regex"},{include:"#object-binding-pattern"},{include:"#array-binding-pattern"},{include:"#destructuring-variable-rest"},{include:"#variable-initializer"}]},"binding-element-const":{patterns:[{include:"#comment"},{include:"#string"},{include:"#numeric-literal"},{include:"#regex"},{include:"#object-binding-pattern-const"},{include:"#array-binding-pattern-const"},{include:"#destructuring-variable-rest-const"},{include:"#variable-initializer"}]},"boolean-literal":{patterns:[{match:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))true(?![_$0-9A-Za-z])(?:(?=\\.\\.\\.)|(?!\\.))",name:"constant.language.boolean.true.tsx"},{match:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))false(?![_$0-9A-Za-z])(?:(?=\\.\\.\\.)|(?!\\.))",name:"constant.language.boolean.false.tsx"}]},brackets:{patterns:[{begin:"{",end:"}|(?=\\*/)",patterns:[{include:"#brackets"}]},{begin:"\\[",end:"\\]|(?=\\*/)",patterns:[{include:"#brackets"}]}]},cast:{patterns:[{include:"#jsx"}]},"class-declaration":{begin:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(?:(\\bexport)\\s+)?(?:(\\bdeclare)\\s+)?\\b(?:(abstract)\\s+)?\\b(class)\\b(?=\\s+|/[/*])",beginCaptures:{1:{name:"keyword.control.export.tsx"},2:{name:"storage.modifier.tsx"},3:{name:"storage.modifier.tsx"},4:{name:"storage.type.class.tsx"}},end:"(?<=\\})",name:"meta.class.tsx",patterns:[{include:"#class-declaration-or-expression-patterns"}]},"class-declaration-or-expression-patterns":{patterns:[{include:"#comment"},{include:"#class-or-interface-heritage"},{captures:{0:{name:"entity.name.type.class.tsx"}},match:"[_$A-Za-z][_$0-9A-Za-z]*"},{include:"#type-parameters"},{include:"#class-or-interface-body"}]},"class-expression":{begin:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(?:(abstract)\\s+)?(class)\\b(?=\\s+|[<{]|\\/[\\/*])",beginCaptures:{1:{name:"storage.modifier.tsx"},2:{name:"storage.type.class.tsx"}},end:"(?<=\\})",name:"meta.class.tsx",patterns:[{include:"#class-declaration-or-expression-patterns"}]},"class-or-interface-body":{begin:"\\{",beginCaptures:{0:{name:"punctuation.definition.block.tsx"}},end:"\\}",endCaptures:{0:{name:"punctuation.definition.block.tsx"}},patterns:[{include:"#comment"},{include:"#decorator"},{begin:"(?<=:)\\s*",end:"(?=\\s|[;),}\\]:\\-+]|;|^\\s*$|(?:^\\s*(?:abstract|async|(?:\\bawait\\s+(?:\\busing(?=\\s+(?!in\\b|of\\b(?!\\s*(?:of\\b|=)))[_$A-Za-z])\\b)\\b)|break|case|catch|class|const|continue|declare|do|else|enum|export|finally|function|for|goto|if|import|interface|let|module|namespace|switch|return|throw|try|type|(?:\\busing(?=\\s+(?!in\\b|of\\b(?!\\s*(?:of\\b|=)))[_$A-Za-z])\\b)|var|while)\\b))",patterns:[{include:"#expression"}]},{include:"#method-declaration"},{include:"#indexer-declaration"},{include:"#field-declaration"},{include:"#string"},{include:"#type-annotation"},{include:"#variable-initializer"},{include:"#access-modifier"},{include:"#property-accessor"},{include:"#async-modifier"},{include:"#after-operator-block-as-object-literal"},{include:"#decl-block"},{include:"#expression"},{include:"#punctuation-comma"},{include:"#punctuation-semicolon"}]},"class-or-interface-heritage":{begin:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(?:\\b(extends|implements)\\b)(?![_$0-9A-Za-z])(?:(?=\\.\\.\\.)|(?!\\.))",beginCaptures:{1:{name:"storage.modifier.tsx"}},end:"(?=\\{)",patterns:[{include:"#comment"},{include:"#class-or-interface-heritage"},{include:"#type-parameters"},{include:"#expressionWithoutIdentifiers"},{captures:{1:{name:"entity.name.type.module.tsx"},2:{name:"punctuation.accessor.tsx"},3:{name:"punctuation.accessor.optional.tsx"}},match:"([_$A-Za-z][_$0-9A-Za-z]*)\\s*(?:(\\.)|(\\?\\.(?!\\s*[\\d])))(?=\\s*[_$A-Za-z][_$0-9A-Za-z]*(\\s*\\??\\.\\s*[_$A-Za-z][_$0-9A-Za-z]*)*\\s*)"},{captures:{1:{name:"entity.other.inherited-class.tsx"}},match:"([_$A-Za-z][_$0-9A-Za-z]*)"},{include:"#expressionPunctuations"}]},comment:{patterns:[{begin:"/\\*\\*(?!/)",beginCaptures:{0:{name:"punctuation.definition.comment.tsx"}},end:"\\*/",endCaptures:{0:{name:"punctuation.definition.comment.tsx"}},name:"comment.block.documentation.tsx",patterns:[{include:"#docblock"}]},{begin:"(/\\*)(?:\\s*((@)internal)(?=\\s|(\\*/)))?",beginCaptures:{1:{name:"punctuation.definition.comment.tsx"},2:{name:"storage.type.internaldeclaration.tsx"},3:{name:"punctuation.decorator.internaldeclaration.tsx"}},end:"\\*/",endCaptures:{0:{name:"punctuation.definition.comment.tsx"}},name:"comment.block.tsx"},{begin:"(^[ \\t]+)?((//)(?:\\s*((@)internal)(?=\\s|$))?)",beginCaptures:{1:{name:"punctuation.whitespace.comment.leading.tsx"},2:{name:"comment.line.double-slash.tsx"},3:{name:"punctuation.definition.comment.tsx"},4:{name:"storage.type.internaldeclaration.tsx"},5:{name:"punctuation.decorator.internaldeclaration.tsx"}},contentName:"comment.line.double-slash.tsx",end:"(?=$)"}]},"control-statement":{patterns:[{include:"#switch-statement"},{include:"#for-loop"},{match:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(catch|finally|throw|try)(?![_$0-9A-Za-z])(?:(?=\\.\\.\\.)|(?!\\.))",name:"keyword.control.trycatch.tsx"},{captures:{1:{name:"keyword.control.loop.tsx"},2:{name:"entity.name.label.tsx"}},match:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(break|continue|goto)\\s+([_$A-Za-z][_$0-9A-Za-z]*)(?![_$0-9A-Za-z])(?:(?=\\.\\.\\.)|(?!\\.))"},{match:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(break|continue|do|goto|while)(?![_$0-9A-Za-z])(?:(?=\\.\\.\\.)|(?!\\.))",name:"keyword.control.loop.tsx"},{begin:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(return)(?![_$0-9A-Za-z])(?:(?=\\.\\.\\.)|(?!\\.))",beginCaptures:{0:{name:"keyword.control.flow.tsx"}},end:"(?=[;}]|$|;|^\\s*$|(?:^\\s*(?:abstract|async|(?:\\bawait\\s+(?:\\busing(?=\\s+(?!in\\b|of\\b(?!\\s*(?:of\\b|=)))[_$A-Za-z])\\b)\\b)|break|case|catch|class|const|continue|declare|do|else|enum|export|finally|function|for|goto|if|import|interface|let|module|namespace|switch|return|throw|try|type|(?:\\busing(?=\\s+(?!in\\b|of\\b(?!\\s*(?:of\\b|=)))[_$A-Za-z])\\b)|var|while)\\b))",patterns:[{include:"#expression"}]},{match:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(case|default|switch)(?![_$0-9A-Za-z])(?:(?=\\.\\.\\.)|(?!\\.))",name:"keyword.control.switch.tsx"},{include:"#if-statement"},{match:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(else|if)(?![_$0-9A-Za-z])(?:(?=\\.\\.\\.)|(?!\\.))",name:"keyword.control.conditional.tsx"},{match:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(with)(?![_$0-9A-Za-z])(?:(?=\\.\\.\\.)|(?!\\.))",name:"keyword.control.with.tsx"},{match:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(package)(?![_$0-9A-Za-z])(?:(?=\\.\\.\\.)|(?!\\.))",name:"keyword.control.tsx"},{match:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(debugger)(?![_$0-9A-Za-z])(?:(?=\\.\\.\\.)|(?!\\.))",name:"keyword.other.debugger.tsx"}]},"decl-block":{begin:"\\{",beginCaptures:{0:{name:"punctuation.definition.block.tsx"}},end:"\\}",endCaptures:{0:{name:"punctuation.definition.block.tsx"}},name:"meta.block.tsx",patterns:[{include:"#statements"}]},declaration:{patterns:[{include:"#decorator"},{include:"#var-expr"},{include:"#function-declaration"},{include:"#class-declaration"},{include:"#interface-declaration"},{include:"#enum-declaration"},{include:"#namespace-declaration"},{include:"#type-alias-declaration"},{include:"#import-equals-declaration"},{include:"#import-declaration"},{include:"#export-declaration"},{match:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(declare|export)(?![_$0-9A-Za-z])(?:(?=\\.\\.\\.)|(?!\\.))",name:"storage.modifier.tsx"}]},decorator:{begin:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))\\@",beginCaptures:{0:{name:"punctuation.decorator.tsx"}},end:"(?=\\s)",name:"meta.decorator.tsx",patterns:[{include:"#expression"}]},"destructuring-const":{patterns:[{begin:"(?<!=|:|^of|[^\\._$0-9A-Za-z]of|^in|[^\\._$0-9A-Za-z]in)\\s*(?=\\{)",end:"(?=$|^|[;,=}]|((?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(of|in)\\s+))",name:"meta.object-binding-pattern-variable.tsx",patterns:[{include:"#object-binding-pattern-const"},{include:"#type-annotation"},{include:"#comment"}]},{begin:"(?<!=|:|^of|[^\\._$0-9A-Za-z]of|^in|[^\\._$0-9A-Za-z]in)\\s*(?=\\[)",end:"(?=$|^|[;,=}]|((?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(of|in)\\s+))",name:"meta.array-binding-pattern-variable.tsx",patterns:[{include:"#array-binding-pattern-const"},{include:"#type-annotation"},{include:"#comment"}]}]},"destructuring-parameter":{patterns:[{begin:"(?<!=|:)\\s*(?:(\\.\\.\\.)\\s*)?(\\{)",beginCaptures:{1:{name:"keyword.operator.rest.tsx"},2:{name:"punctuation.definition.binding-pattern.object.tsx"}},end:"\\}",endCaptures:{0:{name:"punctuation.definition.binding-pattern.object.tsx"}},name:"meta.parameter.object-binding-pattern.tsx",patterns:[{include:"#parameter-object-binding-element"}]},{begin:"(?<!=|:)\\s*(?:(\\.\\.\\.)\\s*)?(\\[)",beginCaptures:{1:{name:"keyword.operator.rest.tsx"},2:{name:"punctuation.definition.binding-pattern.array.tsx"}},end:"\\]",endCaptures:{0:{name:"punctuation.definition.binding-pattern.array.tsx"}},name:"meta.paramter.array-binding-pattern.tsx",patterns:[{include:"#parameter-binding-element"},{include:"#punctuation-comma"}]}]},"destructuring-parameter-rest":{captures:{1:{name:"keyword.operator.rest.tsx"},2:{name:"variable.parameter.tsx"}},match:"(?:(\\.\\.\\.)\\s*)?([_$A-Za-z][_$0-9A-Za-z]*)"},"destructuring-variable":{patterns:[{begin:"(?<!=|:|^of|[^\\._$0-9A-Za-z]of|^in|[^\\._$0-9A-Za-z]in)\\s*(?=\\{)",end:"(?=$|^|[;,=}]|((?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(of|in)\\s+))",name:"meta.object-binding-pattern-variable.tsx",patterns:[{include:"#object-binding-pattern"},{include:"#type-annotation"},{include:"#comment"}]},{begin:"(?<!=|:|^of|[^\\._$0-9A-Za-z]of|^in|[^\\._$0-9A-Za-z]in)\\s*(?=\\[)",end:"(?=$|^|[;,=}]|((?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(of|in)\\s+))",name:"meta.array-binding-pattern-variable.tsx",patterns:[{include:"#array-binding-pattern"},{include:"#type-annotation"},{include:"#comment"}]}]},"destructuring-variable-rest":{captures:{1:{name:"keyword.operator.rest.tsx"},2:{name:"meta.definition.variable.tsx variable.other.readwrite.tsx"}},match:"(?:(\\.\\.\\.)\\s*)?([_$A-Za-z][_$0-9A-Za-z]*)"},"destructuring-variable-rest-const":{captures:{1:{name:"keyword.operator.rest.tsx"},2:{name:"meta.definition.variable.tsx variable.other.constant.tsx"}},match:"(?:(\\.\\.\\.)\\s*)?([_$A-Za-z][_$0-9A-Za-z]*)"},directives:{begin:"^(///)\\s*(?=<(reference|amd-dependency|amd-module)(\\s+(path|types|no-default-lib|lib|name|resolution-mode)\\s*=\\s*((\\'([^\\'\\\\]|\\\\.)*\\')|(\\\"([^\\\"\\\\]|\\\\.)*\\\")|(\\`([^\\`\\\\]|\\\\.)*\\`)))+\\s*/>\\s*$)",beginCaptures:{1:{name:"punctuation.definition.comment.tsx"}},end:"(?=$)",name:"comment.line.triple-slash.directive.tsx",patterns:[{begin:"(<)(reference|amd-dependency|amd-module)",beginCaptures:{1:{name:"punctuation.definition.tag.directive.tsx"},2:{name:"entity.name.tag.directive.tsx"}},end:"/>",endCaptures:{0:{name:"punctuation.definition.tag.directive.tsx"}},name:"meta.tag.tsx",patterns:[{match:"path|types|no-default-lib|lib|name|resolution-mode",name:"entity.other.attribute-name.directive.tsx"},{match:"=",name:"keyword.operator.assignment.tsx"},{include:"#string"}]}]},docblock:{patterns:[{captures:{1:{name:"storage.type.class.jsdoc"},2:{name:"punctuation.definition.block.tag.jsdoc"},3:{name:"constant.language.access-type.jsdoc"}},match:"((@)(?:access|api))\\s+(private|protected|public)\\b"},{captures:{1:{name:"storage.type.class.jsdoc"},2:{name:"punctuation.definition.block.tag.jsdoc"},3:{name:"entity.name.type.instance.jsdoc"},4:{name:"punctuation.definition.bracket.angle.begin.jsdoc"},5:{name:"constant.other.email.link.underline.jsdoc"},6:{name:"punctuation.definition.bracket.angle.end.jsdoc"}},match:"((@)author)\\s+([^@\\s<>*/](?:[^@<>*/]|\\*[^/])*)(?:\\s*(<)([^>\\s]+)(>))?"},{captures:{1:{name:"storage.type.class.jsdoc"},2:{name:"punctuation.definition.block.tag.jsdoc"},3:{name:"entity.name.type.instance.jsdoc"},4:{name:"keyword.operator.control.jsdoc"},5:{name:"entity.name.type.instance.jsdoc"}},match:"((@)borrows)\\s+((?:[^@\\s*/]|\\*[^/])+)\\s+(as)\\s+((?:[^@\\s*/]|\\*[^/])+)"},{begin:"((@)example)\\s+",beginCaptures:{1:{name:"storage.type.class.jsdoc"},2:{name:"punctuation.definition.block.tag.jsdoc"}},end:"(?=@|\\*/)",name:"meta.example.jsdoc",patterns:[{match:"^\\s\\*\\s+"},{begin:"\\G(<)caption(>)",beginCaptures:{0:{name:"entity.name.tag.inline.jsdoc"},1:{name:"punctuation.definition.bracket.angle.begin.jsdoc"},2:{name:"punctuation.definition.bracket.angle.end.jsdoc"}},contentName:"constant.other.description.jsdoc",end:"(</)caption(>)|(?=\\*/)",endCaptures:{0:{name:"entity.name.tag.inline.jsdoc"},1:{name:"punctuation.definition.bracket.angle.begin.jsdoc"},2:{name:"punctuation.definition.bracket.angle.end.jsdoc"}}},{captures:{0:{name:"source.embedded.tsx"}},match:"[^\\s@*](?:[^*]|\\*[^/])*"}]},{captures:{1:{name:"storage.type.class.jsdoc"},2:{name:"punctuation.definition.block.tag.jsdoc"},3:{name:"constant.language.symbol-type.jsdoc"}},match:"((@)kind)\\s+(class|constant|event|external|file|function|member|mixin|module|namespace|typedef)\\b"},{captures:{1:{name:"storage.type.class.jsdoc"},2:{name:"punctuation.definition.block.tag.jsdoc"},3:{name:"variable.other.link.underline.jsdoc"},4:{name:"entity.name.type.instance.jsdoc"}},match:"((@)see)\\s+(?:((?=https?://)(?:[^\\s*]|\\*[^/])+)|((?!https?://|(?:\\[[^\\[\\]]*\\])?{@(?:link|linkcode|linkplain|tutorial)\\b)(?:[^@\\s*/]|\\*[^/])+))"},{captures:{1:{name:"storage.type.class.jsdoc"},2:{name:"punctuation.definition.block.tag.jsdoc"},3:{name:"variable.other.jsdoc"}},match:"((@)template)\\s+([A-Za-z_$][\\w$.\\[\\]]*(?:\\s*,\\s*[A-Za-z_$][\\w$.\\[\\]]*)*)"},{begin:"((@)template)\\s+(?={)",beginCaptures:{1:{name:"storage.type.class.jsdoc"},2:{name:"punctuation.definition.block.tag.jsdoc"}},end:"(?=\\s|\\*/|[^{}\\[\\]A-Za-z_$])",patterns:[{include:"#jsdoctype"},{match:"([A-Za-z_$][\\w$.\\[\\]]*)",name:"variable.other.jsdoc"}]},{captures:{1:{name:"storage.type.class.jsdoc"},2:{name:"punctuation.definition.block.tag.jsdoc"},3:{name:"variable.other.jsdoc"}},match:"((@)(?:arg|argument|const|constant|member|namespace|param|var))\\s+([A-Za-z_$][\\w$.\\[\\]]*)"},{begin:"((@)typedef)\\s+(?={)",beginCaptures:{1:{name:"storage.type.class.jsdoc"},2:{name:"punctuation.definition.block.tag.jsdoc"}},end:"(?=\\s|\\*/|[^{}\\[\\]A-Za-z_$])",patterns:[{include:"#jsdoctype"},{match:"(?:[^@\\s*/]|\\*[^/])+",name:"entity.name.type.instance.jsdoc"}]},{begin:"((@)(?:arg|argument|const|constant|member|namespace|param|prop|property|var))\\s+(?={)",beginCaptures:{1:{name:"storage.type.class.jsdoc"},2:{name:"punctuation.definition.block.tag.jsdoc"}},end:"(?=\\s|\\*/|[^{}\\[\\]A-Za-z_$])",patterns:[{include:"#jsdoctype"},{match:"([A-Za-z_$][\\w$.\\[\\]]*)",name:"variable.other.jsdoc"},{captures:{1:{name:"punctuation.definition.optional-value.begin.bracket.square.jsdoc"},2:{name:"keyword.operator.assignment.jsdoc"},3:{name:"source.embedded.tsx"},4:{name:"punctuation.definition.optional-value.end.bracket.square.jsdoc"},5:{name:"invalid.illegal.syntax.jsdoc"}},match:"(\\[)\\s*[\\w$]+(?:(?:\\[\\])?\\.[\\w$]+)*(?:\\s*(=)\\s*((?>\"(?:(?:\\*(?!/))|(?:\\\\(?!\"))|[^*\\\\])*?\"|'(?:(?:\\*(?!/))|(?:\\\\(?!'))|[^*\\\\])*?'|\\[(?:(?:\\*(?!/))|[^*])*?\\]|(?:(?:\\*(?!/))|\\s(?!\\s*\\])|\\[.*?(?:\\]|(?=\\*/))|[^*\\s\\[\\]])*)*))?\\s*(?:(\\])((?:[^*\\s]|\\*[^\\s/])+)?|(?=\\*/))",name:"variable.other.jsdoc"}]},{begin:"((@)(?:define|enum|exception|export|extends|lends|implements|modifies|namespace|private|protected|returns?|satisfies|suppress|this|throws|type|yields?))\\s+(?={)",beginCaptures:{1:{name:"storage.type.class.jsdoc"},2:{name:"punctuation.definition.block.tag.jsdoc"}},end:"(?=\\s|\\*/|[^{}\\[\\]A-Za-z_$])",patterns:[{include:"#jsdoctype"}]},{captures:{1:{name:"storage.type.class.jsdoc"},2:{name:"punctuation.definition.block.tag.jsdoc"},3:{name:"entity.name.type.instance.jsdoc"}},match:"((@)(?:alias|augments|callback|constructs|emits|event|fires|exports?|extends|external|function|func|host|lends|listens|interface|memberof!?|method|module|mixes|mixin|name|requires|see|this|typedef|uses))\\s+((?:[^{}@\\s*]|\\*[^/])+)"},{begin:"((@)(?:default(?:value)?|license|version))\\s+(([''\"]))",beginCaptures:{1:{name:"storage.type.class.jsdoc"},2:{name:"punctuation.definition.block.tag.jsdoc"},3:{name:"variable.other.jsdoc"},4:{name:"punctuation.definition.string.begin.jsdoc"}},contentName:"variable.other.jsdoc",end:"(\\3)|(?=$|\\*/)",endCaptures:{0:{name:"variable.other.jsdoc"},1:{name:"punctuation.definition.string.end.jsdoc"}}},{captures:{1:{name:"storage.type.class.jsdoc"},2:{name:"punctuation.definition.block.tag.jsdoc"},3:{name:"variable.other.jsdoc"}},match:"((@)(?:default(?:value)?|license|tutorial|variation|version))\\s+([^\\s*]+)"},{captures:{1:{name:"punctuation.definition.block.tag.jsdoc"}},match:"(@)(?:abstract|access|alias|api|arg|argument|async|attribute|augments|author|beta|borrows|bubbles|callback|chainable|class|classdesc|code|config|const|constant|constructor|constructs|copyright|default|defaultvalue|define|deprecated|desc|description|dict|emits|enum|event|example|exception|exports?|extends|extension(?:_?for)?|external|externs|file|fileoverview|final|fires|for|func|function|generator|global|hideconstructor|host|ignore|implements|implicitCast|inherit[Dd]oc|inner|instance|interface|internal|kind|lends|license|listens|main|member|memberof!?|method|mixes|mixins?|modifies|module|name|namespace|noalias|nocollapse|nocompile|nosideeffects|override|overview|package|param|polymer(?:Behavior)?|preserve|private|prop|property|protected|public|read[Oo]nly|record|require[ds]|returns?|see|since|static|struct|submodule|summary|suppress|template|this|throws|todo|tutorial|type|typedef|unrestricted|uses|var|variation|version|virtual|writeOnce|yields?)\\b",name:"storage.type.class.jsdoc"},{include:"#inline-tags"},{captures:{1:{name:"storage.type.class.jsdoc"},2:{name:"punctuation.definition.block.tag.jsdoc"}},match:"((@)(?:[_$A-Za-z][_$0-9A-Za-z]*))(?=\\s+)"}]},"enum-declaration":{begin:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(?:(\\bexport)\\s+)?(?:(\\bdeclare)\\s+)?(?:\\b(const)\\s+)?\\b(enum)\\s+([_$A-Za-z][_$0-9A-Za-z]*)",beginCaptures:{1:{name:"keyword.control.export.tsx"},2:{name:"storage.modifier.tsx"},3:{name:"storage.modifier.tsx"},4:{name:"storage.type.enum.tsx"},5:{name:"entity.name.type.enum.tsx"}},end:"(?<=\\})",name:"meta.enum.declaration.tsx",patterns:[{include:"#comment"},{begin:"\\{",beginCaptures:{0:{name:"punctuation.definition.block.tsx"}},end:"\\}",endCaptures:{0:{name:"punctuation.definition.block.tsx"}},patterns:[{include:"#comment"},{begin:"([_$A-Za-z][_$0-9A-Za-z]*)",beginCaptures:{0:{name:"variable.other.enummember.tsx"}},end:"(?=,|\\}|$)",patterns:[{include:"#comment"},{include:"#variable-initializer"}]},{begin:"(?=((\\'([^\\'\\\\]|\\\\.)*\\')|(\\\"([^\\\"\\\\]|\\\\.)*\\\")|(\\`([^\\`\\\\]|\\\\.)*\\`)|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])+\\])))",end:"(?=,|\\}|$)",patterns:[{include:"#string"},{include:"#array-literal"},{include:"#comment"},{include:"#variable-initializer"}]},{include:"#punctuation-comma"}]}]},"export-declaration":{patterns:[{captures:{1:{name:"keyword.control.export.tsx"},2:{name:"keyword.control.as.tsx"},3:{name:"storage.type.namespace.tsx"},4:{name:"entity.name.type.module.tsx"}},match:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(export)\\s+(as)\\s+(namespace)\\s+([_$A-Za-z][_$0-9A-Za-z]*)"},{begin:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(export)(?:\\s+(type))?(?:(?:\\s*(=))|(?:\\s+(default)(?=\\s+)))",beginCaptures:{1:{name:"keyword.control.export.tsx"},2:{name:"keyword.control.type.tsx"},3:{name:"keyword.operator.assignment.tsx"},4:{name:"keyword.control.default.tsx"}},end:"(?=$|;|^\\s*$|(?:^\\s*(?:abstract|async|(?:\\bawait\\s+(?:\\busing(?=\\s+(?!in\\b|of\\b(?!\\s*(?:of\\b|=)))[_$A-Za-z])\\b)\\b)|break|case|catch|class|const|continue|declare|do|else|enum|export|finally|function|for|goto|if|import|interface|let|module|namespace|switch|return|throw|try|type|(?:\\busing(?=\\s+(?!in\\b|of\\b(?!\\s*(?:of\\b|=)))[_$A-Za-z])\\b)|var|while)\\b))",name:"meta.export.default.tsx",patterns:[{include:"#interface-declaration"},{include:"#expression"}]},{begin:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(export)(?:\\s+(type))?\\b(?!(\\$)|(\\s*:))((?=\\s*[{*])|((?=\\s*[_$A-Za-z][_$0-9A-Za-z]*(\\s|,))(?!\\s*(?:abstract|async|(?:\\bawait\\s+(?:\\busing(?=\\s+(?!in\\b|of\\b(?!\\s*(?:of\\b|=)))[_$A-Za-z])\\b)\\b)|break|case|catch|class|const|continue|declare|do|else|enum|export|finally|function|for|goto|if|import|interface|let|module|namespace|switch|return|throw|try|type|(?:\\busing(?=\\s+(?!in\\b|of\\b(?!\\s*(?:of\\b|=)))[_$A-Za-z])\\b)|var|while)\\b)))",beginCaptures:{1:{name:"keyword.control.export.tsx"},2:{name:"keyword.control.type.tsx"}},end:"(?=$|;|^\\s*$|(?:^\\s*(?:abstract|async|(?:\\bawait\\s+(?:\\busing(?=\\s+(?!in\\b|of\\b(?!\\s*(?:of\\b|=)))[_$A-Za-z])\\b)\\b)|break|case|catch|class|const|continue|declare|do|else|enum|export|finally|function|for|goto|if|import|interface|let|module|namespace|switch|return|throw|try|type|(?:\\busing(?=\\s+(?!in\\b|of\\b(?!\\s*(?:of\\b|=)))[_$A-Za-z])\\b)|var|while)\\b))",name:"meta.export.tsx",patterns:[{include:"#import-export-declaration"}]}]},expression:{patterns:[{include:"#expressionWithoutIdentifiers"},{include:"#identifiers"},{include:"#expressionPunctuations"}]},"expression-inside-possibly-arrow-parens":{patterns:[{include:"#expressionWithoutIdentifiers"},{include:"#comment"},{include:"#string"},{include:"#decorator"},{include:"#destructuring-parameter"},{captures:{1:{name:"storage.modifier.tsx"}},match:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(override|public|protected|private|readonly)\\s+(?=(override|public|protected|private|readonly)\\s+)"},{captures:{1:{name:"storage.modifier.tsx"},2:{name:"keyword.operator.rest.tsx"},3:{name:"entity.name.function.tsx variable.language.this.tsx"},4:{name:"entity.name.function.tsx"},5:{name:"keyword.operator.optional.tsx"}},match:"(?:(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(override|public|private|protected|readonly)\\s+)?(?:(\\.\\.\\.)\\s*)?(?<!=|:)(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(?:(this)|([_$A-Za-z][_$0-9A-Za-z]*))(?![_$0-9A-Za-z])(?:(?=\\.\\.\\.)|(?!\\.))\\s*(\\??)(?=\\s*(=\\s*(((async\\s+)?((function\\s*[(<*])|(function\\s+)|([_$A-Za-z][_$0-9A-Za-z]*\\s*=>)))|((async\\s*)?(((<\\s*$)|([(]\\s*((([{\\[]\\s*)?$)|((\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})\\s*((:\\s*\\{?$)|((\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\([^()]+\\)|\\{[^{}]+\\})+\\s*)?=\\s*)))|((\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\])\\s*((:\\s*\\[?$)|((\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\([^()]+\\)|\\{[^{}]+\\})+\\s*)?=\\s*))))))|((<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<]|<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<]|<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<])*>)*>)*>\\s*)?[(]\\s*(\\/\\*([^\\*]|(\\*[^\\/]))*\\*\\/\\s*)*(([)]\\s*:)|((\\.\\.\\.\\s*)?[_$A-Za-z][_$0-9A-Za-z]*\\s*:)))|([<]\\s*[_$A-Za-z][_$0-9A-Za-z]*\\s+extends\\s*[^=>])|((<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<]|<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<]|<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<])*>)*>)*>\\s*)?\\(\\s*(\\/\\*([^\\*]|(\\*[^\\/]))*\\*\\/\\s*)*(([_$A-Za-z]|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\])|(\\.\\.\\.\\s*[_$A-Za-z]))([^()\\'\\\"\\`]|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\'([^\\'\\\\]|\\\\.)*\\')|(\\\"([^\\\"\\\\]|\\\\.)*\\\")|(\\`([^\\`\\\\]|\\\\.)*\\`))*)?\\)(\\s*:\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\([^()]+\\)|\\{[^{}]+\\})+)?\\s*=>)))))|(:\\s*((<)|([(]\\s*(([)])|(\\.\\.\\.)|([_$0-9A-Za-z]+\\s*(([:,?=])|([)]\\s*=>)))))))|(:\\s*(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))Function(?![_$0-9A-Za-z])(?:(?=\\.\\.\\.)|(?!\\.)))|(:\\s*((<\\s*$)|([(]\\s*((([{\\[]\\s*)?$)|((\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})\\s*((:\\s*\\{?$)|((\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\([^()]+\\)|\\{[^{}]+\\})+\\s*)?=\\s*)))|((\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\])\\s*((:\\s*\\[?$)|((\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\([^()]+\\)|\\{[^{}]+\\})+\\s*)?=\\s*)))))))|(:\\s*(=>|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(<[^<>]*>)|[^<>(),=])+=\\s*(((async\\s+)?((function\\s*[(<*])|(function\\s+)|([_$A-Za-z][_$0-9A-Za-z]*\\s*=>)))|((async\\s*)?(((<\\s*$)|([(]\\s*((([{\\[]\\s*)?$)|((\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})\\s*((:\\s*\\{?$)|((\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\([^()]+\\)|\\{[^{}]+\\})+\\s*)?=\\s*)))|((\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\])\\s*((:\\s*\\[?$)|((\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\([^()]+\\)|\\{[^{}]+\\})+\\s*)?=\\s*))))))|((<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<]|<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<]|<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<])*>)*>)*>\\s*)?[(]\\s*(\\/\\*([^\\*]|(\\*[^\\/]))*\\*\\/\\s*)*(([)]\\s*:)|((\\.\\.\\.\\s*)?[_$A-Za-z][_$0-9A-Za-z]*\\s*:)))|([<]\\s*[_$A-Za-z][_$0-9A-Za-z]*\\s+extends\\s*[^=>])|((<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<]|<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<]|<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<])*>)*>)*>\\s*)?\\(\\s*(\\/\\*([^\\*]|(\\*[^\\/]))*\\*\\/\\s*)*(([_$A-Za-z]|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\])|(\\.\\.\\.\\s*[_$A-Za-z]))([^()\\'\\\"\\`]|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\'([^\\'\\\\]|\\\\.)*\\')|(\\\"([^\\\"\\\\]|\\\\.)*\\\")|(\\`([^\\`\\\\]|\\\\.)*\\`))*)?\\)(\\s*:\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\([^()]+\\)|\\{[^{}]+\\})+)?\\s*=>))))))"},{captures:{1:{name:"storage.modifier.tsx"},2:{name:"keyword.operator.rest.tsx"},3:{name:"variable.parameter.tsx variable.language.this.tsx"},4:{name:"variable.parameter.tsx"},5:{name:"keyword.operator.optional.tsx"}},match:"(?:(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(override|public|private|protected|readonly)\\s+)?(?:(\\.\\.\\.)\\s*)?(?<!=|:)(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(?:(this)|([_$A-Za-z][_$0-9A-Za-z]*))(?![_$0-9A-Za-z])(?:(?=\\.\\.\\.)|(?!\\.))\\s*(\\??)(?=\\s*[:,]|$)"},{include:"#type-annotation"},{include:"#variable-initializer"},{match:",",name:"punctuation.separator.parameter.tsx"},{include:"#identifiers"},{include:"#expressionPunctuations"}]},"expression-operators":{patterns:[{match:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(await)(?![_$0-9A-Za-z])(?:(?=\\.\\.\\.)|(?!\\.))",name:"keyword.control.flow.tsx"},{begin:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(yield)(?![_$0-9A-Za-z])(?:(?=\\.\\.\\.)|(?!\\.))(?=\\s*\\/\\*([^\\*]|(\\*[^\\/]))*\\*\\/\\s*\\*)",beginCaptures:{1:{name:"keyword.control.flow.tsx"}},end:"\\*",endCaptures:{0:{name:"keyword.generator.asterisk.tsx"}},patterns:[{include:"#comment"}]},{captures:{1:{name:"keyword.control.flow.tsx"},2:{name:"keyword.generator.asterisk.tsx"}},match:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(yield)(?![_$0-9A-Za-z])(?:(?=\\.\\.\\.)|(?!\\.))(?:\\s*(\\*))?"},{match:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))delete(?![_$0-9A-Za-z])(?:(?=\\.\\.\\.)|(?!\\.))",name:"keyword.operator.expression.delete.tsx"},{match:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))in(?![_$0-9A-Za-z])(?:(?=\\.\\.\\.)|(?!\\.))(?!\\()",name:"keyword.operator.expression.in.tsx"},{match:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))of(?![_$0-9A-Za-z])(?:(?=\\.\\.\\.)|(?!\\.))(?!\\()",name:"keyword.operator.expression.of.tsx"},{match:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))instanceof(?![_$0-9A-Za-z])(?:(?=\\.\\.\\.)|(?!\\.))",name:"keyword.operator.expression.instanceof.tsx"},{match:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))new(?![_$0-9A-Za-z])(?:(?=\\.\\.\\.)|(?!\\.))",name:"keyword.operator.new.tsx"},{include:"#typeof-operator"},{match:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))void(?![_$0-9A-Za-z])(?:(?=\\.\\.\\.)|(?!\\.))",name:"keyword.operator.expression.void.tsx"},{captures:{1:{name:"keyword.control.as.tsx"},2:{name:"storage.modifier.tsx"}},match:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(as)\\s+(const)(?=\\s*($|[;,:})\\]]))"},{begin:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(?:(as)|(satisfies))\\s+",beginCaptures:{1:{name:"keyword.control.as.tsx"},2:{name:"keyword.control.satisfies.tsx"}},end:"(?=^|[;),}\\]:?\\-+>]|\\|\\||\\&\\&|!==|$|((?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(as|satisfies)\\s+)|(\\s+<))",patterns:[{include:"#type"}]},{match:"\\.\\.\\.",name:"keyword.operator.spread.tsx"},{match:"\\*=|(?<!\\()/=|%=|\\+=|-=",name:"keyword.operator.assignment.compound.tsx"},{match:"\\&=|\\^=|<<=|>>=|>>>=|\\|=",name:"keyword.operator.assignment.compound.bitwise.tsx"},{match:"<<|>>>|>>",name:"keyword.operator.bitwise.shift.tsx"},{match:"===|!==|==|!=",name:"keyword.operator.comparison.tsx"},{match:"<=|>=|<>|<|>",name:"keyword.operator.relational.tsx"},{captures:{1:{name:"keyword.operator.logical.tsx"},2:{name:"keyword.operator.assignment.compound.tsx"},3:{name:"keyword.operator.arithmetic.tsx"}},match:"(?<=[_$0-9A-Za-z])(!)\\s*(?:(/=)|(?:(/)(?![/*])))"},{match:"!|&&|\\|\\||\\?\\?",name:"keyword.operator.logical.tsx"},{match:"\\&|~|\\^|\\|",name:"keyword.operator.bitwise.tsx"},{match:"=",name:"keyword.operator.assignment.tsx"},{match:"--",name:"keyword.operator.decrement.tsx"},{match:"\\+\\+",name:"keyword.operator.increment.tsx"},{match:"%|\\*|/|-|\\+",name:"keyword.operator.arithmetic.tsx"},{begin:"(?<=[_$0-9A-Za-z)\\]])\\s*(?=(\\/\\*([^\\*]|(\\*[^\\/]))*\\*\\/\\s*)+(?:(/=)|(?:(/)(?![/*]))))",end:"(?:(/=)|(?:(/)(?!\\*([^\\*]|(\\*[^\\/]))*\\*\\/)))",endCaptures:{1:{name:"keyword.operator.assignment.compound.tsx"},2:{name:"keyword.operator.arithmetic.tsx"}},patterns:[{include:"#comment"}]},{captures:{1:{name:"keyword.operator.assignment.compound.tsx"},2:{name:"keyword.operator.arithmetic.tsx"}},match:"(?<=[_$0-9A-Za-z)\\]])\\s*(?:(/=)|(?:(/)(?![/*])))"}]},expressionPunctuations:{patterns:[{include:"#punctuation-comma"},{include:"#punctuation-accessor"}]},expressionWithoutIdentifiers:{patterns:[{include:"#jsx"},{include:"#string"},{include:"#regex"},{include:"#comment"},{include:"#function-expression"},{include:"#class-expression"},{include:"#arrow-function"},{include:"#paren-expression-possibly-arrow"},{include:"#cast"},{include:"#ternary-expression"},{include:"#new-expr"},{include:"#instanceof-expr"},{include:"#object-literal"},{include:"#expression-operators"},{include:"#function-call"},{include:"#literal"},{include:"#support-objects"},{include:"#paren-expression"}]},"field-declaration":{begin:"(?<!\\()(?:(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(readonly)\\s+)?(?=\\s*((\\b(?<!\\$)0(?:x|X)[0-9a-fA-F][0-9a-fA-F_]*(n)?\\b(?!\\$))|(\\b(?<!\\$)0(?:b|B)[01][01_]*(n)?\\b(?!\\$))|(\\b(?<!\\$)0(?:o|O)?[0-7][0-7_]*(n)?\\b(?!\\$))|((?<!\\$)(?:(?:\\b\\d[0-9_]*(\\.)\\d[0-9_]*[eE][+-]?\\d[0-9_]*(n)?\\b)|(?:\\b\\d[0-9_]*(\\.)[eE][+-]?\\d[0-9_]*(n)?\\b)|(?:\\B(\\.)\\d[0-9_]*[eE][+-]?\\d[0-9_]*(n)?\\b)|(?:\\b\\d[0-9_]*[eE][+-]?\\d[0-9_]*(n)?\\b)|(?:\\b\\d[0-9_]*(\\.)\\d[0-9_]*(n)?\\b)|(?:\\b\\d[0-9_]*(\\.)(n)?\\B)|(?:\\B(\\.)\\d[0-9_]*(n)?\\b)|(?:\\b\\d[0-9_]*(n)?\\b(?!\\.)))(?!\\$))|(\\#?[_$A-Za-z][_$0-9A-Za-z]*)|(\\'([^\\'\\\\]|\\\\.)*\\')|(\\\"([^\\\"\\\\]|\\\\.)*\\\")|(\\`([^\\`\\\\]|\\\\.)*\\`)|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])+\\]))\\s*(?:(?:(\\?)|(!))\\s*)?(=|:|;|,|\\}|$))",beginCaptures:{1:{name:"storage.modifier.tsx"}},end:"(?=\\}|;|,|$|(^(?!\\s*((\\b(?<!\\$)0(?:x|X)[0-9a-fA-F][0-9a-fA-F_]*(n)?\\b(?!\\$))|(\\b(?<!\\$)0(?:b|B)[01][01_]*(n)?\\b(?!\\$))|(\\b(?<!\\$)0(?:o|O)?[0-7][0-7_]*(n)?\\b(?!\\$))|((?<!\\$)(?:(?:\\b\\d[0-9_]*(\\.)\\d[0-9_]*[eE][+-]?\\d[0-9_]*(n)?\\b)|(?:\\b\\d[0-9_]*(\\.)[eE][+-]?\\d[0-9_]*(n)?\\b)|(?:\\B(\\.)\\d[0-9_]*[eE][+-]?\\d[0-9_]*(n)?\\b)|(?:\\b\\d[0-9_]*[eE][+-]?\\d[0-9_]*(n)?\\b)|(?:\\b\\d[0-9_]*(\\.)\\d[0-9_]*(n)?\\b)|(?:\\b\\d[0-9_]*(\\.)(n)?\\B)|(?:\\B(\\.)\\d[0-9_]*(n)?\\b)|(?:\\b\\d[0-9_]*(n)?\\b(?!\\.)))(?!\\$))|(\\#?[_$A-Za-z][_$0-9A-Za-z]*)|(\\'([^\\'\\\\]|\\\\.)*\\')|(\\\"([^\\\"\\\\]|\\\\.)*\\\")|(\\`([^\\`\\\\]|\\\\.)*\\`)|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])+\\]))\\s*(?:(?:(\\?)|(!))\\s*)?(=|:|;|,|$))))|(?<=\\})",name:"meta.field.declaration.tsx",patterns:[{include:"#variable-initializer"},{include:"#type-annotation"},{include:"#string"},{include:"#array-literal"},{include:"#numeric-literal"},{include:"#comment"},{captures:{1:{name:"meta.definition.property.tsx entity.name.function.tsx"},2:{name:"keyword.operator.optional.tsx"},3:{name:"keyword.operator.definiteassignment.tsx"}},match:"(\\#?[_$A-Za-z][_$0-9A-Za-z]*)(?:(\\?)|(!))?(?=\\s*\\s*(=\\s*(((async\\s+)?((function\\s*[(<*])|(function\\s+)|([_$A-Za-z][_$0-9A-Za-z]*\\s*=>)))|((async\\s*)?(((<\\s*$)|([(]\\s*((([{\\[]\\s*)?$)|((\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})\\s*((:\\s*\\{?$)|((\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\([^()]+\\)|\\{[^{}]+\\})+\\s*)?=\\s*)))|((\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\])\\s*((:\\s*\\[?$)|((\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\([^()]+\\)|\\{[^{}]+\\})+\\s*)?=\\s*))))))|((<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<]|<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<]|<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<])*>)*>)*>\\s*)?[(]\\s*(\\/\\*([^\\*]|(\\*[^\\/]))*\\*\\/\\s*)*(([)]\\s*:)|((\\.\\.\\.\\s*)?[_$A-Za-z][_$0-9A-Za-z]*\\s*:)))|([<]\\s*[_$A-Za-z][_$0-9A-Za-z]*\\s+extends\\s*[^=>])|((<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<]|<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<]|<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<])*>)*>)*>\\s*)?\\(\\s*(\\/\\*([^\\*]|(\\*[^\\/]))*\\*\\/\\s*)*(([_$A-Za-z]|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\])|(\\.\\.\\.\\s*[_$A-Za-z]))([^()\\'\\\"\\`]|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\'([^\\'\\\\]|\\\\.)*\\')|(\\\"([^\\\"\\\\]|\\\\.)*\\\")|(\\`([^\\`\\\\]|\\\\.)*\\`))*)?\\)(\\s*:\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\([^()]+\\)|\\{[^{}]+\\})+)?\\s*=>)))))|(:\\s*((<)|([(]\\s*(([)])|(\\.\\.\\.)|([_$0-9A-Za-z]+\\s*(([:,?=])|([)]\\s*=>)))))))|(:\\s*(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))Function(?![_$0-9A-Za-z])(?:(?=\\.\\.\\.)|(?!\\.)))|(:\\s*((<\\s*$)|([(]\\s*((([{\\[]\\s*)?$)|((\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})\\s*((:\\s*\\{?$)|((\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\([^()]+\\)|\\{[^{}]+\\})+\\s*)?=\\s*)))|((\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\])\\s*((:\\s*\\[?$)|((\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\([^()]+\\)|\\{[^{}]+\\})+\\s*)?=\\s*)))))))|(:\\s*(=>|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(<[^<>]*>)|[^<>(),=])+=\\s*(((async\\s+)?((function\\s*[(<*])|(function\\s+)|([_$A-Za-z][_$0-9A-Za-z]*\\s*=>)))|((async\\s*)?(((<\\s*$)|([(]\\s*((([{\\[]\\s*)?$)|((\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})\\s*((:\\s*\\{?$)|((\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\([^()]+\\)|\\{[^{}]+\\})+\\s*)?=\\s*)))|((\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\])\\s*((:\\s*\\[?$)|((\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\([^()]+\\)|\\{[^{}]+\\})+\\s*)?=\\s*))))))|((<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<]|<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<]|<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<])*>)*>)*>\\s*)?[(]\\s*(\\/\\*([^\\*]|(\\*[^\\/]))*\\*\\/\\s*)*(([)]\\s*:)|((\\.\\.\\.\\s*)?[_$A-Za-z][_$0-9A-Za-z]*\\s*:)))|([<]\\s*[_$A-Za-z][_$0-9A-Za-z]*\\s+extends\\s*[^=>])|((<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<]|<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<]|<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<])*>)*>)*>\\s*)?\\(\\s*(\\/\\*([^\\*]|(\\*[^\\/]))*\\*\\/\\s*)*(([_$A-Za-z]|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\])|(\\.\\.\\.\\s*[_$A-Za-z]))([^()\\'\\\"\\`]|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\'([^\\'\\\\]|\\\\.)*\\')|(\\\"([^\\\"\\\\]|\\\\.)*\\\")|(\\`([^\\`\\\\]|\\\\.)*\\`))*)?\\)(\\s*:\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\([^()]+\\)|\\{[^{}]+\\})+)?\\s*=>))))))"},{match:"\\#?[_$A-Za-z][_$0-9A-Za-z]*",name:"meta.definition.property.tsx variable.object.property.tsx"},{match:"\\?",name:"keyword.operator.optional.tsx"},{match:"!",name:"keyword.operator.definiteassignment.tsx"}]},"for-loop":{begin:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))for(?=((\\s+|(\\s*\\/\\*([^\\*]|(\\*[^\\/]))*\\*\\/\\s*))await)?\\s*(\\/\\*([^\\*]|(\\*[^\\/]))*\\*\\/\\s*)?(\\())",beginCaptures:{0:{name:"keyword.control.loop.tsx"}},end:"(?<=\\))",patterns:[{include:"#comment"},{match:"await",name:"keyword.control.loop.tsx"},{begin:"\\(",beginCaptures:{0:{name:"meta.brace.round.tsx"}},end:"\\)",endCaptures:{0:{name:"meta.brace.round.tsx"}},patterns:[{include:"#var-expr"},{include:"#expression"},{include:"#punctuation-semicolon"}]}]},"function-body":{patterns:[{include:"#comment"},{include:"#type-parameters"},{include:"#function-parameters"},{include:"#return-type"},{include:"#type-function-return-type"},{include:"#decl-block"},{match:"\\*",name:"keyword.generator.asterisk.tsx"}]},"function-call":{patterns:[{begin:"(?=(((([_$A-Za-z][_$0-9A-Za-z]*)(\\s*\\??\\.\\s*(\\#?[_$A-Za-z][_$0-9A-Za-z]*))*)|(\\??\\.\\s*\\#?[_$A-Za-z][_$0-9A-Za-z]*))|(?<=[)]))\\s*(?:(\\?\\.\\s*)|(!))?((<\\s*(((keyof|infer|typeof|readonly)\\s+)|(([_$A-Za-z][_$0-9A-Za-z]*|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\])|(\\'([^\\'\\\\]|\\\\.)*\\')|(\\\"([^\\\"\\\\]|\\\\.)*\\\")|(\\`([^\\`\\\\]|\\\\.)*\\`))(?=\\s*([<>\\,\\.\\[]|=>|&(?!&)|\\|(?!\\|)))))([^<>(]|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(?<==)>|<\\s*(((keyof|infer|typeof|readonly)\\s+)|(([_$A-Za-z][_$0-9A-Za-z]*|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\])|(\\'([^\\'\\\\]|\\\\.)*\\')|(\\\"([^\\\"\\\\]|\\\\.)*\\\")|(\\`([^\\`\\\\]|\\\\.)*\\`))(?=\\s*([<>\\,\\.\\[]|=>|&(?!&)|\\|(?!\\|)))))(([^<>(]|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(?<==)>|<\\s*(((keyof|infer|typeof|readonly)\\s+)|(([_$A-Za-z][_$0-9A-Za-z]*|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\])|(\\'([^\\'\\\\]|\\\\.)*\\')|(\\\"([^\\\"\\\\]|\\\\.)*\\\")|(\\`([^\\`\\\\]|\\\\.)*\\`))(?=\\s*([<>\\,\\.\\[]|=>|&(?!&)|\\|(?!\\|)))))([^<>(]|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(?<==)>)*(?<!=)>))*(?<!=)>)*(?<!=)>\\s*)?\\())",end:"(?<=\\))(?!(((([_$A-Za-z][_$0-9A-Za-z]*)(\\s*\\??\\.\\s*(\\#?[_$A-Za-z][_$0-9A-Za-z]*))*)|(\\??\\.\\s*\\#?[_$A-Za-z][_$0-9A-Za-z]*))|(?<=[)]))\\s*(?:(\\?\\.\\s*)|(!))?((<\\s*(((keyof|infer|typeof|readonly)\\s+)|(([_$A-Za-z][_$0-9A-Za-z]*|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\])|(\\'([^\\'\\\\]|\\\\.)*\\')|(\\\"([^\\\"\\\\]|\\\\.)*\\\")|(\\`([^\\`\\\\]|\\\\.)*\\`))(?=\\s*([<>\\,\\.\\[]|=>|&(?!&)|\\|(?!\\|)))))([^<>(]|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(?<==)>|<\\s*(((keyof|infer|typeof|readonly)\\s+)|(([_$A-Za-z][_$0-9A-Za-z]*|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\])|(\\'([^\\'\\\\]|\\\\.)*\\')|(\\\"([^\\\"\\\\]|\\\\.)*\\\")|(\\`([^\\`\\\\]|\\\\.)*\\`))(?=\\s*([<>\\,\\.\\[]|=>|&(?!&)|\\|(?!\\|)))))(([^<>(]|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(?<==)>|<\\s*(((keyof|infer|typeof|readonly)\\s+)|(([_$A-Za-z][_$0-9A-Za-z]*|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\])|(\\'([^\\'\\\\]|\\\\.)*\\')|(\\\"([^\\\"\\\\]|\\\\.)*\\\")|(\\`([^\\`\\\\]|\\\\.)*\\`))(?=\\s*([<>\\,\\.\\[]|=>|&(?!&)|\\|(?!\\|)))))([^<>(]|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(?<==)>)*(?<!=)>))*(?<!=)>)*(?<!=)>\\s*)?\\())",patterns:[{begin:"(?=(([_$A-Za-z][_$0-9A-Za-z]*)(\\s*\\??\\.\\s*(\\#?[_$A-Za-z][_$0-9A-Za-z]*))*)|(\\??\\.\\s*\\#?[_$A-Za-z][_$0-9A-Za-z]*))",end:"(?=\\s*(?:(\\?\\.\\s*)|(!))?((<\\s*(((keyof|infer|typeof|readonly)\\s+)|(([_$A-Za-z][_$0-9A-Za-z]*|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\])|(\\'([^\\'\\\\]|\\\\.)*\\')|(\\\"([^\\\"\\\\]|\\\\.)*\\\")|(\\`([^\\`\\\\]|\\\\.)*\\`))(?=\\s*([<>\\,\\.\\[]|=>|&(?!&)|\\|(?!\\|)))))([^<>(]|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(?<==)>|<\\s*(((keyof|infer|typeof|readonly)\\s+)|(([_$A-Za-z][_$0-9A-Za-z]*|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\])|(\\'([^\\'\\\\]|\\\\.)*\\')|(\\\"([^\\\"\\\\]|\\\\.)*\\\")|(\\`([^\\`\\\\]|\\\\.)*\\`))(?=\\s*([<>\\,\\.\\[]|=>|&(?!&)|\\|(?!\\|)))))(([^<>(]|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(?<==)>|<\\s*(((keyof|infer|typeof|readonly)\\s+)|(([_$A-Za-z][_$0-9A-Za-z]*|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\])|(\\'([^\\'\\\\]|\\\\.)*\\')|(\\\"([^\\\"\\\\]|\\\\.)*\\\")|(\\`([^\\`\\\\]|\\\\.)*\\`))(?=\\s*([<>\\,\\.\\[]|=>|&(?!&)|\\|(?!\\|)))))([^<>(]|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(?<==)>)*(?<!=)>))*(?<!=)>)*(?<!=)>\\s*)?\\())",name:"meta.function-call.tsx",patterns:[{include:"#function-call-target"}]},{include:"#comment"},{include:"#function-call-optionals"},{include:"#type-arguments"},{include:"#paren-expression"}]},{begin:"(?=(((([_$A-Za-z][_$0-9A-Za-z]*)(\\s*\\??\\.\\s*(\\#?[_$A-Za-z][_$0-9A-Za-z]*))*)|(\\??\\.\\s*\\#?[_$A-Za-z][_$0-9A-Za-z]*))|(?<=[)]))(<\\s*[{\\[(]\\s*$))",end:"(?<=>)(?!(((([_$A-Za-z][_$0-9A-Za-z]*)(\\s*\\??\\.\\s*(\\#?[_$A-Za-z][_$0-9A-Za-z]*))*)|(\\??\\.\\s*\\#?[_$A-Za-z][_$0-9A-Za-z]*))|(?<=[)]))(<\\s*[{\\[(]\\s*$))",patterns:[{begin:"(?=(([_$A-Za-z][_$0-9A-Za-z]*)(\\s*\\??\\.\\s*(\\#?[_$A-Za-z][_$0-9A-Za-z]*))*)|(\\??\\.\\s*\\#?[_$A-Za-z][_$0-9A-Za-z]*))",end:"(?=(<\\s*[{\\[(]\\s*$))",name:"meta.function-call.tsx",patterns:[{include:"#function-call-target"}]},{include:"#comment"},{include:"#function-call-optionals"},{include:"#type-arguments"}]}]},"function-call-optionals":{patterns:[{match:"\\?\\.",name:"meta.function-call.tsx punctuation.accessor.optional.tsx"},{match:"!",name:"meta.function-call.tsx keyword.operator.definiteassignment.tsx"}]},"function-call-target":{patterns:[{include:"#support-function-call-identifiers"},{match:"(\\#?[_$A-Za-z][_$0-9A-Za-z]*)",name:"entity.name.function.tsx"}]},"function-declaration":{begin:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(?:(\\bexport)\\s+)?(?:(\\bdeclare)\\s+)?(?:(async)\\s+)?(function\\b)(?:\\s*(\\*))?(?:(?:\\s+|(?<=\\*))([_$A-Za-z][_$0-9A-Za-z]*))?\\s*",beginCaptures:{1:{name:"keyword.control.export.tsx"},2:{name:"storage.modifier.tsx"},3:{name:"storage.modifier.async.tsx"},4:{name:"storage.type.function.tsx"},5:{name:"keyword.generator.asterisk.tsx"},6:{name:"meta.definition.function.tsx entity.name.function.tsx"}},end:"(?=;|^\\s*$|(?:^\\s*(?:abstract|async|(?:\\bawait\\s+(?:\\busing(?=\\s+(?!in\\b|of\\b(?!\\s*(?:of\\b|=)))[_$A-Za-z])\\b)\\b)|break|case|catch|class|const|continue|declare|do|else|enum|export|finally|function|for|goto|if|import|interface|let|module|namespace|switch|return|throw|try|type|(?:\\busing(?=\\s+(?!in\\b|of\\b(?!\\s*(?:of\\b|=)))[_$A-Za-z])\\b)|var|while)\\b))|(?<=\\})",name:"meta.function.tsx",patterns:[{include:"#function-name"},{include:"#function-body"}]},"function-expression":{begin:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(?:(async)\\s+)?(function\\b)(?:\\s*(\\*))?(?:(?:\\s+|(?<=\\*))([_$A-Za-z][_$0-9A-Za-z]*))?\\s*",beginCaptures:{1:{name:"storage.modifier.async.tsx"},2:{name:"storage.type.function.tsx"},3:{name:"keyword.generator.asterisk.tsx"},4:{name:"meta.definition.function.tsx entity.name.function.tsx"}},end:"(?=;)|(?<=\\})",name:"meta.function.expression.tsx",patterns:[{include:"#function-name"},{include:"#single-line-comment-consuming-line-ending"},{include:"#function-body"}]},"function-name":{match:"[_$A-Za-z][_$0-9A-Za-z]*",name:"meta.definition.function.tsx entity.name.function.tsx"},"function-parameters":{begin:"\\(",beginCaptures:{0:{name:"punctuation.definition.parameters.begin.tsx"}},end:"\\)",endCaptures:{0:{name:"punctuation.definition.parameters.end.tsx"}},name:"meta.parameters.tsx",patterns:[{include:"#function-parameters-body"}]},"function-parameters-body":{patterns:[{include:"#comment"},{include:"#string"},{include:"#decorator"},{include:"#destructuring-parameter"},{include:"#parameter-name"},{include:"#parameter-type-annotation"},{include:"#variable-initializer"},{match:",",name:"punctuation.separator.parameter.tsx"}]},identifiers:{patterns:[{include:"#object-identifiers"},{captures:{1:{name:"punctuation.accessor.tsx"},2:{name:"punctuation.accessor.optional.tsx"},3:{name:"entity.name.function.tsx"}},match:"(?:(?:(\\.)|(\\?\\.(?!\\s*[\\d])))\\s*)?([_$A-Za-z][_$0-9A-Za-z]*)(?=\\s*=\\s*(((async\\s+)?((function\\s*[(<*])|(function\\s+)|([_$A-Za-z][_$0-9A-Za-z]*\\s*=>)))|((async\\s*)?(((<\\s*$)|([(]\\s*((([{\\[]\\s*)?$)|((\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})\\s*((:\\s*\\{?$)|((\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\([^()]+\\)|\\{[^{}]+\\})+\\s*)?=\\s*)))|((\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\])\\s*((:\\s*\\[?$)|((\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\([^()]+\\)|\\{[^{}]+\\})+\\s*)?=\\s*))))))|((<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<]|<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<]|<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<])*>)*>)*>\\s*)?[(]\\s*(\\/\\*([^\\*]|(\\*[^\\/]))*\\*\\/\\s*)*(([)]\\s*:)|((\\.\\.\\.\\s*)?[_$A-Za-z][_$0-9A-Za-z]*\\s*:)))|([<]\\s*[_$A-Za-z][_$0-9A-Za-z]*\\s+extends\\s*[^=>])|((<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<]|<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<]|<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<])*>)*>)*>\\s*)?\\(\\s*(\\/\\*([^\\*]|(\\*[^\\/]))*\\*\\/\\s*)*(([_$A-Za-z]|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\])|(\\.\\.\\.\\s*[_$A-Za-z]))([^()\\'\\\"\\`]|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\'([^\\'\\\\]|\\\\.)*\\')|(\\\"([^\\\"\\\\]|\\\\.)*\\\")|(\\`([^\\`\\\\]|\\\\.)*\\`))*)?\\)(\\s*:\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\([^()]+\\)|\\{[^{}]+\\})+)?\\s*=>)))))"},{captures:{1:{name:"punctuation.accessor.tsx"},2:{name:"punctuation.accessor.optional.tsx"},3:{name:"variable.other.constant.property.tsx"}},match:"(?:(\\.)|(\\?\\.(?!\\s*[\\d])))\\s*(\\#?[A-Z][_$\\dA-Z]*)(?![_$0-9A-Za-z])"},{captures:{1:{name:"punctuation.accessor.tsx"},2:{name:"punctuation.accessor.optional.tsx"},3:{name:"variable.other.property.tsx"}},match:"(?:(\\.)|(\\?\\.(?!\\s*[\\d])))\\s*(\\#?[_$A-Za-z][_$0-9A-Za-z]*)"},{match:"([A-Z][_$\\dA-Z]*)(?![_$0-9A-Za-z])",name:"variable.other.constant.tsx"},{match:"[_$A-Za-z][_$0-9A-Za-z]*",name:"variable.other.readwrite.tsx"}]},"if-statement":{patterns:[{begin:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(?=\\bif\\s*(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))\\s*(?!\\{))",end:"(?=;|$|\\})",patterns:[{include:"#comment"},{begin:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(if)\\s*(\\()",beginCaptures:{1:{name:"keyword.control.conditional.tsx"},2:{name:"meta.brace.round.tsx"}},end:"\\)",endCaptures:{0:{name:"meta.brace.round.tsx"}},patterns:[{include:"#expression"}]},{begin:"(?<=\\))\\s*\\/(?![\\/*])(?=(?:[^\\/\\\\\\[]|\\\\.|\\[([^\\]\\\\]|\\\\.)*\\])+\\/([dgimsuy]+|(?![\\/\\*])|(?=\\/\\*))(?!\\s*[a-zA-Z0-9_$]))",beginCaptures:{0:{name:"punctuation.definition.string.begin.tsx"}},end:"(/)([dgimsuy]*)",endCaptures:{1:{name:"punctuation.definition.string.end.tsx"},2:{name:"keyword.other.tsx"}},name:"string.regexp.tsx",patterns:[{include:"#regexp"}]},{include:"#statements"}]}]},"import-declaration":{begin:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(?:(\\bexport)\\s+)?(?:(\\bdeclare)\\s+)?\\b(import)(?:\\s+(type)(?!\\s+from))?(?!\\s*[:(])(?![_$0-9A-Za-z])(?:(?=\\.\\.\\.)|(?!\\.))",beginCaptures:{1:{name:"keyword.control.export.tsx"},2:{name:"storage.modifier.tsx"},3:{name:"keyword.control.import.tsx"},4:{name:"keyword.control.type.tsx"}},end:"(?<!^import|[^\\._$0-9A-Za-z]import)(?=;|$|^)",name:"meta.import.tsx",patterns:[{include:"#single-line-comment-consuming-line-ending"},{include:"#comment"},{include:"#string"},{begin:"(?<=^import|[^\\._$0-9A-Za-z]import)(?!\\s*[\"'])",end:"\\bfrom\\b",endCaptures:{0:{name:"keyword.control.from.tsx"}},patterns:[{include:"#import-export-declaration"}]},{include:"#import-export-declaration"}]},"import-equals-declaration":{patterns:[{begin:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(?:(\\bexport)\\s+)?(?:(\\bdeclare)\\s+)?\\b(import)(?:\\s+(type))?\\s+([_$A-Za-z][_$0-9A-Za-z]*)\\s*(=)\\s*(require)\\s*(\\()",beginCaptures:{1:{name:"keyword.control.export.tsx"},2:{name:"storage.modifier.tsx"},3:{name:"keyword.control.import.tsx"},4:{name:"keyword.control.type.tsx"},5:{name:"variable.other.readwrite.alias.tsx"},6:{name:"keyword.operator.assignment.tsx"},7:{name:"keyword.control.require.tsx"},8:{name:"meta.brace.round.tsx"}},end:"\\)",endCaptures:{0:{name:"meta.brace.round.tsx"}},name:"meta.import-equals.external.tsx",patterns:[{include:"#comment"},{include:"#string"}]},{begin:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(?:(\\bexport)\\s+)?(?:(\\bdeclare)\\s+)?\\b(import)(?:\\s+(type))?\\s+([_$A-Za-z][_$0-9A-Za-z]*)\\s*(=)\\s*(?!require\\b)",beginCaptures:{1:{name:"keyword.control.export.tsx"},2:{name:"storage.modifier.tsx"},3:{name:"keyword.control.import.tsx"},4:{name:"keyword.control.type.tsx"},5:{name:"variable.other.readwrite.alias.tsx"},6:{name:"keyword.operator.assignment.tsx"}},end:"(?=;|$|^)",name:"meta.import-equals.internal.tsx",patterns:[{include:"#single-line-comment-consuming-line-ending"},{include:"#comment"},{captures:{1:{name:"entity.name.type.module.tsx"},2:{name:"punctuation.accessor.tsx"},3:{name:"punctuation.accessor.optional.tsx"}},match:"([_$A-Za-z][_$0-9A-Za-z]*)\\s*(?:(\\.)|(\\?\\.(?!\\s*[\\d])))"},{match:"([_$A-Za-z][_$0-9A-Za-z]*)",name:"variable.other.readwrite.tsx"}]}]},"import-export-assert-clause":{begin:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(?:(with)|(assert))\\s*(\\{)",beginCaptures:{1:{name:"keyword.control.with.tsx"},2:{name:"keyword.control.assert.tsx"},3:{name:"punctuation.definition.block.tsx"}},end:"\\}",endCaptures:{0:{name:"punctuation.definition.block.tsx"}},patterns:[{include:"#comment"},{include:"#string"},{match:"(?:[_$A-Za-z][_$0-9A-Za-z]*)\\s*(?=(\\/\\*([^\\*]|(\\*[^\\/]))*\\*\\/\\s*)*:)",name:"meta.object-literal.key.tsx"},{match:":",name:"punctuation.separator.key-value.tsx"}]},"import-export-block":{begin:"\\{",beginCaptures:{0:{name:"punctuation.definition.block.tsx"}},end:"\\}",endCaptures:{0:{name:"punctuation.definition.block.tsx"}},name:"meta.block.tsx",patterns:[{include:"#import-export-clause"}]},"import-export-clause":{patterns:[{include:"#comment"},{captures:{1:{name:"keyword.control.type.tsx"},2:{name:"keyword.control.default.tsx"},3:{name:"constant.language.import-export-all.tsx"},4:{name:"variable.other.readwrite.tsx"},5:{name:"keyword.control.as.tsx"},6:{name:"keyword.control.default.tsx"},7:{name:"variable.other.readwrite.alias.tsx"}},match:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(?:(?:(\\btype)\\s+)?(?:(\\bdefault)|(\\*)|(\\b[_$A-Za-z][_$0-9A-Za-z]*)))\\s+(as)\\s+(?:(default(?![_$0-9A-Za-z])(?:(?=\\.\\.\\.)|(?!\\.)))|([_$A-Za-z][_$0-9A-Za-z]*))"},{include:"#punctuation-comma"},{match:"\\*",name:"constant.language.import-export-all.tsx"},{match:"\\b(default)\\b",name:"keyword.control.default.tsx"},{captures:{1:{name:"keyword.control.type.tsx"},2:{name:"variable.other.readwrite.alias.tsx"}},match:"(?:(\\btype)\\s+)?([_$A-Za-z][_$0-9A-Za-z]*)"}]},"import-export-declaration":{patterns:[{include:"#comment"},{include:"#string"},{include:"#import-export-block"},{match:"\\bfrom\\b",name:"keyword.control.from.tsx"},{include:"#import-export-assert-clause"},{include:"#import-export-clause"}]},"indexer-declaration":{begin:"(?:(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(readonly)\\s*)?\\s*(\\[)\\s*([_$A-Za-z][_$0-9A-Za-z]*)\\s*(?=:)",beginCaptures:{1:{name:"storage.modifier.tsx"},2:{name:"meta.brace.square.tsx"},3:{name:"variable.parameter.tsx"}},end:"(\\])\\s*(\\?\\s*)?|$",endCaptures:{1:{name:"meta.brace.square.tsx"},2:{name:"keyword.operator.optional.tsx"}},name:"meta.indexer.declaration.tsx",patterns:[{include:"#type-annotation"}]},"indexer-mapped-type-declaration":{begin:"(?:(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))([+-])?(readonly)\\s*)?\\s*(\\[)\\s*([_$A-Za-z][_$0-9A-Za-z]*)\\s+(in)\\s+",beginCaptures:{1:{name:"keyword.operator.type.modifier.tsx"},2:{name:"storage.modifier.tsx"},3:{name:"meta.brace.square.tsx"},4:{name:"entity.name.type.tsx"},5:{name:"keyword.operator.expression.in.tsx"}},end:"(\\])([+-])?\\s*(\\?\\s*)?|$",endCaptures:{1:{name:"meta.brace.square.tsx"},2:{name:"keyword.operator.type.modifier.tsx"},3:{name:"keyword.operator.optional.tsx"}},name:"meta.indexer.mappedtype.declaration.tsx",patterns:[{captures:{1:{name:"keyword.control.as.tsx"}},match:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(as)\\s+"},{include:"#type"}]},"inline-tags":{patterns:[{captures:{1:{name:"punctuation.definition.bracket.square.begin.jsdoc"},2:{name:"punctuation.definition.bracket.square.end.jsdoc"}},match:"(\\[)[^\\]]+(\\])(?={@(?:link|linkcode|linkplain|tutorial))",name:"constant.other.description.jsdoc"},{begin:"({)((@)(?:link(?:code|plain)?|tutorial))\\s*",beginCaptures:{1:{name:"punctuation.definition.bracket.curly.begin.jsdoc"},2:{name:"storage.type.class.jsdoc"},3:{name:"punctuation.definition.inline.tag.jsdoc"}},end:"}|(?=\\*/)",endCaptures:{0:{name:"punctuation.definition.bracket.curly.end.jsdoc"}},name:"entity.name.type.instance.jsdoc",patterns:[{captures:{1:{name:"variable.other.link.underline.jsdoc"},2:{name:"punctuation.separator.pipe.jsdoc"}},match:"\\G((?=https?://)(?:[^|}\\s*]|\\*[/])+)(\\|)?"},{captures:{1:{name:"variable.other.description.jsdoc"},2:{name:"punctuation.separator.pipe.jsdoc"}},match:"\\G((?:[^{}@\\s|*]|\\*[^/])+)(\\|)?"}]}]},"instanceof-expr":{begin:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(instanceof)(?![_$0-9A-Za-z])(?:(?=\\.\\.\\.)|(?!\\.))",beginCaptures:{1:{name:"keyword.operator.expression.instanceof.tsx"}},end:"(?<=\\))|(?=[;),}\\]:?\\-+>]|\\|\\||\\&\\&|!==|$|(===|!==|==|!=)|(([\\&\\~\\^\\|]\\s*)?[_$A-Za-z][_$0-9A-Za-z]*\\s+instanceof(?![_$0-9A-Za-z])(?:(?=\\.\\.\\.)|(?!\\.)))|((?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))function((\\s+[_$A-Za-z][_$0-9A-Za-z]*)|(\\s*[(]))))",patterns:[{include:"#type"}]},"interface-declaration":{begin:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(?:(\\bexport)\\s+)?(?:(\\bdeclare)\\s+)?\\b(?:(abstract)\\s+)?\\b(interface)\\b(?=\\s+|/[/*])",beginCaptures:{1:{name:"keyword.control.export.tsx"},2:{name:"storage.modifier.tsx"},3:{name:"storage.modifier.tsx"},4:{name:"storage.type.interface.tsx"}},end:"(?<=\\})",name:"meta.interface.tsx",patterns:[{include:"#comment"},{include:"#class-or-interface-heritage"},{captures:{0:{name:"entity.name.type.interface.tsx"}},match:"[_$A-Za-z][_$0-9A-Za-z]*"},{include:"#type-parameters"},{include:"#class-or-interface-body"}]},jsdoctype:{patterns:[{begin:"\\G({)",beginCaptures:{0:{name:"entity.name.type.instance.jsdoc"},1:{name:"punctuation.definition.bracket.curly.begin.jsdoc"}},contentName:"entity.name.type.instance.jsdoc",end:"((}))\\s*|(?=\\*/)",endCaptures:{1:{name:"entity.name.type.instance.jsdoc"},2:{name:"punctuation.definition.bracket.curly.end.jsdoc"}},patterns:[{include:"#brackets"}]}]},jsx:{patterns:[{include:"#jsx-tag-without-attributes-in-expression"},{include:"#jsx-tag-in-expression"}]},"jsx-children":{patterns:[{include:"#jsx-tag-without-attributes"},{include:"#jsx-tag"},{include:"#jsx-evaluated-code"},{include:"#jsx-entities"}]},"jsx-entities":{patterns:[{captures:{1:{name:"punctuation.definition.entity.tsx"},3:{name:"punctuation.definition.entity.tsx"}},match:"(&)([a-zA-Z0-9]+|#\\d+|#x[0-9a-fA-F]+)(;)",name:"constant.character.entity.tsx"}]},"jsx-evaluated-code":{begin:"\\{",beginCaptures:{0:{name:"punctuation.section.embedded.begin.tsx"}},contentName:"meta.embedded.expression.tsx",end:"\\}",endCaptures:{0:{name:"punctuation.section.embedded.end.tsx"}},patterns:[{include:"#expression"}]},"jsx-string-double-quoted":{begin:'"',beginCaptures:{0:{name:"punctuation.definition.string.begin.tsx"}},end:'"',endCaptures:{0:{name:"punctuation.definition.string.end.tsx"}},name:"string.quoted.double.tsx",patterns:[{include:"#jsx-entities"}]},"jsx-string-single-quoted":{begin:"'",beginCaptures:{0:{name:"punctuation.definition.string.begin.tsx"}},end:"'",endCaptures:{0:{name:"punctuation.definition.string.end.tsx"}},name:"string.quoted.single.tsx",patterns:[{include:"#jsx-entities"}]},"jsx-tag":{begin:"(?=(<)\\s*(?:([_$A-Za-z][-_$0-9A-Za-z.]*)(?<!\\.|-)(:))?((?:[a-z][a-z0-9]*|([_$A-Za-z][-_$0-9A-Za-z.]*))(?<!\\.|-))(?=((<\\s*)|(\\s+))(?!\\?)|\\/?>))",end:"(/>)|(?:(</)\\s*(?:([_$A-Za-z][-_$0-9A-Za-z.]*)(?<!\\.|-)(:))?((?:[a-z][a-z0-9]*|([_$A-Za-z][-_$0-9A-Za-z.]*))(?<!\\.|-))?\\s*(>))",endCaptures:{1:{name:"punctuation.definition.tag.end.tsx"},2:{name:"punctuation.definition.tag.begin.tsx"},3:{name:"entity.name.tag.namespace.tsx"},4:{name:"punctuation.separator.namespace.tsx"},5:{name:"entity.name.tag.tsx"},6:{name:"support.class.component.tsx"},7:{name:"punctuation.definition.tag.end.tsx"}},name:"meta.tag.tsx",patterns:[{begin:"(<)\\s*(?:([_$A-Za-z][-_$0-9A-Za-z.]*)(?<!\\.|-)(:))?((?:[a-z][a-z0-9]*|([_$A-Za-z][-_$0-9A-Za-z.]*))(?<!\\.|-))(?=((<\\s*)|(\\s+))(?!\\?)|\\/?>)",beginCaptures:{1:{name:"punctuation.definition.tag.begin.tsx"},2:{name:"entity.name.tag.namespace.tsx"},3:{name:"punctuation.separator.namespace.tsx"},4:{name:"entity.name.tag.tsx"},5:{name:"support.class.component.tsx"}},end:"(?=[/]?>)",patterns:[{include:"#comment"},{include:"#type-arguments"},{include:"#jsx-tag-attributes"}]},{begin:"(>)",beginCaptures:{1:{name:"punctuation.definition.tag.end.tsx"}},contentName:"meta.jsx.children.tsx",end:"(?=</)",patterns:[{include:"#jsx-children"}]}]},"jsx-tag-attribute-assignment":{match:`=(?=\\s*(?:'|"|{|/\\*|//|\\n))`,name:"keyword.operator.assignment.tsx"},"jsx-tag-attribute-name":{captures:{1:{name:"entity.other.attribute-name.namespace.tsx"},2:{name:"punctuation.separator.namespace.tsx"},3:{name:"entity.other.attribute-name.tsx"}},match:"\\s*(?:([_$A-Za-z][-_$0-9A-Za-z.]*)(:))?([_$A-Za-z][-_$0-9A-Za-z]*)(?=\\s|=|/?>|/\\*|//)"},"jsx-tag-attributes":{begin:"\\s+",end:"(?=[/]?>)",name:"meta.tag.attributes.tsx",patterns:[{include:"#comment"},{include:"#jsx-tag-attribute-name"},{include:"#jsx-tag-attribute-assignment"},{include:"#jsx-string-double-quoted"},{include:"#jsx-string-single-quoted"},{include:"#jsx-evaluated-code"},{include:"#jsx-tag-attributes-illegal"}]},"jsx-tag-attributes-illegal":{match:"\\S+",name:"invalid.illegal.attribute.tsx"},"jsx-tag-in-expression":{begin:"(?<!\\+\\+|--)(?<=[({\\[,?=>:*]|&&|\\|\\||\\?|\\*\\/|^await|[^\\._$0-9A-Za-z]await|^return|[^\\._$0-9A-Za-z]return|^default|[^\\._$0-9A-Za-z]default|^yield|[^\\._$0-9A-Za-z]yield|^)\\s*(?!<\\s*[_$A-Za-z][_$0-9A-Za-z]*((\\s+extends\\s+[^=>])|,))(?=(<)\\s*(?:([_$A-Za-z][-_$0-9A-Za-z.]*)(?<!\\.|-)(:))?((?:[a-z][a-z0-9]*|([_$A-Za-z][-_$0-9A-Za-z.]*))(?<!\\.|-))(?=((<\\s*)|(\\s+))(?!\\?)|\\/?>))",end:"(?!(<)\\s*(?:([_$A-Za-z][-_$0-9A-Za-z.]*)(?<!\\.|-)(:))?((?:[a-z][a-z0-9]*|([_$A-Za-z][-_$0-9A-Za-z.]*))(?<!\\.|-))(?=((<\\s*)|(\\s+))(?!\\?)|\\/?>))",patterns:[{include:"#jsx-tag"}]},"jsx-tag-without-attributes":{begin:"(<)\\s*(?:([_$A-Za-z][-_$0-9A-Za-z.]*)(?<!\\.|-)(:))?((?:[a-z][a-z0-9]*|([_$A-Za-z][-_$0-9A-Za-z.]*))(?<!\\.|-))?\\s*(>)",beginCaptures:{1:{name:"punctuation.definition.tag.begin.tsx"},2:{name:"entity.name.tag.namespace.tsx"},3:{name:"punctuation.separator.namespace.tsx"},4:{name:"entity.name.tag.tsx"},5:{name:"support.class.component.tsx"},6:{name:"punctuation.definition.tag.end.tsx"}},contentName:"meta.jsx.children.tsx",end:"(</)\\s*(?:([_$A-Za-z][-_$0-9A-Za-z.]*)(?<!\\.|-)(:))?((?:[a-z][a-z0-9]*|([_$A-Za-z][-_$0-9A-Za-z.]*))(?<!\\.|-))?\\s*(>)",endCaptures:{1:{name:"punctuation.definition.tag.begin.tsx"},2:{name:"entity.name.tag.namespace.tsx"},3:{name:"punctuation.separator.namespace.tsx"},4:{name:"entity.name.tag.tsx"},5:{name:"support.class.component.tsx"},6:{name:"punctuation.definition.tag.end.tsx"}},name:"meta.tag.without-attributes.tsx",patterns:[{include:"#jsx-children"}]},"jsx-tag-without-attributes-in-expression":{begin:"(?<!\\+\\+|--)(?<=[({\\[,?=>:*]|&&|\\|\\||\\?|\\*\\/|^await|[^\\._$0-9A-Za-z]await|^return|[^\\._$0-9A-Za-z]return|^default|[^\\._$0-9A-Za-z]default|^yield|[^\\._$0-9A-Za-z]yield|^)\\s*(?=(<)\\s*(?:([_$A-Za-z][-_$0-9A-Za-z.]*)(?<!\\.|-)(:))?((?:[a-z][a-z0-9]*|([_$A-Za-z][-_$0-9A-Za-z.]*))(?<!\\.|-))?\\s*(>))",end:"(?!(<)\\s*(?:([_$A-Za-z][-_$0-9A-Za-z.]*)(?<!\\.|-)(:))?((?:[a-z][a-z0-9]*|([_$A-Za-z][-_$0-9A-Za-z.]*))(?<!\\.|-))?\\s*(>))",patterns:[{include:"#jsx-tag-without-attributes"}]},label:{patterns:[{begin:"([_$A-Za-z][_$0-9A-Za-z]*)\\s*(:)(?=\\s*\\{)",beginCaptures:{1:{name:"entity.name.label.tsx"},2:{name:"punctuation.separator.label.tsx"}},end:"(?<=\\})",patterns:[{include:"#decl-block"}]},{captures:{1:{name:"entity.name.label.tsx"},2:{name:"punctuation.separator.label.tsx"}},match:"([_$A-Za-z][_$0-9A-Za-z]*)\\s*(:)"}]},literal:{patterns:[{include:"#numeric-literal"},{include:"#boolean-literal"},{include:"#null-literal"},{include:"#undefined-literal"},{include:"#numericConstant-literal"},{include:"#array-literal"},{include:"#this-literal"},{include:"#super-literal"}]},"method-declaration":{patterns:[{begin:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(?:\\b(override)\\s+)?(?:\\b(public|private|protected)\\s+)?(?:\\b(abstract)\\s+)?(?:\\b(async)\\s+)?\\s*\\b(constructor)\\b(?!:)(?![_$0-9A-Za-z])(?:(?=\\.\\.\\.)|(?!\\.))",beginCaptures:{1:{name:"storage.modifier.tsx"},2:{name:"storage.modifier.tsx"},3:{name:"storage.modifier.tsx"},4:{name:"storage.modifier.async.tsx"},5:{name:"storage.type.tsx"}},end:"(?=\\}|;|,|$)|(?<=\\})",name:"meta.method.declaration.tsx",patterns:[{include:"#method-declaration-name"},{include:"#function-body"}]},{begin:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(?:\\b(override)\\s+)?(?:\\b(public|private|protected)\\s+)?(?:\\b(abstract)\\s+)?(?:\\b(async)\\s+)?(?:(?:\\s*\\b(new)\\b(?!:)(?![_$0-9A-Za-z])(?:(?=\\.\\.\\.)|(?!\\.)))|(?:(\\*)\\s*)?)(?=\\s*((<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<]|<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<]|<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<])*>)*>)*>\\s*))?[(])",beginCaptures:{1:{name:"storage.modifier.tsx"},2:{name:"storage.modifier.tsx"},3:{name:"storage.modifier.tsx"},4:{name:"storage.modifier.async.tsx"},5:{name:"keyword.operator.new.tsx"},6:{name:"keyword.generator.asterisk.tsx"}},end:"(?=\\}|;|,|$)|(?<=\\})",name:"meta.method.declaration.tsx",patterns:[{include:"#method-declaration-name"},{include:"#function-body"}]},{begin:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(?:\\b(override)\\s+)?(?:\\b(public|private|protected)\\s+)?(?:\\b(abstract)\\s+)?(?:\\b(async)\\s+)?(?:\\b(get|set)\\s+)?(?:(\\*)\\s*)?(?=\\s*(((\\b(?<!\\$)0(?:x|X)[0-9a-fA-F][0-9a-fA-F_]*(n)?\\b(?!\\$))|(\\b(?<!\\$)0(?:b|B)[01][01_]*(n)?\\b(?!\\$))|(\\b(?<!\\$)0(?:o|O)?[0-7][0-7_]*(n)?\\b(?!\\$))|((?<!\\$)(?:(?:\\b\\d[0-9_]*(\\.)\\d[0-9_]*[eE][+-]?\\d[0-9_]*(n)?\\b)|(?:\\b\\d[0-9_]*(\\.)[eE][+-]?\\d[0-9_]*(n)?\\b)|(?:\\B(\\.)\\d[0-9_]*[eE][+-]?\\d[0-9_]*(n)?\\b)|(?:\\b\\d[0-9_]*[eE][+-]?\\d[0-9_]*(n)?\\b)|(?:\\b\\d[0-9_]*(\\.)\\d[0-9_]*(n)?\\b)|(?:\\b\\d[0-9_]*(\\.)(n)?\\B)|(?:\\B(\\.)\\d[0-9_]*(n)?\\b)|(?:\\b\\d[0-9_]*(n)?\\b(?!\\.)))(?!\\$))|([_$A-Za-z][_$0-9A-Za-z]*)|(\\'([^\\'\\\\]|\\\\.)*\\')|(\\\"([^\\\"\\\\]|\\\\.)*\\\")|(\\`([^\\`\\\\]|\\\\.)*\\`)|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])+\\]))\\s*(\\??))\\s*((<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<]|<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<]|<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<])*>)*>)*>\\s*))?[(])",beginCaptures:{1:{name:"storage.modifier.tsx"},2:{name:"storage.modifier.tsx"},3:{name:"storage.modifier.tsx"},4:{name:"storage.modifier.async.tsx"},5:{name:"storage.type.property.tsx"},6:{name:"keyword.generator.asterisk.tsx"}},end:"(?=\\}|;|,|$)|(?<=\\})",name:"meta.method.declaration.tsx",patterns:[{include:"#method-declaration-name"},{include:"#function-body"}]}]},"method-declaration-name":{begin:"(?=((\\b(?<!\\$)0(?:x|X)[0-9a-fA-F][0-9a-fA-F_]*(n)?\\b(?!\\$))|(\\b(?<!\\$)0(?:b|B)[01][01_]*(n)?\\b(?!\\$))|(\\b(?<!\\$)0(?:o|O)?[0-7][0-7_]*(n)?\\b(?!\\$))|((?<!\\$)(?:(?:\\b\\d[0-9_]*(\\.)\\d[0-9_]*[eE][+-]?\\d[0-9_]*(n)?\\b)|(?:\\b\\d[0-9_]*(\\.)[eE][+-]?\\d[0-9_]*(n)?\\b)|(?:\\B(\\.)\\d[0-9_]*[eE][+-]?\\d[0-9_]*(n)?\\b)|(?:\\b\\d[0-9_]*[eE][+-]?\\d[0-9_]*(n)?\\b)|(?:\\b\\d[0-9_]*(\\.)\\d[0-9_]*(n)?\\b)|(?:\\b\\d[0-9_]*(\\.)(n)?\\B)|(?:\\B(\\.)\\d[0-9_]*(n)?\\b)|(?:\\b\\d[0-9_]*(n)?\\b(?!\\.)))(?!\\$))|([_$A-Za-z][_$0-9A-Za-z]*)|(\\'([^\\'\\\\]|\\\\.)*\\')|(\\\"([^\\\"\\\\]|\\\\.)*\\\")|(\\`([^\\`\\\\]|\\\\.)*\\`)|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])+\\]))\\s*(\\??)\\s*[(<])",end:"(?=\\(|<)",patterns:[{include:"#string"},{include:"#array-literal"},{include:"#numeric-literal"},{match:"[_$A-Za-z][_$0-9A-Za-z]*",name:"meta.definition.method.tsx entity.name.function.tsx"},{match:"\\?",name:"keyword.operator.optional.tsx"}]},"namespace-declaration":{begin:"(?:(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(?:(\\bexport)\\s+)?(?:(\\bdeclare)\\s+)?\\b(namespace|module)\\s+(?=[_$A-Za-z\"'`]))",beginCaptures:{1:{name:"keyword.control.export.tsx"},2:{name:"storage.modifier.tsx"},3:{name:"storage.type.namespace.tsx"}},end:"(?<=\\})|(?=;|^\\s*$|(?:^\\s*(?:abstract|async|(?:\\bawait\\s+(?:\\busing(?=\\s+(?!in\\b|of\\b(?!\\s*(?:of\\b|=)))[_$A-Za-z])\\b)\\b)|break|case|catch|class|const|continue|declare|do|else|enum|export|finally|function|for|goto|if|import|interface|let|module|namespace|switch|return|throw|try|type|(?:\\busing(?=\\s+(?!in\\b|of\\b(?!\\s*(?:of\\b|=)))[_$A-Za-z])\\b)|var|while)\\b))",name:"meta.namespace.declaration.tsx",patterns:[{include:"#comment"},{include:"#string"},{match:"([_$A-Za-z][_$0-9A-Za-z]*)",name:"entity.name.type.module.tsx"},{include:"#punctuation-accessor"},{include:"#decl-block"}]},"new-expr":{begin:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(new)(?![_$0-9A-Za-z])(?:(?=\\.\\.\\.)|(?!\\.))",beginCaptures:{1:{name:"keyword.operator.new.tsx"}},end:"(?<=\\))|(?=[;),}\\]:?\\-+>]|\\|\\||\\&\\&|!==|$|((?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))new(?![_$0-9A-Za-z])(?:(?=\\.\\.\\.)|(?!\\.)))|((?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))function((\\s+[_$A-Za-z][_$0-9A-Za-z]*)|(\\s*[(]))))",name:"new.expr.tsx",patterns:[{include:"#expression"}]},"null-literal":{match:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))null(?![_$0-9A-Za-z])(?:(?=\\.\\.\\.)|(?!\\.))",name:"constant.language.null.tsx"},"numeric-literal":{patterns:[{captures:{1:{name:"storage.type.numeric.bigint.tsx"}},match:"\\b(?<!\\$)0(?:x|X)[0-9a-fA-F][0-9a-fA-F_]*(n)?\\b(?!\\$)",name:"constant.numeric.hex.tsx"},{captures:{1:{name:"storage.type.numeric.bigint.tsx"}},match:"\\b(?<!\\$)0(?:b|B)[01][01_]*(n)?\\b(?!\\$)",name:"constant.numeric.binary.tsx"},{captures:{1:{name:"storage.type.numeric.bigint.tsx"}},match:"\\b(?<!\\$)0(?:o|O)?[0-7][0-7_]*(n)?\\b(?!\\$)",name:"constant.numeric.octal.tsx"},{captures:{0:{name:"constant.numeric.decimal.tsx"},1:{name:"meta.delimiter.decimal.period.tsx"},2:{name:"storage.type.numeric.bigint.tsx"},3:{name:"meta.delimiter.decimal.period.tsx"},4:{name:"storage.type.numeric.bigint.tsx"},5:{name:"meta.delimiter.decimal.period.tsx"},6:{name:"storage.type.numeric.bigint.tsx"},7:{name:"storage.type.numeric.bigint.tsx"},8:{name:"meta.delimiter.decimal.period.tsx"},9:{name:"storage.type.numeric.bigint.tsx"},10:{name:"meta.delimiter.decimal.period.tsx"},11:{name:"storage.type.numeric.bigint.tsx"},12:{name:"meta.delimiter.decimal.period.tsx"},13:{name:"storage.type.numeric.bigint.tsx"},14:{name:"storage.type.numeric.bigint.tsx"}},match:"(?<!\\$)(?:(?:\\b\\d[0-9_]*(\\.)\\d[0-9_]*[eE][+-]?\\d[0-9_]*(n)?\\b)|(?:\\b\\d[0-9_]*(\\.)[eE][+-]?\\d[0-9_]*(n)?\\b)|(?:\\B(\\.)\\d[0-9_]*[eE][+-]?\\d[0-9_]*(n)?\\b)|(?:\\b\\d[0-9_]*[eE][+-]?\\d[0-9_]*(n)?\\b)|(?:\\b\\d[0-9_]*(\\.)\\d[0-9_]*(n)?\\b)|(?:\\b\\d[0-9_]*(\\.)(n)?\\B)|(?:\\B(\\.)\\d[0-9_]*(n)?\\b)|(?:\\b\\d[0-9_]*(n)?\\b(?!\\.)))(?!\\$)"}]},"numericConstant-literal":{patterns:[{match:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))NaN(?![_$0-9A-Za-z])(?:(?=\\.\\.\\.)|(?!\\.))",name:"constant.language.nan.tsx"},{match:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))Infinity(?![_$0-9A-Za-z])(?:(?=\\.\\.\\.)|(?!\\.))",name:"constant.language.infinity.tsx"}]},"object-binding-element":{patterns:[{include:"#comment"},{begin:"(?=((\\b(?<!\\$)0(?:x|X)[0-9a-fA-F][0-9a-fA-F_]*(n)?\\b(?!\\$))|(\\b(?<!\\$)0(?:b|B)[01][01_]*(n)?\\b(?!\\$))|(\\b(?<!\\$)0(?:o|O)?[0-7][0-7_]*(n)?\\b(?!\\$))|((?<!\\$)(?:(?:\\b\\d[0-9_]*(\\.)\\d[0-9_]*[eE][+-]?\\d[0-9_]*(n)?\\b)|(?:\\b\\d[0-9_]*(\\.)[eE][+-]?\\d[0-9_]*(n)?\\b)|(?:\\B(\\.)\\d[0-9_]*[eE][+-]?\\d[0-9_]*(n)?\\b)|(?:\\b\\d[0-9_]*[eE][+-]?\\d[0-9_]*(n)?\\b)|(?:\\b\\d[0-9_]*(\\.)\\d[0-9_]*(n)?\\b)|(?:\\b\\d[0-9_]*(\\.)(n)?\\B)|(?:\\B(\\.)\\d[0-9_]*(n)?\\b)|(?:\\b\\d[0-9_]*(n)?\\b(?!\\.)))(?!\\$))|([_$A-Za-z][_$0-9A-Za-z]*)|(\\'([^\\'\\\\]|\\\\.)*\\')|(\\\"([^\\\"\\\\]|\\\\.)*\\\")|(\\`([^\\`\\\\]|\\\\.)*\\`)|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])+\\]))\\s*(:))",end:"(?=,|\\})",patterns:[{include:"#object-binding-element-propertyName"},{include:"#binding-element"}]},{include:"#object-binding-pattern"},{include:"#destructuring-variable-rest"},{include:"#variable-initializer"},{include:"#punctuation-comma"}]},"object-binding-element-const":{patterns:[{include:"#comment"},{begin:"(?=((\\b(?<!\\$)0(?:x|X)[0-9a-fA-F][0-9a-fA-F_]*(n)?\\b(?!\\$))|(\\b(?<!\\$)0(?:b|B)[01][01_]*(n)?\\b(?!\\$))|(\\b(?<!\\$)0(?:o|O)?[0-7][0-7_]*(n)?\\b(?!\\$))|((?<!\\$)(?:(?:\\b\\d[0-9_]*(\\.)\\d[0-9_]*[eE][+-]?\\d[0-9_]*(n)?\\b)|(?:\\b\\d[0-9_]*(\\.)[eE][+-]?\\d[0-9_]*(n)?\\b)|(?:\\B(\\.)\\d[0-9_]*[eE][+-]?\\d[0-9_]*(n)?\\b)|(?:\\b\\d[0-9_]*[eE][+-]?\\d[0-9_]*(n)?\\b)|(?:\\b\\d[0-9_]*(\\.)\\d[0-9_]*(n)?\\b)|(?:\\b\\d[0-9_]*(\\.)(n)?\\B)|(?:\\B(\\.)\\d[0-9_]*(n)?\\b)|(?:\\b\\d[0-9_]*(n)?\\b(?!\\.)))(?!\\$))|([_$A-Za-z][_$0-9A-Za-z]*)|(\\'([^\\'\\\\]|\\\\.)*\\')|(\\\"([^\\\"\\\\]|\\\\.)*\\\")|(\\`([^\\`\\\\]|\\\\.)*\\`)|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])+\\]))\\s*(:))",end:"(?=,|\\})",patterns:[{include:"#object-binding-element-propertyName"},{include:"#binding-element-const"}]},{include:"#object-binding-pattern-const"},{include:"#destructuring-variable-rest-const"},{include:"#variable-initializer"},{include:"#punctuation-comma"}]},"object-binding-element-propertyName":{begin:"(?=((\\b(?<!\\$)0(?:x|X)[0-9a-fA-F][0-9a-fA-F_]*(n)?\\b(?!\\$))|(\\b(?<!\\$)0(?:b|B)[01][01_]*(n)?\\b(?!\\$))|(\\b(?<!\\$)0(?:o|O)?[0-7][0-7_]*(n)?\\b(?!\\$))|((?<!\\$)(?:(?:\\b\\d[0-9_]*(\\.)\\d[0-9_]*[eE][+-]?\\d[0-9_]*(n)?\\b)|(?:\\b\\d[0-9_]*(\\.)[eE][+-]?\\d[0-9_]*(n)?\\b)|(?:\\B(\\.)\\d[0-9_]*[eE][+-]?\\d[0-9_]*(n)?\\b)|(?:\\b\\d[0-9_]*[eE][+-]?\\d[0-9_]*(n)?\\b)|(?:\\b\\d[0-9_]*(\\.)\\d[0-9_]*(n)?\\b)|(?:\\b\\d[0-9_]*(\\.)(n)?\\B)|(?:\\B(\\.)\\d[0-9_]*(n)?\\b)|(?:\\b\\d[0-9_]*(n)?\\b(?!\\.)))(?!\\$))|([_$A-Za-z][_$0-9A-Za-z]*)|(\\'([^\\'\\\\]|\\\\.)*\\')|(\\\"([^\\\"\\\\]|\\\\.)*\\\")|(\\`([^\\`\\\\]|\\\\.)*\\`)|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])+\\]))\\s*(:))",end:"(:)",endCaptures:{0:{name:"punctuation.destructuring.tsx"}},patterns:[{include:"#string"},{include:"#array-literal"},{include:"#numeric-literal"},{match:"([_$A-Za-z][_$0-9A-Za-z]*)",name:"variable.object.property.tsx"}]},"object-binding-pattern":{begin:"(?:(\\.\\.\\.)\\s*)?(\\{)",beginCaptures:{1:{name:"keyword.operator.rest.tsx"},2:{name:"punctuation.definition.binding-pattern.object.tsx"}},end:"\\}",endCaptures:{0:{name:"punctuation.definition.binding-pattern.object.tsx"}},patterns:[{include:"#object-binding-element"}]},"object-binding-pattern-const":{begin:"(?:(\\.\\.\\.)\\s*)?(\\{)",beginCaptures:{1:{name:"keyword.operator.rest.tsx"},2:{name:"punctuation.definition.binding-pattern.object.tsx"}},end:"\\}",endCaptures:{0:{name:"punctuation.definition.binding-pattern.object.tsx"}},patterns:[{include:"#object-binding-element-const"}]},"object-identifiers":{patterns:[{match:"([_$A-Za-z][_$0-9A-Za-z]*)(?=\\s*\\??\\.\\s*prototype\\b(?!\\$))",name:"support.class.tsx"},{captures:{1:{name:"punctuation.accessor.tsx"},2:{name:"punctuation.accessor.optional.tsx"},3:{name:"variable.other.constant.object.property.tsx"},4:{name:"variable.other.object.property.tsx"}},match:"(?:(\\.)|(\\?\\.(?!\\s*[\\d])))\\s*(?:(\\#?[A-Z][_$\\dA-Z]*)|(\\#?[_$A-Za-z][_$0-9A-Za-z]*))(?=\\s*\\??\\.\\s*\\#?[_$A-Za-z][_$0-9A-Za-z]*)"},{captures:{1:{name:"variable.other.constant.object.tsx"},2:{name:"variable.other.object.tsx"}},match:"(?:([A-Z][_$\\dA-Z]*)|([_$A-Za-z][_$0-9A-Za-z]*))(?=\\s*\\??\\.\\s*\\#?[_$A-Za-z][_$0-9A-Za-z]*)"}]},"object-literal":{begin:"\\{",beginCaptures:{0:{name:"punctuation.definition.block.tsx"}},end:"\\}",endCaptures:{0:{name:"punctuation.definition.block.tsx"}},name:"meta.objectliteral.tsx",patterns:[{include:"#object-member"}]},"object-literal-method-declaration":{begin:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(?:\\b(async)\\s+)?(?:\\b(get|set)\\s+)?(?:(\\*)\\s*)?(?=\\s*(((\\b(?<!\\$)0(?:x|X)[0-9a-fA-F][0-9a-fA-F_]*(n)?\\b(?!\\$))|(\\b(?<!\\$)0(?:b|B)[01][01_]*(n)?\\b(?!\\$))|(\\b(?<!\\$)0(?:o|O)?[0-7][0-7_]*(n)?\\b(?!\\$))|((?<!\\$)(?:(?:\\b\\d[0-9_]*(\\.)\\d[0-9_]*[eE][+-]?\\d[0-9_]*(n)?\\b)|(?:\\b\\d[0-9_]*(\\.)[eE][+-]?\\d[0-9_]*(n)?\\b)|(?:\\B(\\.)\\d[0-9_]*[eE][+-]?\\d[0-9_]*(n)?\\b)|(?:\\b\\d[0-9_]*[eE][+-]?\\d[0-9_]*(n)?\\b)|(?:\\b\\d[0-9_]*(\\.)\\d[0-9_]*(n)?\\b)|(?:\\b\\d[0-9_]*(\\.)(n)?\\B)|(?:\\B(\\.)\\d[0-9_]*(n)?\\b)|(?:\\b\\d[0-9_]*(n)?\\b(?!\\.)))(?!\\$))|([_$A-Za-z][_$0-9A-Za-z]*)|(\\'([^\\'\\\\]|\\\\.)*\\')|(\\\"([^\\\"\\\\]|\\\\.)*\\\")|(\\`([^\\`\\\\]|\\\\.)*\\`)|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])+\\]))\\s*(\\??))\\s*((<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<]|<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<]|<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<])*>)*>)*>\\s*))?[(])",beginCaptures:{1:{name:"storage.modifier.async.tsx"},2:{name:"storage.type.property.tsx"},3:{name:"keyword.generator.asterisk.tsx"}},end:"(?=\\}|;|,)|(?<=\\})",name:"meta.method.declaration.tsx",patterns:[{include:"#method-declaration-name"},{include:"#function-body"},{begin:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(?:\\b(async)\\s+)?(?:\\b(get|set)\\s+)?(?:(\\*)\\s*)?(?=\\s*(((\\b(?<!\\$)0(?:x|X)[0-9a-fA-F][0-9a-fA-F_]*(n)?\\b(?!\\$))|(\\b(?<!\\$)0(?:b|B)[01][01_]*(n)?\\b(?!\\$))|(\\b(?<!\\$)0(?:o|O)?[0-7][0-7_]*(n)?\\b(?!\\$))|((?<!\\$)(?:(?:\\b\\d[0-9_]*(\\.)\\d[0-9_]*[eE][+-]?\\d[0-9_]*(n)?\\b)|(?:\\b\\d[0-9_]*(\\.)[eE][+-]?\\d[0-9_]*(n)?\\b)|(?:\\B(\\.)\\d[0-9_]*[eE][+-]?\\d[0-9_]*(n)?\\b)|(?:\\b\\d[0-9_]*[eE][+-]?\\d[0-9_]*(n)?\\b)|(?:\\b\\d[0-9_]*(\\.)\\d[0-9_]*(n)?\\b)|(?:\\b\\d[0-9_]*(\\.)(n)?\\B)|(?:\\B(\\.)\\d[0-9_]*(n)?\\b)|(?:\\b\\d[0-9_]*(n)?\\b(?!\\.)))(?!\\$))|([_$A-Za-z][_$0-9A-Za-z]*)|(\\'([^\\'\\\\]|\\\\.)*\\')|(\\\"([^\\\"\\\\]|\\\\.)*\\\")|(\\`([^\\`\\\\]|\\\\.)*\\`)|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])+\\]))\\s*(\\??))\\s*((<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<]|<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<]|<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<])*>)*>)*>\\s*))?[(])",beginCaptures:{1:{name:"storage.modifier.async.tsx"},2:{name:"storage.type.property.tsx"},3:{name:"keyword.generator.asterisk.tsx"}},end:"(?=\\(|<)",patterns:[{include:"#method-declaration-name"}]}]},"object-member":{patterns:[{include:"#comment"},{include:"#object-literal-method-declaration"},{begin:"(?=\\[)",end:"(?=:)|((?<=[\\]])(?=\\s*[(<]))",name:"meta.object.member.tsx meta.object-literal.key.tsx",patterns:[{include:"#comment"},{include:"#array-literal"}]},{begin:"(?=[\\'\\\"\\`])",end:"(?=:)|((?<=[\\'\\\"\\`])(?=((\\s*[(<,}])|(\\s+(as|satisifies)\\s+))))",name:"meta.object.member.tsx meta.object-literal.key.tsx",patterns:[{include:"#comment"},{include:"#string"}]},{begin:"(?=(\\b(?<!\\$)0(?:x|X)[0-9a-fA-F][0-9a-fA-F_]*(n)?\\b(?!\\$))|(\\b(?<!\\$)0(?:b|B)[01][01_]*(n)?\\b(?!\\$))|(\\b(?<!\\$)0(?:o|O)?[0-7][0-7_]*(n)?\\b(?!\\$))|((?<!\\$)(?:(?:\\b\\d[0-9_]*(\\.)\\d[0-9_]*[eE][+-]?\\d[0-9_]*(n)?\\b)|(?:\\b\\d[0-9_]*(\\.)[eE][+-]?\\d[0-9_]*(n)?\\b)|(?:\\B(\\.)\\d[0-9_]*[eE][+-]?\\d[0-9_]*(n)?\\b)|(?:\\b\\d[0-9_]*[eE][+-]?\\d[0-9_]*(n)?\\b)|(?:\\b\\d[0-9_]*(\\.)\\d[0-9_]*(n)?\\b)|(?:\\b\\d[0-9_]*(\\.)(n)?\\B)|(?:\\B(\\.)\\d[0-9_]*(n)?\\b)|(?:\\b\\d[0-9_]*(n)?\\b(?!\\.)))(?!\\$)))",end:"(?=:)|(?=\\s*([(<,}])|(\\s+as|satisifies\\s+))",name:"meta.object.member.tsx meta.object-literal.key.tsx",patterns:[{include:"#comment"},{include:"#numeric-literal"}]},{begin:"(?<=[\\]\\'\\\"\\`])(?=\\s*[(<])",end:"(?=\\}|;|,)|(?<=\\})",name:"meta.method.declaration.tsx",patterns:[{include:"#function-body"}]},{captures:{0:{name:"meta.object-literal.key.tsx"},1:{name:"constant.numeric.decimal.tsx"}},match:"(?![_$A-Za-z])([\\d]+)\\s*(?=(\\/\\*([^\\*]|(\\*[^\\/]))*\\*\\/\\s*)*:)",name:"meta.object.member.tsx"},{captures:{0:{name:"meta.object-literal.key.tsx"},1:{name:"entity.name.function.tsx"}},match:"(?:([_$A-Za-z][_$0-9A-Za-z]*)\\s*(?=(\\/\\*([^\\*]|(\\*[^\\/]))*\\*\\/\\s*)*:(\\s*\\/\\*([^\\*]|(\\*[^\\/]))*\\*\\/)*\\s*(((async\\s+)?((function\\s*[(<*])|(function\\s+)|([_$A-Za-z][_$0-9A-Za-z]*\\s*=>)))|((async\\s*)?(((<\\s*$)|([(]\\s*((([{\\[]\\s*)?$)|((\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})\\s*((:\\s*\\{?$)|((\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\([^()]+\\)|\\{[^{}]+\\})+\\s*)?=\\s*)))|((\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\])\\s*((:\\s*\\[?$)|((\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\([^()]+\\)|\\{[^{}]+\\})+\\s*)?=\\s*))))))|((<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<]|<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<]|<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<])*>)*>)*>\\s*)?[(]\\s*(\\/\\*([^\\*]|(\\*[^\\/]))*\\*\\/\\s*)*(([)]\\s*:)|((\\.\\.\\.\\s*)?[_$A-Za-z][_$0-9A-Za-z]*\\s*:)))|([<]\\s*[_$A-Za-z][_$0-9A-Za-z]*\\s+extends\\s*[^=>])|((<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<]|<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<]|<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<])*>)*>)*>\\s*)?\\(\\s*(\\/\\*([^\\*]|(\\*[^\\/]))*\\*\\/\\s*)*(([_$A-Za-z]|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\])|(\\.\\.\\.\\s*[_$A-Za-z]))([^()\\'\\\"\\`]|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\'([^\\'\\\\]|\\\\.)*\\')|(\\\"([^\\\"\\\\]|\\\\.)*\\\")|(\\`([^\\`\\\\]|\\\\.)*\\`))*)?\\)(\\s*:\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\([^()]+\\)|\\{[^{}]+\\})+)?\\s*=>))))))",name:"meta.object.member.tsx"},{captures:{0:{name:"meta.object-literal.key.tsx"}},match:"(?:[_$A-Za-z][_$0-9A-Za-z]*)\\s*(?=(\\/\\*([^\\*]|(\\*[^\\/]))*\\*\\/\\s*)*:)",name:"meta.object.member.tsx"},{begin:"\\.\\.\\.",beginCaptures:{0:{name:"keyword.operator.spread.tsx"}},end:"(?=,|\\})",name:"meta.object.member.tsx",patterns:[{include:"#expression"}]},{captures:{1:{name:"variable.other.readwrite.tsx"}},match:"([_$A-Za-z][_$0-9A-Za-z]*)\\s*(?=,|\\}|$|\\/\\/|\\/\\*)",name:"meta.object.member.tsx"},{captures:{1:{name:"keyword.control.as.tsx"},2:{name:"storage.modifier.tsx"}},match:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(as)\\s+(const)(?=\\s*([,}]|$))",name:"meta.object.member.tsx"},{begin:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(?:(as)|(satisfies))\\s+",beginCaptures:{1:{name:"keyword.control.as.tsx"},2:{name:"keyword.control.satisfies.tsx"}},end:"(?=[;),}\\]:?\\-+>]|\\|\\||\\&\\&|!==|$|^|((?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(as|satisifies)\\s+))",name:"meta.object.member.tsx",patterns:[{include:"#type"}]},{begin:"(?=[_$A-Za-z][_$0-9A-Za-z]*\\s*=)",end:"(?=,|\\}|$|\\/\\/|\\/\\*)",name:"meta.object.member.tsx",patterns:[{include:"#expression"}]},{begin:":",beginCaptures:{0:{name:"meta.object-literal.key.tsx punctuation.separator.key-value.tsx"}},end:"(?=,|\\})",name:"meta.object.member.tsx",patterns:[{begin:"(?<=:)\\s*(async)?(?=\\s*(<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<]|<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<]|<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<])*>)*>)*>\\s*)\\(\\s*((([{\\[]\\s*)?$)|((\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})\\s*((:\\s*\\{?$)|((\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\([^()]+\\)|\\{[^{}]+\\})+\\s*)?=\\s*)))|((\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\])\\s*((:\\s*\\[?$)|((\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\([^()]+\\)|\\{[^{}]+\\})+\\s*)?=\\s*)))))",beginCaptures:{1:{name:"storage.modifier.async.tsx"}},end:"(?<=\\))",patterns:[{include:"#type-parameters"},{begin:"\\(",beginCaptures:{0:{name:"meta.brace.round.tsx"}},end:"\\)",endCaptures:{0:{name:"meta.brace.round.tsx"}},patterns:[{include:"#expression-inside-possibly-arrow-parens"}]}]},{begin:"(?<=:)\\s*(async)?\\s*(\\()(?=\\s*((([{\\[]\\s*)?$)|((\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})\\s*((:\\s*\\{?$)|((\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\([^()]+\\)|\\{[^{}]+\\})+\\s*)?=\\s*)))|((\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\])\\s*((:\\s*\\[?$)|((\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\([^()]+\\)|\\{[^{}]+\\})+\\s*)?=\\s*)))))",beginCaptures:{1:{name:"storage.modifier.async.tsx"},2:{name:"meta.brace.round.tsx"}},end:"\\)",endCaptures:{0:{name:"meta.brace.round.tsx"}},patterns:[{include:"#expression-inside-possibly-arrow-parens"}]},{begin:"(?<=:)\\s*(async)?\\s*(?=<\\s*$)",beginCaptures:{1:{name:"storage.modifier.async.tsx"}},end:"(?<=>)",patterns:[{include:"#type-parameters"}]},{begin:"(?<=>)\\s*(\\()(?=\\s*((([{\\[]\\s*)?$)|((\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})\\s*((:\\s*\\{?$)|((\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\([^()]+\\)|\\{[^{}]+\\})+\\s*)?=\\s*)))|((\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\])\\s*((:\\s*\\[?$)|((\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\([^()]+\\)|\\{[^{}]+\\})+\\s*)?=\\s*)))))",beginCaptures:{1:{name:"meta.brace.round.tsx"}},end:"\\)",endCaptures:{0:{name:"meta.brace.round.tsx"}},patterns:[{include:"#expression-inside-possibly-arrow-parens"}]},{include:"#possibly-arrow-return-type"},{include:"#expression"}]},{include:"#punctuation-comma"},{include:"#decl-block"}]},"parameter-array-binding-pattern":{begin:"(?:(\\.\\.\\.)\\s*)?(\\[)",beginCaptures:{1:{name:"keyword.operator.rest.tsx"},2:{name:"punctuation.definition.binding-pattern.array.tsx"}},end:"\\]",endCaptures:{0:{name:"punctuation.definition.binding-pattern.array.tsx"}},patterns:[{include:"#parameter-binding-element"},{include:"#punctuation-comma"}]},"parameter-binding-element":{patterns:[{include:"#comment"},{include:"#string"},{include:"#numeric-literal"},{include:"#regex"},{include:"#parameter-object-binding-pattern"},{include:"#parameter-array-binding-pattern"},{include:"#destructuring-parameter-rest"},{include:"#variable-initializer"}]},"parameter-name":{patterns:[{captures:{1:{name:"storage.modifier.tsx"}},match:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(override|public|protected|private|readonly)\\s+(?=(override|public|protected|private|readonly)\\s+)"},{captures:{1:{name:"storage.modifier.tsx"},2:{name:"keyword.operator.rest.tsx"},3:{name:"entity.name.function.tsx variable.language.this.tsx"},4:{name:"entity.name.function.tsx"},5:{name:"keyword.operator.optional.tsx"}},match:"(?:(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(override|public|private|protected|readonly)\\s+)?(?:(\\.\\.\\.)\\s*)?(?<!=|:)(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(?:(this)|([_$A-Za-z][_$0-9A-Za-z]*))(?![_$0-9A-Za-z])(?:(?=\\.\\.\\.)|(?!\\.))\\s*(\\??)(?=\\s*(=\\s*(((async\\s+)?((function\\s*[(<*])|(function\\s+)|([_$A-Za-z][_$0-9A-Za-z]*\\s*=>)))|((async\\s*)?(((<\\s*$)|([(]\\s*((([{\\[]\\s*)?$)|((\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})\\s*((:\\s*\\{?$)|((\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\([^()]+\\)|\\{[^{}]+\\})+\\s*)?=\\s*)))|((\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\])\\s*((:\\s*\\[?$)|((\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\([^()]+\\)|\\{[^{}]+\\})+\\s*)?=\\s*))))))|((<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<]|<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<]|<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<])*>)*>)*>\\s*)?[(]\\s*(\\/\\*([^\\*]|(\\*[^\\/]))*\\*\\/\\s*)*(([)]\\s*:)|((\\.\\.\\.\\s*)?[_$A-Za-z][_$0-9A-Za-z]*\\s*:)))|([<]\\s*[_$A-Za-z][_$0-9A-Za-z]*\\s+extends\\s*[^=>])|((<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<]|<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<]|<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<])*>)*>)*>\\s*)?\\(\\s*(\\/\\*([^\\*]|(\\*[^\\/]))*\\*\\/\\s*)*(([_$A-Za-z]|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\])|(\\.\\.\\.\\s*[_$A-Za-z]))([^()\\'\\\"\\`]|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\'([^\\'\\\\]|\\\\.)*\\')|(\\\"([^\\\"\\\\]|\\\\.)*\\\")|(\\`([^\\`\\\\]|\\\\.)*\\`))*)?\\)(\\s*:\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\([^()]+\\)|\\{[^{}]+\\})+)?\\s*=>)))))|(:\\s*((<)|([(]\\s*(([)])|(\\.\\.\\.)|([_$0-9A-Za-z]+\\s*(([:,?=])|([)]\\s*=>)))))))|(:\\s*(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))Function(?![_$0-9A-Za-z])(?:(?=\\.\\.\\.)|(?!\\.)))|(:\\s*((<\\s*$)|([(]\\s*((([{\\[]\\s*)?$)|((\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})\\s*((:\\s*\\{?$)|((\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\([^()]+\\)|\\{[^{}]+\\})+\\s*)?=\\s*)))|((\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\])\\s*((:\\s*\\[?$)|((\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\([^()]+\\)|\\{[^{}]+\\})+\\s*)?=\\s*)))))))|(:\\s*(=>|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(<[^<>]*>)|[^<>(),=])+=\\s*(((async\\s+)?((function\\s*[(<*])|(function\\s+)|([_$A-Za-z][_$0-9A-Za-z]*\\s*=>)))|((async\\s*)?(((<\\s*$)|([(]\\s*((([{\\[]\\s*)?$)|((\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})\\s*((:\\s*\\{?$)|((\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\([^()]+\\)|\\{[^{}]+\\})+\\s*)?=\\s*)))|((\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\])\\s*((:\\s*\\[?$)|((\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\([^()]+\\)|\\{[^{}]+\\})+\\s*)?=\\s*))))))|((<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<]|<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<]|<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<])*>)*>)*>\\s*)?[(]\\s*(\\/\\*([^\\*]|(\\*[^\\/]))*\\*\\/\\s*)*(([)]\\s*:)|((\\.\\.\\.\\s*)?[_$A-Za-z][_$0-9A-Za-z]*\\s*:)))|([<]\\s*[_$A-Za-z][_$0-9A-Za-z]*\\s+extends\\s*[^=>])|((<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<]|<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<]|<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<])*>)*>)*>\\s*)?\\(\\s*(\\/\\*([^\\*]|(\\*[^\\/]))*\\*\\/\\s*)*(([_$A-Za-z]|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\])|(\\.\\.\\.\\s*[_$A-Za-z]))([^()\\'\\\"\\`]|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\'([^\\'\\\\]|\\\\.)*\\')|(\\\"([^\\\"\\\\]|\\\\.)*\\\")|(\\`([^\\`\\\\]|\\\\.)*\\`))*)?\\)(\\s*:\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\([^()]+\\)|\\{[^{}]+\\})+)?\\s*=>))))))"},{captures:{1:{name:"storage.modifier.tsx"},2:{name:"keyword.operator.rest.tsx"},3:{name:"variable.parameter.tsx variable.language.this.tsx"},4:{name:"variable.parameter.tsx"},5:{name:"keyword.operator.optional.tsx"}},match:"(?:(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(override|public|private|protected|readonly)\\s+)?(?:(\\.\\.\\.)\\s*)?(?<!=|:)(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(?:(this)|([_$A-Za-z][_$0-9A-Za-z]*))(?![_$0-9A-Za-z])(?:(?=\\.\\.\\.)|(?!\\.))\\s*(\\??)"}]},"parameter-object-binding-element":{patterns:[{include:"#comment"},{begin:"(?=((\\b(?<!\\$)0(?:x|X)[0-9a-fA-F][0-9a-fA-F_]*(n)?\\b(?!\\$))|(\\b(?<!\\$)0(?:b|B)[01][01_]*(n)?\\b(?!\\$))|(\\b(?<!\\$)0(?:o|O)?[0-7][0-7_]*(n)?\\b(?!\\$))|((?<!\\$)(?:(?:\\b\\d[0-9_]*(\\.)\\d[0-9_]*[eE][+-]?\\d[0-9_]*(n)?\\b)|(?:\\b\\d[0-9_]*(\\.)[eE][+-]?\\d[0-9_]*(n)?\\b)|(?:\\B(\\.)\\d[0-9_]*[eE][+-]?\\d[0-9_]*(n)?\\b)|(?:\\b\\d[0-9_]*[eE][+-]?\\d[0-9_]*(n)?\\b)|(?:\\b\\d[0-9_]*(\\.)\\d[0-9_]*(n)?\\b)|(?:\\b\\d[0-9_]*(\\.)(n)?\\B)|(?:\\B(\\.)\\d[0-9_]*(n)?\\b)|(?:\\b\\d[0-9_]*(n)?\\b(?!\\.)))(?!\\$))|([_$A-Za-z][_$0-9A-Za-z]*)|(\\'([^\\'\\\\]|\\\\.)*\\')|(\\\"([^\\\"\\\\]|\\\\.)*\\\")|(\\`([^\\`\\\\]|\\\\.)*\\`)|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])+\\]))\\s*(:))",end:"(?=,|\\})",patterns:[{include:"#object-binding-element-propertyName"},{include:"#parameter-binding-element"},{include:"#paren-expression"}]},{include:"#parameter-object-binding-pattern"},{include:"#destructuring-parameter-rest"},{include:"#variable-initializer"},{include:"#punctuation-comma"}]},"parameter-object-binding-pattern":{begin:"(?:(\\.\\.\\.)\\s*)?(\\{)",beginCaptures:{1:{name:"keyword.operator.rest.tsx"},2:{name:"punctuation.definition.binding-pattern.object.tsx"}},end:"\\}",endCaptures:{0:{name:"punctuation.definition.binding-pattern.object.tsx"}},patterns:[{include:"#parameter-object-binding-element"}]},"parameter-type-annotation":{patterns:[{begin:"(:)",beginCaptures:{1:{name:"keyword.operator.type.annotation.tsx"}},end:"(?=[,)])|(?==[^>])",name:"meta.type.annotation.tsx",patterns:[{include:"#type"}]}]},"paren-expression":{begin:"\\(",beginCaptures:{0:{name:"meta.brace.round.tsx"}},end:"\\)",endCaptures:{0:{name:"meta.brace.round.tsx"}},patterns:[{include:"#expression"}]},"paren-expression-possibly-arrow":{patterns:[{begin:"(?<=[(=,])\\s*(async)?(?=\\s*((<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<]|<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<]|<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<])*>)*>)*>\\s*))?\\(\\s*((([{\\[]\\s*)?$)|((\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})\\s*((:\\s*\\{?$)|((\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\([^()]+\\)|\\{[^{}]+\\})+\\s*)?=\\s*)))|((\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\])\\s*((:\\s*\\[?$)|((\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\([^()]+\\)|\\{[^{}]+\\})+\\s*)?=\\s*)))))",beginCaptures:{1:{name:"storage.modifier.async.tsx"}},end:"(?<=\\))",patterns:[{include:"#paren-expression-possibly-arrow-with-typeparameters"}]},{begin:"(?<=[(=,]|=>|^return|[^\\._$0-9A-Za-z]return)\\s*(async)?(?=\\s*((((<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<]|<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<]|<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<])*>)*>)*>\\s*))?\\()|(<)|((<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<]|<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<]|<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<])*>)*>)*>\\s*)))\\s*$)",beginCaptures:{1:{name:"storage.modifier.async.tsx"}},end:"(?<=\\))",patterns:[{include:"#paren-expression-possibly-arrow-with-typeparameters"}]},{include:"#possibly-arrow-return-type"}]},"paren-expression-possibly-arrow-with-typeparameters":{patterns:[{include:"#type-parameters"},{begin:"\\(",beginCaptures:{0:{name:"meta.brace.round.tsx"}},end:"\\)",endCaptures:{0:{name:"meta.brace.round.tsx"}},patterns:[{include:"#expression-inside-possibly-arrow-parens"}]}]},"possibly-arrow-return-type":{begin:"(?<=\\)|^)\\s*(:)(?=\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\([^()]+\\)|\\{[^{}]+\\})+\\s*=>)",beginCaptures:{1:{name:"meta.arrow.tsx meta.return.type.arrow.tsx keyword.operator.type.annotation.tsx"}},contentName:"meta.arrow.tsx meta.return.type.arrow.tsx",end:"(?==>|\\{|(^\\s*(export|function|class|interface|let|var|(?:\\busing(?=\\s+(?!in\\b|of\\b(?!\\s*(?:of\\b|=)))[_$A-Za-z])\\b)|(?:\\bawait\\s+(?:\\busing(?=\\s+(?!in\\b|of\\b(?!\\s*(?:of\\b|=)))[_$A-Za-z])\\b)\\b)|const|import|enum|namespace|module|type|abstract|declare)\\s+))",patterns:[{include:"#arrow-return-type-body"}]},"property-accessor":{match:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(accessor|get|set)(?![_$0-9A-Za-z])(?:(?=\\.\\.\\.)|(?!\\.))",name:"storage.type.property.tsx"},"punctuation-accessor":{captures:{1:{name:"punctuation.accessor.tsx"},2:{name:"punctuation.accessor.optional.tsx"}},match:"(?:(\\.)|(\\?\\.(?!\\s*[\\d])))"},"punctuation-comma":{match:",",name:"punctuation.separator.comma.tsx"},"punctuation-semicolon":{match:";",name:"punctuation.terminator.statement.tsx"},"qstring-double":{begin:'"',beginCaptures:{0:{name:"punctuation.definition.string.begin.tsx"}},end:'(")|((?:[^\\\\\\n])$)',endCaptures:{1:{name:"punctuation.definition.string.end.tsx"},2:{name:"invalid.illegal.newline.tsx"}},name:"string.quoted.double.tsx",patterns:[{include:"#string-character-escape"}]},"qstring-single":{begin:"'",beginCaptures:{0:{name:"punctuation.definition.string.begin.tsx"}},end:"(\\')|((?:[^\\\\\\n])$)",endCaptures:{1:{name:"punctuation.definition.string.end.tsx"},2:{name:"invalid.illegal.newline.tsx"}},name:"string.quoted.single.tsx",patterns:[{include:"#string-character-escape"}]},regex:{patterns:[{begin:"(?<!\\+\\+|--|})(?<=[=(:,\\[?+!]|^return|[^\\._$0-9A-Za-z]return|^case|[^\\._$0-9A-Za-z]case|=>|&&|\\|\\||\\*\\/)\\s*(\\/)(?![\\/*])(?=(?:[^\\/\\\\\\[()]|\\\\.|\\[([^\\]\\\\]|\\\\.)+\\]|\\(([^)\\\\]|\\\\.)+\\))+\\/([dgimsuy]+|(?![\\/\\*])|(?=\\/\\*))(?!\\s*[a-zA-Z0-9_$]))",beginCaptures:{1:{name:"punctuation.definition.string.begin.tsx"}},end:"(/)([dgimsuy]*)",endCaptures:{1:{name:"punctuation.definition.string.end.tsx"},2:{name:"keyword.other.tsx"}},name:"string.regexp.tsx",patterns:[{include:"#regexp"}]},{begin:"((?<![_$0-9A-Za-z)\\]]|\\+\\+|--|}|\\*\\/)|((?<=^return|[^\\._$0-9A-Za-z]return|^case|[^\\._$0-9A-Za-z]case))\\s*)\\/(?![\\/*])(?=(?:[^\\/\\\\\\[]|\\\\.|\\[([^\\]\\\\]|\\\\.)*\\])+\\/([dgimsuy]+|(?![\\/\\*])|(?=\\/\\*))(?!\\s*[a-zA-Z0-9_$]))",beginCaptures:{0:{name:"punctuation.definition.string.begin.tsx"}},end:"(/)([dgimsuy]*)",endCaptures:{1:{name:"punctuation.definition.string.end.tsx"},2:{name:"keyword.other.tsx"}},name:"string.regexp.tsx",patterns:[{include:"#regexp"}]}]},"regex-character-class":{patterns:[{match:"\\\\[wWsSdDtrnvf]|\\.",name:"constant.other.character-class.regexp"},{match:"\\\\([0-7]{3}|x[0-9A-Fa-f]{2}|u[0-9A-Fa-f]{4})",name:"constant.character.numeric.regexp"},{match:"\\\\c[A-Z]",name:"constant.character.control.regexp"},{match:"\\\\.",name:"constant.character.escape.backslash.regexp"}]},regexp:{patterns:[{match:"\\\\[bB]|\\^|\\$",name:"keyword.control.anchor.regexp"},{captures:{0:{name:"keyword.other.back-reference.regexp"},1:{name:"variable.other.regexp"}},match:"\\\\[1-9]\\d*|\\\\k<([a-zA-Z_$][\\w$]*)>"},{match:"[?+*]|\\{(\\d+,\\d+|\\d+,|,\\d+|\\d+)\\}\\??",name:"keyword.operator.quantifier.regexp"},{match:"\\|",name:"keyword.operator.or.regexp"},{begin:"(\\()((\\?=)|(\\?!)|(\\?<=)|(\\?<!))",beginCaptures:{1:{name:"punctuation.definition.group.regexp"},2:{name:"punctuation.definition.group.assertion.regexp"},3:{name:"meta.assertion.look-ahead.regexp"},4:{name:"meta.assertion.negative-look-ahead.regexp"},5:{name:"meta.assertion.look-behind.regexp"},6:{name:"meta.assertion.negative-look-behind.regexp"}},end:"(\\))",endCaptures:{1:{name:"punctuation.definition.group.regexp"}},name:"meta.group.assertion.regexp",patterns:[{include:"#regexp"}]},{begin:"\\((?:(\\?:)|(?:\\?<([a-zA-Z_$][\\w$]*)>))?",beginCaptures:{0:{name:"punctuation.definition.group.regexp"},1:{name:"punctuation.definition.group.no-capture.regexp"},2:{name:"variable.other.regexp"}},end:"\\)",endCaptures:{0:{name:"punctuation.definition.group.regexp"}},name:"meta.group.regexp",patterns:[{include:"#regexp"}]},{begin:"(\\[)(\\^)?",beginCaptures:{1:{name:"punctuation.definition.character-class.regexp"},2:{name:"keyword.operator.negation.regexp"}},end:"(\\])",endCaptures:{1:{name:"punctuation.definition.character-class.regexp"}},name:"constant.other.character-class.set.regexp",patterns:[{captures:{1:{name:"constant.character.numeric.regexp"},2:{name:"constant.character.control.regexp"},3:{name:"constant.character.escape.backslash.regexp"},4:{name:"constant.character.numeric.regexp"},5:{name:"constant.character.control.regexp"},6:{name:"constant.character.escape.backslash.regexp"}},match:"(?:.|(\\\\(?:[0-7]{3}|x[0-9A-Fa-f]{2}|u[0-9A-Fa-f]{4}))|(\\\\c[A-Z])|(\\\\.))-(?:[^\\]\\\\]|(\\\\(?:[0-7]{3}|x[0-9A-Fa-f]{2}|u[0-9A-Fa-f]{4}))|(\\\\c[A-Z])|(\\\\.))",name:"constant.other.character-class.range.regexp"},{include:"#regex-character-class"}]},{include:"#regex-character-class"}]},"return-type":{patterns:[{begin:"(?<=\\))\\s*(:)(?=\\s*\\S)",beginCaptures:{1:{name:"keyword.operator.type.annotation.tsx"}},end:"(?<![:|&])(?=$|^|[{};,]|//)",name:"meta.return.type.tsx",patterns:[{include:"#return-type-core"}]},{begin:"(?<=\\))\\s*(:)",beginCaptures:{1:{name:"keyword.operator.type.annotation.tsx"}},end:"(?<![:|&])((?=[{};,]|//|^\\s*$)|((?<=\\S)(?=\\s*$)))",name:"meta.return.type.tsx",patterns:[{include:"#return-type-core"}]}]},"return-type-core":{patterns:[{include:"#comment"},{begin:"(?<=[:|&])(?=\\s*\\{)",end:"(?<=\\})",patterns:[{include:"#type-object"}]},{include:"#type-predicate-operator"},{include:"#type"}]},shebang:{captures:{1:{name:"punctuation.definition.comment.tsx"}},match:"\\A(#!).*(?=$)",name:"comment.line.shebang.tsx"},"single-line-comment-consuming-line-ending":{begin:"(^[ \\t]+)?((//)(?:\\s*((@)internal)(?=\\s|$))?)",beginCaptures:{1:{name:"punctuation.whitespace.comment.leading.tsx"},2:{name:"comment.line.double-slash.tsx"},3:{name:"punctuation.definition.comment.tsx"},4:{name:"storage.type.internaldeclaration.tsx"},5:{name:"punctuation.decorator.internaldeclaration.tsx"}},contentName:"comment.line.double-slash.tsx",end:"(?=^)"},statements:{patterns:[{include:"#declaration"},{include:"#control-statement"},{include:"#after-operator-block-as-object-literal"},{include:"#decl-block"},{include:"#label"},{include:"#expression"},{include:"#punctuation-semicolon"},{include:"#string"},{include:"#comment"}]},string:{patterns:[{include:"#qstring-single"},{include:"#qstring-double"},{include:"#template"}]},"string-character-escape":{match:"\\\\(x[0-9A-Fa-f]{2}|u[0-9A-Fa-f]{4}|u\\{[0-9A-Fa-f]+\\}|[0-2][0-7]{0,2}|3[0-6][0-7]?|37[0-7]?|[4-7][0-7]?|.|$)",name:"constant.character.escape.tsx"},"super-literal":{match:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))super\\b(?!\\$)",name:"variable.language.super.tsx"},"support-function-call-identifiers":{patterns:[{include:"#literal"},{include:"#support-objects"},{include:"#object-identifiers"},{include:"#punctuation-accessor"},{match:"(?:(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))import(?=\\s*[(]\\s*[\\\"\\'\\`]))",name:"keyword.operator.expression.import.tsx"}]},"support-objects":{patterns:[{match:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(arguments)\\b(?!\\$)",name:"variable.language.arguments.tsx"},{match:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(Promise)\\b(?!\\$)",name:"support.class.promise.tsx"},{captures:{1:{name:"keyword.control.import.tsx"},2:{name:"punctuation.accessor.tsx"},3:{name:"punctuation.accessor.optional.tsx"},4:{name:"support.variable.property.importmeta.tsx"}},match:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(import)\\s*(?:(\\.)|(\\?\\.(?!\\s*[\\d])))\\s*(meta)\\b(?!\\$)"},{captures:{1:{name:"keyword.operator.new.tsx"},2:{name:"punctuation.accessor.tsx"},3:{name:"punctuation.accessor.optional.tsx"},4:{name:"support.variable.property.target.tsx"}},match:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(new)\\s*(?:(\\.)|(\\?\\.(?!\\s*[\\d])))\\s*(target)\\b(?!\\$)"},{captures:{1:{name:"punctuation.accessor.tsx"},2:{name:"punctuation.accessor.optional.tsx"},3:{name:"support.variable.property.tsx"},4:{name:"support.constant.tsx"}},match:"(?:(\\.)|(\\?\\.(?!\\s*[\\d])))\\s*(?:(?:(constructor|length|prototype|__proto__)\\b(?!\\$|\\s*(<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<]|<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<]|<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<])*>)*>)*>\\s*)?\\())|(?:(EPSILON|MAX_SAFE_INTEGER|MAX_VALUE|MIN_SAFE_INTEGER|MIN_VALUE|NEGATIVE_INFINITY|POSITIVE_INFINITY)\\b(?!\\$)))"},{captures:{1:{name:"support.type.object.module.tsx"},2:{name:"support.type.object.module.tsx"},3:{name:"punctuation.accessor.tsx"},4:{name:"punctuation.accessor.optional.tsx"},5:{name:"support.type.object.module.tsx"}},match:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(?:(exports)|(module)(?:(?:(\\.)|(\\?\\.(?!\\s*[\\d])))(exports|id|filename|loaded|parent|children))?)\\b(?!\\$)"}]},"switch-statement":{begin:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(?=\\bswitch\\s*\\()",end:"\\}",endCaptures:{0:{name:"punctuation.definition.block.tsx"}},name:"switch-statement.expr.tsx",patterns:[{include:"#comment"},{begin:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(switch)\\s*(\\()",beginCaptures:{1:{name:"keyword.control.switch.tsx"},2:{name:"meta.brace.round.tsx"}},end:"\\)",endCaptures:{0:{name:"meta.brace.round.tsx"}},name:"switch-expression.expr.tsx",patterns:[{include:"#expression"}]},{begin:"\\{",beginCaptures:{0:{name:"punctuation.definition.block.tsx"}},end:"(?=\\})",name:"switch-block.expr.tsx",patterns:[{begin:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(case|default(?=:))(?![_$0-9A-Za-z])(?:(?=\\.\\.\\.)|(?!\\.))",beginCaptures:{1:{name:"keyword.control.switch.tsx"}},end:"(?=:)",name:"case-clause.expr.tsx",patterns:[{include:"#expression"}]},{begin:"(:)\\s*(\\{)",beginCaptures:{1:{name:"case-clause.expr.tsx punctuation.definition.section.case-statement.tsx"},2:{name:"meta.block.tsx punctuation.definition.block.tsx"}},contentName:"meta.block.tsx",end:"\\}",endCaptures:{0:{name:"meta.block.tsx punctuation.definition.block.tsx"}},patterns:[{include:"#statements"}]},{captures:{0:{name:"case-clause.expr.tsx punctuation.definition.section.case-statement.tsx"}},match:"(:)"},{include:"#statements"}]}]},template:{patterns:[{include:"#template-call"},{begin:"([_$A-Za-z][_$0-9A-Za-z]*)?(`)",beginCaptures:{1:{name:"entity.name.function.tagged-template.tsx"},2:{name:"string.template.tsx punctuation.definition.string.template.begin.tsx"}},contentName:"string.template.tsx",end:"`",endCaptures:{0:{name:"string.template.tsx punctuation.definition.string.template.end.tsx"}},patterns:[{include:"#template-substitution-element"},{include:"#string-character-escape"}]}]},"template-call":{patterns:[{begin:"(?=(([_$A-Za-z][_$0-9A-Za-z]*\\s*\\??\\.\\s*)*|(\\??\\.\\s*)?)([_$A-Za-z][_$0-9A-Za-z]*)(<\\s*(((keyof|infer|typeof|readonly)\\s+)|(([_$A-Za-z][_$0-9A-Za-z]*|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\])|(\\'([^\\'\\\\]|\\\\.)*\\')|(\\\"([^\\\"\\\\]|\\\\.)*\\\")|(\\`([^\\`\\\\]|\\\\.)*\\`))(?=\\s*([<>\\,\\.\\[]|=>|&(?!&)|\\|(?!\\|)))))([^<>(]|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(?<==)>|<\\s*(((keyof|infer|typeof|readonly)\\s+)|(([_$A-Za-z][_$0-9A-Za-z]*|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\])|(\\'([^\\'\\\\]|\\\\.)*\\')|(\\\"([^\\\"\\\\]|\\\\.)*\\\")|(\\`([^\\`\\\\]|\\\\.)*\\`))(?=\\s*([<>\\,\\.\\[]|=>|&(?!&)|\\|(?!\\|)))))(([^<>(]|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(?<==)>|<\\s*(((keyof|infer|typeof|readonly)\\s+)|(([_$A-Za-z][_$0-9A-Za-z]*|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\])|(\\'([^\\'\\\\]|\\\\.)*\\')|(\\\"([^\\\"\\\\]|\\\\.)*\\\")|(\\`([^\\`\\\\]|\\\\.)*\\`))(?=\\s*([<>\\,\\.\\[]|=>|&(?!&)|\\|(?!\\|)))))([^<>(]|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(?<==)>)*(?<!=)>))*(?<!=)>)*(?<!=)>\\s*)?`)",end:"(?=`)",patterns:[{begin:"(?=(([_$A-Za-z][_$0-9A-Za-z]*\\s*\\??\\.\\s*)*|(\\??\\.\\s*)?)([_$A-Za-z][_$0-9A-Za-z]*))",end:"(?=(<\\s*(((keyof|infer|typeof|readonly)\\s+)|(([_$A-Za-z][_$0-9A-Za-z]*|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\])|(\\'([^\\'\\\\]|\\\\.)*\\')|(\\\"([^\\\"\\\\]|\\\\.)*\\\")|(\\`([^\\`\\\\]|\\\\.)*\\`))(?=\\s*([<>\\,\\.\\[]|=>|&(?!&)|\\|(?!\\|)))))([^<>(]|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(?<==)>|<\\s*(((keyof|infer|typeof|readonly)\\s+)|(([_$A-Za-z][_$0-9A-Za-z]*|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\])|(\\'([^\\'\\\\]|\\\\.)*\\')|(\\\"([^\\\"\\\\]|\\\\.)*\\\")|(\\`([^\\`\\\\]|\\\\.)*\\`))(?=\\s*([<>\\,\\.\\[]|=>|&(?!&)|\\|(?!\\|)))))(([^<>(]|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(?<==)>|<\\s*(((keyof|infer|typeof|readonly)\\s+)|(([_$A-Za-z][_$0-9A-Za-z]*|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\])|(\\'([^\\'\\\\]|\\\\.)*\\')|(\\\"([^\\\"\\\\]|\\\\.)*\\\")|(\\`([^\\`\\\\]|\\\\.)*\\`))(?=\\s*([<>\\,\\.\\[]|=>|&(?!&)|\\|(?!\\|)))))([^<>(]|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(?<==)>)*(?<!=)>))*(?<!=)>)*(?<!=)>\\s*)?`)",patterns:[{include:"#support-function-call-identifiers"},{match:"([_$A-Za-z][_$0-9A-Za-z]*)",name:"entity.name.function.tagged-template.tsx"}]},{include:"#type-arguments"}]},{begin:"([_$A-Za-z][_$0-9A-Za-z]*)?\\s*(?=(<\\s*(((keyof|infer|typeof|readonly)\\s+)|(([_$A-Za-z][_$0-9A-Za-z]*|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\])|(\\'([^\\'\\\\]|\\\\.)*\\')|(\\\"([^\\\"\\\\]|\\\\.)*\\\")|(\\`([^\\`\\\\]|\\\\.)*\\`))(?=\\s*([<>\\,\\.\\[]|=>|&(?!&)|\\|(?!\\|)))))([^<>(]|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(?<==)>|<\\s*(((keyof|infer|typeof|readonly)\\s+)|(([_$A-Za-z][_$0-9A-Za-z]*|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\])|(\\'([^\\'\\\\]|\\\\.)*\\')|(\\\"([^\\\"\\\\]|\\\\.)*\\\")|(\\`([^\\`\\\\]|\\\\.)*\\`))(?=\\s*([<>\\,\\.\\[]|=>|&(?!&)|\\|(?!\\|)))))(([^<>(]|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(?<==)>|<\\s*(((keyof|infer|typeof|readonly)\\s+)|(([_$A-Za-z][_$0-9A-Za-z]*|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\])|(\\'([^\\'\\\\]|\\\\.)*\\')|(\\\"([^\\\"\\\\]|\\\\.)*\\\")|(\\`([^\\`\\\\]|\\\\.)*\\`))(?=\\s*([<>\\,\\.\\[]|=>|&(?!&)|\\|(?!\\|)))))([^<>(]|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(?<==)>)*(?<!=)>))*(?<!=)>)*(?<!=)>\\s*)`)",beginCaptures:{1:{name:"entity.name.function.tagged-template.tsx"}},end:"(?=`)",patterns:[{include:"#type-arguments"}]}]},"template-substitution-element":{begin:"\\$\\{",beginCaptures:{0:{name:"punctuation.definition.template-expression.begin.tsx"}},contentName:"meta.embedded.line.tsx",end:"\\}",endCaptures:{0:{name:"punctuation.definition.template-expression.end.tsx"}},name:"meta.template.expression.tsx",patterns:[{include:"#expression"}]},"template-type":{patterns:[{include:"#template-call"},{begin:"([_$A-Za-z][_$0-9A-Za-z]*)?(`)",beginCaptures:{1:{name:"entity.name.function.tagged-template.tsx"},2:{name:"string.template.tsx punctuation.definition.string.template.begin.tsx"}},contentName:"string.template.tsx",end:"`",endCaptures:{0:{name:"string.template.tsx punctuation.definition.string.template.end.tsx"}},patterns:[{include:"#template-type-substitution-element"},{include:"#string-character-escape"}]}]},"template-type-substitution-element":{begin:"\\$\\{",beginCaptures:{0:{name:"punctuation.definition.template-expression.begin.tsx"}},contentName:"meta.embedded.line.tsx",end:"\\}",endCaptures:{0:{name:"punctuation.definition.template-expression.end.tsx"}},name:"meta.template.expression.tsx",patterns:[{include:"#type"}]},"ternary-expression":{begin:"(?!\\?\\.\\s*[^\\d])(\\?)(?!\\?)",beginCaptures:{1:{name:"keyword.operator.ternary.tsx"}},end:"\\s*(:)",endCaptures:{1:{name:"keyword.operator.ternary.tsx"}},patterns:[{include:"#expression"}]},"this-literal":{match:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))this\\b(?!\\$)",name:"variable.language.this.tsx"},type:{patterns:[{include:"#comment"},{include:"#type-string"},{include:"#numeric-literal"},{include:"#type-primitive"},{include:"#type-builtin-literals"},{include:"#type-parameters"},{include:"#type-tuple"},{include:"#type-object"},{include:"#type-operators"},{include:"#type-conditional"},{include:"#type-fn-type-parameters"},{include:"#type-paren-or-function-parameters"},{include:"#type-function-return-type"},{captures:{1:{name:"storage.modifier.tsx"}},match:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(readonly)(?![_$0-9A-Za-z])(?:(?=\\.\\.\\.)|(?!\\.))\\s*"},{include:"#type-name"}]},"type-alias-declaration":{begin:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(?:(\\bexport)\\s+)?(?:(\\bdeclare)\\s+)?\\b(type)\\b\\s+([_$A-Za-z][_$0-9A-Za-z]*)\\s*",beginCaptures:{1:{name:"keyword.control.export.tsx"},2:{name:"storage.modifier.tsx"},3:{name:"storage.type.type.tsx"},4:{name:"entity.name.type.alias.tsx"}},end:"(?=\\}|;|^\\s*$|(?:^\\s*(?:abstract|async|(?:\\bawait\\s+(?:\\busing(?=\\s+(?!in\\b|of\\b(?!\\s*(?:of\\b|=)))[_$A-Za-z])\\b)\\b)|break|case|catch|class|const|continue|declare|do|else|enum|export|finally|function|for|goto|if|import|interface|let|module|namespace|switch|return|throw|try|type|(?:\\busing(?=\\s+(?!in\\b|of\\b(?!\\s*(?:of\\b|=)))[_$A-Za-z])\\b)|var|while)\\b))",name:"meta.type.declaration.tsx",patterns:[{include:"#comment"},{include:"#type-parameters"},{begin:"(=)\\s*(intrinsic)(?![_$0-9A-Za-z])(?:(?=\\.\\.\\.)|(?!\\.))",beginCaptures:{1:{name:"keyword.operator.assignment.tsx"},2:{name:"keyword.control.intrinsic.tsx"}},end:"(?=\\}|;|^\\s*$|(?:^\\s*(?:abstract|async|(?:\\bawait\\s+(?:\\busing(?=\\s+(?!in\\b|of\\b(?!\\s*(?:of\\b|=)))[_$A-Za-z])\\b)\\b)|break|case|catch|class|const|continue|declare|do|else|enum|export|finally|function|for|goto|if|import|interface|let|module|namespace|switch|return|throw|try|type|(?:\\busing(?=\\s+(?!in\\b|of\\b(?!\\s*(?:of\\b|=)))[_$A-Za-z])\\b)|var|while)\\b))",patterns:[{include:"#type"}]},{begin:"(=)\\s*",beginCaptures:{1:{name:"keyword.operator.assignment.tsx"}},end:"(?=\\}|;|^\\s*$|(?:^\\s*(?:abstract|async|(?:\\bawait\\s+(?:\\busing(?=\\s+(?!in\\b|of\\b(?!\\s*(?:of\\b|=)))[_$A-Za-z])\\b)\\b)|break|case|catch|class|const|continue|declare|do|else|enum|export|finally|function|for|goto|if|import|interface|let|module|namespace|switch|return|throw|try|type|(?:\\busing(?=\\s+(?!in\\b|of\\b(?!\\s*(?:of\\b|=)))[_$A-Za-z])\\b)|var|while)\\b))",patterns:[{include:"#type"}]}]},"type-annotation":{patterns:[{begin:"(:)(?=\\s*\\S)",beginCaptures:{1:{name:"keyword.operator.type.annotation.tsx"}},end:"(?<![:|&])(?!\\s*[|&]\\s+)((?=^|[,);}\\]]|//)|(?==[^>])|((?<=[}>\\])]|[_$A-Za-z])\\s*(?=\\{)))",name:"meta.type.annotation.tsx",patterns:[{include:"#type"}]},{begin:"(:)",beginCaptures:{1:{name:"keyword.operator.type.annotation.tsx"}},end:"(?<![:|&])((?=[,);}\\]]|\\/\\/)|(?==[^>])|(?=^\\s*$)|((?<=[}>\\])]|[_$A-Za-z])\\s*(?=\\{)))",name:"meta.type.annotation.tsx",patterns:[{include:"#type"}]}]},"type-arguments":{begin:"<",beginCaptures:{0:{name:"punctuation.definition.typeparameters.begin.tsx"}},end:">",endCaptures:{0:{name:"punctuation.definition.typeparameters.end.tsx"}},name:"meta.type.parameters.tsx",patterns:[{include:"#type-arguments-body"}]},"type-arguments-body":{patterns:[{captures:{0:{name:"keyword.operator.type.tsx"}},match:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(_)(?![_$0-9A-Za-z])(?:(?=\\.\\.\\.)|(?!\\.))"},{include:"#type"},{include:"#punctuation-comma"}]},"type-builtin-literals":{match:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(this|true|false|undefined|null|object)(?![_$0-9A-Za-z])(?:(?=\\.\\.\\.)|(?!\\.))",name:"support.type.builtin.tsx"},"type-conditional":{patterns:[{begin:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(extends)\\s+",beginCaptures:{1:{name:"storage.modifier.tsx"}},end:"(?<=:)",patterns:[{begin:"\\?",beginCaptures:{0:{name:"keyword.operator.ternary.tsx"}},end:":",endCaptures:{0:{name:"keyword.operator.ternary.tsx"}},patterns:[{include:"#type"}]},{include:"#type"}]}]},"type-fn-type-parameters":{patterns:[{begin:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(?:(abstract)\\s+)?(new)\\b(?=\\s*<)",beginCaptures:{1:{name:"meta.type.constructor.tsx storage.modifier.tsx"},2:{name:"meta.type.constructor.tsx keyword.control.new.tsx"}},end:"(?<=>)",patterns:[{include:"#comment"},{include:"#type-parameters"}]},{begin:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(?:(abstract)\\s+)?(new)\\b\\s*(?=\\()",beginCaptures:{1:{name:"storage.modifier.tsx"},2:{name:"keyword.control.new.tsx"}},end:"(?<=\\))",name:"meta.type.constructor.tsx",patterns:[{include:"#function-parameters"}]},{begin:"((?=[(]\\s*(([)])|(\\.\\.\\.)|([_$0-9A-Za-z]+\\s*(([:,?=])|([)]\\s*=>))))))",end:"(?<=\\))",name:"meta.type.function.tsx",patterns:[{include:"#function-parameters"}]}]},"type-function-return-type":{patterns:[{begin:"(=>)(?=\\s*\\S)",beginCaptures:{1:{name:"storage.type.function.arrow.tsx"}},end:"(?<!=>)(?<![|&])(?=[,\\]){}=;>:?]|//|$)",name:"meta.type.function.return.tsx",patterns:[{include:"#type-function-return-type-core"}]},{begin:"=>",beginCaptures:{0:{name:"storage.type.function.arrow.tsx"}},end:"(?<!=>)(?<![|&])((?=[,\\]){}=;:?>]|//|^\\s*$)|((?<=\\S)(?=\\s*$)))",name:"meta.type.function.return.tsx",patterns:[{include:"#type-function-return-type-core"}]}]},"type-function-return-type-core":{patterns:[{include:"#comment"},{begin:"(?<==>)(?=\\s*\\{)",end:"(?<=\\})",patterns:[{include:"#type-object"}]},{include:"#type-predicate-operator"},{include:"#type"}]},"type-infer":{patterns:[{captures:{1:{name:"keyword.operator.expression.infer.tsx"},2:{name:"entity.name.type.tsx"},3:{name:"keyword.operator.expression.extends.tsx"}},match:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(infer)\\s+([_$A-Za-z][_$0-9A-Za-z]*)(?![_$0-9A-Za-z])(?:(?=\\.\\.\\.)|(?!\\.))(?:\\s+(extends)(?![_$0-9A-Za-z])(?:(?=\\.\\.\\.)|(?!\\.)))?",name:"meta.type.infer.tsx"}]},"type-name":{patterns:[{begin:"([_$A-Za-z][_$0-9A-Za-z]*)\\s*(?:(\\.)|(\\?\\.(?!\\s*[\\d])))\\s*(<)",captures:{1:{name:"entity.name.type.module.tsx"},2:{name:"punctuation.accessor.tsx"},3:{name:"punctuation.accessor.optional.tsx"},4:{name:"meta.type.parameters.tsx punctuation.definition.typeparameters.begin.tsx"}},contentName:"meta.type.parameters.tsx",end:"(>)",endCaptures:{1:{name:"meta.type.parameters.tsx punctuation.definition.typeparameters.end.tsx"}},patterns:[{include:"#type-arguments-body"}]},{begin:"([_$A-Za-z][_$0-9A-Za-z]*)\\s*(<)",beginCaptures:{1:{name:"entity.name.type.tsx"},2:{name:"meta.type.parameters.tsx punctuation.definition.typeparameters.begin.tsx"}},contentName:"meta.type.parameters.tsx",end:"(>)",endCaptures:{1:{name:"meta.type.parameters.tsx punctuation.definition.typeparameters.end.tsx"}},patterns:[{include:"#type-arguments-body"}]},{captures:{1:{name:"entity.name.type.module.tsx"},2:{name:"punctuation.accessor.tsx"},3:{name:"punctuation.accessor.optional.tsx"}},match:"([_$A-Za-z][_$0-9A-Za-z]*)\\s*(?:(\\.)|(\\?\\.(?!\\s*[\\d])))"},{match:"[_$A-Za-z][_$0-9A-Za-z]*",name:"entity.name.type.tsx"}]},"type-object":{begin:"\\{",beginCaptures:{0:{name:"punctuation.definition.block.tsx"}},end:"\\}",endCaptures:{0:{name:"punctuation.definition.block.tsx"}},name:"meta.object.type.tsx",patterns:[{include:"#comment"},{include:"#method-declaration"},{include:"#indexer-declaration"},{include:"#indexer-mapped-type-declaration"},{include:"#field-declaration"},{include:"#type-annotation"},{begin:"\\.\\.\\.",beginCaptures:{0:{name:"keyword.operator.spread.tsx"}},end:"(?=\\}|;|,|$)|(?<=\\})",patterns:[{include:"#type"}]},{include:"#punctuation-comma"},{include:"#punctuation-semicolon"},{include:"#type"}]},"type-operators":{patterns:[{include:"#typeof-operator"},{include:"#type-infer"},{begin:"([&|])(?=\\s*\\{)",beginCaptures:{0:{name:"keyword.operator.type.tsx"}},end:"(?<=\\})",patterns:[{include:"#type-object"}]},{begin:"[&|]",beginCaptures:{0:{name:"keyword.operator.type.tsx"}},end:"(?=\\S)"},{match:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))keyof(?![_$0-9A-Za-z])(?:(?=\\.\\.\\.)|(?!\\.))",name:"keyword.operator.expression.keyof.tsx"},{match:"(\\?|:)",name:"keyword.operator.ternary.tsx"},{match:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))import(?=\\s*\\()",name:"keyword.operator.expression.import.tsx"}]},"type-parameters":{begin:"(<)",beginCaptures:{1:{name:"punctuation.definition.typeparameters.begin.tsx"}},end:"(>)",endCaptures:{1:{name:"punctuation.definition.typeparameters.end.tsx"}},name:"meta.type.parameters.tsx",patterns:[{include:"#comment"},{match:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(extends|in|out|const)(?![_$0-9A-Za-z])(?:(?=\\.\\.\\.)|(?!\\.))",name:"storage.modifier.tsx"},{include:"#type"},{include:"#punctuation-comma"},{match:"(=)(?!>)",name:"keyword.operator.assignment.tsx"}]},"type-paren-or-function-parameters":{begin:"\\(",beginCaptures:{0:{name:"meta.brace.round.tsx"}},end:"\\)",endCaptures:{0:{name:"meta.brace.round.tsx"}},name:"meta.type.paren.cover.tsx",patterns:[{captures:{1:{name:"storage.modifier.tsx"},2:{name:"keyword.operator.rest.tsx"},3:{name:"entity.name.function.tsx variable.language.this.tsx"},4:{name:"entity.name.function.tsx"},5:{name:"keyword.operator.optional.tsx"}},match:"(?:(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(public|private|protected|readonly)\\s+)?(?:(\\.\\.\\.)\\s*)?(?<!=|:)(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(?:(this)|([_$A-Za-z][_$0-9A-Za-z]*))\\s*(\\??)(?=\\s*(:\\s*((<)|([(]\\s*(([)])|(\\.\\.\\.)|([_$0-9A-Za-z]+\\s*(([:,?=])|([)]\\s*=>)))))))|(:\\s*(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))Function(?![_$0-9A-Za-z])(?:(?=\\.\\.\\.)|(?!\\.)))|(:\\s*((<\\s*$)|([(]\\s*((([{\\[]\\s*)?$)|((\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})\\s*((:\\s*\\{?$)|((\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\([^()]+\\)|\\{[^{}]+\\})+\\s*)?=\\s*)))|((\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\])\\s*((:\\s*\\[?$)|((\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\([^()]+\\)|\\{[^{}]+\\})+\\s*)?=\\s*))))))))"},{captures:{1:{name:"storage.modifier.tsx"},2:{name:"keyword.operator.rest.tsx"},3:{name:"variable.parameter.tsx variable.language.this.tsx"},4:{name:"variable.parameter.tsx"},5:{name:"keyword.operator.optional.tsx"}},match:"(?:(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(public|private|protected|readonly)\\s+)?(?:(\\.\\.\\.)\\s*)?(?<!=|:)(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(?:(this)|([_$A-Za-z][_$0-9A-Za-z]*))\\s*(\\??)(?=:)"},{include:"#type-annotation"},{match:",",name:"punctuation.separator.parameter.tsx"},{include:"#type"}]},"type-predicate-operator":{patterns:[{captures:{1:{name:"keyword.operator.type.asserts.tsx"},2:{name:"variable.parameter.tsx variable.language.this.tsx"},3:{name:"variable.parameter.tsx"},4:{name:"keyword.operator.expression.is.tsx"}},match:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(?:(asserts)\\s+)?(?!asserts)(?:(this)|([_$A-Za-z][_$0-9A-Za-z]*))\\s(is)(?![_$0-9A-Za-z])(?:(?=\\.\\.\\.)|(?!\\.))"},{captures:{1:{name:"keyword.operator.type.asserts.tsx"},2:{name:"variable.parameter.tsx variable.language.this.tsx"},3:{name:"variable.parameter.tsx"}},match:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(asserts)\\s+(?!is)(?:(this)|([_$A-Za-z][_$0-9A-Za-z]*))(?![_$0-9A-Za-z])(?:(?=\\.\\.\\.)|(?!\\.))"},{match:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))asserts(?![_$0-9A-Za-z])(?:(?=\\.\\.\\.)|(?!\\.))",name:"keyword.operator.type.asserts.tsx"},{match:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))is(?![_$0-9A-Za-z])(?:(?=\\.\\.\\.)|(?!\\.))",name:"keyword.operator.expression.is.tsx"}]},"type-primitive":{match:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(string|number|bigint|boolean|symbol|any|void|never|unknown)(?![_$0-9A-Za-z])(?:(?=\\.\\.\\.)|(?!\\.))",name:"support.type.primitive.tsx"},"type-string":{patterns:[{include:"#qstring-single"},{include:"#qstring-double"},{include:"#template-type"}]},"type-tuple":{begin:"\\[",beginCaptures:{0:{name:"meta.brace.square.tsx"}},end:"\\]",endCaptures:{0:{name:"meta.brace.square.tsx"}},name:"meta.type.tuple.tsx",patterns:[{match:"\\.\\.\\.",name:"keyword.operator.rest.tsx"},{captures:{1:{name:"entity.name.label.tsx"},2:{name:"keyword.operator.optional.tsx"},3:{name:"punctuation.separator.label.tsx"}},match:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))([_$A-Za-z][_$0-9A-Za-z]*)\\s*(\\?)?\\s*(:)"},{include:"#type"},{include:"#punctuation-comma"}]},"typeof-operator":{begin:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))typeof(?![_$0-9A-Za-z])(?:(?=\\.\\.\\.)|(?!\\.))",beginCaptures:{0:{name:"keyword.operator.expression.typeof.tsx"}},end:"(?=[,);}\\]=>:&|{?]|(extends\\s+)|$|;|^\\s*$|(?:^\\s*(?:abstract|async|(?:\\bawait\\s+(?:\\busing(?=\\s+(?!in\\b|of\\b(?!\\s*(?:of\\b|=)))[_$A-Za-z])\\b)\\b)|break|case|catch|class|const|continue|declare|do|else|enum|export|finally|function|for|goto|if|import|interface|let|module|namespace|switch|return|throw|try|type|(?:\\busing(?=\\s+(?!in\\b|of\\b(?!\\s*(?:of\\b|=)))[_$A-Za-z])\\b)|var|while)\\b))",patterns:[{include:"#type-arguments"},{include:"#expression"}]},"undefined-literal":{match:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))undefined(?![_$0-9A-Za-z])(?:(?=\\.\\.\\.)|(?!\\.))",name:"constant.language.undefined.tsx"},"var-expr":{patterns:[{begin:"(?=(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(?:(\\bexport)\\s+)?(?:(\\bdeclare)\\s+)?\\b(var|let)(?![_$0-9A-Za-z])(?:(?=\\.\\.\\.)|(?!\\.)))",end:"(?!(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(?:(\\bexport)\\s+)?(?:(\\bdeclare)\\s+)?\\b(var|let)(?![_$0-9A-Za-z])(?:(?=\\.\\.\\.)|(?!\\.)))((?=^|;|}|((?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(of|in)\\s+)|;|^\\s*$|(?:^\\s*(?:abstract|async|(?:\\bawait\\s+(?:\\busing(?=\\s+(?!in\\b|of\\b(?!\\s*(?:of\\b|=)))[_$A-Za-z])\\b)\\b)|break|case|catch|class|const|continue|declare|do|else|enum|export|finally|function|for|goto|if|import|interface|let|module|namespace|switch|return|throw|try|type|(?:\\busing(?=\\s+(?!in\\b|of\\b(?!\\s*(?:of\\b|=)))[_$A-Za-z])\\b)|var|while)\\b))|((?<!^let|[^\\._$0-9A-Za-z]let|^var|[^\\._$0-9A-Za-z]var)(?=\\s*$)))",name:"meta.var.expr.tsx",patterns:[{begin:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(?:(\\bexport)\\s+)?(?:(\\bdeclare)\\s+)?\\b(var|let)(?![_$0-9A-Za-z])(?:(?=\\.\\.\\.)|(?!\\.))\\s*",beginCaptures:{1:{name:"keyword.control.export.tsx"},2:{name:"storage.modifier.tsx"},3:{name:"storage.type.tsx"}},end:"(?=\\S)"},{include:"#destructuring-variable"},{include:"#var-single-variable"},{include:"#variable-initializer"},{include:"#comment"},{begin:"(,)\\s*(?=$|\\/\\/)",beginCaptures:{1:{name:"punctuation.separator.comma.tsx"}},end:"(?<!,)(((?==|;|}|((?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(of|in)\\s+)|^\\s*$))|((?<=\\S)(?=\\s*$)))",patterns:[{include:"#single-line-comment-consuming-line-ending"},{include:"#comment"},{include:"#destructuring-variable"},{include:"#var-single-variable"},{include:"#punctuation-comma"}]},{include:"#punctuation-comma"}]},{begin:"(?=(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(?:(\\bexport)\\s+)?(?:(\\bdeclare)\\s+)?\\b(const(?!\\s+enum\\b))(?![_$0-9A-Za-z])(?:(?=\\.\\.\\.)|(?!\\.)))",beginCaptures:{1:{name:"keyword.control.export.tsx"},2:{name:"storage.modifier.tsx"},3:{name:"storage.type.tsx"}},end:"(?!(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(?:(\\bexport)\\s+)?(?:(\\bdeclare)\\s+)?\\b(const(?!\\s+enum\\b))(?![_$0-9A-Za-z])(?:(?=\\.\\.\\.)|(?!\\.)))((?=^|;|}|((?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(of|in)\\s+)|;|^\\s*$|(?:^\\s*(?:abstract|async|(?:\\bawait\\s+(?:\\busing(?=\\s+(?!in\\b|of\\b(?!\\s*(?:of\\b|=)))[_$A-Za-z])\\b)\\b)|break|case|catch|class|const|continue|declare|do|else|enum|export|finally|function|for|goto|if|import|interface|let|module|namespace|switch|return|throw|try|type|(?:\\busing(?=\\s+(?!in\\b|of\\b(?!\\s*(?:of\\b|=)))[_$A-Za-z])\\b)|var|while)\\b))|((?<!^const|[^\\._$0-9A-Za-z]const)(?=\\s*$)))",name:"meta.var.expr.tsx",patterns:[{begin:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(?:(\\bexport)\\s+)?(?:(\\bdeclare)\\s+)?\\b(const(?!\\s+enum\\b))(?![_$0-9A-Za-z])(?:(?=\\.\\.\\.)|(?!\\.))\\s*",beginCaptures:{1:{name:"keyword.control.export.tsx"},2:{name:"storage.modifier.tsx"},3:{name:"storage.type.tsx"}},end:"(?=\\S)"},{include:"#destructuring-const"},{include:"#var-single-const"},{include:"#variable-initializer"},{include:"#comment"},{begin:"(,)\\s*(?=$|\\/\\/)",beginCaptures:{1:{name:"punctuation.separator.comma.tsx"}},end:"(?<!,)(((?==|;|}|((?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(of|in)\\s+)|^\\s*$))|((?<=\\S)(?=\\s*$)))",patterns:[{include:"#single-line-comment-consuming-line-ending"},{include:"#comment"},{include:"#destructuring-const"},{include:"#var-single-const"},{include:"#punctuation-comma"}]},{include:"#punctuation-comma"}]},{begin:"(?=(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(?:(\\bexport)\\s+)?(?:(\\bdeclare)\\s+)?\\b((?:\\busing(?=\\s+(?!in\\b|of\\b(?!\\s*(?:of\\b|=)))[_$A-Za-z])\\b)|(?:\\bawait\\s+(?:\\busing(?=\\s+(?!in\\b|of\\b(?!\\s*(?:of\\b|=)))[_$A-Za-z])\\b)\\b))(?![_$0-9A-Za-z])(?:(?=\\.\\.\\.)|(?!\\.)))",beginCaptures:{1:{name:"keyword.control.export.tsx"},2:{name:"storage.modifier.tsx"},3:{name:"storage.type.tsx"}},end:"(?!(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(?:(\\bexport)\\s+)?(?:(\\bdeclare)\\s+)?\\b((?:\\busing(?=\\s+(?!in\\b|of\\b(?!\\s*(?:of\\b|=)))[_$A-Za-z])\\b)|(?:\\bawait\\s+(?:\\busing(?=\\s+(?!in\\b|of\\b(?!\\s*(?:of\\b|=)))[_$A-Za-z])\\b)\\b))(?![_$0-9A-Za-z])(?:(?=\\.\\.\\.)|(?!\\.)))((?=;|}|((?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(of|in)\\s+)|;|^\\s*$|(?:^\\s*(?:abstract|async|(?:\\bawait\\s+(?:\\busing(?=\\s+(?!in\\b|of\\b(?!\\s*(?:of\\b|=)))[_$A-Za-z])\\b)\\b)|break|case|catch|class|const|continue|declare|do|else|enum|export|finally|function|for|goto|if|import|interface|let|module|namespace|switch|return|throw|try|type|(?:\\busing(?=\\s+(?!in\\b|of\\b(?!\\s*(?:of\\b|=)))[_$A-Za-z])\\b)|var|while)\\b))|((?<!^using|[^\\._$0-9A-Za-z]using|^await\\s+using|[^\\._$0-9A-Za-z]await\\s+using)(?=\\s*$)))",name:"meta.var.expr.tsx",patterns:[{begin:"(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(?:(\\bexport)\\s+)?(?:(\\bdeclare)\\s+)?\\b((?:\\busing(?=\\s+(?!in\\b|of\\b(?!\\s*(?:of\\b|=)))[_$A-Za-z])\\b)|(?:\\bawait\\s+(?:\\busing(?=\\s+(?!in\\b|of\\b(?!\\s*(?:of\\b|=)))[_$A-Za-z])\\b)\\b))(?![_$0-9A-Za-z])(?:(?=\\.\\.\\.)|(?!\\.))\\s*",beginCaptures:{1:{name:"keyword.control.export.tsx"},2:{name:"storage.modifier.tsx"},3:{name:"storage.type.tsx"}},end:"(?=\\S)"},{include:"#var-single-const"},{include:"#variable-initializer"},{include:"#comment"},{begin:"(,)\\s*((?!\\S)|(?=\\/\\/))",beginCaptures:{1:{name:"punctuation.separator.comma.tsx"}},end:"(?<!,)(((?==|;|}|((?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(of|in)\\s+)|^\\s*$))|((?<=\\S)(?=\\s*$)))",patterns:[{include:"#single-line-comment-consuming-line-ending"},{include:"#comment"},{include:"#var-single-const"},{include:"#punctuation-comma"}]},{include:"#punctuation-comma"}]}]},"var-single-const":{patterns:[{begin:"([_$A-Za-z][_$0-9A-Za-z]*)(?=\\s*(=\\s*(((async\\s+)?((function\\s*[(<*])|(function\\s+)|([_$A-Za-z][_$0-9A-Za-z]*\\s*=>)))|((async\\s*)?(((<\\s*$)|([(]\\s*((([{\\[]\\s*)?$)|((\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})\\s*((:\\s*\\{?$)|((\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\([^()]+\\)|\\{[^{}]+\\})+\\s*)?=\\s*)))|((\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\])\\s*((:\\s*\\[?$)|((\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\([^()]+\\)|\\{[^{}]+\\})+\\s*)?=\\s*))))))|((<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<]|<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<]|<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<])*>)*>)*>\\s*)?[(]\\s*(\\/\\*([^\\*]|(\\*[^\\/]))*\\*\\/\\s*)*(([)]\\s*:)|((\\.\\.\\.\\s*)?[_$A-Za-z][_$0-9A-Za-z]*\\s*:)))|([<]\\s*[_$A-Za-z][_$0-9A-Za-z]*\\s+extends\\s*[^=>])|((<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<]|<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<]|<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<])*>)*>)*>\\s*)?\\(\\s*(\\/\\*([^\\*]|(\\*[^\\/]))*\\*\\/\\s*)*(([_$A-Za-z]|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\])|(\\.\\.\\.\\s*[_$A-Za-z]))([^()\\'\\\"\\`]|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\'([^\\'\\\\]|\\\\.)*\\')|(\\\"([^\\\"\\\\]|\\\\.)*\\\")|(\\`([^\\`\\\\]|\\\\.)*\\`))*)?\\)(\\s*:\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\([^()]+\\)|\\{[^{}]+\\})+)?\\s*=>)))))|(:\\s*((<)|([(]\\s*(([)])|(\\.\\.\\.)|([_$0-9A-Za-z]+\\s*(([:,?=])|([)]\\s*=>)))))))|(:\\s*(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))Function(?![_$0-9A-Za-z])(?:(?=\\.\\.\\.)|(?!\\.)))|(:\\s*((<\\s*$)|([(]\\s*((([{\\[]\\s*)?$)|((\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})\\s*((:\\s*\\{?$)|((\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\([^()]+\\)|\\{[^{}]+\\})+\\s*)?=\\s*)))|((\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\])\\s*((:\\s*\\[?$)|((\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\([^()]+\\)|\\{[^{}]+\\})+\\s*)?=\\s*)))))))|(:\\s*(=>|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(<[^<>]*>)|[^<>(),=])+=\\s*(((async\\s+)?((function\\s*[(<*])|(function\\s+)|([_$A-Za-z][_$0-9A-Za-z]*\\s*=>)))|((async\\s*)?(((<\\s*$)|([(]\\s*((([{\\[]\\s*)?$)|((\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})\\s*((:\\s*\\{?$)|((\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\([^()]+\\)|\\{[^{}]+\\})+\\s*)?=\\s*)))|((\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\])\\s*((:\\s*\\[?$)|((\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\([^()]+\\)|\\{[^{}]+\\})+\\s*)?=\\s*))))))|((<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<]|<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<]|<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<])*>)*>)*>\\s*)?[(]\\s*(\\/\\*([^\\*]|(\\*[^\\/]))*\\*\\/\\s*)*(([)]\\s*:)|((\\.\\.\\.\\s*)?[_$A-Za-z][_$0-9A-Za-z]*\\s*:)))|([<]\\s*[_$A-Za-z][_$0-9A-Za-z]*\\s+extends\\s*[^=>])|((<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<]|<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<]|<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<])*>)*>)*>\\s*)?\\(\\s*(\\/\\*([^\\*]|(\\*[^\\/]))*\\*\\/\\s*)*(([_$A-Za-z]|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\])|(\\.\\.\\.\\s*[_$A-Za-z]))([^()\\'\\\"\\`]|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\'([^\\'\\\\]|\\\\.)*\\')|(\\\"([^\\\"\\\\]|\\\\.)*\\\")|(\\`([^\\`\\\\]|\\\\.)*\\`))*)?\\)(\\s*:\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\([^()]+\\)|\\{[^{}]+\\})+)?\\s*=>))))))",beginCaptures:{1:{name:"meta.definition.variable.tsx variable.other.constant.tsx entity.name.function.tsx"}},end:"(?=$|^|[;,=}]|((?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(of|in)\\s+)|(;|^\\s*$|(?:^\\s*(?:abstract|async|(?:\\bawait\\s+(?:\\busing(?=\\s+(?!in\\b|of\\b(?!\\s*(?:of\\b|=)))[_$A-Za-z])\\b)\\b)|break|case|catch|class|const|continue|declare|do|else|enum|export|finally|function|for|goto|if|import|interface|let|module|namespace|switch|return|throw|try|type|(?:\\busing(?=\\s+(?!in\\b|of\\b(?!\\s*(?:of\\b|=)))[_$A-Za-z])\\b)|var|while)\\b)))",name:"meta.var-single-variable.expr.tsx",patterns:[{include:"#var-single-variable-type-annotation"}]},{begin:"([_$A-Za-z][_$0-9A-Za-z]*)",beginCaptures:{1:{name:"meta.definition.variable.tsx variable.other.constant.tsx"}},end:"(?=$|^|[;,=}]|((?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(of|in)\\s+)|(;|^\\s*$|(?:^\\s*(?:abstract|async|(?:\\bawait\\s+(?:\\busing(?=\\s+(?!in\\b|of\\b(?!\\s*(?:of\\b|=)))[_$A-Za-z])\\b)\\b)|break|case|catch|class|const|continue|declare|do|else|enum|export|finally|function|for|goto|if|import|interface|let|module|namespace|switch|return|throw|try|type|(?:\\busing(?=\\s+(?!in\\b|of\\b(?!\\s*(?:of\\b|=)))[_$A-Za-z])\\b)|var|while)\\b)))",name:"meta.var-single-variable.expr.tsx",patterns:[{include:"#var-single-variable-type-annotation"}]}]},"var-single-variable":{patterns:[{begin:"([_$A-Za-z][_$0-9A-Za-z]*)(!)?(?=\\s*(=\\s*(((async\\s+)?((function\\s*[(<*])|(function\\s+)|([_$A-Za-z][_$0-9A-Za-z]*\\s*=>)))|((async\\s*)?(((<\\s*$)|([(]\\s*((([{\\[]\\s*)?$)|((\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})\\s*((:\\s*\\{?$)|((\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\([^()]+\\)|\\{[^{}]+\\})+\\s*)?=\\s*)))|((\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\])\\s*((:\\s*\\[?$)|((\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\([^()]+\\)|\\{[^{}]+\\})+\\s*)?=\\s*))))))|((<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<]|<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<]|<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<])*>)*>)*>\\s*)?[(]\\s*(\\/\\*([^\\*]|(\\*[^\\/]))*\\*\\/\\s*)*(([)]\\s*:)|((\\.\\.\\.\\s*)?[_$A-Za-z][_$0-9A-Za-z]*\\s*:)))|([<]\\s*[_$A-Za-z][_$0-9A-Za-z]*\\s+extends\\s*[^=>])|((<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<]|<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<]|<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<])*>)*>)*>\\s*)?\\(\\s*(\\/\\*([^\\*]|(\\*[^\\/]))*\\*\\/\\s*)*(([_$A-Za-z]|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\])|(\\.\\.\\.\\s*[_$A-Za-z]))([^()\\'\\\"\\`]|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\'([^\\'\\\\]|\\\\.)*\\')|(\\\"([^\\\"\\\\]|\\\\.)*\\\")|(\\`([^\\`\\\\]|\\\\.)*\\`))*)?\\)(\\s*:\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\([^()]+\\)|\\{[^{}]+\\})+)?\\s*=>)))))|(:\\s*((<)|([(]\\s*(([)])|(\\.\\.\\.)|([_$0-9A-Za-z]+\\s*(([:,?=])|([)]\\s*=>)))))))|(:\\s*(?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))Function(?![_$0-9A-Za-z])(?:(?=\\.\\.\\.)|(?!\\.)))|(:\\s*((<\\s*$)|([(]\\s*((([{\\[]\\s*)?$)|((\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})\\s*((:\\s*\\{?$)|((\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\([^()]+\\)|\\{[^{}]+\\})+\\s*)?=\\s*)))|((\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\])\\s*((:\\s*\\[?$)|((\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\([^()]+\\)|\\{[^{}]+\\})+\\s*)?=\\s*)))))))|(:\\s*(=>|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(<[^<>]*>)|[^<>(),=])+=\\s*(((async\\s+)?((function\\s*[(<*])|(function\\s+)|([_$A-Za-z][_$0-9A-Za-z]*\\s*=>)))|((async\\s*)?(((<\\s*$)|([(]\\s*((([{\\[]\\s*)?$)|((\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})\\s*((:\\s*\\{?$)|((\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\([^()]+\\)|\\{[^{}]+\\})+\\s*)?=\\s*)))|((\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\])\\s*((:\\s*\\[?$)|((\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\([^()]+\\)|\\{[^{}]+\\})+\\s*)?=\\s*))))))|((<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<]|<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<]|<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<])*>)*>)*>\\s*)?[(]\\s*(\\/\\*([^\\*]|(\\*[^\\/]))*\\*\\/\\s*)*(([)]\\s*:)|((\\.\\.\\.\\s*)?[_$A-Za-z][_$0-9A-Za-z]*\\s*:)))|([<]\\s*[_$A-Za-z][_$0-9A-Za-z]*\\s+extends\\s*[^=>])|((<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<]|<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<]|<\\s*(((const\\s+)?[_$A-Za-z])|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\]))([^=<>]|=[^<])*>)*>)*>\\s*)?\\(\\s*(\\/\\*([^\\*]|(\\*[^\\/]))*\\*\\/\\s*)*(([_$A-Za-z]|(\\{([^{}]|(\\{([^{}]|\\{[^{}]*\\})*\\}))*\\})|(\\[([^\\[\\]]|(\\[([^\\[\\]]|\\[[^\\[\\]]*\\])*\\]))*\\])|(\\.\\.\\.\\s*[_$A-Za-z]))([^()\\'\\\"\\`]|(\\(([^()]|(\\(([^()]|\\([^()]*\\))*\\)))*\\))|(\\'([^\\'\\\\]|\\\\.)*\\')|(\\\"([^\\\"\\\\]|\\\\.)*\\\")|(\\`([^\\`\\\\]|\\\\.)*\\`))*)?\\)(\\s*:\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\([^()]+\\)|\\{[^{}]+\\})+)?\\s*=>))))))",beginCaptures:{1:{name:"meta.definition.variable.tsx entity.name.function.tsx"},2:{name:"keyword.operator.definiteassignment.tsx"}},end:"(?=$|^|[;,=}]|((?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(of|in)\\s+)|(;|^\\s*$|(?:^\\s*(?:abstract|async|(?:\\bawait\\s+(?:\\busing(?=\\s+(?!in\\b|of\\b(?!\\s*(?:of\\b|=)))[_$A-Za-z])\\b)\\b)|break|case|catch|class|const|continue|declare|do|else|enum|export|finally|function|for|goto|if|import|interface|let|module|namespace|switch|return|throw|try|type|(?:\\busing(?=\\s+(?!in\\b|of\\b(?!\\s*(?:of\\b|=)))[_$A-Za-z])\\b)|var|while)\\b)))",name:"meta.var-single-variable.expr.tsx",patterns:[{include:"#var-single-variable-type-annotation"}]},{begin:"([A-Z][_$\\dA-Z]*)(?![_$0-9A-Za-z])(!)?",beginCaptures:{1:{name:"meta.definition.variable.tsx variable.other.constant.tsx"},2:{name:"keyword.operator.definiteassignment.tsx"}},end:"(?=$|^|[;,=}]|((?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(of|in)\\s+)|(;|^\\s*$|(?:^\\s*(?:abstract|async|(?:\\bawait\\s+(?:\\busing(?=\\s+(?!in\\b|of\\b(?!\\s*(?:of\\b|=)))[_$A-Za-z])\\b)\\b)|break|case|catch|class|const|continue|declare|do|else|enum|export|finally|function|for|goto|if|import|interface|let|module|namespace|switch|return|throw|try|type|(?:\\busing(?=\\s+(?!in\\b|of\\b(?!\\s*(?:of\\b|=)))[_$A-Za-z])\\b)|var|while)\\b)))",name:"meta.var-single-variable.expr.tsx",patterns:[{include:"#var-single-variable-type-annotation"}]},{begin:"([_$A-Za-z][_$0-9A-Za-z]*)(!)?",beginCaptures:{1:{name:"meta.definition.variable.tsx variable.other.readwrite.tsx"},2:{name:"keyword.operator.definiteassignment.tsx"}},end:"(?=$|^|[;,=}]|((?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(of|in)\\s+)|(;|^\\s*$|(?:^\\s*(?:abstract|async|(?:\\bawait\\s+(?:\\busing(?=\\s+(?!in\\b|of\\b(?!\\s*(?:of\\b|=)))[_$A-Za-z])\\b)\\b)|break|case|catch|class|const|continue|declare|do|else|enum|export|finally|function|for|goto|if|import|interface|let|module|namespace|switch|return|throw|try|type|(?:\\busing(?=\\s+(?!in\\b|of\\b(?!\\s*(?:of\\b|=)))[_$A-Za-z])\\b)|var|while)\\b)))",name:"meta.var-single-variable.expr.tsx",patterns:[{include:"#var-single-variable-type-annotation"}]}]},"var-single-variable-type-annotation":{patterns:[{include:"#type-annotation"},{include:"#string"},{include:"#comment"}]},"variable-initializer":{patterns:[{begin:"(?<!=|!)(=)(?!=)(?=\\s*\\S)(?!\\s*.*=>\\s*$)",beginCaptures:{1:{name:"keyword.operator.assignment.tsx"}},end:"(?=$|^|[,);}\\]]|((?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(of|in)\\s+))",patterns:[{include:"#expression"}]},{begin:"(?<!=|!)(=)(?!=)",beginCaptures:{1:{name:"keyword.operator.assignment.tsx"}},end:"(?=[,);}\\]]|((?<![_$0-9A-Za-z])(?:(?<=\\.\\.\\.)|(?<!\\.))(of|in)\\s+))|(?=^\\s*$)|(?<![\\|\\&+\\-\\*\\/])(?<=\\S)(?<!=)(?=\\s*$)",patterns:[{include:"#expression"}]}]}},scopeName:"source.tsx"})]}};