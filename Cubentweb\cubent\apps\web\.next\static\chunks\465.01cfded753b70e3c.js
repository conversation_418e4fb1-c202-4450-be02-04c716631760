"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[465],{30465:(e,t,n)=>{n.r(t),n.d(t,{ClientToolbar:()=>y});var r=n(68776),a=n(50628),o=n(35062),i=(0,r.P)({"../../node_modules/.pnpm/lodash.debounce@4.0.8/node_modules/lodash.debounce/index.js"(e,t){var r=0/0,a=/^\s+|\s+$/g,o=/^[-+]0x[0-9a-f]+$/i,i=/^0b[01]+$/i,l=/^0o[0-7]+$/i,s=parseInt,c="object"==typeof n.g&&n.g&&n.g.Object===Object&&n.g,p="object"==typeof self&&self&&self.Object===Object&&self,b=c||p||Function("return this")(),d=Object.prototype.toString,f=Math.max,u=Math.min,_=function(){return b.Date.now()};function y(e){var t=typeof e;return!!e&&("object"==t||"function"==t)}function m(e){if("number"==typeof e)return e;if("symbol"==typeof(t=e)||t&&"object"==typeof t&&"[object Symbol]"==d.call(t))return r;if(y(e)){var t,n="function"==typeof e.valueOf?e.valueOf():e;e=y(n)?n+"":n}if("string"!=typeof e)return 0===e?e:+e;e=e.replace(a,"");var c=i.test(e);return c||l.test(e)?s(e.slice(2),c?2:8):o.test(e)?r:+e}t.exports=function(e,t,n){var r,a,o,i,l,s,c=0,p=!1,b=!1,d=!0;if("function"!=typeof e)throw TypeError("Expected a function");function h(t){var n=r,o=a;return r=a=void 0,c=t,i=e.apply(o,n)}function g(e){var n=e-s,r=e-c;return void 0===s||n>=t||n<0||b&&r>=o}function w(){var e,n,r,a=_();if(g(a))return v(a);l=setTimeout(w,(e=a-s,n=a-c,r=t-e,b?u(r,o-n):r))}function v(e){return(l=void 0,d&&r)?h(e):(r=a=void 0,i)}function x(){var e,n=_(),o=g(n);if(r=arguments,a=this,s=n,o){if(void 0===l)return c=e=s,l=setTimeout(w,t),p?h(e):i;if(b)return l=setTimeout(w,t),h(s)}return void 0===l&&(l=setTimeout(w,t)),i}return t=m(t)||0,y(n)&&(p=!!n.leading,o=(b="maxWait"in n)?f(m(n.maxWait)||0,t):o,d="trailing"in n?!!n.trailing:d),x.cancel=function(){void 0!==l&&clearTimeout(l),c=0,r=s=a=l=void 0},x.flush=function(){return void 0===l?i:v(_())},x}}}),l="dffb3111f2dbe90df2c9f44aa745e1eaea5704ee2372695a11b6c736b47349b7";!function(){if("undefined"!=typeof document&&!document.getElementById(l)){var e=document.createElement("style");e.id=l,e.textContent='._wrapper_ypbb5_1 {\n  box-sizing: border-box;\n  font-size: 16px;\n}\n._wrapper_ypbb5_1 *,\n._wrapper_ypbb5_1 *:before,\n._wrapper_ypbb5_1 *:after {\n  box-sizing: inherit;\n}\n._wrapper_ypbb5_1 h1,\n._wrapper_ypbb5_1 h2,\n._wrapper_ypbb5_1 h3,\n._wrapper_ypbb5_1 h4,\n._wrapper_ypbb5_1 h5,\n._wrapper_ypbb5_1 h6,\n._wrapper_ypbb5_1 p,\n._wrapper_ypbb5_1 ol,\n._wrapper_ypbb5_1 ul {\n  margin: 0;\n  padding: 0;\n  font-weight: normal;\n}\n._wrapper_ypbb5_1 ol,\n._wrapper_ypbb5_1 ul {\n  list-style: none;\n}\n._wrapper_ypbb5_1 img {\n  max-width: 100%;\n  height: auto;\n}\n\n._branch_ypbb5_32 {\n  padding-left: 9px;\n  padding-right: 12px;\n  height: 100%;\n  display: flex;\n  align-items: center;\n  font-weight: 500;\n  user-select: none;\n}\n\n._wrapper_ypbb5_1 {\n  position: fixed;\n  bottom: 32px;\n  right: 32px;\n  background: #0c0c0c;\n  z-index: 1000;\n  border-radius: 7px;\n  animation: _in_ypbb5_1 0.3s ease-out;\n  display: flex;\n}\n\n._root_ypbb5_53 {\n  --font-family: Inter, Segoe UI, Roboto, sans-serif, Apple Color Emoji,\n    Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji, sans-serif;\n  border-radius: 6px;\n  height: 36px;\n  color: white;\n  display: flex;\n  border: 1px solid #303030;\n  font-family: var(--font-family);\n}\n._root_ypbb5_53[data-draft-active=true] {\n  border-color: #ff6c02;\n  background-color: rgba(255, 108, 2, 0.15);\n}\n._root_ypbb5_53[data-draft-active=true]:has(button._draft_ypbb5_67:enabled:hover) {\n  border-color: #ff8b35;\n}\n\n._draft_ypbb5_67 {\n  all: unset;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 8px 10px;\n  cursor: pointer;\n  color: #646464;\n  border-left: 1px solid #303030;\n  border-radius: 0 5px 5px 0;\n  margin: -1px;\n}\n._draft_ypbb5_67:disabled:hover {\n  cursor: not-allowed;\n}\n._draft_ypbb5_67[data-active=true] {\n  border-color: #ff6c02;\n}\n._draft_ypbb5_67[data-active=true]:enabled:hover {\n  border-color: #ff8b35;\n  background-color: #ff8b35;\n}\n._draft_ypbb5_67[data-active=false] {\n  border: 1px solid #303030;\n}\n._draft_ypbb5_67[data-active=false]:enabled:hover {\n  background-color: #0c0c0c;\n}\n._draft_ypbb5_67:focus-visible {\n  outline: 1px solid;\n  outline-offset: -1px;\n  outline-color: #303030;\n  border-radius: 0 6px 6px 0;\n}\n._draft_ypbb5_67[data-active=true] {\n  color: #f3f3f3;\n  background-color: #ff6c02;\n}\n._draft_ypbb5_67[data-loading=false] ._draft_ypbb5_67[data-active=true] {\n  transition: color 0.2s, background-color 0.2s;\n}\n._draft_ypbb5_67[data-loading=false] ._draft_ypbb5_67[data-active=true]:enabled:hover {\n  color: #fff;\n}\n._draft_ypbb5_67[data-loading=true] {\n  cursor: wait !important;\n}\n._draft_ypbb5_67[data-loading=true] svg {\n  animation: _breathe_ypbb5_1 1s infinite;\n}\n\n._tooltipWrapper_ypbb5_122 {\n  position: relative;\n  display: flex;\n  height: 100%;\n}\n._tooltipWrapper_ypbb5_122:hover ._tooltip_ypbb5_122 {\n  visibility: visible;\n}\n\n._dragHandle_ypbb5_131 {\n  all: unset;\n  cursor: grab;\n}\n._dragHandle_ypbb5_131._dragging_ypbb5_135 {\n  cursor: grabbing;\n}\n._dragHandle_ypbb5_131:active {\n  cursor: grabbing;\n}\n\n._tooltip_ypbb5_122 {\n  position: absolute;\n  bottom: 40px;\n  left: 50%;\n  transform: translateX(-50%) translateY(0);\n  background-color: #0c0c0c;\n  border: 1px solid #303030;\n  color: white;\n  border-radius: 4px;\n  max-width: 250px;\n  width: max-content;\n  font-size: 14px;\n  z-index: 1000;\n  visibility: hidden;\n  --translate-x: -50%;\n}\n._tooltip_ypbb5_122._forceVisible_ypbb5_158 {\n  visibility: visible;\n}\n._tooltip_ypbb5_122._top_ypbb5_161 {\n  top: 40px;\n  bottom: unset;\n  transform: translateY(0) translateX(var(--translate-x));\n}\n._tooltip_ypbb5_122._top_ypbb5_161:before {\n  mask-image: linear-gradient(135deg, rgb(0, 0, 0) 31%, rgba(0, 0, 0, 0) 31%, rgba(0, 0, 0, 0) 100%);\n  top: -4.5px;\n  bottom: unset;\n  transform: translateX(var(--translate-x)) rotate(45deg);\n}\n._tooltip_ypbb5_122._bottom_ypbb5_172 {\n  bottom: unset;\n  top: -40px;\n  transform: translateY(0) translateX(var(--translate-x));\n}\n._tooltip_ypbb5_122._bottom_ypbb5_172:before {\n  bottom: -4.5px;\n  top: unset;\n  transform: translateX(0) rotate(45deg);\n}\n._tooltip_ypbb5_122._right_ypbb5_182 {\n  right: 0;\n  left: unset;\n  transform: translateX(0);\n  --translate-x: 0;\n}\n._tooltip_ypbb5_122._right_ypbb5_182:before {\n  right: 8px;\n  left: unset;\n  transform: translateX(--translate-x) rotate(45deg);\n}\n._tooltip_ypbb5_122._left_ypbb5_193 {\n  left: 50%;\n  right: unset;\n  transform: translateX(-50%);\n  --translate-x: -50%;\n}\n._tooltip_ypbb5_122._left_ypbb5_193:before {\n  left: 50%;\n  right: unset;\n  transform: translateX(-50%) rotate(45deg);\n}\n._tooltip_ypbb5_122:before {\n  z-index: -1;\n  mask-image: linear-gradient(-45deg, rgb(0, 0, 0) 31%, rgba(0, 0, 0, 0) 31%, rgba(0, 0, 0, 0) 100%);\n  content: "";\n  position: absolute;\n  bottom: -4.5px;\n  left: 50%;\n  width: 20px;\n  height: 20px;\n  background-color: #0c0c0c;\n  transform: rotate(45deg) translateX(-50%);\n  border-radius: 2px;\n  border: 1px solid #303030;\n}\n\n._branchSelect_ypbb5_219 {\n  height: 100%;\n  background: none;\n  border: none;\n  font-weight: 500;\n  font-size: 16px;\n  padding-right: 8px;\n  padding-bottom: 0px;\n  padding-top: 0px;\n  margin-bottom: 2px;\n  min-width: 80px;\n  max-width: 250px;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: normal;\n  outline: none;\n  color: inherit;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  opacity: 1;\n  font-family: var(--font-family);\n  appearance: none;\n  -webkit-appearance: none;\n  -moz-appearance: none;\n}\n\n._branchSelectIcon_ypbb5_245 {\n  position: absolute;\n  top: 50%;\n  transform: translateY(-50%);\n  right: 0;\n  pointer-events: none;\n}\n\n@keyframes _in_ypbb5_1 {\n  0% {\n    opacity: 0;\n    transform: translateY(4px) scale(0.98);\n  }\n  100% {\n    opacity: 1;\n    transform: translateY(0) scale(1);\n  }\n}\n@keyframes _breathe_ypbb5_1 {\n  0% {\n    opacity: 1;\n  }\n  50% {\n    opacity: 0.45;\n  }\n  100% {\n    opacity: 1;\n  }\n}',document.head.appendChild(e)}}();var s={wrapper:"_wrapper_ypbb5_1",branch:"_branch_ypbb5_32",root:"_root_ypbb5_53",draft:"_draft_ypbb5_67",tooltipWrapper:"_tooltipWrapper_ypbb5_122",tooltip:"_tooltip_ypbb5_122",dragHandle:"_dragHandle_ypbb5_131",dragging:"_dragging_ypbb5_135",forceVisible:"_forceVisible_ypbb5_158",top:"_top_ypbb5_161",bottom:"_bottom_ypbb5_172",right:"_right_ypbb5_182",left:"_left_ypbb5_193",branchSelect:"_branchSelect_ypbb5_219",branchSelectIcon:"_branchSelectIcon_ypbb5_245"},c=(0,r.f)(i(),1),p=a.forwardRef((e,t)=>{let{children:n,content:r,forceVisible:o}=e,i=a.useRef(null),l=a.useCallback((0,c.default)(()=>{if(i.current){let e=i.current.getBoundingClientRect(),t=i.current.classList.contains(s.left)?0:e.width/2,n=e.height;(i.current.classList.contains(s.bottom)?e.top:e.top-80-n)<=0?(i.current.classList.remove(s.bottom),i.current.classList.add(s.top)):(i.current.classList.remove(s.top),i.current.classList.add(s.bottom)),e.right+t>window.innerWidth?(i.current.classList.remove(s.left),i.current.classList.add(s.right)):(i.current.classList.remove(s.right),i.current.classList.add(s.left))}},100),[]);return a.useEffect(()=>(l(),window.addEventListener("resize",l),()=>{window.removeEventListener("resize",l)}),[l]),a.useImperativeHandle(t,()=>({checkOverflow:l}),[l]),a.createElement("div",{className:s.tooltipWrapper},a.createElement("p",{ref:i,style:{padding:"3px 8px"},className:o?"".concat(s.tooltip," ").concat(s.bottom," ").concat(s.left," ").concat(s.forceVisible):"".concat(s.tooltip," ").concat(s.bottom," ").concat(s.left)},r),n)}),b=a.forwardRef((e,t)=>{let{onDrag:n,children:r}=e,[o,i]=a.useState(!1),l=a.useRef({x:0,y:0}),c=a.useRef({x:0,y:0}),p=a.useRef(!1);a.useImperativeHandle(t,()=>({hasDragged:p.current}));let b=a.useCallback(e=>{if(!o)return;let t=e.clientX-l.current.x,r=e.clientY-l.current.y,a=c.current.x+t,i=c.current.y+r;(Math.abs(t)>2||Math.abs(r)>2)&&(p.current=!0),n({x:a,y:i})},[o,n]);return a.useLayoutEffect(()=>{if(o)return window.addEventListener("pointermove",b),()=>{window.removeEventListener("pointermove",b)}},[o,n,b]),a.useLayoutEffect(()=>{if(!o){p.current=!1;return}let e=()=>{i(!1)};return window.addEventListener("pointerup",e),()=>{window.removeEventListener("pointerup",e)}},[o]),a.createElement("span",{draggable:!0,className:"".concat(s.dragHandle," ").concat(o?s.dragging:""),onPointerDown:e=>{if(e.target instanceof HTMLElement&&("select"===e.target.nodeName.toLowerCase()||e.target.closest("select")))return;let t=e.currentTarget;if(!t)return;e.stopPropagation(),e.preventDefault(),l.current={x:e.clientX,y:e.clientY};let n=t.getBoundingClientRect();c.current.x=n.left,c.current.y=n.top,i(!0)},onPointerUp:()=>{i(!1)}},r)}),d=e=>{let{isForcedDraft:t,draft:n,apiRref:r,latestBranches:o,onRefChange:i,getAndSetLatestBranches:l}=e,c=a.useRef(null),b=a.useRef(null),d=a.useMemo(()=>[...o].sort((e,t)=>e.isDefault?-1:t.isDefault?1:e.name.localeCompare(t.name)),[o]),u=a.useMemo(()=>{let e=new Set(d.map(e=>e.name));return e.add(r),Array.from(e)},[d,r]),[_,y]=a.useState(!1);a.useEffect(()=>{_&&l().then(()=>{y(!1)})},[_,l]),a.useEffect(()=>{let e=c.current,t=b.current;if(!e||!t)return;let n=()=>{let n=e.offsetWidth;t.style.width="".concat(n+20,"px")};return n(),window.addEventListener("resize",n),()=>{window.removeEventListener("resize",n),t&&t.style.removeProperty("width")}},[r]);let m=t||n;return a.createElement("div",{className:s.branch,"data-draft-active":m,onMouseEnter:()=>{y(!0)}},a.createElement(f,null),"\xa0",a.createElement(p,{content:m?"Switch branch":"Switch branch and enter draft mode"},a.createElement("select",{ref:b,value:r,onChange:e=>i(e.target.value,{enableDraftMode:!m}),className:s.branchSelect,onMouseDown:e=>{e.stopPropagation()},onClick:e=>{e.stopPropagation(),y(!0)}},u.map(e=>a.createElement("option",{key:e,value:e},e))),a.createElement("svg",{width:"15",height:"15",viewBox:"0 0 15 15",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:s.branchSelectIcon},a.createElement("path",{d:"M4.93179 5.43179C4.75605 5.60753 4.75605 5.89245 4.93179 6.06819C5.10753 6.24392 5.39245 6.24392 5.56819 6.06819L7.49999 4.13638L9.43179 6.06819C9.60753 6.24392 9.89245 6.24392 10.0682 6.06819C10.2439 5.89245 10.2439 5.60753 10.0682 5.43179L7.81819 3.18179C7.73379 3.0974 7.61933 3.04999 7.49999 3.04999C7.38064 3.04999 7.26618 3.0974 7.18179 3.18179L4.93179 5.43179ZM10.0682 9.56819C10.2439 9.39245 10.2439 9.10753 10.0682 8.93179C9.89245 8.75606 9.60753 8.75606 9.43179 8.93179L7.49999 10.8636L5.56819 8.93179C5.39245 8.75606 5.10753 8.75606 4.93179 8.93179C4.75605 9.10753 4.75605 9.39245 4.93179 9.56819L7.18179 11.8182C7.35753 11.9939 7.64245 11.9939 7.81819 11.8182L10.0682 9.56819Z",fill:"currentColor",fillRule:"evenodd",clipRule:"evenodd"}))),a.createElement("span",{className:s.branchSelect,style:{visibility:"hidden",opacity:0,pointerEvents:"none",position:"absolute",top:0,left:0},"aria-hidden":"true",ref:c},r))},f=()=>a.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"18",height:"18",fill:"none"},a.createElement("path",{fill:"#F3F3F3",fillRule:"evenodd",d:"M12.765 5.365a1.25 1.25 0 1 0 .002-2.502 1.25 1.25 0 0 0-.002 2.502Zm0 1.063a2.315 2.315 0 1 0-2.315-2.313 2.315 2.315 0 0 0 2.316 2.313ZM5.234 15.137a1.25 1.25 0 1 0 .001-2.501 1.25 1.25 0 0 0 0 2.501Zm0 1.064a2.315 2.315 0 1 0-2.316-2.314 2.315 2.315 0 0 0 2.316 2.314Z",clipRule:"evenodd"}),a.createElement("path",{fill:"#F3F3F3",fillRule:"evenodd",d:"M5.767 8.98v3.648H4.702V8.98h1.065ZM13.298 5.798v2.694h-1.065V5.798h1.065Z",clipRule:"evenodd"}),a.createElement("path",{fill:"#F3F3F3",fillRule:"evenodd",d:"M13.298 8.448a.532.532 0 0 1-.533.532H5.29a.532.532 0 1 1 0-1.064h7.476c.294 0 .533.238.533.532ZM5.234 2.864a1.25 1.25 0 1 1 .001 2.502 1.25 1.25 0 0 1 0-2.502Zm0-1.063a2.315 2.315 0 1 1-2.316 2.314A2.315 2.315 0 0 1 5.234 1.8Z",clipRule:"evenodd"}),a.createElement("path",{fill:"#F3F3F3",fillRule:"evenodd",d:"M5.767 9.022V5.374H4.702v3.648h1.065Z",clipRule:"evenodd"})),u=(0,r.f)(i(),1),_="bshb_toolbar_pos",y=e=>{let{draft:t,isForcedDraft:n,enableDraftMode:r,disableDraftMode:o,bshbPreviewToken:i,shouldAutoEnableDraft:l,seekAndStoreBshbPreviewToken:c,resolvedRef:f,getLatestBranches:y}=e,[w,v]=a.useState(null),x=a.useRef(null),L=a.useRef(null),[E,C]=a.useState(""),[k,S]=a.useState(!1),[M,R]=a.useState(f.ref),[Z,P]=a.useState(!0),[j,D]=a.useState(!0),[H,N]=a.useState([]),z=a.useRef(0),T=a.useCallback(e=>{window.clearTimeout(z.current),C(e),z.current=window.setTimeout(()=>C(""),5e3)},[C]),I=a.useCallback(e=>{S(!0),r({bshbPreviewToken:e}).then(e=>{let{status:t,response:n}=e;200===t?(N(e=>{var t;return null!=(t=n.latestBranches)?t:e}),window.location.reload()):"error"in n?T("Draft mode activation error: ".concat(n.error)):T("Draft mode activation error")}).finally(()=>S(!1))},[r,T]),F="bshb-preview-ref-".concat(f.repoHash),O=a.useMemo(()=>({set:e=>{document.cookie="".concat(F,"=").concat(e,"; path=/; Max-Age=").concat(94608e4)},clear:()=>{document.cookie="".concat(F,"=; path=/; Max-Age=-1")},get:()=>{var e,t;return null!=(t=null==(e=document.cookie.split("; ").find(e=>e.startsWith(F)))?void 0:e.split("=")[1])?t:null}}),[F]),[V,X]=a.useState(!1);a.useLayoutEffect(()=>{t||V||!l||n||!i||(I(i),X(!0))},[n,r,c,i,T,I,t,l,V]);let W=a.useCallback(async()=>{let e=[],t=await y({bshbPreviewToken:i});t&&(Array.isArray(t.response)?e=t.response:"error"in t.response&&console.error("BaseHub Toolbar Error: ".concat(t.response.error)),N(e))},[i,y]);a.useEffect(()=>{!async function(){for(;;)try{W(),await new Promise(e=>setTimeout(e,3e4))}catch(e){console.error("BaseHub Toolbar Error: ".concat(e));break}}()},[W]);let B=a.useCallback(e=>{R(e),window.__bshb_ref=e,window.dispatchEvent(new CustomEvent("__bshb_ref_changed")),O.set(e),P(e===f.ref)},[O,f.ref]);a.useEffect(()=>{let e=new URL(window.location.href).searchParams.get("bshb-preview-ref");e||(e=O.get()),D(!1),e&&B(e)},[O,B,f.repoHash]),a.useEffect(()=>{j||P(M===f.ref)},[M,f.ref,j]),a.useEffect(()=>{if(!j&&Z){B(f.ref),O.clear();let e=new URL(window.location.href);e.searchParams.delete("bshb-preview-ref"),window.history.replaceState(null,"",e.toString())}},[Z,j,O,f.ref,B]),a.useLayoutEffect(()=>{var e;null==(e=L.current)||e.checkOverflow()},[E]);let U=a.useCallback(()=>{if(!w||!window.sessionStorage)return;let e=window.sessionStorage.getItem(_);if(!e)return;let t=JSON.parse(e);if("x"in t&&"y"in t)return t},[w]),Y=a.useCallback((0,u.default)(e=>{var t;if(!window.sessionStorage)return;let n=null!=(t=U())?t:{x:0,y:0};window.sessionStorage.setItem(_,JSON.stringify({...n,...e}))},250),[]),A=a.useCallback(e=>{if(!w)return;let t=w.getBoundingClientRect(),n={};e.x-32<0?(w.style.left="".concat(32,"px"),w.style.right="unset",n.x=32):e.x+t.width+32>window.innerWidth?(w.style.right="".concat(32,"px"),w.style.left="unset",n.x=32):(w.style.right="unset",w.style.left="".concat(e.x,"px"),n.x=e.x),e.y-32<0?(w.style.bottom="unset",w.style.top="".concat(32,"px"),n.y=32):e.y+t.height+32>window.innerHeight?(w.style.top="unset",w.style.bottom="".concat(32,"px"),n.y=32):(w.style.bottom="unset",w.style.top="".concat(e.y,"px"),n.x=e.y),Y({x:e.x,y:e.y})},[w,Y]);a.useEffect(()=>{let e=()=>{var e;let t=U();t&&(A(t),null==(e=L.current)||e.checkOverflow())};return e(),window.addEventListener("resize",e),()=>{window.removeEventListener("resize",e)}},[U,A]),a.useEffect(()=>{if(!H)return;let e=O.get();e&&(H.find(t=>t.name===e)||O.clear())},[H,O]);let $=n?"Draft enforced by dev env":"".concat(t?"Disable":"Enable"," draft mode");return a.createElement("div",{className:s.wrapper,ref:v},a.createElement(b,{ref:x,onDrag:e=>{var t;A(e),null==(t=L.current)||t.checkOverflow()}},a.createElement("div",{className:s.root,"data-draft-active":n||t},a.createElement(d,{isForcedDraft:n,draft:t,apiRref:M,latestBranches:H,onRefChange:(e,t)=>{let n=new URL(window.location.href);if(n.searchParams.set("bshb-preview-ref",e),window.history.replaceState(null,"",n.toString()),B(e),t.enableDraftMode){let e=null!=i?i:c();if(!e)return T("Preview token not found");I(e)}},getAndSetLatestBranches:W}),a.createElement(m,{previewRef:M,resolvedRef:f,isDraftModeEnabled:n||t}),a.createElement(p,{content:E||$,ref:L,forceVisible:!!E},a.createElement("button",{className:s.draft,"data-active":n||t,"aria-label":"".concat(t?"Disable":"Enable"," draft mode"),"data-loading":k,disabled:n||k,onClick:()=>{var e;if(!k&&(null==(e=x.current)||!e.hasDragged))if(t)S(!0),o().then(()=>{let e=new URL(window.location.href);e.searchParams.delete("bshb-preview"),e.searchParams.delete("__vercel_draft"),window.location.href=e.toString()}).finally(()=>S(!1));else{let e=null!=i?i:c();if(!e)return T("Preview token not found");I(e)}}},t||n?a.createElement(g,null):a.createElement(h,null))))))},m=e=>{let{previewRef:t,resolvedRef:n,isDraftModeEnabled:r}=e,i=(0,o.usePathname)(),[l,s]=a.useState(i);return a.useEffect(()=>{l||s(i)},[i,l]),a.useEffect(()=>{if(!r&&l!==i&&t!==n.ref){let e=new URL(window.location.href);e.searchParams.set("bshb-preview-ref",t),window.history.replaceState(null,"",e.toString())}},[r,t,n.ref,i,l]),null},h=()=>a.createElement("svg",{"data-testid":"geist-icon",height:"16",strokeLinejoin:"round",viewBox:"0 0 16 16",width:"16",style:{color:"currentcolor"}},a.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M6.51404 3.15793C7.48217 2.87411 8.51776 2.87411 9.48589 3.15793L9.90787 1.71851C8.66422 1.35392 7.33571 1.35392 6.09206 1.71851L6.51404 3.15793ZM10.848 3.78166C11.2578 4.04682 11.6393 4.37568 11.9783 4.76932L13.046 6.00934L14.1827 5.03056L13.1149 3.79054C12.6818 3.28761 12.1918 2.86449 11.6628 2.52224L10.848 3.78166ZM4.02168 4.76932C4.36065 4.37568 4.74209 4.04682 5.15195 3.78166L4.33717 2.52225C3.80815 2.86449 3.3181 3.28761 2.88503 3.79054L1.81723 5.03056L2.95389 6.00934L4.02168 4.76932ZM14.1138 7.24936L14.7602 7.99999L14.1138 8.75062L15.2505 9.72941L16.3183 8.48938V7.5106L15.2505 6.27058L14.1138 7.24936ZM1.88609 7.24936L1.23971 7.99999L1.88609 8.75062L0.749437 9.72941L-0.318359 8.48938V7.5106L0.749436 6.27058L1.88609 7.24936ZM13.0461 9.99064L11.9783 11.2307C11.6393 11.6243 11.2578 11.9532 10.848 12.2183L11.6628 13.4777C12.1918 13.1355 12.6818 12.7124 13.1149 12.2094L14.1827 10.9694L13.0461 9.99064ZM4.02168 11.2307L2.95389 9.99064L1.81723 10.9694L2.88503 12.2094C3.3181 12.7124 3.80815 13.1355 4.33717 13.4777L5.15195 12.2183C4.7421 11.9532 4.36065 11.6243 4.02168 11.2307ZM9.90787 14.2815L9.48589 12.8421C8.51776 13.1259 7.48217 13.1259 6.51405 12.8421L6.09206 14.2815C7.33572 14.6461 8.66422 14.6461 9.90787 14.2815ZM6.49997 7.99999C6.49997 7.17157 7.17154 6.49999 7.99997 6.49999C8.82839 6.49999 9.49997 7.17157 9.49997 7.99999C9.49997 8.82842 8.82839 9.49999 7.99997 9.49999C7.17154 9.49999 6.49997 8.82842 6.49997 7.99999ZM7.99997 4.99999C6.34311 4.99999 4.99997 6.34314 4.99997 7.99999C4.99997 9.65685 6.34311 11 7.99997 11C9.65682 11 11 9.65685 11 7.99999C11 6.34314 9.65682 4.99999 7.99997 4.99999Z",fill:"currentColor"})),g=()=>a.createElement("svg",{"data-testid":"geist-icon",height:"16",strokeLinejoin:"round",viewBox:"0 0 16 16",width:"16",style:{color:"currentcolor"}},a.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M4.02168 4.76932C6.11619 2.33698 9.88374 2.33698 11.9783 4.76932L14.7602 7.99999L11.9783 11.2307C9.88374 13.663 6.1162 13.663 4.02168 11.2307L1.23971 7.99999L4.02168 4.76932ZM13.1149 3.79054C10.422 0.663244 5.57797 0.663247 2.88503 3.79054L-0.318359 7.5106V8.48938L2.88503 12.2094C5.57797 15.3367 10.422 15.3367 13.1149 12.2094L16.3183 8.48938V7.5106L13.1149 3.79054ZM6.49997 7.99999C6.49997 7.17157 7.17154 6.49999 7.99997 6.49999C8.82839 6.49999 9.49997 7.17157 9.49997 7.99999C9.49997 8.82842 8.82839 9.49999 7.99997 9.49999C7.17154 9.49999 6.49997 8.82842 6.49997 7.99999ZM7.99997 4.99999C6.34311 4.99999 4.99997 6.34314 4.99997 7.99999C4.99997 9.65685 6.34311 11 7.99997 11C9.65682 11 11 9.65685 11 7.99999C11 6.34314 9.65682 4.99999 7.99997 4.99999Z",fill:"currentColor"}))},35062:(e,t,n)=>{n.r(t);var r=n(43914),a={};for(let e in r)"default"!==e&&(a[e]=()=>r[e]);n.d(t,a)}}]);