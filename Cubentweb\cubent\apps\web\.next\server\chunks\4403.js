"use strict";exports.id=4403,exports.ids=[4403],exports.modules={4403:(e,n,t)=>{t.r(n),t.d(n,{default:()=>o});var o=[Object.freeze({displayName:"CODEOWNERS",name:"codeowners",patterns:[{include:"#comment"},{include:"#pattern"},{include:"#owner"}],repository:{comment:{patterns:[{begin:"^\\s*#",captures:{0:{name:"punctuation.definition.comment.codeowners"}},end:"$",name:"comment.line.codeowners"}]},owner:{match:"\\S*@\\S+",name:"storage.type.function.codeowners"},pattern:{match:"^\\s*(\\S+)",name:"variable.other.codeowners"}},scopeName:"text.codeowners"})]}};