(()=>{var e={};e.id=1518,e.ids=[1518],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10968:()=>{},16248:(e,t,i)=>{"use strict";i.d(t,{II:()=>n});var r=i(59986),o=i(25);let n=()=>(0,r.w)({server:{VERCEL:o.z.string().optional(),CI:o.z.string().optional(),VERCEL_ENV:o.z.enum(["development","preview","production"]).optional(),VERCEL_URL:o.z.string().optional(),VERCEL_PROJECT_PRODUCTION_URL:o.z.string().optional(),VERCEL_BRANCH_URL:o.z.string().optional(),VERCEL_REGION:o.z.string().optional(),VERCEL_DEPLOYMENT_ID:o.z.string().optional(),VERCEL_SKEW_PROTECTION_ENABLED:o.z.string().optional(),VERCEL_AUTOMATION_BYPASS_SECRET:o.z.string().optional(),VERCEL_GIT_PROVIDER:o.z.string().optional(),VERCEL_GIT_REPO_SLUG:o.z.string().optional(),VERCEL_GIT_REPO_OWNER:o.z.string().optional(),VERCEL_GIT_REPO_ID:o.z.string().optional(),VERCEL_GIT_COMMIT_REF:o.z.string().optional(),VERCEL_GIT_COMMIT_SHA:o.z.string().optional(),VERCEL_GIT_COMMIT_MESSAGE:o.z.string().optional(),VERCEL_GIT_COMMIT_AUTHOR_LOGIN:o.z.string().optional(),VERCEL_GIT_COMMIT_AUTHOR_NAME:o.z.string().optional(),VERCEL_GIT_PREVIOUS_SHA:o.z.string().optional(),VERCEL_GIT_PULL_REQUEST_ID:o.z.string().optional()},runtimeEnv:process.env})},22830:(e,t)=>{"use strict";function i(e){return Array.isArray(e)?e:[e]}function r(e){if(null!=e)return i(e)}function o(e){let t;if("string"==typeof e)try{t=(e=new URL(e)).origin}catch{}return t}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{getOrigin:function(){return o},resolveArray:function(){return i},resolveAsArrayOrUndefined:function(){return r}})},26142:(e,t,i)=>{"use strict";e.exports=i(44870)},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66932:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{resolveManifest:function(){return a},resolveRobots:function(){return o},resolveRouteData:function(){return s},resolveSitemap:function(){return n}});let r=i(22830);function o(e){let t="";for(let i of Array.isArray(e.rules)?e.rules:[e.rules]){for(let e of(0,r.resolveArray)(i.userAgent||["*"]))t+=`User-Agent: ${e}
`;if(i.allow)for(let e of(0,r.resolveArray)(i.allow))t+=`Allow: ${e}
`;if(i.disallow)for(let e of(0,r.resolveArray)(i.disallow))t+=`Disallow: ${e}
`;i.crawlDelay&&(t+=`Crawl-delay: ${i.crawlDelay}
`),t+="\n"}return e.host&&(t+=`Host: ${e.host}
`),e.sitemap&&(0,r.resolveArray)(e.sitemap).forEach(e=>{t+=`Sitemap: ${e}
`}),t}function n(e){let t=e.some(e=>Object.keys(e.alternates??{}).length>0),i=e.some(e=>{var t;return!!(null==(t=e.images)?void 0:t.length)}),r=e.some(e=>{var t;return!!(null==(t=e.videos)?void 0:t.length)}),o="";for(let l of(o+='<?xml version="1.0" encoding="UTF-8"?>\n',o+='<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"',i&&(o+=' xmlns:image="http://www.google.com/schemas/sitemap-image/1.1"'),r&&(o+=' xmlns:video="http://www.google.com/schemas/sitemap-video/1.1"'),t?o+=' xmlns:xhtml="http://www.w3.org/1999/xhtml">\n':o+=">\n",e)){var n,a,s;o+="<url>\n",o+=`<loc>${l.url}</loc>
`;let e=null==(n=l.alternates)?void 0:n.languages;if(e&&Object.keys(e).length)for(let t in e)o+=`<xhtml:link rel="alternate" hreflang="${t}" href="${e[t]}" />
`;if(null==(a=l.images)?void 0:a.length)for(let e of l.images)o+=`<image:image>
<image:loc>${e}</image:loc>
</image:image>
`;if(null==(s=l.videos)?void 0:s.length)for(let e of l.videos)o+=["<video:video>",`<video:title>${e.title}</video:title>`,`<video:thumbnail_loc>${e.thumbnail_loc}</video:thumbnail_loc>`,`<video:description>${e.description}</video:description>`,e.content_loc&&`<video:content_loc>${e.content_loc}</video:content_loc>`,e.player_loc&&`<video:player_loc>${e.player_loc}</video:player_loc>`,e.duration&&`<video:duration>${e.duration}</video:duration>`,e.view_count&&`<video:view_count>${e.view_count}</video:view_count>`,e.tag&&`<video:tag>${e.tag}</video:tag>`,e.rating&&`<video:rating>${e.rating}</video:rating>`,e.expiration_date&&`<video:expiration_date>${e.expiration_date}</video:expiration_date>`,e.publication_date&&`<video:publication_date>${e.publication_date}</video:publication_date>`,e.family_friendly&&`<video:family_friendly>${e.family_friendly}</video:family_friendly>`,e.requires_subscription&&`<video:requires_subscription>${e.requires_subscription}</video:requires_subscription>`,e.live&&`<video:live>${e.live}</video:live>`,e.restriction&&`<video:restriction relationship="${e.restriction.relationship}">${e.restriction.content}</video:restriction>`,e.platform&&`<video:platform relationship="${e.platform.relationship}">${e.platform.content}</video:platform>`,e.uploader&&`<video:uploader${e.uploader.info&&` info="${e.uploader.info}"`}>${e.uploader.content}</video:uploader>`,`</video:video>
`].filter(Boolean).join("\n");if(l.lastModified){let e=l.lastModified instanceof Date?l.lastModified.toISOString():l.lastModified;o+=`<lastmod>${e}</lastmod>
`}l.changeFrequency&&(o+=`<changefreq>${l.changeFrequency}</changefreq>
`),"number"==typeof l.priority&&(o+=`<priority>${l.priority}</priority>
`),o+="</url>\n"}return o+"</urlset>\n"}function a(e){return JSON.stringify(e)}function s(e,t){return"robots"===t?o(e):"sitemap"===t?n(e):"manifest"===t?a(e):""}},73024:e=>{"use strict";e.exports=require("node:fs")},81138:(e,t,i)=>{"use strict";i.a(e,async(e,r)=>{try{i.r(t),i.d(t,{GET:()=>l});var o=i(26239),n=i(98584),a=i(66932),s=e([n]);let u={...n=(s.then?(await s)():s)[0]}.default;if("function"!=typeof u)throw Error('Default export is missing in "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\web\\app\\[locale]\\sitemap.ts"');async function l(e,t){let{__metadata_id__:i,...r}=await t.params||{},n=!!i&&i.endsWith(".xml");if(i&&!n)return new o.NextResponse("Not Found",{status:404});let s=i&&n?i.slice(0,-4):void 0,l=await u({id:s}),p=(0,a.resolveRouteData)(l,"sitemap");return new o.NextResponse(p,{headers:{"Content-Type":"application/xml","Cache-Control":"public, max-age=0, must-revalidate"}})}r()}catch(e){r(e)}})},89259:()=>{},90116:(e,t,i)=>{"use strict";i.a(e,async(e,r)=>{try{i.r(t),i.d(t,{patchFetch:()=>u,routeModule:()=>p,serverHooks:()=>_,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>c});var o=i(26142),n=i(94327),a=i(34862),s=i(81138),l=e([s]);s=(l.then?(await l)():l)[0];let p=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/[locale]/sitemap.xml/route",pathname:"/[locale]/sitemap.xml",filename:"sitemap",bundlePath:"app/[locale]/sitemap.xml/route"},resolvedPagePath:"next-metadata-route-loader?filePath=C%3A%5CUsers%5Canwar%5CDocuments%5C2%20FOLDERS%20FOR%20CUBENT%5CCubentweb%5Ccubent%5Capps%5Cweb%5Capp%5C%5Blocale%5D%5Csitemap.ts&isDynamicRouteExtension=1!?__next_metadata_route__",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:c,serverHooks:_}=p;function u(){return(0,a.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:c})}r()}catch(e){r(e)}})},98584:(e,t,i)=>{"use strict";i.a(e,async(e,r)=>{try{i.r(t),i.d(t,{default:()=>c});var o=i(73024),n=i.n(o),a=i(29804),s=i(23055);let e=n().readdirSync("app",{withFileTypes:!0}).filter(e=>e.isDirectory()).filter(e=>!e.name.startsWith("_")).filter(e=>!e.name.startsWith("(")).map(e=>e.name),l=(await s.h.getPosts()).map(e=>e._slug),u=(await s.a.getPosts()).map(e=>e._slug),p=a._.VERCEL_PROJECT_PRODUCTION_URL?.startsWith("https")?"https":"http",d=new URL(`${p}://${a._.VERCEL_PROJECT_PRODUCTION_URL}`),c=async()=>[{url:new URL("/",d).href,lastModified:new Date},...e.map(e=>({url:new URL(e,d).href,lastModified:new Date})),...l.map(e=>({url:new URL(`blog/${e}`,d).href,lastModified:new Date})),...u.map(e=>({url:new URL(`legal/${e}`,d).href,lastModified:new Date}))];r()}catch(e){r(e)}},1)}};var t=require("../../../webpack-runtime.js");t.C(e);var i=e=>t(t.s=e),r=t.X(0,[5319,3396,6239,9752],()=>i(90116));module.exports=r})();