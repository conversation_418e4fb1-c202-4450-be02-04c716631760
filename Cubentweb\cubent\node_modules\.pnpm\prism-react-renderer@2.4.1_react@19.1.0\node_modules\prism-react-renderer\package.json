{"name": "prism-react-renderer", "version": "2.4.1", "description": "Renders highlighted Prism output using React", "sideEffects": true, "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "license": "MIT", "repository": "https://github.com/FormidableLabs/prism-react-renderer", "files": ["index.d.ts", "dist"], "peerDependencies": {"react": ">=16.0.0"}, "devDependencies": {"@babel/core": "*", "@rollup/plugin-babel": "^6.0.3", "@rollup/plugin-buble": "^0.21.3", "@rollup/plugin-commonjs": "^19.0.2", "@rollup/plugin-json": "^6.0.0", "@rollup/plugin-node-resolve": "^11.2.0", "@rollup/plugin-typescript": "^11.1.0", "@testing-library/react": "^14.0.0", "@types/jest": "^29.5.0", "@types/node": "^18.15.11", "@vitejs/plugin-react": "^3.1.0", "babel-plugin-codegen": "^4.1.5", "happy-dom": "^9.7.1", "prismjs": "*", "react": "*", "react-dom": "*", "rollup": "^2.39.0", "ts-node": "^10.9.1", "tslib": "^2.5.0", "tsup": "^6.7.0", "typescript": "*", "vite": "^4.2.0", "vitest": "^0.30.1"}, "dependencies": {"@types/prismjs": "^1.26.0", "clsx": "^2.0.0"}, "publishConfig": {"provenance": true}, "scripts": {"build": "tsup", "build:watch": "tsup --watch", "test": "vitest", "typecheck": "tsc --noEmit", "lint": "eslint 'src/**/*.{js,jsx,ts,tsx}'", "format": "prettier --write 'src/**/*.js'"}}