(()=>{var e={};e.id=2689,e.ids=[2689],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},8086:e=>{"use strict";e.exports=require("module")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},13440:e=>{"use strict";e.exports=require("util/types")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{"use strict";e.exports=require("node:child_process")},31770:(e,t,s)=>{"use strict";s.d(t,{UsageAnalytics:()=>r});let r=(0,s(6340).registerClientReference)(function(){throw Error("Attempted to call UsageAnalytics() from the server but UsageAnalytics is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\(authenticated)\\profile\\usage\\components\\usage-analytics.tsx","UsageAnalytics")},33873:e=>{"use strict";e.exports=require("path")},34069:(e,t,s)=>{"use strict";s.d(t,{w:()=>l});var r=s(81121),a=s.n(r);let i="next-forge",n={name:"Vercel",url:"https://vercel.com/"},o=process.env.VERCEL_PROJECT_PRODUCTION_URL,l=({title:e,description:t,image:s,...r})=>{let l=`${e} | ${i}`,d={title:l,description:t,applicationName:i,metadataBase:o?new URL(`https://${o}`):void 0,authors:[n],creator:n.name,formatDetection:{telephone:!1},appleWebApp:{capable:!0,statusBarStyle:"default",title:l},openGraph:{title:l,description:t,type:"website",siteName:i,locale:"en_US"},publisher:"Vercel",twitter:{card:"summary_large_image",creator:"@vercel"}},c=a()(d,r);return s&&c.openGraph&&(c.openGraph.images=[{url:s,width:1200,height:630,alt:e}]),c}},34631:e=>{"use strict";e.exports=require("tls")},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},37067:e=>{"use strict";e.exports=require("node:http")},38522:e=>{"use strict";e.exports=require("node:zlib")},40600:(e,t,s)=>{Promise.resolve().then(s.bind(s,31770))},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},44708:e=>{"use strict";e.exports=require("node:https")},48161:e=>{"use strict";e.exports=require("node:os")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},54287:e=>{"use strict";e.exports=require("console")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57416:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>u,metadata:()=>c});var r=s(94752),a=s(37838),i=s(1359),n=s(18815),o=s(34069),l=s(62923),d=s(31770);let c=(0,o.w)({title:"Cubent Units Usage",description:"View your VS Code extension usage statistics and analytics."}),u=async()=>{let{userId:e}=await (0,a.j)(),t=await (0,i.N)();e&&t||(0,l.redirect)("/sign-in");let s=await n.database.user.upsert({where:{clerkId:e},update:{email:t.emailAddresses[0]?.emailAddress||"",name:`${t.firstName||""} ${t.lastName||""}`.trim()||null,picture:t.imageUrl},create:{clerkId:e,email:t.emailAddresses[0]?.emailAddress||"",name:`${t.firstName||""} ${t.lastName||""}`.trim()||null,picture:t.imageUrl},select:{id:!0,cubentUnitsUsed:!0,cubentUnitsLimit:!0,subscriptionTier:!0,usageMetrics:{orderBy:{date:"desc"},take:30,select:{date:!0,cubentUnitsUsed:!0,requestsMade:!0}}}}),o=await n.database.usageAnalytics.count({where:{userId:s.id}}),c={totalCubentUnits:s.cubentUnitsUsed||0,totalMessages:o,userLimit:s.cubentUnitsLimit||50,subscriptionTier:s.subscriptionTier||"free_trial",chartData:s.usageMetrics,user:{name:t.firstName||"User",email:t.emailAddresses[0]?.emailAddress||"",picture:t.imageUrl}};return(0,r.jsx)(d.UsageAnalytics,{initialData:c})}},57975:e=>{"use strict";e.exports=require("node:util")},58940:(e,t,s)=>{"use strict";s.d(t,{BT:()=>l,Wu:()=>d,ZB:()=>o,Zp:()=>i,aR:()=>n});var r=s(99730);s(57752);var a=s(83590);function i({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function n({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function o({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...t})}function l({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...t})}function d({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...t})}},59988:(e,t,s)=>{"use strict";s.r(t),s.d(t,{"7fa248ee4cee001992d543e3927d536ddea63c121c":()=>r.ai,"7fd10f19b29f8e8b2951e0bb60d9466e540ba24937":()=>r.ot,"7fe80fb1c9bdcbbae5a7da0586440a249dd4fb207a":()=>a.y,"7ffdf714159b7e9cad55b4d3d168d12a9fa0cc1b9f":()=>r.at});var r=s(54841),a=s(44089)},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71265:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(19161).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},73024:e=>{"use strict";e.exports=require("node:fs")},73496:e=>{"use strict";e.exports=require("http2")},73566:e=>{"use strict";e.exports=require("worker_threads")},74075:e=>{"use strict";e.exports=require("zlib")},74998:e=>{"use strict";e.exports=require("perf_hooks")},75448:(e,t,s)=>{Promise.resolve().then(s.bind(s,80668))},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},79748:e=>{"use strict";e.exports=require("fs/promises")},80481:e=>{"use strict";e.exports=require("node:readline")},80668:(e,t,s)=>{"use strict";s.d(t,{UsageAnalytics:()=>el});var r=s(99730),a=s(57752),i=s(74938),n=s(58940),o=s(87785),l=s(1493),d=s(56750),c="Progress",[u,x]=(0,l.A)(c),[p,m]=u(c),h=a.forwardRef((e,t)=>{var s,a;let{__scopeProgress:i,value:n=null,max:o,getValueLabel:l=g,...c}=e;(o||0===o)&&!j(o)&&console.error((s=`${o}`,`Invalid prop \`max\` of value \`${s}\` supplied to \`Progress\`. Only numbers greater than 0 are valid max values. Defaulting to \`100\`.`));let u=j(o)?o:100;null===n||w(n,u)||console.error((a=`${n}`,`Invalid prop \`value\` of value \`${a}\` supplied to \`Progress\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or 100 if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`));let x=w(n,u)?n:null,m=y(x)?l(x,u):void 0;return(0,r.jsx)(p,{scope:i,value:x,max:u,children:(0,r.jsx)(d.sG.div,{"aria-valuemax":u,"aria-valuemin":0,"aria-valuenow":y(x)?x:void 0,"aria-valuetext":m,role:"progressbar","data-state":v(x,u),"data-value":x??void 0,"data-max":u,...c,ref:t})})});h.displayName=c;var f="ProgressIndicator",b=a.forwardRef((e,t)=>{let{__scopeProgress:s,...a}=e,i=m(f,s);return(0,r.jsx)(d.sG.div,{"data-state":v(i.value,i.max),"data-value":i.value??void 0,"data-max":i.max,...a,ref:t})});function g(e,t){return`${Math.round(e/t*100)}%`}function v(e,t){return null==e?"indeterminate":e===t?"complete":"loading"}function y(e){return"number"==typeof e}function j(e){return y(e)&&!isNaN(e)&&e>0}function w(e,t){return y(e)&&!isNaN(e)&&e<=t&&e>=0}b.displayName=f;var N=s(83590);function k({className:e,value:t,...s}){return(0,r.jsx)(h,{"data-slot":"progress",className:(0,N.cn)("bg-primary/20 relative h-2 w-full overflow-hidden rounded-full",e),...s,children:(0,r.jsx)(b,{"data-slot":"progress-indicator",className:"bg-primary h-full w-full flex-1 transition-all",style:{transform:`translateX(-${100-(t||0)}%)`}})})}var q=s(46769),U=s(74794),C=s(86552),$=s(44804),A=s(58785),D=s(42703),M="Tabs",[R,_]=(0,l.A)(M,[U.RG]),P=(0,U.RG)(),[L,T]=R(M),F=a.forwardRef((e,t)=>{let{__scopeTabs:s,value:a,onValueChange:i,defaultValue:n,orientation:o="horizontal",dir:l,activationMode:c="automatic",...u}=e,x=(0,$.jH)(l),[p,m]=(0,A.i)({prop:a,onChange:i,defaultProp:n??"",caller:M});return(0,r.jsx)(L,{scope:s,baseId:(0,D.B)(),value:p,onValueChange:m,orientation:o,dir:x,activationMode:c,children:(0,r.jsx)(d.sG.div,{dir:x,"data-orientation":o,...u,ref:t})})});F.displayName=M;var E="TabsList",B=a.forwardRef((e,t)=>{let{__scopeTabs:s,loop:a=!0,...i}=e,n=T(E,s),o=P(s);return(0,r.jsx)(U.bL,{asChild:!0,...o,orientation:n.orientation,dir:n.dir,loop:a,children:(0,r.jsx)(d.sG.div,{role:"tablist","aria-orientation":n.orientation,...i,ref:t})})});B.displayName=E;var G="TabsTrigger",O=a.forwardRef((e,t)=>{let{__scopeTabs:s,value:a,disabled:i=!1,...n}=e,o=T(G,s),l=P(s),c=Z(o.baseId,a),u=I(o.baseId,a),x=a===o.value;return(0,r.jsx)(U.q7,{asChild:!0,...l,focusable:!i,active:x,children:(0,r.jsx)(d.sG.button,{type:"button",role:"tab","aria-selected":x,"aria-controls":u,"data-state":x?"active":"inactive","data-disabled":i?"":void 0,disabled:i,id:c,...n,ref:t,onMouseDown:(0,q.m)(e.onMouseDown,e=>{i||0!==e.button||!1!==e.ctrlKey?e.preventDefault():o.onValueChange(a)}),onKeyDown:(0,q.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&o.onValueChange(a)}),onFocus:(0,q.m)(e.onFocus,()=>{let e="manual"!==o.activationMode;x||i||!e||o.onValueChange(a)})})})});O.displayName=G;var z="TabsContent",S=a.forwardRef((e,t)=>{let{__scopeTabs:s,value:i,forceMount:n,children:o,...l}=e,c=T(z,s),u=Z(c.baseId,i),x=I(c.baseId,i),p=i===c.value,m=a.useRef(p);return a.useEffect(()=>{let e=requestAnimationFrame(()=>m.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,r.jsx)(C.C,{present:n||p,children:({present:s})=>(0,r.jsx)(d.sG.div,{"data-state":p?"active":"inactive","data-orientation":c.orientation,role:"tabpanel","aria-labelledby":u,hidden:!s,id:x,tabIndex:0,...l,ref:t,style:{...e.style,animationDuration:m.current?"0s":void 0},children:s&&o})})});function Z(e,t){return`${e}-trigger-${t}`}function I(e,t){return`${e}-content-${t}`}function V({className:e,...t}){return(0,r.jsx)(F,{"data-slot":"tabs",className:(0,N.cn)("flex flex-col gap-2",e),...t})}function W({className:e,...t}){return(0,r.jsx)(B,{"data-slot":"tabs-list",className:(0,N.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",e),...t})}function H({className:e,...t}){return(0,r.jsx)(O,{"data-slot":"tabs-trigger",className:(0,N.cn)("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...t})}function K({className:e,...t}){return(0,r.jsx)(S,{"data-slot":"tabs-content",className:(0,N.cn)("flex-1 outline-none",e),...t})}S.displayName=z;var X=s(19161);let J=(0,X.A)("crown",[["path",{d:"M11.562 3.266a.5.5 0 0 1 .876 0L15.39 8.87a1 1 0 0 0 1.516.294L21.183 5.5a.5.5 0 0 1 .798.519l-2.834 10.246a1 1 0 0 1-.956.734H5.81a1 1 0 0 1-.957-.734L2.02 6.02a.5.5 0 0 1 .798-.519l4.276 3.664a1 1 0 0 0 1.516-.294z",key:"1vdc57"}],["path",{d:"M5 21h14",key:"11awu3"}]]),Q=(0,X.A)("sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]]),Y=(0,X.A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]]),ee=(0,X.A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);var et=s(71265);let es=(0,X.A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]]);var er=s(83088);let ea=(0,X.A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]]);var ei=s(41265),en=s.n(ei);function eo({data:e}){let t=(0,a.useMemo)(()=>{let t=new Date,s=new Date(t);s.setDate(t.getDate()-29);let r=[];for(let t=0;t<30;t++){let a=new Date(s);a.setDate(s.getDate()+t);let i=e.find(e=>new Date(e.date).toDateString()===a.toDateString());r.push({date:a.toLocaleDateString("en-US",{month:"short",day:"numeric"}),cubentUnits:i?.cubentUnitsUsed||0,requests:i?.requestsMade||0})}return r},[e]),s=Math.max(...t.map(e=>e.cubentUnits),1),i=Math.max(...t.map(e=>e.requests),1),n=e=>{if(e.length<2)return"";let t=`M ${e[0].x} ${e[0].y}`;for(let s=1;s<e.length;s++){let r=e[s-1],a=e[s],i=e[s+1];if(1===s){let e=r.x+(a.x-r.x)*.3,s=r.y,i=a.x-(a.x-r.x)*.3,n=a.y;t+=` C ${e} ${s}, ${i} ${n}, ${a.x} ${a.y}`}else if(s===e.length-1){let e=r.x+(a.x-r.x)*.3,s=r.y,i=a.x-(a.x-r.x)*.3,n=a.y;t+=` C ${e} ${s}, ${i} ${n}, ${a.x} ${a.y}`}else{let n=r.x+(a.x-r.x)*.3,o=r.y+.1*(i.y-e[s-2]?.y||0),l=a.x-(a.x-r.x)*.3,d=a.y-.1*(e[s+1]?.y-r.y||0);t+=` C ${n} ${o}, ${l} ${d}, ${a.x} ${a.y}`}}return t};return(0,r.jsxs)("div",{className:"w-full",children:[(0,r.jsxs)("div",{className:"relative h-64 w-full",children:[(0,r.jsxs)("div",{className:"absolute left-0 top-0 h-full flex flex-col justify-between text-xs text-gray-400 dark:text-gray-500 py-4 pr-4",children:[(0,r.jsx)("span",{children:s.toFixed(0)}),(0,r.jsx)("span",{children:(.75*s).toFixed(0)}),(0,r.jsx)("span",{children:(.5*s).toFixed(0)}),(0,r.jsx)("span",{children:(.25*s).toFixed(0)}),(0,r.jsx)("span",{children:"0"})]}),(0,r.jsx)("div",{className:"absolute inset-0 left-12 flex flex-col justify-between py-4",children:[0,1,2,3,4].map(e=>(0,r.jsx)("div",{className:"w-full border-t border-gray-100 dark:border-gray-800"},e))}),(0,r.jsxs)("div",{className:"relative h-full ml-12 py-4",children:[(0,r.jsxs)("svg",{className:"w-full h-full",viewBox:`0 0 ${10*t.length} 200`,children:[(0,r.jsxs)("defs",{children:[(0,r.jsxs)("linearGradient",{id:"blueGradient",x1:"0%",y1:"0%",x2:"0%",y2:"100%",children:[(0,r.jsx)("stop",{offset:"0%",stopColor:"#3b82f6",stopOpacity:"0.3"}),(0,r.jsx)("stop",{offset:"100%",stopColor:"#3b82f6",stopOpacity:"0.05"})]}),(0,r.jsxs)("linearGradient",{id:"greenGradient",x1:"0%",y1:"0%",x2:"0%",y2:"100%",children:[(0,r.jsx)("stop",{offset:"0%",stopColor:"#10b981",stopOpacity:"0.3"}),(0,r.jsx)("stop",{offset:"100%",stopColor:"#10b981",stopOpacity:"0.05"})]})]}),(()=>{let e=t.map((e,t)=>({x:10*t+5,y:200-(s>0?e.cubentUnits/s*180:0)})),a=n(e),i=a+` L ${e[e.length-1].x} 200 L ${e[0].x} 200 Z`;return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("path",{d:i,fill:"url(#blueGradient)"}),(0,r.jsx)("path",{d:a,stroke:"#3b82f6",strokeWidth:"2",fill:"none",className:"drop-shadow-sm"}),e.map((e,t)=>(0,r.jsx)("circle",{cx:e.x,cy:e.y,r:"3",fill:"#3b82f6",className:"hover:r-4 transition-all cursor-pointer"},t))]})})(),(()=>{let e=t.map((e,t)=>({x:10*t+5,y:200-(i>0?e.requests/i*60:0)})),s=n(e);return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("path",{d:s,stroke:"#10b981",strokeWidth:"2",fill:"none",strokeDasharray:"5,5",className:"opacity-70"}),e.map((e,t)=>(0,r.jsx)("circle",{cx:e.x,cy:e.y,r:"2",fill:"#10b981",className:"hover:r-3 transition-all cursor-pointer"},t))]})})()]}),(0,r.jsx)("div",{className:"absolute inset-0 flex",children:t.map((e,t)=>(0,r.jsxs)("div",{className:"flex-1 relative group cursor-pointer",children:[(0,r.jsx)("div",{className:"absolute inset-0 hover:bg-blue-50 dark:hover:bg-blue-950/20 transition-colors rounded"}),(0,r.jsxs)("div",{className:"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 dark:bg-gray-800 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-20 shadow-lg border border-gray-700",children:[(0,r.jsx)("div",{className:"font-medium text-center",children:e.date}),(0,r.jsxs)("div",{className:"text-blue-300",children:[e.cubentUnits.toFixed(2)," units"]}),(0,r.jsxs)("div",{className:"text-green-300",children:[e.requests," messages"]}),(0,r.jsx)("div",{className:"absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-2 border-r-2 border-t-2 border-transparent border-t-gray-900 dark:border-t-gray-800"})]})]},t))})]}),(0,r.jsx)("div",{className:"absolute bottom-0 left-12 right-0 flex justify-between text-xs text-gray-400 dark:text-gray-500 mt-2",children:t.map((e,t)=>t%6==0&&(0,r.jsx)("div",{className:"transform -rotate-45 origin-left",children:e.date},t))})]}),(0,r.jsx)("div",{className:"mt-6 pt-4 border-t border-gray-100 dark:border-gray-800",children:(0,r.jsxs)("div",{className:"grid grid-cols-3 gap-4 text-center",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100",children:t.reduce((e,t)=>e+t.cubentUnits,0).toFixed(1)}),(0,r.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Total Units"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100",children:t.reduce((e,t)=>e+t.requests,0)}),(0,r.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Total Messages"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100",children:t.reduce((e,t)=>e+t.cubentUnits,0)>0&&t.reduce((e,t)=>e+t.requests,0)>0?(t.reduce((e,t)=>e+t.cubentUnits,0)/t.reduce((e,t)=>e+t.requests,0)).toFixed(2):"0.00"}),(0,r.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Avg Units/Message"})]})]})})]})}function el({initialData:e}){let[t,s]=(0,a.useState)(e),[l,d]=(0,a.useState)(!1),[c,u]=(0,a.useState)(new Date),x=async()=>{d(!0);try{let e=await fetch("/api/extension/usage/stats",{method:"GET",headers:{"Content-Type":"application/json"}});if(e.ok){let t=await e.json();t.success&&(s(e=>({...e,totalCubentUnits:t.totalCubentUnits,totalMessages:t.totalMessages,userLimit:t.userLimit,subscriptionTier:t.subscriptionTier})),u(new Date))}}catch(e){console.error("Failed to refresh usage data:",e)}finally{d(!1)}},p=t.totalCubentUnits/t.userLimit*100,m=p>80,h=p>100,f=(e=>{switch(e){case"pro":return{name:"Pro",icon:J,color:"text-yellow-600"};case"premium":return{name:"Premium",icon:Q,color:"text-purple-600"};default:return{name:"Free Trial",icon:Y,color:"text-blue-600"}}})(t.subscriptionTier),b=f.icon;return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12",children:[(0,r.jsx)("div",{className:"flex items-center gap-2 px-4",children:(0,r.jsx)(i.$,{variant:"ghost",size:"sm",asChild:!0,children:(0,r.jsxs)(en(),{href:"/profile",children:[(0,r.jsx)(ee,{className:"h-4 w-4 mr-2"}),"Back to Profile"]})})}),(0,r.jsxs)("div",{className:"ml-auto flex items-center gap-2 px-4",children:[(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Last updated"}),(0,r.jsx)("p",{className:"text-sm font-medium",children:c.toLocaleTimeString()})]}),(0,r.jsxs)(i.$,{variant:"outline",size:"sm",onClick:x,disabled:l,children:[(0,r.jsx)(et.A,{className:`h-4 w-4 mr-2 ${l?"animate-spin":""}`}),"Refresh"]}),(0,r.jsxs)(i.$,{size:"sm",children:[(0,r.jsx)(es,{className:"h-4 w-4 mr-2"}),"Download"]})]})]}),(0,r.jsxs)("main",{className:"flex flex-1 flex-col gap-4 p-4 pt-0",children:[(0,r.jsx)("div",{className:"flex items-center justify-between space-y-2",children:(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-2xl font-bold tracking-tight",children:"Usage Analytics"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Here's what happening with your usage today"})]})}),(0,r.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,r.jsxs)(n.Zp,{children:[(0,r.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(n.ZB,{className:"text-sm font-medium",children:"Total Cubent Units"}),(0,r.jsx)(Y,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsxs)(n.Wu,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:t.totalCubentUnits.toFixed(2)}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 text-xs text-muted-foreground",children:[(0,r.jsxs)("span",{className:`font-medium ${h?"text-red-500":m?"text-yellow-500":"text-green-500"}`,children:[p.toFixed(0),"%"]}),(0,r.jsxs)("span",{children:["of ",t.userLimit," limit"]})]}),(0,r.jsx)(k,{value:Math.min(p,100),className:"mt-2 h-1"})]})]}),(0,r.jsxs)(n.Zp,{children:[(0,r.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(n.ZB,{className:"text-sm font-medium",children:"Total Messages"}),(0,r.jsx)(er.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsxs)(n.Wu,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:t.totalMessages.toLocaleString()}),(0,r.jsxs)("p",{className:"text-xs text-muted-foreground",children:["+",Math.round(t.totalMessages/30*7)," this week"]})]})]}),(0,r.jsxs)(n.Zp,{children:[(0,r.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(n.ZB,{className:"text-sm font-medium",children:"Efficiency"}),(0,r.jsx)(ea,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsxs)(n.Wu,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:t.totalMessages>0?(t.totalCubentUnits/t.totalMessages).toFixed(2):"0.00"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"units per message"})]})]}),(0,r.jsxs)(n.Zp,{children:[(0,r.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(n.ZB,{className:"text-sm font-medium",children:"Subscription"}),(0,r.jsx)(b,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsxs)(n.Wu,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:t.userLimit}),(0,r.jsx)("div",{className:"flex items-center space-x-2 text-xs text-muted-foreground",children:(0,r.jsxs)(o.E,{variant:"secondary",className:"text-xs",children:[(0,r.jsx)(b,{className:`h-3 w-3 mr-1 ${f.color}`}),f.name]})})]})]})]}),(0,r.jsxs)(V,{defaultValue:"overview",className:"space-y-4",children:[(0,r.jsxs)(W,{children:[(0,r.jsx)(H,{value:"overview",children:"Overview"}),(0,r.jsx)(H,{value:"analytics",children:"Analytics"}),(0,r.jsx)(H,{value:"reports",children:"Reports"})]}),(0,r.jsx)(K,{value:"overview",className:"space-y-4",children:(0,r.jsxs)(n.Zp,{children:[(0,r.jsxs)(n.aR,{children:[(0,r.jsx)(n.ZB,{children:"Usage Overview"}),(0,r.jsx)(n.BT,{children:"Daily consumption for the last 30 days"})]}),(0,r.jsx)(n.Wu,{className:"pl-2",children:(0,r.jsx)(eo,{data:t.chartData})})]})}),(0,r.jsx)(K,{value:"analytics",className:"space-y-4",children:(0,r.jsxs)(n.Zp,{children:[(0,r.jsxs)(n.aR,{children:[(0,r.jsx)(n.ZB,{children:"Detailed Analytics"}),(0,r.jsx)(n.BT,{children:"Advanced usage metrics and trends"})]}),(0,r.jsx)(n.Wu,{children:(0,r.jsx)("div",{className:"text-center py-8 text-muted-foreground",children:"Advanced analytics coming soon..."})})]})}),(0,r.jsx)(K,{value:"reports",className:"space-y-4",children:(0,r.jsxs)(n.Zp,{children:[(0,r.jsxs)(n.aR,{children:[(0,r.jsx)(n.ZB,{children:"Usage Reports"}),(0,r.jsx)(n.BT,{children:"Export and download usage reports"})]}),(0,r.jsx)(n.Wu,{children:(0,r.jsx)("div",{className:"text-center py-8 text-muted-foreground",children:"Report generation coming soon..."})})]})})]}),(m||h)&&"free_trial"===t.subscriptionTier&&(0,r.jsx)(n.Zp,{className:"border-yellow-200 bg-yellow-50 dark:border-yellow-800/50 dark:bg-yellow-900/10",children:(0,r.jsxs)(n.Wu,{className:"flex items-center justify-between p-6",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsx)("div",{className:"flex h-12 w-12 items-center justify-center rounded-xl bg-yellow-100 dark:bg-yellow-900/30",children:(0,r.jsx)(J,{className:"h-6 w-6 text-yellow-600 dark:text-yellow-400"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold",children:h?"Usage Limit Exceeded":"Approaching Usage Limit"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Upgrade to Pro for unlimited Cubent Units and advanced features."})]})]}),(0,r.jsxs)(i.$,{children:[(0,r.jsx)(Q,{className:"h-4 w-4 mr-2"}),"Upgrade Now"]})]})})]})]})}},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},86592:e=>{"use strict";e.exports=require("node:inspector")},87785:(e,t,s)=>{"use strict";s.d(t,{E:()=>l});var r=s(99730);s(57752);var a=s(58576),i=s(72795),n=s(83590);let o=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,asChild:s=!1,...i}){let l=s?a.DX:"span";return(0,r.jsx)(l,{"data-slot":"badge",className:(0,n.cn)(o({variant:t}),e),...i})}},91645:e=>{"use strict";e.exports=require("net")},94175:e=>{"use strict";e.exports=require("stream/web")},94735:e=>{"use strict";e.exports=require("events")},95617:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.default,__next_app__:()=>c,pages:()=>d,routeModule:()=>u,tree:()=>l});var r=s(57864),a=s(94327),i=s(70814),n=s(17984),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);s.d(t,o);let l={children:["",{children:["(authenticated)",{children:["profile",{children:["usage",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,57416)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\(authenticated)\\profile\\usage\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,16703)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\(authenticated)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,47837,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,47168,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,52945,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,56940))).default(e)],apple:[async e=>(await Promise.resolve().then(s.bind(s,54526))).default(e)],openGraph:[async e=>(await Promise.resolve().then(s.bind(s,36334))).default(e)],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,29622)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(s.bind(s,70814)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,47837,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,47168,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,52945,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,56940))).default(e)],apple:[async e=>(await Promise.resolve().then(s.bind(s,54526))).default(e)],openGraph:[async e=>(await Promise.resolve().then(s.bind(s,36334))).default(e)],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\app\\app\\(authenticated)\\profile\\usage\\page.tsx"],c={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(authenticated)/profile/usage/page",pathname:"/profile/usage",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[5319,6239,2923,25,903,7838,5480,3319,2644,277,1988,5432,4841,6904,1121,864,7209,6648],()=>s(95617));module.exports=r})();