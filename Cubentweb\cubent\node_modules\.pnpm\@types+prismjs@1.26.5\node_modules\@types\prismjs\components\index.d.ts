interface LoadLanguages {
    /**
     * Set this to `true` to prevent all warning messages `loadLanguages` logs.
     *
     * @default false
     */
    silent: boolean;

    /**
     * Loads the given languages and adds them to the current Prism instance.
     *
     * If no languages are provided, _all_ Prism languages will be loaded.
     */
    (languages?: string | string[]): void;
}

declare const loadLanguages: LoadLanguages;

export = loadLanguages;
