"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[524],{1524:(e,t,r)=>{r.d(t,{RG:()=>R,bL:()=>S,q7:()=>k});var n=r(50628),o=r(13859),l=r(3057),a=r(98064),u=r(48733),i=r(29823),c=r(64826),s=r(72336),f=r(17691),d=r(85532),p=r(6024),v="rovingFocusGroup.onEntryFocus",m={bubbles:!1,cancelable:!0},w="RovingFocusGroup",[y,h,b]=(0,l.N)(w),[g,R]=(0,u.A)(w,[b]),[x,A]=g(w),C=n.forwardRef((e,t)=>(0,p.jsx)(y.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(y.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(E,{...e,ref:t})})}));C.displayName=w;var E=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:l,loop:u=!1,dir:i,currentTabStopId:y,defaultCurrentTabStopId:b,onCurrentTabStopIdChange:g,onEntryFocus:R,preventScrollOnEntryFocus:A=!1,...C}=e,E=n.useRef(null),F=(0,a.s)(t,E),I=(0,d.jH)(i),[T,S]=(0,f.i)({prop:y,defaultProp:null!=b?b:null,onChange:g,caller:w}),[k,j]=n.useState(!1),D=(0,s.c)(R),L=h(r),N=n.useRef(!1),[G,_]=n.useState(0);return n.useEffect(()=>{let e=E.current;if(e)return e.addEventListener(v,D),()=>e.removeEventListener(v,D)},[D]),(0,p.jsx)(x,{scope:r,orientation:l,dir:I,loop:u,currentTabStopId:T,onItemFocus:n.useCallback(e=>S(e),[S]),onItemShiftTab:n.useCallback(()=>j(!0),[]),onFocusableItemAdd:n.useCallback(()=>_(e=>e+1),[]),onFocusableItemRemove:n.useCallback(()=>_(e=>e-1),[]),children:(0,p.jsx)(c.sG.div,{tabIndex:k||0===G?-1:0,"data-orientation":l,...C,ref:F,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{N.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let t=!N.current;if(e.target===e.currentTarget&&t&&!k){let t=new CustomEvent(v,m);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=L().filter(e=>e.focusable);M([e.find(e=>e.active),e.find(e=>e.id===T),...e].filter(Boolean).map(e=>e.ref.current),A)}}N.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>j(!1))})})}),F="RovingFocusGroupItem",I=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:l=!0,active:a=!1,tabStopId:u,children:s,...f}=e,d=(0,i.B)(),v=u||d,m=A(F,r),w=m.currentTabStopId===v,b=h(r),{onFocusableItemAdd:g,onFocusableItemRemove:R,currentTabStopId:x}=m;return n.useEffect(()=>{if(l)return g(),()=>R()},[l,g,R]),(0,p.jsx)(y.ItemSlot,{scope:r,id:v,focusable:l,active:a,children:(0,p.jsx)(c.sG.span,{tabIndex:w?0:-1,"data-orientation":m.orientation,...f,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{l?m.onItemFocus(v):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>m.onItemFocus(v)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void m.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,r){var n;let o=(n=e.key,"rtl"!==r?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return T[o]}(e,m.orientation,m.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=b().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let n=r.indexOf(e.currentTarget);r=m.loop?function(e,t){return e.map((r,n)=>e[(t+n)%e.length])}(r,n+1):r.slice(n+1)}setTimeout(()=>M(r))}}),children:"function"==typeof s?s({isCurrentTabStop:w,hasTabStop:null!=x}):s})})});I.displayName=F;var T={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function M(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=document.activeElement;for(let n of e)if(n===r||(n.focus({preventScroll:t}),document.activeElement!==r))return}var S=C,k=I},3057:(e,t,r)=>{function n(e,t,r){if(!t.has(e))throw TypeError("attempted to "+r+" private field on non-instance");return t.get(e)}function o(e,t){var r=n(e,t,"get");return r.get?r.get.call(e):r.value}function l(e,t,r){var o=n(e,t,"set");if(o.set)o.set.call(e,r);else{if(!o.writable)throw TypeError("attempted to set read only private field");o.value=r}return r}r.d(t,{N:()=>d});var a,u=r(50628),i=r(48733),c=r(98064),s=r(89840),f=r(6024);function d(e){let t=e+"CollectionProvider",[r,n]=(0,i.A)(t),[o,l]=r(t,{collectionRef:{current:null},itemMap:new Map}),a=e=>{let{scope:t,children:r}=e,n=u.useRef(null),l=u.useRef(new Map).current;return(0,f.jsx)(o,{scope:t,itemMap:l,collectionRef:n,children:r})};a.displayName=t;let d=e+"CollectionSlot",p=(0,s.TL)(d),v=u.forwardRef((e,t)=>{let{scope:r,children:n}=e,o=l(d,r),a=(0,c.s)(t,o.collectionRef);return(0,f.jsx)(p,{ref:a,children:n})});v.displayName=d;let m=e+"CollectionItemSlot",w="data-radix-collection-item",y=(0,s.TL)(m),h=u.forwardRef((e,t)=>{let{scope:r,children:n,...o}=e,a=u.useRef(null),i=(0,c.s)(t,a),s=l(m,r);return u.useEffect(()=>(s.itemMap.set(a,{ref:a,...o}),()=>void s.itemMap.delete(a))),(0,f.jsx)(y,{...{[w]:""},ref:i,children:n})});return h.displayName=m,[{Provider:a,Slot:v,ItemSlot:h},function(t){let r=l(e+"CollectionConsumer",t);return u.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(w,"]")));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},n]}var p=new WeakMap;function v(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let r=function(e,t){let r=e.length,n=m(t),o=n>=0?n:r+n;return o<0||o>=r?-1:o}(e,t);return -1===r?void 0:e[r]}function m(e){return e!=e||0===e?0:Math.trunc(e)}a=new WeakMap},85532:(e,t,r)=>{r.d(t,{jH:()=>l});var n=r(50628);r(6024);var o=n.createContext(void 0);function l(e){let t=n.useContext(o);return e||t||"ltr"}}}]);