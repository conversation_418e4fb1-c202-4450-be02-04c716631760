(()=>{var e={};e.id=7454,e.ids=[7454],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6043:(e,t,r)=>{"use strict";r.r(t),r.d(t,{BaseHubImage:()=>a,basehubImageLoader:()=>i});var s=r(6340);let a=(0,s.registerClientReference)(function(){throw Error("Attempted to call BaseHubImage() from the server but BaseHubImage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\node_modules\\.pnpm\\basehub@8.2.7_@babel+runtim_3aebfa8e064bb601162c04844d38dd02\\node_modules\\basehub\\dist\\next-image.js","BaseHubImage"),i=(0,s.registerClientReference)(function(){throw Error("Attempted to call basehubImageLoader() from the server but basehubImageLoader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\node_modules\\.pnpm\\basehub@8.2.7_@babel+runtim_3aebfa8e064bb601162c04844d38dd02\\node_modules\\basehub\\dist\\next-image.js","basehubImageLoader")},6447:(e,t,r)=>{"use strict";r.r(t),r.d(t,{"00e578e70edcb55dedc8be97f84cdf9c4ce9b9fbb2":()=>s.z2,"40017c5c785fe62ac79085684e0699f270029f4484":()=>i.$$RSC_SERVER_ACTION_0,"405efdbdcc87c20941759ae1bb1e4f3629f6226400":()=>a.x,"6040ce2583939275b9b79a23c761210186958f33ca":()=>s.qr,"60ac4068834bf9e1b32327478a0425a5daded1d553":()=>s.xK,"60d7e646f4b8f4f2b31c38fcbc9963402a4f1620ad":()=>s.q2});var s=r(22589),a=r(18362),i=r(71871)},8086:e=>{"use strict";e.exports=require("module")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},25498:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,21034,23)),Promise.resolve().then(r.bind(r,6043)),Promise.resolve().then(r.t.bind(r,49499,23)),Promise.resolve().then(r.bind(r,93665)),Promise.resolve().then(r.bind(r,40356))},26026:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>d,pages:()=>u,routeModule:()=>p,tree:()=>c});var s=r(57864),a=r(94327),i=r(73391),o=r.n(i),n=r(17984),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);r.d(t,l);let c={children:["",{children:["[locale]",{children:["blog",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,71871)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\web\\app\\[locale]\\blog\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,15190))).default(e)],apple:[async e=>(await Promise.resolve().then(r.bind(r,7820))).default(e)],openGraph:[async e=>(await Promise.resolve().then(r.bind(r,39440))).default(e)],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,37919)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\web\\app\\[locale]\\layout.tsx"],"global-error":[()=>Promise.resolve().then(r.bind(r,84641)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\web\\app\\[locale]\\global-error.tsx"]}]},{"not-found":[()=>Promise.resolve().then(r.t.bind(r,47837,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,47168,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,52945,23)),"next/dist/client/components/unauthorized-error"]}]}.children,u=["C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\web\\app\\[locale]\\blog\\page.tsx"],d={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/[locale]/blog/page",pathname:"/[locale]/blog",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{"use strict";e.exports=require("node:child_process")},33873:e=>{"use strict";e.exports=require("path")},36028:(e,t)=>{},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},37067:e=>{"use strict";e.exports=require("node:http")},38522:e=>{"use strict";e.exports=require("node:zlib")},41692:e=>{"use strict";e.exports=require("node:tls")},42152:e=>{"use strict";e.exports=require("process")},44708:e=>{"use strict";e.exports=require("node:https")},48161:e=>{"use strict";e.exports=require("node:os")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},56450:(e,t,r)=>{"use strict";r.d(t,{w:()=>l});var s=r(81121),a=r.n(s);let i="next-forge",o={name:"Vercel",url:"https://vercel.com/"},n=process.env.VERCEL_PROJECT_PRODUCTION_URL,l=({title:e,description:t,image:r,...s})=>{let l=`${e} | ${i}`,c={title:l,description:t,applicationName:i,metadataBase:n?new URL(`https://${n}`):void 0,authors:[o],creator:o.name,formatDetection:{telephone:!1},appleWebApp:{capable:!0,statusBarStyle:"default",title:l},openGraph:{title:l,description:t,type:"website",siteName:i,locale:"en_US"},publisher:"Vercel",twitter:{card:"summary_large_image",creator:"@vercel"}},u=a()(c,s);return r&&u.openGraph&&(u.openGraph.images=[{url:r,width:1200,height:630,alt:e}]),u}},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64018:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});var s=r(94752);r(36028);let a=({code:e})=>(0,s.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify(e)}})},65666:(e,t,r)=>{Promise.resolve().then(r.bind(r,86332)),Promise.resolve().then(r.bind(r,96860)),Promise.resolve().then(r.t.bind(r,41265,23)),Promise.resolve().then(r.bind(r,22683)),Promise.resolve().then(r.bind(r,38327))},71871:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$RSC_SERVER_ACTION_0:()=>b,default:()=>x,generateMetadata:()=>m});var s=r(94752),a=r(52661);r(37091);var i=r(23055),o=r(17218),n=r(6043),l=r(48384),c=r(77422),u=r(64018),d=r(56450),p=r(49499),h=r.n(p);let m=async({params:e})=>{let{locale:t}=await e,r=await (0,c.T)(t);return(0,d.w)(r.web.blog.meta)},b=async function([e]){return e.blog.posts.items.length?e.blog.posts.items.map((e,t)=>(0,s.jsxs)(h(),{href:`/blog/${e._slug}`,className:(0,l.cn)("flex cursor-pointer flex-col gap-4 hover:opacity-75",!t&&"md:col-span-2"),children:[(0,s.jsx)(n.BaseHubImage,{src:e.image.url,alt:e.image.alt??"",width:e.image.width,height:e.image.height}),(0,s.jsx)("div",{className:"flex flex-row items-center gap-4",children:(0,s.jsx)("p",{className:"text-muted-foreground text-sm",children:new Date(e.date).toLocaleDateString("en-US",{month:"long",day:"numeric",year:"numeric"})})}),(0,s.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,s.jsx)("h3",{className:"max-w-3xl text-4xl tracking-tight",children:e._title}),(0,s.jsx)("p",{className:"max-w-3xl text-base text-muted-foreground",children:e.description})]})]},e._slug)):null},x=async({params:e})=>{let{locale:t}=await e,r=await (0,c.T)(t);return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(u.Z,{code:{"@type":"Blog","@context":"https://schema.org"}}),(0,s.jsx)("div",{className:"w-full pt-40 pb-20 lg:pt-48 lg:pb-40",children:(0,s.jsxs)("div",{className:"container mx-auto flex flex-col gap-14",children:[(0,s.jsx)("div",{className:"flex w-full flex-col gap-8 sm:flex-row sm:items-center sm:justify-between",children:(0,s.jsx)("h4",{className:"max-w-xl font-regular text-3xl tracking-tighter md:text-5xl",children:r.web.blog.meta.title})}),(0,s.jsx)("div",{className:"grid grid-cols-1 gap-8 md:grid-cols-2",children:(0,s.jsx)(o.J,{queries:[i.h.postsQuery],children:(0,a.A)(b,"40017c5c785fe62ac79085684e0699f270029f4484",null)})})]})})]})}},73024:e=>{"use strict";e.exports=require("node:fs")},73566:e=>{"use strict";e.exports=require("worker_threads")},74075:e=>{"use strict";e.exports=require("zlib")},74998:e=>{"use strict";e.exports=require("perf_hooks")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},80481:e=>{"use strict";e.exports=require("node:readline")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},86592:e=>{"use strict";e.exports=require("node:inspector")},94735:e=>{"use strict";e.exports=require("events")},96860:(e,t,r)=>{"use strict";r.r(t),r.d(t,{BaseHubImage:()=>g,basehubImageLoader:()=>f});var s=Object.defineProperty,a=Object.defineProperties,i=Object.getOwnPropertyDescriptors,o=Object.getOwnPropertySymbols,n=Object.prototype.hasOwnProperty,l=Object.prototype.propertyIsEnumerable,c=(e,t,r)=>t in e?s(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,u=(e,t)=>{for(var r in t||(t={}))n.call(t,r)&&c(e,r,t[r]);if(o)for(var r of o(t))l.call(t,r)&&c(e,r,t[r]);return e},d=(e,t)=>a(e,i(t)),p=r(48473),h=r(35371),m=r(99730),b="https://basehub.earth",x="https://assets.basehub.com",f=({src:e,width:t,quality:r})=>{let s;try{s=new URL(e)}catch(t){throw Error(`Invalid BaseHub Image URL: ${e}

Expected origin to be one of:
- ${b} (deprecated)
- ${x}
`)}let a=[`width=${t}`,`quality=${r||90}`];if(s.href.includes(b))if(s.pathname.startsWith("/cdn-cgi/image/")){let[e,t,r,i="",...o]=s.pathname.split("/"),n=[...i.split(",").filter(e=>!e.startsWith("width=")&&!e.startsWith("quality=")&&!e.startsWith("w=")&&!e.startsWith("q=")&&!e.startsWith("h=")&&!e.startsWith("height=")),...a].join(",");!1===n.includes("format=")&&(n+=",format=auto"),s.pathname=`/cdn-cgi/image/${n}/${o.join("/")}`}else a.push("format=auto"),s.pathname=`/cdn-cgi/image/${a.join(",")}${s.pathname}`;else s.href.includes(x)&&(a.forEach(e=>{let[t,r]=e.split("=");t&&r&&s.searchParams.set(t,r)}),!1===s.searchParams.has("format")&&s.searchParams.set("format","auto"),s.searchParams.delete("height"),s.searchParams.delete("h"));let i=new URL(x);if(s.href.includes(b))if(s.pathname.startsWith("/cdn-cgi/image/")){let[e,t,r,a="",...o]=s.pathname.split("/");i.pathname=o.join("/"),i.search=a.split(",").join("&")}else i.pathname=s.pathname,i.search=s.search;else{if(!s.href.includes(x))return e;i.pathname=s.pathname,i.search=s.search}return i.toString()},g=(0,h.forwardRef)((e,t)=>{var r,s,a;let i=null!=(a=null!=(s=e.unoptimized)?s:null==(r=e.src.toString().split("?")[0])?void 0:r.endsWith(".svg"))?a:void 0;return(0,m.jsx)(p.default,d(u({},e),{placeholder:e.placeholder,loader:f,unoptimized:i,ref:t}))})}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[5319,3396,415,2644,6784,1121,9752,4788],()=>r(26026));module.exports=s})();