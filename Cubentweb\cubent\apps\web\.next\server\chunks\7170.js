"use strict";exports.id=7170,exports.ids=[7170],exports.modules={1702:e=>{let t=e=>"object"==typeof e&&null!==e,r=Symbol("skip"),i=e=>t(e)&&!(e instanceof RegExp)&&!(e instanceof Error)&&!(e instanceof Date),s=(e,t,a,n=new WeakMap)=>{if(a={deep:!1,target:{},...a},n.has(e))return n.get(e);n.set(e,a.target);let{target:o}=a;delete a.target;let l=e=>e.map(e=>i(e)?s(e,t,a,n):e);if(Array.isArray(e))return l(e);for(let[c,u]of Object.entries(e)){let d=t(c,u,e);if(d===r)continue;let[h,p,{shouldRecurse:f=!0}={}]=d;"__proto__"!==h&&(a.deep&&f&&i(p)&&(p=Array.isArray(p)?l(p):s(p,t,a,n)),o[h]=p)}return o};e.exports=(e,r,i)=>{if(!t(e))throw TypeError(`Expected an object, got \`${e}\` (${typeof e})`);return s(e,r,i)},e.exports.mapObjectSkip=r},3560:(e,t,r)=>{r.d(t,{A:()=>i});let i=(e,t)=>{if(void 0!==t&&(!Array.isArray(t)||t.some(e=>"string"!=typeof e)))throw TypeError(`"${e}" option must be an array of strings`);if(t)return new Set(t)}},6798:(e,t,r)=>{r.d(t,{M3:()=>n,W2:()=>a,ll:()=>s,t9:()=>o});var i=r(67407);function s(e){return(0,i.A)(e)&&"string"==typeof e.kty}function a(e){return"oct"!==e.kty&&"string"==typeof e.d}function n(e){return"oct"!==e.kty&&void 0===e.d}function o(e){return s(e)&&"oct"===e.kty&&"string"==typeof e.k}},8392:(e,t,r)=>{r.d(t,{Jt:()=>X});var i=r(13690),s=r(77598),a=r(57975),n=r(14295);function o(e){switch(e){case"PS256":case"RS256":case"ES256":case"ES256K":return"sha256";case"PS384":case"RS384":case"ES384":return"sha384";case"PS512":case"RS512":case"ES512":return"sha512";case"Ed25519":case"EdDSA":return;default:throw new n.T0(`alg ${e} is not supported either by JOSE or your javascript runtime`)}}var l=r(46524),c=r(13317);let u=new Map([["ES256","P-256"],["ES256K","secp256k1"],["ES384","P-384"],["ES512","P-521"]]);function d(e,t){let r,i,a,o;if(t instanceof s.KeyObject)r=t.asymmetricKeyType,i=t.asymmetricKeyDetails;else switch(a=!0,t.kty){case"RSA":r="rsa";break;case"EC":r="ec";break;case"OKP":if("Ed25519"===t.crv){r="ed25519";break}if("Ed448"===t.crv){r="ed448";break}throw TypeError("Invalid key for this operation, its crv must be Ed25519 or Ed448");default:throw TypeError("Invalid key for this operation, its kty must be RSA, OKP, or EC")}switch(e){case"Ed25519":if("ed25519"!==r)throw TypeError("Invalid key for this operation, its asymmetricKeyType must be ed25519");break;case"EdDSA":if(!["ed25519","ed448"].includes(r))throw TypeError("Invalid key for this operation, its asymmetricKeyType must be ed25519 or ed448");break;case"RS256":case"RS384":case"RS512":if("rsa"!==r)throw TypeError("Invalid key for this operation, its asymmetricKeyType must be rsa");(0,c.A)(t,e);break;case"PS256":case"PS384":case"PS512":if("rsa-pss"===r){let{hashAlgorithm:t,mgf1HashAlgorithm:r,saltLength:s}=i,a=parseInt(e.slice(-3),10);if(void 0!==t&&(t!==`sha${a}`||r!==t))throw TypeError(`Invalid key for this operation, its RSA-PSS parameters do not meet the requirements of "alg" ${e}`);if(void 0!==s&&s>a>>3)throw TypeError(`Invalid key for this operation, its RSA-PSS parameter saltLength does not meet the requirements of "alg" ${e}`)}else if("rsa"!==r)throw TypeError("Invalid key for this operation, its asymmetricKeyType must be rsa or rsa-pss");(0,c.A)(t,e),o={padding:s.constants.RSA_PKCS1_PSS_PADDING,saltLength:s.constants.RSA_PSS_SALTLEN_DIGEST};break;case"ES256":case"ES256K":case"ES384":case"ES512":{if("ec"!==r)throw TypeError("Invalid key for this operation, its asymmetricKeyType must be ec");let i=(0,l.A)(t),s=u.get(e);if(i!==s)throw TypeError(`Invalid key curve for the algorithm, its curve must be ${s}, got ${i}`);o={dsaEncoding:"ieee-p1363"};break}default:throw new n.T0(`alg ${e} is not supported either by JOSE or your javascript runtime`)}return a?{format:"jwk",key:t,...o}:o?{...o,key:t}:t}var h=r(89605),p=r(25840),f=r(63669),g=r(69472),y=r(6798);function m(e,t,r){if(t instanceof Uint8Array){if(!e.startsWith("HS"))throw TypeError((0,f.A)(t,...g.g));return(0,s.createSecretKey)(t)}if(t instanceof s.KeyObject)return t;if((0,h.R)(t))return(0,p.Y)(t,e,r),s.KeyObject.from(t);if(y.ll(t))return e.startsWith("HS")?(0,s.createSecretKey)(Buffer.from(t.k,"base64url")):t;throw TypeError((0,f.A)(t,...g.g,"Uint8Array","JSON Web Key"))}let w=(0,a.promisify)(s.sign),b=async(e,t,r)=>{let i=m(e,t,"sign");if(e.startsWith("HS")){let t=s.createHmac(function(e){switch(e){case"HS256":return"sha256";case"HS384":return"sha384";case"HS512":return"sha512";default:throw new n.T0(`alg ${e} is not supported either by JOSE or your javascript runtime`)}}(e),i);return t.update(r),t.digest()}return w(o(e),r,d(e,i))},_=(0,a.promisify)(s.verify),v=async(e,t,r,i)=>{let a=m(e,t,"verify");if(e.startsWith("HS")){let t=await b(e,a,i);try{return s.timingSafeEqual(r,t)}catch{return!1}}let n=o(e),l=d(e,a);try{return await _(n,i,l,r)}catch{return!1}};var S=r(45271),k=r(61824),A=r(67407),E=r(70556),T=r(49010),P=r(3560),x=r(76285);async function I(e,t,r){let s,a;if(!(0,A.A)(e))throw new n.Ye("Flattened JWS must be an object");if(void 0===e.protected&&void 0===e.header)throw new n.Ye('Flattened JWS must have either of the "protected" or "header" members');if(void 0!==e.protected&&"string"!=typeof e.protected)throw new n.Ye("JWS Protected Header incorrect type");if(void 0===e.payload)throw new n.Ye("JWS Payload missing");if("string"!=typeof e.signature)throw new n.Ye("JWS Signature missing or incorrect type");if(void 0!==e.header&&!(0,A.A)(e.header))throw new n.Ye("JWS Unprotected Header incorrect type");let o={};if(e.protected)try{let t=(0,i.D4)(e.protected);o=JSON.parse(S.D0.decode(t))}catch{throw new n.Ye("JWS Protected Header is invalid")}if(!(0,k.A)(o,e.header))throw new n.Ye("JWS Protected and JWS Unprotected Header Parameter names must be disjoint");let l={...o,...e.header},c=(0,T.A)(n.Ye,new Map([["b64",!0]]),r?.crit,o,l),u=!0;if(c.has("b64")&&"boolean"!=typeof(u=o.b64))throw new n.Ye('The "b64" (base64url-encode payload) Header Parameter must be a boolean');let{alg:d}=l;if("string"!=typeof d||!d)throw new n.Ye('JWS "alg" (Algorithm) Header Parameter missing or invalid');let h=r&&(0,P.A)("algorithms",r.algorithms);if(h&&!h.has(d))throw new n.Rb('"alg" (Algorithm) Header Parameter value not allowed');if(u){if("string"!=typeof e.payload)throw new n.Ye("JWS Payload must be a string")}else if("string"!=typeof e.payload&&!(e.payload instanceof Uint8Array))throw new n.Ye("JWS Payload must be a string or an Uint8Array instance");let p=!1;"function"==typeof t?(t=await t(o,e),p=!0,(0,E.I)(d,t,"verify"),(0,y.ll)(t)&&(t=await (0,x.Og)(t,d))):(0,E.I)(d,t,"verify");let f=(0,S.xW)(S.Rd.encode(e.protected??""),S.Rd.encode("."),"string"==typeof e.payload?S.Rd.encode(e.payload):e.payload);try{s=(0,i.D4)(e.signature)}catch{throw new n.Ye("Failed to base64url decode the signature")}if(!await v(d,t,s,f))throw new n.h2;if(u)try{a=(0,i.D4)(e.payload)}catch{throw new n.Ye("Failed to base64url decode the payload")}else a="string"==typeof e.payload?S.Rd.encode(e.payload):e.payload;let g={payload:a};return(void 0!==e.protected&&(g.protectedHeader=o),void 0!==e.header&&(g.unprotectedHeader=e.header),p)?{...g,key:t}:g}async function C(e,t,r){if(e instanceof Uint8Array&&(e=S.D0.decode(e)),"string"!=typeof e)throw new n.Ye("Compact JWS must be a string or Uint8Array");let{0:i,1:s,2:a,length:o}=e.split(".");if(3!==o)throw new n.Ye("Invalid Compact JWS");let l=await I({payload:s,protected:i,signature:a},t,r),c={payload:l.payload,protectedHeader:l.protectedHeader};return"function"==typeof t?{...c,key:l.key}:c}var O=r(44214);class R{_payload;_protectedHeader;_unprotectedHeader;constructor(e){if(!(e instanceof Uint8Array))throw TypeError("payload must be an instance of Uint8Array");this._payload=e}setProtectedHeader(e){if(this._protectedHeader)throw TypeError("setProtectedHeader can only be called once");return this._protectedHeader=e,this}setUnprotectedHeader(e){if(this._unprotectedHeader)throw TypeError("setUnprotectedHeader can only be called once");return this._unprotectedHeader=e,this}async sign(e,t){let r;if(!this._protectedHeader&&!this._unprotectedHeader)throw new n.Ye("either setProtectedHeader or setUnprotectedHeader must be called before #sign()");if(!(0,k.A)(this._protectedHeader,this._unprotectedHeader))throw new n.Ye("JWS Protected and JWS Unprotected Header Parameter names must be disjoint");let s={...this._protectedHeader,...this._unprotectedHeader},a=(0,T.A)(n.Ye,new Map([["b64",!0]]),t?.crit,this._protectedHeader,s),o=!0;if(a.has("b64")&&"boolean"!=typeof(o=this._protectedHeader.b64))throw new n.Ye('The "b64" (base64url-encode payload) Header Parameter must be a boolean');let{alg:l}=s;if("string"!=typeof l||!l)throw new n.Ye('JWS "alg" (Algorithm) Header Parameter missing or invalid');(0,E.I)(l,e,"sign");let c=this._payload;o&&(c=S.Rd.encode((0,i.lF)(c))),r=this._protectedHeader?S.Rd.encode((0,i.lF)(JSON.stringify(this._protectedHeader))):S.Rd.encode("");let u=(0,S.xW)(r,S.Rd.encode("."),c),d=await b(l,e,u),h={signature:(0,i.lF)(d),payload:""};return o&&(h.payload=S.D0.decode(c)),this._unprotectedHeader&&(h.header=this._unprotectedHeader),this._protectedHeader&&(h.protected=S.D0.decode(r)),h}}class F{_flattened;constructor(e){this._flattened=new R(e)}setProtectedHeader(e){return this._flattened.setProtectedHeader(e),this}async sign(e,t){let r=await this._flattened.sign(e,t);if(void 0===r.payload)throw TypeError("use the flattened module for creating JWS with b64: false");return`${r.protected}.${r.payload}.${r.signature}`}}function U(e,t,{cachePromiseRejection:r=!1}={}){let i,s,a=!1;return function(...n){return a&&t(n,i)||(s=e.apply(this,n),!r&&s.catch&&s.catch(()=>a=!1),a=!0,i=n),s}}var H=U((e,t)=>C(e,O.D(t),{algorithms:["HS256"]}),(e,t)=>e[0]===t[0]&&e[1]===t[1],{cachePromiseRejection:!0});async function $(e,t,r){var i,s;let{payload:a}=await H(e,r),[n,o]=a.length===t.length?[a]:(i=a,s=t.length,[i.slice(0,s),i.slice(s)]),l=o?JSON.parse(`[${new TextDecoder().decode(o)}]`):null,c=0;return n.reduce((e,r,i)=>{let s=t[i];if(!s)throw Error(`flags: No flag at index ${i}`);switch(r){case 253:e[s.key]=!1;break;case 254:e[s.key]=!0;break;case 255:e[s.key]=l[c++];break;case 252:e[s.key]=null;break;default:e[s.key]=s.options?.[r]?.value}return e},{})}U((e,t)=>new F(e).setProtectedHeader({alg:"HS256"}).sign(O.D(t)),(e,t)=>e[0].length===t[0].length&&e[0].every((e,r)=>t[0][r]===e)&&e[1]===t[1],{cachePromiseRejection:!0});var N=r(26818);function J(e){var t;let r=["path"in e&&e.path&&`Path=${e.path}`,"expires"in e&&(e.expires||0===e.expires)&&`Expires=${("number"==typeof e.expires?new Date(e.expires):e.expires).toUTCString()}`,"maxAge"in e&&"number"==typeof e.maxAge&&`Max-Age=${e.maxAge}`,"domain"in e&&e.domain&&`Domain=${e.domain}`,"secure"in e&&e.secure&&"Secure","httpOnly"in e&&e.httpOnly&&"HttpOnly","sameSite"in e&&e.sameSite&&`SameSite=${e.sameSite}`,"partitioned"in e&&e.partitioned&&"Partitioned","priority"in e&&e.priority&&`Priority=${e.priority}`].filter(Boolean),i=`${e.name}=${encodeURIComponent(null!=(t=e.value)?t:"")}`;return 0===r.length?i:`${i}; ${r.join("; ")}`}var M=class{constructor(e){this._parsed=new Map,this._headers=e;let t=e.get("cookie");if(t)for(let[e,r]of function(e){let t=new Map;for(let r of e.split(/; */)){if(!r)continue;let e=r.indexOf("=");if(-1===e){t.set(r,"true");continue}let[i,s]=[r.slice(0,e),r.slice(e+1)];try{t.set(i,decodeURIComponent(null!=s?s:"true"))}catch{}}return t}(t))this._parsed.set(e,{name:e,value:r})}[Symbol.iterator](){return this._parsed[Symbol.iterator]()}get size(){return this._parsed.size}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed);if(!e.length)return r.map(([e,t])=>t);let i="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(([e])=>e===i).map(([e,t])=>t)}has(e){return this._parsed.has(e)}set(...e){let[t,r]=1===e.length?[e[0].name,e[0].value]:e,i=this._parsed;return i.set(t,{name:t,value:r}),this._headers.set("cookie",Array.from(i).map(([e,t])=>J(t)).join("; ")),this}delete(e){let t=this._parsed,r=Array.isArray(e)?e.map(e=>t.delete(e)):t.delete(e);return this._headers.set("cookie",Array.from(t).map(([e,t])=>J(t)).join("; ")),r}clear(){return this.delete(Array.from(this._parsed.keys())),this}[Symbol.for("edge-runtime.inspect.custom")](){return`RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(e=>`${e.name}=${encodeURIComponent(e.value)}`).join("; ")}};Symbol.for("edge-runtime.inspect.custom");var q=U(e=>(0,N.PO)(e),(e,t)=>e[0]===t[0],{cachePromiseRejection:!0});async function D(e){return"string"==typeof e&&""!==e?await q(e)??null:null}async function j(e,t,r=process.env.FLAGS_SECRET){if(!r)throw Error("flags: Can not serialize due to missing secret");return $(t,e,r)}async function L(e,t,r,i=process.env.FLAGS_SECRET){if(!i)throw Error("flags: getPrecomputed was called without a secret. Please set FLAGS_SECRET environment variable.");let s=await j(t,r,i);return Array.isArray(e)?e.map(e=>s[e.key]):s[e.key]}var K=Symbol.for("react.postpone");var W=new WeakMap;function z(e,t,r,i){let s=W.get(e);if(!s)return void W.set(e,new Map([[t,new Map([[r,i]])]]));let a=s.get(t);if(!a)return void s.set(t,new Map([[r,i]]));a.set(r,i)}var B=new WeakMap,V=new WeakMap,G=new WeakMap,Y=new WeakMap;async function Q(e,t,r,i){if(!e)return;if("function"!=typeof e)return e;let s=Y.get(t);if(s)return e(...s);let a=[{headers:r,cookies:i}];return Y.set(t,a),e(...a)}function X(e){var t;let i=function(t){if("function"==typeof e.decide)return e.decide(t);if("function"==typeof e.adapter?.decide)return e.adapter.decide({key:e.key,...t});throw Error(`flags: No decide function provided for ${e.key}`)},s=function(t){return"function"==typeof e.identify?e.identify(t):"function"==typeof e.adapter?.identify?e.adapter.identify(t):e.identify},a=async function(t){let s,a,n;if(t.request){let e=function(e){let t=B.get(e);if(void 0!==t)return t;let r=new Headers;for(let[t,i]of Object.entries(e))Array.isArray(i)?i.forEach(e=>r.append(t,e)):void 0!==i&&r.append(t,i);return B.set(e,r),r}(t.request.headers);s=function(e){let t=V.get(e);if(void 0!==t)return t;let r=N.oF.seal(e);return V.set(e,r),r}(e),a=function(e){let t=G.get(e);if(void 0!==t)return t;let r=N.Ck.seal(new M(e));return G.set(e,r),r}(e),n=t.request.headers}else{let{headers:e,cookies:t}=await Promise.all([r.e(2644),r.e(673)]).then(r.bind(r,62644)),[i,o]=await Promise.all([e(),t()]);s=i,a=o,n=i}let o=await D(a.get("vercel-flag-overrides")?.value),l=await Q(t.identify,n,s,a),c=JSON.stringify(l)??"",u=function(e,t,r){let i=W.get(e)?.get(t);if(i)return i.get(r)}(s,e.key,c);if(void 0!==u)return(0,N.J_)("method","cached"),await u;if(o&&void 0!==o[e.key]){(0,N.J_)("method","override");let t=o[e.key];return z(s,e.key,c,Promise.resolve(t)),(0,N.HC)(e.key,t,{reason:"override"}),t}let d=(async()=>i({defaultValue:e.defaultValue,headers:s,cookies:a,entities:l}))().then(t=>{if(void 0!==t)return t;if(void 0!==e.defaultValue)return e.defaultValue;throw Error(`flags: Flag "${e.key}" must have a defaultValue or a decide function that returns a value`)},t=>{if(function(e){if("object"==typeof e&&null!==e&&"$$typeof"in e&&e.$$typeof===K)return!0;if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let t=e.digest.split(";")[0];return"NEXT_REDIRECT"===t||"DYNAMIC_SERVER_USAGE"===t||"BAILOUT_TO_CLIENT_SIDE_RENDERING"===t||"NEXT_NOT_FOUND"===t}(t))throw t;if(void 0!==e.defaultValue)return console.warn(`flags: Flag "${e.key}" is falling back to its defaultValue after catching the following error`,t),e.defaultValue;throw console.warn(`flags: Flag "${e.key}" could not be evaluated`),t});z(s,e.key,c,d);let h=await d;return e.config?.reportValue!==!1&&(0,N.aC)(e.key,h),h},n=e.origin?e.origin:"function"==typeof e.adapter?.origin?e.adapter.origin(e.key):e.adapter?.origin,o=(0,N.uP)(async(...e)=>{if((0,N.J_)("method","decided"),"string"==typeof e[0]&&Array.isArray(e[1])){let[t,r,i]=e;if(t&&r)return(0,N.J_)("method","precomputed"),L(o,r,t,i)}if(e[0]&&"object"==typeof e[0]&&"headers"in e[0]){let[t]=e;return a({identify:s,request:t})}return a({identify:s,request:void 0})},{name:"flag",isVerboseTrace:!1,attributes:{key:e.key}});return o.key=e.key,o.defaultValue=e.defaultValue,o.origin=n,o.options=Array.isArray(t=e.options)?t.map(e=>"boolean"==typeof e||"number"==typeof e||"string"==typeof e||null===e?{value:e}:e):t,o.description=e.description,o.identify=s?(0,N.uP)(s,{isVerboseTrace:!1,name:"identify",attributes:{key:e.key}}):s,o.decide=(0,N.uP)(i,{isVerboseTrace:!1,name:"decide",attributes:{key:e.key}}),o.run=(0,N.uP)(a,{isVerboseTrace:!1,name:"run",attributes:{key:e.key}}),o}},13317:(e,t,r)=>{r.d(t,{A:()=>s});var i=r(77598);let s=(e,t)=>{let r;try{r=e instanceof i.KeyObject?e.asymmetricKeyDetails?.modulusLength:Buffer.from(e.n,"base64url").byteLength<<3}catch{}if("number"!=typeof r||r<2048)throw TypeError(`${t} requires key modulusLength to be 2048 bits or larger`)}},13690:(e,t,r)=>{r.d(t,{D4:()=>n,lF:()=>a});var i=r(4573),s=r(45271);let a=e=>i.Buffer.from(e).toString("base64url"),n=e=>new Uint8Array(i.Buffer.from(function(e){let t=e;return t instanceof Uint8Array&&(t=s.D0.decode(t)),t}(e),"base64url"))},14295:(e,t,r)=>{r.d(t,{Dp:()=>d,Rb:()=>n,T0:()=>o,Ye:()=>u,aA:()=>c,h2:()=>p,ie:()=>s,n:()=>a,xO:()=>l});class i extends Error{static code="ERR_JOSE_GENERIC";code="ERR_JOSE_GENERIC";constructor(e,t){super(e,t),this.name=this.constructor.name,Error.captureStackTrace?.(this,this.constructor)}}class s extends i{static code="ERR_JWT_CLAIM_VALIDATION_FAILED";code="ERR_JWT_CLAIM_VALIDATION_FAILED";claim;reason;payload;constructor(e,t,r="unspecified",i="unspecified"){super(e,{cause:{claim:r,reason:i,payload:t}}),this.claim=r,this.reason=i,this.payload=t}}class a extends i{static code="ERR_JWT_EXPIRED";code="ERR_JWT_EXPIRED";claim;reason;payload;constructor(e,t,r="unspecified",i="unspecified"){super(e,{cause:{claim:r,reason:i,payload:t}}),this.claim=r,this.reason=i,this.payload=t}}class n extends i{static code="ERR_JOSE_ALG_NOT_ALLOWED";code="ERR_JOSE_ALG_NOT_ALLOWED"}class o extends i{static code="ERR_JOSE_NOT_SUPPORTED";code="ERR_JOSE_NOT_SUPPORTED"}class l extends i{static code="ERR_JWE_DECRYPTION_FAILED";code="ERR_JWE_DECRYPTION_FAILED";constructor(e="decryption operation failed",t){super(e,t)}}class c extends i{static code="ERR_JWE_INVALID";code="ERR_JWE_INVALID"}class u extends i{static code="ERR_JWS_INVALID";code="ERR_JWS_INVALID"}class d extends i{static code="ERR_JWT_INVALID";code="ERR_JWT_INVALID"}class h extends i{[Symbol.asyncIterator];static code="ERR_JWKS_MULTIPLE_MATCHING_KEYS";code="ERR_JWKS_MULTIPLE_MATCHING_KEYS";constructor(e="multiple matching keys found in the JSON Web Key Set",t){super(e,t)}}class p extends i{static code="ERR_JWS_SIGNATURE_VERIFICATION_FAILED";code="ERR_JWS_SIGNATURE_VERIFICATION_FAILED";constructor(e="signature verification failed",t){super(e,t)}}},18639:(e,t,r)=>{r.d(t,{j:()=>sr});var i=Object.defineProperty,s=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,n=Object.prototype.hasOwnProperty,o=e=>{throw TypeError(e)},l=async e=>new Promise(t=>setTimeout(t,e)),c=(e,t)=>t?e*(1+Math.random()):e,u=e=>"undefined"!=typeof atob&&"function"==typeof atob?atob(e):"undefined"!=typeof global&&global.Buffer?new global.Buffer(e,"base64").toString():e,d=[".lcl.dev",".lclstage.dev",".lclclerk.com"],h=[".lcl.dev",".stg.dev",".lclstage.dev",".stgstage.dev",".dev.lclclerk.com",".stg.lclclerk.com",".accounts.lclclerk.com","accountsstage.dev","accounts.dev"],p=[".lcl.dev","lclstage.dev",".lclclerk.com",".accounts.lclclerk.com"],f=[".accountsstage.dev"],g="https://api.clerk.com",y="pk_live_";function m(e,t={}){if(!(e=e||"")||!w(e)){if(t.fatal&&!e)throw Error("Publishable key is missing. Ensure that your publishable key is correctly configured. Double-check your environment configuration for your keys, or access them here: https://dashboard.clerk.com/last-active?path=api-keys");if(t.fatal&&!w(e))throw Error("Publishable key not valid.");return null}let r=e.startsWith(y)?"production":"development",i=u(e.split("_")[2]);return i=i.slice(0,-1),t.proxyUrl?i=t.proxyUrl:"development"!==r&&t.domain&&t.isSatellite&&(i=`clerk.${t.domain}`),{instanceType:r,frontendApi:i}}function w(e=""){try{let t=e.startsWith(y)||e.startsWith("pk_test_"),r=u(e.split("_")[2]||"").endsWith("$");return t&&r}catch{return!1}}var b=()=>!1,_=()=>{try{return!0}catch{}return!1},v=new Set,S=(e,t,r)=>{let i=b()||_(),s=r??e;v.has(s)||i||(v.add(s),console.warn(`Clerk - DEPRECATION WARNING: "${e}" is deprecated and will be removed in the next major release.
${t}`))};function k(e){return{code:e.code,message:e.message,longMessage:e.long_message,meta:{paramName:e?.meta?.param_name,sessionId:e?.meta?.session_id,emailAddresses:e?.meta?.email_addresses,identifiers:e?.meta?.identifiers,zxcvbn:e?.meta?.zxcvbn}}}var A=class e extends Error{constructor(t,{data:r,status:i,clerkTraceId:s,retryAfter:a}){super(t),this.toString=()=>{let e=`[${this.name}]
Message:${this.message}
Status:${this.status}
Serialized errors: ${this.errors.map(e=>JSON.stringify(e))}`;return this.clerkTraceId&&(e+=`
Clerk Trace ID: ${this.clerkTraceId}`),e},Object.setPrototypeOf(this,e.prototype),this.status=i,this.message=t,this.clerkTraceId=s,this.retryAfter=a,this.clerkError=!0,this.errors=function(e=[]){return e.length>0?e.map(k):[]}(r)}},E=Object.freeze({InvalidProxyUrlErrorMessage:"The proxyUrl passed to Clerk is invalid. The expected value for proxyUrl is an absolute URL or a relative path with a leading '/'. (key={{url}})",InvalidPublishableKeyErrorMessage:"The publishableKey passed to Clerk is invalid. You can get your Publishable key at https://dashboard.clerk.com/last-active?path=api-keys. (key={{key}})",MissingPublishableKeyErrorMessage:"Missing publishableKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.",MissingSecretKeyErrorMessage:"Missing secretKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.",MissingClerkProvider:"{{source}} can only be used within the <ClerkProvider /> component. Learn more: https://clerk.com/docs/components/clerk-provider"}),T=function({packageName:e,customMessages:t}){let r=e,i={...E,...t};function s(e,t){if(!t)return`${r}: ${e}`;let i=e;for(let r of e.matchAll(/{{([a-zA-Z0-9-_]+)}}/g)){let e=(t[r[1]]||"").toString();i=i.replace(`{{${r[1]}}}`,e)}return`${r}: ${i}`}return{setPackageName({packageName:e}){return"string"==typeof e&&(r=e),this},setMessages({customMessages:e}){return Object.assign(i,e||{}),this},throwInvalidPublishableKeyError(e){throw Error(s(i.InvalidPublishableKeyErrorMessage,e))},throwInvalidProxyUrl(e){throw Error(s(i.InvalidProxyUrlErrorMessage,e))},throwMissingPublishableKeyError(){throw Error(s(i.MissingPublishableKeyErrorMessage))},throwMissingSecretKeyError(){throw Error(s(i.MissingSecretKeyErrorMessage))},throwMissingClerkProviderError(e){throw Error(s(i.MissingClerkProvider,e))},throw(e){throw Error(s(e))}}}({packageName:"@clerk/backend"}),{isDevOrStagingUrl:P}=function(){let e=new Map;return{isDevOrStagingUrl:t=>{if(!t)return!1;let r="string"==typeof t?t:t.hostname,i=e.get(r);return void 0===i&&(i=h.some(e=>r.endsWith(e)),e.set(r,i)),i}}}(),x={TokenExpired:"token-expired",TokenInvalid:"token-invalid",TokenInvalidAlgorithm:"token-invalid-algorithm",TokenInvalidAuthorizedParties:"token-invalid-authorized-parties",TokenInvalidSignature:"token-invalid-signature",TokenNotActiveYet:"token-not-active-yet",TokenIatInTheFuture:"token-iat-in-the-future",TokenVerificationFailed:"token-verification-failed"},I={EnsureClerkJWT:"Make sure that this is a valid Clerk generate JWT."},C=class e extends Error{constructor({action:t,message:r,reason:i}){super(r),Object.setPrototypeOf(this,e.prototype),this.reason=i,this.message=r,this.action=t}getFullMessage(){return`${[this.message,this.action].filter(e=>e).join(" ")} (reason=${this.reason}, token-carrier=${this.tokenCarrier})`}},O=class extends Error{},R=r(77598),F=fetch.bind(globalThis),U={crypto:R.webcrypto,get fetch(){return F},AbortController:globalThis.AbortController,Blob:globalThis.Blob,FormData:globalThis.FormData,Headers:globalThis.Headers,Request:globalThis.Request,Response:globalThis.Response},H={parse:(e,t)=>(function(e,t,r={}){if(!t.codes){t.codes={};for(let e=0;e<t.chars.length;++e)t.codes[t.chars[e]]=e}if(!r.loose&&e.length*t.bits&7)throw SyntaxError("Invalid padding");let i=e.length;for(;"="===e[i-1];)if(--i,!r.loose&&!((e.length-i)*t.bits&7))throw SyntaxError("Invalid padding");let s=new(r.out??Uint8Array)(i*t.bits/8|0),a=0,n=0,o=0;for(let r=0;r<i;++r){let i=t.codes[e[r]];if(void 0===i)throw SyntaxError("Invalid character "+e[r]);n=n<<t.bits|i,(a+=t.bits)>=8&&(a-=8,s[o++]=255&n>>a)}if(a>=t.bits||255&n<<8-a)throw SyntaxError("Unexpected end of data");return s})(e,$,t),stringify:(e,t)=>(function(e,t,r={}){let{pad:i=!0}=r,s=(1<<t.bits)-1,a="",n=0,o=0;for(let r=0;r<e.length;++r)for(o=o<<8|255&e[r],n+=8;n>t.bits;)n-=t.bits,a+=t.chars[s&o>>n];if(n&&(a+=t.chars[s&o<<t.bits-n]),i)for(;a.length*t.bits&7;)a+="=";return a})(e,$,t)},$={chars:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_",bits:6},N={RS256:"SHA-256",RS384:"SHA-384",RS512:"SHA-512"},J="RSASSA-PKCS1-v1_5",M={RS256:J,RS384:J,RS512:J},q=Object.keys(N);function D(e){let t=N[e],r=M[e];if(!t||!r)throw Error(`Unsupported algorithm ${e}, expected one of ${q.join(",")}.`);return{hash:{name:N[e]},name:M[e]}}var j=e=>Array.isArray(e)&&e.length>0&&e.every(e=>"string"==typeof e),L=(e,t)=>{let r=[t].flat().filter(e=>!!e),i=[e].flat().filter(e=>!!e);if(r.length>0&&i.length>0){if("string"==typeof e){if(!r.includes(e))throw new C({action:I.EnsureClerkJWT,reason:x.TokenVerificationFailed,message:`Invalid JWT audience claim (aud) ${JSON.stringify(e)}. Is not included in "${JSON.stringify(r)}".`})}else if(j(e)&&!e.some(e=>r.includes(e)))throw new C({action:I.EnsureClerkJWT,reason:x.TokenVerificationFailed,message:`Invalid JWT audience claim array (aud) ${JSON.stringify(e)}. Is not included in "${JSON.stringify(r)}".`})}},K=e=>{if(void 0!==e&&"JWT"!==e)throw new C({action:I.EnsureClerkJWT,reason:x.TokenInvalid,message:`Invalid JWT type ${JSON.stringify(e)}. Expected "JWT".`})},W=e=>{if(!q.includes(e))throw new C({action:I.EnsureClerkJWT,reason:x.TokenInvalidAlgorithm,message:`Invalid JWT algorithm ${JSON.stringify(e)}. Supported: ${q}.`})},z=e=>{if("string"!=typeof e)throw new C({action:I.EnsureClerkJWT,reason:x.TokenVerificationFailed,message:`Subject claim (sub) is required and must be a string. Received ${JSON.stringify(e)}.`})},B=(e,t)=>{if(e&&t&&0!==t.length&&!t.includes(e))throw new C({reason:x.TokenInvalidAuthorizedParties,message:`Invalid JWT Authorized party claim (azp) ${JSON.stringify(e)}. Expected "${t}".`})},V=(e,t)=>{if("number"!=typeof e)throw new C({action:I.EnsureClerkJWT,reason:x.TokenVerificationFailed,message:`Invalid JWT expiry date claim (exp) ${JSON.stringify(e)}. Expected number.`});let r=new Date(Date.now()),i=new Date(0);if(i.setUTCSeconds(e),i.getTime()<=r.getTime()-t)throw new C({reason:x.TokenExpired,message:`JWT is expired. Expiry date: ${i.toUTCString()}, Current date: ${r.toUTCString()}.`})},G=(e,t)=>{if(void 0===e)return;if("number"!=typeof e)throw new C({action:I.EnsureClerkJWT,reason:x.TokenVerificationFailed,message:`Invalid JWT not before date claim (nbf) ${JSON.stringify(e)}. Expected number.`});let r=new Date(Date.now()),i=new Date(0);if(i.setUTCSeconds(e),i.getTime()>r.getTime()+t)throw new C({reason:x.TokenNotActiveYet,message:`JWT cannot be used prior to not before date claim (nbf). Not before date: ${i.toUTCString()}; Current date: ${r.toUTCString()};`})},Y=(e,t)=>{if(void 0===e)return;if("number"!=typeof e)throw new C({action:I.EnsureClerkJWT,reason:x.TokenVerificationFailed,message:`Invalid JWT issued at date claim (iat) ${JSON.stringify(e)}. Expected number.`});let r=new Date(Date.now()),i=new Date(0);if(i.setUTCSeconds(e),i.getTime()>r.getTime()+t)throw new C({reason:x.TokenIatInTheFuture,message:`JWT issued at date claim (iat) is in the future. Issued at date: ${i.toUTCString()}; Current date: ${r.toUTCString()};`})};function Q(e,t,r){if("object"==typeof e)return U.crypto.subtle.importKey("jwk",e,t,!1,[r]);let i=function(e){let t=u(e.replace(/-----BEGIN.*?-----/g,"").replace(/-----END.*?-----/g,"").replace(/\s/g,"")),r=new Uint8Array(new ArrayBuffer(t.length));for(let e=0,i=t.length;e<i;e++)r[e]=t.charCodeAt(e);return r}(e),s="sign"===r?"pkcs8":"spki";return U.crypto.subtle.importKey(s,i,t,!1,[r])}async function X(e,t){let{header:r,signature:i,raw:s}=e,a=new TextEncoder().encode([s.header,s.payload].join(".")),n=D(r.alg);try{let e=await Q(t,n,"verify");return{data:await U.crypto.subtle.verify(n.name,e,i,a)}}catch(e){return{errors:[new C({reason:x.TokenInvalidSignature,message:e?.message})]}}}function Z(e){let t=(e||"").toString().split(".");if(3!==t.length)return{errors:[new C({reason:x.TokenInvalid,message:"Invalid JWT form. A JWT consists of three parts separated by dots."})]};let[r,i,s]=t,a=new TextDecoder,n=JSON.parse(a.decode(H.parse(r,{loose:!0}))),o=JSON.parse(a.decode(H.parse(i,{loose:!0})));return{data:{header:n,payload:o,signature:H.parse(s,{loose:!0}),raw:{header:r,payload:i,signature:s,text:e}}}}async function ee(e,t){let{audience:r,authorizedParties:i,clockSkewInMs:s,key:a}=t,n=s||5e3,{data:o,errors:l}=Z(e);if(l)return{errors:l};let{header:c,payload:u}=o;try{let{typ:e,alg:t}=c;K(e),W(t);let{azp:s,sub:a,aud:o,iat:l,exp:d,nbf:h}=u;z(a),L([o],[r]),B(s,i),V(d,n),G(h,n),Y(l,n)}catch(e){return{errors:[e]}}let{data:d,errors:h}=await X(o,a);return h?{errors:[new C({action:I.EnsureClerkJWT,reason:x.TokenVerificationFailed,message:`Error verifying JWT signature. ${h[0]}`})]}:d?{data:u}:{errors:[new C({reason:x.TokenInvalidSignature,message:"JWT signature is invalid."})]}}var et=r(76190),er={strict_mfa:{afterMinutes:10,level:"multi_factor"},strict:{afterMinutes:10,level:"second_factor"},moderate:{afterMinutes:60,level:"second_factor"},lax:{afterMinutes:1440,level:"second_factor"}},ei=new Set(["first_factor","second_factor","multi_factor"]),es=new Set(["strict_mfa","strict","moderate","lax"]),ea=e=>"number"==typeof e&&e>0,en=e=>ei.has(e),eo=e=>es.has(e),el=e=>e.startsWith("org:")?e:`org:${e}`,ec=(e,t)=>{let{orgId:r,orgRole:i,orgPermissions:s}=t;return(e.role||e.permission)&&r&&i&&s?e.permission?s.includes(el(e.permission)):e.role?i===el(e.role):null:null},eu=(e,t)=>{let{org:r,user:i}=eh(e),[s,a]=t.split(":"),n=a||s;return"org"===s?r.includes(n):"user"===s?i.includes(n):[...r,...i].includes(n)},ed=(e,t)=>{let{features:r,plans:i}=t;return e.feature&&r?eu(r,e.feature):e.plan&&i?eu(i,e.plan):null},eh=e=>{let t=e?e.split(",").map(e=>e.trim()):[];return{org:t.filter(e=>e.split(":")[0].includes("o")).map(e=>e.split(":")[1]),user:t.filter(e=>e.split(":")[0].includes("u")).map(e=>e.split(":")[1])}},ep=e=>{if(!e)return!1;let t="string"==typeof e&&eo(e),r="object"==typeof e&&en(e.level)&&ea(e.afterMinutes);return(!!t||!!r)&&(e=>"string"==typeof e?er[e]:e).bind(null,e)},ef=(e,{factorVerificationAge:t})=>{if(!e.reverification||!t)return null;let r=ep(e.reverification);if(!r)return null;let{level:i,afterMinutes:s}=r(),[a,n]=t,o=-1!==a?s>a:null,l=-1!==n?s>n:null;switch(i){case"first_factor":return o;case"second_factor":return -1!==n?l:o;case"multi_factor":return -1===n?o:o&&l}},eg=e=>t=>{if(!e.userId)return!1;let r=ed(t,e),i=ec(t,e),s=ef(t,e);return[r||i,s].some(e=>null===e)?[r||i,s].some(e=>!0===e):[r||i,s].every(e=>!0===e)},ey=({per:e,fpm:t})=>{if(!e||!t)return{permissions:[],featurePermissionMap:[]};let r=e.split(",").map(e=>e.trim()),i=t.split(",").map(e=>Number.parseInt(e.trim(),10)).map(e=>e.toString(2).padStart(r.length,"0").split("").map(e=>Number.parseInt(e,10)).reverse()).filter(Boolean);return{permissions:r,featurePermissionMap:i}},em=e=>{let t,r,i,s,a=e.fva??null,n=e.sts??null;if(2===e.v){if(e.o){t=e.o?.id,i=e.o?.slg,e.o?.rol&&(r=`org:${e.o?.rol}`);let{org:a}=eh(e.fea),{permissions:n,featurePermissionMap:o}=ey({per:e.o?.per,fpm:e.o?.fpm});s=function({features:e,permissions:t,featurePermissionMap:r}){if(!e||!t||!r)return[];let i=[];for(let s=0;s<e.length;s++){let a=e[s];if(s>=r.length)continue;let n=r[s];if(n)for(let e=0;e<n.length;e++)1===n[e]&&i.push(`org:${a}:${t[e]}`)}return i}({features:a,featurePermissionMap:o,permissions:n})}}else t=e.org_id,r=e.org_role,i=e.org_slug,s=e.org_permissions;return{sessionClaims:e,sessionId:e.sid,sessionStatus:n,actor:e.act,userId:e.sub,orgId:t,orgRole:r,orgSlug:i,orgPermissions:s,factorVerificationAge:a}},ew=r(72645),eb="https://api.clerk.com",e_="@clerk/backend@1.33.0",ev="2025-04-10",eS={Session:"__session",Refresh:"__refresh",ClientUat:"__client_uat",Handshake:"__clerk_handshake",DevBrowser:"__clerk_db_jwt",RedirectCount:"__clerk_redirect_count",HandshakeNonce:"__clerk_handshake_nonce"},ek={ClerkSynced:"__clerk_synced",SuffixedCookies:"suffixed_cookies",ClerkRedirectUrl:"__clerk_redirect_url",DevBrowser:eS.DevBrowser,Handshake:eS.Handshake,HandshakeHelp:"__clerk_help",LegacyDevBrowser:"__dev_session",HandshakeReason:"__clerk_hs_reason",HandshakeNonce:eS.HandshakeNonce},eA={Attributes:{AuthToken:"__clerkAuthToken",AuthSignature:"__clerkAuthSignature",AuthStatus:"__clerkAuthStatus",AuthReason:"__clerkAuthReason",AuthMessage:"__clerkAuthMessage",ClerkUrl:"__clerkUrl"},Cookies:eS,Headers:{Accept:"accept",AuthMessage:"x-clerk-auth-message",Authorization:"authorization",AuthReason:"x-clerk-auth-reason",AuthSignature:"x-clerk-auth-signature",AuthStatus:"x-clerk-auth-status",AuthToken:"x-clerk-auth-token",CacheControl:"cache-control",ClerkRedirectTo:"x-clerk-redirect-to",ClerkRequestData:"x-clerk-request-data",ClerkUrl:"x-clerk-clerk-url",CloudFrontForwardedProto:"cloudfront-forwarded-proto",ContentType:"content-type",ContentSecurityPolicy:"content-security-policy",ContentSecurityPolicyReportOnly:"content-security-policy-report-only",EnableDebug:"x-clerk-debug",ForwardedHost:"x-forwarded-host",ForwardedPort:"x-forwarded-port",ForwardedProto:"x-forwarded-proto",Host:"host",Location:"location",Nonce:"x-nonce",Origin:"origin",Referrer:"referer",SecFetchDest:"sec-fetch-dest",UserAgent:"user-agent",ReportingEndpoints:"reporting-endpoints"},ContentTypes:{Json:"application/json"},QueryParameters:ek},eE=RegExp("(?<!:)/{1,}","g");function eT(...e){return e.filter(e=>e).join("/").replace(eE,"/")}var eP=class{constructor(e){this.request=e}requireId(e){if(!e)throw Error("A valid resource ID is required.")}},ex="/actor_tokens",eI=class extends eP{async create(e){return this.request({method:"POST",path:ex,bodyParams:e})}async revoke(e){return this.requireId(e),this.request({method:"POST",path:eT(ex,e,"revoke")})}},eC="/accountless_applications",eO=class extends eP{async createAccountlessApplication(){return this.request({method:"POST",path:eC})}async completeAccountlessApplicationOnboarding(){return this.request({method:"POST",path:eT(eC,"complete")})}},eR="/allowlist_identifiers",eF=class extends eP{async getAllowlistIdentifierList(e={}){return this.request({method:"GET",path:eR,queryParams:{...e,paginated:!0}})}async createAllowlistIdentifier(e){return this.request({method:"POST",path:eR,bodyParams:e})}async deleteAllowlistIdentifier(e){return this.requireId(e),this.request({method:"DELETE",path:eT(eR,e)})}},eU=class extends eP{async changeDomain(e){return this.request({method:"POST",path:eT("/beta_features","change_domain"),bodyParams:e})}},eH="/blocklist_identifiers",e$=class extends eP{async getBlocklistIdentifierList(e={}){return this.request({method:"GET",path:eH,queryParams:e})}async createBlocklistIdentifier(e){return this.request({method:"POST",path:eH,bodyParams:e})}async deleteBlocklistIdentifier(e){return this.requireId(e),this.request({method:"DELETE",path:eT(eH,e)})}},eN="/clients",eJ=class extends eP{async getClientList(e={}){return this.request({method:"GET",path:eN,queryParams:{...e,paginated:!0}})}async getClient(e){return this.requireId(e),this.request({method:"GET",path:eT(eN,e)})}verifyClient(e){return this.request({method:"POST",path:eT(eN,"verify"),bodyParams:{token:e}})}async getHandshakePayload(e){return this.request({method:"GET",path:eT(eN,"handshake_payload"),queryParams:e})}},eM="/domains",eq=class extends eP{async list(){return this.request({method:"GET",path:eM})}async add(e){return this.request({method:"POST",path:eM,bodyParams:e})}async update(e){let{domainId:t,...r}=e;return this.requireId(t),this.request({method:"PATCH",path:eT(eM,t),bodyParams:r})}async delete(e){return this.deleteDomain(e)}async deleteDomain(e){return this.requireId(e),this.request({method:"DELETE",path:eT(eM,e)})}},eD="/email_addresses",ej=class extends eP{async getEmailAddress(e){return this.requireId(e),this.request({method:"GET",path:eT(eD,e)})}async createEmailAddress(e){return this.request({method:"POST",path:eD,bodyParams:e})}async updateEmailAddress(e,t={}){return this.requireId(e),this.request({method:"PATCH",path:eT(eD,e),bodyParams:t})}async deleteEmailAddress(e){return this.requireId(e),this.request({method:"DELETE",path:eT(eD,e)})}},eL="/instance",eK=class extends eP{async get(){return this.request({method:"GET",path:eL})}async update(e){return this.request({method:"PATCH",path:eL,bodyParams:e})}async updateRestrictions(e){return this.request({method:"PATCH",path:eT(eL,"restrictions"),bodyParams:e})}async updateOrganizationSettings(e){return this.request({method:"PATCH",path:eT(eL,"organization_settings"),bodyParams:e})}},eW="/invitations",ez=class extends eP{async getInvitationList(e={}){return this.request({method:"GET",path:eW,queryParams:{...e,paginated:!0}})}async createInvitation(e){return this.request({method:"POST",path:eW,bodyParams:e})}async revokeInvitation(e){return this.requireId(e),this.request({method:"POST",path:eT(eW,e,"revoke")})}},eB=class extends eP{async getJwks(){return this.request({method:"GET",path:"/jwks"})}},eV="/jwt_templates",eG=class extends eP{async list(e={}){return this.request({method:"GET",path:eV,queryParams:{...e,paginated:!0}})}async get(e){return this.requireId(e),this.request({method:"GET",path:eT(eV,e)})}async create(e){return this.request({method:"POST",path:eV,bodyParams:e})}async update(e){let{templateId:t,...r}=e;return this.requireId(t),this.request({method:"PATCH",path:eT(eV,t),bodyParams:r})}async delete(e){return this.requireId(e),this.request({method:"DELETE",path:eT(eV,e)})}},eY="/organizations",eQ=class extends eP{async getOrganizationList(e){return this.request({method:"GET",path:eY,queryParams:e})}async createOrganization(e){return this.request({method:"POST",path:eY,bodyParams:e})}async getOrganization(e){let{includeMembersCount:t}=e,r="organizationId"in e?e.organizationId:e.slug;return this.requireId(r),this.request({method:"GET",path:eT(eY,r),queryParams:{includeMembersCount:t}})}async updateOrganization(e,t){return this.requireId(e),this.request({method:"PATCH",path:eT(eY,e),bodyParams:t})}async updateOrganizationLogo(e,t){this.requireId(e);let r=new U.FormData;return r.append("file",t?.file),t?.uploaderUserId&&r.append("uploader_user_id",t?.uploaderUserId),this.request({method:"PUT",path:eT(eY,e,"logo"),formData:r})}async deleteOrganizationLogo(e){return this.requireId(e),this.request({method:"DELETE",path:eT(eY,e,"logo")})}async updateOrganizationMetadata(e,t){return this.requireId(e),this.request({method:"PATCH",path:eT(eY,e,"metadata"),bodyParams:t})}async deleteOrganization(e){return this.request({method:"DELETE",path:eT(eY,e)})}async getOrganizationMembershipList(e){let{organizationId:t,...r}=e;return this.requireId(t),this.request({method:"GET",path:eT(eY,t,"memberships"),queryParams:r})}async createOrganizationMembership(e){let{organizationId:t,...r}=e;return this.requireId(t),this.request({method:"POST",path:eT(eY,t,"memberships"),bodyParams:r})}async updateOrganizationMembership(e){let{organizationId:t,userId:r,...i}=e;return this.requireId(t),this.request({method:"PATCH",path:eT(eY,t,"memberships",r),bodyParams:i})}async updateOrganizationMembershipMetadata(e){let{organizationId:t,userId:r,...i}=e;return this.request({method:"PATCH",path:eT(eY,t,"memberships",r,"metadata"),bodyParams:i})}async deleteOrganizationMembership(e){let{organizationId:t,userId:r}=e;return this.requireId(t),this.request({method:"DELETE",path:eT(eY,t,"memberships",r)})}async getOrganizationInvitationList(e){let{organizationId:t,...r}=e;return this.requireId(t),this.request({method:"GET",path:eT(eY,t,"invitations"),queryParams:r})}async createOrganizationInvitation(e){let{organizationId:t,...r}=e;return this.requireId(t),this.request({method:"POST",path:eT(eY,t,"invitations"),bodyParams:r})}async createOrganizationInvitationBulk(e,t){return this.requireId(e),this.request({method:"POST",path:eT(eY,e,"invitations","bulk"),bodyParams:t})}async getOrganizationInvitation(e){let{organizationId:t,invitationId:r}=e;return this.requireId(t),this.requireId(r),this.request({method:"GET",path:eT(eY,t,"invitations",r)})}async revokeOrganizationInvitation(e){let{organizationId:t,invitationId:r,...i}=e;return this.requireId(t),this.request({method:"POST",path:eT(eY,t,"invitations",r,"revoke"),bodyParams:i})}async getOrganizationDomainList(e){let{organizationId:t,...r}=e;return this.requireId(t),this.request({method:"GET",path:eT(eY,t,"domains"),queryParams:r})}async createOrganizationDomain(e){let{organizationId:t,...r}=e;return this.requireId(t),this.request({method:"POST",path:eT(eY,t,"domains"),bodyParams:{...r,verified:r.verified??!0}})}async updateOrganizationDomain(e){let{organizationId:t,domainId:r,...i}=e;return this.requireId(t),this.requireId(r),this.request({method:"PATCH",path:eT(eY,t,"domains",r),bodyParams:i})}async deleteOrganizationDomain(e){let{organizationId:t,domainId:r}=e;return this.requireId(t),this.requireId(r),this.request({method:"DELETE",path:eT(eY,t,"domains",r)})}},eX="/oauth_applications",eZ=class extends eP{async list(e={}){return this.request({method:"GET",path:eX,queryParams:e})}async get(e){return this.requireId(e),this.request({method:"GET",path:eT(eX,e)})}async create(e){return this.request({method:"POST",path:eX,bodyParams:e})}async update(e){let{oauthApplicationId:t,...r}=e;return this.requireId(t),this.request({method:"PATCH",path:eT(eX,t),bodyParams:r})}async delete(e){return this.requireId(e),this.request({method:"DELETE",path:eT(eX,e)})}async rotateSecret(e){return this.requireId(e),this.request({method:"POST",path:eT(eX,e,"rotate_secret")})}},e0="/phone_numbers",e1=class extends eP{async getPhoneNumber(e){return this.requireId(e),this.request({method:"GET",path:eT(e0,e)})}async createPhoneNumber(e){return this.request({method:"POST",path:e0,bodyParams:e})}async updatePhoneNumber(e,t={}){return this.requireId(e),this.request({method:"PATCH",path:eT(e0,e),bodyParams:t})}async deletePhoneNumber(e){return this.requireId(e),this.request({method:"DELETE",path:eT(e0,e)})}},e2=class extends eP{async verify(e){return this.request({method:"POST",path:"/proxy_checks",bodyParams:e})}},e4="/redirect_urls",e5=class extends eP{async getRedirectUrlList(){return this.request({method:"GET",path:e4,queryParams:{paginated:!0}})}async getRedirectUrl(e){return this.requireId(e),this.request({method:"GET",path:eT(e4,e)})}async createRedirectUrl(e){return this.request({method:"POST",path:e4,bodyParams:e})}async deleteRedirectUrl(e){return this.requireId(e),this.request({method:"DELETE",path:eT(e4,e)})}},e6="/saml_connections",e8=class extends eP{async getSamlConnectionList(e={}){return this.request({method:"GET",path:e6,queryParams:e})}async createSamlConnection(e){return this.request({method:"POST",path:e6,bodyParams:e})}async getSamlConnection(e){return this.requireId(e),this.request({method:"GET",path:eT(e6,e)})}async updateSamlConnection(e,t={}){return this.requireId(e),this.request({method:"PATCH",path:eT(e6,e),bodyParams:t})}async deleteSamlConnection(e){return this.requireId(e),this.request({method:"DELETE",path:eT(e6,e)})}},e3="/sessions",e9=class extends eP{async getSessionList(e={}){return this.request({method:"GET",path:e3,queryParams:{...e,paginated:!0}})}async getSession(e){return this.requireId(e),this.request({method:"GET",path:eT(e3,e)})}async createSession(e){return this.request({method:"POST",path:e3,bodyParams:e})}async revokeSession(e){return this.requireId(e),this.request({method:"POST",path:eT(e3,e,"revoke")})}async verifySession(e,t){return this.requireId(e),this.request({method:"POST",path:eT(e3,e,"verify"),bodyParams:{token:t}})}async getToken(e,t){return this.requireId(e),this.request({method:"POST",path:eT(e3,e,"tokens",t||"")})}async refreshSession(e,t){this.requireId(e);let{suffixed_cookies:r,...i}=t;return this.request({method:"POST",path:eT(e3,e,"refresh"),bodyParams:i,queryParams:{suffixed_cookies:r}})}},e7="/sign_in_tokens",te=class extends eP{async createSignInToken(e){return this.request({method:"POST",path:e7,bodyParams:e})}async revokeSignInToken(e){return this.requireId(e),this.request({method:"POST",path:eT(e7,e,"revoke")})}},tt="/sign_ups",tr=class extends eP{async get(e){return this.requireId(e),this.request({method:"GET",path:eT(tt,e)})}async update(e){let{signUpAttemptId:t,...r}=e;return this.request({method:"PATCH",path:eT(tt,t),bodyParams:r})}},ti=class extends eP{async createTestingToken(){return this.request({method:"POST",path:"/testing_tokens"})}},ts="/users",ta=class extends eP{async getUserList(e={}){let{limit:t,offset:r,orderBy:i,...s}=e,[a,n]=await Promise.all([this.request({method:"GET",path:ts,queryParams:e}),this.getCount(s)]);return{data:a,totalCount:n}}async getUser(e){return this.requireId(e),this.request({method:"GET",path:eT(ts,e)})}async createUser(e){return this.request({method:"POST",path:ts,bodyParams:e})}async updateUser(e,t={}){return this.requireId(e),this.request({method:"PATCH",path:eT(ts,e),bodyParams:t})}async updateUserProfileImage(e,t){this.requireId(e);let r=new U.FormData;return r.append("file",t?.file),this.request({method:"POST",path:eT(ts,e,"profile_image"),formData:r})}async updateUserMetadata(e,t){return this.requireId(e),this.request({method:"PATCH",path:eT(ts,e,"metadata"),bodyParams:t})}async deleteUser(e){return this.requireId(e),this.request({method:"DELETE",path:eT(ts,e)})}async getCount(e={}){return this.request({method:"GET",path:eT(ts,"count"),queryParams:e})}async getUserOauthAccessToken(e,t){this.requireId(e);let r=t.startsWith("oauth_"),i=r?t:`oauth_${t}`;return r&&S("getUserOauthAccessToken(userId, provider)","Remove the `oauth_` prefix from the `provider` argument."),this.request({method:"GET",path:eT(ts,e,"oauth_access_tokens",i),queryParams:{paginated:!0}})}async disableUserMFA(e){return this.requireId(e),this.request({method:"DELETE",path:eT(ts,e,"mfa")})}async getOrganizationMembershipList(e){let{userId:t,limit:r,offset:i}=e;return this.requireId(t),this.request({method:"GET",path:eT(ts,t,"organization_memberships"),queryParams:{limit:r,offset:i}})}async getOrganizationInvitationList(e){let{userId:t,...r}=e;return this.requireId(t),this.request({method:"GET",path:eT(ts,t,"organization_invitations"),queryParams:r})}async verifyPassword(e){let{userId:t,password:r}=e;return this.requireId(t),this.request({method:"POST",path:eT(ts,t,"verify_password"),bodyParams:{password:r}})}async verifyTOTP(e){let{userId:t,code:r}=e;return this.requireId(t),this.request({method:"POST",path:eT(ts,t,"verify_totp"),bodyParams:{code:r}})}async banUser(e){return this.requireId(e),this.request({method:"POST",path:eT(ts,e,"ban")})}async unbanUser(e){return this.requireId(e),this.request({method:"POST",path:eT(ts,e,"unban")})}async lockUser(e){return this.requireId(e),this.request({method:"POST",path:eT(ts,e,"lock")})}async unlockUser(e){return this.requireId(e),this.request({method:"POST",path:eT(ts,e,"unlock")})}async deleteUserProfileImage(e){return this.requireId(e),this.request({method:"DELETE",path:eT(ts,e,"profile_image")})}async deleteUserPasskey(e){return this.requireId(e.userId),this.requireId(e.passkeyIdentificationId),this.request({method:"DELETE",path:eT(ts,e.userId,"passkeys",e.passkeyIdentificationId)})}async deleteUserWeb3Wallet(e){return this.requireId(e.userId),this.requireId(e.web3WalletIdentificationId),this.request({method:"DELETE",path:eT(ts,e.userId,"web3_wallets",e.web3WalletIdentificationId)})}async deleteUserExternalAccount(e){return this.requireId(e.userId),this.requireId(e.externalAccountId),this.request({method:"DELETE",path:eT(ts,e.userId,"external_accounts",e.externalAccountId)})}async deleteUserBackupCodes(e){return this.requireId(e),this.request({method:"DELETE",path:eT(ts,e,"backup_code")})}async deleteUserTOTP(e){return this.requireId(e),this.request({method:"DELETE",path:eT(ts,e,"totp")})}},tn="/waitlist_entries",to=class extends eP{async list(e={}){return this.request({method:"GET",path:tn,queryParams:e})}async create(e){return this.request({method:"POST",path:tn,bodyParams:e})}},tl="/webhooks",tc=class extends eP{async createSvixApp(){return this.request({method:"POST",path:eT(tl,"svix")})}async generateSvixAuthURL(){return this.request({method:"POST",path:eT(tl,"svix_url")})}async deleteSvixApp(){return this.request({method:"DELETE",path:eT(tl,"svix")})}};function tu(e){if(!e||"string"!=typeof e)throw Error("Missing Clerk Secret Key. Go to https://dashboard.clerk.com and get your key for your instance.")}var td=class e{constructor(e,t,r,i,s,a,n,o){this.id=e,this.status=t,this.userId=r,this.actor=i,this.token=s,this.url=a,this.createdAt=n,this.updatedAt=o}static fromJSON(t){return new e(t.id,t.status,t.user_id,t.actor,t.token,t.url,t.created_at,t.updated_at)}},th=class e{constructor(e,t,r,i){this.publishableKey=e,this.secretKey=t,this.claimUrl=r,this.apiKeysUrl=i}static fromJSON(t){return new e(t.publishable_key,t.secret_key,t.claim_url,t.api_keys_url)}},tp=class e{constructor(e,t,r,i,s,a,n){this.id=e,this.identifier=t,this.identifierType=r,this.createdAt=i,this.updatedAt=s,this.instanceId=a,this.invitationId=n}static fromJSON(t){return new e(t.id,t.identifier,t.identifier_type,t.created_at,t.updated_at,t.instance_id,t.invitation_id)}},tf=class e{constructor(e,t,r,i,s,a){this.id=e,this.identifier=t,this.identifierType=r,this.createdAt=i,this.updatedAt=s,this.instanceId=a}static fromJSON(t){return new e(t.id,t.identifier,t.identifier_type,t.created_at,t.updated_at,t.instance_id)}},tg=class e{constructor(e,t,r,i,s,a,n,o){this.id=e,this.isMobile=t,this.ipAddress=r,this.city=i,this.country=s,this.browserVersion=a,this.browserName=n,this.deviceType=o}static fromJSON(t){return new e(t.id,t.is_mobile,t.ip_address,t.city,t.country,t.browser_version,t.browser_name,t.device_type)}},ty=class e{constructor(e,t,r,i,s,a,n,o,l,c,u,d=null){this.id=e,this.clientId=t,this.userId=r,this.status=i,this.lastActiveAt=s,this.expireAt=a,this.abandonAt=n,this.createdAt=o,this.updatedAt=l,this.lastActiveOrganizationId=c,this.latestActivity=u,this.actor=d}static fromJSON(t){return new e(t.id,t.client_id,t.user_id,t.status,t.last_active_at,t.expire_at,t.abandon_at,t.created_at,t.updated_at,t.last_active_organization_id,t.latest_activity&&tg.fromJSON(t.latest_activity),t.actor)}},tm=class e{constructor(e,t,r,i,s,a,n,o){this.id=e,this.sessionIds=t,this.sessions=r,this.signInId=i,this.signUpId=s,this.lastActiveSessionId=a,this.createdAt=n,this.updatedAt=o}static fromJSON(t){return new e(t.id,t.session_ids,t.sessions.map(e=>ty.fromJSON(e)),t.sign_in_id,t.sign_up_id,t.last_active_session_id,t.created_at,t.updated_at)}},tw=class e{constructor(e,t,r){this.host=e,this.value=t,this.required=r}static fromJSON(t){return new e(t.host,t.value,t.required)}},tb=class e{constructor(e){this.cookies=e}static fromJSON(t){return new e(t.cookies)}},t_=class e{constructor(e,t,r,i){this.object=e,this.id=t,this.slug=r,this.deleted=i}static fromJSON(t){return new e(t.object,t.id||null,t.slug||null,t.deleted)}},tv=class e{constructor(e,t,r,i,s,a,n,o){this.id=e,this.name=t,this.isSatellite=r,this.frontendApiUrl=i,this.developmentOrigin=s,this.cnameTargets=a,this.accountsPortalUrl=n,this.proxyUrl=o}static fromJSON(t){return new e(t.id,t.name,t.is_satellite,t.frontend_api_url,t.development_origin,t.cname_targets&&t.cname_targets.map(e=>tw.fromJSON(e)),t.accounts_portal_url,t.proxy_url)}},tS=class e{constructor(e,t,r,i,s,a,n,o,l,c,u){this.id=e,this.fromEmailName=t,this.emailAddressId=r,this.toEmailAddress=i,this.subject=s,this.body=a,this.bodyPlain=n,this.status=o,this.slug=l,this.data=c,this.deliveredByClerk=u}static fromJSON(t){return new e(t.id,t.from_email_name,t.email_address_id,t.to_email_address,t.subject,t.body,t.body_plain,t.status,t.slug,t.data,t.delivered_by_clerk)}},tk=class e{constructor(e,t){this.id=e,this.type=t}static fromJSON(t){return new e(t.id,t.type)}},tA=class e{constructor(e,t,r=null,i=null,s=null,a=null,n=null){this.status=e,this.strategy=t,this.externalVerificationRedirectURL=r,this.attempts=i,this.expireAt=s,this.nonce=a,this.message=n}static fromJSON(t){return new e(t.status,t.strategy,t.external_verification_redirect_url?new URL(t.external_verification_redirect_url):null,t.attempts,t.expire_at,t.nonce)}},tE=class e{constructor(e,t,r,i){this.id=e,this.emailAddress=t,this.verification=r,this.linkedTo=i}static fromJSON(t){return new e(t.id,t.email_address,t.verification&&tA.fromJSON(t.verification),t.linked_to.map(e=>tk.fromJSON(e)))}},tT=class e{constructor(e,t,r,i,s,a,n,o,l,c,u,d={},h,p){this.id=e,this.provider=t,this.identificationId=r,this.externalId=i,this.approvedScopes=s,this.emailAddress=a,this.firstName=n,this.lastName=o,this.imageUrl=l,this.username=c,this.phoneNumber=u,this.publicMetadata=d,this.label=h,this.verification=p}static fromJSON(t){return new e(t.id,t.provider,t.identification_id,t.provider_user_id,t.approved_scopes,t.email_address,t.first_name,t.last_name,t.image_url||"",t.username,t.phone_number,t.public_metadata,t.label,t.verification&&tA.fromJSON(t.verification))}},tP=class e{constructor(e,t,r){this.id=e,this.environmentType=t,this.allowedOrigins=r}static fromJSON(t){return new e(t.id,t.environment_type,t.allowed_origins)}},tx=class e{constructor(e,t,r,i,s){this.allowlist=e,this.blocklist=t,this.blockEmailSubaddresses=r,this.blockDisposableEmailDomains=i,this.ignoreDotsForGmailAddresses=s}static fromJSON(t){return new e(t.allowlist,t.blocklist,t.block_email_subaddresses,t.block_disposable_email_domains,t.ignore_dots_for_gmail_addresses)}},tI=class e{constructor(e,t,r,i,s){this.id=e,this.restrictedToAllowlist=t,this.fromEmailAddress=r,this.progressiveSignUp=i,this.enhancedEmailDeliverability=s}static fromJSON(t){return new e(t.id,t.restricted_to_allowlist,t.from_email_address,t.progressive_sign_up,t.enhanced_email_deliverability)}},tC=class e{constructor(e,t,r,i,s,a,n,o){this.id=e,this.emailAddress=t,this.publicMetadata=r,this.createdAt=i,this.updatedAt=s,this.status=a,this.url=n,this.revoked=o,this._raw=null}get raw(){return this._raw}static fromJSON(t){let r=new e(t.id,t.email_address,t.public_metadata,t.created_at,t.updated_at,t.status,t.url,t.revoked);return r._raw=t,r}},tO={AccountlessApplication:"accountless_application",ActorToken:"actor_token",AllowlistIdentifier:"allowlist_identifier",BlocklistIdentifier:"blocklist_identifier",Client:"client",Cookies:"cookies",Domain:"domain",Email:"email",EmailAddress:"email_address",Instance:"instance",InstanceRestrictions:"instance_restrictions",InstanceSettings:"instance_settings",Invitation:"invitation",JwtTemplate:"jwt_template",OauthAccessToken:"oauth_access_token",OAuthApplication:"oauth_application",Organization:"organization",OrganizationInvitation:"organization_invitation",OrganizationMembership:"organization_membership",OrganizationSettings:"organization_settings",PhoneNumber:"phone_number",ProxyCheck:"proxy_check",RedirectUrl:"redirect_url",Session:"session",SignInToken:"sign_in_token",SignUpAttempt:"sign_up_attempt",SmsMessage:"sms_message",User:"user",WaitlistEntry:"waitlist_entry",Token:"token",TotalCount:"total_count"},tR=class e{constructor(e,t,r,i,s,a,n,o,l){this.id=e,this.name=t,this.claims=r,this.lifetime=i,this.allowedClockSkew=s,this.customSigningKey=a,this.signingAlgorithm=n,this.createdAt=o,this.updatedAt=l}static fromJSON(t){return new e(t.id,t.name,t.claims,t.lifetime,t.allowed_clock_skew,t.custom_signing_key,t.signing_algorithm,t.created_at,t.updated_at)}},tF=class e{constructor(e,t,r,i={},s,a,n,o){this.externalAccountId=e,this.provider=t,this.token=r,this.publicMetadata=i,this.label=s,this.scopes=a,this.tokenSecret=n,this.expiresAt=o}static fromJSON(t){return new e(t.external_account_id,t.provider,t.token,t.public_metadata,t.label||"",t.scopes,t.token_secret,t.expires_at)}},tU=class e{constructor(e,t,r,i,s,a,n,o,l,c,u,d,h,p,f){this.id=e,this.instanceId=t,this.name=r,this.clientId=i,this.isPublic=s,this.scopes=a,this.redirectUris=n,this.authorizeUrl=o,this.tokenFetchUrl=l,this.userInfoUrl=c,this.discoveryUrl=u,this.tokenIntrospectionUrl=d,this.createdAt=h,this.updatedAt=p,this.clientSecret=f}static fromJSON(t){return new e(t.id,t.instance_id,t.name,t.client_id,t.public,t.scopes,t.redirect_uris,t.authorize_url,t.token_fetch_url,t.user_info_url,t.discovery_url,t.token_introspection_url,t.created_at,t.updated_at,t.client_secret)}},tH=class e{constructor(e,t,r,i,s,a,n,o={},l={},c,u,d,h){this.id=e,this.name=t,this.slug=r,this.imageUrl=i,this.hasImage=s,this.createdAt=a,this.updatedAt=n,this.publicMetadata=o,this.privateMetadata=l,this.maxAllowedMemberships=c,this.adminDeleteEnabled=u,this.membersCount=d,this.createdBy=h,this._raw=null}get raw(){return this._raw}static fromJSON(t){let r=new e(t.id,t.name,t.slug,t.image_url||"",t.has_image,t.created_at,t.updated_at,t.public_metadata,t.private_metadata,t.max_allowed_memberships,t.admin_delete_enabled,t.members_count,t.created_by);return r._raw=t,r}},t$=class e{constructor(e,t,r,i,s,a,n,o,l,c,u={},d={},h){this.id=e,this.emailAddress=t,this.role=r,this.roleName=i,this.organizationId=s,this.createdAt=a,this.updatedAt=n,this.expiresAt=o,this.url=l,this.status=c,this.publicMetadata=u,this.privateMetadata=d,this.publicOrganizationData=h,this._raw=null}get raw(){return this._raw}static fromJSON(t){let r=new e(t.id,t.email_address,t.role,t.role_name,t.organization_id,t.created_at,t.updated_at,t.expires_at,t.url,t.status,t.public_metadata,t.private_metadata,t.public_organization_data);return r._raw=t,r}},tN=class e{constructor(e,t,r,i={},s={},a,n,o,l){this.id=e,this.role=t,this.permissions=r,this.publicMetadata=i,this.privateMetadata=s,this.createdAt=a,this.updatedAt=n,this.organization=o,this.publicUserData=l,this._raw=null}get raw(){return this._raw}static fromJSON(t){let r=new e(t.id,t.role,t.permissions,t.public_metadata,t.private_metadata,t.created_at,t.updated_at,tH.fromJSON(t.organization),tJ.fromJSON(t.public_user_data));return r._raw=t,r}},tJ=class e{constructor(e,t,r,i,s,a){this.identifier=e,this.firstName=t,this.lastName=r,this.imageUrl=i,this.hasImage=s,this.userId=a}static fromJSON(t){return new e(t.identifier,t.first_name,t.last_name,t.image_url,t.has_image,t.user_id)}},tM=class e{constructor(e,t,r,i,s,a,n,o,l){this.enabled=e,this.maxAllowedMemberships=t,this.maxAllowedRoles=r,this.maxAllowedPermissions=i,this.creatorRole=s,this.adminDeleteEnabled=a,this.domainsEnabled=n,this.domainsEnrollmentModes=o,this.domainsDefaultRole=l}static fromJSON(t){return new e(t.enabled,t.max_allowed_memberships,t.max_allowed_roles,t.max_allowed_permissions,t.creator_role,t.admin_delete_enabled,t.domains_enabled,t.domains_enrollment_modes,t.domains_default_role)}},tq=class e{constructor(e,t,r,i,s,a){this.id=e,this.phoneNumber=t,this.reservedForSecondFactor=r,this.defaultSecondFactor=i,this.verification=s,this.linkedTo=a}static fromJSON(t){return new e(t.id,t.phone_number,t.reserved_for_second_factor,t.default_second_factor,t.verification&&tA.fromJSON(t.verification),t.linked_to.map(e=>tk.fromJSON(e)))}},tD=class e{constructor(e,t,r,i,s,a,n){this.id=e,this.domainId=t,this.lastRunAt=r,this.proxyUrl=i,this.successful=s,this.createdAt=a,this.updatedAt=n}static fromJSON(t){return new e(t.id,t.domain_id,t.last_run_at,t.proxy_url,t.successful,t.created_at,t.updated_at)}},tj=class e{constructor(e,t,r,i){this.id=e,this.url=t,this.createdAt=r,this.updatedAt=i}static fromJSON(t){return new e(t.id,t.url,t.created_at,t.updated_at)}},tL=class e{constructor(e,t,r,i,s,a,n){this.id=e,this.userId=t,this.token=r,this.status=i,this.url=s,this.createdAt=a,this.updatedAt=n}static fromJSON(t){return new e(t.id,t.user_id,t.token,t.status,t.url,t.created_at,t.updated_at)}},tK=class e{constructor(e,t){this.nextAction=e,this.supportedStrategies=t}static fromJSON(t){return new e(t.next_action,t.supported_strategies)}},tW=class e{constructor(e,t,r,i){this.emailAddress=e,this.phoneNumber=t,this.web3Wallet=r,this.externalAccount=i}static fromJSON(t){return new e(t.email_address&&tK.fromJSON(t.email_address),t.phone_number&&tK.fromJSON(t.phone_number),t.web3_wallet&&tK.fromJSON(t.web3_wallet),t.external_account)}},tz=class e{constructor(e,t,r,i,s,a,n,o,l,c,u,d,h,p,f,g,y,m,w,b,_,v){this.id=e,this.status=t,this.requiredFields=r,this.optionalFields=i,this.missingFields=s,this.unverifiedFields=a,this.verifications=n,this.username=o,this.emailAddress=l,this.phoneNumber=c,this.web3Wallet=u,this.passwordEnabled=d,this.firstName=h,this.lastName=p,this.customAction=f,this.externalId=g,this.createdSessionId=y,this.createdUserId=m,this.abandonAt=w,this.legalAcceptedAt=b,this.publicMetadata=_,this.unsafeMetadata=v}static fromJSON(t){return new e(t.id,t.status,t.required_fields,t.optional_fields,t.missing_fields,t.unverified_fields,t.verifications?tW.fromJSON(t.verifications):null,t.username,t.email_address,t.phone_number,t.web3_wallet,t.password_enabled,t.first_name,t.last_name,t.custom_action,t.external_id,t.created_session_id,t.created_user_id,t.abandon_at,t.legal_accepted_at,t.public_metadata,t.unsafe_metadata)}},tB=class e{constructor(e,t,r,i,s,a,n){this.id=e,this.fromPhoneNumber=t,this.toPhoneNumber=r,this.message=i,this.status=s,this.phoneNumberId=a,this.data=n}static fromJSON(t){return new e(t.id,t.from_phone_number,t.to_phone_number,t.message,t.status,t.phone_number_id,t.data)}},tV=class e{constructor(e){this.jwt=e}static fromJSON(t){return new e(t.jwt)}},tG=class e{constructor(e,t,r,i,s,a,n,o,l,c){this.id=e,this.name=t,this.domain=r,this.active=i,this.provider=s,this.syncUserAttributes=a,this.allowSubdomains=n,this.allowIdpInitiated=o,this.createdAt=l,this.updatedAt=c}static fromJSON(t){return new e(t.id,t.name,t.domain,t.active,t.provider,t.sync_user_attributes,t.allow_subdomains,t.allow_idp_initiated,t.created_at,t.updated_at)}},tY=class e{constructor(e,t,r,i,s,a,n,o,l){this.id=e,this.provider=t,this.providerUserId=r,this.active=i,this.emailAddress=s,this.firstName=a,this.lastName=n,this.verification=o,this.samlConnection=l}static fromJSON(t){return new e(t.id,t.provider,t.provider_user_id,t.active,t.email_address,t.first_name,t.last_name,t.verification&&tA.fromJSON(t.verification),t.saml_connection&&tG.fromJSON(t.saml_connection))}},tQ=class e{constructor(e,t,r){this.id=e,this.web3Wallet=t,this.verification=r}static fromJSON(t){return new e(t.id,t.web3_wallet,t.verification&&tA.fromJSON(t.verification))}},tX=class e{constructor(e,t,r,i,s,a,n,o,l,c,u,d,h,p,f,g,y,m,w,b={},_={},v={},S=[],k=[],A=[],E=[],T=[],P,x,I=null,C,O){this.id=e,this.passwordEnabled=t,this.totpEnabled=r,this.backupCodeEnabled=i,this.twoFactorEnabled=s,this.banned=a,this.locked=n,this.createdAt=o,this.updatedAt=l,this.imageUrl=c,this.hasImage=u,this.primaryEmailAddressId=d,this.primaryPhoneNumberId=h,this.primaryWeb3WalletId=p,this.lastSignInAt=f,this.externalId=g,this.username=y,this.firstName=m,this.lastName=w,this.publicMetadata=b,this.privateMetadata=_,this.unsafeMetadata=v,this.emailAddresses=S,this.phoneNumbers=k,this.web3Wallets=A,this.externalAccounts=E,this.samlAccounts=T,this.lastActiveAt=P,this.createOrganizationEnabled=x,this.createOrganizationsLimit=I,this.deleteSelfEnabled=C,this.legalAcceptedAt=O,this._raw=null}get raw(){return this._raw}static fromJSON(t){let r=new e(t.id,t.password_enabled,t.totp_enabled,t.backup_code_enabled,t.two_factor_enabled,t.banned,t.locked,t.created_at,t.updated_at,t.image_url,t.has_image,t.primary_email_address_id,t.primary_phone_number_id,t.primary_web3_wallet_id,t.last_sign_in_at,t.external_id,t.username,t.first_name,t.last_name,t.public_metadata,t.private_metadata,t.unsafe_metadata,(t.email_addresses||[]).map(e=>tE.fromJSON(e)),(t.phone_numbers||[]).map(e=>tq.fromJSON(e)),(t.web3_wallets||[]).map(e=>tQ.fromJSON(e)),(t.external_accounts||[]).map(e=>tT.fromJSON(e)),(t.saml_accounts||[]).map(e=>tY.fromJSON(e)),t.last_active_at,t.create_organization_enabled,t.create_organizations_limit,t.delete_self_enabled,t.legal_accepted_at);return r._raw=t,r}get primaryEmailAddress(){return this.emailAddresses.find(({id:e})=>e===this.primaryEmailAddressId)??null}get primaryPhoneNumber(){return this.phoneNumbers.find(({id:e})=>e===this.primaryPhoneNumberId)??null}get primaryWeb3Wallet(){return this.web3Wallets.find(({id:e})=>e===this.primaryWeb3WalletId)??null}get fullName(){return[this.firstName,this.lastName].join(" ").trim()||null}},tZ=class e{constructor(e,t,r,i,s,a,n){this.id=e,this.emailAddress=t,this.status=r,this.invitation=i,this.createdAt=s,this.updatedAt=a,this.isLocked=n}static fromJSON(t){return new e(t.id,t.email_address,t.status,t.invitation&&tC.fromJSON(t.invitation),t.created_at,t.updated_at,t.is_locked)}};function t0(e){if("string"!=typeof e&&"object"in e&&"deleted"in e)return t_.fromJSON(e);switch(e.object){case tO.AccountlessApplication:return th.fromJSON(e);case tO.ActorToken:return td.fromJSON(e);case tO.AllowlistIdentifier:return tp.fromJSON(e);case tO.BlocklistIdentifier:return tf.fromJSON(e);case tO.Client:return tm.fromJSON(e);case tO.Cookies:return tb.fromJSON(e);case tO.Domain:return tv.fromJSON(e);case tO.EmailAddress:return tE.fromJSON(e);case tO.Email:return tS.fromJSON(e);case tO.Instance:return tP.fromJSON(e);case tO.InstanceRestrictions:return tx.fromJSON(e);case tO.InstanceSettings:return tI.fromJSON(e);case tO.Invitation:return tC.fromJSON(e);case tO.JwtTemplate:return tR.fromJSON(e);case tO.OauthAccessToken:return tF.fromJSON(e);case tO.OAuthApplication:return tU.fromJSON(e);case tO.Organization:return tH.fromJSON(e);case tO.OrganizationInvitation:return t$.fromJSON(e);case tO.OrganizationMembership:return tN.fromJSON(e);case tO.OrganizationSettings:return tM.fromJSON(e);case tO.PhoneNumber:return tq.fromJSON(e);case tO.ProxyCheck:return tD.fromJSON(e);case tO.RedirectUrl:return tj.fromJSON(e);case tO.SignInToken:return tL.fromJSON(e);case tO.SignUpAttempt:return tz.fromJSON(e);case tO.Session:return ty.fromJSON(e);case tO.SmsMessage:return tB.fromJSON(e);case tO.Token:return tV.fromJSON(e);case tO.TotalCount:return e.total_count;case tO.User:return tX.fromJSON(e);case tO.WaitlistEntry:return tZ.fromJSON(e);default:return e}}function t1(e){var t;return t=async t=>{let r,{secretKey:i,requireSecretKey:s=!0,apiUrl:a=eb,apiVersion:n="v1",userAgent:o=e_}=e,{path:l,method:c,queryParams:u,headerParams:d,bodyParams:h,formData:p}=t;s&&tu(i);let f=new URL(eT(a,n,l));if(u)for(let[e,t]of Object.entries(et({...u})))t&&[t].flat().forEach(t=>f.searchParams.append(e,t));let g={"Clerk-API-Version":ev,"User-Agent":o,...d};i&&(g.Authorization=`Bearer ${i}`);try{var y;p?r=await U.fetch(f.href,{method:c,headers:g,body:p}):(g["Content-Type"]="application/json",r=await U.fetch(f.href,{method:c,headers:g,...(()=>{if(!("GET"!==c&&h&&Object.keys(h).length>0))return null;let e=e=>et(e,{deep:!1});return{body:JSON.stringify(Array.isArray(h)?h.map(e):e(h))}})()}));let e=r?.headers&&r.headers?.get(eA.Headers.ContentType)===eA.ContentTypes.Json,t=await (e?r.json():r.text());if(!r.ok)return{data:null,errors:t5(t),status:r?.status,statusText:r?.statusText,clerkTraceId:t2(t,r?.headers),retryAfter:t4(r?.headers)};return{...Array.isArray(t)?{data:t.map(e=>t0(e))}:(y=t)&&"object"==typeof y&&"data"in y&&Array.isArray(y.data)&&void 0!==y.data?{data:t.data.map(e=>t0(e)),totalCount:t.total_count}:{data:t0(t)},errors:null}}catch(e){if(e instanceof Error)return{data:null,errors:[{code:"unexpected_error",message:e.message||"Unexpected error"}],clerkTraceId:t2(e,r?.headers)};return{data:null,errors:t5(e),status:r?.status,statusText:r?.statusText,clerkTraceId:t2(e,r?.headers),retryAfter:t4(r?.headers)}}},async(...e)=>{let{data:r,errors:i,totalCount:s,status:a,statusText:n,clerkTraceId:o,retryAfter:l}=await t(...e);if(i){let e=new A(n||"",{data:[],status:a,clerkTraceId:o,retryAfter:l});throw e.errors=i,e}return void 0!==s?{data:r,totalCount:s}:r}}function t2(e,t){return e&&"object"==typeof e&&"clerk_trace_id"in e&&"string"==typeof e.clerk_trace_id?e.clerk_trace_id:t?.get("cf-ray")||""}function t4(e){let t=e?.get("Retry-After");if(!t)return;let r=parseInt(t,10);if(!isNaN(r))return r}function t5(e){if(e&&"object"==typeof e&&"errors"in e){let t=e.errors;return t.length>0?t.map(k):[]}return[]}var t6=e=>()=>{let t={...e};return t.secretKey=(t.secretKey||"").substring(0,7),t.jwtKey=(t.jwtKey||"").substring(0,7),{...t}};function t8(e,t,r){let{actor:i,sessionId:s,sessionStatus:a,userId:n,orgId:o,orgRole:l,orgSlug:c,orgPermissions:u,factorVerificationAge:d}=em(r),h=function(e){let t=t1(e);return{__experimental_accountlessApplications:new eO(t1({...e,requireSecretKey:!1})),actorTokens:new eI(t),allowlistIdentifiers:new eF(t),betaFeatures:new eU(t),blocklistIdentifiers:new e$(t),clients:new eJ(t),domains:new eq(t),emailAddresses:new ej(t),instance:new eK(t),invitations:new ez(t),jwks:new eB(t),jwtTemplates:new eG(t),oauthApplications:new eZ(t),organizations:new eQ(t),phoneNumbers:new e1(t),proxyChecks:new e2(t),redirectUrls:new e5(t),samlConnections:new e8(t),sessions:new e9(t),signInTokens:new te(t),signUps:new tr(t),testingTokens:new ti(t),users:new ta(t),waitlistEntries:new to(t),webhooks:new tc(t)}}(e),p=t9({sessionId:s,sessionToken:t,fetcher:async(...e)=>(await h.sessions.getToken(...e)).jwt});return{actor:i,sessionClaims:r,sessionId:s,sessionStatus:a,userId:n,orgId:o,orgRole:l,orgSlug:c,orgPermissions:u,factorVerificationAge:d,getToken:p,has:eg({orgId:o,orgRole:l,orgPermissions:u,userId:n,factorVerificationAge:d,features:r.fea||"",plans:r.pla||""}),debug:t6({...e,sessionToken:t})}}function t3(e,t){return{sessionClaims:null,sessionId:null,sessionStatus:t??null,userId:null,actor:null,orgId:null,orgRole:null,orgSlug:null,orgPermissions:null,factorVerificationAge:null,getToken:()=>Promise.resolve(null),has:()=>!1,debug:t6(e)}}var t9=e=>{let{fetcher:t,sessionToken:r,sessionId:i}=e||{};return async(e={})=>i?e.template?t(i,e.template):r:null},t7={SignedIn:"signed-in",SignedOut:"signed-out",Handshake:"handshake"},re={ClientUATWithoutSessionToken:"client-uat-but-no-session-token",DevBrowserMissing:"dev-browser-missing",DevBrowserSync:"dev-browser-sync",PrimaryRespondsToSyncing:"primary-responds-to-syncing",SatelliteCookieNeedsSyncing:"satellite-needs-syncing",SessionTokenAndUATMissing:"session-token-and-uat-missing",SessionTokenMissing:"session-token-missing",SessionTokenExpired:"session-token-expired",SessionTokenIATBeforeClientUAT:"session-token-iat-before-client-uat",SessionTokenNBF:"session-token-nbf",SessionTokenIatInTheFuture:"session-token-iat-in-the-future",SessionTokenWithoutClientUAT:"session-token-but-no-client-uat",ActiveOrganizationMismatch:"active-organization-mismatch",UnexpectedError:"unexpected-error"};function rt(e,t,r=new Headers,i){let s=t8(e,i,t);return{status:t7.SignedIn,reason:null,message:null,proxyUrl:e.proxyUrl||"",publishableKey:e.publishableKey||"",isSatellite:e.isSatellite||!1,domain:e.domain||"",signInUrl:e.signInUrl||"",signUpUrl:e.signUpUrl||"",afterSignInUrl:e.afterSignInUrl||"",afterSignUpUrl:e.afterSignUpUrl||"",isSignedIn:!0,toAuth:({treatPendingAsSignedOut:e=!0}={})=>e&&"pending"===s.sessionStatus?t3(void 0,s.sessionStatus):s,headers:r,token:i}}function rr(e,t,r="",i=new Headers){return ri({status:t7.SignedOut,reason:t,message:r,proxyUrl:e.proxyUrl||"",publishableKey:e.publishableKey||"",isSatellite:e.isSatellite||!1,domain:e.domain||"",signInUrl:e.signInUrl||"",signUpUrl:e.signUpUrl||"",afterSignInUrl:e.afterSignInUrl||"",afterSignUpUrl:e.afterSignUpUrl||"",isSignedIn:!1,headers:i,toAuth:()=>t3({...e,status:t7.SignedOut,reason:t,message:r}),token:null})}var ri=e=>{let t=new Headers(e.headers||{});if(e.message)try{t.set(eA.Headers.AuthMessage,e.message)}catch{}if(e.reason)try{t.set(eA.Headers.AuthReason,e.reason)}catch{}if(e.status)try{t.set(eA.Headers.AuthStatus,e.status)}catch{}return e.headers=t,e},rs=class extends URL{isCrossOrigin(e){return this.origin!==new URL(e.toString()).origin}},ra=(...e)=>new rs(...e),rn=class extends Request{constructor(e,t){super("string"!=typeof e&&"url"in e?e.url:String(e),t||"string"==typeof e?void 0:e),this.clerkUrl=this.deriveUrlFromHeaders(this),this.cookies=this.parseCookies(this)}toJSON(){return{url:this.clerkUrl.href,method:this.method,headers:JSON.stringify(Object.fromEntries(this.headers)),clerkUrl:this.clerkUrl.toString(),cookies:JSON.stringify(Object.fromEntries(this.cookies))}}deriveUrlFromHeaders(e){let t=new URL(e.url),r=e.headers.get(eA.Headers.ForwardedProto),i=e.headers.get(eA.Headers.ForwardedHost),s=e.headers.get(eA.Headers.Host),a=t.protocol,n=this.getFirstValueFromHeader(i)??s,o=this.getFirstValueFromHeader(r)??a?.replace(/[:/]/,""),l=n&&o?`${o}://${n}`:t.origin;return l===t.origin?ra(t):ra(t.pathname+t.search,l)}getFirstValueFromHeader(e){return e?.split(",")[0]}parseCookies(e){return new Map(Object.entries((0,ew.qg)(this.decodeCookieValue(e.headers.get("cookie")||""))))}decodeCookieValue(e){return e?e.replace(/(%[0-9A-Z]{2})+/g,decodeURIComponent):e}},ro=(...e)=>e[0]instanceof rn?e[0]:new rn(...e),rl={},rc=0;function ru(e,t=!0){rl[e.kid]=e,rc=t?Date.now():-1}var rd="local";function rh(e){if(!rl[rd]){if(!e)throw new TokenVerificationError({action:TokenVerificationErrorAction.SetClerkJWTKey,message:"Missing local JWK.",reason:TokenVerificationErrorReason.LocalJWKMissing});ru({kid:"local",kty:"RSA",alg:"RS256",n:e.replace(/\r\n|\n|\r/g,"").replace("-----BEGIN PUBLIC KEY-----","").replace("-----END PUBLIC KEY-----","").replace("MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA","").replace("IDAQAB","").replace(/\+/g,"-").replace(/\//g,"_"),e:"AQAB"},!1)}return rl[rd]}async function rp({secretKey:e,apiUrl:t=eb,apiVersion:r="v1",kid:i,skipJwksCache:s}){if(s||function(){if(-1===rc)return!1;let e=Date.now()-rc>=0;return e&&(rl={}),e}()||!rl[i]){if(!e)throw new TokenVerificationError({action:TokenVerificationErrorAction.ContactSupport,message:"Failed to load JWKS from Clerk Backend or Frontend API.",reason:TokenVerificationErrorReason.RemoteJWKFailedToLoad});let{keys:i}=await retry(()=>rf(t,e,r));if(!i||!i.length)throw new TokenVerificationError({action:TokenVerificationErrorAction.ContactSupport,message:"The JWKS endpoint did not contain any signing keys. Contact <EMAIL>.",reason:TokenVerificationErrorReason.RemoteJWKFailedToLoad});i.forEach(e=>ru(e))}let a=rl[i];if(!a){let e=Object.values(rl).map(e=>e.kid).sort().join(", ");throw new TokenVerificationError({action:`Go to your Dashboard and validate your secret and public keys are correct. ${TokenVerificationErrorAction.ContactSupport} if the issue persists.`,message:`Unable to find a signing key in JWKS that matches the kid='${i}' of the provided session token. Please make sure that the __session cookie or the HTTP authorization header contain a Clerk-generated session JWT. The following kid is available: ${e}`,reason:TokenVerificationErrorReason.JWKKidMismatch})}return a}async function rf(e,t,r){if(!t)throw new TokenVerificationError({action:TokenVerificationErrorAction.SetClerkSecretKey,message:"Missing Clerk Secret Key or API Key. Go to https://dashboard.clerk.com and get your key for your instance.",reason:TokenVerificationErrorReason.RemoteJWKFailedToLoad});let i=new URL(e);i.pathname=eT(i.pathname,r,"/jwks");let s=await runtime.fetch(i.href,{headers:{Authorization:`Bearer ${t}`,"Clerk-API-Version":ev,"Content-Type":"application/json","User-Agent":e_}});if(!s.ok){let e=await s.json(),t=rg(e?.errors,TokenVerificationErrorCode.InvalidSecretKey);if(t){let e=TokenVerificationErrorReason.InvalidSecretKey;throw new TokenVerificationError({action:TokenVerificationErrorAction.ContactSupport,message:t.message,reason:e})}throw new TokenVerificationError({action:TokenVerificationErrorAction.ContactSupport,message:`Error loading Clerk JWKS from ${i.href} with code=${s.status}`,reason:TokenVerificationErrorReason.RemoteJWKFailedToLoad})}return s.json()}var rg=(e,t)=>e?e.find(e=>e.code===t):null;async function ry(e,t){let{data:r,errors:i}=decodeJwt(e);if(i)return{errors:i};let{header:s}=r,{kid:a}=s;try{let r;if(t.jwtKey)r=rh(t.jwtKey);else{if(!t.secretKey)return{errors:[new TokenVerificationError({action:TokenVerificationErrorAction.SetClerkJWTKey,message:"Failed to resolve JWK during verification.",reason:TokenVerificationErrorReason.JWKFailedToResolve})]};r=await rp({...t,kid:a})}return await verifyJwt(e,{...t,key:r})}catch(e){return{errors:[e]}}}var rm=class{constructor(e,t,r){this.cookieSuffix=e,this.clerkRequest=t,this.initPublishableKeyValues(r),this.initHeaderValues(),this.initCookieValues(),this.initHandshakeValues(),Object.assign(this,r),this.clerkUrl=this.clerkRequest.clerkUrl}get sessionToken(){return this.sessionTokenInCookie||this.sessionTokenInHeader}usesSuffixedCookies(){let e=this.getSuffixedCookie(eA.Cookies.ClientUat),t=this.getCookie(eA.Cookies.ClientUat),r=this.getSuffixedCookie(eA.Cookies.Session)||"",i=this.getCookie(eA.Cookies.Session)||"";if(i&&!this.tokenHasIssuer(i))return!1;if(i&&!this.tokenBelongsToInstance(i))return!0;if(!e&&!r)return!1;let{data:s}=decodeJwt(i),a=s?.payload.iat||0,{data:n}=decodeJwt(r),o=n?.payload.iat||0;if("0"!==e&&"0"!==t&&a>o||"0"===e&&"0"!==t)return!1;if("production"!==this.instanceType){let r=this.sessionExpired(n);if("0"!==e&&"0"===t&&r)return!1}return!!e||!r}initPublishableKeyValues(e){parsePublishableKey(e.publishableKey,{fatal:!0}),this.publishableKey=e.publishableKey;let t=parsePublishableKey(this.publishableKey,{fatal:!0,proxyUrl:e.proxyUrl,domain:e.domain,isSatellite:e.isSatellite});this.instanceType=t.instanceType,this.frontendApi=t.frontendApi}initHeaderValues(){this.sessionTokenInHeader=this.parseAuthorizationHeader(this.getHeader(eA.Headers.Authorization)),this.origin=this.getHeader(eA.Headers.Origin),this.host=this.getHeader(eA.Headers.Host),this.forwardedHost=this.getHeader(eA.Headers.ForwardedHost),this.forwardedProto=this.getHeader(eA.Headers.CloudFrontForwardedProto)||this.getHeader(eA.Headers.ForwardedProto),this.referrer=this.getHeader(eA.Headers.Referrer),this.userAgent=this.getHeader(eA.Headers.UserAgent),this.secFetchDest=this.getHeader(eA.Headers.SecFetchDest),this.accept=this.getHeader(eA.Headers.Accept)}initCookieValues(){this.sessionTokenInCookie=this.getSuffixedOrUnSuffixedCookie(eA.Cookies.Session),this.refreshTokenInCookie=this.getSuffixedCookie(eA.Cookies.Refresh),this.clientUat=Number.parseInt(this.getSuffixedOrUnSuffixedCookie(eA.Cookies.ClientUat)||"")||0}initHandshakeValues(){this.devBrowserToken=this.getQueryParam(eA.QueryParameters.DevBrowser)||this.getSuffixedOrUnSuffixedCookie(eA.Cookies.DevBrowser),this.handshakeToken=this.getQueryParam(eA.QueryParameters.Handshake)||this.getCookie(eA.Cookies.Handshake),this.handshakeRedirectLoopCounter=Number(this.getCookie(eA.Cookies.RedirectCount))||0,this.handshakeNonce=this.getQueryParam(eA.QueryParameters.HandshakeNonce)||this.getCookie(eA.Cookies.HandshakeNonce)}getQueryParam(e){return this.clerkRequest.clerkUrl.searchParams.get(e)}getHeader(e){return this.clerkRequest.headers.get(e)||void 0}getCookie(e){return this.clerkRequest.cookies.get(e)||void 0}getSuffixedCookie(e){return this.getCookie(getSuffixedCookieName(e,this.cookieSuffix))||void 0}getSuffixedOrUnSuffixedCookie(e){return this.usesSuffixedCookies()?this.getSuffixedCookie(e):this.getCookie(e)}parseAuthorizationHeader(e){if(!e)return;let[t,r]=e.split(" ",2);return r?"Bearer"===t?r:void 0:t}tokenHasIssuer(e){let{data:t,errors:r}=decodeJwt(e);return!r&&!!t.payload.iss}tokenBelongsToInstance(e){if(!e)return!1;let{data:t,errors:r}=decodeJwt(e);if(r)return!1;let i=t.payload.iss.replace(/https?:\/\//gi,"");return this.frontendApi===i}sessionExpired(e){return!!e&&e?.payload.exp<=(Date.now()/1e3|0)}},rw=async(e,t)=>new rm(t.publishableKey?await getCookieSuffix(t.publishableKey,runtime.crypto.subtle):"",e,t),rb=e=>e.split(";")[0]?.split("=")[0],r_=e=>e.split(";")[0]?.split("=")[1];async function rv(e,{key:t}){let{data:r,errors:i}=decodeJwt(e);if(i)throw i[0];let{header:s,payload:a}=r,{typ:n,alg:o}=s;assertHeaderType(n),assertHeaderAlgorithm(o);let{data:l,errors:c}=await hasValidSignature(r,t);if(c)throw new TokenVerificationError({reason:TokenVerificationErrorReason.TokenVerificationFailed,message:`Error verifying handshake token. ${c[0]}`});if(!l)throw new TokenVerificationError({reason:TokenVerificationErrorReason.TokenInvalidSignature,message:"Handshake signature is invalid."});return a}async function rS(e,t){let r,{secretKey:i,apiUrl:s,apiVersion:a,jwksCacheTtlInMs:n,jwtKey:o,skipJwksCache:l}=t,{data:c,errors:u}=decodeJwt(e);if(u)throw u[0];let{kid:d}=c.header;if(o)r=rh(o);else if(i)r=await rp({secretKey:i,apiUrl:s,apiVersion:a,kid:d,jwksCacheTtlInMs:n,skipJwksCache:l});else throw new TokenVerificationError({action:TokenVerificationErrorAction.SetClerkJWTKey,message:"Failed to resolve JWK during handshake verification.",reason:TokenVerificationErrorReason.JWKFailedToResolve});return await rv(e,{key:r})}var rk=class{constructor(e,t,r){this.authenticateContext=e,this.options=t,this.organizationMatcher=r}isRequestEligibleForHandshake(){let{accept:e,secFetchDest:t}=this.authenticateContext;return!!("document"===t||"iframe"===t||!t&&e?.startsWith("text/html"))}buildRedirectToHandshake(e){if(!this.authenticateContext?.clerkUrl)throw Error("Missing clerkUrl in authenticateContext");let t=this.removeDevBrowserFromURL(this.authenticateContext.clerkUrl),r=this.authenticateContext.frontendApi.replace(/http(s)?:\/\//,""),i=new URL(`https://${r}/v1/client/handshake`);i.searchParams.append("redirect_url",t?.href||""),i.searchParams.append("__clerk_api_version",ev),i.searchParams.append(eA.QueryParameters.SuffixedCookies,this.authenticateContext.usesSuffixedCookies().toString()),i.searchParams.append(eA.QueryParameters.HandshakeReason,e),"development"===this.authenticateContext.instanceType&&this.authenticateContext.devBrowserToken&&i.searchParams.append(eA.QueryParameters.DevBrowser,this.authenticateContext.devBrowserToken);let s=this.getOrganizationSyncTarget(this.authenticateContext.clerkUrl,this.organizationMatcher);return s&&this.getOrganizationSyncQueryParams(s).forEach((e,t)=>{i.searchParams.append(t,e)}),new Headers({[eA.Headers.Location]:i.href})}async getCookiesFromHandshake(){let e=[];if(this.authenticateContext.handshakeNonce)try{let t=await this.authenticateContext.apiClient?.clients.getHandshakePayload({nonce:this.authenticateContext.handshakeNonce});t&&e.push(...t.directives)}catch(e){console.error("Clerk: HandshakeService: error getting handshake payload:",e)}else if(this.authenticateContext.handshakeToken){let t=await rS(this.authenticateContext.handshakeToken,this.authenticateContext);t&&Array.isArray(t.handshake)&&e.push(...t.handshake)}return e}async resolveHandshake(){let e=new Headers({"Access-Control-Allow-Origin":"null","Access-Control-Allow-Credentials":"true"}),t=await this.getCookiesFromHandshake(),r="";if(t.forEach(t=>{e.append("Set-Cookie",t),rb(t).startsWith(eA.Cookies.Session)&&(r=r_(t))}),"development"===this.authenticateContext.instanceType){let t=new URL(this.authenticateContext.clerkUrl);t.searchParams.delete(eA.QueryParameters.Handshake),t.searchParams.delete(eA.QueryParameters.HandshakeHelp),e.append(eA.Headers.Location,t.toString()),e.set(eA.Headers.CacheControl,"no-store")}if(""===r)return rr(this.authenticateContext,re.SessionTokenMissing,"",e);let{data:i,errors:[s]=[]}=await ry(r,this.authenticateContext);if(i)return rt(this.authenticateContext,i,e,r);if("development"===this.authenticateContext.instanceType&&(s?.reason===TokenVerificationErrorReason.TokenExpired||s?.reason===TokenVerificationErrorReason.TokenNotActiveYet||s?.reason===TokenVerificationErrorReason.TokenIatInTheFuture)){let t=new TokenVerificationError({action:s.action,message:s.message,reason:s.reason});t.tokenCarrier="cookie",console.error(`Clerk: Clock skew detected. This usually means that your system clock is inaccurate. Clerk will attempt to account for the clock skew in development.

To resolve this issue, make sure your system's clock is set to the correct time (e.g. turn off and on automatic time synchronization).

---

${t.getFullMessage()}`);let{data:i,errors:[a]=[]}=await ry(r,{...this.authenticateContext,clockSkewInMs:864e5});if(i)return rt(this.authenticateContext,i,e,r);throw Error(a?.message||"Clerk: Handshake retry failed.")}throw Error(s?.message||"Clerk: Handshake failed.")}handleTokenVerificationErrorInDevelopment(e){if(e.reason===TokenVerificationErrorReason.TokenInvalidSignature)throw Error("Clerk: Handshake token verification failed due to an invalid signature. If you have switched Clerk keys locally, clear your cookies and try again.");throw Error(`Clerk: Handshake token verification failed: ${e.getFullMessage()}.`)}checkAndTrackRedirectLoop(e){if(3===this.authenticateContext.handshakeRedirectLoopCounter)return!0;let t=this.authenticateContext.handshakeRedirectLoopCounter+1,r=eA.Cookies.RedirectCount;return e.append("Set-Cookie",`${r}=${t}; SameSite=Lax; HttpOnly; Max-Age=3`),!1}removeDevBrowserFromURL(e){let t=new URL(e);return t.searchParams.delete(eA.QueryParameters.DevBrowser),t.searchParams.delete(eA.QueryParameters.LegacyDevBrowser),t}getOrganizationSyncTarget(e,t){return t.findTarget(e)}getOrganizationSyncQueryParams(e){let t=new Map;return"personalAccount"===e.type&&t.set("organization_id",""),"organization"===e.type&&(e.organizationId&&t.set("organization_id",e.organizationId),e.organizationSlug&&t.set("organization_id",e.organizationSlug)),t}},rA=class{constructor(e){this.organizationPattern=this.createMatcher(e?.organizationPatterns),this.personalAccountPattern=this.createMatcher(e?.personalAccountPatterns)}createMatcher(e){if(!e)return null;try{return match(e)}catch(t){throw Error(`Invalid pattern "${e}": ${t}`)}}findTarget(e){let t=this.findOrganizationTarget(e);return t||this.findPersonalAccountTarget(e)}findOrganizationTarget(e){if(!this.organizationPattern)return null;try{let t=this.organizationPattern(e.pathname);if(!t||!("params"in t))return null;let r=t.params;if(r.id)return{type:"organization",organizationId:r.id};if(r.slug)return{type:"organization",organizationSlug:r.slug};return null}catch(e){return console.error("Failed to match organization pattern:",e),null}}findPersonalAccountTarget(e){if(!this.personalAccountPattern)return null;try{return this.personalAccountPattern(e.pathname)?{type:"personalAccount"}:null}catch(e){return console.error("Failed to match personal account pattern:",e),null}}},rE={NonEligibleNoCookie:"non-eligible-no-refresh-cookie",NonEligibleNonGet:"non-eligible-non-get",InvalidSessionToken:"invalid-session-token",MissingApiClient:"missing-api-client",MissingSessionToken:"missing-session-token",MissingRefreshToken:"missing-refresh-token",ExpiredSessionTokenDecodeFailed:"expired-session-token-decode-failed",ExpiredSessionTokenMissingSidClaim:"expired-session-token-missing-sid-claim",FetchError:"fetch-error",UnexpectedSDKError:"unexpected-sdk-error",UnexpectedBAPIError:"unexpected-bapi-error"},rT=({tokenError:e,refreshError:t})=>{switch(e){case TokenVerificationErrorReason.TokenExpired:return`${re.SessionTokenExpired}-refresh-${t}`;case TokenVerificationErrorReason.TokenNotActiveYet:return re.SessionTokenNBF;case TokenVerificationErrorReason.TokenIatInTheFuture:return re.SessionTokenIatInTheFuture;default:return re.UnexpectedError}},rP=(e,t,r,i)=>{if(""===e)return rx(t.toString(),r?.toString());let s=new URL(e),a=r?new URL(r,s):void 0,n=new URL(t,s);return a&&n.searchParams.set("redirect_url",a.toString()),i&&s.hostname!==n.hostname&&n.searchParams.set(eA.QueryParameters.DevBrowser,i),n.toString()},rx=(e,t)=>{let r;if(e.startsWith("http"))r=new URL(e);else{if(!t||!t.startsWith("http"))throw Error("destination url or return back url should be an absolute path url!");let i=new URL(t);r=new URL(e,i.origin)}return t&&r.searchParams.set("redirect_url",t),r.toString()},rI=e=>{let{publishableKey:t,redirectAdapter:r,signInUrl:i,signUpUrl:s,baseUrl:a,sessionStatus:n}=e,o=m(t),l=o?.frontendApi,c=o?.instanceType==="development",u=function(e){if(!e)return"";let t=e.replace(/clerk\.accountsstage\./,"accountsstage.").replace(/clerk\.accounts\.|clerk\./,"accounts.");return`https://${t}`}(l),d="pending"===n,h=(t,{returnBackUrl:i})=>r(rP(a,`${t}/tasks`,i,c?e.devBrowserToken:null));return{redirectToSignUp:({returnBackUrl:t}={})=>{s||u||T.throwMissingPublishableKeyError();let n=`${u}/sign-up`,o=s||function(e){if(!e)return;let t=new URL(e,a);return t.pathname=`${t.pathname}/create`,t.toString()}(i)||n;return d?h(o,{returnBackUrl:t}):r(rP(a,o,t,c?e.devBrowserToken:null))},redirectToSignIn:({returnBackUrl:t}={})=>{i||u||T.throwMissingPublishableKeyError();let s=`${u}/sign-in`,n=i||s;return d?h(n,{returnBackUrl:t}):r(rP(a,n,t,c?e.devBrowserToken:null))}}},rC=r(62923),rO=e=>{let t=r=>{if(!r)return r;if(Array.isArray(r))return r.map(e=>"object"==typeof e||Array.isArray(e)?t(e):e);let i={...r};for(let r of Object.keys(i)){let s=e(r.toString());s!==r&&(i[s]=i[r],delete i[r]),"object"==typeof i[s]&&(i[s]=t(i[s]))}return i};return t};function rR(e){if("boolean"==typeof e)return e;if(null==e)return!1;if("string"==typeof e){if("true"===e.toLowerCase())return!0;if("false"===e.toLowerCase())return!1}let t=parseInt(e,10);return!isNaN(t)&&t>0}rO(function(e){return e?e.replace(/[A-Z]/g,e=>`_${e.toLowerCase()}`):""}),rO(function(e){return e?e.replace(/([-_][a-z])/g,e=>e.toUpperCase().replace(/-|_/,"")):""}),process.env.NEXT_PUBLIC_CLERK_JS_VERSION,process.env.NEXT_PUBLIC_CLERK_JS_URL;let rF=process.env.CLERK_API_VERSION||"v1",rU=process.env.CLERK_SECRET_KEY||"",rH="pk_live_Y2xlcmsuY3ViZW50LmRldiQ",r$=process.env.CLERK_ENCRYPTION_KEY||"",rN=process.env.CLERK_API_URL||(e=>{let t=m(e)?.frontendApi;return t?.startsWith("clerk.")&&d.some(e=>t?.endsWith(e))?g:p.some(e=>t?.endsWith(e))?"https://api.lclclerk.com":f.some(e=>t?.endsWith(e))?"https://api.clerkstage.dev":g})(rH);process.env.NEXT_PUBLIC_CLERK_DOMAIN,process.env.NEXT_PUBLIC_CLERK_PROXY_URL,rR(process.env.NEXT_PUBLIC_CLERK_IS_SATELLITE),rR(process.env.NEXT_PUBLIC_CLERK_TELEMETRY_DISABLED),rR(process.env.NEXT_PUBLIC_CLERK_TELEMETRY_DEBUG);let rJ=rR(process.env.NEXT_PUBLIC_CLERK_KEYLESS_DISABLED)||!1,rM={rE:"15.3.2"},rq=e=>{if(!e||"string"!=typeof e)return e;try{return(e||"").replace(/^(sk_(live|test)_)(.+?)(.{3})$/,"$1*********$4")}catch{return""}},rD=e=>(Array.isArray(e)?e:[e]).map(e=>"string"==typeof e?rq(e):JSON.stringify(Object.fromEntries(Object.entries(e).map(([e,t])=>[e,rq(t)])),null,2)).join(", "),rj=(e,t)=>()=>{let r=[],i=!1;return{enable:()=>{i=!0},debug:(...e)=>{i&&r.push(e.map(e=>"function"==typeof e?e():e))},commit:()=>{if(i){var s,a;for(let i of(console.log((s=e,`[clerk debug start: ${s}]`)),r)){let e=t(i);e=e.split("\n").map(e=>`  ${e}`).join("\n"),process.env.VERCEL&&(e=function(e,t){let r=new TextEncoder,i=new TextDecoder("utf-8"),s=r.encode(e).slice(0,4096);return i.decode(s).replace(/\uFFFD/g,"")}(e,4096)),console.log(e)}console.log((a=e,`[clerk debug end: ${a}] (@clerk/nextjs=6.20.0,next=${rM.rE},timestamp=${Math.round(new Date().getTime()/1e3)})`))}}}},rL=(e,t)=>(...r)=>{let i=("string"==typeof e?rj(e,rD):e)(),s=t(i);try{let e=s(...r);if("object"==typeof e&&"then"in e&&"function"==typeof e.then)return e.then(e=>(i.commit(),e)).catch(e=>{throw i.commit(),e});return i.commit(),e}catch(e){throw i.commit(),e}},rK=rM.rE.startsWith("13.")||rM.rE.startsWith("14.0");function rW(e){return async(...t)=>{let{data:r,errors:i}=await e(...t);if(i)throw i[0];return r}}function rz(e){let t=JSON.stringify(e),r=new TextEncoder().encode(t);return H.stringify(r,{pad:!1})}async function rB(e,t,r){if(!r.algorithm)throw Error("No algorithm specified");let i=new TextEncoder,s=D(r.algorithm);if(!s)return{errors:[new O(`Unsupported algorithm ${r.algorithm}`)]};let a=await Q(t,s,"sign"),n=r.header||{typ:"JWT"};n.alg=r.algorithm,e.iat=Math.floor(Date.now()/1e3);let o=rz(n),l=rz(e),c=`${o}.${l}`;try{let e=await U.crypto.subtle.sign(s,a,i.encode(c));return{data:`${c}.${H.stringify(new Uint8Array(e),{pad:!1})}`}}catch(e){return{errors:[new O(e?.message)]}}}rW(ee);var rV=(...e)=>{let{data:t,errors:r}=Z(...e);if(r)throw r[0];return t};function rG(e,t){var r;return((r=eA.Attributes[t])in e?e[r]:void 0)||rY(e,eA.Headers[t])}function rY(e,t){var r,i;return function(e){try{let{headers:t,nextUrl:r,cookies:i}=e||{};return"function"==typeof(null==t?void 0:t.get)&&"function"==typeof(null==r?void 0:r.searchParams.get)&&"function"==typeof(null==i?void 0:i.get)}catch{return!1}}(e)||function(e){try{let{headers:t}=e||{};return"function"==typeof(null==t?void 0:t.get)}catch{return!1}}(e)?e.headers.get(t):e.headers[t]||e.headers[t.toLowerCase()]||(null==(i=null==(r=e.socket)?void 0:r._httpMessage)?void 0:i.getHeader(t))}function rQ(e){return!!rG(e,"AuthStatus")}rW(rB),rW(X);var rX=r(26239);let rZ=!rK&&!1;var r0,r1,r2,r4,r5,r6,r8,r3=Object.defineProperty,r9=(e,t,r)=>t in e?r3(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,r7=(null==(r0="undefined"!=typeof globalThis?globalThis:void 0)?void 0:r0.crypto)||(null==(r1="undefined"!=typeof global?global:void 0)?void 0:r1.crypto)||(null==(r2="undefined"!=typeof window?window:void 0)?void 0:r2.crypto)||(null==(r4="undefined"!=typeof self?self:void 0)?void 0:r4.crypto)||(null==(r6=null==(r5="undefined"!=typeof frames?frames:void 0)?void 0:r5[0])?void 0:r6.crypto);r8=r7?e=>{let t=[];for(let r=0;r<e;r+=4)t.push(r7.getRandomValues(new Uint32Array(1))[0]);return new it(t,e)}:e=>{let t=[],r=e=>{let t=e,r=0x3ade68b1;return()=>{let e=((r=36969*(65535&r)+(r>>16)|0)<<16)+(t=18e3*(65535&t)+(t>>16)|0)|0;return e/=0x100000000,(e+=.5)*(Math.random()>.5?1:-1)}};for(let i=0,s;i<e;i+=4){let e=r(0x100000000*(s||Math.random()));s=0x3ade67b7*e(),t.push(0x100000000*e()|0)}return new it(t,e)};var ie=class{static create(...e){return new this(...e)}mixIn(e){return Object.assign(this,e)}clone(){let e=new this.constructor;return Object.assign(e,this),e}},it=class extends ie{constructor(e=[],t=4*e.length){super();let r=e;if(r instanceof ArrayBuffer&&(r=new Uint8Array(r)),(r instanceof Int8Array||r instanceof Uint8ClampedArray||r instanceof Int16Array||r instanceof Uint16Array||r instanceof Int32Array||r instanceof Uint32Array||r instanceof Float32Array||r instanceof Float64Array)&&(r=new Uint8Array(r.buffer,r.byteOffset,r.byteLength)),r instanceof Uint8Array){let e=r.byteLength,t=[];for(let i=0;i<e;i+=1)t[i>>>2]|=r[i]<<24-i%4*8;this.words=t,this.sigBytes=e}else this.words=e,this.sigBytes=t}toString(e=ir){return e.stringify(this)}concat(e){let t=this.words,r=e.words,i=this.sigBytes,s=e.sigBytes;if(this.clamp(),i%4)for(let e=0;e<s;e+=1){let s=r[e>>>2]>>>24-e%4*8&255;t[i+e>>>2]|=s<<24-(i+e)%4*8}else for(let e=0;e<s;e+=4)t[i+e>>>2]=r[e>>>2];return this.sigBytes+=s,this}clamp(){let{words:e,sigBytes:t}=this;e[t>>>2]&=0xffffffff<<32-t%4*8,e.length=Math.ceil(t/4)}clone(){let e=super.clone.call(this);return e.words=this.words.slice(0),e}};((e,t,r)=>r9(e,"symbol"!=typeof t?t+"":t,r))(it,"random",r8);var ir={stringify(e){let{words:t,sigBytes:r}=e,i=[];for(let e=0;e<r;e+=1){let r=t[e>>>2]>>>24-e%4*8&255;i.push((r>>>4).toString(16)),i.push((15&r).toString(16))}return i.join("")},parse(e){let t=e.length,r=[];for(let i=0;i<t;i+=2)r[i>>>3]|=parseInt(e.substr(i,2),16)<<24-i%8*4;return new it(r,t/2)}},ii={stringify(e){let{words:t,sigBytes:r}=e,i=[];for(let e=0;e<r;e+=1){let r=t[e>>>2]>>>24-e%4*8&255;i.push(String.fromCharCode(r))}return i.join("")},parse(e){let t=e.length,r=[];for(let i=0;i<t;i+=1)r[i>>>2]|=(255&e.charCodeAt(i))<<24-i%4*8;return new it(r,t)}},is={stringify(e){try{return decodeURIComponent(escape(ii.stringify(e)))}catch{throw Error("Malformed UTF-8 data")}},parse:e=>ii.parse(unescape(encodeURIComponent(e)))},ia=class extends ie{constructor(){super(),this._minBufferSize=0}reset(){this._data=new it,this._nDataBytes=0}_append(e){let t=e;"string"==typeof t&&(t=is.parse(t)),this._data.concat(t),this._nDataBytes+=t.sigBytes}_process(e){let t,{_data:r,blockSize:i}=this,s=r.words,a=r.sigBytes,n=a/(4*i),o=(n=e?Math.ceil(n):Math.max((0|n)-this._minBufferSize,0))*i,l=Math.min(4*o,a);if(o){for(let e=0;e<o;e+=i)this._doProcessBlock(s,e);t=s.splice(0,o),r.sigBytes-=l}return new it(t,l)}clone(){let e=super.clone.call(this);return e._data=this._data.clone(),e}},io=class extends ia{constructor(e){super(),this.blockSize=16,this.cfg=Object.assign(new ie,e),this.reset()}static _createHelper(e){return(t,r)=>new e(r).finalize(t)}static _createHmacHelper(e){return(t,r)=>new il(e,r).finalize(t)}reset(){super.reset.call(this),this._doReset()}update(e){return this._append(e),this._process(),this}finalize(e){return e&&this._append(e),this._doFinalize()}},il=class extends ie{constructor(e,t){super();let r=new e;this._hasher=r;let i=t;"string"==typeof i&&(i=is.parse(i));let s=r.blockSize,a=4*s;i.sigBytes>a&&(i=r.finalize(t)),i.clamp();let n=i.clone();this._oKey=n;let o=i.clone();this._iKey=o;let l=n.words,c=o.words;for(let e=0;e<s;e+=1)l[e]^=0x5c5c5c5c,c[e]^=0x36363636;n.sigBytes=a,o.sigBytes=a,this.reset()}reset(){let e=this._hasher;e.reset(),e.update(this._iKey)}update(e){return this._hasher.update(e),this}finalize(e){let t=this._hasher,r=t.finalize(e);return t.reset(),t.finalize(this._oKey.clone().concat(r))}},ic=(e,t,r)=>{let i=[],s=0;for(let a=0;a<t;a+=1)if(a%4){let t=r[e.charCodeAt(a-1)]<<a%4*2|r[e.charCodeAt(a)]>>>6-a%4*2;i[s>>>2]|=t<<24-s%4*8,s+=1}return it.create(i,s)},iu={stringify(e){let{words:t,sigBytes:r}=e,i=this._map;e.clamp();let s=[];for(let e=0;e<r;e+=3){let a=(t[e>>>2]>>>24-e%4*8&255)<<16|(t[e+1>>>2]>>>24-(e+1)%4*8&255)<<8|t[e+2>>>2]>>>24-(e+2)%4*8&255;for(let t=0;t<4&&e+.75*t<r;t+=1)s.push(i.charAt(a>>>6*(3-t)&63))}let a=i.charAt(64);if(a)for(;s.length%4;)s.push(a);return s.join("")},parse(e){let t=e.length,r=this._map,i=this._reverseMap;if(!i){this._reverseMap=[],i=this._reverseMap;for(let e=0;e<r.length;e+=1)i[r.charCodeAt(e)]=e}let s=r.charAt(64);if(s){let r=e.indexOf(s);-1!==r&&(t=r)}return ic(e,t,i)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="},id=[];for(let e=0;e<64;e+=1)id[e]=0x100000000*Math.abs(Math.sin(e+1))|0;var ih=(e,t,r,i,s,a,n)=>{let o=e+(t&r|~t&i)+s+n;return(o<<a|o>>>32-a)+t},ip=(e,t,r,i,s,a,n)=>{let o=e+(t&i|r&~i)+s+n;return(o<<a|o>>>32-a)+t},ig=(e,t,r,i,s,a,n)=>{let o=e+(t^r^i)+s+n;return(o<<a|o>>>32-a)+t},iy=(e,t,r,i,s,a,n)=>{let o=e+(r^(t|~i))+s+n;return(o<<a|o>>>32-a)+t},im=class extends io{_doReset(){this._hash=new it([0x67452301,0xefcdab89,0x98badcfe,0x10325476])}_doProcessBlock(e,t){for(let r=0;r<16;r+=1){let i=t+r,s=e[i];e[i]=(s<<8|s>>>24)&0xff00ff|(s<<24|s>>>8)&0xff00ff00}let r=this._hash.words,i=e[t+0],s=e[t+1],a=e[t+2],n=e[t+3],o=e[t+4],l=e[t+5],c=e[t+6],u=e[t+7],d=e[t+8],h=e[t+9],p=e[t+10],f=e[t+11],g=e[t+12],y=e[t+13],m=e[t+14],w=e[t+15],b=r[0],_=r[1],v=r[2],S=r[3];b=ih(b,_,v,S,i,7,id[0]),S=ih(S,b,_,v,s,12,id[1]),v=ih(v,S,b,_,a,17,id[2]),_=ih(_,v,S,b,n,22,id[3]),b=ih(b,_,v,S,o,7,id[4]),S=ih(S,b,_,v,l,12,id[5]),v=ih(v,S,b,_,c,17,id[6]),_=ih(_,v,S,b,u,22,id[7]),b=ih(b,_,v,S,d,7,id[8]),S=ih(S,b,_,v,h,12,id[9]),v=ih(v,S,b,_,p,17,id[10]),_=ih(_,v,S,b,f,22,id[11]),b=ih(b,_,v,S,g,7,id[12]),S=ih(S,b,_,v,y,12,id[13]),v=ih(v,S,b,_,m,17,id[14]),_=ih(_,v,S,b,w,22,id[15]),b=ip(b,_,v,S,s,5,id[16]),S=ip(S,b,_,v,c,9,id[17]),v=ip(v,S,b,_,f,14,id[18]),_=ip(_,v,S,b,i,20,id[19]),b=ip(b,_,v,S,l,5,id[20]),S=ip(S,b,_,v,p,9,id[21]),v=ip(v,S,b,_,w,14,id[22]),_=ip(_,v,S,b,o,20,id[23]),b=ip(b,_,v,S,h,5,id[24]),S=ip(S,b,_,v,m,9,id[25]),v=ip(v,S,b,_,n,14,id[26]),_=ip(_,v,S,b,d,20,id[27]),b=ip(b,_,v,S,y,5,id[28]),S=ip(S,b,_,v,a,9,id[29]),v=ip(v,S,b,_,u,14,id[30]),_=ip(_,v,S,b,g,20,id[31]),b=ig(b,_,v,S,l,4,id[32]),S=ig(S,b,_,v,d,11,id[33]),v=ig(v,S,b,_,f,16,id[34]),_=ig(_,v,S,b,m,23,id[35]),b=ig(b,_,v,S,s,4,id[36]),S=ig(S,b,_,v,o,11,id[37]),v=ig(v,S,b,_,u,16,id[38]),_=ig(_,v,S,b,p,23,id[39]),b=ig(b,_,v,S,y,4,id[40]),S=ig(S,b,_,v,i,11,id[41]),v=ig(v,S,b,_,n,16,id[42]),_=ig(_,v,S,b,c,23,id[43]),b=ig(b,_,v,S,h,4,id[44]),S=ig(S,b,_,v,g,11,id[45]),v=ig(v,S,b,_,w,16,id[46]),_=ig(_,v,S,b,a,23,id[47]),b=iy(b,_,v,S,i,6,id[48]),S=iy(S,b,_,v,u,10,id[49]),v=iy(v,S,b,_,m,15,id[50]),_=iy(_,v,S,b,l,21,id[51]),b=iy(b,_,v,S,g,6,id[52]),S=iy(S,b,_,v,n,10,id[53]),v=iy(v,S,b,_,p,15,id[54]),_=iy(_,v,S,b,s,21,id[55]),b=iy(b,_,v,S,d,6,id[56]),S=iy(S,b,_,v,w,10,id[57]),v=iy(v,S,b,_,c,15,id[58]),_=iy(_,v,S,b,y,21,id[59]),b=iy(b,_,v,S,o,6,id[60]),S=iy(S,b,_,v,f,10,id[61]),v=iy(v,S,b,_,a,15,id[62]),_=iy(_,v,S,b,h,21,id[63]),r[0]=r[0]+b|0,r[1]=r[1]+_|0,r[2]=r[2]+v|0,r[3]=r[3]+S|0}_doFinalize(){let e=this._data,t=e.words,r=8*this._nDataBytes,i=8*e.sigBytes;t[i>>>5]|=128<<24-i%32;let s=Math.floor(r/0x100000000);t[(i+64>>>9<<4)+15]=(s<<8|s>>>24)&0xff00ff|(s<<24|s>>>8)&0xff00ff00,t[(i+64>>>9<<4)+14]=(r<<8|r>>>24)&0xff00ff|(r<<24|r>>>8)&0xff00ff00,e.sigBytes=(t.length+1)*4,this._process();let a=this._hash,n=a.words;for(let e=0;e<4;e+=1){let t=n[e];n[e]=(t<<8|t>>>24)&0xff00ff|(t<<24|t>>>8)&0xff00ff00}return a}clone(){let e=super.clone.call(this);return e._hash=this._hash.clone(),e}};io._createHelper(im),io._createHmacHelper(im);var iw=class extends ie{constructor(e){super(),this.cfg=Object.assign(new ie,{keySize:4,hasher:im,iterations:1},e)}compute(e,t){let r,{cfg:i}=this,s=i.hasher.create(),a=it.create(),n=a.words,{keySize:o,iterations:l}=i;for(;n.length<o;){r&&s.update(r),r=s.update(e).finalize(t),s.reset();for(let e=1;e<l;e+=1)r=s.finalize(r),s.reset();a.concat(r)}return a.sigBytes=4*o,a}},ib=class extends ia{constructor(e,t,r){super(),this.cfg=Object.assign(new ie,r),this._xformMode=e,this._key=t,this.reset()}static createEncryptor(e,t){return this.create(this._ENC_XFORM_MODE,e,t)}static createDecryptor(e,t){return this.create(this._DEC_XFORM_MODE,e,t)}static _createHelper(e){let t=e=>"string"==typeof e?iP:iT;return{encrypt:(r,i,s)=>t(i).encrypt(e,r,i,s),decrypt:(r,i,s)=>t(i).decrypt(e,r,i,s)}}reset(){super.reset.call(this),this._doReset()}process(e){return this._append(e),this._process()}finalize(e){return e&&this._append(e),this._doFinalize()}};ib._ENC_XFORM_MODE=1,ib._DEC_XFORM_MODE=2,ib.keySize=4,ib.ivSize=4;var i_=class extends ie{constructor(e,t){super(),this._cipher=e,this._iv=t}static createEncryptor(e,t){return this.Encryptor.create(e,t)}static createDecryptor(e,t){return this.Decryptor.create(e,t)}};function iv(e,t,r){let i,s=this._iv;s?(i=s,this._iv=void 0):i=this._prevBlock;for(let s=0;s<r;s+=1)e[t+s]^=i[s]}var iS=class extends i_{};iS.Encryptor=class extends iS{processBlock(e,t){let r=this._cipher,{blockSize:i}=r;iv.call(this,e,t,i),r.encryptBlock(e,t),this._prevBlock=e.slice(t,t+i)}},iS.Decryptor=class extends iS{processBlock(e,t){let r=this._cipher,{blockSize:i}=r,s=e.slice(t,t+i);r.decryptBlock(e,t),iv.call(this,e,t,i),this._prevBlock=s}};var ik={pad(e,t){let r=4*t,i=r-e.sigBytes%r,s=i<<24|i<<16|i<<8|i,a=[];for(let e=0;e<i;e+=4)a.push(s);let n=it.create(a,i);e.concat(n)},unpad(e){let t=255&e.words[e.sigBytes-1>>>2];e.sigBytes-=t}},iA=class extends ib{constructor(e,t,r){super(e,t,Object.assign({mode:iS,padding:ik},r)),this.blockSize=4}reset(){let e;super.reset.call(this);let{cfg:t}=this,{iv:r,mode:i}=t;this._xformMode===this.constructor._ENC_XFORM_MODE?e=i.createEncryptor:(e=i.createDecryptor,this._minBufferSize=1),this._mode=e.call(i,this,r&&r.words),this._mode.__creator=e}_doProcessBlock(e,t){this._mode.processBlock(e,t)}_doFinalize(){let e,{padding:t}=this.cfg;return this._xformMode===this.constructor._ENC_XFORM_MODE?(t.pad(this._data,this.blockSize),e=this._process(!0)):(e=this._process(!0),t.unpad(e)),e}},iE=class extends ie{constructor(e){super(),this.mixIn(e)}toString(e){return(e||this.formatter).stringify(this)}},iT=class extends ie{static encrypt(e,t,r,i){let s=Object.assign(new ie,this.cfg,i),a=e.createEncryptor(r,s),n=a.finalize(t),o=a.cfg;return iE.create({ciphertext:n,key:r,iv:o.iv,algorithm:e,mode:o.mode,padding:o.padding,blockSize:a.blockSize,formatter:s.format})}static decrypt(e,t,r,i){let s=t,a=Object.assign(new ie,this.cfg,i);return s=this._parse(s,a.format),e.createDecryptor(r,a).finalize(s.ciphertext)}static _parse(e,t){return"string"==typeof e?t.parse(e,this):e}};iT.cfg=Object.assign(new ie,{format:{stringify(e){let t,{ciphertext:r,salt:i}=e;return(i?it.create([0x53616c74,0x65645f5f]).concat(i).concat(r):r).toString(iu)},parse(e){let t,r=iu.parse(e),i=r.words;return 0x53616c74===i[0]&&0x65645f5f===i[1]&&(t=it.create(i.slice(2,4)),i.splice(0,4),r.sigBytes-=16),iE.create({ciphertext:r,salt:t})}}});var iP=class extends iT{static encrypt(e,t,r,i){let s=Object.assign(new ie,this.cfg,i),a=s.kdf.execute(r,e.keySize,e.ivSize,s.salt,s.hasher);s.iv=a.iv;let n=iT.encrypt.call(this,e,t,a.key,s);return n.mixIn(a),n}static decrypt(e,t,r,i){let s=t,a=Object.assign(new ie,this.cfg,i);s=this._parse(s,a.format);let n=a.kdf.execute(r,e.keySize,e.ivSize,s.salt,a.hasher);return a.iv=n.iv,iT.decrypt.call(this,e,s,n.key,a)}};iP.cfg=Object.assign(iT.cfg,{kdf:{execute(e,t,r,i,s){let a,n=i;n||(n=it.random(8)),a=s?iw.create({keySize:t+r,hasher:s}).compute(e,n):iw.create({keySize:t+r}).compute(e,n);let o=it.create(a.words.slice(t),4*r);return a.sigBytes=4*t,iE.create({key:a,iv:o,salt:n})}}});var ix=[],iI=[],iC=[],iO=[],iR=[],iF=[],iU=[],iH=[],i$=[],iN=[],iJ=[];for(let e=0;e<256;e+=1)e<128?iJ[e]=e<<1:iJ[e]=e<<1^283;var iM=0,iq=0;for(let e=0;e<256;e+=1){let e=iq^iq<<1^iq<<2^iq<<3^iq<<4;e=e>>>8^255&e^99,ix[iM]=e,iI[e]=iM;let t=iJ[iM],r=iJ[t],i=iJ[r],s=257*iJ[e]^0x1010100*e;iC[iM]=s<<24|s>>>8,iO[iM]=s<<16|s>>>16,iR[iM]=s<<8|s>>>24,iF[iM]=s,s=0x1010101*i^65537*r^257*t^0x1010100*iM,iU[e]=s<<24|s>>>8,iH[e]=s<<16|s>>>16,i$[e]=s<<8|s>>>24,iN[e]=s,iM?(iM=t^iJ[iJ[iJ[i^t]]],iq^=iJ[iJ[iq]]):iM=iq=1}var iD=[0,1,2,4,8,16,32,64,128,27,54],ij=class extends iA{_doReset(){let e;if(this._nRounds&&this._keyPriorReset===this._key)return;this._keyPriorReset=this._key;let t=this._keyPriorReset,r=t.words,i=t.sigBytes/4;this._nRounds=i+6;let s=(this._nRounds+1)*4;this._keySchedule=[];let a=this._keySchedule;for(let t=0;t<s;t+=1)t<i?a[t]=r[t]:(e=a[t-1],t%i?i>6&&t%i==4&&(e=ix[e>>>24]<<24|ix[e>>>16&255]<<16|ix[e>>>8&255]<<8|ix[255&e]):e=(ix[(e=e<<8|e>>>24)>>>24]<<24|ix[e>>>16&255]<<16|ix[e>>>8&255]<<8|ix[255&e])^iD[t/i|0]<<24,a[t]=a[t-i]^e);this._invKeySchedule=[];let n=this._invKeySchedule;for(let t=0;t<s;t+=1){let r=s-t;e=t%4?a[r]:a[r-4],t<4||r<=4?n[t]=e:n[t]=iU[ix[e>>>24]]^iH[ix[e>>>16&255]]^i$[ix[e>>>8&255]]^iN[ix[255&e]]}}encryptBlock(e,t){this._doCryptBlock(e,t,this._keySchedule,iC,iO,iR,iF,ix)}decryptBlock(e,t){let r=e[t+1];e[t+1]=e[t+3],e[t+3]=r,this._doCryptBlock(e,t,this._invKeySchedule,iU,iH,i$,iN,iI),r=e[t+1],e[t+1]=e[t+3],e[t+3]=r}_doCryptBlock(e,t,r,i,s,a,n,o){let l=this._nRounds,c=e[t]^r[0],u=e[t+1]^r[1],d=e[t+2]^r[2],h=e[t+3]^r[3],p=4;for(let e=1;e<l;e+=1){let e=i[c>>>24]^s[u>>>16&255]^a[d>>>8&255]^n[255&h]^r[p];p+=1;let t=i[u>>>24]^s[d>>>16&255]^a[h>>>8&255]^n[255&c]^r[p];p+=1;let o=i[d>>>24]^s[h>>>16&255]^a[c>>>8&255]^n[255&u]^r[p];p+=1;let l=i[h>>>24]^s[c>>>16&255]^a[u>>>8&255]^n[255&d]^r[p];p+=1,c=e,u=t,d=o,h=l}let f=(o[c>>>24]<<24|o[u>>>16&255]<<16|o[d>>>8&255]<<8|o[255&h])^r[p];p+=1;let g=(o[u>>>24]<<24|o[d>>>16&255]<<16|o[h>>>8&255]<<8|o[255&c])^r[p];p+=1;let y=(o[d>>>24]<<24|o[h>>>16&255]<<16|o[c>>>8&255]<<8|o[255&u])^r[p];p+=1;let m=(o[h>>>24]<<24|o[c>>>16&255]<<16|o[u>>>8&255]<<8|o[255&d])^r[p];p+=1,e[t]=f,e[t+1]=g,e[t+2]=y,e[t+3]=m}};ij.keySize=8;var iL=iA._createHelper(ij),iK=[],iW=class extends io{_doReset(){this._hash=new it([0x67452301,0xefcdab89,0x98badcfe,0x10325476,0xc3d2e1f0])}_doProcessBlock(e,t){let r=this._hash.words,i=r[0],s=r[1],a=r[2],n=r[3],o=r[4];for(let r=0;r<80;r+=1){if(r<16)iK[r]=0|e[t+r];else{let e=iK[r-3]^iK[r-8]^iK[r-14]^iK[r-16];iK[r]=e<<1|e>>>31}let l=(i<<5|i>>>27)+o+iK[r];r<20?l+=(s&a|~s&n)+0x5a827999:r<40?l+=(s^a^n)+0x6ed9eba1:r<60?l+=(s&a|s&n|a&n)-0x70e44324:l+=(s^a^n)-0x359d3e2a,o=n,n=a,a=s<<30|s>>>2,s=i,i=l}r[0]=r[0]+i|0,r[1]=r[1]+s|0,r[2]=r[2]+a|0,r[3]=r[3]+n|0,r[4]=r[4]+o|0}_doFinalize(){let e=this._data,t=e.words,r=8*this._nDataBytes,i=8*e.sigBytes;return t[i>>>5]|=128<<24-i%32,t[(i+64>>>9<<4)+14]=Math.floor(r/0x100000000),t[(i+64>>>9<<4)+15]=r,e.sigBytes=4*t.length,this._process(),this._hash}clone(){let e=super.clone.call(this);return e._hash=this._hash.clone(),e}},iz=(io._createHelper(iW),io._createHmacHelper(iW));let iB=(e="auth",t)=>`Clerk: ${e}() was called but Clerk can't detect usage of clerkMiddleware(). Please ensure the following:
- ${t?[...t,""].join("\n- "):" "}clerkMiddleware() is used in your Next.js Middleware.
- Your Middleware matcher is configured to match this route or page.
- If you are using the src directory, make sure the Middleware file is inside of it.

For more details, see https://clerk.com/docs/quickstarts/nextjs
`,iV=`Clerk: Unable to decrypt request data.

Refresh the page if your .env file was just updated. If the issue persists, ensure the encryption key is valid and properly set.

For more information, see: https://clerk.com/docs/references/nextjs/clerk-middleware#dynamic-keys. (code=encryption_key_invalid)`,iG="x-middleware-override-headers",iY="x-middleware-request";function iQ(e,t){if(!rQ(e))throw Error(t)}let iX="clerk_keyless_dummy_key";function iZ(e){if(!e)return{};let t=_()?r$||rU:r$||rU||iX;try{return i1(e,t)}catch{if(rZ)try{return i1(e,iX)}catch{i0()}i0()}}function i0(){if(_())throw Error("Clerk: Unable to decrypt request data, this usually means the encryption key is invalid. Ensure the encryption key is properly set. For more information, see: https://clerk.com/docs/references/nextjs/clerk-middleware#dynamic-keys. (code=encryption_key_invalid)");throw Error(iV)}function i1(e,t){return JSON.parse(iL.decrypt(e,t).toString(is))}function i2(e,{treatPendingAsSignedOut:t=!0,...r}={}){let i,s=rG(e,"AuthStatus"),a=rG(e,"AuthToken"),n=rG(e,"AuthMessage"),o=rG(e,"AuthReason"),l=rG(e,"AuthSignature");null==(d=r.logger)||d.debug("headers",{authStatus:s,authMessage:n,authReason:o});let c=iZ(rY(e,eA.Headers.ClerkRequestData)),u={secretKey:(null==r?void 0:r.secretKey)||c.secretKey||rU,publishableKey:c.publishableKey||rH,apiUrl:rN,apiVersion:rF,authStatus:s,authMessage:n,authReason:o,treatPendingAsSignedOut:t};if(null==(h=r.logger)||h.debug("auth options",u),s&&s===t7.SignedIn){var d,h,p,f=u.secretKey;if(!l||iz(a,f).toString()!==l)throw Error("Clerk: Unable to verify request, this usually means the Clerk middleware did not run. Ensure Clerk's middleware is properly integrated and matches the current route. For more information, see: https://clerk.com/docs/references/nextjs/clerk-middleware. (code=auth_signature_invalid)");let e=rV(a);null==(p=r.logger)||p.debug("jwt",e.raw),i=t8(u,e.raw.text,e.payload)}else i=t3(u);return t&&"pending"===i.sessionStatus&&(i=t3(u,i.sessionStatus)),i}let i4=({debugLoggerName:e,noAuthStatusMessage:t})=>rL(e,e=>async(i,s)=>{if(rR(rY(i,eA.Headers.EnableDebug))&&e.enable(),!rQ(i)){rK&&iQ(i,t);let e=await r.e(6560).then(r.bind(r,56560)).then(e=>e.suggestMiddlewareLocation()).catch(()=>void 0);if(e)throw Error(e);iQ(i,t)}return i2(i,{...s,logger:e})});(({debugLoggerName:e,noAuthStatusMessage:t})=>rL(e,e=>(r,i)=>(rR(rY(r,eA.Headers.EnableDebug))&&e.enable(),iQ(r,t),i2(r,{...i,logger:e}))))({debugLoggerName:"getAuth()",noAuthStatusMessage:iB("getAuth")});let i5={Headers:{NextRewrite:"x-middleware-rewrite",NextResume:"x-middleware-next",NextRedirect:"Location",NextUrl:"next-url",NextAction:"next-action",NextjsData:"x-nextjs-data"}},i6=e=>{var t,r;return!!e.headers.get(i5.Headers.NextUrl)&&((null==(t=e.headers.get(eA.Headers.Accept))?void 0:t.includes("text/x-component"))||(null==(r=e.headers.get(eA.Headers.ContentType))?void 0:r.includes("multipart/form-data"))||!!e.headers.get(i5.Headers.NextAction))},i8=e=>{var t;return"document"===e.headers.get(eA.Headers.SecFetchDest)||"iframe"===e.headers.get(eA.Headers.SecFetchDest)||(null==(t=e.headers.get(eA.Headers.Accept))?void 0:t.includes("text/html"))||i3(e)||i7(e)},i3=e=>!!e.headers.get(i5.Headers.NextUrl)&&!i6(e)||i9(),i9=()=>{let e=globalThis.fetch;if(!function(e){return"__nextPatched"in e&&!0===e.__nextPatched}(e))return!1;let{page:t,pagePath:r}=e.__nextGetStaticStore().getStore()||{};return!!(r||t)},i7=e=>!!e.headers.get(i5.Headers.NextjsData),se=e=>{if(!(e instanceof Error)||!("message"in e))return!1;let{message:t}=e,r=t.toLowerCase(),i=r.includes("dynamic server usage"),s=r.includes("this page needs to bail out of prerendering");return/Route .*? needs to bail out of prerendering at this point because it used .*?./.test(t)||i||s};async function st(){try{let{headers:e}=await Promise.all([r.e(2644),r.e(673)]).then(r.bind(r,62644)),t=await e();return new rX.NextRequest("https://placeholder.com",{headers:t})}catch(e){if(e&&se(e))throw e;throw Error(`Clerk: auth(), currentUser() and clerkClient(), are only supported in App Router (/app directory).
If you're using /pages, try getAuth() instead.
Original error: ${e}`)}}let sr=async({treatPendingAsSignedOut:e}={})=>{r(1447);let t=await st(),i=async()=>{if(rK)return[];try{let e=await r.e(6560).then(r.bind(r,56560)).then(e=>e.hasSrcAppDir());return[`Your Middleware exists at ./${e?"src/":""}middleware.(ts|js)`]}catch{return[]}},s=await i4({debugLoggerName:"auth()",noAuthStatusMessage:iB("auth",await i())})(t,{treatPendingAsSignedOut:e}),a=rG(t,"ClerkUrl"),n=(...e)=>{let{returnBackUrl:r}=e[0]||{},i=ro(t),n=i.clerkUrl.searchParams.get(eA.QueryParameters.DevBrowser)||i.cookies.get(eA.Cookies.DevBrowser),o=iZ(rY(t,eA.Headers.ClerkRequestData));return[rI({redirectAdapter:rC.redirect,devBrowserToken:n,baseUrl:i.clerkUrl.toString(),publishableKey:o.publishableKey||rH,signInUrl:o.signInUrl||"/sign-in",signUpUrl:o.signUpUrl||"/sign-up",sessionStatus:s.sessionStatus}),null===r?"":r||(null==a?void 0:a.toString())]};return Object.assign(s,{redirectToSignIn:(e={})=>{let[t,r]=n(e);return t.redirectToSignIn({returnBackUrl:r})},redirectToSignUp:(e={})=>{let[t,r]=n(e);return t.redirectToSignUp({returnBackUrl:r})}})};sr.protect=async(...e)=>{r(1447);let t=await st(),i=await sr();return(function(e){let{redirectToSignIn:t,authObject:r,redirect:i,notFound:s,request:a}=e;return async(...e)=>{var n,o,l,c,u,d;let h=(null==(n=e[0])?void 0:n.unauthenticatedUrl)||(null==(o=e[0])?void 0:o.unauthorizedUrl)?void 0:e[0],p=(null==(l=e[0])?void 0:l.unauthenticatedUrl)||(null==(c=e[1])?void 0:c.unauthenticatedUrl),f=(null==(u=e[0])?void 0:u.unauthorizedUrl)||(null==(d=e[1])?void 0:d.unauthorizedUrl),g=()=>f?i(f):s();return"pending"!==r.sessionStatus&&r.userId?h?"function"==typeof h?h(r.has)?r:g():r.has(h)?r:g():r:p?i(p):i8(a)?t():s()}})({request:t,authObject:i,redirectToSignIn:i.redirectToSignIn,notFound:rC.notFound,redirect:rC.redirect})(...e)}},22556:(e,t,r)=>{r.d(t,{A:()=>s});var i=r(57975);let s=e=>i.types.isKeyObject(e)},25840:(e,t,r)=>{function i(e,t="algorithm.name"){return TypeError(`CryptoKey does not support this operation, its ${t} must be ${e}`)}function s(e,t){return e.name===t}function a(e){return parseInt(e.name.slice(4),10)}function n(e,t){if(t.length&&!t.some(t=>e.usages.includes(t))){let e="CryptoKey does not support this operation, its usages must include ";if(t.length>2){let r=t.pop();e+=`one of ${t.join(", ")}, or ${r}.`}else 2===t.length?e+=`one of ${t[0]} or ${t[1]}.`:e+=`${t[0]}.`;throw TypeError(e)}}function o(e,t,...r){switch(t){case"HS256":case"HS384":case"HS512":{if(!s(e.algorithm,"HMAC"))throw i("HMAC");let r=parseInt(t.slice(2),10);if(a(e.algorithm.hash)!==r)throw i(`SHA-${r}`,"algorithm.hash");break}case"RS256":case"RS384":case"RS512":{if(!s(e.algorithm,"RSASSA-PKCS1-v1_5"))throw i("RSASSA-PKCS1-v1_5");let r=parseInt(t.slice(2),10);if(a(e.algorithm.hash)!==r)throw i(`SHA-${r}`,"algorithm.hash");break}case"PS256":case"PS384":case"PS512":{if(!s(e.algorithm,"RSA-PSS"))throw i("RSA-PSS");let r=parseInt(t.slice(2),10);if(a(e.algorithm.hash)!==r)throw i(`SHA-${r}`,"algorithm.hash");break}case"EdDSA":if("Ed25519"!==e.algorithm.name&&"Ed448"!==e.algorithm.name)throw i("Ed25519 or Ed448");break;case"Ed25519":if(!s(e.algorithm,"Ed25519"))throw i("Ed25519");break;case"ES256":case"ES384":case"ES512":{if(!s(e.algorithm,"ECDSA"))throw i("ECDSA");let r=function(e){switch(e){case"ES256":return"P-256";case"ES384":return"P-384";case"ES512":return"P-521";default:throw Error("unreachable")}}(t);if(e.algorithm.namedCurve!==r)throw i(r,"algorithm.namedCurve");break}default:throw TypeError("CryptoKey does not support this operation")}n(e,r)}function l(e,t,...r){switch(t){case"A128GCM":case"A192GCM":case"A256GCM":{if(!s(e.algorithm,"AES-GCM"))throw i("AES-GCM");let r=parseInt(t.slice(1,4),10);if(e.algorithm.length!==r)throw i(r,"algorithm.length");break}case"A128KW":case"A192KW":case"A256KW":{if(!s(e.algorithm,"AES-KW"))throw i("AES-KW");let r=parseInt(t.slice(1,4),10);if(e.algorithm.length!==r)throw i(r,"algorithm.length");break}case"ECDH":switch(e.algorithm.name){case"ECDH":case"X25519":case"X448":break;default:throw i("ECDH, X25519, or X448")}break;case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":if(!s(e.algorithm,"PBKDF2"))throw i("PBKDF2");break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":{if(!s(e.algorithm,"RSA-OAEP"))throw i("RSA-OAEP");let r=parseInt(t.slice(9),10)||1;if(a(e.algorithm.hash)!==r)throw i(`SHA-${r}`,"algorithm.hash");break}default:throw TypeError("CryptoKey does not support this operation")}n(e,r)}r.d(t,{$:()=>l,Y:()=>o})},26818:(e,t,r)=>{let i;r.d(t,{oF:()=>ef,Ck:()=>ey,PO:()=>eo,HC:()=>ed,aC:()=>eu,J_:()=>ei,uP:()=>es,uX:()=>ec});var s=r(84297),a=r(44214),n=r(13690),o=r(77598),l=r(14295);let c=(e,t)=>{if(t.length<<3!==function(e){switch(e){case"A128GCM":case"A128GCMKW":case"A192GCM":case"A192GCMKW":case"A256GCM":case"A256GCMKW":return 96;case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return 128;default:throw new l.T0(`Unsupported JWE Algorithm: ${e}`)}}(e))throw new l.aA("Invalid Initialization Vector length")};var u=r(22556);let d=(e,t)=>{let r;switch(e){case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":r=parseInt(e.slice(-3),10);break;case"A128GCM":case"A192GCM":case"A256GCM":r=parseInt(e.slice(1,4),10);break;default:throw new l.T0(`Content Encryption Algorithm ${e} is not supported either by JOSE or your javascript runtime`)}if(t instanceof Uint8Array){let e=t.byteLength<<3;if(e!==r)throw new l.aA(`Invalid Content Encryption Key length. Expected ${r} bits, got ${e} bits`);return}if((0,u.A)(t)&&"secret"===t.type){let e=t.symmetricKeySize<<3;if(e!==r)throw new l.aA(`Invalid Content Encryption Key length. Expected ${r} bits, got ${e} bits`);return}throw TypeError("Invalid Content Encryption Key type")};var h=r(45271);let p=o.timingSafeEqual;var f=r(89605),g=r(25840),y=r(63669);let m=e=>(i||=new Set((0,o.getCiphers)())).has(e);var w=r(69472);let b=(e,t,r,i,s,a)=>{let n;if((0,f.R)(t))(0,g.$)(t,e,"decrypt"),n=o.KeyObject.from(t);else if(t instanceof Uint8Array||(0,u.A)(t))n=t;else throw TypeError((0,y.A)(t,...w.g,"Uint8Array"));if(!i)throw new l.aA("JWE Initialization Vector missing");if(!s)throw new l.aA("JWE Authentication Tag missing");switch(d(e,n),c(e,i),e){case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return function(e,t,r,i,s,a){let n,c,d=parseInt(e.slice(1,4),10);(0,u.A)(t)&&(t=t.export());let f=t.subarray(d>>3),g=t.subarray(0,d>>3),y=parseInt(e.slice(-3),10),w=`aes-${d}-cbc`;if(!m(w))throw new l.T0(`alg ${e} is not supported by your javascript runtime`);let b=function(e,t,r,i,s,a){let n=(0,h.xW)(e,t,r,(0,h.mx)(e.length<<3)),l=(0,o.createHmac)(`sha${i}`,s);return l.update(n),l.digest().slice(0,a>>3)}(a,i,r,y,g,d);try{n=p(s,b)}catch{}if(!n)throw new l.xO;try{let e=(0,o.createDecipheriv)(w,f,i);c=(0,h.xW)(e.update(r),e.final())}catch{}if(!c)throw new l.xO;return c}(e,n,r,i,s,a);case"A128GCM":case"A192GCM":case"A256GCM":return function(e,t,r,i,s,a){let n=parseInt(e.slice(1,4),10),c=`aes-${n}-gcm`;if(!m(c))throw new l.T0(`alg ${e} is not supported by your javascript runtime`);try{let e=(0,o.createDecipheriv)(c,t,i,{authTagLength:16});e.setAuthTag(s),a.byteLength&&e.setAAD(a,{plaintextLength:r.length});let n=e.update(r);return e.final(),n}catch{throw new l.xO}}(e,n,r,i,s,a);default:throw new l.T0("Unsupported JWE Content Encryption Algorithm")}};var _=r(61824),v=r(67407),S=r(4573);let k=(e,t,r)=>{let i=parseInt(e.slice(1,4),10),s=`aes${i}-wrap`;if(!m(s))throw new l.T0(`alg ${e} is not supported either by JOSE or your javascript runtime`);let a=function(e,t,r){if((0,u.A)(e))return e;if(e instanceof Uint8Array)return(0,o.createSecretKey)(e);if((0,f.R)(e))return(0,g.$)(e,t,r),o.KeyObject.from(e);throw TypeError((0,y.A)(e,...w.g,"Uint8Array"))}(t,e,"unwrapKey");!function(e,t){if(e.symmetricKeySize<<3!==parseInt(t.slice(1,4),10))throw TypeError(`Invalid key size for alg: ${t}`)}(a,e);let n=(0,o.createDecipheriv)(s,a,S.Buffer.alloc(8,166));return(0,h.xW)(n.update(r),n.final())};var A=r(57975),E=r(46524);async function T(e,t,r,i,s=new Uint8Array(0),a=new Uint8Array(0)){let n,l;if((0,f.R)(e))(0,g.$)(e,"ECDH"),n=o.KeyObject.from(e);else if((0,u.A)(e))n=e;else throw TypeError((0,y.A)(e,...w.g));if((0,f.R)(t))(0,g.$)(t,"ECDH","deriveBits"),l=o.KeyObject.from(t);else if((0,u.A)(t))l=t;else throw TypeError((0,y.A)(t,...w.g));let c=(0,h.xW)((0,h.Kp)(h.Rd.encode(r)),(0,h.Kp)(s),(0,h.Kp)(a),(0,h.VS)(i)),d=(0,o.diffieHellman)({privateKey:l,publicKey:n});return(0,h.yI)(d,i,c)}(0,A.promisify)(o.generateKeyPair);let P=e=>["P-256","P-384","P-521","X25519","X448"].includes((0,E.A)(e)),x=(0,A.promisify)(o.pbkdf2),I=async(e,t,r,i,s)=>{!function(e){if(!(e instanceof Uint8Array)||e.length<8)throw new l.aA("PBES2 Salt Input must be 8 or more octets")}(s);let a=(0,h.MT)(e,s),n=parseInt(e.slice(13,16),10)>>3,c=function(e,t){if((0,u.A)(e))return e.export();if(e instanceof Uint8Array)return e;if((0,f.R)(e))return(0,g.$)(e,t,"deriveBits","deriveKey"),o.KeyObject.from(e).export();throw TypeError((0,y.A)(e,...w.g,"Uint8Array"))}(t,e),d=await x(c,a,i,n,`sha${e.slice(8,11)}`);return k(e.slice(-6),d,r)};var C=r(13317);let O=(e,t)=>{if("rsa"!==e.asymmetricKeyType)throw TypeError("Invalid key for this operation, its asymmetricKeyType must be rsa");(0,C.A)(e,t)},R=(0,A.deprecate)(()=>o.constants.RSA_PKCS1_PADDING,'The RSA1_5 "alg" (JWE Algorithm) is deprecated and will be removed in the next major revision.'),F=e=>{switch(e){case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":return o.constants.RSA_PKCS1_OAEP_PADDING;case"RSA1_5":return R();default:return}},U=e=>{switch(e){case"RSA-OAEP":return"sha1";case"RSA-OAEP-256":return"sha256";case"RSA-OAEP-384":return"sha384";case"RSA-OAEP-512":return"sha512";default:return}},H=(e,t,r)=>{let i=F(e),s=U(e),a=function(e,t,...r){if((0,u.A)(e))return e;if((0,f.R)(e))return(0,g.$)(e,t,...r),o.KeyObject.from(e);throw TypeError((0,y.A)(e,...w.g))}(t,e,"unwrapKey","decrypt");return O(a,e),(0,o.privateDecrypt)({key:a,oaepHash:s,padding:i},r)},$={};function N(e){switch(e){case"A128GCM":return 128;case"A192GCM":return 192;case"A256GCM":case"A128CBC-HS256":return 256;case"A192CBC-HS384":return 384;case"A256CBC-HS512":return 512;default:throw new l.T0(`Unsupported JWE Algorithm: ${e}`)}}let J=e=>(0,o.randomFillSync)(new Uint8Array(N(e)>>3));var M=r(76285),q=r(70556);async function D(e,t,r,i,s){return b(e.slice(0,7),t,r,i,s,new Uint8Array(0))}async function j(e,t,r,i,s){switch((0,q.A)(e,t,"decrypt"),t=await $.normalizePrivateKey?.(t,e)||t,e){case"dir":if(void 0!==r)throw new l.aA("Encountered unexpected JWE Encrypted Key");return t;case"ECDH-ES":if(void 0!==r)throw new l.aA("Encountered unexpected JWE Encrypted Key");case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":{let s,a;if(!(0,v.A)(i.epk))throw new l.aA('JOSE Header "epk" (Ephemeral Public Key) missing or invalid');if(!P(t))throw new l.T0("ECDH with the provided key is not allowed or not supported by your javascript runtime");let o=await (0,M.Og)(i.epk,e);if(void 0!==i.apu){if("string"!=typeof i.apu)throw new l.aA('JOSE Header "apu" (Agreement PartyUInfo) invalid');try{s=(0,n.D4)(i.apu)}catch{throw new l.aA("Failed to base64url decode the apu")}}if(void 0!==i.apv){if("string"!=typeof i.apv)throw new l.aA('JOSE Header "apv" (Agreement PartyVInfo) invalid');try{a=(0,n.D4)(i.apv)}catch{throw new l.aA("Failed to base64url decode the apv")}}let c=await T(o,t,"ECDH-ES"===e?i.enc:e,"ECDH-ES"===e?N(i.enc):parseInt(e.slice(-5,-2),10),s,a);if("ECDH-ES"===e)return c;if(void 0===r)throw new l.aA("JWE Encrypted Key missing");return k(e.slice(-6),c,r)}case"RSA1_5":case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":if(void 0===r)throw new l.aA("JWE Encrypted Key missing");return H(e,t,r);case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":{let a;if(void 0===r)throw new l.aA("JWE Encrypted Key missing");if("number"!=typeof i.p2c)throw new l.aA('JOSE Header "p2c" (PBES2 Count) missing or invalid');let o=s?.maxPBES2Count||1e4;if(i.p2c>o)throw new l.aA('JOSE Header "p2c" (PBES2 Count) out is of acceptable bounds');if("string"!=typeof i.p2s)throw new l.aA('JOSE Header "p2s" (PBES2 Salt) missing or invalid');try{a=(0,n.D4)(i.p2s)}catch{throw new l.aA("Failed to base64url decode the p2s")}return I(e,t,r,i.p2c,a)}case"A128KW":case"A192KW":case"A256KW":if(void 0===r)throw new l.aA("JWE Encrypted Key missing");return k(e,t,r);case"A128GCMKW":case"A192GCMKW":case"A256GCMKW":{let s,a;if(void 0===r)throw new l.aA("JWE Encrypted Key missing");if("string"!=typeof i.iv)throw new l.aA('JOSE Header "iv" (Initialization Vector) missing or invalid');if("string"!=typeof i.tag)throw new l.aA('JOSE Header "tag" (Authentication Tag) missing or invalid');try{s=(0,n.D4)(i.iv)}catch{throw new l.aA("Failed to base64url decode the iv")}try{a=(0,n.D4)(i.tag)}catch{throw new l.aA("Failed to base64url decode the tag")}return D(e,t,r,s,a)}default:throw new l.T0('Invalid or unsupported "alg" (JWE Algorithm) header value')}}var L=r(49010),K=r(3560);async function W(e,t,r){let i,s,a,o,c,u,d;if(!(0,v.A)(e))throw new l.aA("Flattened JWE must be an object");if(void 0===e.protected&&void 0===e.header&&void 0===e.unprotected)throw new l.aA("JOSE Header missing");if(void 0!==e.iv&&"string"!=typeof e.iv)throw new l.aA("JWE Initialization Vector incorrect type");if("string"!=typeof e.ciphertext)throw new l.aA("JWE Ciphertext missing or incorrect type");if(void 0!==e.tag&&"string"!=typeof e.tag)throw new l.aA("JWE Authentication Tag incorrect type");if(void 0!==e.protected&&"string"!=typeof e.protected)throw new l.aA("JWE Protected Header incorrect type");if(void 0!==e.encrypted_key&&"string"!=typeof e.encrypted_key)throw new l.aA("JWE Encrypted Key incorrect type");if(void 0!==e.aad&&"string"!=typeof e.aad)throw new l.aA("JWE AAD incorrect type");if(void 0!==e.header&&!(0,v.A)(e.header))throw new l.aA("JWE Shared Unprotected Header incorrect type");if(void 0!==e.unprotected&&!(0,v.A)(e.unprotected))throw new l.aA("JWE Per-Recipient Unprotected Header incorrect type");if(e.protected)try{let t=(0,n.D4)(e.protected);i=JSON.parse(h.D0.decode(t))}catch{throw new l.aA("JWE Protected Header is invalid")}if(!(0,_.A)(i,e.header,e.unprotected))throw new l.aA("JWE Protected, JWE Unprotected Header, and JWE Per-Recipient Unprotected Header Parameter names must be disjoint");let p={...i,...e.header,...e.unprotected};if((0,L.A)(l.aA,new Map,r?.crit,i,p),void 0!==p.zip)throw new l.T0('JWE "zip" (Compression Algorithm) Header Parameter is not supported.');let{alg:f,enc:g}=p;if("string"!=typeof f||!f)throw new l.aA("missing JWE Algorithm (alg) in JWE Header");if("string"!=typeof g||!g)throw new l.aA("missing JWE Encryption Algorithm (enc) in JWE Header");let y=r&&(0,K.A)("keyManagementAlgorithms",r.keyManagementAlgorithms),m=r&&(0,K.A)("contentEncryptionAlgorithms",r.contentEncryptionAlgorithms);if(y&&!y.has(f)||!y&&f.startsWith("PBES2"))throw new l.Rb('"alg" (Algorithm) Header Parameter value not allowed');if(m&&!m.has(g))throw new l.Rb('"enc" (Encryption Algorithm) Header Parameter value not allowed');if(void 0!==e.encrypted_key)try{s=(0,n.D4)(e.encrypted_key)}catch{throw new l.aA("Failed to base64url decode the encrypted_key")}let w=!1;"function"==typeof t&&(t=await t(i,e),w=!0);try{a=await j(f,t,s,p,r)}catch(e){if(e instanceof TypeError||e instanceof l.aA||e instanceof l.T0)throw e;a=J(g)}if(void 0!==e.iv)try{o=(0,n.D4)(e.iv)}catch{throw new l.aA("Failed to base64url decode the iv")}if(void 0!==e.tag)try{c=(0,n.D4)(e.tag)}catch{throw new l.aA("Failed to base64url decode the tag")}let S=h.Rd.encode(e.protected??"");u=void 0!==e.aad?(0,h.xW)(S,h.Rd.encode("."),h.Rd.encode(e.aad)):S;try{d=(0,n.D4)(e.ciphertext)}catch{throw new l.aA("Failed to base64url decode the ciphertext")}let k={plaintext:await b(g,a,d,o,c,u)};if(void 0!==e.protected&&(k.protectedHeader=i),void 0!==e.aad)try{k.additionalAuthenticatedData=(0,n.D4)(e.aad)}catch{throw new l.aA("Failed to base64url decode the aad")}return(void 0!==e.unprotected&&(k.sharedUnprotectedHeader=e.unprotected),void 0!==e.header&&(k.unprotectedHeader=e.header),w)?{...k,key:t}:k}async function z(e,t,r){if(e instanceof Uint8Array&&(e=h.D0.decode(e)),"string"!=typeof e)throw new l.aA("Compact JWE must be a string or Uint8Array");let{0:i,1:s,2:a,3:n,4:o,length:c}=e.split(".");if(5!==c)throw new l.aA("Invalid Compact JWE");let u=await W({ciphertext:n,iv:a||void 0,protected:i,tag:o||void 0,encrypted_key:s||void 0},t,r),d={plaintext:u.plaintext,protectedHeader:u.protectedHeader};return"function"==typeof t?{...d,key:u.key}:d}let B=e=>Math.floor(e.getTime()/1e3),V=/^(\+|\-)? ?(\d+|\d+\.\d+) ?(seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)(?: (ago|from now))?$/i,G=e=>{let t,r=V.exec(e);if(!r||r[4]&&r[1])throw TypeError("Invalid time period format");let i=parseFloat(r[2]);switch(r[3].toLowerCase()){case"sec":case"secs":case"second":case"seconds":case"s":t=Math.round(i);break;case"minute":case"minutes":case"min":case"mins":case"m":t=Math.round(60*i);break;case"hour":case"hours":case"hr":case"hrs":case"h":t=Math.round(3600*i);break;case"day":case"days":case"d":t=Math.round(86400*i);break;case"week":case"weeks":case"w":t=Math.round(604800*i);break;default:t=Math.round(0x1e187e0*i)}return"-"===r[1]||"ago"===r[4]?-t:t},Y=e=>e.toLowerCase().replace(/^application\//,""),Q=(e,t)=>"string"==typeof e?t.includes(e):!!Array.isArray(e)&&t.some(Set.prototype.has.bind(new Set(e))),X=(e,t,r={})=>{let i,s;try{i=JSON.parse(h.D0.decode(t))}catch{}if(!(0,v.A)(i))throw new l.Dp("JWT Claims Set must be a top-level JSON object");let{typ:a}=r;if(a&&("string"!=typeof e.typ||Y(e.typ)!==Y(a)))throw new l.ie('unexpected "typ" JWT header value',i,"typ","check_failed");let{requiredClaims:n=[],issuer:o,subject:c,audience:u,maxTokenAge:d}=r,p=[...n];for(let e of(void 0!==d&&p.push("iat"),void 0!==u&&p.push("aud"),void 0!==c&&p.push("sub"),void 0!==o&&p.push("iss"),new Set(p.reverse())))if(!(e in i))throw new l.ie(`missing required "${e}" claim`,i,e,"missing");if(o&&!(Array.isArray(o)?o:[o]).includes(i.iss))throw new l.ie('unexpected "iss" claim value',i,"iss","check_failed");if(c&&i.sub!==c)throw new l.ie('unexpected "sub" claim value',i,"sub","check_failed");if(u&&!Q(i.aud,"string"==typeof u?[u]:u))throw new l.ie('unexpected "aud" claim value',i,"aud","check_failed");switch(typeof r.clockTolerance){case"string":s=G(r.clockTolerance);break;case"number":s=r.clockTolerance;break;case"undefined":s=0;break;default:throw TypeError("Invalid clockTolerance option type")}let{currentDate:f}=r,g=B(f||new Date);if((void 0!==i.iat||d)&&"number"!=typeof i.iat)throw new l.ie('"iat" claim must be a number',i,"iat","invalid");if(void 0!==i.nbf){if("number"!=typeof i.nbf)throw new l.ie('"nbf" claim must be a number',i,"nbf","invalid");if(i.nbf>g+s)throw new l.ie('"nbf" claim timestamp check failed',i,"nbf","check_failed")}if(void 0!==i.exp){if("number"!=typeof i.exp)throw new l.ie('"exp" claim must be a number',i,"exp","invalid");if(i.exp<=g-s)throw new l.n('"exp" claim timestamp check failed',i,"exp","check_failed")}if(d){let e=g-i.iat;if(e-s>("number"==typeof d?d:G(d)))throw new l.n('"iat" claim timestamp check failed (too far in the past)',i,"iat","check_failed");if(e<0-s)throw new l.ie('"iat" claim timestamp check failed (it should be in the past)',i,"iat","check_failed")}return i};async function Z(e,t,r){let i=await z(e,t,r),s=X(i.protectedHeader,i.plaintext,r),{protectedHeader:a}=i;if(void 0!==a.iss&&a.iss!==s.iss)throw new l.ie('replicated "iss" claim header parameter mismatch',s,"iss","mismatch");if(void 0!==a.sub&&a.sub!==s.sub)throw new l.ie('replicated "sub" claim header parameter mismatch',s,"sub","mismatch");if(void 0!==a.aud&&JSON.stringify(a.aud)!==JSON.stringify(s.aud))throw new l.ie('replicated "aud" claim header parameter mismatch',s,"aud","mismatch");let n={payload:s,protectedHeader:a};return"function"==typeof t?{...n,key:i.key}:n}var ee="4.0.1",et=Symbol.for("flags:global-trace"),er=new s.AsyncLocalStorage;function ei(e,t){er.getStore()?.set(e,t)}function es(e,t={name:e.name}){return function(...r){let i=function(){let e=Reflect.get(globalThis,et);return e?.getTracer("flags",ee)}();return i&&("true"===process.env.VERCEL_FLAGS_TRACE_VERBOSE||!1===t.isVerboseTrace)?er.run(new Map,()=>i.startActiveSpan(t.name,i=>{t.attributes&&i.setAttributes(t.attributes);try{let s=e.apply(this,r);return null!==s&&"object"==typeof s&&"then"in s&&"function"==typeof s.then?s.then(e=>{t.attributesSuccess&&i.setAttributes(t.attributesSuccess(e)),er.getStore()?.forEach((e,t)=>{i.setAttribute(t,e)}),i.setStatus({code:1}),i.end()}).catch(e=>{t.attributesError&&i.setAttributes(t.attributesError(e)),i.setStatus({code:2,message:e instanceof Error?e.message:void 0}),er.getStore()?.forEach((e,t)=>{i.setAttribute(t,e)}),i.end()}):(t.attributesSuccess&&i.setAttributes(t.attributesSuccess(s)),er.getStore()?.forEach((e,t)=>{i.setAttribute(t,e)}),i.setStatus({code:1}),i.end()),s}catch(e){throw t.attributesError&&i.setAttributes(t.attributesError(e)),i.setStatus({code:2,message:e instanceof Error?e.message:void 0}),er.getStore()?.forEach((e,t)=>{i.setAttribute(t,e)}),i.end(),e}})):e.apply(this,r)}}var ea=(e,t)=>Array.isArray(e)?e.includes(t):e===t;async function en(e,t,r){if("string"!=typeof e)return;let i=a.D(r);if(32!==i.length)throw Error("flags: Invalid secret, it must be a 256-bit key (32 bytes)");try{let{payload:r}=await Z(e,i);return t(r)?r:void 0}catch{return}}async function eo(e,t=process?.env?.FLAGS_SECRET){if(!t)throw Error("flags: Missing FLAGS_SECRET");let r=await en(e,e=>ea(e.pur,"overrides")&&Object.hasOwn(e,"o"),t);return r?.o}async function el(e,t=process?.env?.FLAGS_SECRET){if(!t)throw Error("flags: Missing FLAGS_SECRET");return!!await en(e,e=>ea(e.pur,"proof"),t)}var ec=es(async function(e,t=process?.env?.FLAGS_SECRET){if(!e)return!1;if(!t)throw Error("flags: verifyAccess was called without a secret. Please set FLAGS_SECRET environment variable.");return await el(e.replace(/^Bearer /i,""),t)},{isVerboseTrace:!1,name:"verifyAccess"});function eu(e,t){let r=Symbol.for("@vercel/request-context"),i=Reflect.get(globalThis,r)?.get();i?.flags?.reportValue(e,t,{sdkVersion:ee})}function ed(e,t,r){let i=Symbol.for("@vercel/request-context"),s=Reflect.get(globalThis,i)?.get();s?.flags?.reportValue(e,t,{sdkVersion:ee,...r})}var eh=class{static get(e,t,r){let i=Reflect.get(e,t,r);return"function"==typeof i?i.bind(e):i}static set(e,t,r,i){return Reflect.set(e,t,r,i)}static has(e,t){return Reflect.has(e,t)}static deleteProperty(e,t){return Reflect.deleteProperty(e,t)}},ep=class e extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new e}},ef=class e extends Headers{constructor(e){super(),this.headers=new Proxy(e,{get(t,r,i){if("symbol"==typeof r)return eh.get(t,r,i);let s=r.toLowerCase(),a=Object.keys(e).find(e=>e.toLowerCase()===s);if(void 0!==a)return eh.get(t,a,i)},set(t,r,i,s){if("symbol"==typeof r)return eh.set(t,r,i,s);let a=r.toLowerCase(),n=Object.keys(e).find(e=>e.toLowerCase()===a);return eh.set(t,n??r,i,s)},has(t,r){if("symbol"==typeof r)return eh.has(t,r);let i=r.toLowerCase(),s=Object.keys(e).find(e=>e.toLowerCase()===i);return void 0!==s&&eh.has(t,s)},deleteProperty(t,r){if("symbol"==typeof r)return eh.deleteProperty(t,r);let i=r.toLowerCase(),s=Object.keys(e).find(e=>e.toLowerCase()===i);return void 0===s||eh.deleteProperty(t,s)}})}static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"append":case"delete":case"set":return ep.callable;default:return eh.get(e,t,r)}}})}merge(e){return Array.isArray(e)?e.join(", "):e}static from(t){return t instanceof Headers?t:new e(t)}append(e,t){let r=this.headers[e];"string"==typeof r?this.headers[e]=[r,t]:Array.isArray(r)?r.push(t):this.headers[e]=t}delete(e){delete this.headers[e]}get(e){let t=this.headers[e];return void 0!==t?this.merge(t):null}has(e){return void 0!==this.headers[e]}set(e,t){this.headers[e]=t}forEach(e,t){for(let[r,i]of this.entries())e.call(t,i,r,this)}*entries(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase(),r=this.get(t);yield[t,r]}}*keys(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase();yield t}}*values(){for(let e of Object.keys(this.headers)){let t=this.get(e);yield t}}[Symbol.iterator](){return this.entries()}},eg=class e extends Error{constructor(){super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#options")}static callable(){throw new e}},ey=class{static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"clear":case"delete":case"set":return eg.callable;default:return eh.get(e,t,r)}}})}};Symbol.for("next.mutated.cookies")},44214:(e,t,r)=>{r.d(t,{D:()=>s});var i=r(13690);i.lF;let s=i.D4},45271:(e,t,r)=>{r.d(t,{xW:()=>o,yI:()=>p,D0:()=>n,Rd:()=>a,Kp:()=>h,MT:()=>l,VS:()=>d,mx:()=>u});var i=r(77598);let s=(e,t)=>(0,i.createHash)(e).update(t).digest(),a=new TextEncoder,n=new TextDecoder;function o(...e){let t=new Uint8Array(e.reduce((e,{length:t})=>e+t,0)),r=0;for(let i of e)t.set(i,r),r+=i.length;return t}function l(e,t){return o(a.encode(e),new Uint8Array([0]),t)}function c(e,t,r){if(t<0||t>=0x100000000)throw RangeError(`value must be >= 0 and <= ${0x100000000-1}. Received ${t}`);e.set([t>>>24,t>>>16,t>>>8,255&t],r)}function u(e){let t=Math.floor(e/0x100000000),r=new Uint8Array(8);return c(r,t,0),c(r,e%0x100000000,4),r}function d(e){let t=new Uint8Array(4);return c(t,e),t}function h(e){return o(d(e.length),e)}async function p(e,t,r){let i=Math.ceil((t>>3)/32),a=new Uint8Array(32*i);for(let t=0;t<i;t++){let i=new Uint8Array(4+e.length+r.length);i.set(d(t+1)),i.set(e,4),i.set(r,4+e.length),a.set(await s("sha256",i),32*t)}return a.slice(0,t>>3)}},46524:(e,t,r)=>{r.d(t,{A:()=>d});var i=r(77598),s=r(14295),a=r(89605),n=r(22556),o=r(63669),l=r(69472),c=r(6798);new WeakMap;let u=e=>{switch(e){case"prime256v1":return"P-256";case"secp384r1":return"P-384";case"secp521r1":return"P-521";case"secp256k1":return"secp256k1";default:throw new s.T0("Unsupported key curve for this operation")}},d=(e,t)=>{let r;if((0,a.R)(e))r=i.KeyObject.from(e);else if((0,n.A)(e))r=e;else if((0,c.ll)(e))return e.crv;else throw TypeError((0,o.A)(e,...l.g));if("secret"===r.type)throw TypeError('only "private" or "public" type keys can be used for this operation');switch(r.asymmetricKeyType){case"ed25519":case"ed448":return`Ed${r.asymmetricKeyType.slice(2)}`;case"x25519":case"x448":return`X${r.asymmetricKeyType.slice(1)}`;case"ec":{let e=r.asymmetricKeyDetails.namedCurve;if(t)return e;return u(e)}default:throw TypeError("Invalid asymmetric key type for this operation")}}},49010:(e,t,r)=>{r.d(t,{A:()=>s});var i=r(14295);let s=function(e,t,r,s,a){let n;if(void 0!==a.crit&&s?.crit===void 0)throw new e('"crit" (Critical) Header Parameter MUST be integrity protected');if(!s||void 0===s.crit)return new Set;if(!Array.isArray(s.crit)||0===s.crit.length||s.crit.some(e=>"string"!=typeof e||0===e.length))throw new e('"crit" (Critical) Header Parameter MUST be an array of non-empty strings when present');for(let o of(n=void 0!==r?new Map([...Object.entries(r),...t.entries()]):t,s.crit)){if(!n.has(o))throw new i.T0(`Extension Header Parameter "${o}" is not recognized`);if(void 0===a[o])throw new e(`Extension Header Parameter "${o}" is missing`);if(n.get(o)&&void 0===s[o])throw new e(`Extension Header Parameter "${o}" MUST be integrity protected`)}return new Set(s.crit)}},51153:(e,t,r)=>{let i;r.d(t,{f2:()=>eO});var s,a,n,o,l,c,u,d,h,p,f,g=r(33873),y=r(73024),m=r(80481);let w="posthog-node";class b{constructor(e,t,r,i){this.name=w,this.name=w,this.setupOnce=function(s,a){s(function(e,{organization:t,projectId:r,prefix:i,severityAllowList:s=["error"]}={}){return a=>{if(!("*"===s||s.includes(a.level)))return a;a.tags||(a.tags={});let n=a.tags[b.POSTHOG_ID_TAG];if(void 0===n)return a;let o=e.options.host??"https://us.i.posthog.com",l=new URL(`/project/${e.apiKey}/person/${n}`,o).toString();a.tags["PostHog Person URL"]=l;let c=a.exception?.values||[],u=c.map(e=>({...e,stacktrace:e.stacktrace?{...e.stacktrace,type:"raw",frames:(e.stacktrace.frames||[]).map(e=>({...e,platform:"node:javascript"}))}:void 0})),d={$exception_message:c[0]?.value||a.message,$exception_type:c[0]?.type,$exception_personURL:l,$exception_level:a.level,$exception_list:u,$sentry_event_id:a.event_id,$sentry_exception:a.exception,$sentry_exception_message:c[0]?.value||a.message,$sentry_exception_type:c[0]?.type,$sentry_tags:a.tags};return t&&r&&(d.$sentry_url=(i||"https://sentry.io/organizations/")+t+"/issues/?project="+r+"&query="+a.event_id),e.capture({event:"$exception",distinctId:n,properties:d}),a}}(e,{organization:t,projectId:a()?.getClient()?.getDsn()?.projectId,prefix:r,severityAllowList:i}))}}}b.POSTHOG_ID_TAG="posthog_distinct_id";let _="0123456789abcdef";class v{constructor(e){this.bytes=e}static ofInner(e){if(16===e.length)return new v(e);throw TypeError("not 128-bit length")}static fromFieldsV7(e,t,r,i){if(!Number.isInteger(e)||!Number.isInteger(t)||!Number.isInteger(r)||!Number.isInteger(i)||e<0||t<0||r<0||i<0||e>0xffffffffffff||t>4095||r>0x3fffffff||i>0xffffffff)throw RangeError("invalid field value");let s=new Uint8Array(16);return s[0]=e/0x10000000000,s[1]=e/0x100000000,s[2]=e/0x1000000,s[3]=e/65536,s[4]=e/256,s[5]=e,s[6]=112|t>>>8,s[7]=t,s[8]=128|r>>>24,s[9]=r>>>16,s[10]=r>>>8,s[11]=r,s[12]=i>>>24,s[13]=i>>>16,s[14]=i>>>8,s[15]=i,new v(s)}static parse(e){let t;switch(e.length){case 32:t=/^[0-9a-f]{32}$/i.exec(e)?.[0];break;case 36:t=/^([0-9a-f]{8})-([0-9a-f]{4})-([0-9a-f]{4})-([0-9a-f]{4})-([0-9a-f]{12})$/i.exec(e)?.slice(1,6).join("");break;case 38:t=/^\{([0-9a-f]{8})-([0-9a-f]{4})-([0-9a-f]{4})-([0-9a-f]{4})-([0-9a-f]{12})\}$/i.exec(e)?.slice(1,6).join("");break;case 45:t=/^urn:uuid:([0-9a-f]{8})-([0-9a-f]{4})-([0-9a-f]{4})-([0-9a-f]{4})-([0-9a-f]{12})$/i.exec(e)?.slice(1,6).join("")}if(t){let e=new Uint8Array(16);for(let r=0;r<16;r+=4){let i=parseInt(t.substring(2*r,2*r+8),16);e[r+0]=i>>>24,e[r+1]=i>>>16,e[r+2]=i>>>8,e[r+3]=i}return new v(e)}throw SyntaxError("could not parse UUID string")}toString(){let e="";for(let t=0;t<this.bytes.length;t++)e+=_.charAt(this.bytes[t]>>>4),e+=_.charAt(15&this.bytes[t]),(3===t||5===t||7===t||9===t)&&(e+="-");return e}toHex(){let e="";for(let t=0;t<this.bytes.length;t++)e+=_.charAt(this.bytes[t]>>>4),e+=_.charAt(15&this.bytes[t]);return e}toJSON(){return this.toString()}getVariant(){let e=this.bytes[8]>>>4;if(e<0)throw Error("unreachable");if(e<=7)return this.bytes.every(e=>0===e)?"NIL":"VAR_0";if(e<=11)return"VAR_10";if(e<=13)return"VAR_110";if(e<=15)return this.bytes.every(e=>255===e)?"MAX":"VAR_RESERVED";else throw Error("unreachable")}getVersion(){return"VAR_10"===this.getVariant()?this.bytes[6]>>>4:void 0}clone(){return new v(this.bytes.slice(0))}equals(e){return 0===this.compareTo(e)}compareTo(e){for(let t=0;t<16;t++){let r=this.bytes[t]-e.bytes[t];if(0!==r)return Math.sign(r)}return 0}}class S{constructor(e){this.timestamp=0,this.counter=0,this.random=e??k()}generate(){return this.generateOrResetCore(Date.now(),1e4)}generateOrAbort(){return this.generateOrAbortCore(Date.now(),1e4)}generateOrResetCore(e,t){let r=this.generateOrAbortCore(e,t);return void 0===r&&(this.timestamp=0,r=this.generateOrAbortCore(e,t)),r}generateOrAbortCore(e,t){if(!Number.isInteger(e)||e<1||e>0xffffffffffff)throw RangeError("`unixTsMs` must be a 48-bit positive integer");if(t<0||t>0xffffffffffff)throw RangeError("`rollbackAllowance` out of reasonable range");if(e>this.timestamp)this.timestamp=e,this.resetCounter();else{if(!(e+t>=this.timestamp))return;this.counter++,this.counter>0x3ffffffffff&&(this.timestamp++,this.resetCounter())}return v.fromFieldsV7(this.timestamp,Math.trunc(this.counter/0x40000000),this.counter&0x40000000-1,this.random.nextUint32())}resetCounter(){this.counter=1024*this.random.nextUint32()+(1023&this.random.nextUint32())}generateV4(){let e=new Uint8Array(Uint32Array.of(this.random.nextUint32(),this.random.nextUint32(),this.random.nextUint32(),this.random.nextUint32()).buffer);return e[6]=64|e[6]>>>4,e[8]=128|e[8]>>>2,v.ofInner(e)}}let k=()=>({nextUint32:()=>65536*Math.trunc(65536*Math.random())+Math.trunc(65536*Math.random())}),A=()=>E().toString(),E=()=>(i||(i=new S)).generate();function T(e){switch(Object.prototype.toString.call(e)){case"[object Error]":case"[object Exception]":case"[object DOMException]":case"[object WebAssembly.Exception]":return!0;default:return P(e,Error)}}function P(e,t){try{return e instanceof t}catch{return!1}}function x(e,t){return Object.prototype.toString.call(e)===`[object ${t}]`}async function I(e,t,r,i){let s=i&&i.mechanism||{handled:!0,type:"generic"},a=function e(t,r,i){let s=function(e,t,r){if(T(t))return t;if(e.synthetic=!0,x(t,"Object")){let e=function(e){for(let t in e)if(Object.prototype.hasOwnProperty.call(e,t)){let r=e[t];if(T(r))return r}}(t);if(e)return e;let i=function(e){if("name"in e&&"string"==typeof e.name){let t=`'${e.name}' captured as exception`;return"message"in e&&"string"==typeof e.message&&(t+=` with message '${e.message}'`),t}if("message"in e&&"string"==typeof e.message)return e.message;let t=function(e,t=40){let r=Object.keys(function(e){return T(e)?{message:e.message,name:e.name,stack:e.stack,...O(e)}:"undefined"!=typeof Event&&P(e,Event)?{type:e.type,target:R(e.target),currentTarget:R(e.currentTarget),...O(e)}:e}(e));r.sort();let i=r[0];if(!i)return"[object has no keys]";if(i.length>=t)return C(i,t);for(let e=r.length;e>0;e--){let i=r.slice(0,e).join(", ");if(!(i.length>t)){if(e===r.length)return i;return C(i,t)}}return""}(e);if(x(e,"ErrorEvent"))return`Event \`ErrorEvent\` captured as exception with message \`${e.message}\``;let r=function(e){try{let t=Object.getPrototypeOf(e);return t?t.constructor.name:void 0}catch(e){}}(e);return`${r&&"Object"!==r?`'${r}'`:"Object"} captured as exception with keys: ${t}`}(t),s=r?.syntheticException||Error(i);return s.message=i,s}let i=r?.syntheticException||Error(t);return i.message=`${t}`,i}(t,r,i);return s.cause?[s,...e(t,s.cause,i)]:[s]}(s,r,i);return{$exception_list:await Promise.all(a.map(async r=>{let i=await F(e,t,r);return i.value=i.value||"",i.type=i.type||"Error",i.mechanism=s,i}))}}function C(e,t=0){return"string"!=typeof e||0===t||e.length<=t?e:`${e.slice(0,t)}...`}function O(e){if("object"!=typeof e||null===e)return{};{let t={};for(let r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t}}function R(e){try{return Object.prototype.toString.call(e)}catch(e){return"<unknown>"}}async function F(e,t,r){let i={type:r.name||r.constructor.name,value:r.message};let s=e(r.stack||"",1);for(let e of t)s=await e(s);return s.length&&(i.stacktrace={frames:s,type:"raw"}),i}class U{static async captureException(e,t,r,i,s){let a={...s};i||(a.$process_person_profile=!1);let n=await I(this.stackParser,this.frameModifiers,t,r);e.capture({event:"$exception",distinctId:i||A(),properties:{...n,...a}})}constructor(e,t){this.client=e,this._exceptionAutocaptureEnabled=t.enableExceptionAutocapture||!1,this.startAutocaptureIfEnabled()}startAutocaptureIfEnabled(){if(this.isEnabled()){var e,t,r;let i;e=this.onException.bind(this),t=this.onFatalError.bind(this),global.process.on("uncaughtException",(i=!1,Object.assign(r=>{let s=global.process.listeners("uncaughtException").filter(e=>"domainUncaughtExceptionClear"!==e.name&&!0!==e._posthogErrorHandler).length;e(r,{mechanism:{type:"onuncaughtexception",handled:!1}}),i||0!==s||(i=!0,t())},{_posthogErrorHandler:!0}))),r=this.onException.bind(this),global.process.on("unhandledRejection",e=>{r(e,{mechanism:{type:"onunhandledrejection",handled:!1}})})}}onException(e,t){U.captureException(this.client,e,t)}async onFatalError(){await this.client.shutdown(2e3)}isEnabled(){return!this.client.isDisabled&&this._exceptionAutocaptureEnabled}}function H(e){return e.replace(/^[A-Z]:/,"").replace(/\\/g,"/")}class ${constructor(e){this._maxSize=e,this._cache=new Map}get(e){let t=this._cache.get(e);if(void 0!==t)return this._cache.delete(e),this._cache.set(e,t),t}set(e,t){this._cache.set(e,t)}reduce(){for(;this._cache.size>=this._maxSize;){let e=this._cache.keys().next().value;e&&this._cache.delete(e)}}}let N=new $(25),J=new $(20);async function M(e){let t={};for(let s=e.length-1;s>=0;s--){var r,i;let a=e[s],n=a?.filename;!(!a||"string"!=typeof n||"number"!=typeof a.lineno||(r=n).startsWith("node:")||r.endsWith(".min.js")||r.endsWith(".min.cjs")||r.endsWith(".min.mjs")||r.startsWith("data:")||void 0!==(i=a).lineno&&i.lineno>1e4||void 0!==i.colno&&i.colno>1e3)&&(t[n]||(t[n]=[]),t[n].push(a.lineno))}let s=Object.keys(t);if(0==s.length)return e;let a=[];for(let e of s){if(J.get(e))continue;let r=t[e];if(!r)continue;r.sort((e,t)=>e-t);let i=function(e){if(!e.length)return[];let t=0,r=e[0];if("number"!=typeof r)return[];let i=D(r),s=[];for(;;){if(t===e.length-1){s.push(i);break}let r=e[t+1];if("number"!=typeof r)break;r<=i[1]?i[1]=r+7:(s.push(i),i=D(r)),t++}return s}(r);if(i.every(t=>(function(e,t){let r=N.get(e);if(void 0===r)return!1;for(let e=t[0];e<=t[1];e++)if(void 0===r[e])return!1;return!0})(e,t)))continue;let s=function(e,t,r){let i=e.get(t);return void 0===i?(e.set(t,r),r):i}(N,e,{});a.push(function(e,t,r){return new Promise(i=>{let s=(0,y.createReadStream)(e),a=(0,m.createInterface)({input:s});function n(){s.destroy(),i()}let o=0,l=0,c=t[0];if(void 0===c)return void n();let u=c[0],d=c[1];function h(){J.set(e,1),a.close(),a.removeAllListeners(),n()}s.on("error",h),a.on("error",h),a.on("close",n),a.on("line",e=>{if(!(++o<u)&&(r[o]=function(e,t){let r=e,i=r.length;if(i<=150)return r;t>i&&(t=i);let s=Math.max(t-60,0);s<5&&(s=0);let a=Math.min(s+140,i);return a>i-5&&(a=i),a===i&&(s=Math.max(a-140,0)),r=r.slice(s,a),s>0&&(r=`...${r}`),a<i&&(r+="..."),r}(e,0),o>=d)){if(l===t.length-1){a.close(),a.removeAllListeners();return}let e=t[++l];if(void 0===e){a.close(),a.removeAllListeners();return}u=e[0],d=e[1]}})})}(e,i,s))}return await Promise.all(a).catch(()=>{}),e&&e.length>0&&function(e,t){for(let r of e)if(r.filename&&void 0===r.context_line&&"number"==typeof r.lineno){let e=t.get(r.filename);if(void 0===e)continue;!function(e,t,r){if(void 0===t.lineno||void 0===r)return;t.pre_context=[];for(let i=j(e);i<e;i++){let e=r[i];if(void 0===e)return void q(t);t.pre_context.push(e)}if(void 0===r[e])return q(t);t.context_line=r[e];let i=function(e){return e+7}(e);t.post_context=[];for(let s=e+1;s<=i;s++){let e=r[s];if(void 0===e)break;t.post_context.push(e)}}(r.lineno,r,e)}}(e,N),N.reduce(),e}function q(e){delete e.pre_context,delete e.context_line,delete e.post_context}function D(e){return[j(e),e+7]}function j(e){return Math.max(1,e-7)}!function(e){e.AnonymousId="anonymous_id",e.DistinctId="distinct_id",e.Props="props",e.FeatureFlagDetails="feature_flag_details",e.FeatureFlags="feature_flags",e.FeatureFlagPayloads="feature_flag_payloads",e.BootstrapFeatureFlagDetails="bootstrap_feature_flag_details",e.BootstrapFeatureFlags="bootstrap_feature_flags",e.BootstrapFeatureFlagPayloads="bootstrap_feature_flag_payloads",e.OverrideFeatureFlags="override_feature_flags",e.Queue="queue",e.OptedOut="opted_out",e.SessionId="session_id",e.SessionLastTimestamp="session_timestamp",e.PersonProperties="person_properties",e.GroupProperties="group_properties",e.InstalledAppBuild="installed_app_build",e.InstalledAppVersion="installed_app_version",e.SessionReplay="session_replay",e.DecideEndpointWasHit="decide_endpoint_was_hit",e.SurveyLastSeenDate="survey_last_seen_date",e.SurveysSeen="surveys_seen",e.Surveys="surveys",e.RemoteConfig="remote_config"}(s||(s={})),function(e){e.Left="left",e.Right="right",e.Center="center"}(a||(a={})),function(e){e.Button="button",e.Tab="tab",e.Selector="selector"}(n||(n={})),function(e){e.Popover="popover",e.API="api",e.Widget="widget"}(o||(o={})),function(e){e.Html="html",e.Text="text"}(l||(l={})),function(e){e.Number="number",e.Emoji="emoji"}(c||(c={})),function(e){e.Open="open",e.MultipleChoice="multiple_choice",e.SingleChoice="single_choice",e.Rating="rating",e.Link="link"}(u||(u={})),function(e){e.NextQuestion="next_question",e.End="end",e.ResponseBased="response_based",e.SpecificQuestion="specific_question"}(d||(d={})),function(e){e.Regex="regex",e.NotRegex="not_regex",e.Exact="exact",e.IsNot="is_not",e.Icontains="icontains",e.NotIcontains="not_icontains"}(h||(h={})),function(e){e.Contains="contains",e.Exact="exact",e.Regex="regex"}(p||(p={}));let L=e=>{if("flags"in e){let t=K(e.flags),r=W(e.flags);return{...e,featureFlags:t,featureFlagPayloads:r}}{let t=e.featureFlags??{},r=Object.fromEntries(Object.entries(e.featureFlagPayloads||{}).map(([e,t])=>[e,B(t)])),i=Object.fromEntries(Object.entries(t).map(([e,t])=>[e,function(e,t,r){return{key:e,enabled:"string"==typeof t||t,variant:"string"==typeof t?t:void 0,reason:void 0,metadata:{id:void 0,version:void 0,payload:r?JSON.stringify(r):void 0,description:void 0}}}(e,t,r[e])]));return{...e,featureFlags:t,featureFlagPayloads:r,flags:i}}},K=e=>Object.fromEntries(Object.entries(e??{}).map(([e,t])=>[e,z(t)]).filter(([,e])=>void 0!==e)),W=e=>{let t=e??{};return Object.fromEntries(Object.keys(t).filter(e=>{let r=t[e];return r.enabled&&r.metadata&&void 0!==r.metadata.payload}).map(e=>{let r=t[e].metadata?.payload;return[e,r?B(r):void 0]}))},z=e=>void 0===e?void 0:e.variant??e.enabled,B=e=>{if("string"!=typeof e)return e;try{return JSON.parse(e)}catch{return e}},V=new Set(["61be3dd8","96f6df5f","8cfdba9b","bf027177","e59430a8","7fa5500b","569798e9","04809ff7","0ebc61a5","32de7f98","3beeb69a","12d34ad9","733853ec","0645bb64","5dcbee21","b1f95fa3","2189e408","82b460c2","3a8cc979","29ef8843","2cdbf767","38084b54","50f9f8de","41d0df91","5c236689","c11aedd3","ada46672","f4331ee1","42fed62a","c957462c","d62f705a","e0162666","01b3e5cf","441cef7f","bb9cafee","8f348eb0","b2553f3a","97469d7d","39f21a76","03706dcc","27d50569","307584a7","6433e92e","150c7fbb","49f57f22","3772f65b","01eb8256","3c9e9234","f853c7f7","c0ac4b67","cd609d40","10ca9b1a","8a87f11b","8e8e5216","1f6b63b3","db7943dd","79b7164c","07f78e33","2d21b6fd","952db5ee","a7d3b43f","1924dd9c","84e1b8f6","dff631b6","c5aa8a79","fa133a95","498a4508","24748755","98f3d658","21bbda67","7dbfed69","be3ec24c","fc80b8e2","75cc0998"]);async function G(e,t){let r=null;for(let i=0;i<t.retryCount+1;i++){i>0&&await new Promise(e=>setTimeout(e,t.retryDelay));try{return await e()}catch(e){if(r=e,!t.retryCheck(e))throw e}}throw r}function Y(){return new Date().getTime()}function Q(){return new Date().toISOString()}function X(e,t){let r=setTimeout(e,t);return r?.unref&&r?.unref(),r}function Z(e){return Promise.all(e.map(e=>(e??Promise.resolve()).then(e=>({status:"fulfilled",value:e}),e=>({status:"rejected",reason:e}))))}let ee=String.fromCharCode,et="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",er={},ei={compressToBase64:function(e){if(null==e)return"";let t=ei._compress(e,6,function(e){return et.charAt(e)});switch(t.length%4){default:case 0:return t;case 1:return t+"===";case 2:return t+"==";case 3:return t+"="}},decompressFromBase64:function(e){return null==e?"":""==e?null:ei._decompress(e.length,32,function(t){var r=e.charAt(t);if(!er[et]){er[et]={};for(let e=0;e<et.length;e++)er[et][et.charAt(e)]=e}return er[et][r]})},compress:function(e){return ei._compress(e,16,function(e){return ee(e)})},_compress:function(e,t,r){if(null==e)return"";let i={},s={},a=[],n,o,l="",c="",u="",d=2,h=3,p=2,f=0,g=0,y;for(y=0;y<e.length;y+=1)if(l=e.charAt(y),Object.prototype.hasOwnProperty.call(i,l)||(i[l]=h++,s[l]=!0),c=u+l,Object.prototype.hasOwnProperty.call(i,c))u=c;else{if(Object.prototype.hasOwnProperty.call(s,u)){if(256>u.charCodeAt(0)){for(n=0;n<p;n++)f<<=1,g==t-1?(g=0,a.push(r(f)),f=0):g++;for(n=0,o=u.charCodeAt(0);n<8;n++)f=f<<1|1&o,g==t-1?(g=0,a.push(r(f)),f=0):g++,o>>=1}else{for(n=0,o=1;n<p;n++)f=f<<1|o,g==t-1?(g=0,a.push(r(f)),f=0):g++,o=0;for(n=0,o=u.charCodeAt(0);n<16;n++)f=f<<1|1&o,g==t-1?(g=0,a.push(r(f)),f=0):g++,o>>=1}0==--d&&(d=Math.pow(2,p),p++),delete s[u]}else for(n=0,o=i[u];n<p;n++)f=f<<1|1&o,g==t-1?(g=0,a.push(r(f)),f=0):g++,o>>=1;0==--d&&(d=Math.pow(2,p),p++),i[c]=h++,u=String(l)}if(""!==u){if(Object.prototype.hasOwnProperty.call(s,u)){if(256>u.charCodeAt(0)){for(n=0;n<p;n++)f<<=1,g==t-1?(g=0,a.push(r(f)),f=0):g++;for(n=0,o=u.charCodeAt(0);n<8;n++)f=f<<1|1&o,g==t-1?(g=0,a.push(r(f)),f=0):g++,o>>=1}else{for(n=0,o=1;n<p;n++)f=f<<1|o,g==t-1?(g=0,a.push(r(f)),f=0):g++,o=0;for(n=0,o=u.charCodeAt(0);n<16;n++)f=f<<1|1&o,g==t-1?(g=0,a.push(r(f)),f=0):g++,o>>=1}0==--d&&(d=Math.pow(2,p),p++),delete s[u]}else for(n=0,o=i[u];n<p;n++)f=f<<1|1&o,g==t-1?(g=0,a.push(r(f)),f=0):g++,o>>=1;0==--d&&(d=Math.pow(2,p),p++)}for(n=0,o=2;n<p;n++)f=f<<1|1&o,g==t-1?(g=0,a.push(r(f)),f=0):g++,o>>=1;for(;;){if(f<<=1,g==t-1){a.push(r(f));break}g++}return a.join("")},decompress:function(e){return null==e?"":""==e?null:ei._decompress(e.length,32768,function(t){return e.charCodeAt(t)})},_decompress:function(e,t,r){let i=[],s=[],a={val:r(0),position:t,index:1},n=4,o=4,l=3,c="",u,d,h,p,f,g,y;for(u=0;u<3;u+=1)i[u]=u;for(h=0,f=4,g=1;g!=f;)p=a.val&a.position,a.position>>=1,0==a.position&&(a.position=t,a.val=r(a.index++)),h|=(p>0)*g,g<<=1;switch(h){case 0:for(h=0,f=256,g=1;g!=f;)p=a.val&a.position,a.position>>=1,0==a.position&&(a.position=t,a.val=r(a.index++)),h|=(p>0)*g,g<<=1;y=ee(h);break;case 1:for(h=0,f=65536,g=1;g!=f;)p=a.val&a.position,a.position>>=1,0==a.position&&(a.position=t,a.val=r(a.index++)),h|=(p>0)*g,g<<=1;y=ee(h);break;case 2:return""}for(i[3]=y,d=y,s.push(y);;){if(a.index>e)return"";for(h=0,f=Math.pow(2,l),g=1;g!=f;)p=a.val&a.position,a.position>>=1,0==a.position&&(a.position=t,a.val=r(a.index++)),h|=(p>0)*g,g<<=1;switch(y=h){case 0:for(h=0,f=256,g=1;g!=f;)p=a.val&a.position,a.position>>=1,0==a.position&&(a.position=t,a.val=r(a.index++)),h|=(p>0)*g,g<<=1;i[o++]=ee(h),y=o-1,n--;break;case 1:for(h=0,f=65536,g=1;g!=f;)p=a.val&a.position,a.position>>=1,0==a.position&&(a.position=t,a.val=r(a.index++)),h|=(p>0)*g,g<<=1;i[o++]=ee(h),y=o-1,n--;break;case 2:return s.join("")}if(0==n&&(n=Math.pow(2,l),l++),i[y])c=i[y];else{if(y!==o)return null;c=d+d.charAt(0)}s.push(c),i[o++]=d+c.charAt(0),n--,d=c,0==n&&(n=Math.pow(2,l),l++)}}};class es{constructor(){this.events={},this.events={}}on(e,t){return this.events[e]||(this.events[e]=[]),this.events[e].push(t),()=>{this.events[e]=this.events[e].filter(e=>e!==t)}}emit(e,t){for(let r of this.events[e]||[])r(t);for(let r of this.events["*"]||[])r(e,t)}}class ea extends Error{constructor(e,t){super("HTTP error while fetching PostHog: status="+e.status+", reqByteLength="+t),this.response=e,this.reqByteLength=t,this.name="PostHogFetchHttpError"}get status(){return this.response.status}get text(){return this.response.text()}get json(){return this.response.json()}}class en extends Error{constructor(e){super("Network error while fetching PostHog",e instanceof Error?{cause:e}:{}),this.error=e,this.name="PostHogFetchNetworkError"}}async function eo(e){if(e instanceof ea){let t="";try{t=await e.text}catch{}console.error(`Error while flushing PostHog: message=${e.message}, response body=${t}`,e)}else console.error("Error while flushing PostHog",e);return Promise.resolve()}function el(e){return"object"==typeof e&&(e instanceof ea||e instanceof en)}function ec(e){return"object"==typeof e&&e instanceof ea&&413===e.status}!function(e){e.FeatureFlags="feature_flags",e.Recordings="recordings"}(f||(f={}));class eu{constructor(e,t){var r;this.flushPromise=null,this.shutdownPromise=null,this.pendingPromises={},this._events=new es,this._isInitialized=!1,function(e,t){if(!e||"string"!=typeof e||0===e.trim().length)throw Error(t)}(e,"You must pass your PostHog project's api key."),this.apiKey=e,this.host=(r=t?.host||"https://us.i.posthog.com",r?.replace(/\/+$/,"")),this.flushAt=t?.flushAt?Math.max(t?.flushAt,1):20,this.maxBatchSize=Math.max(this.flushAt,t?.maxBatchSize??100),this.maxQueueSize=Math.max(this.flushAt,t?.maxQueueSize??1e3),this.flushInterval=t?.flushInterval??1e4,this.captureMode=t?.captureMode||"json",this.preloadFeatureFlags=t?.preloadFeatureFlags??!0,this.defaultOptIn=t?.defaultOptIn??!0,this.disableSurveys=t?.disableSurveys??!1,this._retryOptions={retryCount:t?.fetchRetryCount??3,retryDelay:t?.fetchRetryDelay??3e3,retryCheck:el},this.requestTimeout=t?.requestTimeout??1e4,this.featureFlagsRequestTimeoutMs=t?.featureFlagsRequestTimeoutMs??3e3,this.remoteConfigRequestTimeoutMs=t?.remoteConfigRequestTimeoutMs??3e3,this.disableGeoip=t?.disableGeoip??!0,this.disabled=t?.disabled??!1,this.historicalMigration=t?.historicalMigration??!1,this._initPromise=Promise.resolve(),this._isInitialized=!0}logMsgIfDebug(e){this.isDebug&&e()}wrap(e){return this.disabled?void this.logMsgIfDebug(()=>console.warn("[PostHog] The client is disabled")):this._isInitialized?e():void this._initPromise.then(()=>e())}getCommonEventProperties(){return{$lib:this.getLibraryId(),$lib_version:this.getLibraryVersion()}}get optedOut(){return this.getPersistedProperty(s.OptedOut)??!this.defaultOptIn}async optIn(){this.wrap(()=>{this.setPersistedProperty(s.OptedOut,!1)})}async optOut(){this.wrap(()=>{this.setPersistedProperty(s.OptedOut,!0)})}on(e,t){return this._events.on(e,t)}debug(e=!0){if(this.removeDebugCallback?.(),e){let e=this.on("*",(e,t)=>console.log("PostHog Debug",e,t));this.removeDebugCallback=()=>{e(),this.removeDebugCallback=void 0}}}get isDebug(){return!!this.removeDebugCallback}get isDisabled(){return this.disabled}buildPayload(e){return{distinct_id:e.distinct_id,event:e.event,properties:{...e.properties||{},...this.getCommonEventProperties()}}}addPendingPromise(e){let t=A();return this.pendingPromises[t]=e,e.catch(()=>{}).finally(()=>{delete this.pendingPromises[t]}),e}identifyStateless(e,t,r){this.wrap(()=>{let i={...this.buildPayload({distinct_id:e,event:"$identify",properties:t})};this.enqueue("identify",i,r)})}async identifyStatelessImmediate(e,t,r){let i={...this.buildPayload({distinct_id:e,event:"$identify",properties:t})};await this.sendImmediate("identify",i,r)}captureStateless(e,t,r,i){this.wrap(()=>{let s=this.buildPayload({distinct_id:e,event:t,properties:r});this.enqueue("capture",s,i)})}async captureStatelessImmediate(e,t,r,i){let s=this.buildPayload({distinct_id:e,event:t,properties:r});await this.sendImmediate("capture",s,i)}aliasStateless(e,t,r,i){this.wrap(()=>{let s=this.buildPayload({event:"$create_alias",distinct_id:t,properties:{...r||{},distinct_id:t,alias:e}});this.enqueue("alias",s,i)})}async aliasStatelessImmediate(e,t,r,i){let s=this.buildPayload({event:"$create_alias",distinct_id:t,properties:{...r||{},distinct_id:t,alias:e}});await this.sendImmediate("alias",s,i)}groupIdentifyStateless(e,t,r,i,s,a){this.wrap(()=>{let n=this.buildPayload({distinct_id:s||`$${e}_${t}`,event:"$groupidentify",properties:{$group_type:e,$group_key:t,$group_set:r||{},...a||{}}});this.enqueue("capture",n,i)})}async getRemoteConfig(){await this._initPromise;let e=this.host;"https://us.i.posthog.com"===e?e="https://us-assets.i.posthog.com":"https://eu.i.posthog.com"===e&&(e="https://eu-assets.i.posthog.com");let t=`${e}/array/${this.apiKey}/config`,r={method:"GET",headers:{...this.getCustomHeaders(),"Content-Type":"application/json"}};return this.fetchWithRetry(t,r,{retryCount:0},this.remoteConfigRequestTimeoutMs).then(e=>e.json()).catch(e=>{this.logMsgIfDebug(()=>console.error("Remote config could not be loaded",e)),this._events.emit("error",e)})}async getDecide(e,t={},r={},i={},s={}){await this._initPromise;let a=!function(e,t=0,r){let i=function(e){let t=0x811c9dc5;for(let r=0;r<e.length;r++)t^=e.charCodeAt(r),t+=(t<<1)+(t<<4)+(t<<7)+(t<<8)+(t<<24);return(t>>>0).toString(16).padStart(8,"0")}(e);return!r?.has(i)&&parseInt(i,16)/0xffffffff<t}(this.apiKey,1,V)?`${this.host}/decide/?v=4`:`${this.host}/flags/?v=2`,n={method:"POST",headers:{...this.getCustomHeaders(),"Content-Type":"application/json"},body:JSON.stringify({token:this.apiKey,distinct_id:e,groups:t,person_properties:r,group_properties:i,...s})};return this.logMsgIfDebug(()=>console.log("PostHog Debug","Decide URL",a)),this.fetchWithRetry(a,n,{retryCount:0},this.featureFlagsRequestTimeoutMs).then(e=>e.json()).then(e=>L(e)).catch(e=>{this._events.emit("error",e)})}async getFeatureFlagStateless(e,t,r={},i={},s={},a){await this._initPromise;let n=await this.getFeatureFlagDetailStateless(e,t,r,i,s,a);if(void 0===n)return{response:void 0,requestId:void 0};let o=z(n.response);return void 0===o&&(o=!1),{response:o,requestId:n.requestId}}async getFeatureFlagDetailStateless(e,t,r={},i={},s={},a){await this._initPromise;let n=await this.getFeatureFlagDetailsStateless(t,r,i,s,a,[e]);if(void 0!==n)return{response:n.flags[e],requestId:n.requestId}}async getFeatureFlagPayloadStateless(e,t,r={},i={},s={},a){await this._initPromise;let n=await this.getFeatureFlagPayloadsStateless(t,r,i,s,a,[e]);if(!n)return;let o=n[e];return void 0===o?null:o}async getFeatureFlagPayloadsStateless(e,t={},r={},i={},s,a){return await this._initPromise,(await this.getFeatureFlagsAndPayloadsStateless(e,t,r,i,s,a)).payloads}async getFeatureFlagsStateless(e,t={},r={},i={},s,a){return await this._initPromise,await this.getFeatureFlagsAndPayloadsStateless(e,t,r,i,s,a)}async getFeatureFlagsAndPayloadsStateless(e,t={},r={},i={},s,a){await this._initPromise;let n=await this.getFeatureFlagDetailsStateless(e,t,r,i,s,a);return n?{flags:n.featureFlags,payloads:n.featureFlagPayloads,requestId:n.requestId}:{flags:void 0,payloads:void 0,requestId:void 0}}async getFeatureFlagDetailsStateless(e,t={},r={},i={},s,a){await this._initPromise;let n={};(s??this.disableGeoip)&&(n.geoip_disable=!0),a&&(n.flag_keys_to_evaluate=a);let o=await this.getDecide(e,t,r,i,n);if(void 0!==o)return(o.errorsWhileComputingFlags&&console.error("[FEATURE FLAGS] Error while computing feature flags, some flags may be missing or incorrect. Learn more at https://posthog.com/docs/feature-flags/best-practices"),o.quotaLimited?.includes(f.FeatureFlags))?(console.warn("[FEATURE FLAGS] Feature flags quota limit exceeded - feature flags unavailable. Learn more about billing limits at https://posthog.com/docs/billing/limits-alerts"),{flags:{},featureFlags:{},featureFlagPayloads:{},requestId:o?.requestId}):o}async getSurveysStateless(){if(await this._initPromise,!0===this.disableSurveys)return this.logMsgIfDebug(()=>console.log("PostHog Debug","Loading surveys is disabled.")),[];let e=`${this.host}/api/surveys/?token=${this.apiKey}`,t={method:"GET",headers:{...this.getCustomHeaders(),"Content-Type":"application/json"}},r=await this.fetchWithRetry(e,t).then(e=>{if(200!==e.status||!e.json){let t=`Surveys API could not be loaded: ${e.status}`,r=Error(t);this.logMsgIfDebug(()=>console.error(r)),this._events.emit("error",Error(t));return}return e.json()}).catch(e=>{this.logMsgIfDebug(()=>console.error("Surveys API could not be loaded",e)),this._events.emit("error",e)}),i=r?.surveys;return i&&this.logMsgIfDebug(()=>console.log("PostHog Debug","Surveys fetched from API: ",JSON.stringify(i))),i??[]}get props(){return this._props||(this._props=this.getPersistedProperty(s.Props)),this._props||{}}set props(e){this._props=e}async register(e){this.wrap(()=>{this.props={...this.props,...e},this.setPersistedProperty(s.Props,this.props)})}async unregister(e){this.wrap(()=>{delete this.props[e],this.setPersistedProperty(s.Props,this.props)})}enqueue(e,t,r){this.wrap(()=>{if(this.optedOut)return void this._events.emit(e,"Library is disabled. Not sending event. To re-enable, call posthog.optIn()");let i=this.prepareMessage(e,t,r),a=this.getPersistedProperty(s.Queue)||[];a.length>=this.maxQueueSize&&(a.shift(),this.logMsgIfDebug(()=>console.info("Queue is full, the oldest event is dropped."))),a.push({message:i}),this.setPersistedProperty(s.Queue,a),this._events.emit(e,i),a.length>=this.flushAt&&this.flushBackground(),this.flushInterval&&!this._flushTimer&&(this._flushTimer=X(()=>this.flushBackground(),this.flushInterval))})}async sendImmediate(e,t,r){if(this.disabled)return void this.logMsgIfDebug(()=>console.warn("[PostHog] The client is disabled"));if(this._isInitialized||await this._initPromise,this.optedOut)return void this._events.emit(e,"Library is disabled. Not sending event. To re-enable, call posthog.optIn()");let i={api_key:this.apiKey,batch:[this.prepareMessage(e,t,r)],sent_at:Q()};this.historicalMigration&&(i.historical_migration=!0);let s=JSON.stringify(i),a="form"===this.captureMode?`${this.host}/e/?ip=1&_=${Y()}&v=${this.getLibraryVersion()}`:`${this.host}/batch/`,n="form"===this.captureMode?{method:"POST",mode:"no-cors",credentials:"omit",headers:{...this.getCustomHeaders(),"Content-Type":"application/x-www-form-urlencoded"},body:`data=${encodeURIComponent(ei.compressToBase64(s))}&compression=lz64`}:{method:"POST",headers:{...this.getCustomHeaders(),"Content-Type":"application/json"},body:s};try{await this.fetchWithRetry(a,n)}catch(e){this._events.emit("error",e)}}prepareMessage(e,t,r){let i={...t,type:e,library:this.getLibraryId(),library_version:this.getLibraryVersion(),timestamp:r?.timestamp?r?.timestamp:Q(),uuid:r?.uuid?r.uuid:A()};return(r?.disableGeoip??this.disableGeoip)&&(i.properties||(i.properties={}),i.properties.$geoip_disable=!0),i.distinctId&&(i.distinct_id=i.distinctId,delete i.distinctId),i}clearFlushTimer(){this._flushTimer&&(clearTimeout(this._flushTimer),this._flushTimer=void 0)}flushBackground(){this.flush().catch(async e=>{await eo(e)})}async flush(){let e=Z([this.flushPromise]).then(()=>this._flush());return this.flushPromise=e,this.addPendingPromise(e),Z([e]).then(()=>{this.flushPromise===e&&(this.flushPromise=null)}),e}getCustomHeaders(){let e=this.getCustomUserAgent(),t={};return e&&""!==e&&(t["User-Agent"]=e),t}async _flush(){this.clearFlushTimer(),await this._initPromise;let e=this.getPersistedProperty(s.Queue)||[];if(!e.length)return;let t=[],r=e.length;for(;e.length>0&&t.length<r;){let r=e.slice(0,this.maxBatchSize),i=r.map(e=>e.message),a=()=>{let t=(this.getPersistedProperty(s.Queue)||[]).slice(r.length);this.setPersistedProperty(s.Queue,t),e=t},n={api_key:this.apiKey,batch:i,sent_at:Q()};this.historicalMigration&&(n.historical_migration=!0);let o=JSON.stringify(n),l="form"===this.captureMode?`${this.host}/e/?ip=1&_=${Y()}&v=${this.getLibraryVersion()}`:`${this.host}/batch/`,c="form"===this.captureMode?{method:"POST",mode:"no-cors",credentials:"omit",headers:{...this.getCustomHeaders(),"Content-Type":"application/x-www-form-urlencoded"},body:`data=${encodeURIComponent(ei.compressToBase64(o))}&compression=lz64`}:{method:"POST",headers:{...this.getCustomHeaders(),"Content-Type":"application/json"},body:o},u={retryCheck:e=>!ec(e)&&el(e)};try{await this.fetchWithRetry(l,c,u)}catch(e){if(ec(e)&&i.length>1){this.maxBatchSize=Math.max(1,Math.floor(i.length/2)),this.logMsgIfDebug(()=>console.warn(`Received 413 when sending batch of size ${i.length}, reducing batch size to ${this.maxBatchSize}`));continue}throw e instanceof en||a(),this._events.emit("error",e),e}a(),t.push(...i)}this._events.emit("flush",t)}async fetchWithRetry(e,t,r,i){var s;(s=AbortSignal).timeout??(s.timeout=function(e){let t=new AbortController;return setTimeout(()=>t.abort(),e),t.signal});let a=t.body?t.body:"",n=-1;try{n=Buffer.byteLength(a,"utf8")}catch{n=new TextEncoder().encode(a).length}return await G(async()=>{let r=null;try{r=await this.fetch(e,{signal:AbortSignal.timeout(i??this.requestTimeout),...t})}catch(e){throw new en(e)}if("no-cors"!==t.mode&&(r.status<200||r.status>=400))throw new ea(r,n);return r},{...this._retryOptions,...r})}async _shutdown(e=3e4){await this._initPromise;let t=!1;this.clearFlushTimer();let r=async()=>{try{for(await Promise.all(Object.values(this.pendingPromises));;){let e=this.getPersistedProperty(s.Queue)||[];if(0===e.length||(await this.flush(),t))break}}catch(e){if(!el(e))throw e;await eo(e)}};return Promise.race([new Promise((r,i)=>{X(()=>{this.logMsgIfDebug(()=>console.error("Timed out while shutting down PostHog")),t=!0,i("Timeout while shutting down PostHog. Some events may not have been sent.")},e)}),r()])}async shutdown(e=3e4){return this.shutdownPromise?this.logMsgIfDebug(()=>console.warn("shutdown() called while already shutting down. shutdown() is meant to be called once before process exit - use flush() for per-request cleanup")):this.shutdownPromise=this._shutdown(e).finally(()=>{this.shutdownPromise=null}),this.shutdownPromise}}let ed="undefined"!=typeof fetch?fetch:void 0!==globalThis.fetch?globalThis.fetch:void 0;if(!ed){let e=require("axios");ed=async(t,r)=>{let i=await e.request({url:t,headers:r.headers,method:r.method.toLowerCase(),data:r.body,signal:r.signal,validateStatus:()=>!0});return{status:i.status,text:async()=>i.data,json:async()=>i.data}}}var eh=ed;class ep{constructor(e){this.factory=e}async getValue(){return void 0!==this.value?this.value:(void 0===this.initializationPromise&&(this.initializationPromise=(async()=>{try{let e=await this.factory();return this.value=e,e}finally{this.initializationPromise=void 0}})()),this.initializationPromise)}isInitialized(){return void 0!==this.value}async waitForInitialization(){this.isInitialized()||await this.getValue()}}let ef=new ep(async()=>{try{return await Promise.resolve().then(r.t.bind(r,55511,19))}catch{return}});async function eg(){return await ef.getValue()}let ey=new ep(async()=>{if(void 0!==globalThis.crypto?.subtle)return globalThis.crypto.subtle;try{let e=await ef.getValue();if(e?.webcrypto?.subtle)return e.webcrypto.subtle}catch{}});async function em(){return await ey.getValue()}async function ew(e){let t=await eg();if(t)return t.createHash("sha1").update(e).digest("hex");let r=await em();if(r)return Array.from(new Uint8Array(await r.digest("SHA-1",new TextEncoder().encode(e)))).map(e=>e.toString(16).padStart(2,"0")).join("");throw Error("No crypto implementation available. Tried Node Crypto API and Web SubtleCrypto API")}let eb=["is_not"];class e_ extends Error{constructor(e){super(),Error.captureStackTrace(this,this.constructor),this.name="ClientError",this.message=e,Object.setPrototypeOf(this,e_.prototype)}}class ev extends Error{constructor(e){super(e),this.name=this.constructor.name,Error.captureStackTrace(this,this.constructor),Object.setPrototypeOf(this,ev.prototype)}}class eS{constructor({pollingInterval:e,personalApiKey:t,projectApiKey:r,timeout:i,host:s,customHeaders:a,...n}){this.debugMode=!1,this.shouldBeginExponentialBackoff=!1,this.backOffCount=0,this.pollingInterval=e,this.personalApiKey=t,this.featureFlags=[],this.featureFlagsByKey={},this.groupTypeMapping={},this.cohorts={},this.loadedSuccessfullyOnce=!1,this.timeout=i,this.projectApiKey=r,this.host=s,this.poller=void 0,this.fetch=n.fetch||eh,this.onError=n.onError,this.customHeaders=a,this.onLoad=n.onLoad,this.loadFeatureFlags()}debug(e=!0){this.debugMode=e}logMsgIfDebug(e){this.debugMode&&e()}async getFeatureFlag(e,t,r={},i={},s={}){let a,n;if(await this.loadFeatureFlags(),!this.loadedSuccessfullyOnce)return a;for(let t of this.featureFlags)if(e===t.key){n=t;break}if(void 0!==n)try{a=await this.computeFlagLocally(n,t,r,i,s),this.logMsgIfDebug(()=>console.debug(`Successfully computed flag locally: ${e} -> ${a}`))}catch(t){t instanceof ev?this.logMsgIfDebug(()=>console.debug(`InconclusiveMatchError when computing flag locally: ${e}: ${t}`)):t instanceof Error&&this.onError?.(Error(`Error computing flag locally: ${e}: ${t}`))}return a}async computeFeatureFlagPayloadLocally(e,t){let r;if(await this.loadFeatureFlags(),this.loadedSuccessfullyOnce){if("boolean"==typeof t?r=this.featureFlagsByKey?.[e]?.filters?.payloads?.[t.toString()]:"string"==typeof t&&(r=this.featureFlagsByKey?.[e]?.filters?.payloads?.[t]),null==r)return null;try{return JSON.parse(r)}catch{return r}}}async getAllFlagsAndPayloads(e,t={},r={},i={}){await this.loadFeatureFlags();let s={},a={},n=0==this.featureFlags.length;return await Promise.all(this.featureFlags.map(async o=>{try{let n=await this.computeFlagLocally(o,e,t,r,i);s[o.key]=n;let l=await this.computeFeatureFlagPayloadLocally(o.key,n);l&&(a[o.key]=l)}catch(e){e instanceof ev||e instanceof Error&&this.onError?.(Error(`Error computing flag locally: ${o.key}: ${e}`)),n=!0}})),{response:s,payloads:a,fallbackToDecide:n}}async computeFlagLocally(e,t,r={},i={},s={}){if(e.ensure_experience_continuity)throw new ev("Flag has experience continuity enabled");if(!e.active)return!1;let a=(e.filters||{}).aggregation_group_type_index;if(void 0==a)return await this.matchFeatureFlagProperties(e,t,i);{let t=this.groupTypeMapping[String(a)];if(!t)throw this.logMsgIfDebug(()=>console.warn(`[FEATURE FLAGS] Unknown group type index ${a} for feature flag ${e.key}`)),new ev("Flag has unknown group type index");if(!(t in r))return this.logMsgIfDebug(()=>console.warn(`[FEATURE FLAGS] Can't compute group feature flag: ${e.key} without group names passed in`)),!1;let i=s[t];return await this.matchFeatureFlagProperties(e,r[t],i)}}async matchFeatureFlagProperties(e,t,r){let i,s=e.filters||{},a=s.groups||[],n=!1;for(let o of[...a].sort((e,t)=>{let r=!!e.variant,i=!!t.variant;return r&&i?0:r?-1:1*!!i}))try{if(await this.isConditionMatch(e,t,o,r)){let r=o.variant,a=s.multivariate?.variants||[];i=r&&a.some(e=>e.key===r)?r:await this.getMatchingVariant(e,t)||!0;break}}catch(e){if(e instanceof ev)n=!0;else throw e}if(void 0!==i)return i;if(n)throw new ev("Can't determine if feature flag is enabled or not with given properties");return!1}async isConditionMatch(e,t,r,i){let s=r.rollout_percentage,a=e=>{this.logMsgIfDebug(()=>console.warn(e))};if((r.properties||[]).length>0){for(let e of r.properties){let t=e.type,r=!1;if(!("cohort"===t?function e(t,r,i,s=!1){let a=String(t.value);if(!(a in i))throw new ev("can't match cohort without a given cohort property value");return function t(r,i,s,a=!1){if(!r)return!0;let n=r.type,o=r.values;if(!o||0===o.length)return!0;let l=!1;if("values"in o[0]){for(let e of o)try{let r=t(e,i,s,a);if("AND"===n){if(!r)return!1}else if(r)return!0}catch(t){if(t instanceof ev)a&&console.debug(`Failed to compute property ${e} locally: ${t}`),l=!0;else throw t}if(l)throw new ev("Can't match cohort without a given cohort property value");return"AND"===n}for(let t of o)try{let r;r="cohort"===t.type?e(t,i,s,a):eA(t,i);let o=t.negation||!1;if("AND"===n){if(!r&&!o||r&&o)return!1}else if(r&&!o||!r&&o)return!0}catch(e){if(e instanceof ev)a&&console.debug(`Failed to compute property ${t} locally: ${e}`),l=!0;else throw e}if(l)throw new ev("can't match cohort without a given cohort property value");return"AND"===n}(i[a],r,i,s)}(e,i,this.cohorts,this.debugMode):eA(e,i,a)))return!1}if(void 0==s)return!0}return!(void 0!=s&&await ek(e.key,t)>s/100)}async getMatchingVariant(e,t){let r=await ek(e.key,t,"variant"),i=this.variantLookupTable(e).find(e=>r>=e.valueMin&&r<e.valueMax);if(i)return i.key}variantLookupTable(e){let t=[],r=0,i=0,s=e.filters||{};return(s.multivariate?.variants||[]).forEach(e=>{i=r+e.rollout_percentage/100,t.push({valueMin:r,valueMax:i,key:e.key}),r=i}),t}async loadFeatureFlags(e=!1){(!this.loadedSuccessfullyOnce||e)&&await this._loadFeatureFlags()}isLocalEvaluationReady(){return(this.loadedSuccessfullyOnce??!1)&&(this.featureFlags?.length??0)>0}getPollingInterval(){return this.shouldBeginExponentialBackoff?Math.min(6e4,this.pollingInterval*2**this.backOffCount):this.pollingInterval}async _loadFeatureFlags(){this.poller&&(clearTimeout(this.poller),this.poller=void 0),this.poller=setTimeout(()=>this._loadFeatureFlags(),this.getPollingInterval());try{let e=await this._requestFeatureFlagDefinitions();if(!e)return;switch(e.status){case 401:throw this.shouldBeginExponentialBackoff=!0,this.backOffCount+=1,new e_(`Your project key or personal API key is invalid. Setting next polling interval to ${this.getPollingInterval()}ms. More information: https://posthog.com/docs/api#rate-limiting`);case 402:console.warn("[FEATURE FLAGS] Feature flags quota limit exceeded - unsetting all local flags. Learn more about billing limits at https://posthog.com/docs/billing/limits-alerts"),this.featureFlags=[],this.featureFlagsByKey={},this.groupTypeMapping={},this.cohorts={};return;case 403:throw this.shouldBeginExponentialBackoff=!0,this.backOffCount+=1,new e_(`Your personal API key does not have permission to fetch feature flag definitions for local evaluation. Setting next polling interval to ${this.getPollingInterval()}ms. Are you sure you're using the correct personal and Project API key pair? More information: https://posthog.com/docs/api/overview`);case 429:throw this.shouldBeginExponentialBackoff=!0,this.backOffCount+=1,new e_(`You are being rate limited. Setting next polling interval to ${this.getPollingInterval()}ms. More information: https://posthog.com/docs/api#rate-limiting`);case 200:{let t=await e.json()??{};if(!("flags"in t))return void this.onError?.(Error(`Invalid response when getting feature flags: ${JSON.stringify(t)}`));this.featureFlags=t.flags??[],this.featureFlagsByKey=this.featureFlags.reduce((e,t)=>(e[t.key]=t,e),{}),this.groupTypeMapping=t.group_type_mapping||{},this.cohorts=t.cohorts||{},this.loadedSuccessfullyOnce=!0,this.shouldBeginExponentialBackoff=!1,this.backOffCount=0,this.onLoad?.(this.featureFlags.length);break}default:return}}catch(e){e instanceof e_&&this.onError?.(e)}}getPersonalApiKeyRequestOptions(e="GET"){return{method:e,headers:{...this.customHeaders,"Content-Type":"application/json",Authorization:`Bearer ${this.personalApiKey}`}}}async _requestFeatureFlagDefinitions(){let e=`${this.host}/api/feature_flag/local_evaluation?token=${this.projectApiKey}&send_cohorts`,t=this.getPersonalApiKeyRequestOptions(),r=null;if(this.timeout&&"number"==typeof this.timeout){let e=new AbortController;r=X(()=>{e.abort()},this.timeout),t.signal=e.signal}try{return await this.fetch(e,t)}finally{clearTimeout(r)}}stopPoller(){clearTimeout(this.poller)}_requestRemoteConfigPayload(e){let t=`${this.host}/api/projects/@current/feature_flags/${e}/remote_config/`,r=this.getPersonalApiKeyRequestOptions(),i=null;if(this.timeout&&"number"==typeof this.timeout){let e=new AbortController;i=X(()=>{e.abort()},this.timeout),r.signal=e.signal}try{return this.fetch(t,r)}finally{clearTimeout(i)}}}async function ek(e,t,r=""){return parseInt((await ew(`${e}.${t}${r}`)).slice(0,15),16)/0x1000000000000000}function eA(e,t,r){let i=e.key,s=e.value,a=e.operator||"exact";if(i in t){if("is_not_set"===a)throw new ev("Operator is_not_set is not supported")}else throw new ev(`Property ${i} not found in propertyValues`);let n=t[i];if(null==n&&!eb.includes(a))return r&&r(`Property ${i} cannot have a value of null/undefined with the ${a} operator`),!1;function o(e,t){return Array.isArray(e)?e.map(e=>String(e).toLowerCase()).includes(String(t).toLowerCase()):String(e).toLowerCase()===String(t).toLowerCase()}function l(e,t,r){if("gt"===r)return e>t;if("gte"===r)return e>=t;if("lt"===r)return e<t;if("lte"===r)return e<=t;throw Error(`Invalid operator: ${r}`)}switch(a){case"exact":return o(s,n);case"is_not":return!o(s,n);case"is_set":return i in t;case"icontains":return String(n).toLowerCase().includes(String(s).toLowerCase());case"not_icontains":return!String(n).toLowerCase().includes(String(s).toLowerCase());case"regex":return eE(String(s))&&null!==String(n).match(String(s));case"not_regex":return eE(String(s))&&null===String(n).match(String(s));case"gt":case"gte":case"lt":case"lte":{let e="number"==typeof s?s:null;if("string"==typeof s)try{e=parseFloat(s)}catch(e){}if(null==e||null==n)return l(String(n),String(s),a);if("string"==typeof n)return l(n,String(s),a);return l(n,e,a)}case"is_date_after":case"is_date_before":{let e=function(e){let t=e.match(/^-?(?<number>[0-9]+)(?<interval>[a-z])$/),r=new Date(new Date().toISOString());if(!t)return null;{if(!t.groups)return null;let e=parseInt(t.groups.number);if(e>=1e4)return null;let i=t.groups.interval;if("h"==i)r.setUTCHours(r.getUTCHours()-e);else if("d"==i)r.setUTCDate(r.getUTCDate()-e);else if("w"==i)r.setUTCDate(r.getUTCDate()-7*e);else if("m"==i)r.setUTCMonth(r.getUTCMonth()-e);else{if("y"!=i)return null;r.setUTCFullYear(r.getUTCFullYear()-e)}return r}}(String(s));if(null==e&&(e=eT(s)),null==e)throw new ev(`Invalid date: ${s}`);let t=eT(n);if(["is_date_before"].includes(a))return t<e;return t>e}default:throw new ev(`Unknown operator: ${a}`)}}function eE(e){try{return new RegExp(e),!0}catch(e){return!1}}function eT(e){if(e instanceof Date)return e;if("string"==typeof e||"number"==typeof e){let t=new Date(e);if(!isNaN(t.valueOf()))return t;throw new ev(`${e} is in an invalid date format`)}throw new ev(`The date provided ${e} must be a string, number, or date object`)}class eP{constructor(){this._memoryStorage={}}getProperty(e){return this._memoryStorage[e]}setProperty(e,t){this._memoryStorage[e]=null!==t?t:void 0}}class ex extends eu{constructor(e,t={}){if(super(e,t),this._memoryStorage=new eP,this.options=t,this.options.featureFlagsPollingInterval="number"==typeof t.featureFlagsPollingInterval?Math.max(t.featureFlagsPollingInterval,100):3e4,t.personalApiKey){if(t.personalApiKey.includes("phc_"))throw Error('Your Personal API key is invalid. These keys are prefixed with "phx_" and can be created in PostHog project settings.');this.featureFlagsPoller=new eS({pollingInterval:this.options.featureFlagsPollingInterval,personalApiKey:t.personalApiKey,projectApiKey:e,timeout:t.requestTimeout??1e4,host:this.host,fetch:t.fetch,onError:e=>{this._events.emit("error",e)},onLoad:e=>{this._events.emit("localEvaluationFlagsLoaded",e)},customHeaders:this.getCustomHeaders()})}this.errorTracking=new U(this,t),this.distinctIdHasSentFlagCalls={},this.maxCacheSize=t.maxCacheSize||5e4}getPersistedProperty(e){return this._memoryStorage.getProperty(e)}setPersistedProperty(e,t){return this._memoryStorage.setProperty(e,t)}fetch(e,t){return this.options.fetch?this.options.fetch(e,t):eh(e,t)}getLibraryVersion(){return"4.17.2"}getCustomUserAgent(){return`${this.getLibraryId()}/${this.getLibraryVersion()}`}enable(){return super.optIn()}disable(){return super.optOut()}debug(e=!0){super.debug(e),this.featureFlagsPoller?.debug(e)}capture(e){"string"==typeof e&&this.logMsgIfDebug(()=>console.warn("Called capture() with a string as the first argument when an object was expected."));let{distinctId:t,event:r,properties:i,groups:s,sendFeatureFlags:a,timestamp:n,disableGeoip:o,uuid:l}=e,c=e=>{super.captureStateless(t,r,e,{timestamp:n,disableGeoip:o,uuid:l})},u=async(e,t,r)=>(await super.getFeatureFlagsStateless(e,t,void 0,void 0,r)).flags,d=Promise.resolve().then(async()=>{if(a)return await u(t,s,o);if("$feature_flag_called"===r)return{};if((this.featureFlagsPoller?.featureFlags?.length||0)>0){let e={};for(let[t,r]of Object.entries(s||{}))e[t]=String(r);return await this.getAllFlags(t,{groups:e,disableGeoip:o,onlyEvaluateLocally:!0})}return{}}).then(e=>{let t={};if(e)for(let[r,i]of Object.entries(e))t[`$feature/${r}`]=i;let r=Object.keys(e||{}).filter(t=>e?.[t]!==!1).sort();return r.length>0&&(t.$active_feature_flags=r),t}).catch(()=>({})).then(e=>{c({...e,...i,$groups:s})});this.addPendingPromise(d)}async captureImmediate(e){"string"==typeof e&&this.logMsgIfDebug(()=>console.warn("Called capture() with a string as the first argument when an object was expected."));let{distinctId:t,event:r,properties:i,groups:s,sendFeatureFlags:a,timestamp:n,disableGeoip:o,uuid:l}=e,c=e=>super.captureStatelessImmediate(t,r,e,{timestamp:n,disableGeoip:o,uuid:l}),u=async(e,t,r)=>(await super.getFeatureFlagsStateless(e,t,void 0,void 0,r)).flags,d=Promise.resolve().then(async()=>{if(a)return await u(t,s,o);if("$feature_flag_called"===r)return{};if((this.featureFlagsPoller?.featureFlags?.length||0)>0){let e={};for(let[t,r]of Object.entries(s||{}))e[t]=String(r);return await this.getAllFlags(t,{groups:e,disableGeoip:o,onlyEvaluateLocally:!0})}return{}}).then(e=>{let t={};if(e)for(let[r,i]of Object.entries(e))t[`$feature/${r}`]=i;let r=Object.keys(e||{}).filter(t=>e?.[t]!==!1).sort();return r.length>0&&(t.$active_feature_flags=r),t}).catch(()=>({})).then(e=>{c({...e,...i,$groups:s})});await d}identify({distinctId:e,properties:t,disableGeoip:r}){let i=t?.$set_once;delete t?.$set_once;let s=t?.$set||t;super.identifyStateless(e,{$set:s,$set_once:i},{disableGeoip:r})}async identifyImmediate({distinctId:e,properties:t,disableGeoip:r}){let i=t?.$set_once;delete t?.$set_once;let s=t?.$set||t;await super.identifyStatelessImmediate(e,{$set:s,$set_once:i},{disableGeoip:r})}alias(e){super.aliasStateless(e.alias,e.distinctId,void 0,{disableGeoip:e.disableGeoip})}async aliasImmediate(e){await super.aliasStatelessImmediate(e.alias,e.distinctId,void 0,{disableGeoip:e.disableGeoip})}isLocalEvaluationReady(){return this.featureFlagsPoller?.isLocalEvaluationReady()??!1}async waitForLocalEvaluationReady(e=3e4){return!!this.isLocalEvaluationReady()||void 0!==this.featureFlagsPoller&&new Promise(t=>{let r=setTimeout(()=>{i(),t(!1)},e),i=this._events.on("localEvaluationFlagsLoaded",e=>{clearTimeout(r),i(),t(e>0)})})}async getFeatureFlag(e,t,r){let i,s,{groups:a,disableGeoip:n}=r||{},{onlyEvaluateLocally:o,sendFeatureFlagEvents:l,personProperties:c,groupProperties:u}=r||{},d=this.addLocalPersonAndGroupProperties(t,a,c,u);c=d.allPersonProperties,u=d.allGroupProperties,void 0==o&&(o=!1),void 0==l&&(l=!0);let h=await this.featureFlagsPoller?.getFeatureFlag(e,t,a,c,u),p=void 0!==h;if(!p&&!o){let r=await super.getFeatureFlagDetailStateless(e,t,a,c,u,n);if(void 0===r)return;h=z(s=r.response),i=r?.requestId}let f=`${e}_${h}`;return!l||t in this.distinctIdHasSentFlagCalls&&this.distinctIdHasSentFlagCalls[t].includes(f)||(Object.keys(this.distinctIdHasSentFlagCalls).length>=this.maxCacheSize&&(this.distinctIdHasSentFlagCalls={}),Array.isArray(this.distinctIdHasSentFlagCalls[t])?this.distinctIdHasSentFlagCalls[t].push(f):this.distinctIdHasSentFlagCalls[t]=[f],this.capture({distinctId:t,event:"$feature_flag_called",properties:{$feature_flag:e,$feature_flag_response:h,$feature_flag_id:s?.metadata?.id,$feature_flag_version:s?.metadata?.version,$feature_flag_reason:s?.reason?.description??s?.reason?.code,locally_evaluated:p,[`$feature/${e}`]:h,$feature_flag_request_id:i},groups:a,disableGeoip:n})),h}async getFeatureFlagPayload(e,t,r,i){let s,{groups:a,disableGeoip:n}=i||{},{onlyEvaluateLocally:o,sendFeatureFlagEvents:l,personProperties:c,groupProperties:u}=i||{},d=this.addLocalPersonAndGroupProperties(t,a,c,u);return c=d.allPersonProperties,u=d.allGroupProperties,void 0!==this.featureFlagsPoller&&(r||(r=await this.getFeatureFlag(e,t,{...i,onlyEvaluateLocally:!0,sendFeatureFlagEvents:!1})),r&&(s=await this.featureFlagsPoller?.computeFeatureFlagPayloadLocally(e,r))),void 0==o&&(o=!1),void 0==l&&(l=!0),void 0==o&&(o=!1),void 0!==s||o||(s=await super.getFeatureFlagPayloadStateless(e,t,a,c,u,n)),s}async getRemoteConfigPayload(e){return(await this.featureFlagsPoller?._requestRemoteConfigPayload(e))?.json()}async isFeatureEnabled(e,t,r){let i=await this.getFeatureFlag(e,t,r);if(void 0!==i)return!!i}async getAllFlags(e,t){return(await this.getAllFlagsAndPayloads(e,t)).featureFlags||{}}async getAllFlagsAndPayloads(e,t){let{groups:r,disableGeoip:i}=t||{},{onlyEvaluateLocally:s,personProperties:a,groupProperties:n}=t||{},o=this.addLocalPersonAndGroupProperties(e,r,a,n);a=o.allPersonProperties,n=o.allGroupProperties,void 0==s&&(s=!1);let l=await this.featureFlagsPoller?.getAllFlagsAndPayloads(e,r,a,n),c={},u={},d=!0;if(l&&(c=l.response,u=l.payloads,d=l.fallbackToDecide),d&&!s){let t=await super.getFeatureFlagsAndPayloadsStateless(e,r,a,n,i);c={...c,...t.flags||{}},u={...u,...t.payloads||{}}}return{featureFlags:c,featureFlagPayloads:u}}groupIdentify({groupType:e,groupKey:t,properties:r,distinctId:i,disableGeoip:s}){super.groupIdentifyStateless(e,t,r,{disableGeoip:s},i)}async reloadFeatureFlags(){await this.featureFlagsPoller?.loadFeatureFlags(!0)}async _shutdown(e){return this.featureFlagsPoller?.stopPoller(),super._shutdown(e)}addLocalPersonAndGroupProperties(e,t,r,i){let s={distinct_id:e,...r||{}},a={};if(t)for(let e of Object.keys(t))a[e]={$group_key:t[e],...i?.[e]||{}};return{allPersonProperties:s,allGroupProperties:a}}captureException(e,t,r){let i=Error("PostHog syntheticException");U.captureException(this,e,{syntheticException:i},t,r)}}let eI=/\(error: (.*)\)/;function eC(e){return parseInt(e||"",10)||void 0}U.stackParser=function(e){let t=[[90,function(e){let t=/^\s*[-]{4,}$/,r=/at (?:async )?(?:(.+?)\s+\()?(?:(.+):(\d+):(\d+)?|([^)]+))\)?/;return i=>{let s=i.match(r);if(s){let t,r,i,a,n;if(s[1]){let e=(i=s[1]).lastIndexOf(".");if("."===i[e-1]&&e--,e>0){t=i.slice(0,e),r=i.slice(e+1);let s=t.indexOf(".Module");s>0&&(i=i.slice(s+1),t=t.slice(0,s))}a=void 0}r&&(a=t,n=r),"<anonymous>"===r&&(n=void 0,i=void 0),void 0===i&&(n=n||"?",i=a?`${a}.${n}`:n);let o=s[2]?.startsWith("file://")?s[2].slice(7):s[2],l="native"===s[5];return o?.match(/\/[A-Z]:/)&&(o=o.slice(1)),o||!s[5]||l||(o=s[5]),{filename:o?decodeURI(o):void 0,module:e?e(o):void 0,function:i,lineno:eC(s[3]),colno:eC(s[4]),in_app:function(e,t=!1){return!(t||e&&!e.startsWith("/")&&!e.match(/^[A-Z]:/)&&!e.startsWith(".")&&!e.match(/^[a-zA-Z]([a-zA-Z0-9.\-+])*:\/\//))&&void 0!==e&&!e.includes("node_modules/")}(o||"",l),platform:"node:javascript"}}if(i.match(t))return{filename:i,platform:"node:javascript"}}}(e)]].sort((e,t)=>e[0]-t[0]).map(e=>e[1]);return(e,r=0)=>{let i=[],s=e.split("\n");for(let e=r;e<s.length;e++){let r=s[e];if(r.length>1024)continue;let a=eI.test(r)?r.replace(eI,"$1"):r;if(!a.match(/\S*Error: /)){for(let e of t){let t=e(a);if(t){i.push(t);break}}if(i.length>=50)break}}var a=i;if(!a.length)return[];let n=Array.from(a);return n.reverse(),n.slice(0,50).map(e=>{var t;return{...e,filename:e.filename||((t=n)[t.length-1]||{}).filename,function:e.function||"?"}})}}(function(e=process.argv[1]?(0,g.dirname)(process.argv[1]):process.cwd(),t="\\"===g.sep){let r=t?H(e):e;return e=>{if(!e)return;let i=t?H(e):e,{dir:s,base:a,ext:n}=g.posix.parse(i);(".js"===n||".mjs"===n||".cjs"===n)&&(a=a.slice(0,-1*n.length));let o=decodeURIComponent(a);s||(s=".");let l=s.lastIndexOf("/node_modules");if(l>-1)return`${s.slice(l+14).replace(/\//g,".")}:${o}`;if(s.startsWith(r)){let e=s.slice(r.length+1).replace(/\//g,".");return e?`${e}:${o}`:o}return o}}()),U.frameModifiers=[M];class eO extends ex{getLibraryId(){return"posthog-node"}}},61824:(e,t,r)=>{r.d(t,{A:()=>i});let i=(...e)=>{let t,r=e.filter(Boolean);if(0===r.length||1===r.length)return!0;for(let e of r){let r=Object.keys(e);if(!t||0===t.size){t=new Set(r);continue}for(let e of r){if(t.has(e))return!1;t.add(e)}}return!0}},63669:(e,t,r)=>{function i(e,t,...r){if((r=r.filter(Boolean)).length>2){let t=r.pop();e+=`one of type ${r.join(", ")}, or ${t}.`}else 2===r.length?e+=`one of type ${r[0]} or ${r[1]}.`:e+=`of type ${r[0]}.`;return null==t?e+=` Received ${t}`:"function"==typeof t&&t.name?e+=` Received function ${t.name}`:"object"==typeof t&&null!=t&&t.constructor?.name&&(e+=` Received an instance of ${t.constructor.name}`),e}r.d(t,{A:()=>s,t:()=>a});let s=(e,...t)=>i("Key must be ",e,...t);function a(e,t,...r){return i(`Key for the ${e} algorithm must be `,t,...r)}},67407:(e,t,r)=>{r.d(t,{A:()=>i});function i(e){if("object"!=typeof e||null===e||"[object Object]"!==Object.prototype.toString.call(e))return!1;if(null===Object.getPrototypeOf(e))return!0;let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}},69472:(e,t,r)=>{r.d(t,{A:()=>a,g:()=>n});var i=r(89605),s=r(22556);let a=e=>(0,s.A)(e)||(0,i.R)(e),n=["KeyObject"];(globalThis.CryptoKey||i.A?.CryptoKey)&&n.push("CryptoKey")},70556:(e,t,r)=>{r.d(t,{A:()=>d,I:()=>h});var i=r(63669),s=r(69472),a=r(6798);let n=e=>e?.[Symbol.toStringTag],o=(e,t,r)=>{if(void 0!==t.use&&"sig"!==t.use)throw TypeError("Invalid key for this operation, when present its use must be sig");if(void 0!==t.key_ops&&t.key_ops.includes?.(r)!==!0)throw TypeError(`Invalid key for this operation, when present its key_ops must include ${r}`);if(void 0!==t.alg&&t.alg!==e)throw TypeError(`Invalid key for this operation, when present its alg must be ${e}`);return!0},l=(e,t,r,l)=>{if(!(t instanceof Uint8Array)){if(l&&a.ll(t)){if(a.t9(t)&&o(e,t,r))return;throw TypeError('JSON Web Key for symmetric algorithms must have JWK "kty" (Key Type) equal to "oct" and the JWK "k" (Key Value) present')}if(!(0,s.A)(t))throw TypeError((0,i.t)(e,t,...s.g,"Uint8Array",l?"JSON Web Key":null));if("secret"!==t.type)throw TypeError(`${n(t)} instances for symmetric algorithms must be of type "secret"`)}},c=(e,t,r,l)=>{if(l&&a.ll(t))switch(r){case"sign":if(a.W2(t)&&o(e,t,r))return;throw TypeError("JSON Web Key for this operation be a private JWK");case"verify":if(a.M3(t)&&o(e,t,r))return;throw TypeError("JSON Web Key for this operation be a public JWK")}if(!(0,s.A)(t))throw TypeError((0,i.t)(e,t,...s.g,l?"JSON Web Key":null));if("secret"===t.type)throw TypeError(`${n(t)} instances for asymmetric algorithms must not be of type "secret"`);if("sign"===r&&"public"===t.type)throw TypeError(`${n(t)} instances for asymmetric algorithm signing must be of type "private"`);if("decrypt"===r&&"public"===t.type)throw TypeError(`${n(t)} instances for asymmetric algorithm decryption must be of type "private"`);if(t.algorithm&&"verify"===r&&"private"===t.type)throw TypeError(`${n(t)} instances for asymmetric algorithm verifying must be of type "public"`);if(t.algorithm&&"encrypt"===r&&"private"===t.type)throw TypeError(`${n(t)} instances for asymmetric algorithm encryption must be of type "public"`)};function u(e,t,r,i){t.startsWith("HS")||"dir"===t||t.startsWith("PBES2")||/^A\d{3}(?:GCM)?KW$/.test(t)?l(t,r,i,e):c(t,r,i,e)}let d=u.bind(void 0,!1),h=u.bind(void 0,!0)},72645:(e,t)=>{t.qg=function(e,t){let n=new r,o=e.length;if(o<2)return n;let l=t?.decode||a,c=0;do{let t=e.indexOf("=",c);if(-1===t)break;let r=e.indexOf(";",c),a=-1===r?o:r;if(t>a){c=e.lastIndexOf(";",t-1)+1;continue}let u=i(e,c,t),d=s(e,t,u),h=e.slice(u,d);if(void 0===n[h]){let r=i(e,t+1,a),o=s(e,a,r),c=l(e.slice(r,o));n[h]=c}c=a+1}while(c<o);return n},Object.prototype.toString;let r=(()=>{let e=function(){};return e.prototype=Object.create(null),e})();function i(e,t,r){do{let r=e.charCodeAt(t);if(32!==r&&9!==r)return t}while(++t<r);return r}function s(e,t,r){for(;t>r;){let r=e.charCodeAt(--t);if(32!==r&&9!==r)return t+1}return r}function a(e){if(-1===e.indexOf("%"))return e;try{return decodeURIComponent(e)}catch(t){return e}}},76190:(e,t,r)=>{let i=r(1702),{snakeCase:s}=r(79488),a={}.constructor;e.exports=function(e,t){if(Array.isArray(e)){if(e.some(e=>e.constructor!==a))throw Error("obj must be array of plain objects")}else if(e.constructor!==a)throw Error("obj must be an plain object");return i(e,function(e,r){var i,a,n,o,l;return[(i=t.exclude,a=e,i.some(function(e){return"string"==typeof e?e===a:e.test(a)}))?e:s(e,t.parsingOptions),r,(n=e,o=r,(l=t).shouldRecurse?{shouldRecurse:l.shouldRecurse(n,o)}:void 0)]},t=Object.assign({deep:!0,exclude:[],parsingOptions:{}},t))}},76285:(e,t,r)=>{r.d(t,{Og:()=>l});var i=r(13690),s=r(77598);let a=e=>e.d?(0,s.createPrivateKey)({format:"jwk",key:e}):(0,s.createPublicKey)({format:"jwk",key:e});var n=r(14295),o=r(67407);async function l(e,t){if(!(0,o.A)(e))throw TypeError("JWK must be an object");switch(t||=e.alg,e.kty){case"oct":if("string"!=typeof e.k||!e.k)throw TypeError('missing "k" (Key Value) Parameter value');return(0,i.D4)(e.k);case"RSA":if("oth"in e&&void 0!==e.oth)throw new n.T0('RSA JWK "oth" (Other Primes Info) Parameter value is not supported');case"EC":case"OKP":return a({...e,alg:t});default:throw new n.T0('Unsupported "kty" (Key Type) Parameter value')}}},79488:(e,t,r)=>{r.r(t),r.d(t,{snakeCase:()=>l});var i=function(){return(i=Object.assign||function(e){for(var t,r=1,i=arguments.length;r<i;r++)for(var s in t=arguments[r])Object.prototype.hasOwnProperty.call(t,s)&&(e[s]=t[s]);return e}).apply(this,arguments)};Object.create;function s(e){return e.toLowerCase()}Object.create,"function"==typeof SuppressedError&&SuppressedError;var a=[/([a-z0-9])([A-Z])/g,/([A-Z])([A-Z][a-z])/g],n=/[^A-Z0-9]+/gi;function o(e,t,r){return t instanceof RegExp?e.replace(t,r):t.reduce(function(e,t){return e.replace(t,r)},e)}function l(e,t){var r;return void 0===t&&(t={}),void 0===(r=i({delimiter:"_"},t))&&(r={}),function(e,t){void 0===t&&(t={});for(var r=t.splitRegexp,i=t.stripRegexp,l=t.transform,c=t.delimiter,u=o(o(e,void 0===r?a:r,"$1\0$2"),void 0===i?n:i,"\0"),d=0,h=u.length;"\0"===u.charAt(d);)d++;for(;"\0"===u.charAt(h-1);)h--;return u.slice(d,h).split("\0").map(void 0===l?s:l).join(void 0===c?" ":c)}(e,i({delimiter:"."},r))}},89605:(e,t,r)=>{r.d(t,{A:()=>a,R:()=>n});var i=r(77598),s=r(57975);let a=i.webcrypto,n=e=>s.types.isCryptoKey(e)}};