[?25l[2J[m[2;1H> web@ build C:\Users\<USER>\Documents\2 FOLDERS FOR CUBENT\Cubentweb\cubent\apps\web
> next build[5;1H]0;C:\Windows\system32\cmd.exe[?25h[?25l   [38;2;173;127;168m[1m▲ Next.js 15.3.2[m
   - Environments: .env.local[8;1H[?25h [37m[1m [m Creating an optimized production build ...
[33mwarn [m- Envvars not detected. If this is production please see https://betterstack.com/docs/logs/javascript/nextjs/ for help[33m
warn [m- Sending Web Vitals to /dev/null[33m
warn [m- Sending logs to console
[31mFailed to compile.
[m
[31m[m
./components/hashed-keys-bento.tsx
[31m[1mModule not found[m: Can't resolve '[32m@/components/hashed-keys[m'[K

https://nextjs.org/docs/messages/module-not-found

Import trace for requested module:
./app/[locale]/(home)/page.tsx

./components/ip-whitelisting-bento.tsx
[31m[1mModule not found[m: Can't resolve '[32m../images/ip.svg[m'[K

https://nextjs.org/docs/messages/module-not-found

Import trace for requested module:
./app/[locale]/(home)/page.tsx

./components/latency-bento.tsx
[31m[1mModule not found[m: Can't resolve '[32m../images/map.svg[m'[K

https://nextjs.org/docs/messages/module-not-found

Import trace for requested module:
./app/[locale]/(home)/page.tsx

C:\Users\<USER>\Documents\2 FOLDERS FOR CUBENT\Cubentweb\cubent\apps\web\app\[locale]\(home)\page.tsx:21
[31m[1mError[m: Image import "../../../images/mainboard.svg" is not a valid image file. The image may be corrupted or an unsupported format.  [11;1H

next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Canwar%5C%5CDocuments%5C%5C2%20FOLDERS%20FOR%20CUBE
[10;133HENT%5C%5CCubentweb%5C%5Ccubent%5C%5Capps%5C%5Cweb%5C%5Capp%5C%5C%5Blocale%5D%5C%5C(home)%5C%5Ccode-examples.tsx%22%2C%22ids%22%3A%5B%2
[10;133H22CodeExamples%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Canwar%5C%5CDocuments%5C%5C2%20FOLDERS%20FOR%20CUBENT%5C%5CC
[10;133HCubentweb%5C%5Ccubent%5C%5Capps%5C%5Cweb%5C%5Ccomponents%5C%5Caudit%5C%5Caudit-logs.tsx%22%2C%22ids%22%3A%5B%22AuditLogs%22%5D%7D&modu
[10;133Hules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Canwar%5C%5CDocuments%5C%5C2%20FOLDERS%20FOR%20CUBENT%5C%5CCubentweb%5C%5Ccubent%5C%5Ca
[10;133Happs%5C%5Cweb%5C%5Ccomponents%5C%5Ccta.tsx%22%2C%22ids%22%3A%5B%22CTA%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Canwa
[10;133Har%5C%5CDocuments%5C%5C2%20FOLDERS%20FOR%20CUBENT%5C%5CCubentweb%5C%5Ccubent%5C%5Capps%5C%5Cweb%5C%5Ccomponents%5C%5Chero%5C%5Chero.ts
[10;133Hsx%22%2C%22ids%22%3A%5B%22Hero%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Canwar%5C%5CDocuments%5C%5C2%20FOLDERS%20FOR
[10;133HR%20CUBENT%5C%5CCubentweb%5C%5Ccubent%5C%5Capps%5C%5Cweb%5C%5Ccomponents%5C%5Copen-source.tsx%22%2C%22ids%22%3A%5B%22OpenSource%22%5D%
[10;133H%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Canwar%5C%5CDocuments%5C%5C2%20FOLDERS%20FOR%20CUBENT%5C%5CCubentweb%5C%5Ccubent
[10;133Ht%5C%5Capps%5C%5Cweb%5C%5Ccomponents%5C%5Cusage-bento.tsx%22%2C%22ids%22%3A%5B%22UsageBento%22%5D%7D&modules=%7B%22request%22%3A%22C%3
[10;133H3A%5C%5CUsers%5C%5Canwar%5C%5CDocuments%5C%5C2%20FOLDERS%20FOR%20CUBENT%5C%5CCubentweb%5C%5Ccubent%5C%5Capps%5C%5Cweb%5C%5Cimages%5C%5
[10;133H5Cmainboard.svg%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Canwar%5C%5CDocuments%5C%5C2%20FOL
[10;133HLDERS%20FOR%20CUBENT%5C%5CCubentweb%5C%5Ccubent%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.3.2_%40babel%2Bcore%407.2_09efc9edc30971f6
[10;133H6ad8d5c21a0ab8f1f%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%2C%22__esMod
[10;133Hdule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Canwar%5C%5CDocuments%5C%5C2%20FOLDERS%20FOR%20CUBENT%
[10;133H%5C%5CCubentweb%5C%5Ccubent%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.3.2_%40babel%2Bcore%407.2_09efc9edc30971f6ad8d5c21a0ab8f1f%5C%
[10;133H%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!:17
[31m[1mError[m: Image import "C:\Users\<USER>\Documents\2 FOLDERS FOR CUBENT\Cubentweb\cubent\apps\web\images\mainboard.svg" is not a valid ima
[10;133Hage file. The image may be corrupted or an unsupported format.


> Build failed because of webpack errors
[30m[41m ELIFECYCLE [m [31mCommand failed with exit code 1.
[m
