[?25l[2J[m[2;1H> web@ build C:\Users\<USER>\Documents\2 FOLDERS FOR CUBENT\Cubentweb\cubent\apps\web
> next build[5;1H]0;C:\Windows\system32\cmd.exe[?25h[?25l   [38;2;173;127;168m[1m▲ Next.js 15.3.2[m
   - Environments: .env.local[8;1H[?25h [37m[1m [m Creating an optimized production build ...
[33mwarn [m- Envvars not detected. If this is production please see https://betterstack.com/docs/logs/javascript/nextjs/ for help[33m
warn [m- Sending Web Vitals to /dev/null[33m
warn [m- Sending logs to console
 [33m[1m⚠[m Compiled with warnings in 49s

./app/[locale]/(home)/code-examples.tsx
Attempted import error: 'JavaIcon' is not exported from '@/components/svg/lang-icons' (imported as 'JavaIcon').

Import trace for requested module:
./app/[locale]/(home)/code-examples.tsx

./app/[locale]/(home)/code-examples.tsx
Attempted import error: 'JavaIcon' is not exported from '@/components/svg/lang-icons' (imported as 'JavaIcon').

Import trace for requested module:
./app/[locale]/(home)/code-examples.tsx

./app/[locale]/(home)/code-examples.tsx
Attempted import error: 'JavaIcon' is not exported from '@/components/svg/lang-icons' (imported as 'JavaIcon').

Import trace for requested module:
./app/[locale]/(home)/code-examples.tsx

./components/usage-bento.tsx
Attempted import error: 'UsageSparkles' is not exported from '@/components/svg/usage' (imported as 'UsageSparkles').

Import trace for requested module:
./components/usage-bento.tsx

./app/[locale]/(home)/code-examples.tsx
Attempted import error: 'JavaIcon' is not exported from '@/components/svg/lang-icons' (imported as 'JavaIcon').

Import trace for requested module:
./app/[locale]/(home)/code-examples.tsx

./app/[locale]/(home)/code-examples.tsx
Attempted import error: 'JavaIcon' is not exported from '@/components/svg/lang-icons' (imported as 'JavaIcon').

Import trace for requested module:
./app/[locale]/(home)/code-examples.tsx

./app/[locale]/(home)/code-examples.tsx
Attempted import error: 'JavaIcon' is not exported from '@/components/svg/lang-icons' (imported as 'JavaIcon').

Import trace for requested module:
./app/[locale]/(home)/code-examples.tsx

./components/usage-bento.tsx
Attempted import error: 'UsageSparkles' is not exported from '@/components/svg/usage' (imported as 'UsageSparkles').

Import trace for requested module:
./components/usage-bento.tsx

 [37m[1m [m Linting and checking validity of types  [36m.[?25l[m
 [37m[1m [m Linting and checking validity of types  [36m..[K[88C[m
 [37m[1m [m Linting and checking validity of types  [36m...[K[87C[m
 [37m[1m [m Linting and checking validity of types  [36m.[K[89C[m
 [37m[1m [m Linting and checking validity of types  [36m..[K[88C[m
 [37m[1m [m Linting and checking validity of types  [36m...[K[87C[m
 [37m[1m [m Linting and checking validity of types  [36m.[K[89C[m
 [37m[1m [m Linting and checking validity of types  [36m..[K[88C[m
 [37m[1m [m Linting and checking validity of types  [36m...[K[87C[m
 [37m[1m [m Linting and checking validity of types  [36m.[K[89C[m
 [37m[1m [m Linting and checking validity of types  [36m..[K[88C[m
 [37m[1m [m Linting and checking validity of types  [36m...[K[87C[m
 [37m[1m [m Linting and checking validity of types  [36m.[K[89C[m
 [37m[1m [m Linting and checking validity of types  [36m..[K[88C[m
 [37m[1m [m Linting and checking validity of types  [36m...[K[87C[m
 [37m[1m [m Linting and checking validity of types  [36m.[K[89C[m
 [37m[1m [m Linting and checking validity of types  [36m..[K[88C[m
 [37m[1m [m Linting and checking validity of types  [36m...[K[87C[m
 [37m[1m [m Linting and checking validity of types  [36m.[K[89C[m
 [37m[1m [m Linting and checking validity of types  [36m..[K[88C[m
 [37m[1m [m Linting and checking validity of types  [36m...[K[87C[m
 [37m[1m [m Linting and checking validity of types  [36m.[K[89C[m
 [37m[1m [m Linting and checking validity of types  [36m..[K[88C[m
 [37m[1m [m Linting and checking validity of types  [36m...[K[87C[m
 [37m[1m [m Linting and checking validity of types  [36m.[K[89C[m
 [37m[1m [m Linting and checking validity of types  [36m..[K[88C[m
 [37m[1m [m Linting and checking validity of types  [36m...[K[87C[m
 [37m[1m [m Linting and checking validity of types  [36m.[K[89C[m
 [37m[1m [m Linting and checking validity of types  [36m..[K[88C[m
 [37m[1m [m Linting and checking validity of types  [36m...[K[87C[m
 [37m[1m [m Linting and checking validity of types  [36m.[K[89C[m
 [37m[1m [m Linting and checking validity of types  [36m..[K[88C[m
 [37m[1m [m Linting and checking validity of types  [36m...[K[87C[m
 [37m[1m [m Linting and checking validity of types  [36m.[K[89C[m
 [37m[1m [m Linting and checking validity of types  [36m..[K[88C[m
 [37m[1m [m Linting and checking validity of types  [36m...[K[87C[m
 [37m[1m [m Linting and checking validity of types  [36m.[K[89C[m
 [37m[1m [m Linting and checking validity of types  [36m..[K[88C[m
 [37m[1m [m Linting and checking validity of types  [36m...[K[87C[m
 [37m[1m [m Linting and checking validity of types  [36m.[K[89C[m
 [37m[1m [m Linting and checking validity of types  [36m..[K[88C[m
 [37m[1m [m Linting and checking validity of types  [36m...[K[87C[m
 [37m[1m [m Linting and checking validity of types  [36m.[K[89C[m
 [37m[1m [m Linting and checking validity of types  [36m..[K[88C[m
 [37m[1m [m Linting and checking validity of types  [36m...[K[87C[m
 [37m[1m [m Linting and checking validity of types  [36m.[K[89C[m
 [37m[1m [m Linting and checking validity of types  [36m..[K[88C[m
 [37m[1m [m Linting and checking validity of types  [36m...[K[87C[m
 [37m[1m [m Linting and checking validity of types  [36m.[K[89C[m
 [37m[1m [m Linting and checking validity of types  [36m..[K[88C[m
 [37m[1m [m Linting and checking validity of types  [36m...[K[87C[m
 [37m[1m [m Linting and checking validity of types  [36m.[K[89C[m
 [37m[1m [m Linting and checking validity of types  [36m..[K[88C[m
 [37m[1m [m Linting and checking validity of types  [36m...[K[87C[m
 [37m[1m [m Linting and checking validity of types  [36m.[K[89C[m
 [37m[1m [m Linting and checking validity of types  [36m..[K[88C[m
 [37m[1m [m Linting and checking validity of types  [36m...[K[87C[m
 [37m[1m [m Linting and checking validity of types  [36m.[K[89C[m
 [37m[1m [m Linting and checking validity of types  [36m..[K[88C[m
 [37m[1m [m Linting and checking validity of types  [36m...[K[87C[m
 [37m[1m [m Linting and checking validity of types  [36m.[K[89C[m
 [37m[1m [m Linting and checking validity of types  [36m..[K[88C[m
 [37m[1m [m Linting and checking validity of types  [36m...[K[87C[m
 [37m[1m [m Linting and checking validity of types  [36m.[K[89C[m
 [37m[1m [m Linting and checking validity of types  [36m..[K[88C[m
 [37m[1m [m Linting and checking validity of types  [36m...[K[87C[m
 [37m[1m [m Linting and checking validity of types  [36m.[K[89C[m
 [37m[1m [m Linting and checking validity of types  [36m..[K[88C[m
 [37m[1m [m Linting and checking validity of types  [36m...[K[87C[m
 [37m[1m [m Linting and checking validity of types  [36m.[K[89C[m
 [37m[1m [m Linting and checking validity of types  [36m..[K[88C[m
 [37m[1m [m Linting and checking validity of types  [36m...[K[87C[m
 [37m[1m [m Linting and checking validity of types  [36m.[K[89C[m
 [37m[1m [m Linting and checking validity of types  [36m..[K[88C[m
 [37m[1m [m Linting and checking validity of types  [36m...[K[87C[m
 [37m[1m [m Linting and checking validity of types  [36m.[K[89C[m
 [37m[1m [m Linting and checking validity of types  [36m..[K[88C[m
 [37m[1m [m Linting and checking validity of types  [36m...[K[87C[m
 [37m[1m [m Linting and checking validity of types  [36m.[K[89C[m
 [37m[1m [m Linting and checking validity of types  [36m..[K[88C[m
 [37m[1m [m Linting and checking validity of types  [36m...[K[87C[m
 [37m[1m [m Linting and checking validity of types  [36m.[K[89C[m
 [37m[1m [m Linting and checking validity of types  [36m..[K[88C[m
 [37m[1m [m Linting and checking validity of types  [36m...[K[87C[m
 [37m[1m [m Linting and checking validity of types  [36m.[K[89C[m
 [37m[1m [m Linting and checking validity of types  [36m..[K[88C[m
 [37m[1m [m Linting and checking validity of types  [36m...[K[87C[m
 [37m[1m [m Linting and checking validity of types  [36m.[K[89C[m
 [37m[1m [m Linting and checking validity of types  [36m..[K[88C[m
 [37m[1m [m Linting and checking validity of types  [36m...[K[87C[m
 [37m[1m [m Linting and checking validity of types  [36m.[K[89C[m
 [37m[1m [m Linting and checking validity of types  [36m..[K[88C[m
 [37m[1m [m Linting and checking validity of types  [36m...[K[87C[m
 [37m[1m [m Linting and checking validity of types  [36m.[K[89C[m
 [37m[1m [m Linting and checking validity of types  [36m..[K[88C[m
 [37m[1m [m Linting and checking validity of types  [36m...[K[87C[m
 [37m[1m [m Linting and checking validity of types  [36m.[K[89C[m
 [37m[1m [m Linting and checking validity of types  [36m..[K[88C[m
 [37m[1m [m Linting and checking validity of types  [36m...[K[87C[m
 [37m[1m [m Linting and checking validity of types  [36m.[K[89C[m
 [37m[1m [m Linting and checking validity of types  [36m..[K[88C[m
 [37m[1m [m Linting and checking validity of types  [36m...[K[87C[m
 [37m[1m [m Linting and checking validity of types  [36m.[K[89C[m
 [37m[1m [m Linting and checking validity of types  [36m..[K[88C[m
 [37m[1m [m Linting and checking validity of types  [36m...[K[87C[m
 [37m[1m [m Linting and checking validity of types  [36m.[K[89C[m
 [37m[1m [m Linting and checking validity of types  [36m..[K[88C[m
 [37m[1m [m Linting and checking validity of types  [36m...[K[87C[m
 [37m[1m [m Linting and checking validity of types  [36m.[K[89C[m
[K[133C
 [37m[1m [m Linting and checking validity of types  [36m..[m
 [37m[1m [m Linting and checking validity of types  [36m...[K[87C[m
 [37m[1m [m Linting and checking validity of types  [36m.[K[89C[m
 [37m[1m [m Linting and checking validity of types  [36m..[K[88C[m
 [37m[1m [m Linting and checking validity of types  [36m...[K[87C[m
 [37m[1m [m Linting and checking validity of types  [36m.[K[89C[m
 [37m[1m [m Linting and checking validity of types  [36m..[K[88C[m
 [37m[1m [m Linting and checking validity of types  [36m...[K[87C[m
 [37m[1m [m Linting and checking validity of types  [36m.[K[89C[m
 [37m[1m [m Linting and checking validity of types  [36m..[K[88C[m
 [37m[1m [m Linting and checking validity of types  [36m...[K[87C[m
 [37m[1m [m Linting and checking validity of types  [36m.[K[89C[m
 [37m[1m [m Linting and checking validity of types  [36m..[K[88C[m
 [37m[1m [m Linting and checking validity of types  [36m...[K[87C[m[11;47HFailed to compile.
[133C
[133C
./app/[locale]/(home)/code-examples.tsx:9:3
Type error: Module '"@/components/svg/lang-icons"' has no exported member 'JavaIcon'.[48C
[133C
   [90m7 |   [33mElixirIcon,[K[113C[m
   [90m8 |   [33mGoIcon,[K[117C[m
[31m[1m>[90m[22m  9 |   [33mJavaIcon,[K[115C[m
     [90m|   [31m[1m^[m[K[123C
  [90m10 |   [33mPythonIcon,[K[113C[m
  [90m11 |   [33mRustIcon,[K[115C[m
  [90m12 |   [33mTSIcon,[K[117C[m
[133C
Next.js build worker exited with code: 1 and signal: null[K
[?25h
 [36m.[K
[m[30m[41m ELIFECYCLE [m [31mCommand failed with exit code 1.
[m
