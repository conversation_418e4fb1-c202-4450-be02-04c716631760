(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[414],{24323:(e,t,n)=>{"use strict";n.r(t),n.d(t,{BaseHubImage:()=>w,basehubImageLoader:()=>v});var r=Object.defineProperty,a=Object.defineProperties,l=Object.getOwnPropertyDescriptors,s=Object.getOwnPropertySymbols,o=Object.prototype.hasOwnProperty,i=Object.prototype.propertyIsEnumerable,u=(e,t,n)=>t in e?r(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,c=(e,t)=>{for(var n in t||(t={}))o.call(t,n)&&u(e,n,t[n]);if(s)for(var n of s(t))i.call(t,n)&&u(e,n,t[n]);return e},p=(e,t)=>a(e,l(t)),d=n(82757),h=n(50628),f=n(6024),m="https://basehub.earth",b="https://assets.basehub.com",v=e=>{let t,{src:n,width:r,quality:a}=e;try{t=new URL(n)}catch(e){throw Error("Invalid BaseHub Image URL: ".concat(n,"\n\nExpected origin to be one of:\n- ").concat(m," (deprecated)\n- ").concat(b,"\n"))}let l=["width=".concat(r),"quality=".concat(a||90)];if(t.href.includes(m))if(t.pathname.startsWith("/cdn-cgi/image/")){let[e,n,r,a="",...s]=t.pathname.split("/"),o=[...a.split(",").filter(e=>!e.startsWith("width=")&&!e.startsWith("quality=")&&!e.startsWith("w=")&&!e.startsWith("q=")&&!e.startsWith("h=")&&!e.startsWith("height=")),...l].join(",");!1===o.includes("format=")&&(o+=",format=auto"),t.pathname="/cdn-cgi/image/".concat(o,"/").concat(s.join("/"))}else l.push("format=auto"),t.pathname="/cdn-cgi/image/".concat(l.join(",")).concat(t.pathname);else t.href.includes(b)&&(l.forEach(e=>{let[n,r]=e.split("=");n&&r&&t.searchParams.set(n,r)}),!1===t.searchParams.has("format")&&t.searchParams.set("format","auto"),t.searchParams.delete("height"),t.searchParams.delete("h"));let s=new URL(b);if(t.href.includes(m))if(t.pathname.startsWith("/cdn-cgi/image/")){let[e,n,r,a="",...l]=t.pathname.split("/");s.pathname=l.join("/"),s.search=a.split(",").join("&")}else s.pathname=t.pathname,s.search=t.search;else{if(!t.href.includes(b))return n;s.pathname=t.pathname,s.search=t.search}return s.toString()},w=(0,h.forwardRef)((e,t)=>{var n,r,a;let l=null!=(a=null!=(r=e.unoptimized)?r:null==(n=e.src.toString().split("?")[0])?void 0:n.endsWith(".svg"))?a:void 0;return(0,f.jsx)(d.default,p(c({},e),{placeholder:e.placeholder,loader:v,unoptimized:l,ref:t}))})},47025:(e,t,n)=>{Promise.resolve().then(n.bind(n,43432)),Promise.resolve().then(n.bind(n,72044)),Promise.resolve().then(n.bind(n,24323)),Promise.resolve().then(n.t.bind(n,35685,23)),Promise.resolve().then(n.bind(n,13957)),Promise.resolve().then(n.bind(n,71497))},71497:(e,t,n)=>{"use strict";n.r(t),n.d(t,{ClientPump:()=>u}),n(82920);var r=n(50628);let a="__alias__";var l=!1,s=new Set,o=new Map,i=new Map,u=e=>{var t;let{children:u,rawQueries:c,pumpEndpoint:p,pumpToken:d,initialState:h,initialResolvedChildren:f,apiVersion:m,previewRef:b}=e,v=r.useRef(d),[w,g]=r.useState(h),y=r.useRef(h);y.current=h;let[P,_]=r.useState(b),E=r.useRef(P);E.current=P;let k=r.useCallback(async()=>{let e,t,n,r=await Promise.all(c.map(async(r,l)=>{var s,u;if(!v.current)return console.warn("No pump token found. Skipping query."),null;let c=JSON.stringify(r),d=c+P,h=i.get(c)||(null==(u=y.current)||null==(s=u.responseHashes)?void 0:s[l])||"";if(o.has(d)){let r=o.get(d);if(performance.now()-r.start<32){let a=await r.response;return a?(a.newPumpToken&&(e=a.newPumpToken),t=a.pusherData,n=a.spaceID,a):null}}let f=fetch(p,{cache:"no-store",method:"POST",headers:{"content-type":"application/json","x-basehub-api-version":m,"x-basehub-ref":P},body:JSON.stringify({...r,pumpToken:v.current,lastResponseHash:h})}).then(async e=>{let{data:t=null,errors:n=null,newPumpToken:r,spaceID:l,pusherData:s,responseHash:o}=await e.json();return i.set(c,o),{data:function e(t){if("object"!=typeof t||null===t)return t;if(Array.isArray(t))return t.map(t=>e(t));let n={};for(let[r,l]of Object.entries(t))if(r.includes(a)){let[t,...s]=r.split(a);n[s.join(a)]=e(l)}else n[r]=e(l);return n}(t),spaceID:l,pusherData:s,newPumpToken:r,errors:n,responseHash:o,changed:h!==o}}).catch(e=>{console.error("Error fetching data from the BaseHub Draft API:\n              \n".concat(JSON.stringify(e,null,2),"\n              \nContact <EMAIL> for help."))});o.set(d,{start:performance.now(),response:f});let b=await f;return b?(b.newPumpToken&&(e=b.newPumpToken),t=b.pusherData,n=b.spaceID,b):null}));if(r.some(e=>null==e?void 0:e.changed)){if(!t||!n)return;g(e=>t&&n?{data:r.map((t,n)=>{var r,a,l;return(null==t?void 0:t.changed)?null!=(l=null==t?void 0:t.data)?l:null:null!=(a=null==e||null==(r=e.data)?void 0:r[n])?a:null}),errors:r.map((t,n)=>{var r,a,l;return(null==t?void 0:t.changed)?null!=(l=null==t?void 0:t.errors)?l:null:null!=(a=null==e||null==(r=e.errors)?void 0:r[n])?a:null}),responseHashes:r.map(e=>{var t;return null!=(t=null==e?void 0:e.responseHash)?t:""}),pusherData:t,spaceID:n}:e)}e&&(v.current=e)},[p,c,m,P]);r.useRef(null),r.useEffect(()=>{var e;if(!(null==w?void 0:w.errors))return;let t=null==(e=w.errors[0])?void 0:e[0];t&&console.error("Error fetching data from the BaseHub Draft API: ".concat(t.message).concat(t.path?" at ".concat(t.path.join(".")):""))},[null==w?void 0:w.errors]),r.useEffect(()=>{function e(){k()}return e(),s.add(e),()=>{s.delete(e)}},[k]);let[j,O]=r.useState(null),S=null==w||null==(t=w.pusherData)?void 0:t.channel_key,D=null==w?void 0:w.pusherData.app_key,I=null==w?void 0:w.pusherData.cluster;r.useEffect(()=>{if(!l&&D&&I)return l=!0,n.e(71).then(n.bind(n,9071)).then(e=>{O(new e.default(D,{cluster:I}))}).catch(e=>{console.log("error importing pusher"),console.error(e)}),()=>{l=!1}},[D,I]),r.useEffect(()=>{if(!S||!j)return;let e=j.subscribe(S);return e.bind("poke",e=>{var t;(null==e||null==(t=e.mutatedEntryTypes)?void 0:t.includes("block"))&&e.branch===E.current&&s.forEach(e=>e())}),()=>{e.unsubscribe()}},[j,S]),r.useEffect(()=>{function e(){let e=window.__bshb_ref;e&&"string"==typeof e&&_(e)}return e(),window.addEventListener("__bshb_ref_changed",e),()=>{window.removeEventListener("__bshb_ref_changed",e)}},[]);let T=r.useMemo(()=>null==w?void 0:w.data.map((e,t)=>{var n,r;return null!=(r=null!=e?e:null==h||null==(n=h.data)?void 0:n[t])?r:null}),[null==h?void 0:h.data,null==w?void 0:w.data]),[x,C]=r.useState("function"==typeof u?f:u);return r.useEffect(()=>{if(T)if("function"==typeof u){let e=u(T);e instanceof Promise?e.then(C):C(e)}else C(u)},[u,T]),null!=x?x:f}},72044:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>s});var r=n(50628),a=r.createContext(void 0),l=n(6024),s=e=>{let{children:t,snippets:n,storeSnippetSelection:s,groupId:o}=e,i=1===n.length,[u,c]=r.useState(n[0]);r.useEffect(()=>{document.querySelectorAll('[data-snippet-group-id="'.concat(o,'"]')).forEach(e=>{e.getAttribute("data-snippet-id")===(null==u?void 0:u.id)?(e.style.display="block",e.setAttribute("data-active","true")):(e.style.display="none",e.setAttribute("data-active","false"))})},[u,o]);let p=!i&&s?"__bshb-active-snippet-for-".concat(n.map(e=>e.label||e.id).sort((e,t)=>e.localeCompare(t)).join("-")):null;r.useEffect(()=>{var e;if(p){try{let t=null==(e=window.localStorage)?void 0:e.getItem(p);if(t){let e=n.find(e=>e.label===t||e.id===t);e&&c(e)}}catch(e){}return window.addEventListener("__bshb-snippet-change",t),()=>{window.removeEventListener("__bshb-snippet-change",t)}}function t(e){if(e.detail.key!==p)return;let t=n.find(t=>t.label===e.detail.snippet.label||t.id===e.detail.snippet.id);t&&c(t)}},[p,n]);let d=r.useCallback(e=>{var t;if(c(e),!p)return;try{null==(t=window.localStorage)||t.setItem(p,e.label||e.id)}catch(e){}let n=new CustomEvent("__bshb-snippet-change",{detail:{key:p,snippet:e}});window.dispatchEvent(n)},[p]);return(0,l.jsx)(a.Provider,{value:{snippets:n,activeSnippet:u,selectSnippet:d,groupId:o},children:t})}},82920:(e,t,n)=>{"use strict";n.d(t,{P:()=>a});var r=Object.getOwnPropertyNames,a=(e,t)=>function(){return t||(0,e[r(e)[0]])((t={exports:{}}).exports,t),t.exports}}},e=>{var t=t=>e(e.s=t);e.O(0,[213,685,757,913,499,358],()=>t(47025)),_N_E=e.O()}]);