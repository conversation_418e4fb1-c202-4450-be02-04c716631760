exports.id=4788,exports.ids=[4788],exports.modules={7820:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var n=r(46097);let s=async e=>[{type:"image/png",sizes:"192x192",url:(0,n.fillMetadataSegment)("/[locale]",await e.params,"apple-icon.png")+"?6b949b4e1bd36892"}]},15190:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var n=r(46097);let s=async e=>[{type:"image/png",sizes:"32x32",url:(0,n.fillMetadataSegment)("/[locale]",await e.params,"icon.png")+"?0fb4a24cefe3ddc0"}]},15330:(e,t,r)=>{"use strict";r.d(t,{PostHogProvider:()=>s});var n=r(6340);let s=(0,n.registerClientReference)(function(){throw Error("Attempted to call PostHogProvider() from the server but PostHogProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\analytics\\posthog\\client.tsx","PostHogProvider");(0,n.registerClientReference)(function(){throw Error("Attempted to call useAnalytics() from the server but useAnalytics is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\analytics\\posthog\\client.tsx","useAnalytics")},15813:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>m});var n=r(99730),s=r(74938),a=r(83590),o=r(24319),i=r.n(o),l=r(24087),d=r.n(l);let c=(0,a.cn)(d().variable,i().variable,"touch-manipulation font-sans antialiased");var u=r(74562),h=r(35371);let m=({error:e,reset:t})=>((0,h.useEffect)(()=>{(0,u.captureException)(e)},[e]),(0,n.jsx)("html",{lang:"en",className:c,children:(0,n.jsxs)("body",{children:[(0,n.jsx)("h1",{children:"Oops, something went wrong"}),(0,n.jsx)(s.$,{onClick:()=>t(),children:"Try again"})]})}))},17218:(e,t,r)=>{"use strict";r.d(t,{J:()=>c}),Object.getOwnPropertyNames;var n=r(23233),s=r(51472),a=n.lazy(()=>Promise.resolve().then(r.bind(r,40356)).then(e=>({default:e.ClientPump}))),o=new Map,i=null,l=null,d=null,c=async({children:e,queries:t,bind:c,...u})=>{let h,m=[],p=[];if(s.f0){let e=!1;if(void 0===u.draft)try{let{draftMode:t}=await Promise.resolve().then(r.bind(r,62644));e=(await t()).isEnabled}catch(e){}e&&void 0===u.draft&&(u.draft=!0)}let{headers:f,draft:b}=(0,s.GJ)(u),g=f["x-basehub-token"],v=f["x-basehub-api-version"],x="https://aws.basehub.com/pump",w=0===t.length,j=b&&w?[{_sys:{id:!0}}]:t;if(b&&s.f0)try{let{cookies:e}=await Promise.resolve().then(r.bind(r,62644)),t=await e(),n=t.get("bshb-preview-ref-"+s.Y2.repoHash)?.value;n&&(f["x-basehub-ref"]=n)}catch(e){}let y=await Promise.all(j.map(async(e,t)=>{let r,n=(0,s.xg)(e),a=JSON.stringify({...n,...f})+(b?"_draft":"_prod");if(o.has(a)){let e=o.get(a);performance.now()-e.start<32&&(r=await e.data)}if(!r){let c=b?fetch(x,{...s.f0?{cache:"no-store"}:{},method:"POST",headers:{...f,"content-type":"application/json","x-basehub-token":g,"x-basehub-api-version":v},body:JSON.stringify(n)}).then(async e=>{let{data:r=null,newPumpToken:n,errors:a=null,spaceID:o,pusherData:c,responseHash:u}=await e.json();return i=n,d=c,l=o,m.push(a),p[t]=u,s.fi.replaceSystemAliases(r)}):(0,s.fi)(u).query(e);o.set(a,{start:performance.now(),data:c}),r=await c}return{data:r,rawQueryOp:n}}));c&&(e=e.bind(null,c));let P=e(y.map(e=>e.data));if(h=P instanceof Promise?await P?.catch(e=>{if(b)return console.error("Error in Pump children function",e),null;throw e}):P,b){if(!i||!l||!d)throw console.log("Results (length):",y?.length),console.log("Errors:",JSON.stringify(m,null,2)),console.log("Pump Endpoint:",x),console.log("Pump Token:",i),console.log("Space ID:",l),console.log("Pusher Data:",d),console.log("Response Hashes:",JSON.stringify(p,null,2)),Error("Pump did not return the necessary data. Look at the logs to see what's missing.");return n.createElement(a,{rawQueries:y.map(e=>e.rawQueryOp),initialState:{data:w?[]:y.map(e=>e.data??null),errors:m,responseHashes:p,pusherData:d,spaceID:l},pumpEndpoint:x,pumpToken:i??void 0,initialResolvedChildren:h,apiVersion:v,previewRef:f["x-basehub-ref"]||s.Y2.ref},e)}return h}},18362:(e,t,r)=>{"use strict";r.d(t,{x:()=>p,w:()=>f});var n=r(94752),s=r(52661);r(37091);var a=r(29804),o=r(23055),i=r(17218),l=r(82263);let d=(0,l.H)().BETTERSTACK_API_KEY,c=(0,l.H)().BETTERSTACK_URL,u=async()=>{if(!d||!c)return null;let e="bg-muted-foreground",t="Unable to fetch status";try{let r=await fetch("https://uptime.betterstack.com/api/v2/monitors",{headers:{Authorization:`Bearer ${d}`}});if(!r.ok)throw Error("Failed to fetch status");let{data:n}=await r.json(),s=n.filter(e=>"up"===e.attributes.status).length/n.length;0===s?(e="bg-destructive",t="Degraded performance"):s<1?(e="bg-warning",t="Partial outage"):(e="bg-success",t="All systems normal")}catch{e="bg-muted-foreground",t="Unable to fetch status"}return(0,n.jsxs)("a",{className:"flex items-center gap-3 font-medium text-sm",target:"_blank",rel:"noreferrer",href:c,children:[(0,n.jsxs)("span",{className:"relative flex h-2 w-2",children:[(0,n.jsx)("span",{className:`absolute inline-flex h-full w-full animate-ping rounded-full opacity-75 ${e}`}),(0,n.jsx)("span",{className:`relative inline-flex h-2 w-2 rounded-full ${e}`})]}),(0,n.jsx)("span",{className:"text-muted-foreground",children:t})]})};var h=r(49499),m=r.n(h);let p=async function([e]){let t=[{title:"Home",href:"/",description:""},{title:"Pages",description:"Managing a small business today is already tough.",items:[{title:"Blog",href:"/blog"}]},{title:"Legal",description:"We stay on top of the latest legal requirements.",items:e.legalPages.items.map(e=>({title:e._title,href:`/legal/${e._slug}`}))}];return a._.NEXT_PUBLIC_DOCS_URL&&t.at(1)?.items?.push({title:"Docs",href:a._.NEXT_PUBLIC_DOCS_URL}),(0,n.jsx)("section",{className:"dark border-foreground/10 border-t",children:(0,n.jsx)("div",{className:"w-full bg-background py-20 text-foreground lg:py-40",children:(0,n.jsx)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:(0,n.jsxs)("div",{className:"grid items-center gap-10 lg:grid-cols-2",children:[(0,n.jsxs)("div",{className:"flex flex-col items-start gap-8",children:[(0,n.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,n.jsx)("h2",{className:"max-w-xl text-left font-regular text-3xl tracking-tighter md:text-5xl",children:"Cubent"}),(0,n.jsx)("p",{className:"max-w-lg text-left text-foreground/75 text-lg leading-relaxed tracking-tight",children:"The future of AI-powered coding."})]}),(0,n.jsx)("div",{className:"flex flex-col gap-4",children:(0,n.jsx)(u,{})})]}),(0,n.jsx)("div",{className:"grid items-start gap-10 lg:grid-cols-3",children:t.map(e=>(0,n.jsx)("div",{className:"flex flex-col items-start gap-1 text-base",children:(0,n.jsxs)("div",{className:"flex flex-col gap-2",children:[e.href?(0,n.jsx)(m(),{href:e.href,className:"flex items-center justify-between",target:e.href.includes("http")?"_blank":void 0,rel:e.href.includes("http")?"noopener noreferrer":void 0,children:(0,n.jsx)("span",{className:"text-xl",children:e.title})}):(0,n.jsx)("p",{className:"text-xl",children:e.title}),e.items?.map(e=>(0,n.jsx)(m(),{href:e.href,className:"flex items-center justify-between",target:e.href.includes("http")?"_blank":void 0,rel:e.href.includes("http")?"noopener noreferrer":void 0,children:(0,n.jsx)("span",{className:"text-foreground/75",children:e.title})},e.title))]})},e.title))})]})})})})},f=()=>(0,n.jsx)(i.J,{queries:[o.a.postsQuery],children:(0,s.A)(p,"405efdbdcc87c20941759ae1bb1e4f3629f6226400",null)})},18915:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>i});var n=r(99730),s=r(26708),a=r(45550),o=r(48585);let i=({privacyUrl:e,termsUrl:t,helpUrl:r,...i})=>{let{resolvedTheme:l}=(0,o.D)(),d="dark"===l?a.dark:void 0;return(0,n.jsx)(s.lJ,{...i,appearance:{layout:{privacyPageUrl:e,termsPageUrl:t,helpPageUrl:r},baseTheme:d,elements:{dividerLine:"bg-border",socialButtonsIconButton:"bg-card",navbarButton:"text-foreground",organizationSwitcherTrigger__open:"bg-background",organizationPreviewMainIdentifier:"text-foreground",organizationSwitcherTriggerIcon:"text-muted-foreground",organizationPreview__organizationSwitcherTrigger:"gap-2",organizationPreviewAvatarContainer:"shrink-0"},variables:{fontFamily:"var(--font-sans)",fontFamilyButtons:"var(--font-sans)",fontWeight:{bold:"var(--font-weight-bold)",normal:"var(--font-weight-normal)",medium:"var(--font-weight-medium)"}}}})}},21731:(e,t,r)=>{Promise.resolve().then(r.bind(r,50273)),Promise.resolve().then(r.bind(r,86332)),Promise.resolve().then(r.bind(r,19859)),Promise.resolve().then(r.bind(r,19865)),Promise.resolve().then(r.bind(r,354)),Promise.resolve().then(r.bind(r,27022)),Promise.resolve().then(r.bind(r,38980)),Promise.resolve().then(r.bind(r,15138)),Promise.resolve().then(r.bind(r,48585)),Promise.resolve().then(r.t.bind(r,41265,23)),Promise.resolve().then(r.t.bind(r,96908,23)),Promise.resolve().then(r.bind(r,22683)),Promise.resolve().then(r.bind(r,89620)),Promise.resolve().then(r.bind(r,18915)),Promise.resolve().then(r.bind(r,79047)),Promise.resolve().then(r.bind(r,38327)),Promise.resolve().then(r.bind(r,84685)),Promise.resolve().then(r.bind(r,41033))},22589:(e,t,r)=>{"use strict";r.d(t,{xK:()=>m,qr:()=>p,z2:()=>f,q2:()=>b,M7:()=>g});var n=r(52661);r(37091),Object.create;var s=Object.defineProperty,a=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,i=(Object.getPrototypeOf,Object.prototype.hasOwnProperty),l=r(23233),d=r(62644),c=r(55686),u=r(51472),h=l.lazy(()=>Promise.resolve().then(r.bind(r,43401)).then(e=>({default:e.ClientConditionalRenderer})));let m=async function(e,{bshbPreviewToken:t}){try{let{headers:r,url:n}=(0,u.GJ)(e),s=v(n,"/api/nextjs/preview-auth"),a=await fetch(s,{cache:"no-store",method:"POST",headers:{"content-type":"application/json","x-basehub-token":r["x-basehub-token"]},body:JSON.stringify({bshbPreview:t})});if(!a.headers.get("content-type")?.includes("json"))return{status:400,response:{error:"Bad request"}};let o=await a.json();return 200===a.status&&(await (0,d.draftMode)()).enable(),{status:a.status,response:o}}catch(e){return{status:500,response:{error:"Something went wrong"}}}},p=async function(e,{bshbPreviewToken:t}){try{let{headers:r,url:n,isForcedDraft:s}=(0,u.GJ)(e);if(!1===(await (0,d.draftMode)()).isEnabled&&!s&&!t)return{status:403,response:{error:"Unauthorized"}};let a=v(n,"/api/nextjs/latest-branches"),o=await fetch(a,{cache:"no-store",method:"GET",headers:{"content-type":"application/json","x-basehub-token":r["x-basehub-token"],...t&&{"x-basehub-preview-token":t},...s&&{"x-basehub-forced-draft":"true"}}});if(!o.headers.get("content-type")?.includes("json"))return{status:400,response:{error:"Bad request"}};let i=await o.json();return{status:o.status,response:i}}catch(e){return{status:500,response:{error:"Something went wrong"}}}},f=async function(){(await (0,d.draftMode)()).disable()},b=async function(e,{bshbPreviewToken:t,ref:r}){let{headers:n,url:s}=(0,u.GJ)(e),a=v(s,"/api/nextjs/pending-tags");if(!t)return{success:!1,error:"Unauthorized"};let o=await fetch(a,{cache:"no-store",method:"GET",headers:{"content-type":"application/json","x-basehub-token":n["x-basehub-token"],"x-basehub-ref":r||n["x-basehub-ref"],"x-basehub-preview-token":t,"x-basehub-sdk-build-id":n["x-basehub-sdk-build-id"]}});if(200!==o.status)return{success:!1,message:`Received status ${o.status} from server`};let i=await o.json();try{let{tags:e}=i;if(!e||!Array.isArray(e)||0===e.length)return{success:!0,message:"No tags to revalidate"};return await Promise.all(e.map(async e=>{let t=e.startsWith("basehub-")?e:`basehub-${e}`;await (0,c.revalidateTag)(t)})),{success:!0,message:`Revalidated ${e.length} tags`}}catch(e){return console.log(i),console.error(e),{success:!1,message:"Something went wrong while revalidating tags"}}};var g=async({...e})=>{let{isForcedDraft:t}=(0,u.GJ)(e),r=(0,n.A)(m,"60ac4068834bf9e1b32327478a0425a5daded1d553",null),s=(0,n.A)(p,"6040ce2583939275b9b79a23c761210186958f33ca",null),a=(0,n.A)(f,"00e578e70edcb55dedc8be97f84cdf9c4ce9b9fbb2",null),o=(0,n.A)(b,"60d7e646f4b8f4f2b31c38fcbc9963402a4f1620ad",null),i=r.bind(null,e),c=s.bind(null,e),g=o.bind(null,e);return l.createElement(h,{draft:(await (0,d.draftMode)()).isEnabled,isForcedDraft:t,enableDraftMode:i,disableDraftMode:a,revalidateTags:g,getLatestBranches:c,resolvedRef:u.Y2})};function v(e,t){let r;switch(!0){case e.origin.includes("api.basehub.com"):r="https://basehub.com"+t+e.search+e.hash;break;case e.origin.includes("api.bshb.dev"):r="https://basehub.dev"+t+e.search+e.hash;break;case e.origin.includes("localhost:3001"):r="http://localhost:3000"+t+e.search+e.hash;break;default:r=e.origin+t+e.search+e.hash}return r}},28364:(e,t,r)=>{"use strict";r.d(t,{H:()=>a});var n=r(71166),s=r(25);let a=()=>(0,n.w)({client:{NEXT_PUBLIC_POSTHOG_KEY:s.z.string().startsWith("phc_").optional(),NEXT_PUBLIC_POSTHOG_HOST:s.z.string().url().optional(),NEXT_PUBLIC_GA_MEASUREMENT_ID:s.z.string().startsWith("G-").optional()},runtimeEnv:{NEXT_PUBLIC_POSTHOG_KEY:"phc_IIiOB59nWFyFh8azKXcqkOucMA9x5jTUYPTEDx2ccP9",NEXT_PUBLIC_POSTHOG_HOST:"https://us.i.posthog.com",NEXT_PUBLIC_GA_MEASUREMENT_ID:"G-PLACEHOLDER123"}})},28427:(e,t,r)=>{var n={"./en.json":[94202,4202]};function s(e){if(!r.o(n,e))return Promise.resolve().then(()=>{var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t});var t=n[e],s=t[0];return r.e(t[1]).then(()=>r.t(s,19))}s.keys=()=>Object.keys(n),s.id=28427,e.exports=s},30846:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,99597,23)),Promise.resolve().then(r.t.bind(r,71453,23)),Promise.resolve().then(r.t.bind(r,20213,23)),Promise.resolve().then(r.t.bind(r,13748,23)),Promise.resolve().then(r.t.bind(r,52812,23)),Promise.resolve().then(r.t.bind(r,73488,23)),Promise.resolve().then(r.t.bind(r,20134,23)),Promise.resolve().then(r.t.bind(r,15336,23))},34226:(e,t,r)=>{Promise.resolve().then(r.bind(r,15813))},34526:(e,t,r)=>{"use strict";r.d(t,{P:()=>d,f:()=>u});var n=Object.create,s=Object.defineProperty,a=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,i=Object.getPrototypeOf,l=Object.prototype.hasOwnProperty,d=(e,t)=>function(){return t||(0,e[o(e)[0]])((t={exports:{}}).exports,t),t.exports},c=(e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let i of o(t))l.call(e,i)||i===r||s(e,i,{get:()=>t[i],enumerable:!(n=a(t,i))||n.enumerable});return e},u=(e,t,r)=>(r=null!=e?n(i(e)):{},c(!t&&e&&e.__esModule?r:s(r,"default",{value:e,enumerable:!0}),e))},35574:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,10487,23)),Promise.resolve().then(r.t.bind(r,89819,23)),Promise.resolve().then(r.t.bind(r,73391,23)),Promise.resolve().then(r.t.bind(r,31310,23)),Promise.resolve().then(r.t.bind(r,28138,23)),Promise.resolve().then(r.t.bind(r,82926,23)),Promise.resolve().then(r.t.bind(r,58084,23)),Promise.resolve().then(r.t.bind(r,1934,23))},37919:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>T});var n=r(94752);r(77648);var s=r(77021),a=r(29329),o=r(28364),i=r(15330),l=r(44920);let{NEXT_PUBLIC_GA_MEASUREMENT_ID:d}=(0,o.H)(),c=({children:e})=>(0,n.jsxs)(i.PostHogProvider,{children:[e,(0,n.jsx)(l.Analytics,{}),d&&(0,n.jsx)(a.GoogleAnalytics,{gaId:d})]});var u=r(44577),h=r(54191),m=r(90411),p=r(15991);let f=({children:e,...t})=>(0,n.jsx)(p.ThemeProvider,{attribute:"class",defaultTheme:"system",enableSystem:!0,disableTransitionOnChange:!0,...t,children:e}),b=({children:e,privacyUrl:t,termsUrl:r,helpUrl:s,...a})=>(0,n.jsx)(f,{...a,children:(0,n.jsx)(u.AuthProvider,{privacyUrl:t,termsUrl:r,helpUrl:s,children:(0,n.jsxs)(c,{children:[(0,n.jsx)(m.TooltipProvider,{children:e}),(0,n.jsx)(h.Toaster,{})]})})});var g=r(48384),v=r(94055),x=r.n(v),w=r(29663),j=r.n(w);let y=(0,g.cn)(j().variable,x().variable,"touch-manipulation font-sans antialiased");var P=r(99956),C=r(51460);let N=()=>(0,C.H)().FLAGS_SECRET?(0,n.jsx)(P.N,{}):null;var E=r(77422),_=r(18362),k=r(74664);let T=async({children:e,params:t})=>{let{locale:r}=await t,a=await (0,E.T)(r);return(0,n.jsx)("html",{lang:r,className:(0,g.cn)(y,"scroll-smooth"),suppressHydrationWarning:!0,children:(0,n.jsxs)("body",{children:[(0,n.jsxs)(b,{children:[(0,n.jsx)(k.Header,{dictionary:a}),e,(0,n.jsx)(_.w,{})]}),(0,n.jsx)(N,{}),(0,n.jsx)(s.M,{})]})})}},38327:(e,t,r)=>{"use strict";r.r(t),r.d(t,{ClientPump:()=>d}),r(76262);var n=r(35371);let s="__alias__";var a=!1,o=new Set,i=new Map,l=new Map,d=({children:e,rawQueries:t,pumpEndpoint:d,pumpToken:c,initialState:u,initialResolvedChildren:h,apiVersion:m,previewRef:p})=>{let f=n.useRef(c),[b,g]=n.useState(u),v=n.useRef(u);v.current=u;let[x,w]=n.useState(p),j=n.useRef(x);j.current=x;let y=n.useCallback(async()=>{let e,r,n,a=await Promise.all(t.map(async(t,a)=>{if(!f.current)return console.warn("No pump token found. Skipping query."),null;let o=JSON.stringify(t),c=o+x,u=l.get(o)||v.current?.responseHashes?.[a]||"";if(i.has(c)){let t=i.get(c);if(performance.now()-t.start<32){let s=await t.response;return s?(s.newPumpToken&&(e=s.newPumpToken),r=s.pusherData,n=s.spaceID,s):null}}let h=fetch(d,{cache:"no-store",method:"POST",headers:{"content-type":"application/json","x-basehub-api-version":m,"x-basehub-ref":x},body:JSON.stringify({...t,pumpToken:f.current,lastResponseHash:u})}).then(async e=>{let{data:t=null,errors:r=null,newPumpToken:n,spaceID:a,pusherData:i,responseHash:d}=await e.json();return l.set(o,d),{data:function e(t){if("object"!=typeof t||null===t)return t;if(Array.isArray(t))return t.map(t=>e(t));let r={};for(let[n,a]of Object.entries(t))if(n.includes(s)){let[t,...o]=n.split(s);r[o.join(s)]=e(a)}else r[n]=e(a);return r}(t),spaceID:a,pusherData:i,newPumpToken:n,errors:r,responseHash:d,changed:u!==d}}).catch(e=>{console.error(`Error fetching data from the BaseHub Draft API:
              
${JSON.stringify(e,null,2)}
              
Contact <EMAIL> for help.`)});i.set(c,{start:performance.now(),response:h});let p=await h;return p?(p.newPumpToken&&(e=p.newPumpToken),r=p.pusherData,n=p.spaceID,p):null}));if(a.some(e=>e?.changed)){if(!r||!n)return;g(e=>r&&n?{data:a.map((t,r)=>t?.changed?t?.data??null:e?.data?.[r]??null),errors:a.map((t,r)=>t?.changed?t?.errors??null:e?.errors?.[r]??null),responseHashes:a.map(e=>e?.responseHash??""),pusherData:r,spaceID:n}:e)}e&&(f.current=e)},[d,t,m,x]);n.useRef(null),n.useEffect(()=>{if(!b?.errors)return;let e=b.errors[0]?.[0];e&&console.error(`Error fetching data from the BaseHub Draft API: ${e.message}${e.path?` at ${e.path.join(".")}`:""}`)},[b?.errors]),n.useEffect(()=>{function e(){y()}return e(),o.add(e),()=>{o.delete(e)}},[y]);let[P,C]=n.useState(null),N=b?.pusherData?.channel_key,E=b?.pusherData.app_key,_=b?.pusherData.cluster;n.useEffect(()=>{if(!a&&E&&_)return a=!0,r.e(3611).then(r.bind(r,83611)).then(e=>{C(new e.default(E,{cluster:_}))}).catch(e=>{console.log("error importing pusher"),console.error(e)}),()=>{a=!1}},[E,_]),n.useEffect(()=>{if(!N||!P)return;let e=P.subscribe(N);return e.bind("poke",e=>{e?.mutatedEntryTypes?.includes("block")&&e.branch===j.current&&o.forEach(e=>e())}),()=>{e.unsubscribe()}},[P,N]),n.useEffect(()=>{function e(){let e=window.__bshb_ref;e&&"string"==typeof e&&w(e)}return e(),window.addEventListener("__bshb_ref_changed",e),()=>{window.removeEventListener("__bshb_ref_changed",e)}},[]);let k=n.useMemo(()=>b?.data.map((e,t)=>e??u?.data?.[t]??null),[u?.data,b?.data]),[T,O]=n.useState("function"==typeof e?h:e);return n.useEffect(()=>{if(k)if("function"==typeof e){let t=e(k);t instanceof Promise?t.then(O):O(t)}else O(e)},[e,k]),T??h}},39440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var n=r(46097);let s=async e=>[{type:"image/png",width:1200,height:630,url:(0,n.fillMetadataSegment)("/[locale]",await e.params,"opengraph-image.png")+"?1de9f909622c0a32"}]},40356:(e,t,r)=>{"use strict";r.r(t),r.d(t,{ClientPump:()=>n});let n=(0,r(6340).registerClientReference)(function(){throw Error("Attempted to call ClientPump() from the server but ClientPump is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\cms\\.basehub\\react-pump\\client-pump-WYUPTPKD.js","ClientPump")},41033:(e,t,r)=>{"use strict";r.d(t,{TooltipProvider:()=>a});var n=r(99730);r(35371);var s=r(47887);function a({delayDuration:e=0,...t}){return(0,n.jsx)(s.Kq,{"data-slot":"tooltip-provider",delayDuration:e,...t})}r(83590)},43401:(e,t,r)=>{"use strict";r.r(t),r.d(t,{ClientConditionalRenderer:()=>n});let n=(0,r(6340).registerClientReference)(function(){throw Error("Attempted to call ClientConditionalRenderer() from the server but ClientConditionalRenderer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\cms\\.basehub\\next-toolbar\\client-conditional-renderer-KQINRCBN.js","ClientConditionalRenderer")},44577:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>n});let n=(0,r(6340).registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\auth\\provider.tsx","AuthProvider")},45707:(e,t,r)=>{Promise.resolve().then(r.bind(r,74664)),Promise.resolve().then(r.t.bind(r,21034,23)),Promise.resolve().then(r.t.bind(r,41853,23)),Promise.resolve().then(r.t.bind(r,48031,23)),Promise.resolve().then(r.t.bind(r,80476,23)),Promise.resolve().then(r.bind(r,44920)),Promise.resolve().then(r.bind(r,11106)),Promise.resolve().then(r.bind(r,8340)),Promise.resolve().then(r.bind(r,15991)),Promise.resolve().then(r.t.bind(r,49499,23)),Promise.resolve().then(r.t.bind(r,21970,23)),Promise.resolve().then(r.bind(r,93665)),Promise.resolve().then(r.bind(r,15330)),Promise.resolve().then(r.bind(r,44577)),Promise.resolve().then(r.bind(r,43401)),Promise.resolve().then(r.bind(r,40356)),Promise.resolve().then(r.bind(r,54191)),Promise.resolve().then(r.bind(r,90411))},48384:(e,t,r)=>{"use strict";r.d(t,{Z:()=>o,cn:()=>a}),r(42870);var n=r(57752);r(93665);var s=r(48410);let a=(...e)=>(0,s.QP)((0,n.$)(e)),o=e=>e.charAt(0).toUpperCase()+e.slice(1)},50273:(e,t,r)=>{"use strict";r.d(t,{Header:()=>$});var n=r(99730),s=r(62458),a=r(48585),o=r(74938),i=r(35371),l=r(33187),d=r(83590);function c({...e}){return(0,n.jsx)(l.bL,{"data-slot":"dropdown-menu",...e})}function u({...e}){return(0,n.jsx)(l.l9,{"data-slot":"dropdown-menu-trigger",...e})}function h({className:e,sideOffset:t=4,...r}){return(0,n.jsx)(l.ZL,{children:(0,n.jsx)(l.UC,{"data-slot":"dropdown-menu-content",sideOffset:t,className:(0,d.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",e),...r})})}function m({className:e,inset:t,variant:r="default",...s}){return(0,n.jsx)(l.q7,{"data-slot":"dropdown-menu-item","data-inset":t,"data-variant":r,className:(0,d.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...s})}function p({className:e,inset:t,...r}){return(0,n.jsx)(l.JU,{"data-slot":"dropdown-menu-label","data-inset":t,className:(0,d.cn)("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",e),...r})}function f({className:e,...t}){return(0,n.jsx)(l.wv,{"data-slot":"dropdown-menu-separator",className:(0,d.cn)("bg-border -mx-1 my-1 h-px",e),...t})}let b=[{label:"Light",value:"light"},{label:"Dark",value:"dark"},{label:"System",value:"system"}],g=()=>{let{setTheme:e}=(0,a.D)();return(0,n.jsxs)(c,{children:[(0,n.jsx)(u,{asChild:!0,children:(0,n.jsxs)(o.$,{variant:"ghost",size:"icon",className:"shrink-0 text-foreground",children:[(0,n.jsx)(s.gLX,{className:"dark:-rotate-90 h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:scale-0"}),(0,n.jsx)(s.rRK,{className:"absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100"}),(0,n.jsx)("span",{className:"sr-only",children:"Toggle theme"})]})}),(0,n.jsx)(h,{children:b.map(({label:t,value:r})=>(0,n.jsx)(m,{onClick:()=>e(r),children:t},r))})]})};var v=r(22866),x=r(72795),w=r(10439);function j({className:e,children:t,viewport:r=!0,...s}){return(0,n.jsxs)(v.bL,{"data-slot":"navigation-menu","data-viewport":r,className:(0,d.cn)("group/navigation-menu relative flex max-w-max flex-1 items-center justify-center",e),...s,children:[t,r&&(0,n.jsx)(_,{})]})}function y({className:e,...t}){return(0,n.jsx)(v.B8,{"data-slot":"navigation-menu-list",className:(0,d.cn)("group flex flex-1 list-none items-center justify-center gap-1",e),...t})}function P({className:e,...t}){return(0,n.jsx)(v.q7,{"data-slot":"navigation-menu-item",className:(0,d.cn)("relative",e),...t})}let C=(0,x.F)("group inline-flex h-9 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground disabled:pointer-events-none disabled:opacity-50 data-[state=open]:hover:bg-accent data-[state=open]:text-accent-foreground data-[state=open]:focus:bg-accent data-[state=open]:bg-accent/50 focus-visible:ring-ring/50 outline-none transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1");function N({className:e,children:t,...r}){return(0,n.jsxs)(v.l9,{"data-slot":"navigation-menu-trigger",className:(0,d.cn)(C(),"group",e),...r,children:[t," ",(0,n.jsx)(w.A,{className:"relative top-[1px] ml-1 size-3 transition duration-300 group-data-[state=open]:rotate-180","aria-hidden":"true"})]})}function E({className:e,...t}){return(0,n.jsx)(v.UC,{"data-slot":"navigation-menu-content",className:(0,d.cn)("data-[motion^=from-]:animate-in data-[motion^=to-]:animate-out data-[motion^=from-]:fade-in data-[motion^=to-]:fade-out data-[motion=from-end]:slide-in-from-right-52 data-[motion=from-start]:slide-in-from-left-52 data-[motion=to-end]:slide-out-to-right-52 data-[motion=to-start]:slide-out-to-left-52 top-0 left-0 w-full p-2 pr-2.5 md:absolute md:w-auto","group-data-[viewport=false]/navigation-menu:bg-popover group-data-[viewport=false]/navigation-menu:text-popover-foreground group-data-[viewport=false]/navigation-menu:data-[state=open]:animate-in group-data-[viewport=false]/navigation-menu:data-[state=closed]:animate-out group-data-[viewport=false]/navigation-menu:data-[state=closed]:zoom-out-95 group-data-[viewport=false]/navigation-menu:data-[state=open]:zoom-in-95 group-data-[viewport=false]/navigation-menu:data-[state=open]:fade-in-0 group-data-[viewport=false]/navigation-menu:data-[state=closed]:fade-out-0 group-data-[viewport=false]/navigation-menu:top-full group-data-[viewport=false]/navigation-menu:mt-1.5 group-data-[viewport=false]/navigation-menu:overflow-hidden group-data-[viewport=false]/navigation-menu:rounded-md group-data-[viewport=false]/navigation-menu:border group-data-[viewport=false]/navigation-menu:shadow group-data-[viewport=false]/navigation-menu:duration-200 **:data-[slot=navigation-menu-link]:focus:ring-0 **:data-[slot=navigation-menu-link]:focus:outline-none",e),...t})}function _({className:e,...t}){return(0,n.jsx)("div",{className:(0,d.cn)("absolute top-full left-0 isolate z-50 flex justify-center"),children:(0,n.jsx)(v.LM,{"data-slot":"navigation-menu-viewport",className:(0,d.cn)("origin-top-center bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-90 relative mt-1.5 h-[var(--radix-navigation-menu-viewport-height)] w-full overflow-hidden rounded-md border shadow md:w-[var(--radix-navigation-menu-viewport-width)]",e),...t})})}function k({className:e,...t}){return(0,n.jsx)(v.N_,{"data-slot":"navigation-menu-link",className:(0,d.cn)("data-[active=true]:focus:bg-accent data-[active=true]:hover:bg-accent data-[active=true]:bg-accent/50 data-[active=true]:text-accent-foreground hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus-visible:ring-ring/50 [&_svg:not([class*='text-'])]:text-muted-foreground flex flex-col gap-1 rounded-sm p-2 text-sm transition-all outline-none focus-visible:ring-[3px] focus-visible:outline-1 [&_svg:not([class*='size-'])]:size-4",e),...t})}var T=r(37047),O=r(88529),S=r(25202),U=r(41265),A=r.n(U),R=r(48473);let D={src:"/_next/static/media/logo.bef10624.svg",height:24,width:24,blurWidth:0,blurHeight:0};var L=r(97480);function H({className:e,...t}){return(0,n.jsx)(L.bL,{"data-slot":"avatar",className:(0,d.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",e),...t})}function I({className:e,...t}){return(0,n.jsx)(L._V,{"data-slot":"avatar-image",className:(0,d.cn)("aspect-square size-full",e),...t})}function z({className:e,...t}){return(0,n.jsx)(L.H4,{"data-slot":"avatar-fallback",className:(0,d.cn)("bg-muted flex size-full items-center justify-center rounded-full",e),...t})}var B=r(39378),F=r(52476),M=r(4862);let G=({user:e})=>{let t=e.fullName||e.firstName||e.emailAddresses[0]?.emailAddress||"User",r=e.emailAddresses[0]?.emailAddress;return(0,n.jsxs)(c,{children:[(0,n.jsx)(u,{asChild:!0,children:(0,n.jsx)(o.$,{variant:"ghost",className:"relative h-10 w-10 rounded-full",children:(0,n.jsxs)(H,{className:"h-10 w-10",children:[(0,n.jsx)(I,{src:e.imageUrl,alt:t}),(0,n.jsx)(z,{children:(e=>e?e.split(" ").map(e=>e[0]).join("").toUpperCase().slice(0,2):"U")(e.fullName)})]})})}),(0,n.jsxs)(h,{className:"w-56",align:"end",forceMount:!0,children:[(0,n.jsx)(p,{className:"font-normal",children:(0,n.jsxs)("div",{className:"flex flex-col space-y-1",children:[(0,n.jsx)("p",{className:"text-sm font-medium leading-none",children:t}),r&&(0,n.jsx)("p",{className:"text-xs leading-none text-muted-foreground",children:r})]})}),(0,n.jsx)(f,{}),(0,n.jsx)(m,{asChild:!0,children:(0,n.jsxs)(A(),{href:"https://app.cubent.dev",className:"flex items-center",children:[(0,n.jsx)(B.A,{className:"mr-2 h-4 w-4"}),(0,n.jsx)("span",{children:"Dashboard"})]})}),(0,n.jsx)(m,{asChild:!0,children:(0,n.jsxs)(A(),{href:"https://app.cubent.dev/settings",className:"flex items-center",children:[(0,n.jsx)(F.A,{className:"mr-2 h-4 w-4"}),(0,n.jsx)("span",{children:"Settings"})]})}),(0,n.jsx)(f,{}),(0,n.jsxs)(m,{onClick:()=>{window.location.href="https://app.cubent.dev/sign-out"},className:"flex items-center",children:[(0,n.jsx)(M.A,{className:"mr-2 h-4 w-4"}),(0,n.jsx)("span",{children:"Sign out"})]})]})]})},$=({dictionary:e})=>{let{isAuthenticated:t,user:r,isLoading:s}=function(){let[e,t]=(0,i.useState)({isAuthenticated:!1,user:null,isLoading:!0});return e}(),a=[{title:e.web.header.home,href:"/",description:""},{title:"Features",description:"Discover what makes Cubent Coder powerful",items:[{title:"Chat Mode",href:"/features/chat"},{title:"Agent Mode",href:"/features/agent"},{title:"Custom Modes",href:"/features/custom"}]},{title:e.web.header.docs,href:"https://cubentdev.mintlify.app",description:""},{title:e.web.header.blog,href:"/blog",description:""},{title:"Support",href:"https://discord.gg/cubent",description:""}],[l,d]=(0,i.useState)(!1);return(0,n.jsx)("header",{className:"sticky top-0 left-0 z-40 w-full border-b bg-background",children:(0,n.jsxs)("div",{className:"relative w-full max-w-none flex min-h-20 flex-row items-center justify-between",style:{paddingInline:"clamp(1rem, 2.5%, 2rem)"},children:[(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)(R.default,{src:D,alt:"Cubent Logo",width:32,height:32,className:"dark:invert"}),(0,n.jsx)("p",{className:"whitespace-nowrap font-semibold",children:"Cubent"})]}),(0,n.jsx)("div",{className:"hidden flex-row items-center justify-center gap-3 lg:flex absolute left-1/2 transform -translate-x-1/2 top-1/2 -translate-y-1/2",children:(0,n.jsx)(j,{className:"flex items-center justify-center",children:(0,n.jsx)(y,{className:"flex flex-row justify-center gap-3",children:a.map(e=>(0,n.jsx)(P,{children:e.href?(0,n.jsx)(n.Fragment,{children:(0,n.jsx)(k,{asChild:!0,children:(0,n.jsx)(o.$,{variant:"ghost",asChild:!0,children:(0,n.jsx)(A(),{href:e.href,target:e.href.startsWith("http")?"_blank":void 0,rel:e.href.startsWith("http")?"noopener noreferrer":void 0,children:e.title})})})}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(N,{className:"font-medium text-sm",children:e.title}),(0,n.jsx)(E,{className:"!w-[450px] p-4",children:(0,n.jsxs)("div",{className:"flex grid-cols-2 flex-col gap-4 lg:grid",children:[(0,n.jsxs)("div",{className:"flex h-full flex-col justify-between",children:[(0,n.jsxs)("div",{className:"flex flex-col",children:[(0,n.jsx)("p",{className:"text-base",children:e.title}),(0,n.jsx)("p",{className:"text-muted-foreground text-sm",children:e.description})]}),(0,n.jsx)(o.$,{size:"sm",className:"mt-10",asChild:!0,children:(0,n.jsx)(A(),{href:"https://marketplace.visualstudio.com/items?itemName=cubent.cubent",children:"Download Extension"})})]}),(0,n.jsx)("div",{className:"flex h-full flex-col justify-end text-sm",children:e.items?.map((e,t)=>(0,n.jsxs)(k,{href:e.href,className:"flex flex-row items-center justify-between rounded px-4 py-2 hover:bg-muted",children:[(0,n.jsx)("span",{children:e.title}),(0,n.jsx)(T.A,{className:"h-4 w-4 text-muted-foreground"})]},t))})]})})]})},e.title))})})}),(0,n.jsxs)("div",{className:"flex justify-end gap-2",children:[(0,n.jsx)("div",{className:"hidden md:inline",children:(0,n.jsx)(g,{})}),(0,n.jsx)(o.$,{variant:"outline",asChild:!0,className:"hidden md:inline-flex h-10",children:(0,n.jsxs)(A(),{href:"https://marketplace.visualstudio.com/items?itemName=cubent.cubent",className:"flex flex-row items-center gap-2 px-4 py-2 whitespace-nowrap",children:[(0,n.jsx)("svg",{className:"h-4 w-4 shrink-0",viewBox:"0 0 88 88",fill:"currentColor",children:(0,n.jsx)("path",{d:"M0 12.402l35.687-4.86.016 34.423L0 45.194zm35.67 33.529l.028 34.453L.028 75.48.026 45.7zm4.326-39.025L87.314 0v41.527l-47.318 4.425zm47.329 39.349v41.527L40.028 81.441l.016-34.486z"})}),(0,n.jsx)("span",{className:"shrink-0 text-sm",children:"Download"})]})}),s?(0,n.jsx)("div",{className:"h-10 w-10 animate-pulse bg-gray-200 rounded-full"}):t&&r?(0,n.jsx)(G,{user:r}):(0,n.jsx)(o.$,{asChild:!0,children:(0,n.jsx)(A(),{href:"https://app.cubent.dev/sign-in",children:"Sign In"})})]}),(0,n.jsxs)("div",{className:"flex w-12 shrink items-end justify-end lg:hidden",children:[(0,n.jsx)(o.$,{variant:"ghost",onClick:()=>d(!l),children:l?(0,n.jsx)(O.A,{className:"h-5 w-5"}):(0,n.jsx)(S.A,{className:"h-5 w-5"})}),l&&(0,n.jsx)("div",{className:"container absolute top-20 right-0 flex w-full flex-col gap-8 border-t bg-background py-4 shadow-lg px-4 sm:px-6 lg:px-8",children:a.map(e=>(0,n.jsx)("div",{children:(0,n.jsxs)("div",{className:"flex flex-col gap-2",children:[e.href?(0,n.jsxs)(A(),{href:e.href,className:"flex items-center justify-between",target:e.href.startsWith("http")?"_blank":void 0,rel:e.href.startsWith("http")?"noopener noreferrer":void 0,children:[(0,n.jsx)("span",{className:"text-lg",children:e.title}),(0,n.jsx)(T.A,{className:"h-4 w-4 stroke-1 text-muted-foreground"})]}):(0,n.jsx)("p",{className:"text-lg",children:e.title}),e.items?.map(e=>(0,n.jsxs)(A(),{href:e.href,className:"flex items-center justify-between",children:[(0,n.jsx)("span",{className:"text-muted-foreground",children:e.title}),(0,n.jsx)(T.A,{className:"h-4 w-4 stroke-1"})]},e.title))]})},e.title))})]})]})})}},54191:(e,t,r)=>{"use strict";r.d(t,{Toaster:()=>n});let n=(0,r(6340).registerClientReference)(function(){throw Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\sonner.tsx","Toaster")},71178:(e,t,r)=>{Promise.resolve().then(r.bind(r,84641))},74619:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=74619,e.exports=t},74664:(e,t,r)=>{"use strict";r.d(t,{Header:()=>n});let n=(0,r(6340).registerClientReference)(function(){throw Error("Attempted to call Header() from the server but Header is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\web\\app\\[locale]\\components\\header\\index.tsx","Header")},74938:(e,t,r)=>{"use strict";r.d(t,{$:()=>l,r:()=>i});var n=r(99730);r(35371);var s=r(58576),a=r(72795),o=r(83590);let i=(0,a.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l({className:e,variant:t,size:r,asChild:a=!1,...l}){let d=a?s.DX:"button";return(0,n.jsx)(d,{"data-slot":"button",className:(0,o.cn)(i({variant:t,size:r,className:e})),...l})}},76262:(e,t,r)=>{"use strict";r.d(t,{P:()=>s});var n=Object.getOwnPropertyNames,s=(e,t)=>function(){return t||(0,e[n(e)[0]])((t={exports:{}}).exports,t),t.exports}},77021:(e,t,r)=>{"use strict";r.d(t,{M:()=>n.M7});var n=r(22589)},77422:(e,t,r)=>{"use strict";r.d(t,{T:()=>o});let n=JSON.parse('{"H":{"s":"en","z":[]}}'),s=[n.H.s,...n.H.z],a=Object.fromEntries(s.map(e=>[e,()=>r(28427)(`./${e}.json`).then(e=>e.default).catch(t=>(console.error(`Failed to load dictionary for locale: ${e}`,t),r.e(4202).then(r.t.bind(r,94202,19)).then(e=>e.default)))])),o=async e=>{let t=e.split("-")[0];if(!s.includes(t))return console.warn(`Locale "${e}" is not supported, defaulting to "en"`),a.en();try{return await a[t]()}catch(e){return console.error(`Error loading dictionary for locale "${t}", falling back to "en"`,e),a.en()}}},77648:()=>{},79047:(e,t,r)=>{"use strict";r.r(t),r.d(t,{ClientConditionalRenderer:()=>o}),r(34526);var n=r(35371),s=r(6754),a=n.lazy(()=>r.e(7453).then(r.bind(r,87453)).then(e=>({default:e.ClientToolbar}))),o=({draft:e,isForcedDraft:t,enableDraftMode:r,disableDraftMode:o,revalidateTags:i,resolvedRef:l,getLatestBranches:d})=>{let[c,u]=n.useState(!1);n.useEffect(()=>{u(!0)},[]);let h=`bshb-preview-${l.repoHash}`,m=n.useCallback(e=>{},[h]),[p,f]=n.useState(m),[b,g]=n.useState();return(n.useLayoutEffect(()=>{if(e||t)return void g(!1);let r=m("url-only");if(!r)return void g(!1);f(r),g(!0)},[e,t,m]),n.useEffect(()=>{let e=new URL(window.location.href),t="true"===e.searchParams.get("__bshb-odr"),r=e.searchParams.get("__bshb-odr-token"),n=e.searchParams.get("__bshb-odr-ref");t&&r&&i({bshbPreviewToken:r,...n?{ref:n}:{}}).then(({success:e,message:t})=>{document.documentElement.dataset.basehubOdrStatus=e?"success":"error",e||(document.documentElement.dataset.basehubOdrErrorMessage="Response failed"),t&&(document.documentElement.dataset.basehubOdrMessage=t)}).catch(e=>{document.documentElement.dataset.basehubOdrStatus="error";let t="";try{t=e.message}catch(e){console.error(e),t="Unknown error"}document.documentElement.dataset.basehubOdrErrorMessage=t})},[i]),(p||t)&&c&&"undefined"!=typeof document)?(0,s.createPortal)(n.createElement(a,{disableDraftMode:o,enableDraftMode:r,draft:e,isForcedDraft:t,bshbPreviewToken:p,shouldAutoEnableDraft:b,seekAndStoreBshbPreviewToken:m,resolvedRef:l,getLatestBranches:d,bshbPreviewLSName:h}),document.body):null}},82456:(e,t,r)=>{"use strict";r.r(t),r.d(t,{"7f1ba63e790efded4a1979348dcde31ce9042e1270":()=>s.y,"7f721ae5e082b6e4eb1bce6634826e6feb0af40095":()=>n.at,"7f751146da0a2fa306cea6447c8db7bb5b49a0bc33":()=>n.ot,"7fafdab18e4336438fc499c2c4e643a9436e2a9e02":()=>n.ai});var n=r(73819),s=r(44089)},83590:(e,t,r)=>{"use strict";r.d(t,{cn:()=>a}),r(60188);var n=r(29085);r(22683);var s=r(63632);let a=(...e)=>(0,s.QP)((0,n.$)(e))},84641:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(6340).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\2 FOLDERS FOR CUBENT\\\\Cubentweb\\\\cubent\\\\apps\\\\web\\\\app\\\\[locale]\\\\global-error.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\web\\app\\[locale]\\global-error.tsx","default")},84685:(e,t,r)=>{"use strict";r.d(t,{Toaster:()=>o});var n=r(99730),s=r(48585),a=r(22683);let o=({...e})=>{let{theme:t="system"}=(0,s.D)();return(0,n.jsx)(a.l,{theme:t,className:"toaster group",style:{"--normal-bg":"var(--popover)","--normal-text":"var(--popover-foreground)","--normal-border":"var(--border)"},...e})}},89620:(e,t,r)=>{"use strict";r.d(t,{PostHogProvider:()=>c});var n=r(99730),s=r(1429),a=r(87805),o=r(35371),i=r(79796),l=r(42294);let d=()=>(0,i.w)({client:{NEXT_PUBLIC_POSTHOG_KEY:l.z.string().startsWith("phc_").optional(),NEXT_PUBLIC_POSTHOG_HOST:l.z.string().url().optional(),NEXT_PUBLIC_GA_MEASUREMENT_ID:l.z.string().startsWith("G-").optional()},runtimeEnv:{NEXT_PUBLIC_POSTHOG_KEY:"phc_IIiOB59nWFyFh8azKXcqkOucMA9x5jTUYPTEDx2ccP9",NEXT_PUBLIC_POSTHOG_HOST:"https://us.i.posthog.com",NEXT_PUBLIC_GA_MEASUREMENT_ID:"G-PLACEHOLDER123"}}),c=e=>((0,o.useEffect)(()=>{try{let e=d();e.NEXT_PUBLIC_POSTHOG_KEY&&e.NEXT_PUBLIC_POSTHOG_HOST?s.Ay.init(e.NEXT_PUBLIC_POSTHOG_KEY,{api_host:"/ingest",ui_host:e.NEXT_PUBLIC_POSTHOG_HOST,person_profiles:"identified_only",capture_pageview:!1,capture_pageleave:!0}):console.warn("PostHog environment variables not configured. Analytics disabled.")}catch(e){console.warn("PostHog initialization failed:",e)}},[]),(0,n.jsx)(a.so,{client:s.Ay,...e}))},90411:(e,t,r)=>{"use strict";r.d(t,{TooltipProvider:()=>s});var n=r(6340);(0,n.registerClientReference)(function(){throw Error("Attempted to call Tooltip() from the server but Tooltip is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\tooltip.tsx","Tooltip"),(0,n.registerClientReference)(function(){throw Error("Attempted to call TooltipTrigger() from the server but TooltipTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\tooltip.tsx","TooltipTrigger"),(0,n.registerClientReference)(function(){throw Error("Attempted to call TooltipContent() from the server but TooltipContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\tooltip.tsx","TooltipContent");let s=(0,n.registerClientReference)(function(){throw Error("Attempted to call TooltipProvider() from the server but TooltipProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\packages\\design-system\\components\\ui\\tooltip.tsx","TooltipProvider")},96574:(e,t,r)=>{"use strict";r.d(t,{y:()=>s});var n=r(96242);let s=(0,n.createServerReference)("7f1ba63e790efded4a1979348dcde31ce9042e1270",n.callServer,void 0,n.findSourceMapURL,"invalidateCacheAction")}};