(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[450],{1885:(e,t,a)=>{Promise.resolve().then(a.bind(a,54372)),Promise.resolve().then(a.t.bind(a,37827,23)),Promise.resolve().then(a.bind(a,43432)),Promise.resolve().then(a.bind(a,93181)),Promise.resolve().then(a.bind(a,67461)),Promise.resolve().then(a.bind(a,40644)),Promise.resolve().then(a.bind(a,30426)),Promise.resolve().then(a.bind(a,94134)),Promise.resolve().then(a.bind(a,31800)),Promise.resolve().then(a.bind(a,72881)),Promise.resolve().then(a.t.bind(a,35685,23)),Promise.resolve().then(a.t.bind(a,88224,23)),Promise.resolve().then(a.t.bind(a,9658,23)),Promise.resolve().then(a.t.bind(a,5826,23)),Promise.resolve().then(a.bind(a,13957)),Promise.resolve().then(a.bind(a,23454)),Promise.resolve().then(a.bind(a,70433)),Promise.resolve().then(a.bind(a,11713)),Promise.resolve().then(a.bind(a,71497)),Promise.resolve().then(a.bind(a,89069)),Promise.resolve().then(a.bind(a,37539))},11713:(e,t,a)=>{"use strict";a.r(t),a.d(t,{ClientConditionalRenderer:()=>o}),a(68776);var n=a(50628),r=a(6341),s=n.lazy(()=>a.e(465).then(a.bind(a,30465)).then(e=>({default:e.ClientToolbar}))),o=e=>{let{draft:t,isForcedDraft:a,enableDraftMode:o,disableDraftMode:i,revalidateTags:l,resolvedRef:d,getLatestBranches:c}=e,[u,m]=n.useState(!1);n.useEffect(()=>{m(!0)},[]);let f="bshb-preview-".concat(d.repoHash),h=n.useCallback(e=>{var t,a;let n=new URLSearchParams(window.location.search).get("bshb-preview");if(n){try{null==(t=window.localStorage)||t.setItem(f,n)}catch(e){}return n}if("url-only"!==e)try{let e=null==(a=window.localStorage)?void 0:a.getItem(f);if(e)return e}catch(e){}},[f]),[p,v]=n.useState(h),[g,x]=n.useState();return(n.useLayoutEffect(()=>{if(t||a)return void x(!1);let e=h("url-only");if(!e)return void x(!1);v(e),x(!0)},[t,a,h]),n.useEffect(()=>{let e=new URL(window.location.href),t="true"===e.searchParams.get("__bshb-odr"),a=e.searchParams.get("__bshb-odr-token"),n=e.searchParams.get("__bshb-odr-ref");t&&a&&l({bshbPreviewToken:a,...n?{ref:n}:{}}).then(e=>{let{success:t,message:a}=e;document.documentElement.dataset.basehubOdrStatus=t?"success":"error",t||(document.documentElement.dataset.basehubOdrErrorMessage="Response failed"),a&&(document.documentElement.dataset.basehubOdrMessage=a)}).catch(e=>{document.documentElement.dataset.basehubOdrStatus="error";let t="";try{t=e.message}catch(e){console.error(e),t="Unknown error"}document.documentElement.dataset.basehubOdrErrorMessage=t})},[l]),(p||a)&&u&&"undefined"!=typeof document)?(0,r.createPortal)(n.createElement(s,{disableDraftMode:i,enableDraftMode:o,draft:t,isForcedDraft:a,bshbPreviewToken:p,shouldAutoEnableDraft:g,seekAndStoreBshbPreviewToken:h,resolvedRef:d,getLatestBranches:c,bshbPreviewLSName:f}),document.body):null}},23454:(e,t,a)=>{"use strict";a.d(t,{PostHogProvider:()=>c});var n=a(6024),r=a(87219),s=a(19921),o=a(50628),i=a(91028),l=a(74822);let d=()=>(0,i.w)({client:{NEXT_PUBLIC_POSTHOG_KEY:l.z.string().startsWith("phc_").optional(),NEXT_PUBLIC_POSTHOG_HOST:l.z.string().url().optional(),NEXT_PUBLIC_GA_MEASUREMENT_ID:l.z.string().startsWith("G-").optional()},runtimeEnv:{NEXT_PUBLIC_POSTHOG_KEY:"phc_IIiOB59nWFyFh8azKXcqkOucMA9x5jTUYPTEDx2ccP9",NEXT_PUBLIC_POSTHOG_HOST:"https://us.i.posthog.com",NEXT_PUBLIC_GA_MEASUREMENT_ID:"G-PLACEHOLDER123"}}),c=e=>((0,o.useEffect)(()=>{try{let e=d();e.NEXT_PUBLIC_POSTHOG_KEY&&e.NEXT_PUBLIC_POSTHOG_HOST?r.Ay.init(e.NEXT_PUBLIC_POSTHOG_KEY,{api_host:"/ingest",ui_host:e.NEXT_PUBLIC_POSTHOG_HOST,person_profiles:"identified_only",capture_pageview:!1,capture_pageleave:!0}):console.warn("PostHog environment variables not configured. Analytics disabled.")}catch(e){console.warn("PostHog initialization failed:",e)}},[]),(0,n.jsx)(s.so,{client:r.Ay,...e}))},31918:(e,t,a)=>{"use strict";a.d(t,{cn:()=>s}),a(63410);var n=a(49973);a(13957);var r=a(22928);let s=function(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,r.QP)((0,n.$)(t))}},37539:(e,t,a)=>{"use strict";a.d(t,{TooltipProvider:()=>s});var n=a(6024);a(50628);var r=a(9379);function s(e){let{delayDuration:t=0,...a}=e;return(0,n.jsx)(r.Kq,{"data-slot":"tooltip-provider",delayDuration:t,...a})}a(31918)},37827:()=>{},54372:(e,t,a)=>{"use strict";a.d(t,{Header:()=>F});var n=a(6024),r=a(14844),s=a(72881),o=a(70234),i=a(50628),l=a(75447),d=a(31918);function c(e){let{...t}=e;return(0,n.jsx)(l.bL,{"data-slot":"dropdown-menu",...t})}function u(e){let{...t}=e;return(0,n.jsx)(l.l9,{"data-slot":"dropdown-menu-trigger",...t})}function m(e){let{className:t,sideOffset:a=4,...r}=e;return(0,n.jsx)(l.ZL,{children:(0,n.jsx)(l.UC,{"data-slot":"dropdown-menu-content",sideOffset:a,className:(0,d.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",t),...r})})}function f(e){let{className:t,inset:a,variant:r="default",...s}=e;return(0,n.jsx)(l.q7,{"data-slot":"dropdown-menu-item","data-inset":a,"data-variant":r,className:(0,d.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...s})}function h(e){let{className:t,inset:a,...r}=e;return(0,n.jsx)(l.JU,{"data-slot":"dropdown-menu-label","data-inset":a,className:(0,d.cn)("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",t),...r})}function p(e){let{className:t,...a}=e;return(0,n.jsx)(l.wv,{"data-slot":"dropdown-menu-separator",className:(0,d.cn)("bg-border -mx-1 my-1 h-px",t),...a})}let v=[{label:"Light",value:"light"},{label:"Dark",value:"dark"},{label:"System",value:"system"}],g=()=>{let{setTheme:e}=(0,s.D)();return(0,n.jsxs)(c,{children:[(0,n.jsx)(u,{asChild:!0,children:(0,n.jsxs)(o.$,{variant:"ghost",size:"icon",className:"shrink-0 text-foreground",children:[(0,n.jsx)(r.gLX,{className:"dark:-rotate-90 h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:scale-0"}),(0,n.jsx)(r.rRK,{className:"absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100"}),(0,n.jsx)("span",{className:"sr-only",children:"Toggle theme"})]})}),(0,n.jsx)(m,{children:v.map(t=>{let{label:a,value:r}=t;return(0,n.jsx)(f,{onClick:()=>e(r),children:a},r)})})]})};var x=a(5011),b=a(81197),w=a(76227);function j(e){let{className:t,children:a,viewport:r=!0,...s}=e;return(0,n.jsxs)(x.bL,{"data-slot":"navigation-menu","data-viewport":r,className:(0,d.cn)("group/navigation-menu relative flex max-w-max flex-1 items-center justify-center",t),...s,children:[a,r&&(0,n.jsx)(T,{})]})}function N(e){let{className:t,...a}=e;return(0,n.jsx)(x.B8,{"data-slot":"navigation-menu-list",className:(0,d.cn)("group flex flex-1 list-none items-center justify-center gap-1",t),...a})}function y(e){let{className:t,...a}=e;return(0,n.jsx)(x.q7,{"data-slot":"navigation-menu-item",className:(0,d.cn)("relative",t),...a})}let _=(0,b.F)("group inline-flex h-9 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground disabled:pointer-events-none disabled:opacity-50 data-[state=open]:hover:bg-accent data-[state=open]:text-accent-foreground data-[state=open]:focus:bg-accent data-[state=open]:bg-accent/50 focus-visible:ring-ring/50 outline-none transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1");function k(e){let{className:t,children:a,...r}=e;return(0,n.jsxs)(x.l9,{"data-slot":"navigation-menu-trigger",className:(0,d.cn)(_(),"group",t),...r,children:[a," ",(0,n.jsx)(w.A,{className:"relative top-[1px] ml-1 size-3 transition duration-300 group-data-[state=open]:rotate-180","aria-hidden":"true"})]})}function P(e){let{className:t,...a}=e;return(0,n.jsx)(x.UC,{"data-slot":"navigation-menu-content",className:(0,d.cn)("data-[motion^=from-]:animate-in data-[motion^=to-]:animate-out data-[motion^=from-]:fade-in data-[motion^=to-]:fade-out data-[motion=from-end]:slide-in-from-right-52 data-[motion=from-start]:slide-in-from-left-52 data-[motion=to-end]:slide-out-to-right-52 data-[motion=to-start]:slide-out-to-left-52 top-0 left-0 w-full p-2 pr-2.5 md:absolute md:w-auto","group-data-[viewport=false]/navigation-menu:bg-popover group-data-[viewport=false]/navigation-menu:text-popover-foreground group-data-[viewport=false]/navigation-menu:data-[state=open]:animate-in group-data-[viewport=false]/navigation-menu:data-[state=closed]:animate-out group-data-[viewport=false]/navigation-menu:data-[state=closed]:zoom-out-95 group-data-[viewport=false]/navigation-menu:data-[state=open]:zoom-in-95 group-data-[viewport=false]/navigation-menu:data-[state=open]:fade-in-0 group-data-[viewport=false]/navigation-menu:data-[state=closed]:fade-out-0 group-data-[viewport=false]/navigation-menu:top-full group-data-[viewport=false]/navigation-menu:mt-1.5 group-data-[viewport=false]/navigation-menu:overflow-hidden group-data-[viewport=false]/navigation-menu:rounded-md group-data-[viewport=false]/navigation-menu:border group-data-[viewport=false]/navigation-menu:shadow group-data-[viewport=false]/navigation-menu:duration-200 **:data-[slot=navigation-menu-link]:focus:ring-0 **:data-[slot=navigation-menu-link]:focus:outline-none",t),...a})}function T(e){let{className:t,...a}=e;return(0,n.jsx)("div",{className:(0,d.cn)("absolute top-full left-0 isolate z-50 flex justify-center"),children:(0,n.jsx)(x.LM,{"data-slot":"navigation-menu-viewport",className:(0,d.cn)("origin-top-center bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-90 relative mt-1.5 h-[var(--radix-navigation-menu-viewport-height)] w-full overflow-hidden rounded-md border shadow md:w-[var(--radix-navigation-menu-viewport-width)]",t),...a})})}function E(e){let{className:t,...a}=e;return(0,n.jsx)(x.N_,{"data-slot":"navigation-menu-link",className:(0,d.cn)("data-[active=true]:focus:bg-accent data-[active=true]:hover:bg-accent data-[active=true]:bg-accent/50 data-[active=true]:text-accent-foreground hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus-visible:ring-ring/50 [&_svg:not([class*='text-'])]:text-muted-foreground flex flex-col gap-1 rounded-sm p-2 text-sm transition-all outline-none focus-visible:ring-[3px] focus-visible:outline-1 [&_svg:not([class*='size-'])]:size-4",t),...a})}var O=a(15127),A=a(25451),C=a(45370),S=a(35685),z=a.n(S),U=a(82757);let L={src:"/_next/static/media/logo.bef10624.svg",height:24,width:24,blurWidth:0,blurHeight:0};var H=a(17087);function D(e){let{className:t,...a}=e;return(0,n.jsx)(H.bL,{"data-slot":"avatar",className:(0,d.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",t),...a})}function I(e){let{className:t,...a}=e;return(0,n.jsx)(H._V,{"data-slot":"avatar-image",className:(0,d.cn)("aspect-square size-full",t),...a})}function M(e){let{className:t,...a}=e;return(0,n.jsx)(H.H4,{"data-slot":"avatar-fallback",className:(0,d.cn)("bg-muted flex size-full items-center justify-center rounded-full",t),...a})}var B=a(9502),R=a(60392),X=a(31704);let G=e=>{var t,a;let{user:r}=e,s=r.fullName||r.firstName||(null==(t=r.emailAddresses[0])?void 0:t.emailAddress)||"User",i=null==(a=r.emailAddresses[0])?void 0:a.emailAddress;return(0,n.jsxs)(c,{children:[(0,n.jsx)(u,{asChild:!0,children:(0,n.jsx)(o.$,{variant:"ghost",className:"relative h-10 w-10 rounded-full",children:(0,n.jsxs)(D,{className:"h-10 w-10",children:[(0,n.jsx)(I,{src:r.imageUrl,alt:s}),(0,n.jsx)(M,{children:(e=>e?e.split(" ").map(e=>e[0]).join("").toUpperCase().slice(0,2):"U")(r.fullName)})]})})}),(0,n.jsxs)(m,{className:"w-56",align:"end",forceMount:!0,children:[(0,n.jsx)(h,{className:"font-normal",children:(0,n.jsxs)("div",{className:"flex flex-col space-y-1",children:[(0,n.jsx)("p",{className:"text-sm font-medium leading-none",children:s}),i&&(0,n.jsx)("p",{className:"text-xs leading-none text-muted-foreground",children:i})]})}),(0,n.jsx)(p,{}),(0,n.jsx)(f,{asChild:!0,children:(0,n.jsxs)(z(),{href:"https://app.cubent.dev",className:"flex items-center",children:[(0,n.jsx)(B.A,{className:"mr-2 h-4 w-4"}),(0,n.jsx)("span",{children:"Dashboard"})]})}),(0,n.jsx)(f,{asChild:!0,children:(0,n.jsxs)(z(),{href:"https://app.cubent.dev/settings",className:"flex items-center",children:[(0,n.jsx)(R.A,{className:"mr-2 h-4 w-4"}),(0,n.jsx)("span",{children:"Settings"})]})}),(0,n.jsx)(p,{}),(0,n.jsxs)(f,{onClick:()=>{window.location.href="https://app.cubent.dev/sign-out"},className:"flex items-center",children:[(0,n.jsx)(X.A,{className:"mr-2 h-4 w-4"}),(0,n.jsx)("span",{children:"Sign out"})]})]})]})},F=e=>{let{dictionary:t}=e,{isAuthenticated:a,user:r,isLoading:s}=function(){let[e,t]=(0,i.useState)({isAuthenticated:!1,user:null,isLoading:!0});return(0,i.useEffect)(()=>{!function(){try{console.log("[AUTH] Checking authentication status..."),console.log("[AUTH] All cookies:",document.cookie);let e=document.cookie.split("; ").find(e=>e.startsWith("cubent_auth_token="));if(console.log("[AUTH] Auth token cookie found:",!!e),!e){console.log("[AUTH] No auth token cookie, setting not authenticated"),t({isAuthenticated:!1,user:null,isLoading:!1});return}let a=e.split("=")[1],n=decodeURIComponent(a);console.log("[AUTH] Token found:",n.substring(0,50)+"...");try{let e=JSON.parse(atob(n));if(console.log("[AUTH] User data decoded:",e),Date.now()-e.timestamp>6048e5){console.log("[AUTH] Token expired, setting not authenticated"),t({isAuthenticated:!1,user:null,isLoading:!1});return}t({isAuthenticated:!0,user:e,isLoading:!1})}catch(e){console.error("[AUTH] Failed to decode token:",e),t({isAuthenticated:!1,user:null,isLoading:!1})}}catch(e){console.error("[AUTH] Failed to check auth status:",e),t({isAuthenticated:!1,user:null,isLoading:!1})}}()},[]),e}(),l=[{title:t.web.header.home,href:"/",description:""},{title:"Features",description:"Discover what makes Cubent Coder powerful",items:[{title:"Chat Mode",href:"/features/chat"},{title:"Agent Mode",href:"/features/agent"},{title:"Custom Modes",href:"/features/custom"}]},{title:t.web.header.docs,href:"https://cubentdev.mintlify.app",description:""},{title:t.web.header.blog,href:"/blog",description:""},{title:"Support",href:"https://discord.gg/cubent",description:""}],[d,c]=(0,i.useState)(!1);return(0,n.jsx)("header",{className:"sticky top-0 left-0 z-40 w-full border-b bg-background",children:(0,n.jsxs)("div",{className:"relative w-full max-w-none flex min-h-20 flex-row items-center justify-between",style:{paddingInline:"clamp(1rem, 2.5%, 2rem)"},children:[(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)(U.default,{src:L,alt:"Cubent Logo",width:32,height:32,className:"dark:invert"}),(0,n.jsx)("p",{className:"whitespace-nowrap font-semibold",children:"Cubent"})]}),(0,n.jsx)("div",{className:"hidden flex-row items-center justify-center gap-3 lg:flex absolute left-1/2 transform -translate-x-1/2 top-1/2 -translate-y-1/2",children:(0,n.jsx)(j,{className:"flex items-center justify-center",children:(0,n.jsx)(N,{className:"flex flex-row justify-center gap-3",children:l.map(e=>{var t;return(0,n.jsx)(y,{children:e.href?(0,n.jsx)(n.Fragment,{children:(0,n.jsx)(E,{asChild:!0,children:(0,n.jsx)(o.$,{variant:"ghost",asChild:!0,children:(0,n.jsx)(z(),{href:e.href,target:e.href.startsWith("http")?"_blank":void 0,rel:e.href.startsWith("http")?"noopener noreferrer":void 0,children:e.title})})})}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(k,{className:"font-medium text-sm",children:e.title}),(0,n.jsx)(P,{className:"!w-[450px] p-4",children:(0,n.jsxs)("div",{className:"flex grid-cols-2 flex-col gap-4 lg:grid",children:[(0,n.jsxs)("div",{className:"flex h-full flex-col justify-between",children:[(0,n.jsxs)("div",{className:"flex flex-col",children:[(0,n.jsx)("p",{className:"text-base",children:e.title}),(0,n.jsx)("p",{className:"text-muted-foreground text-sm",children:e.description})]}),(0,n.jsx)(o.$,{size:"sm",className:"mt-10",asChild:!0,children:(0,n.jsx)(z(),{href:"https://marketplace.visualstudio.com/items?itemName=cubent.cubent",children:"Download Extension"})})]}),(0,n.jsx)("div",{className:"flex h-full flex-col justify-end text-sm",children:null==(t=e.items)?void 0:t.map((e,t)=>(0,n.jsxs)(E,{href:e.href,className:"flex flex-row items-center justify-between rounded px-4 py-2 hover:bg-muted",children:[(0,n.jsx)("span",{children:e.title}),(0,n.jsx)(O.A,{className:"h-4 w-4 text-muted-foreground"})]},t))})]})})]})},e.title)})})})}),(0,n.jsxs)("div",{className:"flex justify-end gap-2",children:[(0,n.jsx)("div",{className:"hidden md:inline",children:(0,n.jsx)(g,{})}),(0,n.jsx)(o.$,{variant:"outline",asChild:!0,className:"hidden md:inline-flex h-10",children:(0,n.jsxs)(z(),{href:"https://marketplace.visualstudio.com/items?itemName=cubent.cubent",className:"flex flex-row items-center gap-2 px-4 py-2 whitespace-nowrap",children:[(0,n.jsx)("svg",{className:"h-4 w-4 shrink-0",viewBox:"0 0 88 88",fill:"currentColor",children:(0,n.jsx)("path",{d:"M0 12.402l35.687-4.86.016 34.423L0 45.194zm35.67 33.529l.028 34.453L.028 75.48.026 45.7zm4.326-39.025L87.314 0v41.527l-47.318 4.425zm47.329 39.349v41.527L40.028 81.441l.016-34.486z"})}),(0,n.jsx)("span",{className:"shrink-0 text-sm",children:"Download"})]})}),s?(0,n.jsx)("div",{className:"h-10 w-10 animate-pulse bg-gray-200 rounded-full"}):a&&r?(0,n.jsx)(G,{user:r}):(0,n.jsx)(o.$,{asChild:!0,children:(0,n.jsx)(z(),{href:"https://app.cubent.dev/sign-in",children:"Sign In"})})]}),(0,n.jsxs)("div",{className:"flex w-12 shrink items-end justify-end lg:hidden",children:[(0,n.jsx)(o.$,{variant:"ghost",onClick:()=>c(!d),children:d?(0,n.jsx)(A.A,{className:"h-5 w-5"}):(0,n.jsx)(C.A,{className:"h-5 w-5"})}),d&&(0,n.jsx)("div",{className:"container absolute top-20 right-0 flex w-full flex-col gap-8 border-t bg-background py-4 shadow-lg px-4 sm:px-6 lg:px-8",children:l.map(e=>{var t;return(0,n.jsx)("div",{children:(0,n.jsxs)("div",{className:"flex flex-col gap-2",children:[e.href?(0,n.jsxs)(z(),{href:e.href,className:"flex items-center justify-between",target:e.href.startsWith("http")?"_blank":void 0,rel:e.href.startsWith("http")?"noopener noreferrer":void 0,children:[(0,n.jsx)("span",{className:"text-lg",children:e.title}),(0,n.jsx)(O.A,{className:"h-4 w-4 stroke-1 text-muted-foreground"})]}):(0,n.jsx)("p",{className:"text-lg",children:e.title}),null==(t=e.items)?void 0:t.map(e=>(0,n.jsxs)(z(),{href:e.href,className:"flex items-center justify-between",children:[(0,n.jsx)("span",{className:"text-muted-foreground",children:e.title}),(0,n.jsx)(O.A,{className:"h-4 w-4 stroke-1"})]},e.title))]})},e.title)})})]})]})})}},66999:(e,t,a)=>{"use strict";a.d(t,{y:()=>r});var n=a(70988);let r=(0,n.createServerReference)("7f1ba63e790efded4a1979348dcde31ce9042e1270",n.callServer,void 0,n.findSourceMapURL,"invalidateCacheAction")},68776:(e,t,a)=>{"use strict";a.d(t,{P:()=>d,f:()=>u});var n=Object.create,r=Object.defineProperty,s=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,i=Object.getPrototypeOf,l=Object.prototype.hasOwnProperty,d=(e,t)=>function(){return t||(0,e[o(e)[0]])((t={exports:{}}).exports,t),t.exports},c=(e,t,a,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let i of o(t))l.call(e,i)||i===a||r(e,i,{get:()=>t[i],enumerable:!(n=s(t,i))||n.enumerable});return e},u=(e,t,a)=>(a=null!=e?n(i(e)):{},c(!t&&e&&e.__esModule?a:r(a,"default",{value:e,enumerable:!0}),e))},70234:(e,t,a)=>{"use strict";a.d(t,{$:()=>l,r:()=>i});var n=a(6024);a(50628);var r=a(89840),s=a(81197),o=a(31918);let i=(0,s.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:t,variant:a,size:s,asChild:l=!1,...d}=e,c=l?r.DX:"button";return(0,n.jsx)(c,{"data-slot":"button",className:(0,o.cn)(i({variant:a,size:s,className:t})),...d})}},70433:(e,t,a)=>{"use strict";a.d(t,{AuthProvider:()=>i});var n=a(6024),r=a(29763),s=a(5850),o=a(72881);let i=e=>{let{privacyUrl:t,termsUrl:a,helpUrl:i,...l}=e,{resolvedTheme:d}=(0,o.D)(),c="dark"===d?s.dark:void 0;return(0,n.jsx)(r.lJ,{...l,appearance:{layout:{privacyPageUrl:t,termsPageUrl:a,helpPageUrl:i},baseTheme:c,elements:{dividerLine:"bg-border",socialButtonsIconButton:"bg-card",navbarButton:"text-foreground",organizationSwitcherTrigger__open:"bg-background",organizationPreviewMainIdentifier:"text-foreground",organizationSwitcherTriggerIcon:"text-muted-foreground",organizationPreview__organizationSwitcherTrigger:"gap-2",organizationPreviewAvatarContainer:"shrink-0"},variables:{fontFamily:"var(--font-sans)",fontFamilyButtons:"var(--font-sans)",fontWeight:{bold:"var(--font-weight-bold)",normal:"var(--font-weight-normal)",medium:"var(--font-weight-medium)"}}}})}},71497:(e,t,a)=>{"use strict";a.r(t),a.d(t,{ClientPump:()=>d}),a(82920);var n=a(50628);let r="__alias__";var s=!1,o=new Set,i=new Map,l=new Map,d=e=>{var t;let{children:d,rawQueries:c,pumpEndpoint:u,pumpToken:m,initialState:f,initialResolvedChildren:h,apiVersion:p,previewRef:v}=e,g=n.useRef(m),[x,b]=n.useState(f),w=n.useRef(f);w.current=f;let[j,N]=n.useState(v),y=n.useRef(j);y.current=j;let _=n.useCallback(async()=>{let e,t,a,n=await Promise.all(c.map(async(n,s)=>{var o,d;if(!g.current)return console.warn("No pump token found. Skipping query."),null;let c=JSON.stringify(n),m=c+j,f=l.get(c)||(null==(d=w.current)||null==(o=d.responseHashes)?void 0:o[s])||"";if(i.has(m)){let n=i.get(m);if(performance.now()-n.start<32){let r=await n.response;return r?(r.newPumpToken&&(e=r.newPumpToken),t=r.pusherData,a=r.spaceID,r):null}}let h=fetch(u,{cache:"no-store",method:"POST",headers:{"content-type":"application/json","x-basehub-api-version":p,"x-basehub-ref":j},body:JSON.stringify({...n,pumpToken:g.current,lastResponseHash:f})}).then(async e=>{let{data:t=null,errors:a=null,newPumpToken:n,spaceID:s,pusherData:o,responseHash:i}=await e.json();return l.set(c,i),{data:function e(t){if("object"!=typeof t||null===t)return t;if(Array.isArray(t))return t.map(t=>e(t));let a={};for(let[n,s]of Object.entries(t))if(n.includes(r)){let[t,...o]=n.split(r);a[o.join(r)]=e(s)}else a[n]=e(s);return a}(t),spaceID:s,pusherData:o,newPumpToken:n,errors:a,responseHash:i,changed:f!==i}}).catch(e=>{console.error("Error fetching data from the BaseHub Draft API:\n              \n".concat(JSON.stringify(e,null,2),"\n              \nContact <EMAIL> for help."))});i.set(m,{start:performance.now(),response:h});let v=await h;return v?(v.newPumpToken&&(e=v.newPumpToken),t=v.pusherData,a=v.spaceID,v):null}));if(n.some(e=>null==e?void 0:e.changed)){if(!t||!a)return;b(e=>t&&a?{data:n.map((t,a)=>{var n,r,s;return(null==t?void 0:t.changed)?null!=(s=null==t?void 0:t.data)?s:null:null!=(r=null==e||null==(n=e.data)?void 0:n[a])?r:null}),errors:n.map((t,a)=>{var n,r,s;return(null==t?void 0:t.changed)?null!=(s=null==t?void 0:t.errors)?s:null:null!=(r=null==e||null==(n=e.errors)?void 0:n[a])?r:null}),responseHashes:n.map(e=>{var t;return null!=(t=null==e?void 0:e.responseHash)?t:""}),pusherData:t,spaceID:a}:e)}e&&(g.current=e)},[u,c,p,j]);n.useRef(null),n.useEffect(()=>{var e;if(!(null==x?void 0:x.errors))return;let t=null==(e=x.errors[0])?void 0:e[0];t&&console.error("Error fetching data from the BaseHub Draft API: ".concat(t.message).concat(t.path?" at ".concat(t.path.join(".")):""))},[null==x?void 0:x.errors]),n.useEffect(()=>{function e(){_()}return e(),o.add(e),()=>{o.delete(e)}},[_]);let[k,P]=n.useState(null),T=null==x||null==(t=x.pusherData)?void 0:t.channel_key,E=null==x?void 0:x.pusherData.app_key,O=null==x?void 0:x.pusherData.cluster;n.useEffect(()=>{if(!s&&E&&O)return s=!0,a.e(71).then(a.bind(a,9071)).then(e=>{P(new e.default(E,{cluster:O}))}).catch(e=>{console.log("error importing pusher"),console.error(e)}),()=>{s=!1}},[E,O]),n.useEffect(()=>{if(!T||!k)return;let e=k.subscribe(T);return e.bind("poke",e=>{var t;(null==e||null==(t=e.mutatedEntryTypes)?void 0:t.includes("block"))&&e.branch===y.current&&o.forEach(e=>e())}),()=>{e.unsubscribe()}},[k,T]),n.useEffect(()=>{function e(){let e=window.__bshb_ref;e&&"string"==typeof e&&N(e)}return e(),window.addEventListener("__bshb_ref_changed",e),()=>{window.removeEventListener("__bshb_ref_changed",e)}},[]);let A=n.useMemo(()=>null==x?void 0:x.data.map((e,t)=>{var a,n;return null!=(n=null!=e?e:null==f||null==(a=f.data)?void 0:a[t])?n:null}),[null==f?void 0:f.data,null==x?void 0:x.data]),[C,S]=n.useState("function"==typeof d?h:d);return n.useEffect(()=>{if(A)if("function"==typeof d){let e=d(A);e instanceof Promise?e.then(S):S(e)}else S(d)},[d,A]),null!=C?C:h}},82920:(e,t,a)=>{"use strict";a.d(t,{P:()=>r});var n=Object.getOwnPropertyNames,r=(e,t)=>function(){return t||(0,e[n(e)[0]])((t={exports:{}}).exports,t),t.exports}},89069:(e,t,a)=>{"use strict";a.d(t,{Toaster:()=>o});var n=a(6024),r=a(72881),s=a(13957);let o=e=>{let{...t}=e,{theme:a="system"}=(0,r.D)();return(0,n.jsx)(s.l,{theme:a,className:"toaster group",style:{"--normal-bg":"var(--popover)","--normal-text":"var(--popover-foreground)","--normal-border":"var(--border)"},...t})}}},e=>{var t=t=>e(e.s=t);e.O(0,[98,861,81,584,213,685,325,757,84,762,836,524,551,913,499,358],()=>t(1885)),_N_E=e.O()}]);