"use strict";exports.id=602,exports.ids=[602],exports.modules={70602:(e,s,t)=>{t.r(s),t.d(s,{default:()=>o});var l=t(31767);let a=Object.freeze({displayName:"Shell Session",fileTypes:["sh-session"],name:"shellsession",patterns:[{captures:{1:{name:"entity.other.prompt-prefix.shell-session"},2:{name:"punctuation.separator.prompt.shell-session"},3:{name:"source.shell",patterns:[{include:"source.shell"}]}},match:"^(?:((?:\\(\\S+\\)\\s*)?(?:sh\\S*?|\\w+\\S+[@:]\\S+(?:\\s+\\S+)?|\\[\\S+?[@:][^\\n]+?\\].*?))\\s*)?([>$#%❯➜]|\\p{Greek})\\s+(.*)$"},{match:"^.+$",name:"meta.output.shell-session"}],scopeName:"text.shell-session",embeddedLangs:["shellscript"],aliases:["console"]});var o=[...l.default,a]}};