(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[454],{24323:(e,t,r)=>{"use strict";r.r(t),r.d(t,{BaseHubImage:()=>g,basehubImageLoader:()=>v});var n=Object.defineProperty,a=Object.defineProperties,l=Object.getOwnPropertyDescriptors,s=Object.getOwnPropertySymbols,o=Object.prototype.hasOwnProperty,u=Object.prototype.propertyIsEnumerable,i=(e,t,r)=>t in e?n(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,c=(e,t)=>{for(var r in t||(t={}))o.call(t,r)&&i(e,r,t[r]);if(s)for(var r of s(t))u.call(t,r)&&i(e,r,t[r]);return e},h=(e,t)=>a(e,l(t)),p=r(82757),f=r(50628),d=r(6024),m="https://basehub.earth",b="https://assets.basehub.com",v=e=>{let t,{src:r,width:n,quality:a}=e;try{t=new URL(r)}catch(e){throw Error("Invalid BaseHub Image URL: ".concat(r,"\n\nExpected origin to be one of:\n- ").concat(m," (deprecated)\n- ").concat(b,"\n"))}let l=["width=".concat(n),"quality=".concat(a||90)];if(t.href.includes(m))if(t.pathname.startsWith("/cdn-cgi/image/")){let[e,r,n,a="",...s]=t.pathname.split("/"),o=[...a.split(",").filter(e=>!e.startsWith("width=")&&!e.startsWith("quality=")&&!e.startsWith("w=")&&!e.startsWith("q=")&&!e.startsWith("h=")&&!e.startsWith("height=")),...l].join(",");!1===o.includes("format=")&&(o+=",format=auto"),t.pathname="/cdn-cgi/image/".concat(o,"/").concat(s.join("/"))}else l.push("format=auto"),t.pathname="/cdn-cgi/image/".concat(l.join(",")).concat(t.pathname);else t.href.includes(b)&&(l.forEach(e=>{let[r,n]=e.split("=");r&&n&&t.searchParams.set(r,n)}),!1===t.searchParams.has("format")&&t.searchParams.set("format","auto"),t.searchParams.delete("height"),t.searchParams.delete("h"));let s=new URL(b);if(t.href.includes(m))if(t.pathname.startsWith("/cdn-cgi/image/")){let[e,r,n,a="",...l]=t.pathname.split("/");s.pathname=l.join("/"),s.search=a.split(",").join("&")}else s.pathname=t.pathname,s.search=t.search;else{if(!t.href.includes(b))return r;s.pathname=t.pathname,s.search=t.search}return s.toString()},g=(0,f.forwardRef)((e,t)=>{var r,n,a;let l=null!=(a=null!=(n=e.unoptimized)?n:null==(r=e.src.toString().split("?")[0])?void 0:r.endsWith(".svg"))?a:void 0;return(0,d.jsx)(p.default,h(c({},e),{placeholder:e.placeholder,loader:v,unoptimized:l,ref:t}))})},28574:(e,t,r)=>{Promise.resolve().then(r.bind(r,43432)),Promise.resolve().then(r.bind(r,24323)),Promise.resolve().then(r.t.bind(r,35685,23)),Promise.resolve().then(r.bind(r,13957)),Promise.resolve().then(r.bind(r,71497))},71497:(e,t,r)=>{"use strict";r.r(t),r.d(t,{ClientPump:()=>i}),r(82920);var n=r(50628);let a="__alias__";var l=!1,s=new Set,o=new Map,u=new Map,i=e=>{var t;let{children:i,rawQueries:c,pumpEndpoint:h,pumpToken:p,initialState:f,initialResolvedChildren:d,apiVersion:m,previewRef:b}=e,v=n.useRef(p),[g,w]=n.useState(f),y=n.useRef(f);y.current=f;let[P,_]=n.useState(b),j=n.useRef(P);j.current=P;let k=n.useCallback(async()=>{let e,t,r,n=await Promise.all(c.map(async(n,l)=>{var s,i;if(!v.current)return console.warn("No pump token found. Skipping query."),null;let c=JSON.stringify(n),p=c+P,f=u.get(c)||(null==(i=y.current)||null==(s=i.responseHashes)?void 0:s[l])||"";if(o.has(p)){let n=o.get(p);if(performance.now()-n.start<32){let a=await n.response;return a?(a.newPumpToken&&(e=a.newPumpToken),t=a.pusherData,r=a.spaceID,a):null}}let d=fetch(h,{cache:"no-store",method:"POST",headers:{"content-type":"application/json","x-basehub-api-version":m,"x-basehub-ref":P},body:JSON.stringify({...n,pumpToken:v.current,lastResponseHash:f})}).then(async e=>{let{data:t=null,errors:r=null,newPumpToken:n,spaceID:l,pusherData:s,responseHash:o}=await e.json();return u.set(c,o),{data:function e(t){if("object"!=typeof t||null===t)return t;if(Array.isArray(t))return t.map(t=>e(t));let r={};for(let[n,l]of Object.entries(t))if(n.includes(a)){let[t,...s]=n.split(a);r[s.join(a)]=e(l)}else r[n]=e(l);return r}(t),spaceID:l,pusherData:s,newPumpToken:n,errors:r,responseHash:o,changed:f!==o}}).catch(e=>{console.error("Error fetching data from the BaseHub Draft API:\n              \n".concat(JSON.stringify(e,null,2),"\n              \nContact <EMAIL> for help."))});o.set(p,{start:performance.now(),response:d});let b=await d;return b?(b.newPumpToken&&(e=b.newPumpToken),t=b.pusherData,r=b.spaceID,b):null}));if(n.some(e=>null==e?void 0:e.changed)){if(!t||!r)return;w(e=>t&&r?{data:n.map((t,r)=>{var n,a,l;return(null==t?void 0:t.changed)?null!=(l=null==t?void 0:t.data)?l:null:null!=(a=null==e||null==(n=e.data)?void 0:n[r])?a:null}),errors:n.map((t,r)=>{var n,a,l;return(null==t?void 0:t.changed)?null!=(l=null==t?void 0:t.errors)?l:null:null!=(a=null==e||null==(n=e.errors)?void 0:n[r])?a:null}),responseHashes:n.map(e=>{var t;return null!=(t=null==e?void 0:e.responseHash)?t:""}),pusherData:t,spaceID:r}:e)}e&&(v.current=e)},[h,c,m,P]);n.useRef(null),n.useEffect(()=>{var e;if(!(null==g?void 0:g.errors))return;let t=null==(e=g.errors[0])?void 0:e[0];t&&console.error("Error fetching data from the BaseHub Draft API: ".concat(t.message).concat(t.path?" at ".concat(t.path.join(".")):""))},[null==g?void 0:g.errors]),n.useEffect(()=>{function e(){k()}return e(),s.add(e),()=>{s.delete(e)}},[k]);let[E,O]=n.useState(null),D=null==g||null==(t=g.pusherData)?void 0:t.channel_key,S=null==g?void 0:g.pusherData.app_key,I=null==g?void 0:g.pusherData.cluster;n.useEffect(()=>{if(!l&&S&&I)return l=!0,r.e(71).then(r.bind(r,9071)).then(e=>{O(new e.default(S,{cluster:I}))}).catch(e=>{console.log("error importing pusher"),console.error(e)}),()=>{l=!1}},[S,I]),n.useEffect(()=>{if(!D||!E)return;let e=E.subscribe(D);return e.bind("poke",e=>{var t;(null==e||null==(t=e.mutatedEntryTypes)?void 0:t.includes("block"))&&e.branch===j.current&&s.forEach(e=>e())}),()=>{e.unsubscribe()}},[E,D]),n.useEffect(()=>{function e(){let e=window.__bshb_ref;e&&"string"==typeof e&&_(e)}return e(),window.addEventListener("__bshb_ref_changed",e),()=>{window.removeEventListener("__bshb_ref_changed",e)}},[]);let T=n.useMemo(()=>null==g?void 0:g.data.map((e,t)=>{var r,n;return null!=(n=null!=e?e:null==f||null==(r=f.data)?void 0:r[t])?n:null}),[null==f?void 0:f.data,null==g?void 0:g.data]),[R,W]=n.useState("function"==typeof i?d:i);return n.useEffect(()=>{if(T)if("function"==typeof i){let e=i(T);e instanceof Promise?e.then(W):W(e)}else W(i)},[i,T]),null!=R?R:d}},82920:(e,t,r)=>{"use strict";r.d(t,{P:()=>a});var n=Object.getOwnPropertyNames,a=(e,t)=>function(){return t||(0,e[n(e)[0]])((t={exports:{}}).exports,t),t.exports}}},e=>{var t=t=>e(e.s=t);e.O(0,[213,685,757,913,499,358],()=>t(28574)),_N_E=e.O()}]);