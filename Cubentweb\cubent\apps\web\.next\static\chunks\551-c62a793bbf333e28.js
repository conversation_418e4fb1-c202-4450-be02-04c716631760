(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[551],{463:(e,t)=>{"use strict";function r(e,t){return void 0===t&&(t=""),("/"===e?"/index":/^\/index(\/|$)/.test(e)?"/index"+e:e)+t}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return r}})},4937:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{bindSnapshot:function(){return o},createAsyncLocalStorage:function(){return a},createSnapshot:function(){return s}});let r=Object.defineProperty(Error("Invariant: AsyncLocalStorage accessed in runtime where it is not available"),"__NEXT_ERROR_CODE",{value:"E504",enumerable:!1,configurable:!0});class n{disable(){throw r}getStore(){}run(){throw r}exit(){throw r}enterWith(){throw r}static bind(e){return e}}let i="undefined"!=typeof globalThis&&globalThis.AsyncLocalStorage;function a(){return i?new i:new n}function o(e){return i?i.bind(e):n.bind(e)}function s(){return i?i.snapshot():function(e,...t){return e(...t)}}},5011:(e,t,r)=>{"use strict";r.d(t,{UC:()=>eg,q7:()=>ep,N_:()=>em,B8:()=>eh,bL:()=>ec,l9:()=>ef,LM:()=>ev});var n=r(50628),i=r(6341),a=r(48733),o=r(13859),s=r(64826),l=r(17691),u=r(98064),d=r(85532),c=r(64714),h=r(29823),p=r(3057),f=r(78557),m=r(84268),g=r(72336),v=r(63680),_=r(6024),y="NavigationMenu",[b,w,E]=(0,p.N)(y),[k,P,x]=(0,p.N)(y),[S,R]=(0,a.A)(y,[E,x]),[C,O]=S(y),[j,T]=S(y),A=n.forwardRef((e,t)=>{let{__scopeNavigationMenu:r,value:i,onValueChange:a,defaultValue:o,delayDuration:c=200,skipDelayDuration:h=300,orientation:p="horizontal",dir:f,...m}=e,[g,v]=n.useState(null),b=(0,u.s)(t,e=>v(e)),w=(0,d.jH)(f),E=n.useRef(0),k=n.useRef(0),P=n.useRef(0),[x,S]=n.useState(!0),[R,C]=(0,l.i)({prop:i,onChange:e=>{let t=h>0;""!==e?(window.clearTimeout(P.current),t&&S(!1)):(window.clearTimeout(P.current),P.current=window.setTimeout(()=>S(!0),h)),null==a||a(e)},defaultProp:null!=o?o:"",caller:y}),O=n.useCallback(()=>{window.clearTimeout(k.current),k.current=window.setTimeout(()=>C(""),150)},[C]),j=n.useCallback(e=>{window.clearTimeout(k.current),C(e)},[C]),T=n.useCallback(e=>{R===e?window.clearTimeout(k.current):E.current=window.setTimeout(()=>{window.clearTimeout(k.current),C(e)},c)},[R,C,c]);return n.useEffect(()=>()=>{window.clearTimeout(E.current),window.clearTimeout(k.current),window.clearTimeout(P.current)},[]),(0,_.jsx)(M,{scope:r,isRootMenu:!0,value:R,dir:w,orientation:p,rootNavigationMenu:g,onTriggerEnter:e=>{window.clearTimeout(E.current),x?T(e):j(e)},onTriggerLeave:()=>{window.clearTimeout(E.current),O()},onContentEnter:()=>window.clearTimeout(k.current),onContentLeave:O,onItemSelect:e=>{C(t=>t===e?"":e)},onItemDismiss:()=>C(""),children:(0,_.jsx)(s.sG.nav,{"aria-label":"Main","data-orientation":p,dir:w,...m,ref:b})})});A.displayName=y;var I="NavigationMenuSub";n.forwardRef((e,t)=>{let{__scopeNavigationMenu:r,value:n,onValueChange:i,defaultValue:a,orientation:o="horizontal",...u}=e,d=O(I,r),[c,h]=(0,l.i)({prop:n,onChange:i,defaultProp:null!=a?a:"",caller:I});return(0,_.jsx)(M,{scope:r,isRootMenu:!1,value:c,dir:d.dir,orientation:o,rootNavigationMenu:d.rootNavigationMenu,onTriggerEnter:e=>h(e),onItemSelect:e=>h(e),onItemDismiss:()=>h(""),children:(0,_.jsx)(s.sG.div,{"data-orientation":o,...u,ref:t})})}).displayName=I;var M=e=>{let{scope:t,isRootMenu:r,rootNavigationMenu:i,dir:a,orientation:o,children:s,value:l,onItemSelect:u,onItemDismiss:d,onTriggerEnter:c,onTriggerLeave:p,onContentEnter:f,onContentLeave:m}=e,[v,y]=n.useState(null),[w,E]=n.useState(new Map),[k,P]=n.useState(null);return(0,_.jsx)(C,{scope:t,isRootMenu:r,rootNavigationMenu:i,value:l,previousValue:function(e){let t=n.useRef({value:e,previous:e});return n.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}(l),baseId:(0,h.B)(),dir:a,orientation:o,viewport:v,onViewportChange:y,indicatorTrack:k,onIndicatorTrackChange:P,onTriggerEnter:(0,g.c)(c),onTriggerLeave:(0,g.c)(p),onContentEnter:(0,g.c)(f),onContentLeave:(0,g.c)(m),onItemSelect:(0,g.c)(u),onItemDismiss:(0,g.c)(d),onViewportContentChange:n.useCallback((e,t)=>{E(r=>(r.set(e,t),new Map(r)))},[]),onViewportContentRemove:n.useCallback(e=>{E(t=>t.has(e)?(t.delete(e),new Map(t)):t)},[]),children:(0,_.jsx)(b.Provider,{scope:t,children:(0,_.jsx)(j,{scope:t,items:w,children:s})})})},N="NavigationMenuList",L=n.forwardRef((e,t)=>{let{__scopeNavigationMenu:r,...n}=e,i=O(N,r),a=(0,_.jsx)(s.sG.ul,{"data-orientation":i.orientation,...n,ref:t});return(0,_.jsx)(s.sG.div,{style:{position:"relative"},ref:i.onIndicatorTrackChange,children:(0,_.jsx)(b.Slot,{scope:r,children:i.isRootMenu?(0,_.jsx)(et,{asChild:!0,children:a}):a})})});L.displayName=N;var U="NavigationMenuItem",[D,F]=S(U),B=n.forwardRef((e,t)=>{let{__scopeNavigationMenu:r,value:i,...a}=e,o=(0,h.B)(),l=n.useRef(null),u=n.useRef(null),d=n.useRef(null),c=n.useRef(()=>{}),p=n.useRef(!1),f=n.useCallback(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"start";if(l.current){c.current();let t=ei(l.current);t.length&&ea("start"===e?t:t.reverse())}},[]),m=n.useCallback(()=>{if(l.current){let e=ei(l.current);e.length&&(c.current=function(e){return e.forEach(e=>{e.dataset.tabindex=e.getAttribute("tabindex")||"",e.setAttribute("tabindex","-1")}),()=>{e.forEach(e=>{let t=e.dataset.tabindex;e.setAttribute("tabindex",t)})}}(e))}},[]);return(0,_.jsx)(D,{scope:r,value:i||o||"LEGACY_REACT_AUTO_VALUE",triggerRef:u,contentRef:l,focusProxyRef:d,wasEscapeCloseRef:p,onEntryKeyDown:f,onFocusProxyEnter:f,onRootContentClose:m,onContentFocusOutside:m,children:(0,_.jsx)(s.sG.li,{...a,ref:t})})});B.displayName=U;var W="NavigationMenuTrigger",z=n.forwardRef((e,t)=>{let{__scopeNavigationMenu:r,disabled:i,...a}=e,l=O(W,e.__scopeNavigationMenu),d=F(W,e.__scopeNavigationMenu),c=n.useRef(null),h=(0,u.s)(c,d.triggerRef,t),p=el(l.baseId,d.value),f=eu(l.baseId,d.value),m=n.useRef(!1),g=n.useRef(!1),y=d.value===l.value;return(0,_.jsxs)(_.Fragment,{children:[(0,_.jsx)(b.ItemSlot,{scope:r,value:d.value,children:(0,_.jsx)(en,{asChild:!0,children:(0,_.jsx)(s.sG.button,{id:p,disabled:i,"data-disabled":i?"":void 0,"data-state":es(y),"aria-expanded":y,"aria-controls":f,...a,ref:h,onPointerEnter:(0,o.m)(e.onPointerEnter,()=>{g.current=!1,d.wasEscapeCloseRef.current=!1}),onPointerMove:(0,o.m)(e.onPointerMove,ed(()=>{i||g.current||d.wasEscapeCloseRef.current||m.current||(l.onTriggerEnter(d.value),m.current=!0)})),onPointerLeave:(0,o.m)(e.onPointerLeave,ed(()=>{i||(l.onTriggerLeave(),m.current=!1)})),onClick:(0,o.m)(e.onClick,()=>{l.onItemSelect(d.value),g.current=y}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let t={horizontal:"ArrowDown",vertical:"rtl"===l.dir?"ArrowLeft":"ArrowRight"}[l.orientation];y&&e.key===t&&(d.onEntryKeyDown(),e.preventDefault())})})})}),y&&(0,_.jsxs)(_.Fragment,{children:[(0,_.jsx)(v.bL,{"aria-hidden":!0,tabIndex:0,ref:d.focusProxyRef,onFocus:e=>{let t=d.contentRef.current,r=e.relatedTarget,n=r===c.current,i=null==t?void 0:t.contains(r);(n||!i)&&d.onFocusProxyEnter(n?"start":"end")}}),l.viewport&&(0,_.jsx)("span",{"aria-owns":f})]})]})});z.displayName=W;var Z="navigationMenu.linkSelect",K=n.forwardRef((e,t)=>{let{__scopeNavigationMenu:r,active:n,onSelect:i,...a}=e;return(0,_.jsx)(en,{asChild:!0,children:(0,_.jsx)(s.sG.a,{"data-active":n?"":void 0,"aria-current":n?"page":void 0,...a,ref:t,onClick:(0,o.m)(e.onClick,e=>{let t=e.target,r=new CustomEvent(Z,{bubbles:!0,cancelable:!0});if(t.addEventListener(Z,e=>null==i?void 0:i(e),{once:!0}),(0,s.hO)(t,r),!r.defaultPrevented&&!e.metaKey){let e=new CustomEvent(q,{bubbles:!0,cancelable:!0});(0,s.hO)(t,e)}},{checkForDefaultPrevented:!1})})})});K.displayName="NavigationMenuLink";var V="NavigationMenuIndicator";n.forwardRef((e,t)=>{let{forceMount:r,...n}=e,a=O(V,e.__scopeNavigationMenu),o=!!a.value;return a.indicatorTrack?i.createPortal((0,_.jsx)(c.C,{present:r||o,children:(0,_.jsx)(H,{...n,ref:t})}),a.indicatorTrack):null}).displayName=V;var H=n.forwardRef((e,t)=>{let{__scopeNavigationMenu:r,...i}=e,a=O(V,r),o=w(r),[l,u]=n.useState(null),[d,c]=n.useState(null),h="horizontal"===a.orientation,p=!!a.value;n.useEffect(()=>{var e;let t=null==(e=o().find(e=>e.value===a.value))?void 0:e.ref.current;t&&u(t)},[o,a.value]);let f=()=>{l&&c({size:h?l.offsetWidth:l.offsetHeight,offset:h?l.offsetLeft:l.offsetTop})};return eo(l,f),eo(a.indicatorTrack,f),d?(0,_.jsx)(s.sG.div,{"aria-hidden":!0,"data-state":p?"visible":"hidden","data-orientation":a.orientation,...i,ref:t,style:{position:"absolute",...h?{left:0,width:d.size+"px",transform:"translateX(".concat(d.offset,"px)")}:{top:0,height:d.size+"px",transform:"translateY(".concat(d.offset,"px)")},...i.style}}):null}),$="NavigationMenuContent",X=n.forwardRef((e,t)=>{let{forceMount:r,...n}=e,i=O($,e.__scopeNavigationMenu),a=F($,e.__scopeNavigationMenu),s=(0,u.s)(a.contentRef,t),l=a.value===i.value,d={value:a.value,triggerRef:a.triggerRef,focusProxyRef:a.focusProxyRef,wasEscapeCloseRef:a.wasEscapeCloseRef,onContentFocusOutside:a.onContentFocusOutside,onRootContentClose:a.onRootContentClose,...n};return i.viewport?(0,_.jsx)(G,{forceMount:r,...d,ref:s}):(0,_.jsx)(c.C,{present:r||l,children:(0,_.jsx)(Y,{"data-state":es(l),...d,ref:s,onPointerEnter:(0,o.m)(e.onPointerEnter,i.onContentEnter),onPointerLeave:(0,o.m)(e.onPointerLeave,ed(i.onContentLeave)),style:{pointerEvents:!l&&i.isRootMenu?"none":void 0,...d.style}})})});X.displayName=$;var G=n.forwardRef((e,t)=>{let{onViewportContentChange:r,onViewportContentRemove:n}=O($,e.__scopeNavigationMenu);return(0,m.N)(()=>{r(e.value,{ref:t,...e})},[e,t,r]),(0,m.N)(()=>()=>n(e.value),[e.value,n]),null}),q="navigationMenu.rootContentDismiss",Y=n.forwardRef((e,t)=>{let{__scopeNavigationMenu:r,value:i,triggerRef:a,focusProxyRef:s,wasEscapeCloseRef:l,onRootContentClose:d,onContentFocusOutside:c,...h}=e,p=O($,r),m=n.useRef(null),g=(0,u.s)(m,t),v=el(p.baseId,i),y=eu(p.baseId,i),b=w(r),E=n.useRef(null),{onItemDismiss:k}=p;n.useEffect(()=>{let e=m.current;if(p.isRootMenu&&e){let t=()=>{var t;k(),d(),e.contains(document.activeElement)&&(null==(t=a.current)||t.focus())};return e.addEventListener(q,t),()=>e.removeEventListener(q,t)}},[p.isRootMenu,e.value,a,k,d]);let P=n.useMemo(()=>{let e=b().map(e=>e.value);"rtl"===p.dir&&e.reverse();let t=e.indexOf(p.value),r=e.indexOf(p.previousValue),n=i===p.value,a=r===e.indexOf(i);if(!n&&!a)return E.current;let o=(()=>{if(t!==r){if(n&&-1!==r)return t>r?"from-end":"from-start";if(a&&-1!==t)return t>r?"to-start":"to-end"}return null})();return E.current=o,o},[p.previousValue,p.value,p.dir,b,i]);return(0,_.jsx)(et,{asChild:!0,children:(0,_.jsx)(f.qW,{id:y,"aria-labelledby":v,"data-motion":P,"data-orientation":p.orientation,...h,ref:g,disableOutsidePointerEvents:!1,onDismiss:()=>{var e;let t=new Event(q,{bubbles:!0,cancelable:!0});null==(e=m.current)||e.dispatchEvent(t)},onFocusOutside:(0,o.m)(e.onFocusOutside,e=>{var t;c();let r=e.target;(null==(t=p.rootNavigationMenu)?void 0:t.contains(r))&&e.preventDefault()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{var t;let r=e.target,n=b().some(e=>{var t;return null==(t=e.ref.current)?void 0:t.contains(r)}),i=p.isRootMenu&&(null==(t=p.viewport)?void 0:t.contains(r));(n||i||!p.isRootMenu)&&e.preventDefault()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let t=e.altKey||e.ctrlKey||e.metaKey;if("Tab"===e.key&&!t){let t=ei(e.currentTarget),n=document.activeElement,i=t.findIndex(e=>e===n);if(ea(e.shiftKey?t.slice(0,i).reverse():t.slice(i+1,t.length)))e.preventDefault();else{var r;null==(r=s.current)||r.focus()}}}),onEscapeKeyDown:(0,o.m)(e.onEscapeKeyDown,e=>{l.current=!0})})})}),J="NavigationMenuViewport",Q=n.forwardRef((e,t)=>{let{forceMount:r,...n}=e,i=!!O(J,e.__scopeNavigationMenu).value;return(0,_.jsx)(c.C,{present:r||i,children:(0,_.jsx)(ee,{...n,ref:t})})});Q.displayName=J;var ee=n.forwardRef((e,t)=>{let{__scopeNavigationMenu:r,children:i,...a}=e,l=O(J,r),d=(0,u.s)(t,l.onViewportChange),h=T($,e.__scopeNavigationMenu),[p,f]=n.useState(null),[m,g]=n.useState(null),v=p?(null==p?void 0:p.width)+"px":void 0,y=p?(null==p?void 0:p.height)+"px":void 0,b=!!l.value,w=b?l.value:l.previousValue;return eo(m,()=>{m&&f({width:m.offsetWidth,height:m.offsetHeight})}),(0,_.jsx)(s.sG.div,{"data-state":es(b),"data-orientation":l.orientation,...a,ref:d,style:{pointerEvents:!b&&l.isRootMenu?"none":void 0,"--radix-navigation-menu-viewport-width":v,"--radix-navigation-menu-viewport-height":y,...a.style},onPointerEnter:(0,o.m)(e.onPointerEnter,l.onContentEnter),onPointerLeave:(0,o.m)(e.onPointerLeave,ed(l.onContentLeave)),children:Array.from(h.items).map(e=>{let[t,{ref:r,forceMount:n,...i}]=e,a=w===t;return(0,_.jsx)(c.C,{present:n||a,children:(0,_.jsx)(Y,{...i,ref:(0,u.t)(r,e=>{a&&e&&g(e)})})},t)})})}),et=n.forwardRef((e,t)=>{let{__scopeNavigationMenu:r,...n}=e,i=O("FocusGroup",r);return(0,_.jsx)(k.Provider,{scope:r,children:(0,_.jsx)(k.Slot,{scope:r,children:(0,_.jsx)(s.sG.div,{dir:i.dir,...n,ref:t})})})}),er=["ArrowRight","ArrowLeft","ArrowUp","ArrowDown"],en=n.forwardRef((e,t)=>{let{__scopeNavigationMenu:r,...n}=e,i=P(r),a=O("FocusGroupItem",r);return(0,_.jsx)(k.ItemSlot,{scope:r,children:(0,_.jsx)(s.sG.button,{...n,ref:t,onKeyDown:(0,o.m)(e.onKeyDown,e=>{if(["Home","End",...er].includes(e.key)){let t=i().map(e=>e.ref.current);if(["rtl"===a.dir?"ArrowRight":"ArrowLeft","ArrowUp","End"].includes(e.key)&&t.reverse(),er.includes(e.key)){let r=t.indexOf(e.currentTarget);t=t.slice(r+1)}setTimeout(()=>ea(t)),e.preventDefault()}})})})});function ei(e){let t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}function ea(e){let t=document.activeElement;return e.some(e=>e===t||(e.focus(),document.activeElement!==t))}function eo(e,t){let r=(0,g.c)(t);(0,m.N)(()=>{let t=0;if(e){let n=new ResizeObserver(()=>{cancelAnimationFrame(t),t=window.requestAnimationFrame(r)});return n.observe(e),()=>{window.cancelAnimationFrame(t),n.unobserve(e)}}},[e,r])}function es(e){return e?"open":"closed"}function el(e,t){return"".concat(e,"-trigger-").concat(t)}function eu(e,t){return"".concat(e,"-content-").concat(t)}function ed(e){return t=>"mouse"===t.pointerType?e(t):void 0}var ec=A,eh=L,ep=B,ef=z,em=K,eg=X,ev=Q},5343:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{compileNonPath:function(){return d},matchHas:function(){return u},parseDestination:function(){return c},prepareDestination:function(){return h}});let n=r(83824),i=r(94472),a=r(19910),o=r(25336),s=r(84905);function l(e){return e.replace(/__ESC_COLON_/gi,":")}function u(e,t,r,n){void 0===r&&(r=[]),void 0===n&&(n=[]);let i={},a=r=>{let n,a=r.key;switch(r.type){case"header":a=a.toLowerCase(),n=e.headers[a];break;case"cookie":n="cookies"in e?e.cookies[r.key]:(0,s.getCookieParser)(e.headers)()[r.key];break;case"query":n=t[a];break;case"host":{let{host:t}=(null==e?void 0:e.headers)||{};n=null==t?void 0:t.split(":",1)[0].toLowerCase()}}if(!r.value&&n)return i[function(e){let t="";for(let r=0;r<e.length;r++){let n=e.charCodeAt(r);(n>64&&n<91||n>96&&n<123)&&(t+=e[r])}return t}(a)]=n,!0;if(n){let e=RegExp("^"+r.value+"$"),t=Array.isArray(n)?n.slice(-1)[0].match(e):n.match(e);if(t)return Array.isArray(t)&&(t.groups?Object.keys(t.groups).forEach(e=>{i[e]=t.groups[e]}):"host"===r.type&&t[0]&&(i.host=t[0])),!0}return!1};return!(!r.every(e=>a(e))||n.some(e=>a(e)))&&i}function d(e,t){if(!e.includes(":"))return e;for(let r of Object.keys(t))e.includes(":"+r)&&(e=e.replace(RegExp(":"+r+"\\*","g"),":"+r+"--ESCAPED_PARAM_ASTERISKS").replace(RegExp(":"+r+"\\?","g"),":"+r+"--ESCAPED_PARAM_QUESTION").replace(RegExp(":"+r+"\\+","g"),":"+r+"--ESCAPED_PARAM_PLUS").replace(RegExp(":"+r+"(?!\\w)","g"),"--ESCAPED_PARAM_COLON"+r));return e=e.replace(/(:|\*|\?|\+|\(|\)|\{|\})/g,"\\$1").replace(/--ESCAPED_PARAM_PLUS/g,"+").replace(/--ESCAPED_PARAM_COLON/g,":").replace(/--ESCAPED_PARAM_QUESTION/g,"?").replace(/--ESCAPED_PARAM_ASTERISKS/g,"*"),(0,n.compile)("/"+e,{validate:!1})(t).slice(1)}function c(e){let t=e.destination;for(let r of Object.keys({...e.params,...e.query}))r&&(t=t.replace(RegExp(":"+(0,i.escapeStringRegexp)(r),"g"),"__ESC_COLON_"+r));let r=(0,a.parseUrl)(t),n=r.pathname;n&&(n=l(n));let o=r.href;o&&(o=l(o));let s=r.hostname;s&&(s=l(s));let u=r.hash;return u&&(u=l(u)),{...r,pathname:n,hostname:s,href:o,hash:u}}function h(e){let t,r,i=Object.assign({},e.query),a=c(e),{hostname:s,query:u}=a,h=a.pathname;a.hash&&(h=""+h+a.hash);let p=[],f=[];for(let e of((0,n.pathToRegexp)(h,f),f))p.push(e.name);if(s){let e=[];for(let t of((0,n.pathToRegexp)(s,e),e))p.push(t.name)}let m=(0,n.compile)(h,{validate:!1});for(let[r,i]of(s&&(t=(0,n.compile)(s,{validate:!1})),Object.entries(u)))Array.isArray(i)?u[r]=i.map(t=>d(l(t),e.params)):"string"==typeof i&&(u[r]=d(l(i),e.params));let g=Object.keys(e.params).filter(e=>"nextInternalLocale"!==e);if(e.appendParamsToQuery&&!g.some(e=>p.includes(e)))for(let t of g)t in u||(u[t]=e.params[t]);if((0,o.isInterceptionRouteAppPath)(h))for(let t of h.split("/")){let r=o.INTERCEPTION_ROUTE_MARKERS.find(e=>t.startsWith(e));if(r){"(..)(..)"===r?(e.params["0"]="(..)",e.params["1"]="(..)"):e.params["0"]=r;break}}try{let[n,i]=(r=m(e.params)).split("#",2);t&&(a.hostname=t(e.params)),a.pathname=n,a.hash=(i?"#":"")+(i||""),delete a.search}catch(e){if(e.message.match(/Expected .*? to not repeat, but got an array/))throw Object.defineProperty(Error("To use a multi-match in the destination you must add `*` at the end of the param name to signify it should repeat. https://nextjs.org/docs/messages/invalid-multi-match"),"__NEXT_ERROR_CODE",{value:"E329",enumerable:!1,configurable:!0});throw e}return a.query={...i,...a.query},{newUrl:r,destQuery:u,parsedDestination:a}}},5527:(e,t)=>{"use strict";function r(e){return e.replace(/\\/g,"/")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathSep",{enumerable:!0,get:function(){return r}})},5659:(e,t,r)=>{"use strict";function n(e){let{reason:t,children:r}=e;return r}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"BailoutToCSR",{enumerable:!0,get:function(){return n}}),r(46319)},5826:e=>{e.exports={style:{fontFamily:"'GeistSans', 'GeistSans Fallback'"},className:"__className_fb8f2c",variable:"__variable_fb8f2c"}},5850:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let n=r(88237);n.__exportStar(r(79715),t),n.__exportStar(r(62311),t)},9379:(e,t,r)=>{"use strict";r.d(t,{Kq:()=>L});var n=r(50628),i=r(13859),a=r(98064),o=r(48733),s=r(78557),l=(r(29823),r(77222)),u=(r(4844),r(64714)),d=r(64826),c=r(89840),h=(r(17691),r(63680)),p=r(6024),[f,m]=(0,o.A)("Tooltip",[l.Bk]),g=(0,l.Bk)(),v="TooltipProvider",_="tooltip.open",[y,b]=f(v),w=e=>{let{__scopeTooltip:t,delayDuration:r=700,skipDelayDuration:i=300,disableHoverableContent:a=!1,children:o}=e,s=n.useRef(!0),l=n.useRef(!1),u=n.useRef(0);return n.useEffect(()=>{let e=u.current;return()=>window.clearTimeout(e)},[]),(0,p.jsx)(y,{scope:t,isOpenDelayedRef:s,delayDuration:r,onOpen:n.useCallback(()=>{window.clearTimeout(u.current),s.current=!1},[]),onClose:n.useCallback(()=>{window.clearTimeout(u.current),u.current=window.setTimeout(()=>s.current=!0,i)},[i]),isPointerInTransitRef:l,onPointerInTransitChange:n.useCallback(e=>{l.current=e},[]),disableHoverableContent:a,children:o})};w.displayName=v;var E="Tooltip",[k,P]=f(E),x="TooltipTrigger";n.forwardRef((e,t)=>{let{__scopeTooltip:r,...o}=e,s=P(x,r),u=b(x,r),c=g(r),h=n.useRef(null),f=(0,a.s)(t,h,s.onTriggerChange),m=n.useRef(!1),v=n.useRef(!1),_=n.useCallback(()=>m.current=!1,[]);return n.useEffect(()=>()=>document.removeEventListener("pointerup",_),[_]),(0,p.jsx)(l.Mz,{asChild:!0,...c,children:(0,p.jsx)(d.sG.button,{"aria-describedby":s.open?s.contentId:void 0,"data-state":s.stateAttribute,...o,ref:f,onPointerMove:(0,i.m)(e.onPointerMove,e=>{"touch"!==e.pointerType&&(v.current||u.isPointerInTransitRef.current||(s.onTriggerEnter(),v.current=!0))}),onPointerLeave:(0,i.m)(e.onPointerLeave,()=>{s.onTriggerLeave(),v.current=!1}),onPointerDown:(0,i.m)(e.onPointerDown,()=>{s.open&&s.onClose(),m.current=!0,document.addEventListener("pointerup",_,{once:!0})}),onFocus:(0,i.m)(e.onFocus,()=>{m.current||s.onOpen()}),onBlur:(0,i.m)(e.onBlur,s.onClose),onClick:(0,i.m)(e.onClick,s.onClose)})})}).displayName=x;var[S,R]=f("TooltipPortal",{forceMount:void 0}),C="TooltipContent",O=n.forwardRef((e,t)=>{let r=R(C,e.__scopeTooltip),{forceMount:n=r.forceMount,side:i="top",...a}=e,o=P(C,e.__scopeTooltip);return(0,p.jsx)(u.C,{present:n||o.open,children:o.disableHoverableContent?(0,p.jsx)(M,{side:i,...a,ref:t}):(0,p.jsx)(j,{side:i,...a,ref:t})})}),j=n.forwardRef((e,t)=>{let r=P(C,e.__scopeTooltip),i=b(C,e.__scopeTooltip),o=n.useRef(null),s=(0,a.s)(t,o),[l,u]=n.useState(null),{trigger:d,onClose:c}=r,h=o.current,{onPointerInTransitChange:f}=i,m=n.useCallback(()=>{u(null),f(!1)},[f]),g=n.useCallback((e,t)=>{let r=e.currentTarget,n={x:e.clientX,y:e.clientY},i=function(e,t){let r=Math.abs(t.top-e.y),n=Math.abs(t.bottom-e.y),i=Math.abs(t.right-e.x),a=Math.abs(t.left-e.x);switch(Math.min(r,n,i,a)){case a:return"left";case i:return"right";case r:return"top";case n:return"bottom";default:throw Error("unreachable")}}(n,r.getBoundingClientRect());u(function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:1*!!(e.y>t.y)),function(e){if(e.length<=1)return e.slice();let t=[];for(let r=0;r<e.length;r++){let n=e[r];for(;t.length>=2;){let e=t[t.length-1],r=t[t.length-2];if((e.x-r.x)*(n.y-r.y)>=(e.y-r.y)*(n.x-r.x))t.pop();else break}t.push(n)}t.pop();let r=[];for(let t=e.length-1;t>=0;t--){let n=e[t];for(;r.length>=2;){let e=r[r.length-1],t=r[r.length-2];if((e.x-t.x)*(n.y-t.y)>=(e.y-t.y)*(n.x-t.x))r.pop();else break}r.push(n)}return(r.pop(),1===t.length&&1===r.length&&t[0].x===r[0].x&&t[0].y===r[0].y)?t:t.concat(r)}(t)}([...function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5,n=[];switch(t){case"top":n.push({x:e.x-r,y:e.y+r},{x:e.x+r,y:e.y+r});break;case"bottom":n.push({x:e.x-r,y:e.y-r},{x:e.x+r,y:e.y-r});break;case"left":n.push({x:e.x+r,y:e.y-r},{x:e.x+r,y:e.y+r});break;case"right":n.push({x:e.x-r,y:e.y-r},{x:e.x-r,y:e.y+r})}return n}(n,i),...function(e){let{top:t,right:r,bottom:n,left:i}=e;return[{x:i,y:t},{x:r,y:t},{x:r,y:n},{x:i,y:n}]}(t.getBoundingClientRect())])),f(!0)},[f]);return n.useEffect(()=>()=>m(),[m]),n.useEffect(()=>{if(d&&h){let e=e=>g(e,h),t=e=>g(e,d);return d.addEventListener("pointerleave",e),h.addEventListener("pointerleave",t),()=>{d.removeEventListener("pointerleave",e),h.removeEventListener("pointerleave",t)}}},[d,h,g,m]),n.useEffect(()=>{if(l){let e=e=>{let t=e.target,r={x:e.clientX,y:e.clientY},n=(null==d?void 0:d.contains(t))||(null==h?void 0:h.contains(t)),i=!function(e,t){let{x:r,y:n}=e,i=!1;for(let e=0,a=t.length-1;e<t.length;a=e++){let o=t[e],s=t[a],l=o.x,u=o.y,d=s.x,c=s.y;u>n!=c>n&&r<(d-l)*(n-u)/(c-u)+l&&(i=!i)}return i}(r,l);n?m():i&&(m(),c())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[d,h,l,c,m]),(0,p.jsx)(M,{...e,ref:s})}),[T,A]=f(E,{isInside:!1}),I=(0,c.Dc)("TooltipContent"),M=n.forwardRef((e,t)=>{let{__scopeTooltip:r,children:i,"aria-label":a,onEscapeKeyDown:o,onPointerDownOutside:u,...d}=e,c=P(C,r),f=g(r),{onClose:m}=c;return n.useEffect(()=>(document.addEventListener(_,m),()=>document.removeEventListener(_,m)),[m]),n.useEffect(()=>{if(c.trigger){let e=e=>{let t=e.target;(null==t?void 0:t.contains(c.trigger))&&m()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[c.trigger,m]),(0,p.jsx)(s.qW,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:o,onPointerDownOutside:u,onFocusOutside:e=>e.preventDefault(),onDismiss:m,children:(0,p.jsxs)(l.UC,{"data-state":c.stateAttribute,...f,...d,ref:t,style:{...d.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,p.jsx)(I,{children:i}),(0,p.jsx)(T,{scope:r,isInside:!0,children:(0,p.jsx)(h.bL,{id:c.contentId,role:"tooltip",children:a||i})})]})})});O.displayName=C;var N="TooltipArrow";n.forwardRef((e,t)=>{let{__scopeTooltip:r,...n}=e,i=g(r);return A(N,r).isInside?null:(0,p.jsx)(l.i3,{...i,...n,ref:t})}).displayName=N;var L=w},9502:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(45707).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},9658:e=>{e.exports={style:{fontFamily:"'GeistMono', ui-monospace, SFMono-Regular, Roboto Mono, Menlo, Monaco, Liberation Mono, DejaVu Sans Mono, Courier New, monospace"},className:"__className_f910ec",variable:"__variable_f910ec"}},11275:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addLocale",{enumerable:!0,get:function(){return a}});let n=r(55113),i=r(28552);function a(e,t,r,a){if(!t||t===r)return e;let o=e.toLowerCase();return!a&&((0,i.pathHasPrefix)(o,"/api")||(0,i.pathHasPrefix)(o,"/"+t.toLowerCase()))?e:(0,n.addPathPrefix)(e,"/"+t)}},14110:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ACTION_SUFFIX:function(){return c},APP_DIR_ALIAS:function(){return T},CACHE_ONE_YEAR:function(){return k},DOT_NEXT_ALIAS:function(){return O},ESLINT_DEFAULT_DIRS:function(){return q},GSP_NO_RETURNED_VALUE:function(){return K},GSSP_COMPONENT_MEMBER_ERROR:function(){return $},GSSP_NO_RETURNED_VALUE:function(){return V},INFINITE_CACHE:function(){return P},INSTRUMENTATION_HOOK_FILENAME:function(){return R},MATCHED_PATH_HEADER:function(){return i},MIDDLEWARE_FILENAME:function(){return x},MIDDLEWARE_LOCATION_REGEXP:function(){return S},NEXT_BODY_SUFFIX:function(){return f},NEXT_CACHE_IMPLICIT_TAG_ID:function(){return E},NEXT_CACHE_REVALIDATED_TAGS_HEADER:function(){return g},NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER:function(){return v},NEXT_CACHE_SOFT_TAG_MAX_LENGTH:function(){return w},NEXT_CACHE_TAGS_HEADER:function(){return m},NEXT_CACHE_TAG_MAX_ITEMS:function(){return y},NEXT_CACHE_TAG_MAX_LENGTH:function(){return b},NEXT_DATA_SUFFIX:function(){return h},NEXT_INTERCEPTION_MARKER_PREFIX:function(){return n},NEXT_META_SUFFIX:function(){return p},NEXT_QUERY_PARAM_PREFIX:function(){return r},NEXT_RESUME_HEADER:function(){return _},NON_STANDARD_NODE_ENV:function(){return X},PAGES_DIR_ALIAS:function(){return C},PRERENDER_REVALIDATE_HEADER:function(){return a},PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER:function(){return o},PUBLIC_DIR_MIDDLEWARE_CONFLICT:function(){return D},ROOT_DIR_ALIAS:function(){return j},RSC_ACTION_CLIENT_WRAPPER_ALIAS:function(){return U},RSC_ACTION_ENCRYPTION_ALIAS:function(){return L},RSC_ACTION_PROXY_ALIAS:function(){return M},RSC_ACTION_VALIDATE_ALIAS:function(){return I},RSC_CACHE_WRAPPER_ALIAS:function(){return N},RSC_MOD_REF_PROXY_ALIAS:function(){return A},RSC_PREFETCH_SUFFIX:function(){return s},RSC_SEGMENTS_DIR_SUFFIX:function(){return l},RSC_SEGMENT_SUFFIX:function(){return u},RSC_SUFFIX:function(){return d},SERVER_PROPS_EXPORT_ERROR:function(){return Z},SERVER_PROPS_GET_INIT_PROPS_CONFLICT:function(){return B},SERVER_PROPS_SSG_CONFLICT:function(){return W},SERVER_RUNTIME:function(){return Y},SSG_FALLBACK_EXPORT_ERROR:function(){return G},SSG_GET_INITIAL_PROPS_CONFLICT:function(){return F},STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR:function(){return z},UNSTABLE_REVALIDATE_RENAME_ERROR:function(){return H},WEBPACK_LAYERS:function(){return Q},WEBPACK_RESOURCE_QUERIES:function(){return ee}});let r="nxtP",n="nxtI",i="x-matched-path",a="x-prerender-revalidate",o="x-prerender-revalidate-if-generated",s=".prefetch.rsc",l=".segments",u=".segment.rsc",d=".rsc",c=".action",h=".json",p=".meta",f=".body",m="x-next-cache-tags",g="x-next-revalidated-tags",v="x-next-revalidate-tag-token",_="next-resume",y=128,b=256,w=1024,E="_N_T_",k=31536e3,P=0xfffffffe,x="middleware",S=`(?:src/)?${x}`,R="instrumentation",C="private-next-pages",O="private-dot-next",j="private-next-root-dir",T="private-next-app-dir",A="private-next-rsc-mod-ref-proxy",I="private-next-rsc-action-validate",M="private-next-rsc-server-reference",N="private-next-rsc-cache-wrapper",L="private-next-rsc-action-encryption",U="private-next-rsc-action-client-wrapper",D="You can not have a '_next' folder inside of your public folder. This conflicts with the internal '/_next' route. https://nextjs.org/docs/messages/public-next-folder-conflict",F="You can not use getInitialProps with getStaticProps. To use SSG, please remove your getInitialProps",B="You can not use getInitialProps with getServerSideProps. Please remove getInitialProps.",W="You can not use getStaticProps or getStaticPaths with getServerSideProps. To use SSG, please remove getServerSideProps",z="can not have getInitialProps/getServerSideProps, https://nextjs.org/docs/messages/404-get-initial-props",Z="pages with `getServerSideProps` can not be exported. See more info here: https://nextjs.org/docs/messages/gssp-export",K="Your `getStaticProps` function did not return an object. Did you forget to add a `return`?",V="Your `getServerSideProps` function did not return an object. Did you forget to add a `return`?",H="The `unstable_revalidate` property is available for general use.\nPlease use `revalidate` instead.",$="can not be attached to a page's component and must be exported from the page. See more info here: https://nextjs.org/docs/messages/gssp-component-member",X='You are using a non-standard "NODE_ENV" value in your environment. This creates inconsistencies in the project and is strongly advised against. Read more: https://nextjs.org/docs/messages/non-standard-node-env',G="Pages with `fallback` enabled in `getStaticPaths` can not be exported. See more info here: https://nextjs.org/docs/messages/ssg-fallback-true-export",q=["app","pages","components","lib","src"],Y={edge:"edge",experimentalEdge:"experimental-edge",nodejs:"nodejs"},J={shared:"shared",reactServerComponents:"rsc",serverSideRendering:"ssr",actionBrowser:"action-browser",apiNode:"api-node",apiEdge:"api-edge",middleware:"middleware",instrument:"instrument",edgeAsset:"edge-asset",appPagesBrowser:"app-pages-browser",pagesDirBrowser:"pages-dir-browser",pagesDirEdge:"pages-dir-edge",pagesDirNode:"pages-dir-node"},Q={...J,GROUP:{builtinReact:[J.reactServerComponents,J.actionBrowser],serverOnly:[J.reactServerComponents,J.actionBrowser,J.instrument,J.middleware],neutralTarget:[J.apiNode,J.apiEdge],clientOnly:[J.serverSideRendering,J.appPagesBrowser],bundled:[J.reactServerComponents,J.actionBrowser,J.serverSideRendering,J.appPagesBrowser,J.shared,J.instrument,J.middleware],appPages:[J.reactServerComponents,J.serverSideRendering,J.appPagesBrowser,J.actionBrowser]}},ee={edgeSSREntry:"__next_edge_ssr_entry__",metadata:"__next_metadata__",metadataRoute:"__next_metadata_route__",metadataImageMeta:"__next_metadata_image_meta__"}},17087:(e,t,r)=>{"use strict";r.d(t,{H4:()=>P,_V:()=>k,bL:()=>E});var n=r(50628),i=r(48733),a=r(72336),o=r(84268),s=r(64826),l=r(92144);function u(){return()=>{}}var d=r(6024),c="Avatar",[h,p]=(0,i.A)(c),[f,m]=h(c),g=n.forwardRef((e,t)=>{let{__scopeAvatar:r,...i}=e,[a,o]=n.useState("idle");return(0,d.jsx)(f,{scope:r,imageLoadingStatus:a,onImageLoadingStatusChange:o,children:(0,d.jsx)(s.sG.span,{...i,ref:t})})});g.displayName=c;var v="AvatarImage",_=n.forwardRef((e,t)=>{let{__scopeAvatar:r,src:i,onLoadingStatusChange:c=()=>{},...h}=e,p=m(v,r),f=function(e,t){let{referrerPolicy:r,crossOrigin:i}=t,a=(0,l.useSyncExternalStore)(u,()=>!0,()=>!1),s=n.useRef(null),d=a?(s.current||(s.current=new window.Image),s.current):null,[c,h]=n.useState(()=>w(d,e));return(0,o.N)(()=>{h(w(d,e))},[d,e]),(0,o.N)(()=>{let e=e=>()=>{h(e)};if(!d)return;let t=e("loaded"),n=e("error");return d.addEventListener("load",t),d.addEventListener("error",n),r&&(d.referrerPolicy=r),"string"==typeof i&&(d.crossOrigin=i),()=>{d.removeEventListener("load",t),d.removeEventListener("error",n)}},[d,i,r]),c}(i,h),g=(0,a.c)(e=>{c(e),p.onImageLoadingStatusChange(e)});return(0,o.N)(()=>{"idle"!==f&&g(f)},[f,g]),"loaded"===f?(0,d.jsx)(s.sG.img,{...h,ref:t,src:i}):null});_.displayName=v;var y="AvatarFallback",b=n.forwardRef((e,t)=>{let{__scopeAvatar:r,delayMs:i,...a}=e,o=m(y,r),[l,u]=n.useState(void 0===i);return n.useEffect(()=>{if(void 0!==i){let e=window.setTimeout(()=>u(!0),i);return()=>window.clearTimeout(e)}},[i]),l&&"loaded"!==o.imageLoadingStatus?(0,d.jsx)(s.sG.span,{...a,ref:t}):null});function w(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}b.displayName=y;var E=g,k=_,P=b},17115:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getSortedRouteObjects:function(){return i},getSortedRoutes:function(){return n}});class r{insert(e){this._insert(e.split("/").filter(Boolean),[],!1)}smoosh(){return this._smoosh()}_smoosh(e){void 0===e&&(e="/");let t=[...this.children.keys()].sort();null!==this.slugName&&t.splice(t.indexOf("[]"),1),null!==this.restSlugName&&t.splice(t.indexOf("[...]"),1),null!==this.optionalRestSlugName&&t.splice(t.indexOf("[[...]]"),1);let r=t.map(t=>this.children.get(t)._smoosh(""+e+t+"/")).reduce((e,t)=>[...e,...t],[]);if(null!==this.slugName&&r.push(...this.children.get("[]")._smoosh(e+"["+this.slugName+"]/")),!this.placeholder){let t="/"===e?"/":e.slice(0,-1);if(null!=this.optionalRestSlugName)throw Object.defineProperty(Error('You cannot define a route with the same specificity as a optional catch-all route ("'+t+'" and "'+t+"[[..."+this.optionalRestSlugName+']]").'),"__NEXT_ERROR_CODE",{value:"E458",enumerable:!1,configurable:!0});r.unshift(t)}return null!==this.restSlugName&&r.push(...this.children.get("[...]")._smoosh(e+"[..."+this.restSlugName+"]/")),null!==this.optionalRestSlugName&&r.push(...this.children.get("[[...]]")._smoosh(e+"[[..."+this.optionalRestSlugName+"]]/")),r}_insert(e,t,n){if(0===e.length){this.placeholder=!1;return}if(n)throw Object.defineProperty(Error("Catch-all must be the last part of the URL."),"__NEXT_ERROR_CODE",{value:"E392",enumerable:!1,configurable:!0});let i=e[0];if(i.startsWith("[")&&i.endsWith("]")){let r=i.slice(1,-1),o=!1;if(r.startsWith("[")&&r.endsWith("]")&&(r=r.slice(1,-1),o=!0),r.startsWith("…"))throw Object.defineProperty(Error("Detected a three-dot character ('…') at ('"+r+"'). Did you mean ('...')?"),"__NEXT_ERROR_CODE",{value:"E147",enumerable:!1,configurable:!0});if(r.startsWith("...")&&(r=r.substring(3),n=!0),r.startsWith("[")||r.endsWith("]"))throw Object.defineProperty(Error("Segment names may not start or end with extra brackets ('"+r+"')."),"__NEXT_ERROR_CODE",{value:"E421",enumerable:!1,configurable:!0});if(r.startsWith("."))throw Object.defineProperty(Error("Segment names may not start with erroneous periods ('"+r+"')."),"__NEXT_ERROR_CODE",{value:"E288",enumerable:!1,configurable:!0});function a(e,r){if(null!==e&&e!==r)throw Object.defineProperty(Error("You cannot use different slug names for the same dynamic path ('"+e+"' !== '"+r+"')."),"__NEXT_ERROR_CODE",{value:"E337",enumerable:!1,configurable:!0});t.forEach(e=>{if(e===r)throw Object.defineProperty(Error('You cannot have the same slug name "'+r+'" repeat within a single dynamic path'),"__NEXT_ERROR_CODE",{value:"E247",enumerable:!1,configurable:!0});if(e.replace(/\W/g,"")===i.replace(/\W/g,""))throw Object.defineProperty(Error('You cannot have the slug names "'+e+'" and "'+r+'" differ only by non-word symbols within a single dynamic path'),"__NEXT_ERROR_CODE",{value:"E499",enumerable:!1,configurable:!0})}),t.push(r)}if(n)if(o){if(null!=this.restSlugName)throw Object.defineProperty(Error('You cannot use both an required and optional catch-all route at the same level ("[...'+this.restSlugName+']" and "'+e[0]+'" ).'),"__NEXT_ERROR_CODE",{value:"E299",enumerable:!1,configurable:!0});a(this.optionalRestSlugName,r),this.optionalRestSlugName=r,i="[[...]]"}else{if(null!=this.optionalRestSlugName)throw Object.defineProperty(Error('You cannot use both an optional and required catch-all route at the same level ("[[...'+this.optionalRestSlugName+']]" and "'+e[0]+'").'),"__NEXT_ERROR_CODE",{value:"E300",enumerable:!1,configurable:!0});a(this.restSlugName,r),this.restSlugName=r,i="[...]"}else{if(o)throw Object.defineProperty(Error('Optional route parameters are not yet supported ("'+e[0]+'").'),"__NEXT_ERROR_CODE",{value:"E435",enumerable:!1,configurable:!0});a(this.slugName,r),this.slugName=r,i="[]"}}this.children.has(i)||this.children.set(i,new r),this.children.get(i)._insert(e.slice(1),t,n)}constructor(){this.placeholder=!0,this.children=new Map,this.slugName=null,this.restSlugName=null,this.optionalRestSlugName=null}}function n(e){let t=new r;return e.forEach(e=>t.insert(e)),t.smoosh()}function i(e,t){let r={},i=[];for(let n=0;n<e.length;n++){let a=t(e[n]);r[a]=n,i[n]=a}return n(i).map(t=>e[r[t]])}},19271:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.experimental__simple=void 0,t.experimental__simple=(0,r(79715).experimental_createTheme)({simpleStyles:!0})},19910:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseUrl",{enumerable:!0,get:function(){return a}});let n=r(78756),i=r(95765);function a(e){if(e.startsWith("/"))return(0,i.parseRelativeUrl)(e);let t=new URL(e);return{hash:t.hash,hostname:t.hostname,href:t.href,pathname:t.pathname,port:t.port,protocol:t.protocol,query:(0,n.searchParamsToUrlQuery)(t.searchParams),search:t.search}}},19921:(e,t,r)=>{"use strict";r.d(t,{so:()=>o});var n=r(87219),i=r(50628),a=(0,i.createContext)({client:n.Ay});function o(e){var t=e.children,r=e.client,o=e.apiKey,s=e.options,l=(0,i.useRef)(null),u=(0,i.useMemo)(function(){return r?(o&&console.warn("[PostHog.js] You have provided both `client` and `apiKey` to `PostHogProvider`. `apiKey` will be ignored in favour of `client`."),s&&console.warn("[PostHog.js] You have provided both `client` and `options` to `PostHogProvider`. `options` will be ignored in favour of `client`."),r):(o||console.warn("[PostHog.js] No `apiKey` or `client` were provided to `PostHogProvider`. Using default global `window.posthog` instance. You must initialize it manually. This is not recommended behavior."),n.Ay)},[r,o,JSON.stringify(s)]);return(0,i.useEffect)(function(){if(!r){var e=l.current;e?(o!==e.apiKey&&console.warn("[PostHog.js] You have provided a different `apiKey` to `PostHogProvider` than the one that was already initialized. This is not supported by our provider and we'll keep using the previous key. If you need to toggle between API Keys you need to control the `client` yourself and pass it in as a prop rather than an `apiKey` prop."),s&&!function e(t,r,n){if(void 0===n&&(n=new WeakMap),t===r)return!0;if("object"!=typeof t||null===t||"object"!=typeof r||null===r)return!1;if(n.has(t)&&n.get(t)===r)return!0;n.set(t,r);var i=Object.keys(t),a=Object.keys(r);if(i.length!==a.length)return!1;for(var o=0;o<i.length;o++){var s=i[o];if(!a.includes(s)||!e(t[s],r[s],n))return!1}return!0}(s,e.options)&&n.Ay.set_config(s)):(n.Ay.__loaded&&console.warn("[PostHog.js] `posthog` was already loaded elsewhere. This may cause issues."),n.Ay.init(o,s)),l.current={apiKey:o,options:null!=s?s:{}}}},[r,o,JSON.stringify(s)]),i.createElement(a.Provider,{value:{client:u}},t)}var s=function(){return useContext(a).client},l=function(e,t){return(l=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)},u=function(){return(u=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var i in t=arguments[r])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)};function d(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var i=0,n=Object.getOwnPropertySymbols(e);i<n.length;i++)0>t.indexOf(n[i])&&Object.prototype.propertyIsEnumerable.call(e,n[i])&&(r[n[i]]=e[n[i]]);return r}var c=function(e){return"function"==typeof e};function h(e){var t=e.flag,r=e.children,n=e.onIntersect,i=e.onClick,a=e.trackView,o=e.options,l=d(e,["flag","children","onIntersect","onClick","trackView","options"]),c=useRef(null);return useEffect(function(){if(null!==c.current&&a){var e=new IntersectionObserver(function(e){return n(e[0])},u({threshold:.1},o));return e.observe(c.current),function(){return e.disconnect()}}},[t,o,s(),c,a,n]),React.createElement("div",u({ref:c},l,{onClick:i}),r)}var p={componentStack:null,error:null},f={INVALID_FALLBACK:"[PostHog.js][PostHogErrorBoundary] Invalid fallback prop, provide a valid React element or a function that returns a valid React element."};!function(e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function t(){this.constructor=r}function r(t){var r=e.call(this,t)||this;return r.state=p,r}l(r,e),r.prototype=null===e?Object.create(e):(t.prototype=e.prototype,new t),r.prototype.componentDidCatch=function(e,t){var r,n=t.componentStack,i=this.props.additionalProperties;this.setState({error:e,componentStack:n}),c(i)?r=i(e):"object"==typeof i&&(r=i),this.context.client.captureException(e,r)},r.prototype.render=function(){var e=this.props,t=e.children,r=e.fallback,n=this.state;if(null==n.componentStack)return c(t)?t():t;var a=c(r)?i.createElement(r,{error:n.error,componentStack:n.componentStack}):r;return i.isValidElement(a)?a:(console.warn(f.INVALID_FALLBACK),i.createElement(i.Fragment,null))},r.contextType=a}(i.Component)},24218:(e,t)=>{"use strict";let r;function n(e){var t;return(null==(t=function(){if(void 0===r){var e;r=(null==(e=window.trustedTypes)?void 0:e.createPolicy("nextjs",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e}))||null}return r}())?void 0:t.createScriptURL(e))||e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"__unsafeCreateTrustedScriptURL",{enumerable:!0,get:function(){return n}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},25084:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return l}});let n=r(6024),i=r(50628),a=r(5659);function o(e){return{default:e&&"default"in e?e.default:e}}r(60006);let s={loader:()=>Promise.resolve(o(()=>null)),loading:null,ssr:!0},l=function(e){let t={...s,...e},r=(0,i.lazy)(()=>t.loader().then(o)),l=t.loading;function u(e){let o=l?(0,n.jsx)(l,{isLoading:!0,pastDelay:!0,error:null}):null,s=!t.ssr||!!t.loading,u=s?i.Suspense:i.Fragment,d=t.ssr?(0,n.jsxs)(n.Fragment,{children:[null,(0,n.jsx)(r,{...e})]}):(0,n.jsx)(a.BailoutToCSR,{reason:"next/dynamic",children:(0,n.jsx)(r,{...e})});return(0,n.jsx)(u,{...s?{fallback:o}:{},children:d})}return u.displayName="LoadableComponent",u}},25451:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(45707).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},27062:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{Router:function(){return a.default},createRouter:function(){return m},default:function(){return p},makePublicRouterInstance:function(){return g},useRouter:function(){return f},withRouter:function(){return l.default}});let n=r(53854),i=n._(r(50628)),a=n._(r(33896)),o=r(61360),s=n._(r(51546)),l=n._(r(32143)),u={router:null,readyCallbacks:[],ready(e){if(this.router)return e();this.readyCallbacks.push(e)}},d=["pathname","route","query","asPath","components","isFallback","basePath","locale","locales","defaultLocale","isReady","isPreview","isLocaleDomain","domainLocales"],c=["push","replace","reload","back","prefetch","beforePopState"];function h(){if(!u.router)throw Object.defineProperty(Error('No router instance found.\nYou should only use "next/router" on the client side of your app.\n'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return u.router}Object.defineProperty(u,"events",{get:()=>a.default.events}),d.forEach(e=>{Object.defineProperty(u,e,{get:()=>h()[e]})}),c.forEach(e=>{u[e]=function(){for(var t=arguments.length,r=Array(t),n=0;n<t;n++)r[n]=arguments[n];return h()[e](...r)}}),["routeChangeStart","beforeHistoryChange","routeChangeComplete","routeChangeError","hashChangeStart","hashChangeComplete"].forEach(e=>{u.ready(()=>{a.default.events.on(e,function(){for(var t=arguments.length,r=Array(t),n=0;n<t;n++)r[n]=arguments[n];let i="on"+e.charAt(0).toUpperCase()+e.substring(1);if(u[i])try{u[i](...r)}catch(e){console.error("Error when running the Router event: "+i),console.error((0,s.default)(e)?e.message+"\n"+e.stack:e+"")}})})});let p=u;function f(){let e=i.default.useContext(o.RouterContext);if(!e)throw Object.defineProperty(Error("NextRouter was not mounted. https://nextjs.org/docs/messages/next-router-not-mounted"),"__NEXT_ERROR_CODE",{value:"E509",enumerable:!1,configurable:!0});return e}function m(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return u.router=new a.default(...t),u.readyCallbacks.forEach(e=>e()),u.readyCallbacks=[],u.router}function g(e){let t={};for(let r of d){if("object"==typeof e[r]){t[r]=Object.assign(Array.isArray(e[r])?[]:{},e[r]);continue}t[r]=e[r]}return t.events=a.default.events,c.forEach(r=>{t[r]=function(){for(var t=arguments.length,n=Array(t),i=0;i<t;i++)n[i]=arguments[i];return e[r](...n)}}),t}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},29763:(e,t,r)=>{"use strict";let n,i;r.d(t,{lJ:()=>nq});var a,o,s,l,u,d,c,h,p,f,m,g,v,_,y,b,w,E,k,P,x,S,R,C={};r.r(C),r.d(C,{SWRConfig:()=>to,default:()=>ts,mutate:()=>eV,preload:()=>e0,unstable_serialize:()=>tn,useSWRConfig:()=>eQ});var O=r(51702),j=r(50628),T=Object.freeze({InvalidProxyUrlErrorMessage:"The proxyUrl passed to Clerk is invalid. The expected value for proxyUrl is an absolute URL or a relative path with a leading '/'. (key={{url}})",InvalidPublishableKeyErrorMessage:"The publishableKey passed to Clerk is invalid. You can get your Publishable key at https://dashboard.clerk.com/last-active?path=api-keys. (key={{key}})",MissingPublishableKeyErrorMessage:"Missing publishableKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.",MissingSecretKeyErrorMessage:"Missing secretKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.",MissingClerkProvider:"{{source}} can only be used within the <ClerkProvider /> component. Learn more: https://clerk.com/docs/components/clerk-provider"});function A({packageName:e,customMessages:t}){let r=e,n={...T,...t};function i(e,t){if(!t)return`${r}: ${e}`;let n=e;for(let r of e.matchAll(/{{([a-zA-Z0-9-_]+)}}/g)){let e=(t[r[1]]||"").toString();n=n.replace(`{{${r[1]}}}`,e)}return`${r}: ${n}`}return{setPackageName({packageName:e}){return"string"==typeof e&&(r=e),this},setMessages({customMessages:e}){return Object.assign(n,e||{}),this},throwInvalidPublishableKeyError(e){throw Error(i(n.InvalidPublishableKeyErrorMessage,e))},throwInvalidProxyUrl(e){throw Error(i(n.InvalidProxyUrlErrorMessage,e))},throwMissingPublishableKeyError(){throw Error(i(n.MissingPublishableKeyErrorMessage))},throwMissingSecretKeyError(){throw Error(i(n.MissingSecretKeyErrorMessage))},throwMissingClerkProviderError(e){throw Error(i(n.MissingClerkProvider,e))},throw(e){throw Error(i(e))}}}var I=Object.defineProperty,M=Object.getOwnPropertyDescriptor,N=Object.getOwnPropertyNames,L=Object.prototype.hasOwnProperty,U=e=>{throw TypeError(e)},D=(e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let i of N(t))L.call(e,i)||i===r||I(e,i,{get:()=>t[i],enumerable:!(n=M(t,i))||n.enumerable});return e},F=(e,t,r)=>t.has(e)||U("Cannot "+r),B=(e,t,r)=>(F(e,t,"read from private field"),r?r.call(e):t.get(e)),W=(e,t,r,n)=>(F(e,t,"write to private field"),n?n.call(e,r):t.set(e,r),r),z=(e,t,r)=>(F(e,t,"access private method"),r),Z={strict_mfa:{afterMinutes:10,level:"multi_factor"},strict:{afterMinutes:10,level:"second_factor"},moderate:{afterMinutes:60,level:"second_factor"},lax:{afterMinutes:1440,level:"second_factor"}},K=new Set(["first_factor","second_factor","multi_factor"]),V=new Set(["strict_mfa","strict","moderate","lax"]),H=e=>"number"==typeof e&&e>0,$=e=>K.has(e),X=e=>V.has(e),G=e=>e.startsWith("org:")?e:`org:${e}`,q=(e,t)=>{let{orgId:r,orgRole:n,orgPermissions:i}=t;return(e.role||e.permission)&&r&&n&&i?e.permission?i.includes(G(e.permission)):e.role?n===G(e.role):null:null},Y=(e,t)=>{let{org:r,user:n}=Q(e),[i,a]=t.split(":"),o=a||i;return"org"===i?r.includes(o):"user"===i?n.includes(o):[...r,...n].includes(o)},J=(e,t)=>{let{features:r,plans:n}=t;return e.feature&&r?Y(r,e.feature):e.plan&&n?Y(n,e.plan):null},Q=e=>{let t=e?e.split(",").map(e=>e.trim()):[];return{org:t.filter(e=>e.split(":")[0].includes("o")).map(e=>e.split(":")[1]),user:t.filter(e=>e.split(":")[0].includes("u")).map(e=>e.split(":")[1])}},ee=e=>{if(!e)return!1;let t="string"==typeof e&&X(e),r="object"==typeof e&&$(e.level)&&H(e.afterMinutes);return(!!t||!!r)&&(e=>"string"==typeof e?Z[e]:e).bind(null,e)},et=(e,{factorVerificationAge:t})=>{if(!e.reverification||!t)return null;let r=ee(e.reverification);if(!r)return null;let{level:n,afterMinutes:i}=r(),[a,o]=t,s=-1!==a?i>a:null,l=-1!==o?i>o:null;switch(n){case"first_factor":return s;case"second_factor":return -1!==o?l:s;case"multi_factor":return -1===o?s:s&&l}},er=e=>t=>{if(!e.userId)return!1;let r=J(t,e),n=q(t,e),i=et(t,e);return[r||n,i].some(e=>null===e)?[r||n,i].some(e=>!0===e):[r||n,i].every(e=>!0===e)},en=({authObject:{sessionId:e,sessionStatus:t,userId:r,actor:n,orgId:i,orgRole:a,orgSlug:o,signOut:s,getToken:l,has:u,sessionClaims:d},options:{treatPendingAsSignedOut:c=!0}})=>void 0===e&&void 0===r?{isLoaded:!1,isSignedIn:void 0,sessionId:e,sessionClaims:void 0,userId:r,actor:void 0,orgId:void 0,orgRole:void 0,orgSlug:void 0,has:void 0,signOut:s,getToken:l}:null===e&&null===r?{isLoaded:!0,isSignedIn:!1,sessionId:e,userId:r,sessionClaims:null,actor:null,orgId:null,orgRole:null,orgSlug:null,has:()=>!1,signOut:s,getToken:l}:c&&"pending"===t?{isLoaded:!0,isSignedIn:!1,sessionId:null,userId:null,sessionClaims:null,actor:null,orgId:null,orgRole:null,orgSlug:null,has:()=>!1,signOut:s,getToken:l}:e&&d&&r&&i&&a?{isLoaded:!0,isSignedIn:!0,sessionId:e,sessionClaims:d,userId:r,actor:n||null,orgId:i,orgRole:a,orgSlug:o||null,has:u,signOut:s,getToken:l}:e&&d&&r&&!i?{isLoaded:!0,isSignedIn:!0,sessionId:e,sessionClaims:d,userId:r,actor:n||null,orgId:null,orgRole:null,orgSlug:null,has:u,signOut:s,getToken:l}:void 0,ei=e=>{let t=r=>{if(!r)return r;if(Array.isArray(r))return r.map(e=>"object"==typeof e||Array.isArray(e)?t(e):e);let n={...r};for(let r of Object.keys(n)){let i=e(r.toString());i!==r&&(n[i]=n[r],delete n[r]),"object"==typeof n[i]&&(n[i]=t(n[i]))}return n};return t};function ea(e){if("boolean"==typeof e)return e;if(null==e)return!1;if("string"==typeof e){if("true"===e.toLowerCase())return!0;if("false"===e.toLowerCase())return!1}let t=parseInt(e,10);return!isNaN(t)&&t>0}ei(function(e){return e?e.replace(/[A-Z]/g,e=>`_${e.toLowerCase()}`):""}),ei(function(e){return e?e.replace(/([-_][a-z])/g,e=>e.toUpperCase().replace(/-|_/,"")):""}),r(37811),a=new WeakMap,o=new WeakMap,s=new WeakSet,l=function(e){let{sk:t,pk:r,payload:n,...i}=e,a={...n,...i};return JSON.stringify(Object.keys({...n,...i}).sort().map(e=>a[e]))},u=function(){let e=localStorage.getItem(B(this,a));return e?JSON.parse(e):{}},d=function(){if("undefined"==typeof window)return!1;let e=window.localStorage;if(!e)return!1;try{let t="test";return e.setItem(t,t),e.removeItem(t),!0}catch(t){return t instanceof DOMException&&("QuotaExceededError"===t.name||"NS_ERROR_DOM_QUOTA_REACHED"===t.name)&&e.length>0&&e.removeItem(B(this,a)),!1}};c=new WeakMap,h=new WeakMap,p=new WeakMap,f=new WeakMap,m=new WeakMap,g=new WeakSet,v=function(e,t){let r=Math.random();return!!(r<=B(this,c).samplingRate&&(void 0===t||r<=t))&&!B(this,h).isEventThrottled(e)},_=function(){fetch(new URL("/v1/event",B(this,c).endpoint),{method:"POST",body:JSON.stringify({events:B(this,f)}),headers:{"Content-Type":"application/json"}}).catch(()=>void 0).then(()=>{W(this,f,[])}).catch(()=>void 0)},y=function(){let e={name:B(this,p).sdk,version:B(this,p).sdkVersion};return"undefined"!=typeof window&&window.Clerk&&(e={...e,...window.Clerk.constructor.sdkMetadata}),e};var eo=r(71885),es=Object.prototype.hasOwnProperty;let el=new WeakMap,eu=()=>{},ed=eu(),ec=Object,eh=e=>e===ed,ep=e=>"function"==typeof e,ef=(e,t)=>({...e,...t}),em=e=>ep(e.then),eg={},ev={},e_="undefined",ey=typeof window!=e_,eb=typeof document!=e_,ew=ey&&"Deno"in window,eE=()=>ey&&typeof window.requestAnimationFrame!=e_,ek=(e,t)=>{let r=el.get(e);return[()=>!eh(t)&&e.get(t)||eg,n=>{if(!eh(t)){let i=e.get(t);t in ev||(ev[t]=i),r[5](t,ef(i,n),i||eg)}},r[6],()=>!eh(t)&&t in ev?ev[t]:!eh(t)&&e.get(t)||eg]},eP=!0,[ex,eS]=ey&&window.addEventListener?[window.addEventListener.bind(window),window.removeEventListener.bind(window)]:[eu,eu],eR={initFocus:e=>(eb&&document.addEventListener("visibilitychange",e),ex("focus",e),()=>{eb&&document.removeEventListener("visibilitychange",e),eS("focus",e)}),initReconnect:e=>{let t=()=>{eP=!0,e()},r=()=>{eP=!1};return ex("online",t),ex("offline",r),()=>{eS("online",t),eS("offline",r)}}},eC=!j.useId,eO=!ey||ew,ej=e=>eE()?window.requestAnimationFrame(e):setTimeout(e,1),eT=eO?j.useEffect:j.useLayoutEffect,eA="undefined"!=typeof navigator&&navigator.connection,eI=!eO&&eA&&(["slow-2g","2g"].includes(eA.effectiveType)||eA.saveData),eM=new WeakMap,eN=(e,t)=>ec.prototype.toString.call(e)==="[object ".concat(t,"]"),eL=0,eU=e=>{let t,r,n=typeof e,i=eN(e,"Date"),a=eN(e,"RegExp"),o=eN(e,"Object");if(ec(e)!==e||i||a)t=i?e.toJSON():"symbol"==n?e.toString():"string"==n?JSON.stringify(e):""+e;else{if(t=eM.get(e))return t;if(t=++eL+"~",eM.set(e,t),Array.isArray(e)){for(r=0,t="@";r<e.length;r++)t+=eU(e[r])+",";eM.set(e,t)}if(o){t="#";let n=ec.keys(e).sort();for(;!eh(r=n.pop());)eh(e[r])||(t+=r+":"+eU(e[r])+",");eM.set(e,t)}}return t},eD=e=>{if(ep(e))try{e=e()}catch(t){e=""}let t=e;return[e="string"==typeof e?e:(Array.isArray(e)?e.length:e)?eU(e):"",t]},eF=0,eB=()=>++eF;async function eW(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];let[n,i,a,o]=t,s=ef({populateCache:!0,throwOnError:!0},"boolean"==typeof o?{revalidate:o}:o||{}),l=s.populateCache,u=s.rollbackOnError,d=s.optimisticData,c=e=>"function"==typeof u?u(e):!1!==u,h=s.throwOnError;if(ep(i)){let e=[];for(let t of n.keys())!/^\$(inf|sub)\$/.test(t)&&i(n.get(t)._k)&&e.push(t);return Promise.all(e.map(p))}return p(i);async function p(e){let r,[i]=eD(e);if(!i)return;let[o,u]=ek(n,i),[p,f,m,g]=el.get(n),v=()=>{let t=p[i];return(ep(s.revalidate)?s.revalidate(o().data,e):!1!==s.revalidate)&&(delete m[i],delete g[i],t&&t[0])?t[0](2).then(()=>o().data):o().data};if(t.length<3)return v();let _=a,y=eB();f[i]=[y,0];let b=!eh(d),w=o(),E=w.data,k=w._c,P=eh(k)?E:k;if(b&&u({data:d=ep(d)?d(P,E):d,_c:P}),ep(_))try{_=_(P)}catch(e){r=e}if(_&&em(_)){if(_=await _.catch(e=>{r=e}),y!==f[i][0]){if(r)throw r;return _}r&&b&&c(r)&&(l=!0,u({data:P,_c:ed}))}if(l&&!r&&(ep(l)?u({data:l(_,P),error:ed,_c:ed}):u({data:_,error:ed,_c:ed})),f[i][1]=eB(),Promise.resolve(v()).then(()=>{u({_c:ed})}),r){if(h)throw r;return}return _}}let ez=(e,t)=>{for(let r in e)e[r][0]&&e[r][0](t)},eZ=(e,t)=>{if(!el.has(e)){let r=ef(eR,t),n=Object.create(null),i=eW.bind(ed,e),a=eu,o=Object.create(null),s=(e,t)=>{let r=o[e]||[];return o[e]=r,r.push(t),()=>r.splice(r.indexOf(t),1)},l=(t,r,n)=>{e.set(t,r);let i=o[t];if(i)for(let e of i)e(r,n)},u=()=>{if(!el.has(e)&&(el.set(e,[n,Object.create(null),Object.create(null),Object.create(null),i,l,s]),!eO)){let t=r.initFocus(setTimeout.bind(ed,ez.bind(ed,n,0))),i=r.initReconnect(setTimeout.bind(ed,ez.bind(ed,n,1)));a=()=>{t&&t(),i&&i(),el.delete(e)}}};return u(),[e,i,u,a]}return[e,el.get(e)[4]]},[eK,eV]=eZ(new Map),eH=ef({onLoadingSlow:eu,onSuccess:eu,onError:eu,onErrorRetry:(e,t,r,n,i)=>{let a=r.errorRetryCount,o=i.retryCount,s=~~((Math.random()+.5)*(1<<(o<8?o:8)))*r.errorRetryInterval;(eh(a)||!(o>a))&&setTimeout(n,s,i)},onDiscarded:eu,revalidateOnFocus:!0,revalidateOnReconnect:!0,revalidateIfStale:!0,shouldRetryOnError:!0,errorRetryInterval:eI?1e4:5e3,focusThrottleInterval:5e3,dedupingInterval:2e3,loadingTimeout:eI?5e3:3e3,compare:function e(t,r){var n,i;if(t===r)return!0;if(t&&r&&(n=t.constructor)===r.constructor){if(n===Date)return t.getTime()===r.getTime();if(n===RegExp)return t.toString()===r.toString();if(n===Array){if((i=t.length)===r.length)for(;i--&&e(t[i],r[i]););return -1===i}if(!n||"object"==typeof t){for(n in i=0,t)if(es.call(t,n)&&++i&&!es.call(r,n)||!(n in r)||!e(t[n],r[n]))return!1;return Object.keys(r).length===i}}return t!=t&&r!=r},isPaused:()=>!1,cache:eK,mutate:eV,fallback:{}},{isOnline:()=>eP,isVisible:()=>{let e=eb&&document.visibilityState;return eh(e)||"hidden"!==e}}),e$=(e,t)=>{let r=ef(e,t);if(t){let{use:n,fallback:i}=e,{use:a,fallback:o}=t;n&&a&&(r.use=n.concat(a)),i&&o&&(r.fallback=ef(i,o))}return r},eX=(0,j.createContext)({}),eG="$inf$",eq=ey&&window.__SWR_DEVTOOLS_USE__,eY=eq?window.__SWR_DEVTOOLS_USE__:[],eJ=e=>ep(e[1])?[e[0],e[1],e[2]||{}]:[e[0],null,(null===e[1]?e[2]:e[1])||{}],eQ=()=>ef(eH,(0,j.useContext)(eX)),e0=(e,t)=>{let[r,n]=eD(e),[,,,i]=el.get(eK);if(i[r])return i[r];let a=t(n);return i[r]=a,a},e1=eY.concat(e=>(t,r,n)=>{let i=r&&((...e)=>{let[n]=eD(t),[,,,i]=el.get(eK);if(n.startsWith(eG))return r(...e);let a=i[n];return eh(a)?r(...e):(delete i[n],a)});return e(t,i,n)}),e2=(e,t,r)=>{let n=t[e]||(t[e]=[]);return n.push(r),()=>{let e=n.indexOf(r);e>=0&&(n[e]=n[n.length-1],n.pop())}};eq&&(window.__SWR_DEVTOOLS_REACT__=j);let e4=()=>{},e5=e4(),e9=Object,e3=e=>e===e5,e7=e=>"function"==typeof e,e6=new WeakMap,e8=(e,t)=>e9.prototype.toString.call(e)===`[object ${t}]`,te=0,tt=e=>{let t,r,n=typeof e,i=e8(e,"Date"),a=e8(e,"RegExp"),o=e8(e,"Object");if(e9(e)!==e||i||a)t=i?e.toJSON():"symbol"==n?e.toString():"string"==n?JSON.stringify(e):""+e;else{if(t=e6.get(e))return t;if(t=++te+"~",e6.set(e,t),Array.isArray(e)){for(r=0,t="@";r<e.length;r++)t+=tt(e[r])+",";e6.set(e,t)}if(o){t="#";let n=e9.keys(e).sort();for(;!e3(r=n.pop());)e3(e[r])||(t+=r+":"+tt(e[r])+",");e6.set(e,t)}}return t},tr=e=>{if(e7(e))try{e=e()}catch(t){e=""}let t=e;return[e="string"==typeof e?e:(Array.isArray(e)?e.length:e)?tt(e):"",t]},tn=e=>tr(e)[0],ti=j.use||(e=>{switch(e.status){case"pending":throw e;case"fulfilled":return e.value;case"rejected":throw e.reason;default:throw e.status="pending",e.then(t=>{e.status="fulfilled",e.value=t},t=>{e.status="rejected",e.reason=t}),e}}),ta={dedupe:!0},to=ec.defineProperty(e=>{let{value:t}=e,r=(0,j.useContext)(eX),n=ep(t),i=(0,j.useMemo)(()=>n?t(r):t,[n,r,t]),a=(0,j.useMemo)(()=>n?i:e$(r,i),[n,r,i]),o=i&&i.provider,s=(0,j.useRef)(ed);o&&!s.current&&(s.current=eZ(o(a.cache||eK),i));let l=s.current;return l&&(a.cache=l[0],a.mutate=l[1]),eT(()=>{if(l)return l[2]&&l[2](),l[3]},[]),(0,j.createElement)(eX.Provider,ef(e,{value:a}))},"defaultValue",{value:eH}),ts=(n=(e,t,r)=>{let{cache:n,compare:i,suspense:a,fallbackData:o,revalidateOnMount:s,revalidateIfStale:l,refreshInterval:u,refreshWhenHidden:d,refreshWhenOffline:c,keepPreviousData:h}=r,[p,f,m,g]=el.get(n),[v,_]=eD(e),y=(0,j.useRef)(!1),b=(0,j.useRef)(!1),w=(0,j.useRef)(v),E=(0,j.useRef)(t),k=(0,j.useRef)(r),P=()=>k.current,x=()=>P().isVisible()&&P().isOnline(),[S,R,C,O]=ek(n,v),T=(0,j.useRef)({}).current,A=eh(o)?eh(r.fallback)?ed:r.fallback[v]:o,I=(e,t)=>{for(let r in T)if("data"===r){if(!i(e[r],t[r])&&(!eh(e[r])||!i(z,t[r])))return!1}else if(t[r]!==e[r])return!1;return!0},M=(0,j.useMemo)(()=>{let e=!!v&&!!t&&(eh(s)?!P().isPaused()&&!a&&!1!==l:s),r=t=>{let r=ef(t);return(delete r._k,e)?{isValidating:!0,isLoading:!0,...r}:r},n=S(),i=O(),o=r(n),u=n===i?o:r(i),d=o;return[()=>{let e=r(S());return I(e,d)?(d.data=e.data,d.isLoading=e.isLoading,d.isValidating=e.isValidating,d.error=e.error,d):(d=e,e)},()=>u]},[n,v]),N=(0,eo.useSyncExternalStore)((0,j.useCallback)(e=>C(v,(t,r)=>{I(r,t)||e()}),[n,v]),M[0],M[1]),L=!y.current,U=p[v]&&p[v].length>0,D=N.data,F=eh(D)?A&&em(A)?ti(A):A:D,B=N.error,W=(0,j.useRef)(F),z=h?eh(D)?eh(W.current)?F:W.current:D:F,Z=(!U||!!eh(B))&&(L&&!eh(s)?s:!P().isPaused()&&(a?!eh(F)&&l:eh(F)||l)),K=!!(v&&t&&L&&Z),V=eh(N.isValidating)?K:N.isValidating,H=eh(N.isLoading)?K:N.isLoading,$=(0,j.useCallback)(async e=>{let t,n,a=E.current;if(!v||!a||b.current||P().isPaused())return!1;let o=!0,s=e||{},l=!m[v]||!s.dedupe,u=()=>eC?!b.current&&v===w.current&&y.current:v===w.current,d={isValidating:!1,isLoading:!1},c=()=>{R(d)},h=()=>{let e=m[v];e&&e[1]===n&&delete m[v]},g={isValidating:!0};eh(S().data)&&(g.isLoading=!0);try{if(l&&(R(g),r.loadingTimeout&&eh(S().data)&&setTimeout(()=>{o&&u()&&P().onLoadingSlow(v,r)},r.loadingTimeout),m[v]=[a(_),eB()]),[t,n]=m[v],t=await t,l&&setTimeout(h,r.dedupingInterval),!m[v]||m[v][1]!==n)return l&&u()&&P().onDiscarded(v),!1;d.error=ed;let e=f[v];if(!eh(e)&&(n<=e[0]||n<=e[1]||0===e[1]))return c(),l&&u()&&P().onDiscarded(v),!1;let s=S().data;d.data=i(s,t)?s:t,l&&u()&&P().onSuccess(t,v,r)}catch(r){h();let e=P(),{shouldRetryOnError:t}=e;!e.isPaused()&&(d.error=r,l&&u()&&(e.onError(r,v,e),(!0===t||ep(t)&&t(r))&&(!P().revalidateOnFocus||!P().revalidateOnReconnect||x())&&e.onErrorRetry(r,v,e,e=>{let t=p[v];t&&t[0]&&t[0](3,e)},{retryCount:(s.retryCount||0)+1,dedupe:!0})))}return o=!1,c(),!0},[v,n]),X=(0,j.useCallback)((...e)=>eW(n,w.current,...e),[]);if(eT(()=>{E.current=t,k.current=r,eh(D)||(W.current=D)}),eT(()=>{if(!v)return;let e=$.bind(ed,ta),t=0;P().revalidateOnFocus&&(t=Date.now()+P().focusThrottleInterval);let r=e2(v,p,(r,n={})=>{if(0==r){let r=Date.now();P().revalidateOnFocus&&r>t&&x()&&(t=r+P().focusThrottleInterval,e())}else if(1==r)P().revalidateOnReconnect&&x()&&e();else if(2==r)return $();else if(3==r)return $(n)});return b.current=!1,w.current=v,y.current=!0,R({_k:_}),Z&&(eh(F)||eO?e():ej(e)),()=>{b.current=!0,r()}},[v]),eT(()=>{let e;function t(){let t=ep(u)?u(S().data):u;t&&-1!==e&&(e=setTimeout(r,t))}function r(){!S().error&&(d||P().isVisible())&&(c||P().isOnline())?$(ta).then(t):t()}return t(),()=>{e&&(clearTimeout(e),e=-1)}},[u,d,c,v]),(0,j.useDebugValue)(z),a&&eh(F)&&v){if(!eC&&eO)throw Error("Fallback data is required when using Suspense in SSR.");E.current=t,k.current=r,b.current=!1;let e=g[v];if(eh(e)||ti(X(e)),eh(B)){let e=$(ta);eh(z)||(e.status="fulfilled",e.value=!0),ti(e)}else throw B}return{mutate:X,get data(){return T.data=!0,z},get error(){return T.error=!0,B},get isValidating(){return T.isValidating=!0,V},get isLoading(){return T.isLoading=!0,H}}},function(...e){let t=eQ(),[r,i,a]=eJ(e),o=e$(t,a),s=n,{use:l}=o,u=(l||[]).concat(e1);for(let e=u.length;e--;)s=u[e](s);return s(r,i||o.fetcher||null,o)}),tl=()=>{},tu=tl(),td=Object,tc=e=>e===tu,th=e=>"function"==typeof e,tp=new WeakMap,tf=(e,t)=>td.prototype.toString.call(e)===`[object ${t}]`,tm=0,tg=e=>{let t,r,n=typeof e,i=tf(e,"Date"),a=tf(e,"RegExp"),o=tf(e,"Object");if(td(e)!==e||i||a)t=i?e.toJSON():"symbol"==n?e.toString():"string"==n?JSON.stringify(e):""+e;else{if(t=tp.get(e))return t;if(t=++tm+"~",tp.set(e,t),Array.isArray(e)){for(r=0,t="@";r<e.length;r++)t+=tg(e[r])+",";tp.set(e,t)}if(o){t="#";let n=td.keys(e).sort();for(;!tc(r=n.pop());)tc(e[r])||(t+=r+":"+tg(e[r])+",");tp.set(e,t)}}return t},tv=e=>{if(th(e))try{e=e()}catch(t){e=""}let t=e;return[e="string"==typeof e?e:(Array.isArray(e)?e.length:e)?tg(e):"",t]},t_=e=>tv(e?e(0,null):null)[0],ty=Promise.resolve(),tb=(i=e=>(t,r,n)=>{let i,a=(0,j.useRef)(!1),{cache:o,initialSize:s=1,revalidateAll:l=!1,persistSize:u=!1,revalidateFirstPage:d=!0,revalidateOnMount:c=!1,parallel:h=!1}=n,[,,,p]=el.get(eK);try{(i=t_(t))&&(i=eG+i)}catch(e){}let[f,m,g]=ek(o,i),v=(0,j.useCallback)(()=>eh(f()._l)?s:f()._l,[o,i,s]);(0,eo.useSyncExternalStore)((0,j.useCallback)(e=>i?g(i,()=>{e()}):()=>{},[o,i]),v,v);let _=(0,j.useCallback)(()=>{let e=f()._l;return eh(e)?s:e},[i,s]),y=(0,j.useRef)(_());eT(()=>{if(!a.current){a.current=!0;return}i&&m({_l:u?y.current:_()})},[i,o]);let b=c&&!a.current,w=e(i,async e=>{let i=f()._i,a=f()._r;m({_r:ed});let s=[],u=_(),[c]=ek(o,e),g=c().data,v=[],y=null;for(let e=0;e<u;++e){let[u,c]=eD(t(e,h?null:y));if(!u)break;let[f,m]=ek(o,u),_=f().data,w=l||i||eh(_)||d&&!e&&!eh(g)||b||g&&!eh(g[e])&&!n.compare(g[e],_);if(r&&("function"==typeof a?a(_,c):w)){let t=async()=>{if(u in p){let e=p[u];delete p[u],_=await e}else _=await r(c);m({data:_,_k:c}),s[e]=_};h?v.push(t):await t()}else s[e]=_;h||(y=_)}return h&&await Promise.all(v.map(e=>e())),m({_i:ed}),s},n),E=(0,j.useCallback)(function(e,t){let r="boolean"==typeof t?{revalidate:t}:t||{},n=!1!==r.revalidate;return i?(n&&(eh(e)?m({_i:!0,_r:r.revalidate}):m({_i:!1,_r:r.revalidate})),arguments.length?w.mutate(e,{...r,revalidate:n}):w.mutate()):ty},[i,o]),k=(0,j.useCallback)(e=>{let r;if(!i)return ty;let[,n]=ek(o,i);if(ep(e)?r=e(_()):"number"==typeof e&&(r=e),"number"!=typeof r)return ty;n({_l:r}),y.current=r;let a=[],[s]=ek(o,i),l=null;for(let e=0;e<r;++e){let[r]=eD(t(e,l)),[n]=ek(o,r),i=r?n().data:ed;if(eh(i))return E(s().data);a.push(i),l=i}return E(a)},[i,o,E,_]);return{size:_(),setSize:k,mutate:E,get data(){return w.data},get error(){return w.error},get isValidating(){return w.isValidating},get isLoading(){return w.isLoading}}},(...e)=>{let[t,r,n]=eJ(e),a=(n.use||[]).concat(i);return ts(t,r,{...n,use:a})});var tw=r(61372);function tE(e,t){if(!e)throw"string"==typeof t?Error(t):Error(`${t.displayName} not found`)}var tk=(e,t)=>{let{assertCtxFn:r=tE}=t||{},n=j.createContext(void 0);return n.displayName=e,[n,()=>{let t=j.useContext(n);return r(t,`${e} not found`),t.value},()=>{let e=j.useContext(n);return e?e.value:{}}]},tP={};((e,t)=>{for(var r in t)I(e,r,{get:t[r],enumerable:!0})})(tP,{useSWR:()=>ts,useSWRInfinite:()=>tb}),((e,t,r)=>(D(e,t,"default"),r&&D(r,t,"default")))(tP,C);var[tx,tS]=tk("ClerkInstanceContext"),[tR,tC]=tk("UserContext"),[tO,tj]=tk("ClientContext"),[tT,tA]=tk("SessionContext"),[tI,tM]=(j.createContext({}),tk("OrganizationContext")),tN=({children:e,organization:t,swrConfig:r})=>j.createElement(tP.SWRConfig,{value:r},j.createElement(tI.Provider,{value:{value:{organization:t}}},e));function tL(e){if(!j.useContext(tx)){if("function"==typeof e)return void e();throw Error(`${e} can only be used within the <ClerkProvider /> component.

Possible fixes:
1. Ensure that the <ClerkProvider /> is correctly wrapping your application where this component is used.
2. Check for multiple versions of the \`@clerk/shared\` package in your project. Use a tool like \`npm ls @clerk/shared\` to identify multiple versions, and update your dependencies to only rely on one.

Learn more: https://clerk.com/docs/components/clerk-provider`.trim())}}function tU(e,t){let r=new Set(Object.keys(t)),n={};for(let t of Object.keys(e))r.has(t)||(n[t]=e[t]);return n}var tD={dedupingInterval:6e4,focusThrottleInterval:12e4},tF=("undefined"!=typeof window?j.useLayoutEffect:j.useEffect,()=>(tL("useClerk"),tS())),tB=tw.j,tW=()=>!1,tz=()=>!1,tZ=()=>{try{return!0}catch{}return!1},tK=new Set,tV=(e,t,r)=>{let n=tz()||tZ(),i=r??e;tK.has(i)||n||(tK.add(i),console.warn(`Clerk - DEPRECATION WARNING: "${e}" is deprecated and will be removed in the next major release.
${t}`))},tH=A({packageName:"@clerk/clerk-react"});function t$(e){tH.setMessages(e).setPackageName(e)}var[tX,tG]=tk("AuthContext"),tq=e=>`You've passed multiple children components to <${e}/>. You can only pass a single child component or text.`,tY="Unsupported usage of isSatellite, domain or proxyUrl. The usage of isSatellite, domain or proxyUrl as function is not supported in non-browser environments.",tJ=e=>`<${e} /> can only accept <${e}.Page /> and <${e}.Link /> as its children. Any other provided component will be ignored. Additionally, please ensure that the component is rendered in a client component.`,tQ=e=>`Missing props. <${e}.Page /> component requires the following props: url, label, labelIcon, alongside with children to be rendered inside the page.`,t0=e=>`Missing props. <${e}.Link /> component requires the following props: url, label and labelIcon.`,t1=e=>{tL(()=>{tH.throwMissingClerkProviderError({source:e})})},t2=e=>new Promise(t=>{let r=n=>{["ready","degraded"].includes(n)&&(t(),e.off("status",r))};e.on("status",r,{notify:!0})}),t4=e=>async t=>(await t2(e),e.session)?e.session.getToken(t):null,t5=e=>async(...t)=>(await t2(e),e.signOut(...t)),t9=(e,t)=>{let r=("string"==typeof t?t:null==t?void 0:t.component)||e.displayName||e.name||"Component";e.displayName=r;let n="string"==typeof t?void 0:t,i=t=>{t1(r||"withClerk");let i=tS();return i.loaded||(null==n?void 0:n.renderWhileLoading)?j.createElement(e,{...t,component:r,clerk:i}):null};return i.displayName=`withClerk(${r})`,i};t9(({clerk:e,...t})=>{let{client:r,session:n}=e,i=r.signedInSessions?r.signedInSessions.length>0:r.activeSessions&&r.activeSessions.length>0;return j.useEffect(()=>{null===n&&i?e.redirectToAfterSignOut():e.redirectToSignIn(t)},[]),null},"RedirectToSignIn"),t9(({clerk:e,...t})=>(j.useEffect(()=>{e.redirectToSignUp(t)},[]),null),"RedirectToSignUp"),t9(({clerk:e})=>(j.useEffect(()=>{tV("RedirectToUserProfile","Use the `redirectToUserProfile()` method instead."),e.redirectToUserProfile()},[]),null),"RedirectToUserProfile"),t9(({clerk:e})=>(j.useEffect(()=>{tV("RedirectToOrganizationProfile","Use the `redirectToOrganizationProfile()` method instead."),e.redirectToOrganizationProfile()},[]),null),"RedirectToOrganizationProfile"),t9(({clerk:e})=>(j.useEffect(()=>{tV("RedirectToCreateOrganization","Use the `redirectToCreateOrganization()` method instead."),e.redirectToCreateOrganization()},[]),null),"RedirectToCreateOrganization"),t9(({clerk:e,...t})=>(j.useEffect(()=>{e.handleRedirectCallback(t)},[]),null),"AuthenticateWithRedirectCallback");var t3=e=>{throw TypeError(e)},t7=(e,t,r)=>t.has(e)||t3("Cannot "+r),t6=(e,t,r)=>(t7(e,t,"read from private field"),r?r.call(e):t.get(e)),t8=(e,t,r)=>t.has(e)?t3("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,r),re=(e,t,r,n)=>(t7(e,t,"write to private field"),n?n.call(e,r):t.set(e,r),r),rt=(e,t,r)=>(t7(e,t,"access private method"),r),rr=(e,t="5.67.3")=>{if(e)return e;let r=rn(t);return r?"snapshot"===r?"5.67.3":r:ri(t)},rn=e=>e.trim().replace(/^v/,"").match(/-(.+?)(\.|$)/)?.[1],ri=e=>e.trim().replace(/^v/,"").split(".")[0];function ra(e){return e.startsWith("/")}var ro=/\/$|\/\?|\/#/,rs={initialDelay:125,maxDelayBetweenRetries:0,factor:2,shouldRetry:(e,t)=>t<5,retryImmediately:!1,jitter:!0},rl=async e=>new Promise(t=>setTimeout(t,e)),ru=(e,t)=>t?e*(1+Math.random()):e,rd=e=>{let t=0,r=()=>{let r=e.initialDelay*Math.pow(e.factor,t);return r=ru(r,e.jitter),Math.min(e.maxDelayBetweenRetries||r,r)};return async()=>{await rl(r()),t++}},rc=async(e,t={})=>{let r=0,{shouldRetry:n,initialDelay:i,maxDelayBetweenRetries:a,factor:o,retryImmediately:s,jitter:l}={...rs,...t},u=rd({initialDelay:i,maxDelayBetweenRetries:a,factor:o,jitter:l});for(;;)try{return await e()}catch(e){if(!n(e,++r))throw e;s&&1===r?await rl(ru(100,l)):await u()}};async function rh(e="",t){let{async:r,defer:n,beforeLoad:i,crossOrigin:a,nonce:o}=t||{};return rc(()=>new Promise((t,s)=>{e||s(Error("loadScript cannot be called without a src")),document&&document.body||s("loadScript cannot be called when document does not exist");let l=document.createElement("script");a&&l.setAttribute("crossorigin",a),l.async=r||!1,l.defer=n||!1,l.addEventListener("load",()=>{l.remove(),t(l)}),l.addEventListener("error",()=>{l.remove(),s()}),l.src=e,l.nonce=o,i?.(l),document.body.appendChild(l)}),{shouldRetry:(e,t)=>t<=5})}var rp=e=>"undefined"!=typeof atob&&"function"==typeof atob?atob(e):"undefined"!=typeof global&&global.Buffer?new global.Buffer(e,"base64").toString():e,rf=[".lcl.dev",".lclstage.dev",".lclclerk.com"],rm=[".lcl.dev",".stg.dev",".lclstage.dev",".stgstage.dev",".dev.lclclerk.com",".stg.lclclerk.com",".accounts.lclclerk.com","accountsstage.dev","accounts.dev"],rg=[".lcl.dev","lclstage.dev",".lclclerk.com",".accounts.lclclerk.com"],rv=[".accountsstage.dev"],r_="pk_live_";function ry(e,t={}){if(!(e=e||"")||!rb(e)){if(t.fatal&&!e)throw Error("Publishable key is missing. Ensure that your publishable key is correctly configured. Double-check your environment configuration for your keys, or access them here: https://dashboard.clerk.com/last-active?path=api-keys");if(t.fatal&&!rb(e))throw Error("Publishable key not valid.");return null}let r=e.startsWith(r_)?"production":"development",n=rp(e.split("_")[2]);return n=n.slice(0,-1),t.proxyUrl?n=t.proxyUrl:"development"!==r&&t.domain&&t.isSatellite&&(n=`clerk.${t.domain}`),{instanceType:r,frontendApi:n}}function rb(e=""){try{let t=e.startsWith(r_)||e.startsWith("pk_test_"),r=rp(e.split("_")[2]||"").endsWith("$");return t&&r}catch{return!1}}var rw="Clerk: Failed to load Clerk",{isDevOrStagingUrl:rE}=function(){let e=new Map;return{isDevOrStagingUrl:t=>{if(!t)return!1;let r="string"==typeof t?t:t.hostname,n=e.get(r);return void 0===n&&(n=rm.some(e=>r.endsWith(e)),e.set(r,n)),n}}}(),rk=A({packageName:"@clerk/shared"});function rP(e){rk.setPackageName({packageName:e})}var rx=async e=>{let t=document.querySelector("script[data-clerk-js-script]");return t?new Promise((e,r)=>{t.addEventListener("load",()=>{e(t)}),t.addEventListener("error",()=>{r(rw)})}):e?.publishableKey?rh(rS(e),{async:!0,crossOrigin:"anonymous",nonce:e.nonce,beforeLoad:rC(e)}).catch(()=>{throw Error(rw)}):void rk.throwMissingPublishableKeyError()},rS=e=>{let{clerkJSUrl:t,clerkJSVariant:r,clerkJSVersion:n,proxyUrl:i,domain:a,publishableKey:o}=e;if(t)return t;let s="";s=i&&function(e){var t;return!e||(t=e,/^http(s)?:\/\//.test(t||""))||ra(e)}(i)?(function(e){return e?ra(e)?new URL(e,window.location.origin).toString():e:""})(i).replace(/http(s)?:\/\//,""):a&&!rE(ry(o)?.frontendApi||"")?function(e){let t;if(!e)return"";if(e.match(/^(clerk\.)+\w*$/))t=/(clerk\.)*(?=clerk\.)/;else{if(e.match(/\.clerk.accounts/))return e;t=/^(clerk\.)*/gi}let r=e.replace(t,"");return`clerk.${r}`}(a):ry(o)?.frontendApi||"";let l=r?`${r.replace(/\.+$/,"")}.`:"",u=rr(n);return`https://${s}/npm/@clerk/clerk-js@${u}/dist/clerk.${l}browser.js`},rR=e=>{let t={};return e.publishableKey&&(t["data-clerk-publishable-key"]=e.publishableKey),e.proxyUrl&&(t["data-clerk-proxy-url"]=e.proxyUrl),e.domain&&(t["data-clerk-domain"]=e.domain),e.nonce&&(t.nonce=e.nonce),t},rC=e=>t=>{let r=rR(e);for(let e in r)t.setAttribute(e,r[e])},rO=e=>{tW()&&console.error(`Clerk: ${e}`)};function rj(e,t,r){return"function"==typeof e?e(t):void 0!==e?e:void 0!==r?r:void 0}var rT=r(6341),rA=(e,...t)=>{let r={...e};for(let e of t)delete r[e];return r},rI=(e,t,r)=>!e&&r?rM(r):rN(t),rM=e=>{let t=e.userId,r=e.user,n=e.sessionId,i=e.sessionStatus,a=e.sessionClaims,o=e.session,s=e.organization,l=e.orgId,u=e.orgRole,d=e.orgPermissions,c=e.orgSlug;return{userId:t,user:r,sessionId:n,session:o,sessionStatus:i,sessionClaims:a,organization:s,orgId:l,orgRole:u,orgPermissions:d,orgSlug:c,actor:e.actor,factorVerificationAge:e.factorVerificationAge}},rN=e=>{let t=e.user?e.user.id:e.user,r=e.user,n=e.session?e.session.id:e.session,i=e.session,a=e.session?.status,o=e.session?e.session.lastActiveToken?.jwt?.claims:null,s=e.session?e.session.factorVerificationAge:null,l=i?.actor,u=e.organization,d=e.organization?e.organization.id:e.organization,c=u?.slug,h=u?r?.organizationMemberships?.find(e=>e.organization.id===d):u,p=h?h.permissions:h;return{userId:t,user:r,sessionId:n,session:i,sessionStatus:a,sessionClaims:o,organization:u,orgId:d,orgRole:h?h.role:h,orgSlug:c,orgPermissions:p,actor:l,factorVerificationAge:s}};function rL(){return"undefined"!=typeof window}RegExp("bot|spider|crawl|APIs-Google|AdsBot|Googlebot|mediapartners|Google Favicon|FeedFetcher|Google-Read-Aloud|DuplexWeb-Google|googleweblight|bing|yandex|baidu|duckduck|yahoo|ecosia|ia_archiver|facebook|instagram|pinterest|reddit|slack|twitter|whatsapp|youtube|semrush","i");var rU=(e,t,r,n,i)=>{let{notify:a}=i||{},o=e.get(r);o||(o=[],e.set(r,o)),o.push(n),a&&t.has(r)&&n(t.get(r))},rD=(e,t,r)=>(e.get(t)||[]).map(e=>e(r)),rF=(e,t,r)=>{let n=e.get(t);n&&(r?n.splice(n.indexOf(r)>>>0,1):e.set(t,[]))},rB=()=>{let e=new Map,t=new Map,r=new Map;return{on:(...r)=>rU(e,t,...r),prioritizedOn:(...e)=>rU(r,t,...e),emit:(n,i)=>{t.set(n,i),rD(r,n,i),rD(e,n,i)},off:(...t)=>rF(e,...t),prioritizedOff:(...e)=>rF(r,...e),internal:{retrieveListeners:t=>e.get(t)||[]}}},rW={Status:"status"},rz=()=>rB();"undefined"==typeof window||window.global||(window.global="undefined"==typeof global?window:global);var rZ=e=>t=>{try{return j.Children.only(e)}catch{return tH.throw(tq(t))}},rK=(e,t)=>(e||(e=t),"string"==typeof e&&(e=j.createElement("button",null,e)),e),rV=e=>(...t)=>{if(e&&"function"==typeof e)return e(...t)},rH=new Map,r$=e=>{let t=Array(e.length).fill(null),[r,n]=(0,j.useState)(t);return e.map((e,t)=>({id:e.id,mount:e=>n(r=>r.map((r,n)=>n===t?e:r)),unmount:()=>n(e=>e.map((e,r)=>r===t?null:e)),portal:()=>j.createElement(j.Fragment,null,r[t]?(0,rT.createPortal)(e.component,r[t]):null)}))},rX=(e,t)=>!!e&&j.isValidElement(e)&&(null==e?void 0:e.type)===t,rG=(e,t)=>rJ({children:e,reorderItemsLabels:["account","security"],LinkComponent:ni,PageComponent:nn,MenuItemsComponent:no,componentName:"UserProfile"},t),rq=(e,t)=>rJ({children:e,reorderItemsLabels:["general","members"],LinkComponent:nd,PageComponent:nu,componentName:"OrganizationProfile"},t),rY=e=>{let t=[],r=[nd,nu,no,nn,ni];return j.Children.forEach(e,e=>{r.some(t=>rX(e,t))||t.push(e)}),t},rJ=(e,t)=>{let{children:r,LinkComponent:n,PageComponent:i,MenuItemsComponent:a,reorderItemsLabels:o,componentName:s}=e,{allowForAnyChildren:l=!1}=t||{},u=[];j.Children.forEach(r,e=>{if(!rX(e,i)&&!rX(e,n)&&!rX(e,a)){e&&!l&&rO(tJ(s));return}let{props:t}=e,{children:r,label:d,url:c,labelIcon:h}=t;if(rX(e,i))if(rQ(t,o))u.push({label:d});else{if(!r0(t))return void rO(tQ(s));u.push({label:d,labelIcon:h,children:r,url:c})}if(rX(e,n))if(!r1(t))return void rO(t0(s));else u.push({label:d,labelIcon:h,url:c})});let d=[],c=[],h=[];u.forEach((e,t)=>{if(r0(e)){d.push({component:e.children,id:t}),c.push({component:e.labelIcon,id:t});return}r1(e)&&h.push({component:e.labelIcon,id:t})});let p=r$(d),f=r$(c),m=r$(h),g=[],v=[];return u.forEach((e,t)=>{if(rQ(e,o))return void g.push({label:e.label});if(r0(e)){let{portal:r,mount:n,unmount:i}=p.find(e=>e.id===t),{portal:a,mount:o,unmount:s}=f.find(e=>e.id===t);g.push({label:e.label,url:e.url,mount:n,unmount:i,mountIcon:o,unmountIcon:s}),v.push(r),v.push(a);return}if(r1(e)){let{portal:r,mount:n,unmount:i}=m.find(e=>e.id===t);g.push({label:e.label,url:e.url,mountIcon:n,unmountIcon:i}),v.push(r);return}}),{customPages:g,customPagesPortals:v}},rQ=(e,t)=>{let{children:r,label:n,url:i,labelIcon:a}=e;return!r&&!i&&!a&&t.some(e=>e===n)},r0=e=>{let{children:t,label:r,url:n,labelIcon:i}=e;return!!t&&!!n&&!!i&&!!r},r1=e=>{let{children:t,label:r,url:n,labelIcon:i}=e;return!t&&!!n&&!!i&&!!r},r2=e=>r4({children:e,reorderItemsLabels:["manageAccount","signOut"],MenuItemsComponent:no,MenuActionComponent:ns,MenuLinkComponent:nl,UserProfileLinkComponent:ni,UserProfilePageComponent:nn}),r4=({children:e,MenuItemsComponent:t,MenuActionComponent:r,MenuLinkComponent:n,UserProfileLinkComponent:i,UserProfilePageComponent:a,reorderItemsLabels:o})=>{let s=[],l=[],u=[];j.Children.forEach(e,e=>{if(!rX(e,t)&&!rX(e,i)&&!rX(e,a)){e&&rO("<UserButton /> can only accept <UserButton.UserProfilePage />, <UserButton.UserProfileLink /> and <UserButton.MenuItems /> as its children. Any other provided component will be ignored. Additionally, please ensure that the component is rendered in a client component.");return}if(rX(e,i)||rX(e,a))return;let{props:l}=e;j.Children.forEach(l.children,e=>{if(!rX(e,r)&&!rX(e,n)){e&&rO("<UserButton.MenuItems /> component can only accept <UserButton.Action /> and <UserButton.Link /> as its children. Any other provided component will be ignored. Additionally, please ensure that the component is rendered in a client component.");return}let{props:t}=e,{label:i,labelIcon:a,href:l,onClick:u,open:d}=t;if(rX(e,r))if(r5(t,o))s.push({label:i});else{if(!r9(t))return void rO("Missing props. <UserButton.Action /> component requires the following props: label.");let e={label:i,labelIcon:a};if(void 0!==u)s.push({...e,onClick:u});else{if(void 0===d)return void rO("Custom menu item must have either onClick or open property");s.push({...e,open:d.startsWith("/")?d:`/${d}`})}}if(rX(e,n))if(!r3(t))return void rO("Missing props. <UserButton.Link /> component requires the following props: href, label and labelIcon.");else s.push({label:i,labelIcon:a,href:l})})});let d=[],c=[];s.forEach((e,t)=>{r9(e)&&d.push({component:e.labelIcon,id:t}),r3(e)&&c.push({component:e.labelIcon,id:t})});let h=r$(d),p=r$(c);return s.forEach((e,t)=>{if(r5(e,o)&&l.push({label:e.label}),r9(e)){let{portal:r,mount:n,unmount:i}=h.find(e=>e.id===t),a={label:e.label,mountIcon:n,unmountIcon:i};"onClick"in e?a.onClick=e.onClick:"open"in e&&(a.open=e.open),l.push(a),u.push(r)}if(r3(e)){let{portal:r,mount:n,unmount:i}=p.find(e=>e.id===t);l.push({label:e.label,href:e.href,mountIcon:n,unmountIcon:i}),u.push(r)}}),{customMenuItems:l,customMenuItemsPortals:u}},r5=(e,t)=>{let{children:r,label:n,onClick:i,labelIcon:a}=e;return!r&&!i&&!a&&t.some(e=>e===n)},r9=e=>{let{label:t,labelIcon:r,onClick:n,open:i}=e;return!!r&&!!t&&("function"==typeof n||"string"==typeof i)},r3=e=>{let{label:t,href:r,labelIcon:n}=e;return!!r&&!!n&&!!t};function r7(e){let t=(0,j.useRef)(),[r,n]=(0,j.useState)("rendering");return(0,j.useEffect)(()=>{if(!e)throw Error("Clerk: no component name provided, unable to detect mount.");"undefined"==typeof window||t.current||(t.current=(function(e){let{root:t=null==document?void 0:document.body,selector:r,timeout:n=0}=e;return new Promise((e,i)=>{if(!t)return void i(Error("No root element provided"));let a=t;if(r&&(a=null==t?void 0:t.querySelector(r)),(null==a?void 0:a.childElementCount)&&a.childElementCount>0)return void e();let o=new MutationObserver(n=>{for(let i of n)if("childList"===i.type&&(!a&&r&&(a=null==t?void 0:t.querySelector(r)),(null==a?void 0:a.childElementCount)&&a.childElementCount>0)){o.disconnect(),e();return}});o.observe(t,{childList:!0,subtree:!0}),n>0&&setTimeout(()=>{o.disconnect(),i(Error("Timeout waiting for element children"))},n)})})({selector:`[data-clerk-component="${e}"]`}).then(()=>{n("rendered")}).catch(()=>{n("error")}))},[e]),r}var r6=e=>"mount"in e,r8=e=>"open"in e,ne=e=>null==e?void 0:e.map(({mountIcon:e,unmountIcon:t,...r})=>r),nt=class extends j.PureComponent{constructor(){super(...arguments),this.rootRef=j.createRef()}componentDidUpdate(e){var t,r,n,i;if(!r6(e)||!r6(this.props))return;let a=rA(e.props,"customPages","customMenuItems","children"),o=rA(this.props.props,"customPages","customMenuItems","children"),s=(null==(t=a.customPages)?void 0:t.length)!==(null==(r=o.customPages)?void 0:r.length),l=(null==(n=a.customMenuItems)?void 0:n.length)!==(null==(i=o.customMenuItems)?void 0:i.length),u=ne(e.props.customMenuItems),d=ne(this.props.props.customMenuItems);(!tB(a,o)||!tB(u,d)||s||l)&&this.rootRef.current&&this.props.updateProps({node:this.rootRef.current,props:this.props.props})}componentDidMount(){this.rootRef.current&&(r6(this.props)&&this.props.mount(this.rootRef.current,this.props.props),r8(this.props)&&this.props.open(this.props.props))}componentWillUnmount(){this.rootRef.current&&(r6(this.props)&&this.props.unmount(this.rootRef.current),r8(this.props)&&this.props.close())}render(){let{hideRootHtmlElement:e=!1}=this.props,t={ref:this.rootRef,...this.props.rootProps,...this.props.component&&{"data-clerk-component":this.props.component}};return j.createElement(j.Fragment,null,!e&&j.createElement("div",{...t}),this.props.children)}},nr=e=>{var t,r;return j.createElement(j.Fragment,null,null==(t=null==e?void 0:e.customPagesPortals)?void 0:t.map((e,t)=>(0,j.createElement)(e,{key:t})),null==(r=null==e?void 0:e.customMenuItemsPortals)?void 0:r.map((e,t)=>(0,j.createElement)(e,{key:t})))};function nn({children:e}){return rO("<UserProfile.Page /> component needs to be a direct child of `<UserProfile />` or `<UserButton />`."),j.createElement(j.Fragment,null,e)}function ni({children:e}){return rO("<UserProfile.Link /> component needs to be a direct child of `<UserProfile />` or `<UserButton />`."),j.createElement(j.Fragment,null,e)}t9(({clerk:e,component:t,fallback:r,...n})=>{let i="rendering"===r7(t)||!e.loaded,a={...i&&r&&{style:{display:"none"}}};return j.createElement(j.Fragment,null,i&&r,e.loaded&&j.createElement(nt,{component:t,mount:e.mountSignIn,unmount:e.unmountSignIn,updateProps:e.__unstable__updateProps,props:n,rootProps:a}))},{component:"SignIn",renderWhileLoading:!0}),t9(({clerk:e,component:t,fallback:r,...n})=>{let i="rendering"===r7(t)||!e.loaded,a={...i&&r&&{style:{display:"none"}}};return j.createElement(j.Fragment,null,i&&r,e.loaded&&j.createElement(nt,{component:t,mount:e.mountSignUp,unmount:e.unmountSignUp,updateProps:e.__unstable__updateProps,props:n,rootProps:a}))},{component:"SignUp",renderWhileLoading:!0}),Object.assign(t9(({clerk:e,component:t,fallback:r,...n})=>{let i="rendering"===r7(t)||!e.loaded,a={...i&&r&&{style:{display:"none"}}},{customPages:o,customPagesPortals:s}=rG(n.children);return j.createElement(j.Fragment,null,i&&r,j.createElement(nt,{component:t,mount:e.mountUserProfile,unmount:e.unmountUserProfile,updateProps:e.__unstable__updateProps,props:{...n,customPages:o},rootProps:a},j.createElement(nr,{customPagesPortals:s})))},{component:"UserProfile",renderWhileLoading:!0}),{Page:nn,Link:ni});var na=(0,j.createContext)({mount:()=>{},unmount:()=>{},updateProps:()=>{}});function no({children:e}){return rO("<UserButton.MenuItems /> component needs to be a direct child of `<UserButton />`."),j.createElement(j.Fragment,null,e)}function ns({children:e}){return rO("<UserButton.Action /> component needs to be a direct child of `<UserButton.MenuItems />`."),j.createElement(j.Fragment,null,e)}function nl({children:e}){return rO("<UserButton.Link /> component needs to be a direct child of `<UserButton.MenuItems />`."),j.createElement(j.Fragment,null,e)}function nu({children:e}){return rO("<OrganizationProfile.Page /> component needs to be a direct child of `<OrganizationProfile />` or `<OrganizationSwitcher />`."),j.createElement(j.Fragment,null,e)}function nd({children:e}){return rO("<OrganizationProfile.Link /> component needs to be a direct child of `<OrganizationProfile />` or `<OrganizationSwitcher />`."),j.createElement(j.Fragment,null,e)}Object.assign(t9(({clerk:e,component:t,fallback:r,...n})=>{let i="rendering"===r7(t)||!e.loaded,a={...i&&r&&{style:{display:"none"}}},{customPages:o,customPagesPortals:s}=rG(n.children,{allowForAnyChildren:!!n.__experimental_asProvider}),l=Object.assign(n.userProfileProps||{},{customPages:o}),{customMenuItems:u,customMenuItemsPortals:d}=r2(n.children),c=rY(n.children),h={mount:e.mountUserButton,unmount:e.unmountUserButton,updateProps:e.__unstable__updateProps,props:{...n,userProfileProps:l,customMenuItems:u}};return j.createElement(na.Provider,{value:h},i&&r,e.loaded&&j.createElement(nt,{component:t,...h,hideRootHtmlElement:!!n.__experimental_asProvider,rootProps:a},n.__experimental_asProvider?c:null,j.createElement(nr,{customPagesPortals:s,customMenuItemsPortals:d})))},{component:"UserButton",renderWhileLoading:!0}),{UserProfilePage:nn,UserProfileLink:ni,MenuItems:no,Action:ns,Link:nl,__experimental_Outlet:function(e){let t=(0,j.useContext)(na),r={...t,props:{...t.props,...e}};return j.createElement(nt,{...r})}}),Object.assign(t9(({clerk:e,component:t,fallback:r,...n})=>{let i="rendering"===r7(t)||!e.loaded,a={...i&&r&&{style:{display:"none"}}},{customPages:o,customPagesPortals:s}=rq(n.children);return j.createElement(j.Fragment,null,i&&r,e.loaded&&j.createElement(nt,{component:t,mount:e.mountOrganizationProfile,unmount:e.unmountOrganizationProfile,updateProps:e.__unstable__updateProps,props:{...n,customPages:o},rootProps:a},j.createElement(nr,{customPagesPortals:s})))},{component:"OrganizationProfile",renderWhileLoading:!0}),{Page:nu,Link:nd}),t9(({clerk:e,component:t,fallback:r,...n})=>{let i="rendering"===r7(t)||!e.loaded,a={...i&&r&&{style:{display:"none"}}};return j.createElement(j.Fragment,null,i&&r,e.loaded&&j.createElement(nt,{component:t,mount:e.mountCreateOrganization,unmount:e.unmountCreateOrganization,updateProps:e.__unstable__updateProps,props:n,rootProps:a}))},{component:"CreateOrganization",renderWhileLoading:!0});var nc=(0,j.createContext)({mount:()=>{},unmount:()=>{},updateProps:()=>{}});Object.assign(t9(({clerk:e,component:t,fallback:r,...n})=>{let i="rendering"===r7(t)||!e.loaded,a={...i&&r&&{style:{display:"none"}}},{customPages:o,customPagesPortals:s}=rq(n.children,{allowForAnyChildren:!!n.__experimental_asProvider}),l=Object.assign(n.organizationProfileProps||{},{customPages:o}),u=rY(n.children),d={mount:e.mountOrganizationSwitcher,unmount:e.unmountOrganizationSwitcher,updateProps:e.__unstable__updateProps,props:{...n,organizationProfileProps:l},rootProps:a,component:t};return e.__experimental_prefetchOrganizationSwitcher(),j.createElement(nc.Provider,{value:d},j.createElement(j.Fragment,null,i&&r,e.loaded&&j.createElement(nt,{...d,hideRootHtmlElement:!!n.__experimental_asProvider},n.__experimental_asProvider?u:null,j.createElement(nr,{customPagesPortals:s}))))},{component:"OrganizationSwitcher",renderWhileLoading:!0}),{OrganizationProfilePage:nu,OrganizationProfileLink:nd,__experimental_Outlet:function(e){let t=(0,j.useContext)(nc),r={...t,props:{...t.props,...e}};return j.createElement(nt,{...r})}}),t9(({clerk:e,component:t,fallback:r,...n})=>{let i="rendering"===r7(t)||!e.loaded,a={...i&&r&&{style:{display:"none"}}};return j.createElement(j.Fragment,null,i&&r,e.loaded&&j.createElement(nt,{component:t,mount:e.mountOrganizationList,unmount:e.unmountOrganizationList,updateProps:e.__unstable__updateProps,props:n,rootProps:a}))},{component:"OrganizationList",renderWhileLoading:!0}),t9(({clerk:e,component:t,fallback:r,...n})=>{let i="rendering"===r7(t)||!e.loaded,a={...i&&r&&{style:{display:"none"}}};return j.createElement(j.Fragment,null,i&&r,e.loaded&&j.createElement(nt,{component:t,open:e.openGoogleOneTap,close:e.closeGoogleOneTap,updateProps:e.__unstable__updateProps,props:n,rootProps:a}))},{component:"GoogleOneTap",renderWhileLoading:!0}),t9(({clerk:e,component:t,fallback:r,...n})=>{let i="rendering"===r7(t)||!e.loaded,a={...i&&r&&{style:{display:"none"}}};return j.createElement(j.Fragment,null,i&&r,e.loaded&&j.createElement(nt,{component:t,mount:e.mountWaitlist,unmount:e.unmountWaitlist,updateProps:e.__unstable__updateProps,props:n,rootProps:a}))},{component:"Waitlist",renderWhileLoading:!0}),t9(({clerk:e,component:t,fallback:r,...n})=>{let i="rendering"===r7(t)||!e.loaded,a={...i&&r&&{style:{display:"none"}}};return j.createElement(j.Fragment,null,i&&r,e.loaded&&j.createElement(nt,{component:t,mount:e.mountPricingTable,unmount:e.unmountPricingTable,updateProps:e.__unstable__updateProps,props:n,rootProps:a}))},{component:"PricingTable",renderWhileLoading:!0}),t9(({clerk:e,children:t,...r})=>{let{signUpFallbackRedirectUrl:n,forceRedirectUrl:i,fallbackRedirectUrl:a,signUpForceRedirectUrl:o,mode:s,initialValues:l,withSignUp:u,oauthFlow:d,...c}=r,h=rZ(t=rK(t,"Sign in"))("SignInButton"),p=()=>{let t={forceRedirectUrl:i,fallbackRedirectUrl:a,signUpFallbackRedirectUrl:n,signUpForceRedirectUrl:o,initialValues:l,withSignUp:u,oauthFlow:d};return"modal"===s?e.openSignIn({...t,appearance:r.appearance}):e.redirectToSignIn({...t,signInFallbackRedirectUrl:a,signInForceRedirectUrl:i})},f=async e=>(h&&"object"==typeof h&&"props"in h&&await rV(h.props.onClick)(e),p()),m={...c,onClick:f};return j.cloneElement(h,m)},{component:"SignInButton",renderWhileLoading:!0}),t9(({clerk:e,children:t,...r})=>{let{fallbackRedirectUrl:n,forceRedirectUrl:i,signInFallbackRedirectUrl:a,signInForceRedirectUrl:o,mode:s,unsafeMetadata:l,initialValues:u,oauthFlow:d,...c}=r,h=rZ(t=rK(t,"Sign up"))("SignUpButton"),p=()=>{let t={fallbackRedirectUrl:n,forceRedirectUrl:i,signInFallbackRedirectUrl:a,signInForceRedirectUrl:o,unsafeMetadata:l,initialValues:u,oauthFlow:d};return"modal"===s?e.openSignUp({...t,appearance:r.appearance}):e.redirectToSignUp({...t,signUpFallbackRedirectUrl:n,signUpForceRedirectUrl:i})},f=async e=>(h&&"object"==typeof h&&"props"in h&&await rV(h.props.onClick)(e),p()),m={...c,onClick:f};return j.cloneElement(h,m)},{component:"SignUpButton",renderWhileLoading:!0}),t9(({clerk:e,children:t,...r})=>{let{redirectUrl:n="/",signOutOptions:i,...a}=r,o=rZ(t=rK(t,"Sign out"))("SignOutButton"),s=()=>e.signOut({redirectUrl:n,...i}),l=async e=>(await rV(o.props.onClick)(e),s()),u={...a,onClick:l};return j.cloneElement(o,u)},{component:"SignOutButton",renderWhileLoading:!0}),t9(({clerk:e,children:t,...r})=>{let{redirectUrl:n,...i}=r,a=rZ(t=rK(t,"Sign in with Metamask"))("SignInWithMetamaskButton"),o=async()=>{!async function(){await e.authenticateWithMetamask({redirectUrl:n||void 0})}()},s=async e=>(await rV(a.props.onClick)(e),o()),l={...i,onClick:s};return j.cloneElement(a,l)},{component:"SignInWithMetamask",renderWhileLoading:!0}),void 0===globalThis.__BUILD_DISABLE_RHC__&&(globalThis.__BUILD_DISABLE_RHC__=!1);var nh={name:"@clerk/clerk-react",version:"5.31.6",environment:"production"},np=class e{constructor(e){t8(this,S),this.clerkjs=null,this.preopenOneTap=null,this.preopenUserVerification=null,this.preopenSignIn=null,this.preopenCheckout=null,this.preopenPlanDetails=null,this.preopenSignUp=null,this.preopenUserProfile=null,this.preopenOrganizationProfile=null,this.preopenCreateOrganization=null,this.preOpenWaitlist=null,this.premountSignInNodes=new Map,this.premountSignUpNodes=new Map,this.premountUserProfileNodes=new Map,this.premountUserButtonNodes=new Map,this.premountOrganizationProfileNodes=new Map,this.premountCreateOrganizationNodes=new Map,this.premountOrganizationSwitcherNodes=new Map,this.premountOrganizationListNodes=new Map,this.premountMethodCalls=new Map,this.premountWaitlistNodes=new Map,this.premountPricingTableNodes=new Map,this.premountAddListenerCalls=new Map,this.loadedListeners=[],t8(this,b,"loading"),t8(this,w),t8(this,E),t8(this,k),t8(this,P,rz()),this.buildSignInUrl=e=>{let t=()=>{var t;return(null==(t=this.clerkjs)?void 0:t.buildSignInUrl(e))||""};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set("buildSignInUrl",t)},this.buildSignUpUrl=e=>{let t=()=>{var t;return(null==(t=this.clerkjs)?void 0:t.buildSignUpUrl(e))||""};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set("buildSignUpUrl",t)},this.buildAfterSignInUrl=(...e)=>{let t=()=>{var t;return(null==(t=this.clerkjs)?void 0:t.buildAfterSignInUrl(...e))||""};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set("buildAfterSignInUrl",t)},this.buildAfterSignUpUrl=(...e)=>{let t=()=>{var t;return(null==(t=this.clerkjs)?void 0:t.buildAfterSignUpUrl(...e))||""};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set("buildAfterSignUpUrl",t)},this.buildAfterSignOutUrl=()=>{let e=()=>{var e;return(null==(e=this.clerkjs)?void 0:e.buildAfterSignOutUrl())||""};if(this.clerkjs&&this.loaded)return e();this.premountMethodCalls.set("buildAfterSignOutUrl",e)},this.buildNewSubscriptionRedirectUrl=()=>{let e=()=>{var e;return(null==(e=this.clerkjs)?void 0:e.buildNewSubscriptionRedirectUrl())||""};if(this.clerkjs&&this.loaded)return e();this.premountMethodCalls.set("buildNewSubscriptionRedirectUrl",e)},this.buildAfterMultiSessionSingleSignOutUrl=()=>{let e=()=>{var e;return(null==(e=this.clerkjs)?void 0:e.buildAfterMultiSessionSingleSignOutUrl())||""};if(this.clerkjs&&this.loaded)return e();this.premountMethodCalls.set("buildAfterMultiSessionSingleSignOutUrl",e)},this.buildUserProfileUrl=()=>{let e=()=>{var e;return(null==(e=this.clerkjs)?void 0:e.buildUserProfileUrl())||""};if(this.clerkjs&&this.loaded)return e();this.premountMethodCalls.set("buildUserProfileUrl",e)},this.buildCreateOrganizationUrl=()=>{let e=()=>{var e;return(null==(e=this.clerkjs)?void 0:e.buildCreateOrganizationUrl())||""};if(this.clerkjs&&this.loaded)return e();this.premountMethodCalls.set("buildCreateOrganizationUrl",e)},this.buildOrganizationProfileUrl=()=>{let e=()=>{var e;return(null==(e=this.clerkjs)?void 0:e.buildOrganizationProfileUrl())||""};if(this.clerkjs&&this.loaded)return e();this.premountMethodCalls.set("buildOrganizationProfileUrl",e)},this.buildWaitlistUrl=()=>{let e=()=>{var e;return(null==(e=this.clerkjs)?void 0:e.buildWaitlistUrl())||""};if(this.clerkjs&&this.loaded)return e();this.premountMethodCalls.set("buildWaitlistUrl",e)},this.buildUrlWithAuth=e=>{let t=()=>{var t;return(null==(t=this.clerkjs)?void 0:t.buildUrlWithAuth(e))||""};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set("buildUrlWithAuth",t)},this.handleUnauthenticated=async()=>{let e=()=>{var e;return null==(e=this.clerkjs)?void 0:e.handleUnauthenticated()};this.clerkjs&&this.loaded?e():this.premountMethodCalls.set("handleUnauthenticated",e)},this.on=(...e)=>{var t;if(null==(t=this.clerkjs)?void 0:t.on)return this.clerkjs.on(...e);t6(this,P).on(...e)},this.off=(...e)=>{var t;if(null==(t=this.clerkjs)?void 0:t.off)return this.clerkjs.off(...e);t6(this,P).off(...e)},this.addOnLoaded=e=>{this.loadedListeners.push(e),this.loaded&&this.emitLoaded()},this.emitLoaded=()=>{this.loadedListeners.forEach(e=>e()),this.loadedListeners=[]},this.beforeLoad=e=>{if(!e)throw Error("Failed to hydrate latest Clerk JS")},this.hydrateClerkJS=e=>{var t;if(!e)throw Error("Failed to hydrate latest Clerk JS");return this.clerkjs=e,this.premountMethodCalls.forEach(e=>e()),this.premountAddListenerCalls.forEach((t,r)=>{t.nativeUnsubscribe=e.addListener(r)}),null==(t=t6(this,P).internal.retrieveListeners("status"))||t.forEach(e=>{this.on("status",e,{notify:!0})}),null!==this.preopenSignIn&&e.openSignIn(this.preopenSignIn),null!==this.preopenCheckout&&e.__internal_openCheckout(this.preopenCheckout),null!==this.preopenPlanDetails&&e.__internal_openPlanDetails(this.preopenPlanDetails),null!==this.preopenSignUp&&e.openSignUp(this.preopenSignUp),null!==this.preopenUserProfile&&e.openUserProfile(this.preopenUserProfile),null!==this.preopenUserVerification&&e.__internal_openReverification(this.preopenUserVerification),null!==this.preopenOneTap&&e.openGoogleOneTap(this.preopenOneTap),null!==this.preopenOrganizationProfile&&e.openOrganizationProfile(this.preopenOrganizationProfile),null!==this.preopenCreateOrganization&&e.openCreateOrganization(this.preopenCreateOrganization),null!==this.preOpenWaitlist&&e.openWaitlist(this.preOpenWaitlist),this.premountSignInNodes.forEach((t,r)=>{e.mountSignIn(r,t)}),this.premountSignUpNodes.forEach((t,r)=>{e.mountSignUp(r,t)}),this.premountUserProfileNodes.forEach((t,r)=>{e.mountUserProfile(r,t)}),this.premountUserButtonNodes.forEach((t,r)=>{e.mountUserButton(r,t)}),this.premountOrganizationListNodes.forEach((t,r)=>{e.mountOrganizationList(r,t)}),this.premountWaitlistNodes.forEach((t,r)=>{e.mountWaitlist(r,t)}),this.premountPricingTableNodes.forEach((t,r)=>{e.mountPricingTable(r,t)}),void 0===this.clerkjs.status&&t6(this,P).emit(rW.Status,"ready"),this.emitLoaded(),this.clerkjs},this.__unstable__updateProps=async e=>{let t=await rt(this,S,R).call(this);if(t&&"__unstable__updateProps"in t)return t.__unstable__updateProps(e)},this.__experimental_navigateToTask=async e=>this.clerkjs?this.clerkjs.__experimental_navigateToTask(e):Promise.reject(),this.setActive=e=>this.clerkjs?this.clerkjs.setActive(e):Promise.reject(),this.openSignIn=e=>{this.clerkjs&&this.loaded?this.clerkjs.openSignIn(e):this.preopenSignIn=e},this.closeSignIn=()=>{this.clerkjs&&this.loaded?this.clerkjs.closeSignIn():this.preopenSignIn=null},this.__internal_openCheckout=e=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_openCheckout(e):this.preopenCheckout=e},this.__internal_closeCheckout=()=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_closeCheckout():this.preopenCheckout=null},this.__internal_openPlanDetails=e=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_openPlanDetails(e):this.preopenPlanDetails=e},this.__internal_closePlanDetails=()=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_closePlanDetails():this.preopenPlanDetails=null},this.__internal_openReverification=e=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_openReverification(e):this.preopenUserVerification=e},this.__internal_closeReverification=()=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_closeReverification():this.preopenUserVerification=null},this.openGoogleOneTap=e=>{this.clerkjs&&this.loaded?this.clerkjs.openGoogleOneTap(e):this.preopenOneTap=e},this.closeGoogleOneTap=()=>{this.clerkjs&&this.loaded?this.clerkjs.closeGoogleOneTap():this.preopenOneTap=null},this.openUserProfile=e=>{this.clerkjs&&this.loaded?this.clerkjs.openUserProfile(e):this.preopenUserProfile=e},this.closeUserProfile=()=>{this.clerkjs&&this.loaded?this.clerkjs.closeUserProfile():this.preopenUserProfile=null},this.openOrganizationProfile=e=>{this.clerkjs&&this.loaded?this.clerkjs.openOrganizationProfile(e):this.preopenOrganizationProfile=e},this.closeOrganizationProfile=()=>{this.clerkjs&&this.loaded?this.clerkjs.closeOrganizationProfile():this.preopenOrganizationProfile=null},this.openCreateOrganization=e=>{this.clerkjs&&this.loaded?this.clerkjs.openCreateOrganization(e):this.preopenCreateOrganization=e},this.closeCreateOrganization=()=>{this.clerkjs&&this.loaded?this.clerkjs.closeCreateOrganization():this.preopenCreateOrganization=null},this.openWaitlist=e=>{this.clerkjs&&this.loaded?this.clerkjs.openWaitlist(e):this.preOpenWaitlist=e},this.closeWaitlist=()=>{this.clerkjs&&this.loaded?this.clerkjs.closeWaitlist():this.preOpenWaitlist=null},this.openSignUp=e=>{this.clerkjs&&this.loaded?this.clerkjs.openSignUp(e):this.preopenSignUp=e},this.closeSignUp=()=>{this.clerkjs&&this.loaded?this.clerkjs.closeSignUp():this.preopenSignUp=null},this.mountSignIn=(e,t)=>{this.clerkjs&&this.loaded?this.clerkjs.mountSignIn(e,t):this.premountSignInNodes.set(e,t)},this.unmountSignIn=e=>{this.clerkjs&&this.loaded?this.clerkjs.unmountSignIn(e):this.premountSignInNodes.delete(e)},this.mountSignUp=(e,t)=>{this.clerkjs&&this.loaded?this.clerkjs.mountSignUp(e,t):this.premountSignUpNodes.set(e,t)},this.unmountSignUp=e=>{this.clerkjs&&this.loaded?this.clerkjs.unmountSignUp(e):this.premountSignUpNodes.delete(e)},this.mountUserProfile=(e,t)=>{this.clerkjs&&this.loaded?this.clerkjs.mountUserProfile(e,t):this.premountUserProfileNodes.set(e,t)},this.unmountUserProfile=e=>{this.clerkjs&&this.loaded?this.clerkjs.unmountUserProfile(e):this.premountUserProfileNodes.delete(e)},this.mountOrganizationProfile=(e,t)=>{this.clerkjs&&this.loaded?this.clerkjs.mountOrganizationProfile(e,t):this.premountOrganizationProfileNodes.set(e,t)},this.unmountOrganizationProfile=e=>{this.clerkjs&&this.loaded?this.clerkjs.unmountOrganizationProfile(e):this.premountOrganizationProfileNodes.delete(e)},this.mountCreateOrganization=(e,t)=>{this.clerkjs&&this.loaded?this.clerkjs.mountCreateOrganization(e,t):this.premountCreateOrganizationNodes.set(e,t)},this.unmountCreateOrganization=e=>{this.clerkjs&&this.loaded?this.clerkjs.unmountCreateOrganization(e):this.premountCreateOrganizationNodes.delete(e)},this.mountOrganizationSwitcher=(e,t)=>{this.clerkjs&&this.loaded?this.clerkjs.mountOrganizationSwitcher(e,t):this.premountOrganizationSwitcherNodes.set(e,t)},this.unmountOrganizationSwitcher=e=>{this.clerkjs&&this.loaded?this.clerkjs.unmountOrganizationSwitcher(e):this.premountOrganizationSwitcherNodes.delete(e)},this.__experimental_prefetchOrganizationSwitcher=()=>{let e=()=>{var e;return null==(e=this.clerkjs)?void 0:e.__experimental_prefetchOrganizationSwitcher()};this.clerkjs&&this.loaded?e():this.premountMethodCalls.set("__experimental_prefetchOrganizationSwitcher",e)},this.mountOrganizationList=(e,t)=>{this.clerkjs&&this.loaded?this.clerkjs.mountOrganizationList(e,t):this.premountOrganizationListNodes.set(e,t)},this.unmountOrganizationList=e=>{this.clerkjs&&this.loaded?this.clerkjs.unmountOrganizationList(e):this.premountOrganizationListNodes.delete(e)},this.mountUserButton=(e,t)=>{this.clerkjs&&this.loaded?this.clerkjs.mountUserButton(e,t):this.premountUserButtonNodes.set(e,t)},this.unmountUserButton=e=>{this.clerkjs&&this.loaded?this.clerkjs.unmountUserButton(e):this.premountUserButtonNodes.delete(e)},this.mountWaitlist=(e,t)=>{this.clerkjs&&this.loaded?this.clerkjs.mountWaitlist(e,t):this.premountWaitlistNodes.set(e,t)},this.unmountWaitlist=e=>{this.clerkjs&&this.loaded?this.clerkjs.unmountWaitlist(e):this.premountWaitlistNodes.delete(e)},this.mountPricingTable=(e,t)=>{this.clerkjs&&this.loaded?this.clerkjs.mountPricingTable(e,t):this.premountPricingTableNodes.set(e,t)},this.unmountPricingTable=e=>{this.clerkjs&&this.loaded?this.clerkjs.unmountPricingTable(e):this.premountPricingTableNodes.delete(e)},this.addListener=e=>{if(this.clerkjs)return this.clerkjs.addListener(e);{let t=()=>{var t;let r=this.premountAddListenerCalls.get(e);r&&(null==(t=r.nativeUnsubscribe)||t.call(r),this.premountAddListenerCalls.delete(e))};return this.premountAddListenerCalls.set(e,{unsubscribe:t,nativeUnsubscribe:void 0}),t}},this.navigate=e=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.navigate(e)};this.clerkjs&&this.loaded?t():this.premountMethodCalls.set("navigate",t)},this.redirectWithAuth=async(...e)=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.redirectWithAuth(...e)};return this.clerkjs&&this.loaded?t():void this.premountMethodCalls.set("redirectWithAuth",t)},this.redirectToSignIn=async e=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.redirectToSignIn(e)};return this.clerkjs&&this.loaded?t():void this.premountMethodCalls.set("redirectToSignIn",t)},this.redirectToSignUp=async e=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.redirectToSignUp(e)};return this.clerkjs&&this.loaded?t():void this.premountMethodCalls.set("redirectToSignUp",t)},this.redirectToUserProfile=async()=>{let e=()=>{var e;return null==(e=this.clerkjs)?void 0:e.redirectToUserProfile()};return this.clerkjs&&this.loaded?e():void this.premountMethodCalls.set("redirectToUserProfile",e)},this.redirectToAfterSignUp=()=>{let e=()=>{var e;return null==(e=this.clerkjs)?void 0:e.redirectToAfterSignUp()};if(this.clerkjs&&this.loaded)return e();this.premountMethodCalls.set("redirectToAfterSignUp",e)},this.redirectToAfterSignIn=()=>{let e=()=>{var e;return null==(e=this.clerkjs)?void 0:e.redirectToAfterSignIn()};this.clerkjs&&this.loaded?e():this.premountMethodCalls.set("redirectToAfterSignIn",e)},this.redirectToAfterSignOut=()=>{let e=()=>{var e;return null==(e=this.clerkjs)?void 0:e.redirectToAfterSignOut()};this.clerkjs&&this.loaded?e():this.premountMethodCalls.set("redirectToAfterSignOut",e)},this.redirectToOrganizationProfile=async()=>{let e=()=>{var e;return null==(e=this.clerkjs)?void 0:e.redirectToOrganizationProfile()};return this.clerkjs&&this.loaded?e():void this.premountMethodCalls.set("redirectToOrganizationProfile",e)},this.redirectToCreateOrganization=async()=>{let e=()=>{var e;return null==(e=this.clerkjs)?void 0:e.redirectToCreateOrganization()};return this.clerkjs&&this.loaded?e():void this.premountMethodCalls.set("redirectToCreateOrganization",e)},this.redirectToWaitlist=async()=>{let e=()=>{var e;return null==(e=this.clerkjs)?void 0:e.redirectToWaitlist()};return this.clerkjs&&this.loaded?e():void this.premountMethodCalls.set("redirectToWaitlist",e)},this.handleRedirectCallback=async e=>{var t;let r=()=>{var t;return null==(t=this.clerkjs)?void 0:t.handleRedirectCallback(e)};this.clerkjs&&this.loaded?null==(t=r())||t.catch(()=>{}):this.premountMethodCalls.set("handleRedirectCallback",r)},this.handleGoogleOneTapCallback=async(e,t)=>{var r;let n=()=>{var r;return null==(r=this.clerkjs)?void 0:r.handleGoogleOneTapCallback(e,t)};this.clerkjs&&this.loaded?null==(r=n())||r.catch(()=>{}):this.premountMethodCalls.set("handleGoogleOneTapCallback",n)},this.handleEmailLinkVerification=async e=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.handleEmailLinkVerification(e)};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set("handleEmailLinkVerification",t)},this.authenticateWithMetamask=async e=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.authenticateWithMetamask(e)};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set("authenticateWithMetamask",t)},this.authenticateWithCoinbaseWallet=async e=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.authenticateWithCoinbaseWallet(e)};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set("authenticateWithCoinbaseWallet",t)},this.authenticateWithOKXWallet=async e=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.authenticateWithOKXWallet(e)};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set("authenticateWithOKXWallet",t)},this.authenticateWithWeb3=async e=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.authenticateWithWeb3(e)};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set("authenticateWithWeb3",t)},this.authenticateWithGoogleOneTap=async e=>(await rt(this,S,R).call(this)).authenticateWithGoogleOneTap(e),this.createOrganization=async e=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.createOrganization(e)};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set("createOrganization",t)},this.getOrganization=async e=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.getOrganization(e)};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set("getOrganization",t)},this.joinWaitlist=async e=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.joinWaitlist(e)};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set("joinWaitlist",t)},this.signOut=async(...e)=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.signOut(...e)};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set("signOut",t)};let{Clerk:t=null,publishableKey:r}=e||{};re(this,k,r),re(this,E,null==e?void 0:e.proxyUrl),re(this,w,null==e?void 0:e.domain),this.options=e,this.Clerk=t,this.mode=rL()?"browser":"server",this.options.sdkMetadata||(this.options.sdkMetadata=nh),t6(this,P).emit(rW.Status,"loading"),t6(this,P).prioritizedOn(rW.Status,e=>re(this,b,e)),t6(this,k)&&this.loadClerkJS()}get publishableKey(){return t6(this,k)}get loaded(){var e;return(null==(e=this.clerkjs)?void 0:e.loaded)||!1}get status(){var e;return this.clerkjs?(null==(e=this.clerkjs)?void 0:e.status)||(this.clerkjs.loaded?"ready":"loading"):t6(this,b)}static getOrCreateInstance(t){return rL()&&t6(this,x)&&(!t.Clerk||t6(this,x).Clerk===t.Clerk)&&t6(this,x).publishableKey===t.publishableKey||re(this,x,new e(t)),t6(this,x)}static clearInstance(){re(this,x,null)}get domain(){return"undefined"!=typeof window&&window.location?rj(t6(this,w),new URL(window.location.href),""):"function"==typeof t6(this,w)?tH.throw(tY):t6(this,w)||""}get proxyUrl(){return"undefined"!=typeof window&&window.location?rj(t6(this,E),new URL(window.location.href),""):"function"==typeof t6(this,E)?tH.throw(tY):t6(this,E)||""}__internal_getOption(e){var t,r;return(null==(t=this.clerkjs)?void 0:t.__internal_getOption)?null==(r=this.clerkjs)?void 0:r.__internal_getOption(e):this.options[e]}get sdkMetadata(){var e;return(null==(e=this.clerkjs)?void 0:e.sdkMetadata)||this.options.sdkMetadata||void 0}get instanceType(){var e;return null==(e=this.clerkjs)?void 0:e.instanceType}get frontendApi(){var e;return(null==(e=this.clerkjs)?void 0:e.frontendApi)||""}get isStandardBrowser(){var e;return(null==(e=this.clerkjs)?void 0:e.isStandardBrowser)||this.options.standardBrowser||!1}get isSatellite(){return"undefined"!=typeof window&&window.location?rj(this.options.isSatellite,new URL(window.location.href),!1):"function"==typeof this.options.isSatellite&&tH.throw(tY)}async loadClerkJS(){var e,t;if("browser"===this.mode&&!this.loaded){"undefined"!=typeof window&&(window.__clerk_publishable_key=t6(this,k),window.__clerk_proxy_url=this.proxyUrl,window.__clerk_domain=this.domain);try{if(this.Clerk){let e;(t=this.Clerk,"function"==typeof t)?(e=new this.Clerk(t6(this,k),{proxyUrl:this.proxyUrl,domain:this.domain}),this.beforeLoad(e),await e.load(this.options)):(e=this.Clerk).loaded||(this.beforeLoad(e),await e.load(this.options)),global.Clerk=e}else if(!__BUILD_DISABLE_RHC__){if(global.Clerk||await rx({...this.options,publishableKey:t6(this,k),proxyUrl:this.proxyUrl,domain:this.domain,nonce:this.options.nonce}),!global.Clerk)throw Error("Failed to download latest ClerkJS. Contact <EMAIL>.");this.beforeLoad(global.Clerk),await global.Clerk.load(this.options)}if(null==(e=global.Clerk)?void 0:e.loaded)return this.hydrateClerkJS(global.Clerk);return}catch(e){t6(this,P).emit(rW.Status,"error"),console.error(e.stack||e.message||e);return}}}get version(){var e;return null==(e=this.clerkjs)?void 0:e.version}get client(){return this.clerkjs?this.clerkjs.client:void 0}get session(){return this.clerkjs?this.clerkjs.session:void 0}get user(){return this.clerkjs?this.clerkjs.user:void 0}get organization(){return this.clerkjs?this.clerkjs.organization:void 0}get telemetry(){return this.clerkjs?this.clerkjs.telemetry:void 0}get __unstable__environment(){return this.clerkjs?this.clerkjs.__unstable__environment:void 0}get isSignedIn(){return!!this.clerkjs&&this.clerkjs.isSignedIn}get billing(){var e;return null==(e=this.clerkjs)?void 0:e.billing}__unstable__setEnvironment(...e){this.clerkjs&&"__unstable__setEnvironment"in this.clerkjs&&this.clerkjs.__unstable__setEnvironment(e)}};function nf(e){let{isomorphicClerkOptions:t,initialState:r,children:n}=e,{isomorphicClerk:i,clerkStatus:a}=nm(t),[o,s]=j.useState({client:i.client,session:i.session,user:i.user,organization:i.organization});j.useEffect(()=>i.addListener(e=>s({...e})),[]);let l=rI(i.loaded,o,r),u=j.useMemo(()=>({value:i}),[a]),d=j.useMemo(()=>({value:o.client}),[o.client]),{sessionId:c,sessionStatus:h,sessionClaims:p,session:f,userId:m,user:g,orgId:v,actor:_,organization:y,orgRole:b,orgSlug:w,orgPermissions:E,factorVerificationAge:k}=l,P=j.useMemo(()=>({value:{sessionId:c,sessionStatus:h,sessionClaims:p,userId:m,actor:_,orgId:v,orgRole:b,orgSlug:w,orgPermissions:E,factorVerificationAge:k}}),[c,h,m,_,v,b,w,k,null==p?void 0:p.__raw]),x=j.useMemo(()=>({value:f}),[c,f]),S=j.useMemo(()=>({value:g}),[m,g]),R=j.useMemo(()=>({value:{organization:y}}),[v,y]);return j.createElement(tx.Provider,{value:u},j.createElement(tO.Provider,{value:d},j.createElement(tT.Provider,{value:x},j.createElement(tN,{...R.value},j.createElement(tX.Provider,{value:P},j.createElement(tR.Provider,{value:S},n))))))}b=new WeakMap,w=new WeakMap,E=new WeakMap,k=new WeakMap,P=new WeakMap,x=new WeakMap,S=new WeakSet,R=function(){return new Promise(e=>{this.addOnLoaded(()=>e(this.clerkjs))})},t8(np,x);var nm=e=>{let t=j.useMemo(()=>np.getOrCreateInstance(e),[]),[r,n]=j.useState(t.status);return j.useEffect(()=>{t.__unstable__updateProps({appearance:e.appearance})},[e.appearance]),j.useEffect(()=>{t.__unstable__updateProps({options:e})},[e.localization]),j.useEffect(()=>(t.on("status",n),()=>t.off("status",n)),[t]),j.useEffect(()=>()=>{np.clearInstance()},[]),{isomorphicClerk:t,clerkStatus:r}},ng=function(e,t,r){let n=e.displayName||e.name||t||"Component",i=n=>(!function(e,t,r=1){j.useEffect(()=>{let n=rH.get(e)||0;return n==r?tH.throw(t):(rH.set(e,n+1),()=>{rH.set(e,(rH.get(e)||1)-1)})},[])}(t,r),j.createElement(e,{...n}));return i.displayName=`withMaxAllowedInstancesGuard(${n})`,i}(function(e){let{initialState:t,children:r,__internal_bypassMissingPublishableKey:n,...i}=e,{publishableKey:a="",Clerk:o}=i;return o||n||(a?a&&!rb(a)&&tH.throwInvalidPublishableKeyError({key:a}):tH.throwMissingPublishableKeyError()),j.createElement(nf,{initialState:t,isomorphicClerkOptions:i},r)},"ClerkProvider","You've added multiple <ClerkProvider> components in your React component tree. Wrap your components in a single <ClerkProvider>.");ng.displayName="ClerkProvider",t$({packageName:"@clerk/clerk-react"}),rP("@clerk/clerk-react");var nv=new Set,n_={warnOnce:e=>{nv.has(e)||(nv.add(e),console.warn(e))},logOnce:e=>{nv.has(e)||(console.log(e),nv.add(e))}},ny=r(74876),nb=r.n(ny),nw=r(35062);let nE={rE:"15.3.2"},nk="undefined"!=typeof window?j.useLayoutEffect:j.useEffect,nP=j.createContext(void 0);nP.displayName="ClerkNextOptionsCtx";let nx=()=>{let e=j.useContext(nP);return null==e?void 0:e.value},nS=e=>{let{children:t,options:r}=e;return j.createElement(nP.Provider,{value:{value:r}},t)};var nR=r(70787);function nC(e){let{publishableKey:t,clerkJSUrl:r,clerkJSVersion:n,clerkJSVariant:i,nonce:a}=nx(),{domain:o,proxyUrl:s}=tF();if(!t)return null;let l={domain:o,proxyUrl:s,publishableKey:t,clerkJSUrl:r,clerkJSVersion:n,clerkJSVariant:i,nonce:a},u=rS(l),d="app"===e.router?"script":nR.default;return j.createElement(d,{src:u,"data-clerk-js-script":!0,async:!0,defer:"pages"!==e.router&&void 0,crossOrigin:"anonymous",strategy:"pages"===e.router?"beforeInteractive":void 0,...rR(l)})}var nO=r(37811);nO.env.NEXT_PUBLIC_CLERK_JS_VERSION,nO.env.NEXT_PUBLIC_CLERK_JS_URL,nO.env.CLERK_API_VERSION,nO.env.CLERK_SECRET_KEY,nO.env.CLERK_ENCRYPTION_KEY,nO.env.CLERK_API_URL||(e=>{let t=ry(e)?.frontendApi;return t?.startsWith("clerk.")&&rf.some(e=>t?.endsWith(e))||rg.some(e=>t?.endsWith(e))||rv.some(e=>t?.endsWith(e))})("pk_live_Y2xlcmsuY3ViZW50LmRldiQ"),nO.env.NEXT_PUBLIC_CLERK_DOMAIN,nO.env.NEXT_PUBLIC_CLERK_PROXY_URL,ea(nO.env.NEXT_PUBLIC_CLERK_IS_SATELLITE);let nj={name:"@clerk/nextjs",version:"6.20.0",environment:"production"};ea(nO.env.NEXT_PUBLIC_CLERK_TELEMETRY_DISABLED),ea(nO.env.NEXT_PUBLIC_CLERK_TELEMETRY_DEBUG);let nT=ea(nO.env.NEXT_PUBLIC_CLERK_KEYLESS_DISABLED)||!1,nA=nE.rE.startsWith("13.")||nE.rE.startsWith("14.0"),nI=!nA&&tW()&&!nT;var nM=r(37811);let nN=e=>{var t;return{...e,publishableKey:e.publishableKey||"pk_live_Y2xlcmsuY3ViZW50LmRldiQ",clerkJSUrl:e.clerkJSUrl||nM.env.NEXT_PUBLIC_CLERK_JS_URL,clerkJSVersion:e.clerkJSVersion||nM.env.NEXT_PUBLIC_CLERK_JS_VERSION,proxyUrl:e.proxyUrl||nM.env.NEXT_PUBLIC_CLERK_PROXY_URL||"",domain:e.domain||nM.env.NEXT_PUBLIC_CLERK_DOMAIN||"",isSatellite:e.isSatellite||ea(nM.env.NEXT_PUBLIC_CLERK_IS_SATELLITE),signInUrl:e.signInUrl||"/sign-in",signUpUrl:e.signUpUrl||"/sign-up",signInForceRedirectUrl:e.signInForceRedirectUrl||nM.env.NEXT_PUBLIC_CLERK_SIGN_IN_FORCE_REDIRECT_URL||"",signUpForceRedirectUrl:e.signUpForceRedirectUrl||nM.env.NEXT_PUBLIC_CLERK_SIGN_UP_FORCE_REDIRECT_URL||"",signInFallbackRedirectUrl:e.signInFallbackRedirectUrl||nM.env.NEXT_PUBLIC_CLERK_SIGN_IN_FALLBACK_REDIRECT_URL||"",signUpFallbackRedirectUrl:e.signUpFallbackRedirectUrl||nM.env.NEXT_PUBLIC_CLERK_SIGN_UP_FALLBACK_REDIRECT_URL||"",afterSignInUrl:e.afterSignInUrl||"/",afterSignUpUrl:e.afterSignUpUrl||"/",newSubscriptionRedirectUrl:e.newSubscriptionRedirectUrl||nM.env.NEXT_PUBLIC_CLERK_CHECKOUT_CONTINUE_URL||"",telemetry:null!=(t=e.telemetry)?t:{disabled:ea(nM.env.NEXT_PUBLIC_CLERK_TELEMETRY_DISABLED),debug:ea(nM.env.NEXT_PUBLIC_CLERK_TELEMETRY_DEBUG)},sdkMetadata:nj}},nL=()=>({pagesRouter:(0,O.useRouter)()}),nU=()=>{var e,t;let r=tF(),{pagesRouter:n}=nL();return null==(t=r.telemetry)||t.record({event:"FRAMEWORK_METADATA",eventSamplingRate:.1,payload:{router:n?"pages":"app",...(null==(e=null==globalThis?void 0:globalThis.next)?void 0:e.version)?{nextjsVersion:globalThis.next.version}:{}}}),null};var nD=r(66999);function nF(e){return e}let nB=e=>{var t;return null!=window.__clerk_internal_navigations||(window.__clerk_internal_navigations={}),null!=(t=window.__clerk_internal_navigations)[e]||(t[e]={}),window.__clerk_internal_navigations[e]},nW=e=>{let{windowNav:t,routerNav:r,name:n}=e,i=(0,nw.usePathname)(),[a,o]=(0,j.useTransition)();t&&(nB(n).fun=(e,i)=>new Promise(a=>{var s,l;null!=(s=nB(n)).promisesBuffer||(s.promisesBuffer=[]),null==(l=nB(n).promisesBuffer)||l.push(a),o(()=>{var n,a,o,s;(null==(n=null==i?void 0:i.__internal_metadata)?void 0:n.navigationType)==="internal"?t((null!=(o=null==(a=window.next)?void 0:a.version)?o:"")<"14.1.0"?history.state:null,"",e):r(s=e)})}));let s=()=>{var e;null==(e=nB(n).promisesBuffer)||e.forEach(e=>e()),nB(n).promisesBuffer=[]};return(0,j.useEffect)(()=>(s(),s),[]),(0,j.useEffect)(()=>{a||s()},[i,a]),(0,j.useCallback)((e,t)=>nB(n).fun(e,t),[])},nz=()=>{let e=(0,nw.useRouter)();return nW({windowNav:"undefined"!=typeof window?window.history.pushState.bind(window.history):void 0,routerNav:e.push.bind(e),name:"push"})},nZ=()=>{let e=(0,nw.useRouter)();return nW({windowNav:"undefined"!=typeof window?window.history.replaceState.bind(window.history):void 0,routerNav:e.replace.bind(e),name:"replace"})},nK=nb()(()=>r.e(445).then(r.bind(r,14445)).then(e=>e.KeylessCreatorOrReader),{loadableGenerated:{webpack:()=>[14445]}}),nV=e=>{if(nA){let e="Clerk:\nYour current Next.js version (".concat(nE.rE,') will be deprecated in the next major release of "@clerk/nextjs". Please upgrade to next@14.1.0 or later.');rL()?n_.warnOnce(e):n_.logOnce("\n\x1b[43m----------\n".concat(e,"\n----------\x1b[0m\n"))}let{__unstable_invokeMiddlewareOnAuthStateChange:t=!0,children:r}=e,n=(0,nw.useRouter)(),i=nz(),a=nZ(),[o,s]=(0,j.useTransition)();if(nx())return e.children;(0,j.useEffect)(()=>{var e;o||null==(e=window.__clerk_internal_invalidateCachePromise)||e.call(window)},[o]),nk(()=>{window.__unstable__onBeforeSetActive=e=>new Promise(t=>{var r;window.__clerk_internal_invalidateCachePromise=t;let i=(null==(r=null==window?void 0:window.next)?void 0:r.version)||"";i.startsWith("13")?s(()=>{n.refresh()}):i.startsWith("15")&&"sign-out"===e?t():(0,nD.y)().then(()=>t())}),window.__unstable__onAfterSetActive=()=>{if(t)return n.refresh()}},[]);let l=nN({...e,routerPush:i,routerReplace:a});return j.createElement(nS,{options:l},j.createElement(ng,{...l},j.createElement(nU,null),j.createElement(nC,{router:"app"}),r))},nH=e=>{let{children:t,disableKeyless:r=!1,...n}=e;return nN(n).publishableKey||!nI||r?j.createElement(nV,{...n},t):j.createElement(nK,null,j.createElement(nV,{...n},t))};var n$=r(27062);let nX=()=>{if("undefined"==typeof window)return;let e=e=>{Object.keys(e).forEach(t=>{delete e[t]})};try{e(window.next.router.sdc),e(window.next.router.sbc)}catch{return}};function nG({children:e,...t}){var r;let{__unstable_invokeMiddlewareOnAuthStateChange:n=!0}=t,{push:i,replace:a}=(0,n$.useRouter)();ng.displayName="ReactClerkProvider",nk(()=>{window.__unstable__onBeforeSetActive=nX},[]),nk(()=>{window.__unstable__onAfterSetActive=()=>{n&&i(window.location.href)}},[]);let o=nN({...t,routerPush:e=>{var t;return i(t=e)},routerReplace:e=>{var t;return a(t=e)}}),s=(null==(r=t.authServerSideProps)?void 0:r.__clerk_ssr_state)||t.__clerk_ssr_state;return j.createElement(nS,{options:o},j.createElement(ng,{...o,initialState:s},j.createElement(nU,null),j.createElement(nC,{router:"pages"}),e))}t$({packageName:"@clerk/nextjs"}),rP("@clerk/nextjs");let nq=function(e){let t=(0,O.useRouter)();return j.createElement(t?nG:nH,{...e})}},30426:(e,t,r)=>{"use strict";r.d(t,{Analytics:()=>d});var n=r(50628),i=r(37811),a=()=>{window.va||(window.va=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];(window.vaq=window.vaq||[]).push(t)})};function o(){return"undefined"!=typeof window}function s(){return"production"}function l(){return(o()?window.vam:s())||"production"}function u(){return"development"===l()}function d(e){return(0,n.useEffect)(()=>{var t;e.beforeSend&&(null==(t=window.va)||t.call(window,"beforeSend",e.beforeSend))},[e.beforeSend]),(0,n.useEffect)(()=>{var t;!function(){var e;let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{debug:!0};if(!o())return;!function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"auto";if("auto"===e){window.vam=s();return}window.vam=e}(t.mode),a(),t.beforeSend&&(null==(e=window.va)||e.call(window,"beforeSend",t.beforeSend));let r=t.scriptSrc?t.scriptSrc:u()?"https://va.vercel-scripts.com/v1/script.debug.js":t.basePath?"".concat(t.basePath,"/insights/script.js"):"/_vercel/insights/script.js";if(document.head.querySelector('script[src*="'.concat(r,'"]')))return;let n=document.createElement("script");n.src=r,n.defer=!0,n.dataset.sdkn="@vercel/analytics"+(t.framework?"/".concat(t.framework):""),n.dataset.sdkv="1.5.0",t.disableAutoTrack&&(n.dataset.disableAutoTrack="1"),t.endpoint?n.dataset.endpoint=t.endpoint:t.basePath&&(n.dataset.endpoint="".concat(t.basePath,"/insights")),t.dsn&&(n.dataset.dsn=t.dsn),n.onerror=()=>{let e=u()?"Please check if any ad blockers are enabled and try again.":"Be sure to enable Web Analytics for your project and deploy again. See https://vercel.com/docs/analytics/quickstart for more information.";console.log("[Vercel Web Analytics] Failed to load script from ".concat(r,". ").concat(e))},u()&&!1===t.debug&&(n.dataset.debug="false"),document.head.appendChild(n)}({framework:e.framework||"react",basePath:null!=(t=e.basePath)?t:function(){if(void 0!==i&&void 0!==i.env)return i.env.REACT_APP_VERCEL_OBSERVABILITY_BASEPATH}(),...void 0!==e.route&&{disableAutoTrack:!0},...e})},[]),(0,n.useEffect)(()=>{e.route&&e.path&&function(e){var t;let{route:r,path:n}=e;null==(t=window.va)||t.call(window,"pageview",{route:r,path:n})}({route:e.route,path:e.path})},[e.route,e.path]),null}},31704:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(45707).A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]])},31800:(e,t,r)=>{"use strict";r.d(t,{UnmountController:()=>l});var n,i=r(37811),a={scriptHostname:("u">typeof i?i.env.NEXT_PUBLIC_VERCEL_TOOLBAR_HOST:void 0)??"https://vercel.live",ownerId:"u">typeof i?i.env.NEXT_PUBLIC_VERCEL_TOOLBAR_OWNER_ID:void 0,projectId:"u">typeof i?i.env.NEXT_PUBLIC_VERCEL_TOOLBAR_PROJECT_ID:void 0,branch:"u">typeof i?i.env.NEXT_PUBLIC_VERCEL_TOOLBAR_BRANCH:void 0,deploymentId:"u">typeof i?i.env.NEXT_PUBLIC_VERCEL_TOOLBAR_DEPLOYMENT_ID||i.env.NEXT_PUBLIC_VERCEL_DEPLOYMENT_ID||i.env.VERCEL_DEPLOYMENT_ID:void 0},o=r(50628),s=!1;function l(e){let{shouldMount:t}=e;return(0,o.useEffect)(()=>(t&&s&&function(e={}){if(typeof window>"u"||null===document.querySelector("vercel-live-feedback")){Object.assign(a,e);let t=document.createElement("script");for(let[e,r]of(t.src=`${a.scriptHostname}/_next-live/feedback/feedback.js`,t.setAttribute("data-explicit-opt-in","true"),Object.entries((a.ownerId&&a.projectId?{"data-owner-id":a.ownerId,"data-project-id":a.projectId,"data-branch":n??a.branch}:a.deploymentId?{"data-deployment-id":a.deploymentId}:void 0)??{})))r&&t.setAttribute(e,r);(document.head||document.documentElement).appendChild(t)}}(),s=!0,()=>{(typeof window>"u"?void 0:window.__vercel_toolbar)?.unmount()}),[]),null}},32143:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return a}}),r(53854);let n=r(6024);r(50628);let i=r(27062);function a(e){function t(t){return(0,n.jsx)(e,{router:(0,i.useRouter)(),...t})}return t.getInitialProps=e.getInitialProps,t.origGetInitialProps=e.origGetInitialProps,t}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},33896:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{createKey:function(){return V},default:function(){return X},matchesMiddleware:function(){return U}});let n=r(53854),i=r(59543),a=r(44390),o=r(35940),s=r(88224),l=i._(r(51546)),u=r(83031),d=r(74934),c=n._(r(78931)),h=r(61074),p=r(93687),f=r(95765),m=n._(r(90949)),g=r(84179),v=r(91246),_=r(7580);r(92004);let y=r(36550),b=r(93571),w=r(99008),E=r(16369),k=r(91824),P=r(29547),x=r(53521),S=r(41860),R=r(77955),C=r(35016),O=r(36123),j=r(73285),T=r(22067),A=r(95774),I=r(84541),M=r(99472),N=r(14110);function L(){return Object.assign(Object.defineProperty(Error("Route Cancelled"),"__NEXT_ERROR_CODE",{value:"E315",enumerable:!1,configurable:!0}),{cancelled:!0})}async function U(e){let t=await Promise.resolve(e.router.pageLoader.getMiddleware());if(!t)return!1;let{pathname:r}=(0,y.parsePath)(e.asPath),n=(0,P.hasBasePath)(r)?(0,E.removeBasePath)(r):r,i=(0,k.addBasePath)((0,b.addLocale)(n,e.locale));return t.some(e=>new RegExp(e.regexp).test(i))}function D(e){let t=(0,h.getLocationOrigin)();return e.startsWith(t)?e.substring(t.length):e}function F(e,t,r){let[n,i]=(0,x.resolveHref)(e,t,!0),a=(0,h.getLocationOrigin)(),o=n.startsWith(a),s=i&&i.startsWith(a);n=D(n),i=i?D(i):i;let l=o?n:(0,k.addBasePath)(n),u=r?D((0,x.resolveHref)(e,r)):i||n;return{url:l,as:s?u:(0,k.addBasePath)(u)}}function B(e,t){let r=(0,a.removeTrailingSlash)((0,u.denormalizePagePath)(e));return"/404"===r||"/_error"===r?e:(t.includes(r)||t.some(t=>{if((0,p.isDynamicRoute)(t)&&(0,v.getRouteRegex)(t).re.test(r))return e=t,!0}),(0,a.removeTrailingSlash)(e))}async function W(e){if(!await U(e)||!e.fetchData)return null;let t=await e.fetchData(),r=await function(e,t,r){let n={basePath:r.router.basePath,i18n:{locales:r.router.locales},trailingSlash:!1},i=t.headers.get("x-nextjs-rewrite"),s=i||t.headers.get("x-nextjs-matched-path"),l=t.headers.get(N.MATCHED_PATH_HEADER);if(!l||s||l.includes("__next_data_catchall")||l.includes("/_error")||l.includes("/404")||(s=l),s){if(s.startsWith("/")){let t=(0,f.parseRelativeUrl)(s),l=(0,R.getNextPathnameInfo)(t.pathname,{nextConfig:n,parseData:!0}),u=(0,a.removeTrailingSlash)(l.pathname);return Promise.all([r.router.pageLoader.getPageList(),(0,o.getClientBuildManifest)()]).then(n=>{let[a,{__rewrites:o}]=n,s=(0,b.addLocale)(l.pathname,l.locale);if((0,p.isDynamicRoute)(s)||!i&&a.includes((0,d.normalizeLocalePath)((0,E.removeBasePath)(s),r.router.locales).pathname)){let r=(0,R.getNextPathnameInfo)((0,f.parseRelativeUrl)(e).pathname,{nextConfig:void 0,parseData:!0});t.pathname=s=(0,k.addBasePath)(r.pathname)}{let e=(0,m.default)(s,a,o,t.query,e=>B(e,a),r.router.locales);e.matchedPage&&(t.pathname=e.parsedAs.pathname,s=t.pathname,Object.assign(t.query,e.parsedAs.query))}let c=a.includes(u)?u:B((0,d.normalizeLocalePath)((0,E.removeBasePath)(t.pathname),r.router.locales).pathname,a);if((0,p.isDynamicRoute)(c)){let e=(0,g.getRouteMatcher)((0,v.getRouteRegex)(c))(s);Object.assign(t.query,e||{})}return{type:"rewrite",parsedAs:t,resolvedHref:c}})}let t=(0,y.parsePath)(e);return Promise.resolve({type:"redirect-external",destination:""+(0,C.formatNextPathnameInfo)({...(0,R.getNextPathnameInfo)(t.pathname,{nextConfig:n,parseData:!0}),defaultLocale:r.router.defaultLocale,buildId:""})+t.query+t.hash})}let u=t.headers.get("x-nextjs-redirect");if(u){if(u.startsWith("/")){let e=(0,y.parsePath)(u),t=(0,C.formatNextPathnameInfo)({...(0,R.getNextPathnameInfo)(e.pathname,{nextConfig:n,parseData:!0}),defaultLocale:r.router.defaultLocale,buildId:""});return Promise.resolve({type:"redirect-internal",newAs:""+t+e.query+e.hash,newUrl:""+t+e.query+e.hash})}return Promise.resolve({type:"redirect-external",destination:u})}return Promise.resolve({type:"next"})}(t.dataHref,t.response,e);return{dataHref:t.dataHref,json:t.json,response:t.response,text:t.text,cacheKey:t.cacheKey,effect:r}}let z=Symbol("SSG_DATA_NOT_FOUND");function Z(e){try{return JSON.parse(e)}catch(e){return null}}function K(e){let{dataHref:t,inflightCache:r,isPrefetch:n,hasMiddleware:i,isServerRender:a,parseJSON:s,persistCache:l,isBackground:u,unstable_skipClientCache:d}=e,{href:c}=new URL(t,window.location.href),h=e=>{var u;return(function e(t,r,n){return fetch(t,{credentials:"same-origin",method:n.method||"GET",headers:Object.assign({},n.headers,{"x-nextjs-data":"1"})}).then(i=>!i.ok&&r>1&&i.status>=500?e(t,r-1,n):i)})(t,a?3:1,{headers:Object.assign({},n?{purpose:"prefetch"}:{},n&&i?{"x-middleware-prefetch":"1"}:{},{}),method:null!=(u=null==e?void 0:e.method)?u:"GET"}).then(r=>r.ok&&(null==e?void 0:e.method)==="HEAD"?{dataHref:t,response:r,text:"",json:{},cacheKey:c}:r.text().then(e=>{if(!r.ok){if(i&&[301,302,307,308].includes(r.status))return{dataHref:t,response:r,text:e,json:{},cacheKey:c};if(404===r.status){var n;if(null==(n=Z(e))?void 0:n.notFound)return{dataHref:t,json:{notFound:z},response:r,text:e,cacheKey:c}}let s=Object.defineProperty(Error("Failed to load static props"),"__NEXT_ERROR_CODE",{value:"E124",enumerable:!1,configurable:!0});throw a||(0,o.markAssetError)(s),s}return{dataHref:t,json:s?Z(e):null,response:r,text:e,cacheKey:c}})).then(e=>(l&&"no-cache"!==e.response.headers.get("x-middleware-cache")||delete r[c],e)).catch(e=>{throw d||delete r[c],("Failed to fetch"===e.message||"NetworkError when attempting to fetch resource."===e.message||"Load failed"===e.message)&&(0,o.markAssetError)(e),e})};return d&&l?h({}).then(e=>("no-cache"!==e.response.headers.get("x-middleware-cache")&&(r[c]=Promise.resolve(e)),e)):void 0!==r[c]?r[c]:r[c]=h(u?{method:"HEAD"}:{})}function V(){return Math.random().toString(36).slice(2,10)}function H(e){let{url:t,router:r}=e;if(t===(0,k.addBasePath)((0,b.addLocale)(r.asPath,r.locale)))throw Object.defineProperty(Error("Invariant: attempted to hard navigate to the same URL "+t+" "+location.href),"__NEXT_ERROR_CODE",{value:"E282",enumerable:!1,configurable:!0});window.location.href=t}let $=e=>{let{route:t,router:r}=e,n=!1,i=r.clc=()=>{n=!0};return()=>{if(n){let e=Object.defineProperty(Error('Abort fetching component for route: "'+t+'"'),"__NEXT_ERROR_CODE",{value:"E483",enumerable:!1,configurable:!0});throw e.cancelled=!0,e}i===r.clc&&(r.clc=null)}};class X{reload(){window.location.reload()}back(){window.history.back()}forward(){window.history.forward()}push(e,t,r){return void 0===r&&(r={}),{url:e,as:t}=F(this,e,t),this.change("pushState",e,t,r)}replace(e,t,r){return void 0===r&&(r={}),{url:e,as:t}=F(this,e,t),this.change("replaceState",e,t,r)}async _bfl(e,t,n,i){{if(!this._bfl_s&&!this._bfl_d){let t,a,{BloomFilter:s}=r(83901);try{({__routerFilterStatic:t,__routerFilterDynamic:a}=await (0,o.getClientBuildManifest)())}catch(t){if(console.error(t),i)return!0;return H({url:(0,k.addBasePath)((0,b.addLocale)(e,n||this.locale,this.defaultLocale)),router:this}),new Promise(()=>{})}(null==t?void 0:t.numHashes)&&(this._bfl_s=new s(t.numItems,t.errorRate),this._bfl_s.import(t)),(null==a?void 0:a.numHashes)&&(this._bfl_d=new s(a.numItems,a.errorRate),this._bfl_d.import(a))}let d=!1,c=!1;for(let{as:r,allowMatchCurrent:o}of[{as:e},{as:t}])if(r){let t=(0,a.removeTrailingSlash)(new URL(r,"http://n").pathname),h=(0,k.addBasePath)((0,b.addLocale)(t,n||this.locale));if(o||t!==(0,a.removeTrailingSlash)(new URL(this.asPath,"http://n").pathname)){var s,l,u;for(let e of(d=d||!!(null==(s=this._bfl_s)?void 0:s.contains(t))||!!(null==(l=this._bfl_s)?void 0:l.contains(h)),[t,h])){let t=e.split("/");for(let e=0;!c&&e<t.length+1;e++){let r=t.slice(0,e).join("/");if(r&&(null==(u=this._bfl_d)?void 0:u.contains(r))){c=!0;break}}}if(d||c){if(i)return!0;return H({url:(0,k.addBasePath)((0,b.addLocale)(e,n||this.locale,this.defaultLocale)),router:this}),new Promise(()=>{})}}}}return!1}async change(e,t,r,n,i){var u,d,c,x,S,R,C,T,M;let N,D;if(!(0,j.isLocalURL)(t))return H({url:t,router:this}),!1;let W=1===n._h;W||n.shallow||await this._bfl(r,void 0,n.locale);let Z=W||n._shouldResolveHref||(0,y.parsePath)(t).pathname===(0,y.parsePath)(r).pathname,K={...this.state},V=!0!==this.isReady;this.isReady=!0;let $=this.isSsr;if(W||(this.isSsr=!1),W&&this.clc)return!1;let G=K.locale;h.ST&&performance.mark("routeChange");let{shallow:q=!1,scroll:Y=!0}=n,J={shallow:q};this._inFlightRoute&&this.clc&&($||X.events.emit("routeChangeError",L(),this._inFlightRoute,J),this.clc(),this.clc=null),r=(0,k.addBasePath)((0,b.addLocale)((0,P.hasBasePath)(r)?(0,E.removeBasePath)(r):r,n.locale,this.defaultLocale));let Q=(0,w.removeLocale)((0,P.hasBasePath)(r)?(0,E.removeBasePath)(r):r,K.locale);this._inFlightRoute=r;let ee=G!==K.locale;if(!W&&this.onlyAHashChange(Q)&&!ee){K.asPath=Q,X.events.emit("hashChangeStart",r,J),this.changeState(e,t,r,{...n,scroll:!1}),Y&&this.scrollToHash(Q);try{await this.set(K,this.components[K.route],null)}catch(e){throw(0,l.default)(e)&&e.cancelled&&X.events.emit("routeChangeError",e,Q,J),e}return X.events.emit("hashChangeComplete",r,J),!0}let et=(0,f.parseRelativeUrl)(t),{pathname:er,query:en}=et;try{[N,{__rewrites:D}]=await Promise.all([this.pageLoader.getPageList(),(0,o.getClientBuildManifest)(),this.pageLoader.getMiddleware()])}catch(e){return H({url:r,router:this}),!1}this.urlIsNew(Q)||ee||(e="replaceState");let ei=r;er=er?(0,a.removeTrailingSlash)((0,E.removeBasePath)(er)):er;let ea=(0,a.removeTrailingSlash)(er),eo=r.startsWith("/")&&(0,f.parseRelativeUrl)(r).pathname;if(null==(u=this.components[er])?void 0:u.__appRouter)return H({url:r,router:this}),new Promise(()=>{});let es=!!(eo&&ea!==eo&&(!(0,p.isDynamicRoute)(ea)||!(0,g.getRouteMatcher)((0,v.getRouteRegex)(ea))(eo))),el=!n.shallow&&await U({asPath:r,locale:K.locale,router:this});if(W&&el&&(Z=!1),Z&&"/_error"!==er)if(n._shouldResolveHref=!0,r.startsWith("/")){let e=(0,m.default)((0,k.addBasePath)((0,b.addLocale)(Q,K.locale),!0),N,D,en,e=>B(e,N),this.locales);if(e.externalDest)return H({url:r,router:this}),!0;el||(ei=e.asPath),e.matchedPage&&e.resolvedHref&&(er=e.resolvedHref,et.pathname=(0,k.addBasePath)(er),el||(t=(0,_.formatWithValidation)(et)))}else et.pathname=B(er,N),et.pathname!==er&&(er=et.pathname,et.pathname=(0,k.addBasePath)(er),el||(t=(0,_.formatWithValidation)(et)));if(!(0,j.isLocalURL)(r))return H({url:r,router:this}),!1;ei=(0,w.removeLocale)((0,E.removeBasePath)(ei),K.locale),ea=(0,a.removeTrailingSlash)(er);let eu=!1;if((0,p.isDynamicRoute)(ea)){let e=(0,f.parseRelativeUrl)(ei),n=e.pathname,i=(0,v.getRouteRegex)(ea);eu=(0,g.getRouteMatcher)(i)(n);let a=ea===n,o=a?(0,I.interpolateAs)(ea,n,en):{};if(eu&&(!a||o.result))a?r=(0,_.formatWithValidation)(Object.assign({},e,{pathname:o.result,query:(0,A.omit)(en,o.params)})):Object.assign(en,eu);else{let e=Object.keys(i.groups).filter(e=>!en[e]&&!i.groups[e].optional);if(e.length>0&&!el)throw Object.defineProperty(Error((a?"The provided `href` ("+t+") value is missing query values ("+e.join(", ")+") to be interpolated properly. ":"The provided `as` value ("+n+") is incompatible with the `href` value ("+ea+"). ")+"Read more: https://nextjs.org/docs/messages/"+(a?"href-interpolation-failed":"incompatible-href-as")),"__NEXT_ERROR_CODE",{value:"E344",enumerable:!1,configurable:!0})}}W||X.events.emit("routeChangeStart",r,J);let ed="/404"===this.pathname||"/_error"===this.pathname;try{let a=await this.getRouteInfo({route:ea,pathname:er,query:en,as:r,resolvedAs:ei,routeProps:J,locale:K.locale,isPreview:K.isPreview,hasMiddleware:el,unstable_skipClientCache:n.unstable_skipClientCache,isQueryUpdating:W&&!this.isFallback,isMiddlewareRewrite:es});if(W||n.shallow||await this._bfl(r,"resolvedAs"in a?a.resolvedAs:void 0,K.locale),"route"in a&&el){ea=er=a.route||ea,J.shallow||(en=Object.assign({},a.query||{},en));let e=(0,P.hasBasePath)(et.pathname)?(0,E.removeBasePath)(et.pathname):et.pathname;if(eu&&er!==e&&Object.keys(eu).forEach(e=>{eu&&en[e]===eu[e]&&delete en[e]}),(0,p.isDynamicRoute)(er)){let e=!J.shallow&&a.resolvedAs?a.resolvedAs:(0,k.addBasePath)((0,b.addLocale)(new URL(r,location.href).pathname,K.locale),!0);(0,P.hasBasePath)(e)&&(e=(0,E.removeBasePath)(e));let t=(0,v.getRouteRegex)(er),n=(0,g.getRouteMatcher)(t)(new URL(e,location.href).pathname);n&&Object.assign(en,n)}}if("type"in a)if("redirect-internal"===a.type)return this.change(e,a.newUrl,a.newAs,n);else return H({url:a.destination,router:this}),new Promise(()=>{});let o=a.Component;if(o&&o.unstable_scriptLoader&&[].concat(o.unstable_scriptLoader()).forEach(e=>{(0,s.handleClientScriptLoad)(e.props)}),(a.__N_SSG||a.__N_SSP)&&a.props){if(a.props.pageProps&&a.props.pageProps.__N_REDIRECT){n.locale=!1;let t=a.props.pageProps.__N_REDIRECT;if(t.startsWith("/")&&!1!==a.props.pageProps.__N_REDIRECT_BASE_PATH){let r=(0,f.parseRelativeUrl)(t);r.pathname=B(r.pathname,N);let{url:i,as:a}=F(this,t,t);return this.change(e,i,a,n)}return H({url:t,router:this}),new Promise(()=>{})}if(K.isPreview=!!a.props.__N_PREVIEW,a.props.notFound===z){let e;try{await this.fetchComponent("/404"),e="/404"}catch(t){e="/_error"}if(a=await this.getRouteInfo({route:e,pathname:e,query:en,as:r,resolvedAs:ei,routeProps:{shallow:!1},locale:K.locale,isPreview:K.isPreview,isNotFound:!0}),"type"in a)throw Object.defineProperty(Error("Unexpected middleware effect on /404"),"__NEXT_ERROR_CODE",{value:"E158",enumerable:!1,configurable:!0})}}W&&"/_error"===this.pathname&&(null==(c=self.__NEXT_DATA__.props)||null==(d=c.pageProps)?void 0:d.statusCode)===500&&(null==(x=a.props)?void 0:x.pageProps)&&(a.props.pageProps.statusCode=500);let u=n.shallow&&K.route===(null!=(S=a.route)?S:ea),h=null!=(R=n.scroll)?R:!W&&!u,m=null!=i?i:h?{x:0,y:0}:null,_={...K,route:ea,pathname:er,query:en,asPath:Q,isFallback:!1};if(W&&ed){if(a=await this.getRouteInfo({route:this.pathname,pathname:this.pathname,query:en,as:r,resolvedAs:ei,routeProps:{shallow:!1},locale:K.locale,isPreview:K.isPreview,isQueryUpdating:W&&!this.isFallback}),"type"in a)throw Object.defineProperty(Error("Unexpected middleware effect on "+this.pathname),"__NEXT_ERROR_CODE",{value:"E225",enumerable:!1,configurable:!0});"/_error"===this.pathname&&(null==(T=self.__NEXT_DATA__.props)||null==(C=T.pageProps)?void 0:C.statusCode)===500&&(null==(M=a.props)?void 0:M.pageProps)&&(a.props.pageProps.statusCode=500);try{await this.set(_,a,m)}catch(e){throw(0,l.default)(e)&&e.cancelled&&X.events.emit("routeChangeError",e,Q,J),e}return!0}if(X.events.emit("beforeHistoryChange",r,J),this.changeState(e,t,r,n),!(W&&!m&&!V&&!ee&&(0,O.compareRouterStates)(_,this.state))){try{await this.set(_,a,m)}catch(e){if(e.cancelled)a.error=a.error||e;else throw e}if(a.error)throw W||X.events.emit("routeChangeError",a.error,Q,J),a.error;W||X.events.emit("routeChangeComplete",r,J),h&&/#.+$/.test(r)&&this.scrollToHash(r)}return!0}catch(e){if((0,l.default)(e)&&e.cancelled)return!1;throw e}}changeState(e,t,r,n){void 0===n&&(n={}),("pushState"!==e||(0,h.getURL)()!==r)&&(this._shallow=n.shallow,window.history[e]({url:t,as:r,options:n,__N:!0,key:this._key="pushState"!==e?this._key:V()},"",r))}async handleRouteInfoError(e,t,r,n,i,a){if(e.cancelled)throw e;if((0,o.isAssetError)(e)||a)throw X.events.emit("routeChangeError",e,n,i),H({url:n,router:this}),L();console.error(e);try{let n,{page:i,styleSheets:a}=await this.fetchComponent("/_error"),o={props:n,Component:i,styleSheets:a,err:e,error:e};if(!o.props)try{o.props=await this.getInitialProps(i,{err:e,pathname:t,query:r})}catch(e){console.error("Error in error page `getInitialProps`: ",e),o.props={}}return o}catch(e){return this.handleRouteInfoError((0,l.default)(e)?e:Object.defineProperty(Error(e+""),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0}),t,r,n,i,!0)}}async getRouteInfo(e){let{route:t,pathname:r,query:n,as:i,resolvedAs:o,routeProps:s,locale:u,hasMiddleware:c,isPreview:h,unstable_skipClientCache:p,isQueryUpdating:f,isMiddlewareRewrite:m,isNotFound:g}=e,v=t;try{var y,b,w,k;let e=this.components[v];if(s.shallow&&e&&this.route===v)return e;let t=$({route:v,router:this});c&&(e=void 0);let l=!e||"initial"in e?void 0:e,P={dataHref:this.pageLoader.getDataHref({href:(0,_.formatWithValidation)({pathname:r,query:n}),skipInterpolation:!0,asPath:g?"/404":o,locale:u}),hasMiddleware:!0,isServerRender:this.isSsr,parseJSON:!0,inflightCache:f?this.sbc:this.sdc,persistCache:!h,isPrefetch:!1,unstable_skipClientCache:p,isBackground:f},x=f&&!m?null:await W({fetchData:()=>K(P),asPath:g?"/404":o,locale:u,router:this}).catch(e=>{if(f)return null;throw e});if(x&&("/_error"===r||"/404"===r)&&(x.effect=void 0),f&&(x?x.json=self.__NEXT_DATA__.props:x={json:self.__NEXT_DATA__.props}),t(),(null==x||null==(y=x.effect)?void 0:y.type)==="redirect-internal"||(null==x||null==(b=x.effect)?void 0:b.type)==="redirect-external")return x.effect;if((null==x||null==(w=x.effect)?void 0:w.type)==="rewrite"){let t=(0,a.removeTrailingSlash)(x.effect.resolvedHref),i=await this.pageLoader.getPageList();if((!f||i.includes(t))&&(v=t,r=x.effect.resolvedHref,n={...n,...x.effect.parsedAs.query},o=(0,E.removeBasePath)((0,d.normalizeLocalePath)(x.effect.parsedAs.pathname,this.locales).pathname),e=this.components[v],s.shallow&&e&&this.route===v&&!c))return{...e,route:v}}if((0,S.isAPIRoute)(v))return H({url:i,router:this}),new Promise(()=>{});let R=l||await this.fetchComponent(v).then(e=>({Component:e.page,styleSheets:e.styleSheets,__N_SSG:e.mod.__N_SSG,__N_SSP:e.mod.__N_SSP})),C=null==x||null==(k=x.response)?void 0:k.headers.get("x-middleware-skip"),O=R.__N_SSG||R.__N_SSP;C&&(null==x?void 0:x.dataHref)&&delete this.sdc[x.dataHref];let{props:j,cacheKey:T}=await this._getData(async()=>{if(O){if((null==x?void 0:x.json)&&!C)return{cacheKey:x.cacheKey,props:x.json};let e=(null==x?void 0:x.dataHref)?x.dataHref:this.pageLoader.getDataHref({href:(0,_.formatWithValidation)({pathname:r,query:n}),asPath:o,locale:u}),t=await K({dataHref:e,isServerRender:this.isSsr,parseJSON:!0,inflightCache:C?{}:this.sdc,persistCache:!h,isPrefetch:!1,unstable_skipClientCache:p});return{cacheKey:t.cacheKey,props:t.json||{}}}return{headers:{},props:await this.getInitialProps(R.Component,{pathname:r,query:n,asPath:i,locale:u,locales:this.locales,defaultLocale:this.defaultLocale})}});return R.__N_SSP&&P.dataHref&&T&&delete this.sdc[T],this.isPreview||!R.__N_SSG||f||K(Object.assign({},P,{isBackground:!0,persistCache:!1,inflightCache:this.sbc})).catch(()=>{}),j.pageProps=Object.assign({},j.pageProps),R.props=j,R.route=v,R.query=n,R.resolvedAs=o,this.components[v]=R,R}catch(e){return this.handleRouteInfoError((0,l.getProperError)(e),r,n,i,s)}}set(e,t,r){return this.state=e,this.sub(t,this.components["/_app"].Component,r)}beforePopState(e){this._bps=e}onlyAHashChange(e){if(!this.asPath)return!1;let[t,r]=this.asPath.split("#",2),[n,i]=e.split("#",2);return!!i&&t===n&&r===i||t===n&&r!==i}scrollToHash(e){let[,t=""]=e.split("#",2);(0,M.handleSmoothScroll)(()=>{if(""===t||"top"===t)return void window.scrollTo(0,0);let e=decodeURIComponent(t),r=document.getElementById(e);if(r)return void r.scrollIntoView();let n=document.getElementsByName(e)[0];n&&n.scrollIntoView()},{onlyHashChange:this.onlyAHashChange(e)})}urlIsNew(e){return this.asPath!==e}async prefetch(e,t,r){if(void 0===t&&(t=e),void 0===r&&(r={}),(0,T.isBot)(window.navigator.userAgent))return;let n=(0,f.parseRelativeUrl)(e),i=n.pathname,{pathname:s,query:l}=n,u=s,d=await this.pageLoader.getPageList(),c=t,h=void 0!==r.locale?r.locale||void 0:this.locale,P=await U({asPath:t,locale:h,router:this});if(t.startsWith("/")){let r;({__rewrites:r}=await (0,o.getClientBuildManifest)());let i=(0,m.default)((0,k.addBasePath)((0,b.addLocale)(t,this.locale),!0),d,r,n.query,e=>B(e,d),this.locales);if(i.externalDest)return;P||(c=(0,w.removeLocale)((0,E.removeBasePath)(i.asPath),this.locale)),i.matchedPage&&i.resolvedHref&&(n.pathname=s=i.resolvedHref,P||(e=(0,_.formatWithValidation)(n)))}n.pathname=B(n.pathname,d),(0,p.isDynamicRoute)(n.pathname)&&(s=n.pathname,n.pathname=s,Object.assign(l,(0,g.getRouteMatcher)((0,v.getRouteRegex)(n.pathname))((0,y.parsePath)(t).pathname)||{}),P||(e=(0,_.formatWithValidation)(n)));let x=await W({fetchData:()=>K({dataHref:this.pageLoader.getDataHref({href:(0,_.formatWithValidation)({pathname:u,query:l}),skipInterpolation:!0,asPath:c,locale:h}),hasMiddleware:!0,isServerRender:!1,parseJSON:!0,inflightCache:this.sdc,persistCache:!this.isPreview,isPrefetch:!0}),asPath:t,locale:h,router:this});if((null==x?void 0:x.effect.type)==="rewrite"&&(n.pathname=x.effect.resolvedHref,s=x.effect.resolvedHref,l={...l,...x.effect.parsedAs.query},c=x.effect.parsedAs.pathname,e=(0,_.formatWithValidation)(n)),(null==x?void 0:x.effect.type)==="redirect-external")return;let S=(0,a.removeTrailingSlash)(s);await this._bfl(t,c,r.locale,!0)&&(this.components[i]={__appRouter:!0}),await Promise.all([this.pageLoader._isSsg(S).then(t=>!!t&&K({dataHref:(null==x?void 0:x.json)?null==x?void 0:x.dataHref:this.pageLoader.getDataHref({href:e,asPath:c,locale:h}),isServerRender:!1,parseJSON:!0,inflightCache:this.sdc,persistCache:!this.isPreview,isPrefetch:!0,unstable_skipClientCache:r.unstable_skipClientCache||r.priority&&!0}).then(()=>!1).catch(()=>!1)),this.pageLoader[r.priority?"loadPage":"prefetch"](S)])}async fetchComponent(e){let t=$({route:e,router:this});try{let r=await this.pageLoader.loadPage(e);return t(),r}catch(e){throw t(),e}}_getData(e){let t=!1,r=()=>{t=!0};return this.clc=r,e().then(e=>{if(r===this.clc&&(this.clc=null),t){let e=Object.defineProperty(Error("Loading initial props cancelled"),"__NEXT_ERROR_CODE",{value:"E405",enumerable:!1,configurable:!0});throw e.cancelled=!0,e}return e})}getInitialProps(e,t){let{Component:r}=this.components["/_app"],n=this._wrapApp(r);return t.AppTree=n,(0,h.loadGetInitialProps)(r,{AppTree:n,Component:e,router:this,ctx:t})}get route(){return this.state.route}get pathname(){return this.state.pathname}get query(){return this.state.query}get asPath(){return this.state.asPath}get locale(){return this.state.locale}get isFallback(){return this.state.isFallback}get isPreview(){return this.state.isPreview}constructor(e,t,r,{initialProps:n,pageLoader:i,App:o,wrapApp:s,Component:l,err:u,subscription:d,isFallback:c,locale:m,locales:g,defaultLocale:v,domainLocales:y,isPreview:b}){this.sdc={},this.sbc={},this.isFirstPopStateEvent=!0,this._key=V(),this.onPopState=e=>{let t,{isFirstPopStateEvent:r}=this;this.isFirstPopStateEvent=!1;let n=e.state;if(!n){let{pathname:e,query:t}=this;this.changeState("replaceState",(0,_.formatWithValidation)({pathname:(0,k.addBasePath)(e),query:t}),(0,h.getURL)());return}if(n.__NA)return void window.location.reload();if(!n.__N||r&&this.locale===n.options.locale&&n.as===this.asPath)return;let{url:i,as:a,options:o,key:s}=n;this._key=s;let{pathname:l}=(0,f.parseRelativeUrl)(i);(!this.isSsr||a!==(0,k.addBasePath)(this.asPath)||l!==(0,k.addBasePath)(this.pathname))&&(!this._bps||this._bps(n))&&this.change("replaceState",i,a,Object.assign({},o,{shallow:o.shallow&&this._shallow,locale:o.locale||this.defaultLocale,_h:0}),t)};let w=(0,a.removeTrailingSlash)(e);this.components={},"/_error"!==e&&(this.components[w]={Component:l,initial:!0,props:n,err:u,__N_SSG:n&&n.__N_SSG,__N_SSP:n&&n.__N_SSP}),this.components["/_app"]={Component:o,styleSheets:[]},this.events=X.events,this.pageLoader=i;let E=(0,p.isDynamicRoute)(e)&&self.__NEXT_DATA__.autoExport;if(this.basePath="",this.sub=d,this.clc=null,this._wrapApp=s,this.isSsr=!0,this.isLocaleDomain=!1,this.isReady=!!(self.__NEXT_DATA__.gssp||self.__NEXT_DATA__.gip||self.__NEXT_DATA__.isExperimentalCompile||self.__NEXT_DATA__.appGip&&!self.__NEXT_DATA__.gsp||!E&&!self.location.search&&0),this.state={route:w,pathname:e,query:t,asPath:E?e:r,isPreview:!!b,locale:void 0,isFallback:c},this._initialMatchesMiddlewarePromise=Promise.resolve(!1),!r.startsWith("//")){let n={locale:m},i=(0,h.getURL)();this._initialMatchesMiddlewarePromise=U({router:this,locale:m,asPath:i}).then(a=>(n._shouldResolveHref=r!==e,this.changeState("replaceState",a?i:(0,_.formatWithValidation)({pathname:(0,k.addBasePath)(e),query:t}),i,n),a))}window.addEventListener("popstate",this.onPopState)}}X.events=(0,c.default)()},35016:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"formatNextPathnameInfo",{enumerable:!0,get:function(){return s}});let n=r(44390),i=r(55113),a=r(71820),o=r(11275);function s(e){let t=(0,o.addLocale)(e.pathname,e.locale,e.buildId?void 0:e.defaultLocale,e.ignorePrefix);return(e.buildId||!e.trailingSlash)&&(t=(0,n.removeTrailingSlash)(t)),e.buildId&&(t=(0,a.addPathSuffix)((0,i.addPathPrefix)(t,"/_next/data/"+e.buildId),"/"===e.pathname?"index.json":".json")),t=(0,i.addPathPrefix)(t,e.basePath),!e.buildId&&e.trailingSlash?t.endsWith("/")?t:(0,a.addPathSuffix)(t,"/"):(0,n.removeTrailingSlash)(t)}},35619:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useRouter",{enumerable:!0,get:function(){return a}});let n=r(50628),i=r(61360);function a(){return(0,n.useContext)(i.RouterContext)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},35940:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{createRouteLoader:function(){return g},getClientBuildManifest:function(){return f},isAssetError:function(){return d},markAssetError:function(){return u}}),r(53854),r(463);let n=r(24218),i=r(59235),a=r(71347),o=r(85524);function s(e,t,r){let n,i=t.get(e);if(i)return"future"in i?i.future:Promise.resolve(i);let a=new Promise(e=>{n=e});return t.set(e,{resolve:n,future:a}),r?r().then(e=>(n(e),e)).catch(r=>{throw t.delete(e),r}):a}let l=Symbol("ASSET_LOAD_ERROR");function u(e){return Object.defineProperty(e,l,{})}function d(e){return e&&l in e}let c=function(e){try{return e=document.createElement("link"),!!window.MSInputMethodContext&&!!document.documentMode||e.relList.supports("prefetch")}catch(e){return!1}}(),h=()=>(0,a.getDeploymentIdQueryOrEmptyString)();function p(e,t,r){return new Promise((n,a)=>{let o=!1;e.then(e=>{o=!0,n(e)}).catch(a),(0,i.requestIdleCallback)(()=>setTimeout(()=>{o||a(r)},t))})}function f(){return self.__BUILD_MANIFEST?Promise.resolve(self.__BUILD_MANIFEST):p(new Promise(e=>{let t=self.__BUILD_MANIFEST_CB;self.__BUILD_MANIFEST_CB=()=>{e(self.__BUILD_MANIFEST),t&&t()}}),3800,u(Object.defineProperty(Error("Failed to load client build manifest"),"__NEXT_ERROR_CODE",{value:"E273",enumerable:!1,configurable:!0})))}function m(e,t){return f().then(r=>{if(!(t in r))throw u(Object.defineProperty(Error("Failed to lookup route: "+t),"__NEXT_ERROR_CODE",{value:"E446",enumerable:!1,configurable:!0}));let i=r[t].map(t=>e+"/_next/"+(0,o.encodeURIPath)(t));return{scripts:i.filter(e=>e.endsWith(".js")).map(e=>(0,n.__unsafeCreateTrustedScriptURL)(e)+h()),css:i.filter(e=>e.endsWith(".css")).map(e=>e+h())}})}function g(e){let t=new Map,r=new Map,n=new Map,a=new Map;function o(e){{var t;let n=r.get(e.toString());return n?n:document.querySelector('script[src^="'+e+'"]')?Promise.resolve():(r.set(e.toString(),n=new Promise((r,n)=>{(t=document.createElement("script")).onload=r,t.onerror=()=>n(u(Object.defineProperty(Error("Failed to load script: "+e),"__NEXT_ERROR_CODE",{value:"E74",enumerable:!1,configurable:!0}))),t.crossOrigin=void 0,t.src=e,document.body.appendChild(t)})),n)}}function l(e){let t=n.get(e);return t||n.set(e,t=fetch(e,{credentials:"same-origin"}).then(t=>{if(!t.ok)throw Object.defineProperty(Error("Failed to load stylesheet: "+e),"__NEXT_ERROR_CODE",{value:"E189",enumerable:!1,configurable:!0});return t.text().then(t=>({href:e,content:t}))}).catch(e=>{throw u(e)})),t}return{whenEntrypoint:e=>s(e,t),onEntrypoint(e,r){(r?Promise.resolve().then(()=>r()).then(e=>({component:e&&e.default||e,exports:e}),e=>({error:e})):Promise.resolve(void 0)).then(r=>{let n=t.get(e);n&&"resolve"in n?r&&(t.set(e,r),n.resolve(r)):(r?t.set(e,r):t.delete(e),a.delete(e))})},loadRoute(r,n){return s(r,a,()=>{let i;return p(m(e,r).then(e=>{let{scripts:n,css:i}=e;return Promise.all([t.has(r)?[]:Promise.all(n.map(o)),Promise.all(i.map(l))])}).then(e=>this.whenEntrypoint(r).then(t=>({entrypoint:t,styles:e[1]}))),3800,u(Object.defineProperty(Error("Route did not complete loading: "+r),"__NEXT_ERROR_CODE",{value:"E12",enumerable:!1,configurable:!0}))).then(e=>{let{entrypoint:t,styles:r}=e,n=Object.assign({styles:r},t);return"error"in t?t:n}).catch(e=>{if(n)throw e;return{error:e}}).finally(()=>null==i?void 0:i())})},prefetch(t){let r;return(r=navigator.connection)&&(r.saveData||/2g/.test(r.effectiveType))?Promise.resolve():m(e,t).then(e=>Promise.all(c?e.scripts.map(e=>{var t,r,n;return t=e.toString(),r="script",new Promise((e,i)=>{let a='\n      link[rel="prefetch"][href^="'+t+'"],\n      link[rel="preload"][href^="'+t+'"],\n      script[src^="'+t+'"]';if(document.querySelector(a))return e();n=document.createElement("link"),r&&(n.as=r),n.rel="prefetch",n.crossOrigin=void 0,n.onload=e,n.onerror=()=>i(u(Object.defineProperty(Error("Failed to prefetch: "+t),"__NEXT_ERROR_CODE",{value:"E268",enumerable:!1,configurable:!0}))),n.href=t,document.head.appendChild(n)})}):[])).then(()=>{(0,i.requestIdleCallback)(()=>this.loadRoute(t,!0).catch(()=>{}))}).catch(()=>{})}}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},36123:(e,t)=>{"use strict";function r(e,t){let r=Object.keys(e);if(r.length!==Object.keys(t).length)return!1;for(let n=r.length;n--;){let i=r[n];if("query"===i){let r=Object.keys(e.query);if(r.length!==Object.keys(t.query).length)return!1;for(let n=r.length;n--;){let i=r[n];if(!t.query.hasOwnProperty(i)||e.query[i]!==t.query[i])return!1}}else if(!t.hasOwnProperty(i)||e[i]!==t[i])return!1}return!0}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"compareRouterStates",{enumerable:!0,get:function(){return r}})},36741:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.dark=void 0,t.dark=(0,r(79715).experimental_createTheme)({variables:{colorBackground:"#212126",colorNeutral:"white",colorPrimary:"#ffffff",colorTextOnPrimaryBackground:"black",colorText:"white",colorInputText:"white",colorInputBackground:"#26262B"},elements:{providerIcon__apple:{filter:"invert(1)"},providerIcon__github:{filter:"invert(1)"},providerIcon__okx_wallet:{filter:"invert(1)"},activeDeviceIcon:{"--cl-chassis-bottom":"#d2d2d2","--cl-chassis-back":"#e6e6e6","--cl-chassis-screen":"#e6e6e6","--cl-screen":"#111111"}}})},40644:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){let{html:t,height:r=null,width:a=null,children:o,dataNtpc:s=""}=e;return(0,i.useEffect)(()=>{s&&performance.mark("mark_feature_usage",{detail:{feature:"next-third-parties-".concat(s)}})},[s]),(0,n.jsxs)(n.Fragment,{children:[o,t?(0,n.jsx)("div",{style:{height:null!=r?"".concat(r,"px"):"auto",width:null!=a?"".concat(a,"px"):"auto"},"data-ntpc":s,dangerouslySetInnerHTML:{__html:t}}):null]})};let n=r(6024),i=r(50628)},41860:(e,t)=>{"use strict";function r(e){return"/api"===e||!!(null==e?void 0:e.startsWith("/api/"))}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isAPIRoute",{enumerable:!0,get:function(){return r}})},45370:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(45707).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},47276:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.shadesOfPurple=void 0;let n=r(79715),i=r(36741);t.shadesOfPurple=(0,n.experimental_createTheme)({baseTheme:i.dark,variables:{colorBackground:"#3f3c77",colorPrimary:"#f8d80d",colorTextOnPrimaryBackground:"#38375f",colorInputText:"#a1fdfe",colorShimmer:"rgba(161,253,254,0.36)"}})},48957:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"workAsyncStorage",{enumerable:!0,get:function(){return n.workAsyncStorageInstance}});let n=r(69555)},51702:(e,t,r)=>{e.exports=r(35619)},53208:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removePathPrefix",{enumerable:!0,get:function(){return i}});let n=r(28552);function i(e,t){if(!(0,n.pathHasPrefix)(e,t))return e;let r=e.slice(t.length);return r.startsWith("/")?r:"/"+r}},53521:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"resolveHref",{enumerable:!0,get:function(){return c}});let n=r(78756),i=r(7580),a=r(95774),o=r(61074),s=r(7749),l=r(73285),u=r(86325),d=r(84541);function c(e,t,r){let c,h="string"==typeof t?t:(0,i.formatWithValidation)(t),p=h.match(/^[a-zA-Z]{1,}:\/\//),f=p?h.slice(p[0].length):h;if((f.split("?",1)[0]||"").match(/(\/\/|\\)/)){console.error("Invalid href '"+h+"' passed to next/router in page: '"+e.pathname+"'. Repeated forward-slashes (//) or backslashes \\ are not valid in the href.");let t=(0,o.normalizeRepeatedSlashes)(f);h=(p?p[0]:"")+t}if(!(0,l.isLocalURL)(h))return r?[h]:h;try{c=new URL(h.startsWith("#")?e.asPath:e.pathname,"http://n")}catch(e){c=new URL("/","http://n")}try{let e=new URL(h,c);e.pathname=(0,s.normalizePathTrailingSlash)(e.pathname);let t="";if((0,u.isDynamicRoute)(e.pathname)&&e.searchParams&&r){let r=(0,n.searchParamsToUrlQuery)(e.searchParams),{result:o,params:s}=(0,d.interpolateAs)(e.pathname,e.pathname,r);o&&(t=(0,i.formatWithValidation)({pathname:o,hash:e.hash,query:(0,a.omit)(r,s)}))}let o=e.origin===c.origin?e.href.slice(e.origin.length):e.href;return r?[o,t||o]:o}catch(e){return r?[h]:h}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},59235:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{cancelIdleCallback:function(){return n},requestIdleCallback:function(){return r}});let r="undefined"!=typeof self&&self.requestIdleCallback&&self.requestIdleCallback.bind(window)||function(e){let t=Date.now();return self.setTimeout(function(){e({didTimeout:!1,timeRemaining:function(){return Math.max(0,50-(Date.now()-t))}})},1)},n="undefined"!=typeof self&&self.cancelIdleCallback&&self.cancelIdleCallback.bind(window)||function(e){return clearTimeout(e)};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},60006:(e,t,r)=>{"use strict";function n(e){let{moduleIds:t}=e;return null}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PreloadChunks",{enumerable:!0,get:function(){return n}}),r(6024),r(6341),r(48957),r(85524)},60392:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(45707).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},62311:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let n=r(88237);n.__exportStar(r(36741),t),n.__exportStar(r(47276),t),n.__exportStar(r(83424),t),n.__exportStar(r(19271),t)},63680:(e,t,r)=>{"use strict";r.d(t,{bL:()=>l});var n=r(50628),i=r(64826),a=r(6024),o=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),s=n.forwardRef((e,t)=>(0,a.jsx)(i.sG.span,{...e,ref:t,style:{...o,...e.style}}));s.displayName="VisuallyHidden";var l=s},67461:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.sendGTMEvent=void 0,t.GoogleTagManager=function(e){let{gtmId:t,gtmScriptUrl:r="https://www.googletagmanager.com/gtm.js",dataLayerName:s="dataLayer",auth:l,preview:u,dataLayer:d,nonce:c}=e;o=s;let h="dataLayer"!==s?"&l=".concat(s):"";return(0,i.useEffect)(()=>{performance.mark("mark_feature_usage",{detail:{feature:"next-third-parties-gtm"}})},[]),(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(a.default,{id:"_next-gtm-init",dangerouslySetInnerHTML:{__html:"\n      (function(w,l){\n        w[l]=w[l]||[];\n        w[l].push({'gtm.start': new Date().getTime(),event:'gtm.js'});\n        ".concat(d?"w[l].push(".concat(JSON.stringify(d),")"):"","\n      })(window,'").concat(s,"');")},nonce:c}),(0,n.jsx)(a.default,{id:"_next-gtm","data-ntpc":"GTM",src:"".concat(r,"?id=").concat(t).concat(h).concat(l?"&gtm_auth=".concat(l):"").concat(u?"&gtm_preview=".concat(u,"&gtm_cookies_win=x"):""),nonce:c})]})};let n=r(6024),i=r(50628),a=function(e){return e&&e.__esModule?e:{default:e}}(r(70787)),o="dataLayer";t.sendGTMEvent=(e,t)=>{let r=t||o;window[r]=window[r]||[],window[r].push(e)}},69555:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"workAsyncStorageInstance",{enumerable:!0,get:function(){return n}});let n=(0,r(4937).createAsyncLocalStorage)()},70787:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i.a});var n=r(88224),i=r.n(n),a={};for(let e in n)"default"!==e&&(a[e]=()=>n[e]);r.d(t,a)},70988:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{callServer:function(){return n.callServer},createServerReference:function(){return a},findSourceMapURL:function(){return i.findSourceMapURL}});let n=r(20091),i=r(39763),a=r(20240).createServerReference},71646:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab="//");var t={};(()=>{t.parse=function(t,r){if("string"!=typeof t)throw TypeError("argument str must be a string");for(var i={},a=t.split(n),o=(r||{}).decode||e,s=0;s<a.length;s++){var l=a[s],u=l.indexOf("=");if(!(u<0)){var d=l.substr(0,u).trim(),c=l.substr(++u,l.length).trim();'"'==c[0]&&(c=c.slice(1,-1)),void 0==i[d]&&(i[d]=function(e,t){try{return t(e)}catch(t){return e}}(c,o))}}return i},t.serialize=function(e,t,n){var a=n||{},o=a.encode||r;if("function"!=typeof o)throw TypeError("option encode is invalid");if(!i.test(e))throw TypeError("argument name is invalid");var s=o(t);if(s&&!i.test(s))throw TypeError("argument val is invalid");var l=e+"="+s;if(null!=a.maxAge){var u=a.maxAge-0;if(isNaN(u)||!isFinite(u))throw TypeError("option maxAge is invalid");l+="; Max-Age="+Math.floor(u)}if(a.domain){if(!i.test(a.domain))throw TypeError("option domain is invalid");l+="; Domain="+a.domain}if(a.path){if(!i.test(a.path))throw TypeError("option path is invalid");l+="; Path="+a.path}if(a.expires){if("function"!=typeof a.expires.toUTCString)throw TypeError("option expires is invalid");l+="; Expires="+a.expires.toUTCString()}if(a.httpOnly&&(l+="; HttpOnly"),a.secure&&(l+="; Secure"),a.sameSite)switch("string"==typeof a.sameSite?a.sameSite.toLowerCase():a.sameSite){case!0:case"strict":l+="; SameSite=Strict";break;case"lax":l+="; SameSite=Lax";break;case"none":l+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return l};var e=decodeURIComponent,r=encodeURIComponent,n=/; */,i=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),e.exports=t})()},71820:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addPathSuffix",{enumerable:!0,get:function(){return i}});let n=r(36550);function i(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:i,hash:a}=(0,n.parsePath)(e);return""+r+t+i+a}},71885:(e,t,r)=>{"use strict";e.exports=r(99512)},72881:(e,t,r)=>{"use strict";r.d(t,{D:()=>d,ThemeProvider:()=>c});var n=r(50628),i=(e,t,r,n,i,a,o,s)=>{let l=document.documentElement,u=["light","dark"];function d(t){var r;(Array.isArray(e)?e:[e]).forEach(e=>{let r="class"===e,n=r&&a?i.map(e=>a[e]||e):i;r?(l.classList.remove(...n),l.classList.add(a&&a[t]?a[t]:t)):l.setAttribute(e,t)}),r=t,s&&u.includes(r)&&(l.style.colorScheme=r)}if(n)d(n);else try{let e=localStorage.getItem(t)||r,n=o&&"system"===e?window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light":e;d(n)}catch(e){}},a=["light","dark"],o="(prefers-color-scheme: dark)",s="undefined"==typeof window,l=n.createContext(void 0),u={setTheme:e=>{},themes:[]},d=()=>{var e;return null!=(e=n.useContext(l))?e:u},c=e=>n.useContext(l)?n.createElement(n.Fragment,null,e.children):n.createElement(p,{...e}),h=["light","dark"],p=e=>{let{forcedTheme:t,disableTransitionOnChange:r=!1,enableSystem:i=!0,enableColorScheme:s=!0,storageKey:u="theme",themes:d=h,defaultTheme:c=i?"system":"light",attribute:p="data-theme",value:_,children:y,nonce:b,scriptProps:w}=e,[E,k]=n.useState(()=>m(u,c)),[P,x]=n.useState(()=>"system"===E?v():E),S=_?Object.values(_):d,R=n.useCallback(e=>{let t=e;if(!t)return;"system"===e&&i&&(t=v());let n=_?_[t]:t,o=r?g(b):null,l=document.documentElement,u=e=>{"class"===e?(l.classList.remove(...S),n&&l.classList.add(n)):e.startsWith("data-")&&(n?l.setAttribute(e,n):l.removeAttribute(e))};if(Array.isArray(p)?p.forEach(u):u(p),s){let e=a.includes(c)?c:null,r=a.includes(t)?t:e;l.style.colorScheme=r}null==o||o()},[b]),C=n.useCallback(e=>{let t="function"==typeof e?e(E):e;k(t);try{localStorage.setItem(u,t)}catch(e){}},[E]),O=n.useCallback(e=>{x(v(e)),"system"===E&&i&&!t&&R("system")},[E,t]);n.useEffect(()=>{let e=window.matchMedia(o);return e.addListener(O),O(e),()=>e.removeListener(O)},[O]),n.useEffect(()=>{let e=e=>{e.key===u&&(e.newValue?k(e.newValue):C(c))};return window.addEventListener("storage",e),()=>window.removeEventListener("storage",e)},[C]),n.useEffect(()=>{R(null!=t?t:E)},[t,E]);let j=n.useMemo(()=>({theme:E,setTheme:C,forcedTheme:t,resolvedTheme:"system"===E?P:E,themes:i?[...d,"system"]:d,systemTheme:i?P:void 0}),[E,C,t,P,i,d]);return n.createElement(l.Provider,{value:j},n.createElement(f,{forcedTheme:t,storageKey:u,attribute:p,enableSystem:i,enableColorScheme:s,defaultTheme:c,value:_,themes:d,nonce:b,scriptProps:w}),y)},f=n.memo(e=>{let{forcedTheme:t,storageKey:r,attribute:a,enableSystem:o,enableColorScheme:s,defaultTheme:l,value:u,themes:d,nonce:c,scriptProps:h}=e,p=JSON.stringify([a,r,l,t,d,u,o,s]).slice(1,-1);return n.createElement("script",{...h,suppressHydrationWarning:!0,nonce:"undefined"==typeof window?c:"",dangerouslySetInnerHTML:{__html:"(".concat(i.toString(),")(").concat(p,")")}})}),m=(e,t)=>{let r;if(!s){try{r=localStorage.getItem(e)||void 0}catch(e){}return r||t}},g=e=>{let t=document.createElement("style");return e&&t.setAttribute("nonce",e),t.appendChild(document.createTextNode("*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(t),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(t)},1)}},v=e=>(e||(e=window.matchMedia(o)),e.matches?"dark":"light")},74320:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getPathMatch",{enumerable:!0,get:function(){return i}});let n=r(83824);function i(e,t){let r=[],i=(0,n.pathToRegexp)(e,r,{delimiter:"/",sensitive:"boolean"==typeof(null==t?void 0:t.sensitive)&&t.sensitive,strict:null==t?void 0:t.strict}),a=(0,n.regexpToFunction)((null==t?void 0:t.regexModifier)?new RegExp(t.regexModifier(i.source),i.flags):i,r);return(e,n)=>{if("string"!=typeof e)return!1;let i=a(e);if(!i)return!1;if(null==t?void 0:t.removeUnnamedParams)for(let e of r)"number"==typeof e.name&&delete i.params[e.name];return{...n,...i.params}}}},74822:(e,t,r)=>{"use strict";let n;r.d(t,{z:()=>d});var i,a,o,s,l,u,d={};r.r(d),r.d(d,{BRAND:()=>eL,DIRTY:()=>x,EMPTY_PATH:()=>w,INVALID:()=>P,NEVER:()=>t_,OK:()=>S,ParseStatus:()=>k,Schema:()=>L,ZodAny:()=>eu,ZodArray:()=>ep,ZodBigInt:()=>en,ZodBoolean:()=>ei,ZodBranded:()=>eU,ZodCatch:()=>eM,ZodDate:()=>ea,ZodDefault:()=>eI,ZodDiscriminatedUnion:()=>ev,ZodEffects:()=>ej,ZodEnum:()=>eR,ZodError:()=>m,ZodFirstPartyTypeKind:()=>u,ZodFunction:()=>ek,ZodIntersection:()=>e_,ZodIssueCode:()=>p,ZodLazy:()=>eP,ZodLiteral:()=>ex,ZodMap:()=>ew,ZodNaN:()=>eN,ZodNativeEnum:()=>eC,ZodNever:()=>ec,ZodNull:()=>el,ZodNullable:()=>eA,ZodNumber:()=>er,ZodObject:()=>ef,ZodOptional:()=>eT,ZodParsedType:()=>c,ZodPipeline:()=>eD,ZodPromise:()=>eO,ZodReadonly:()=>eF,ZodRecord:()=>eb,ZodSchema:()=>L,ZodSet:()=>eE,ZodString:()=>et,ZodSymbol:()=>eo,ZodTransformer:()=>ej,ZodTuple:()=>ey,ZodType:()=>L,ZodUndefined:()=>es,ZodUnion:()=>em,ZodUnknown:()=>ed,ZodVoid:()=>eh,addIssueToContext:()=>E,any:()=>eQ,array:()=>e4,bigint:()=>e$,boolean:()=>eX,coerce:()=>tv,custom:()=>eW,date:()=>eG,datetimeRegex:()=>ee,defaultErrorMap:()=>g,discriminatedUnion:()=>e7,effect:()=>tu,enum:()=>to,function:()=>tn,getErrorMap:()=>y,getParsedType:()=>h,instanceof:()=>eZ,intersection:()=>e6,isAborted:()=>R,isAsync:()=>j,isDirty:()=>C,isValid:()=>O,late:()=>ez,lazy:()=>ti,literal:()=>ta,makeIssue:()=>b,map:()=>tt,nan:()=>eH,nativeEnum:()=>ts,never:()=>e1,null:()=>eJ,nullable:()=>tc,number:()=>eV,object:()=>e5,objectUtil:()=>a,oboolean:()=>tg,onumber:()=>tm,optional:()=>td,ostring:()=>tf,pipeline:()=>tp,preprocess:()=>th,promise:()=>tl,quotelessJson:()=>f,record:()=>te,set:()=>tr,setErrorMap:()=>_,strictObject:()=>e9,string:()=>eK,symbol:()=>eq,transformer:()=>tu,tuple:()=>e8,undefined:()=>eY,union:()=>e3,unknown:()=>e0,util:()=>i,void:()=>e2}),function(e){e.assertEqual=e=>{},e.assertIs=function(e){},e.assertNever=function(e){throw Error()},e.arrayToEnum=e=>{let t={};for(let r of e)t[r]=r;return t},e.getValidEnumValues=t=>{let r=e.objectKeys(t).filter(e=>"number"!=typeof t[t[e]]),n={};for(let e of r)n[e]=t[e];return e.objectValues(n)},e.objectValues=t=>e.objectKeys(t).map(function(e){return t[e]}),e.objectKeys="function"==typeof Object.keys?e=>Object.keys(e):e=>{let t=[];for(let r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.push(r);return t},e.find=(e,t)=>{for(let r of e)if(t(r))return r},e.isInteger="function"==typeof Number.isInteger?e=>Number.isInteger(e):e=>"number"==typeof e&&Number.isFinite(e)&&Math.floor(e)===e,e.joinValues=function(e,t=" | "){return e.map(e=>"string"==typeof e?`'${e}'`:e).join(t)},e.jsonStringifyReplacer=(e,t)=>"bigint"==typeof t?t.toString():t}(i||(i={})),(a||(a={})).mergeShapes=(e,t)=>({...e,...t});let c=i.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),h=e=>{switch(typeof e){case"undefined":return c.undefined;case"string":return c.string;case"number":return Number.isNaN(e)?c.nan:c.number;case"boolean":return c.boolean;case"function":return c.function;case"bigint":return c.bigint;case"symbol":return c.symbol;case"object":if(Array.isArray(e))return c.array;if(null===e)return c.null;if(e.then&&"function"==typeof e.then&&e.catch&&"function"==typeof e.catch)return c.promise;if("undefined"!=typeof Map&&e instanceof Map)return c.map;if("undefined"!=typeof Set&&e instanceof Set)return c.set;if("undefined"!=typeof Date&&e instanceof Date)return c.date;return c.object;default:return c.unknown}},p=i.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]),f=e=>JSON.stringify(e,null,2).replace(/"([^"]+)":/g,"$1:");class m extends Error{get errors(){return this.issues}constructor(e){super(),this.issues=[],this.addIssue=e=>{this.issues=[...this.issues,e]},this.addIssues=(e=[])=>{this.issues=[...this.issues,...e]};let t=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,t):this.__proto__=t,this.name="ZodError",this.issues=e}format(e){let t=e||function(e){return e.message},r={_errors:[]},n=e=>{for(let i of e.issues)if("invalid_union"===i.code)i.unionErrors.map(n);else if("invalid_return_type"===i.code)n(i.returnTypeError);else if("invalid_arguments"===i.code)n(i.argumentsError);else if(0===i.path.length)r._errors.push(t(i));else{let e=r,n=0;for(;n<i.path.length;){let r=i.path[n];n===i.path.length-1?(e[r]=e[r]||{_errors:[]},e[r]._errors.push(t(i))):e[r]=e[r]||{_errors:[]},e=e[r],n++}}};return n(this),r}static assert(e){if(!(e instanceof m))throw Error(`Not a ZodError: ${e}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,i.jsonStringifyReplacer,2)}get isEmpty(){return 0===this.issues.length}flatten(e=e=>e.message){let t={},r=[];for(let n of this.issues)n.path.length>0?(t[n.path[0]]=t[n.path[0]]||[],t[n.path[0]].push(e(n))):r.push(e(n));return{formErrors:r,fieldErrors:t}}get formErrors(){return this.flatten()}}m.create=e=>new m(e);let g=(e,t)=>{let r;switch(e.code){case p.invalid_type:r=e.received===c.undefined?"Required":`Expected ${e.expected}, received ${e.received}`;break;case p.invalid_literal:r=`Invalid literal value, expected ${JSON.stringify(e.expected,i.jsonStringifyReplacer)}`;break;case p.unrecognized_keys:r=`Unrecognized key(s) in object: ${i.joinValues(e.keys,", ")}`;break;case p.invalid_union:r="Invalid input";break;case p.invalid_union_discriminator:r=`Invalid discriminator value. Expected ${i.joinValues(e.options)}`;break;case p.invalid_enum_value:r=`Invalid enum value. Expected ${i.joinValues(e.options)}, received '${e.received}'`;break;case p.invalid_arguments:r="Invalid function arguments";break;case p.invalid_return_type:r="Invalid function return type";break;case p.invalid_date:r="Invalid date";break;case p.invalid_string:"object"==typeof e.validation?"includes"in e.validation?(r=`Invalid input: must include "${e.validation.includes}"`,"number"==typeof e.validation.position&&(r=`${r} at one or more positions greater than or equal to ${e.validation.position}`)):"startsWith"in e.validation?r=`Invalid input: must start with "${e.validation.startsWith}"`:"endsWith"in e.validation?r=`Invalid input: must end with "${e.validation.endsWith}"`:i.assertNever(e.validation):r="regex"!==e.validation?`Invalid ${e.validation}`:"Invalid";break;case p.too_small:r="array"===e.type?`Array must contain ${e.exact?"exactly":e.inclusive?"at least":"more than"} ${e.minimum} element(s)`:"string"===e.type?`String must contain ${e.exact?"exactly":e.inclusive?"at least":"over"} ${e.minimum} character(s)`:"number"===e.type?`Number must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${e.minimum}`:"date"===e.type?`Date must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(e.minimum))}`:"Invalid input";break;case p.too_big:r="array"===e.type?`Array must contain ${e.exact?"exactly":e.inclusive?"at most":"less than"} ${e.maximum} element(s)`:"string"===e.type?`String must contain ${e.exact?"exactly":e.inclusive?"at most":"under"} ${e.maximum} character(s)`:"number"===e.type?`Number must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:"bigint"===e.type?`BigInt must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:"date"===e.type?`Date must be ${e.exact?"exactly":e.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(e.maximum))}`:"Invalid input";break;case p.custom:r="Invalid input";break;case p.invalid_intersection_types:r="Intersection results could not be merged";break;case p.not_multiple_of:r=`Number must be a multiple of ${e.multipleOf}`;break;case p.not_finite:r="Number must be finite";break;default:r=t.defaultError,i.assertNever(e)}return{message:r}},v=g;function _(e){v=e}function y(){return v}let b=e=>{let{data:t,path:r,errorMaps:n,issueData:i}=e,a=[...r,...i.path||[]],o={...i,path:a};if(void 0!==i.message)return{...i,path:a,message:i.message};let s="";for(let e of n.filter(e=>!!e).slice().reverse())s=e(o,{data:t,defaultError:s}).message;return{...i,path:a,message:s}},w=[];function E(e,t){let r=v,n=b({issueData:t,data:e.data,path:e.path,errorMaps:[e.common.contextualErrorMap,e.schemaErrorMap,r,r===g?void 0:g].filter(e=>!!e)});e.common.issues.push(n)}class k{constructor(){this.value="valid"}dirty(){"valid"===this.value&&(this.value="dirty")}abort(){"aborted"!==this.value&&(this.value="aborted")}static mergeArray(e,t){let r=[];for(let n of t){if("aborted"===n.status)return P;"dirty"===n.status&&e.dirty(),r.push(n.value)}return{status:e.value,value:r}}static async mergeObjectAsync(e,t){let r=[];for(let e of t){let t=await e.key,n=await e.value;r.push({key:t,value:n})}return k.mergeObjectSync(e,r)}static mergeObjectSync(e,t){let r={};for(let n of t){let{key:t,value:i}=n;if("aborted"===t.status||"aborted"===i.status)return P;"dirty"===t.status&&e.dirty(),"dirty"===i.status&&e.dirty(),"__proto__"!==t.value&&(void 0!==i.value||n.alwaysSet)&&(r[t.value]=i.value)}return{status:e.value,value:r}}}let P=Object.freeze({status:"aborted"}),x=e=>({status:"dirty",value:e}),S=e=>({status:"valid",value:e}),R=e=>"aborted"===e.status,C=e=>"dirty"===e.status,O=e=>"valid"===e.status,j=e=>"undefined"!=typeof Promise&&e instanceof Promise;!function(e){e.errToObj=e=>"string"==typeof e?{message:e}:e||{},e.toString=e=>"string"==typeof e?e:e?.message}(o||(o={}));var T=function(e,t,r,n){if("a"===r&&!n)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!n:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===r?n:"a"===r?n.call(e):n?n.value:t.get(e)},A=function(e,t,r,n,i){if("m"===n)throw TypeError("Private method is not writable");if("a"===n&&!i)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!i:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===n?i.call(e,r):i?i.value=r:t.set(e,r),r};class I{constructor(e,t,r,n){this._cachedPath=[],this.parent=e,this.data=t,this._path=r,this._key=n}get path(){return this._cachedPath.length||(Array.isArray(this._key)?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}let M=(e,t)=>{if(O(t))return{success:!0,data:t.value};if(!e.common.issues.length)throw Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;let t=new m(e.common.issues);return this._error=t,this._error}}};function N(e){if(!e)return{};let{errorMap:t,invalid_type_error:r,required_error:n,description:i}=e;if(t&&(r||n))throw Error('Can\'t use "invalid_type_error" or "required_error" in conjunction with custom error map.');return t?{errorMap:t,description:i}:{errorMap:(t,i)=>{let{message:a}=e;return"invalid_enum_value"===t.code?{message:a??i.defaultError}:void 0===i.data?{message:a??n??i.defaultError}:"invalid_type"!==t.code?{message:i.defaultError}:{message:a??r??i.defaultError}},description:i}}class L{get description(){return this._def.description}_getType(e){return h(e.data)}_getOrReturnCtx(e,t){return t||{common:e.parent.common,data:e.data,parsedType:h(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}_processInputParams(e){return{status:new k,ctx:{common:e.parent.common,data:e.data,parsedType:h(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}}_parseSync(e){let t=this._parse(e);if(j(t))throw Error("Synchronous parse encountered promise.");return t}_parseAsync(e){return Promise.resolve(this._parse(e))}parse(e,t){let r=this.safeParse(e,t);if(r.success)return r.data;throw r.error}safeParse(e,t){let r={common:{issues:[],async:t?.async??!1,contextualErrorMap:t?.errorMap},path:t?.path||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:h(e)},n=this._parseSync({data:e,path:r.path,parent:r});return M(r,n)}"~validate"(e){let t={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:h(e)};if(!this["~standard"].async)try{let r=this._parseSync({data:e,path:[],parent:t});return O(r)?{value:r.value}:{issues:t.common.issues}}catch(e){e?.message?.toLowerCase()?.includes("encountered")&&(this["~standard"].async=!0),t.common={issues:[],async:!0}}return this._parseAsync({data:e,path:[],parent:t}).then(e=>O(e)?{value:e.value}:{issues:t.common.issues})}async parseAsync(e,t){let r=await this.safeParseAsync(e,t);if(r.success)return r.data;throw r.error}async safeParseAsync(e,t){let r={common:{issues:[],contextualErrorMap:t?.errorMap,async:!0},path:t?.path||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:h(e)},n=this._parse({data:e,path:r.path,parent:r});return M(r,await (j(n)?n:Promise.resolve(n)))}refine(e,t){let r=e=>"string"==typeof t||void 0===t?{message:t}:"function"==typeof t?t(e):t;return this._refinement((t,n)=>{let i=e(t),a=()=>n.addIssue({code:p.custom,...r(t)});return"undefined"!=typeof Promise&&i instanceof Promise?i.then(e=>!!e||(a(),!1)):!!i||(a(),!1)})}refinement(e,t){return this._refinement((r,n)=>!!e(r)||(n.addIssue("function"==typeof t?t(r,n):t),!1))}_refinement(e){return new ej({schema:this,typeName:u.ZodEffects,effect:{type:"refinement",refinement:e}})}superRefine(e){return this._refinement(e)}constructor(e){this.spa=this.safeParseAsync,this._def=e,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:e=>this["~validate"](e)}}optional(){return eT.create(this,this._def)}nullable(){return eA.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return ep.create(this)}promise(){return eO.create(this,this._def)}or(e){return em.create([this,e],this._def)}and(e){return e_.create(this,e,this._def)}transform(e){return new ej({...N(this._def),schema:this,typeName:u.ZodEffects,effect:{type:"transform",transform:e}})}default(e){return new eI({...N(this._def),innerType:this,defaultValue:"function"==typeof e?e:()=>e,typeName:u.ZodDefault})}brand(){return new eU({typeName:u.ZodBranded,type:this,...N(this._def)})}catch(e){return new eM({...N(this._def),innerType:this,catchValue:"function"==typeof e?e:()=>e,typeName:u.ZodCatch})}describe(e){return new this.constructor({...this._def,description:e})}pipe(e){return eD.create(this,e)}readonly(){return eF.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}let U=/^c[^\s-]{8,}$/i,D=/^[0-9a-z]+$/,F=/^[0-9A-HJKMNP-TV-Z]{26}$/i,B=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,W=/^[a-z0-9_-]{21}$/i,z=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,Z=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,K=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i,V=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,H=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,$=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,X=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,G=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,q=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,Y="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",J=RegExp(`^${Y}$`);function Q(e){let t="[0-5]\\d";e.precision?t=`${t}\\.\\d{${e.precision}}`:null==e.precision&&(t=`${t}(\\.\\d+)?`);let r=e.precision?"+":"?";return`([01]\\d|2[0-3]):[0-5]\\d(:${t})${r}`}function ee(e){let t=`${Y}T${Q(e)}`,r=[];return r.push(e.local?"Z?":"Z"),e.offset&&r.push("([+-]\\d{2}:?\\d{2})"),t=`${t}(${r.join("|")})`,RegExp(`^${t}$`)}class et extends L{_parse(e){var t,r,a,o;let s;if(this._def.coerce&&(e.data=String(e.data)),this._getType(e)!==c.string){let t=this._getOrReturnCtx(e);return E(t,{code:p.invalid_type,expected:c.string,received:t.parsedType}),P}let l=new k;for(let u of this._def.checks)if("min"===u.kind)e.data.length<u.value&&(E(s=this._getOrReturnCtx(e,s),{code:p.too_small,minimum:u.value,type:"string",inclusive:!0,exact:!1,message:u.message}),l.dirty());else if("max"===u.kind)e.data.length>u.value&&(E(s=this._getOrReturnCtx(e,s),{code:p.too_big,maximum:u.value,type:"string",inclusive:!0,exact:!1,message:u.message}),l.dirty());else if("length"===u.kind){let t=e.data.length>u.value,r=e.data.length<u.value;(t||r)&&(s=this._getOrReturnCtx(e,s),t?E(s,{code:p.too_big,maximum:u.value,type:"string",inclusive:!0,exact:!0,message:u.message}):r&&E(s,{code:p.too_small,minimum:u.value,type:"string",inclusive:!0,exact:!0,message:u.message}),l.dirty())}else if("email"===u.kind)K.test(e.data)||(E(s=this._getOrReturnCtx(e,s),{validation:"email",code:p.invalid_string,message:u.message}),l.dirty());else if("emoji"===u.kind)n||(n=RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),n.test(e.data)||(E(s=this._getOrReturnCtx(e,s),{validation:"emoji",code:p.invalid_string,message:u.message}),l.dirty());else if("uuid"===u.kind)B.test(e.data)||(E(s=this._getOrReturnCtx(e,s),{validation:"uuid",code:p.invalid_string,message:u.message}),l.dirty());else if("nanoid"===u.kind)W.test(e.data)||(E(s=this._getOrReturnCtx(e,s),{validation:"nanoid",code:p.invalid_string,message:u.message}),l.dirty());else if("cuid"===u.kind)U.test(e.data)||(E(s=this._getOrReturnCtx(e,s),{validation:"cuid",code:p.invalid_string,message:u.message}),l.dirty());else if("cuid2"===u.kind)D.test(e.data)||(E(s=this._getOrReturnCtx(e,s),{validation:"cuid2",code:p.invalid_string,message:u.message}),l.dirty());else if("ulid"===u.kind)F.test(e.data)||(E(s=this._getOrReturnCtx(e,s),{validation:"ulid",code:p.invalid_string,message:u.message}),l.dirty());else if("url"===u.kind)try{new URL(e.data)}catch{E(s=this._getOrReturnCtx(e,s),{validation:"url",code:p.invalid_string,message:u.message}),l.dirty()}else"regex"===u.kind?(u.regex.lastIndex=0,u.regex.test(e.data)||(E(s=this._getOrReturnCtx(e,s),{validation:"regex",code:p.invalid_string,message:u.message}),l.dirty())):"trim"===u.kind?e.data=e.data.trim():"includes"===u.kind?e.data.includes(u.value,u.position)||(E(s=this._getOrReturnCtx(e,s),{code:p.invalid_string,validation:{includes:u.value,position:u.position},message:u.message}),l.dirty()):"toLowerCase"===u.kind?e.data=e.data.toLowerCase():"toUpperCase"===u.kind?e.data=e.data.toUpperCase():"startsWith"===u.kind?e.data.startsWith(u.value)||(E(s=this._getOrReturnCtx(e,s),{code:p.invalid_string,validation:{startsWith:u.value},message:u.message}),l.dirty()):"endsWith"===u.kind?e.data.endsWith(u.value)||(E(s=this._getOrReturnCtx(e,s),{code:p.invalid_string,validation:{endsWith:u.value},message:u.message}),l.dirty()):"datetime"===u.kind?ee(u).test(e.data)||(E(s=this._getOrReturnCtx(e,s),{code:p.invalid_string,validation:"datetime",message:u.message}),l.dirty()):"date"===u.kind?J.test(e.data)||(E(s=this._getOrReturnCtx(e,s),{code:p.invalid_string,validation:"date",message:u.message}),l.dirty()):"time"===u.kind?RegExp(`^${Q(u)}$`).test(e.data)||(E(s=this._getOrReturnCtx(e,s),{code:p.invalid_string,validation:"time",message:u.message}),l.dirty()):"duration"===u.kind?Z.test(e.data)||(E(s=this._getOrReturnCtx(e,s),{validation:"duration",code:p.invalid_string,message:u.message}),l.dirty()):"ip"===u.kind?(t=e.data,!(("v4"===(r=u.version)||!r)&&V.test(t)||("v6"===r||!r)&&$.test(t))&&1&&(E(s=this._getOrReturnCtx(e,s),{validation:"ip",code:p.invalid_string,message:u.message}),l.dirty())):"jwt"===u.kind?!function(e,t){if(!z.test(e))return!1;try{let[r]=e.split("."),n=r.replace(/-/g,"+").replace(/_/g,"/").padEnd(r.length+(4-r.length%4)%4,"="),i=JSON.parse(atob(n));if("object"!=typeof i||null===i||"typ"in i&&i?.typ!=="JWT"||!i.alg||t&&i.alg!==t)return!1;return!0}catch{return!1}}(e.data,u.alg)&&(E(s=this._getOrReturnCtx(e,s),{validation:"jwt",code:p.invalid_string,message:u.message}),l.dirty()):"cidr"===u.kind?(a=e.data,!(("v4"===(o=u.version)||!o)&&H.test(a)||("v6"===o||!o)&&X.test(a))&&1&&(E(s=this._getOrReturnCtx(e,s),{validation:"cidr",code:p.invalid_string,message:u.message}),l.dirty())):"base64"===u.kind?G.test(e.data)||(E(s=this._getOrReturnCtx(e,s),{validation:"base64",code:p.invalid_string,message:u.message}),l.dirty()):"base64url"===u.kind?q.test(e.data)||(E(s=this._getOrReturnCtx(e,s),{validation:"base64url",code:p.invalid_string,message:u.message}),l.dirty()):i.assertNever(u);return{status:l.value,value:e.data}}_regex(e,t,r){return this.refinement(t=>e.test(t),{validation:t,code:p.invalid_string,...o.errToObj(r)})}_addCheck(e){return new et({...this._def,checks:[...this._def.checks,e]})}email(e){return this._addCheck({kind:"email",...o.errToObj(e)})}url(e){return this._addCheck({kind:"url",...o.errToObj(e)})}emoji(e){return this._addCheck({kind:"emoji",...o.errToObj(e)})}uuid(e){return this._addCheck({kind:"uuid",...o.errToObj(e)})}nanoid(e){return this._addCheck({kind:"nanoid",...o.errToObj(e)})}cuid(e){return this._addCheck({kind:"cuid",...o.errToObj(e)})}cuid2(e){return this._addCheck({kind:"cuid2",...o.errToObj(e)})}ulid(e){return this._addCheck({kind:"ulid",...o.errToObj(e)})}base64(e){return this._addCheck({kind:"base64",...o.errToObj(e)})}base64url(e){return this._addCheck({kind:"base64url",...o.errToObj(e)})}jwt(e){return this._addCheck({kind:"jwt",...o.errToObj(e)})}ip(e){return this._addCheck({kind:"ip",...o.errToObj(e)})}cidr(e){return this._addCheck({kind:"cidr",...o.errToObj(e)})}datetime(e){return"string"==typeof e?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:e}):this._addCheck({kind:"datetime",precision:void 0===e?.precision?null:e?.precision,offset:e?.offset??!1,local:e?.local??!1,...o.errToObj(e?.message)})}date(e){return this._addCheck({kind:"date",message:e})}time(e){return"string"==typeof e?this._addCheck({kind:"time",precision:null,message:e}):this._addCheck({kind:"time",precision:void 0===e?.precision?null:e?.precision,...o.errToObj(e?.message)})}duration(e){return this._addCheck({kind:"duration",...o.errToObj(e)})}regex(e,t){return this._addCheck({kind:"regex",regex:e,...o.errToObj(t)})}includes(e,t){return this._addCheck({kind:"includes",value:e,position:t?.position,...o.errToObj(t?.message)})}startsWith(e,t){return this._addCheck({kind:"startsWith",value:e,...o.errToObj(t)})}endsWith(e,t){return this._addCheck({kind:"endsWith",value:e,...o.errToObj(t)})}min(e,t){return this._addCheck({kind:"min",value:e,...o.errToObj(t)})}max(e,t){return this._addCheck({kind:"max",value:e,...o.errToObj(t)})}length(e,t){return this._addCheck({kind:"length",value:e,...o.errToObj(t)})}nonempty(e){return this.min(1,o.errToObj(e))}trim(){return new et({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new et({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new et({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find(e=>"datetime"===e.kind)}get isDate(){return!!this._def.checks.find(e=>"date"===e.kind)}get isTime(){return!!this._def.checks.find(e=>"time"===e.kind)}get isDuration(){return!!this._def.checks.find(e=>"duration"===e.kind)}get isEmail(){return!!this._def.checks.find(e=>"email"===e.kind)}get isURL(){return!!this._def.checks.find(e=>"url"===e.kind)}get isEmoji(){return!!this._def.checks.find(e=>"emoji"===e.kind)}get isUUID(){return!!this._def.checks.find(e=>"uuid"===e.kind)}get isNANOID(){return!!this._def.checks.find(e=>"nanoid"===e.kind)}get isCUID(){return!!this._def.checks.find(e=>"cuid"===e.kind)}get isCUID2(){return!!this._def.checks.find(e=>"cuid2"===e.kind)}get isULID(){return!!this._def.checks.find(e=>"ulid"===e.kind)}get isIP(){return!!this._def.checks.find(e=>"ip"===e.kind)}get isCIDR(){return!!this._def.checks.find(e=>"cidr"===e.kind)}get isBase64(){return!!this._def.checks.find(e=>"base64"===e.kind)}get isBase64url(){return!!this._def.checks.find(e=>"base64url"===e.kind)}get minLength(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxLength(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}}et.create=e=>new et({checks:[],typeName:u.ZodString,coerce:e?.coerce??!1,...N(e)});class er extends L{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(e){let t;if(this._def.coerce&&(e.data=Number(e.data)),this._getType(e)!==c.number){let t=this._getOrReturnCtx(e);return E(t,{code:p.invalid_type,expected:c.number,received:t.parsedType}),P}let r=new k;for(let n of this._def.checks)"int"===n.kind?i.isInteger(e.data)||(E(t=this._getOrReturnCtx(e,t),{code:p.invalid_type,expected:"integer",received:"float",message:n.message}),r.dirty()):"min"===n.kind?(n.inclusive?e.data<n.value:e.data<=n.value)&&(E(t=this._getOrReturnCtx(e,t),{code:p.too_small,minimum:n.value,type:"number",inclusive:n.inclusive,exact:!1,message:n.message}),r.dirty()):"max"===n.kind?(n.inclusive?e.data>n.value:e.data>=n.value)&&(E(t=this._getOrReturnCtx(e,t),{code:p.too_big,maximum:n.value,type:"number",inclusive:n.inclusive,exact:!1,message:n.message}),r.dirty()):"multipleOf"===n.kind?0!==function(e,t){let r=(e.toString().split(".")[1]||"").length,n=(t.toString().split(".")[1]||"").length,i=r>n?r:n;return Number.parseInt(e.toFixed(i).replace(".",""))%Number.parseInt(t.toFixed(i).replace(".",""))/10**i}(e.data,n.value)&&(E(t=this._getOrReturnCtx(e,t),{code:p.not_multiple_of,multipleOf:n.value,message:n.message}),r.dirty()):"finite"===n.kind?Number.isFinite(e.data)||(E(t=this._getOrReturnCtx(e,t),{code:p.not_finite,message:n.message}),r.dirty()):i.assertNever(n);return{status:r.value,value:e.data}}gte(e,t){return this.setLimit("min",e,!0,o.toString(t))}gt(e,t){return this.setLimit("min",e,!1,o.toString(t))}lte(e,t){return this.setLimit("max",e,!0,o.toString(t))}lt(e,t){return this.setLimit("max",e,!1,o.toString(t))}setLimit(e,t,r,n){return new er({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:r,message:o.toString(n)}]})}_addCheck(e){return new er({...this._def,checks:[...this._def.checks,e]})}int(e){return this._addCheck({kind:"int",message:o.toString(e)})}positive(e){return this._addCheck({kind:"min",value:0,inclusive:!1,message:o.toString(e)})}negative(e){return this._addCheck({kind:"max",value:0,inclusive:!1,message:o.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:0,inclusive:!0,message:o.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:0,inclusive:!0,message:o.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:o.toString(t)})}finite(e){return this._addCheck({kind:"finite",message:o.toString(e)})}safe(e){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:o.toString(e)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:o.toString(e)})}get minValue(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}get isInt(){return!!this._def.checks.find(e=>"int"===e.kind||"multipleOf"===e.kind&&i.isInteger(e.value))}get isFinite(){let e=null,t=null;for(let r of this._def.checks)if("finite"===r.kind||"int"===r.kind||"multipleOf"===r.kind)return!0;else"min"===r.kind?(null===t||r.value>t)&&(t=r.value):"max"===r.kind&&(null===e||r.value<e)&&(e=r.value);return Number.isFinite(t)&&Number.isFinite(e)}}er.create=e=>new er({checks:[],typeName:u.ZodNumber,coerce:e?.coerce||!1,...N(e)});class en extends L{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(e){let t;if(this._def.coerce)try{e.data=BigInt(e.data)}catch{return this._getInvalidInput(e)}if(this._getType(e)!==c.bigint)return this._getInvalidInput(e);let r=new k;for(let n of this._def.checks)"min"===n.kind?(n.inclusive?e.data<n.value:e.data<=n.value)&&(E(t=this._getOrReturnCtx(e,t),{code:p.too_small,type:"bigint",minimum:n.value,inclusive:n.inclusive,message:n.message}),r.dirty()):"max"===n.kind?(n.inclusive?e.data>n.value:e.data>=n.value)&&(E(t=this._getOrReturnCtx(e,t),{code:p.too_big,type:"bigint",maximum:n.value,inclusive:n.inclusive,message:n.message}),r.dirty()):"multipleOf"===n.kind?e.data%n.value!==BigInt(0)&&(E(t=this._getOrReturnCtx(e,t),{code:p.not_multiple_of,multipleOf:n.value,message:n.message}),r.dirty()):i.assertNever(n);return{status:r.value,value:e.data}}_getInvalidInput(e){let t=this._getOrReturnCtx(e);return E(t,{code:p.invalid_type,expected:c.bigint,received:t.parsedType}),P}gte(e,t){return this.setLimit("min",e,!0,o.toString(t))}gt(e,t){return this.setLimit("min",e,!1,o.toString(t))}lte(e,t){return this.setLimit("max",e,!0,o.toString(t))}lt(e,t){return this.setLimit("max",e,!1,o.toString(t))}setLimit(e,t,r,n){return new en({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:r,message:o.toString(n)}]})}_addCheck(e){return new en({...this._def,checks:[...this._def.checks,e]})}positive(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:o.toString(e)})}negative(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:o.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:o.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:o.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:o.toString(t)})}get minValue(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}}en.create=e=>new en({checks:[],typeName:u.ZodBigInt,coerce:e?.coerce??!1,...N(e)});class ei extends L{_parse(e){if(this._def.coerce&&(e.data=!!e.data),this._getType(e)!==c.boolean){let t=this._getOrReturnCtx(e);return E(t,{code:p.invalid_type,expected:c.boolean,received:t.parsedType}),P}return S(e.data)}}ei.create=e=>new ei({typeName:u.ZodBoolean,coerce:e?.coerce||!1,...N(e)});class ea extends L{_parse(e){let t;if(this._def.coerce&&(e.data=new Date(e.data)),this._getType(e)!==c.date){let t=this._getOrReturnCtx(e);return E(t,{code:p.invalid_type,expected:c.date,received:t.parsedType}),P}if(Number.isNaN(e.data.getTime()))return E(this._getOrReturnCtx(e),{code:p.invalid_date}),P;let r=new k;for(let n of this._def.checks)"min"===n.kind?e.data.getTime()<n.value&&(E(t=this._getOrReturnCtx(e,t),{code:p.too_small,message:n.message,inclusive:!0,exact:!1,minimum:n.value,type:"date"}),r.dirty()):"max"===n.kind?e.data.getTime()>n.value&&(E(t=this._getOrReturnCtx(e,t),{code:p.too_big,message:n.message,inclusive:!0,exact:!1,maximum:n.value,type:"date"}),r.dirty()):i.assertNever(n);return{status:r.value,value:new Date(e.data.getTime())}}_addCheck(e){return new ea({...this._def,checks:[...this._def.checks,e]})}min(e,t){return this._addCheck({kind:"min",value:e.getTime(),message:o.toString(t)})}max(e,t){return this._addCheck({kind:"max",value:e.getTime(),message:o.toString(t)})}get minDate(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return null!=e?new Date(e):null}get maxDate(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return null!=e?new Date(e):null}}ea.create=e=>new ea({checks:[],coerce:e?.coerce||!1,typeName:u.ZodDate,...N(e)});class eo extends L{_parse(e){if(this._getType(e)!==c.symbol){let t=this._getOrReturnCtx(e);return E(t,{code:p.invalid_type,expected:c.symbol,received:t.parsedType}),P}return S(e.data)}}eo.create=e=>new eo({typeName:u.ZodSymbol,...N(e)});class es extends L{_parse(e){if(this._getType(e)!==c.undefined){let t=this._getOrReturnCtx(e);return E(t,{code:p.invalid_type,expected:c.undefined,received:t.parsedType}),P}return S(e.data)}}es.create=e=>new es({typeName:u.ZodUndefined,...N(e)});class el extends L{_parse(e){if(this._getType(e)!==c.null){let t=this._getOrReturnCtx(e);return E(t,{code:p.invalid_type,expected:c.null,received:t.parsedType}),P}return S(e.data)}}el.create=e=>new el({typeName:u.ZodNull,...N(e)});class eu extends L{constructor(){super(...arguments),this._any=!0}_parse(e){return S(e.data)}}eu.create=e=>new eu({typeName:u.ZodAny,...N(e)});class ed extends L{constructor(){super(...arguments),this._unknown=!0}_parse(e){return S(e.data)}}ed.create=e=>new ed({typeName:u.ZodUnknown,...N(e)});class ec extends L{_parse(e){let t=this._getOrReturnCtx(e);return E(t,{code:p.invalid_type,expected:c.never,received:t.parsedType}),P}}ec.create=e=>new ec({typeName:u.ZodNever,...N(e)});class eh extends L{_parse(e){if(this._getType(e)!==c.undefined){let t=this._getOrReturnCtx(e);return E(t,{code:p.invalid_type,expected:c.void,received:t.parsedType}),P}return S(e.data)}}eh.create=e=>new eh({typeName:u.ZodVoid,...N(e)});class ep extends L{_parse(e){let{ctx:t,status:r}=this._processInputParams(e),n=this._def;if(t.parsedType!==c.array)return E(t,{code:p.invalid_type,expected:c.array,received:t.parsedType}),P;if(null!==n.exactLength){let e=t.data.length>n.exactLength.value,i=t.data.length<n.exactLength.value;(e||i)&&(E(t,{code:e?p.too_big:p.too_small,minimum:i?n.exactLength.value:void 0,maximum:e?n.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:n.exactLength.message}),r.dirty())}if(null!==n.minLength&&t.data.length<n.minLength.value&&(E(t,{code:p.too_small,minimum:n.minLength.value,type:"array",inclusive:!0,exact:!1,message:n.minLength.message}),r.dirty()),null!==n.maxLength&&t.data.length>n.maxLength.value&&(E(t,{code:p.too_big,maximum:n.maxLength.value,type:"array",inclusive:!0,exact:!1,message:n.maxLength.message}),r.dirty()),t.common.async)return Promise.all([...t.data].map((e,r)=>n.type._parseAsync(new I(t,e,t.path,r)))).then(e=>k.mergeArray(r,e));let i=[...t.data].map((e,r)=>n.type._parseSync(new I(t,e,t.path,r)));return k.mergeArray(r,i)}get element(){return this._def.type}min(e,t){return new ep({...this._def,minLength:{value:e,message:o.toString(t)}})}max(e,t){return new ep({...this._def,maxLength:{value:e,message:o.toString(t)}})}length(e,t){return new ep({...this._def,exactLength:{value:e,message:o.toString(t)}})}nonempty(e){return this.min(1,e)}}ep.create=(e,t)=>new ep({type:e,minLength:null,maxLength:null,exactLength:null,typeName:u.ZodArray,...N(t)});class ef extends L{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(null!==this._cached)return this._cached;let e=this._def.shape(),t=i.objectKeys(e);return this._cached={shape:e,keys:t},this._cached}_parse(e){if(this._getType(e)!==c.object){let t=this._getOrReturnCtx(e);return E(t,{code:p.invalid_type,expected:c.object,received:t.parsedType}),P}let{status:t,ctx:r}=this._processInputParams(e),{shape:n,keys:i}=this._getCached(),a=[];if(!(this._def.catchall instanceof ec&&"strip"===this._def.unknownKeys))for(let e in r.data)i.includes(e)||a.push(e);let o=[];for(let e of i){let t=n[e],i=r.data[e];o.push({key:{status:"valid",value:e},value:t._parse(new I(r,i,r.path,e)),alwaysSet:e in r.data})}if(this._def.catchall instanceof ec){let e=this._def.unknownKeys;if("passthrough"===e)for(let e of a)o.push({key:{status:"valid",value:e},value:{status:"valid",value:r.data[e]}});else if("strict"===e)a.length>0&&(E(r,{code:p.unrecognized_keys,keys:a}),t.dirty());else if("strip"===e);else throw Error("Internal ZodObject error: invalid unknownKeys value.")}else{let e=this._def.catchall;for(let t of a){let n=r.data[t];o.push({key:{status:"valid",value:t},value:e._parse(new I(r,n,r.path,t)),alwaysSet:t in r.data})}}return r.common.async?Promise.resolve().then(async()=>{let e=[];for(let t of o){let r=await t.key,n=await t.value;e.push({key:r,value:n,alwaysSet:t.alwaysSet})}return e}).then(e=>k.mergeObjectSync(t,e)):k.mergeObjectSync(t,o)}get shape(){return this._def.shape()}strict(e){return o.errToObj,new ef({...this._def,unknownKeys:"strict",...void 0!==e?{errorMap:(t,r)=>{let n=this._def.errorMap?.(t,r).message??r.defaultError;return"unrecognized_keys"===t.code?{message:o.errToObj(e).message??n}:{message:n}}}:{}})}strip(){return new ef({...this._def,unknownKeys:"strip"})}passthrough(){return new ef({...this._def,unknownKeys:"passthrough"})}extend(e){return new ef({...this._def,shape:()=>({...this._def.shape(),...e})})}merge(e){return new ef({unknownKeys:e._def.unknownKeys,catchall:e._def.catchall,shape:()=>({...this._def.shape(),...e._def.shape()}),typeName:u.ZodObject})}setKey(e,t){return this.augment({[e]:t})}catchall(e){return new ef({...this._def,catchall:e})}pick(e){let t={};for(let r of i.objectKeys(e))e[r]&&this.shape[r]&&(t[r]=this.shape[r]);return new ef({...this._def,shape:()=>t})}omit(e){let t={};for(let r of i.objectKeys(this.shape))e[r]||(t[r]=this.shape[r]);return new ef({...this._def,shape:()=>t})}deepPartial(){return function e(t){if(t instanceof ef){let r={};for(let n in t.shape){let i=t.shape[n];r[n]=eT.create(e(i))}return new ef({...t._def,shape:()=>r})}if(t instanceof ep)return new ep({...t._def,type:e(t.element)});if(t instanceof eT)return eT.create(e(t.unwrap()));if(t instanceof eA)return eA.create(e(t.unwrap()));if(t instanceof ey)return ey.create(t.items.map(t=>e(t)));else return t}(this)}partial(e){let t={};for(let r of i.objectKeys(this.shape)){let n=this.shape[r];e&&!e[r]?t[r]=n:t[r]=n.optional()}return new ef({...this._def,shape:()=>t})}required(e){let t={};for(let r of i.objectKeys(this.shape))if(e&&!e[r])t[r]=this.shape[r];else{let e=this.shape[r];for(;e instanceof eT;)e=e._def.innerType;t[r]=e}return new ef({...this._def,shape:()=>t})}keyof(){return eS(i.objectKeys(this.shape))}}ef.create=(e,t)=>new ef({shape:()=>e,unknownKeys:"strip",catchall:ec.create(),typeName:u.ZodObject,...N(t)}),ef.strictCreate=(e,t)=>new ef({shape:()=>e,unknownKeys:"strict",catchall:ec.create(),typeName:u.ZodObject,...N(t)}),ef.lazycreate=(e,t)=>new ef({shape:e,unknownKeys:"strip",catchall:ec.create(),typeName:u.ZodObject,...N(t)});class em extends L{_parse(e){let{ctx:t}=this._processInputParams(e),r=this._def.options;if(t.common.async)return Promise.all(r.map(async e=>{let r={...t,common:{...t.common,issues:[]},parent:null};return{result:await e._parseAsync({data:t.data,path:t.path,parent:r}),ctx:r}})).then(function(e){for(let t of e)if("valid"===t.result.status)return t.result;for(let r of e)if("dirty"===r.result.status)return t.common.issues.push(...r.ctx.common.issues),r.result;let r=e.map(e=>new m(e.ctx.common.issues));return E(t,{code:p.invalid_union,unionErrors:r}),P});{let e,n=[];for(let i of r){let r={...t,common:{...t.common,issues:[]},parent:null},a=i._parseSync({data:t.data,path:t.path,parent:r});if("valid"===a.status)return a;"dirty"!==a.status||e||(e={result:a,ctx:r}),r.common.issues.length&&n.push(r.common.issues)}if(e)return t.common.issues.push(...e.ctx.common.issues),e.result;let i=n.map(e=>new m(e));return E(t,{code:p.invalid_union,unionErrors:i}),P}}get options(){return this._def.options}}em.create=(e,t)=>new em({options:e,typeName:u.ZodUnion,...N(t)});let eg=e=>{if(e instanceof eP)return eg(e.schema);if(e instanceof ej)return eg(e.innerType());if(e instanceof ex)return[e.value];if(e instanceof eR)return e.options;if(e instanceof eC)return i.objectValues(e.enum);else if(e instanceof eI)return eg(e._def.innerType);else if(e instanceof es)return[void 0];else if(e instanceof el)return[null];else if(e instanceof eT)return[void 0,...eg(e.unwrap())];else if(e instanceof eA)return[null,...eg(e.unwrap())];else if(e instanceof eU)return eg(e.unwrap());else if(e instanceof eF)return eg(e.unwrap());else if(e instanceof eM)return eg(e._def.innerType);else return[]};class ev extends L{_parse(e){let{ctx:t}=this._processInputParams(e);if(t.parsedType!==c.object)return E(t,{code:p.invalid_type,expected:c.object,received:t.parsedType}),P;let r=this.discriminator,n=t.data[r],i=this.optionsMap.get(n);return i?t.common.async?i._parseAsync({data:t.data,path:t.path,parent:t}):i._parseSync({data:t.data,path:t.path,parent:t}):(E(t,{code:p.invalid_union_discriminator,options:Array.from(this.optionsMap.keys()),path:[r]}),P)}get discriminator(){return this._def.discriminator}get options(){return this._def.options}get optionsMap(){return this._def.optionsMap}static create(e,t,r){let n=new Map;for(let r of t){let t=eg(r.shape[e]);if(!t.length)throw Error(`A discriminator value for key \`${e}\` could not be extracted from all schema options`);for(let i of t){if(n.has(i))throw Error(`Discriminator property ${String(e)} has duplicate value ${String(i)}`);n.set(i,r)}}return new ev({typeName:u.ZodDiscriminatedUnion,discriminator:e,options:t,optionsMap:n,...N(r)})}}class e_ extends L{_parse(e){let{status:t,ctx:r}=this._processInputParams(e),n=(e,n)=>{if(R(e)||R(n))return P;let a=function e(t,r){let n=h(t),a=h(r);if(t===r)return{valid:!0,data:t};if(n===c.object&&a===c.object){let n=i.objectKeys(r),a=i.objectKeys(t).filter(e=>-1!==n.indexOf(e)),o={...t,...r};for(let n of a){let i=e(t[n],r[n]);if(!i.valid)return{valid:!1};o[n]=i.data}return{valid:!0,data:o}}if(n===c.array&&a===c.array){if(t.length!==r.length)return{valid:!1};let n=[];for(let i=0;i<t.length;i++){let a=e(t[i],r[i]);if(!a.valid)return{valid:!1};n.push(a.data)}return{valid:!0,data:n}}if(n===c.date&&a===c.date&&+t==+r)return{valid:!0,data:t};return{valid:!1}}(e.value,n.value);return a.valid?((C(e)||C(n))&&t.dirty(),{status:t.value,value:a.data}):(E(r,{code:p.invalid_intersection_types}),P)};return r.common.async?Promise.all([this._def.left._parseAsync({data:r.data,path:r.path,parent:r}),this._def.right._parseAsync({data:r.data,path:r.path,parent:r})]).then(([e,t])=>n(e,t)):n(this._def.left._parseSync({data:r.data,path:r.path,parent:r}),this._def.right._parseSync({data:r.data,path:r.path,parent:r}))}}e_.create=(e,t,r)=>new e_({left:e,right:t,typeName:u.ZodIntersection,...N(r)});class ey extends L{_parse(e){let{status:t,ctx:r}=this._processInputParams(e);if(r.parsedType!==c.array)return E(r,{code:p.invalid_type,expected:c.array,received:r.parsedType}),P;if(r.data.length<this._def.items.length)return E(r,{code:p.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),P;!this._def.rest&&r.data.length>this._def.items.length&&(E(r,{code:p.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),t.dirty());let n=[...r.data].map((e,t)=>{let n=this._def.items[t]||this._def.rest;return n?n._parse(new I(r,e,r.path,t)):null}).filter(e=>!!e);return r.common.async?Promise.all(n).then(e=>k.mergeArray(t,e)):k.mergeArray(t,n)}get items(){return this._def.items}rest(e){return new ey({...this._def,rest:e})}}ey.create=(e,t)=>{if(!Array.isArray(e))throw Error("You must pass an array of schemas to z.tuple([ ... ])");return new ey({items:e,typeName:u.ZodTuple,rest:null,...N(t)})};class eb extends L{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){let{status:t,ctx:r}=this._processInputParams(e);if(r.parsedType!==c.object)return E(r,{code:p.invalid_type,expected:c.object,received:r.parsedType}),P;let n=[],i=this._def.keyType,a=this._def.valueType;for(let e in r.data)n.push({key:i._parse(new I(r,e,r.path,e)),value:a._parse(new I(r,r.data[e],r.path,e)),alwaysSet:e in r.data});return r.common.async?k.mergeObjectAsync(t,n):k.mergeObjectSync(t,n)}get element(){return this._def.valueType}static create(e,t,r){return new eb(t instanceof L?{keyType:e,valueType:t,typeName:u.ZodRecord,...N(r)}:{keyType:et.create(),valueType:e,typeName:u.ZodRecord,...N(t)})}}class ew extends L{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){let{status:t,ctx:r}=this._processInputParams(e);if(r.parsedType!==c.map)return E(r,{code:p.invalid_type,expected:c.map,received:r.parsedType}),P;let n=this._def.keyType,i=this._def.valueType,a=[...r.data.entries()].map(([e,t],a)=>({key:n._parse(new I(r,e,r.path,[a,"key"])),value:i._parse(new I(r,t,r.path,[a,"value"]))}));if(r.common.async){let e=new Map;return Promise.resolve().then(async()=>{for(let r of a){let n=await r.key,i=await r.value;if("aborted"===n.status||"aborted"===i.status)return P;("dirty"===n.status||"dirty"===i.status)&&t.dirty(),e.set(n.value,i.value)}return{status:t.value,value:e}})}{let e=new Map;for(let r of a){let n=r.key,i=r.value;if("aborted"===n.status||"aborted"===i.status)return P;("dirty"===n.status||"dirty"===i.status)&&t.dirty(),e.set(n.value,i.value)}return{status:t.value,value:e}}}}ew.create=(e,t,r)=>new ew({valueType:t,keyType:e,typeName:u.ZodMap,...N(r)});class eE extends L{_parse(e){let{status:t,ctx:r}=this._processInputParams(e);if(r.parsedType!==c.set)return E(r,{code:p.invalid_type,expected:c.set,received:r.parsedType}),P;let n=this._def;null!==n.minSize&&r.data.size<n.minSize.value&&(E(r,{code:p.too_small,minimum:n.minSize.value,type:"set",inclusive:!0,exact:!1,message:n.minSize.message}),t.dirty()),null!==n.maxSize&&r.data.size>n.maxSize.value&&(E(r,{code:p.too_big,maximum:n.maxSize.value,type:"set",inclusive:!0,exact:!1,message:n.maxSize.message}),t.dirty());let i=this._def.valueType;function a(e){let r=new Set;for(let n of e){if("aborted"===n.status)return P;"dirty"===n.status&&t.dirty(),r.add(n.value)}return{status:t.value,value:r}}let o=[...r.data.values()].map((e,t)=>i._parse(new I(r,e,r.path,t)));return r.common.async?Promise.all(o).then(e=>a(e)):a(o)}min(e,t){return new eE({...this._def,minSize:{value:e,message:o.toString(t)}})}max(e,t){return new eE({...this._def,maxSize:{value:e,message:o.toString(t)}})}size(e,t){return this.min(e,t).max(e,t)}nonempty(e){return this.min(1,e)}}eE.create=(e,t)=>new eE({valueType:e,minSize:null,maxSize:null,typeName:u.ZodSet,...N(t)});class ek extends L{constructor(){super(...arguments),this.validate=this.implement}_parse(e){let{ctx:t}=this._processInputParams(e);if(t.parsedType!==c.function)return E(t,{code:p.invalid_type,expected:c.function,received:t.parsedType}),P;function r(e,r){return b({data:e,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,v,g].filter(e=>!!e),issueData:{code:p.invalid_arguments,argumentsError:r}})}function n(e,r){return b({data:e,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,v,g].filter(e=>!!e),issueData:{code:p.invalid_return_type,returnTypeError:r}})}let i={errorMap:t.common.contextualErrorMap},a=t.data;if(this._def.returns instanceof eO){let e=this;return S(async function(...t){let o=new m([]),s=await e._def.args.parseAsync(t,i).catch(e=>{throw o.addIssue(r(t,e)),o}),l=await Reflect.apply(a,this,s);return await e._def.returns._def.type.parseAsync(l,i).catch(e=>{throw o.addIssue(n(l,e)),o})})}{let e=this;return S(function(...t){let o=e._def.args.safeParse(t,i);if(!o.success)throw new m([r(t,o.error)]);let s=Reflect.apply(a,this,o.data),l=e._def.returns.safeParse(s,i);if(!l.success)throw new m([n(s,l.error)]);return l.data})}}parameters(){return this._def.args}returnType(){return this._def.returns}args(...e){return new ek({...this._def,args:ey.create(e).rest(ed.create())})}returns(e){return new ek({...this._def,returns:e})}implement(e){return this.parse(e)}strictImplement(e){return this.parse(e)}static create(e,t,r){return new ek({args:e||ey.create([]).rest(ed.create()),returns:t||ed.create(),typeName:u.ZodFunction,...N(r)})}}class eP extends L{get schema(){return this._def.getter()}_parse(e){let{ctx:t}=this._processInputParams(e);return this._def.getter()._parse({data:t.data,path:t.path,parent:t})}}eP.create=(e,t)=>new eP({getter:e,typeName:u.ZodLazy,...N(t)});class ex extends L{_parse(e){if(e.data!==this._def.value){let t=this._getOrReturnCtx(e);return E(t,{received:t.data,code:p.invalid_literal,expected:this._def.value}),P}return{status:"valid",value:e.data}}get value(){return this._def.value}}function eS(e,t){return new eR({values:e,typeName:u.ZodEnum,...N(t)})}ex.create=(e,t)=>new ex({value:e,typeName:u.ZodLiteral,...N(t)});class eR extends L{constructor(){super(...arguments),s.set(this,void 0)}_parse(e){if("string"!=typeof e.data){let t=this._getOrReturnCtx(e),r=this._def.values;return E(t,{expected:i.joinValues(r),received:t.parsedType,code:p.invalid_type}),P}if(T(this,s,"f")||A(this,s,new Set(this._def.values),"f"),!T(this,s,"f").has(e.data)){let t=this._getOrReturnCtx(e),r=this._def.values;return E(t,{received:t.data,code:p.invalid_enum_value,options:r}),P}return S(e.data)}get options(){return this._def.values}get enum(){let e={};for(let t of this._def.values)e[t]=t;return e}get Values(){let e={};for(let t of this._def.values)e[t]=t;return e}get Enum(){let e={};for(let t of this._def.values)e[t]=t;return e}extract(e,t=this._def){return eR.create(e,{...this._def,...t})}exclude(e,t=this._def){return eR.create(this.options.filter(t=>!e.includes(t)),{...this._def,...t})}}s=new WeakMap,eR.create=eS;class eC extends L{constructor(){super(...arguments),l.set(this,void 0)}_parse(e){let t=i.getValidEnumValues(this._def.values),r=this._getOrReturnCtx(e);if(r.parsedType!==c.string&&r.parsedType!==c.number){let e=i.objectValues(t);return E(r,{expected:i.joinValues(e),received:r.parsedType,code:p.invalid_type}),P}if(T(this,l,"f")||A(this,l,new Set(i.getValidEnumValues(this._def.values)),"f"),!T(this,l,"f").has(e.data)){let e=i.objectValues(t);return E(r,{received:r.data,code:p.invalid_enum_value,options:e}),P}return S(e.data)}get enum(){return this._def.values}}l=new WeakMap,eC.create=(e,t)=>new eC({values:e,typeName:u.ZodNativeEnum,...N(t)});class eO extends L{unwrap(){return this._def.type}_parse(e){let{ctx:t}=this._processInputParams(e);return t.parsedType!==c.promise&&!1===t.common.async?(E(t,{code:p.invalid_type,expected:c.promise,received:t.parsedType}),P):S((t.parsedType===c.promise?t.data:Promise.resolve(t.data)).then(e=>this._def.type.parseAsync(e,{path:t.path,errorMap:t.common.contextualErrorMap})))}}eO.create=(e,t)=>new eO({type:e,typeName:u.ZodPromise,...N(t)});class ej extends L{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===u.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(e){let{status:t,ctx:r}=this._processInputParams(e),n=this._def.effect||null,a={addIssue:e=>{E(r,e),e.fatal?t.abort():t.dirty()},get path(){return r.path}};if(a.addIssue=a.addIssue.bind(a),"preprocess"===n.type){let e=n.transform(r.data,a);if(r.common.async)return Promise.resolve(e).then(async e=>{if("aborted"===t.value)return P;let n=await this._def.schema._parseAsync({data:e,path:r.path,parent:r});return"aborted"===n.status?P:"dirty"===n.status||"dirty"===t.value?x(n.value):n});{if("aborted"===t.value)return P;let n=this._def.schema._parseSync({data:e,path:r.path,parent:r});return"aborted"===n.status?P:"dirty"===n.status||"dirty"===t.value?x(n.value):n}}if("refinement"===n.type){let e=e=>{let t=n.refinement(e,a);if(r.common.async)return Promise.resolve(t);if(t instanceof Promise)throw Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return e};if(!1!==r.common.async)return this._def.schema._parseAsync({data:r.data,path:r.path,parent:r}).then(r=>"aborted"===r.status?P:("dirty"===r.status&&t.dirty(),e(r.value).then(()=>({status:t.value,value:r.value}))));{let n=this._def.schema._parseSync({data:r.data,path:r.path,parent:r});return"aborted"===n.status?P:("dirty"===n.status&&t.dirty(),e(n.value),{status:t.value,value:n.value})}}if("transform"===n.type)if(!1!==r.common.async)return this._def.schema._parseAsync({data:r.data,path:r.path,parent:r}).then(e=>O(e)?Promise.resolve(n.transform(e.value,a)).then(e=>({status:t.value,value:e})):e);else{let e=this._def.schema._parseSync({data:r.data,path:r.path,parent:r});if(!O(e))return e;let i=n.transform(e.value,a);if(i instanceof Promise)throw Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:t.value,value:i}}i.assertNever(n)}}ej.create=(e,t,r)=>new ej({schema:e,typeName:u.ZodEffects,effect:t,...N(r)}),ej.createWithPreprocess=(e,t,r)=>new ej({schema:t,effect:{type:"preprocess",transform:e},typeName:u.ZodEffects,...N(r)});class eT extends L{_parse(e){return this._getType(e)===c.undefined?S(void 0):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}eT.create=(e,t)=>new eT({innerType:e,typeName:u.ZodOptional,...N(t)});class eA extends L{_parse(e){return this._getType(e)===c.null?S(null):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}eA.create=(e,t)=>new eA({innerType:e,typeName:u.ZodNullable,...N(t)});class eI extends L{_parse(e){let{ctx:t}=this._processInputParams(e),r=t.data;return t.parsedType===c.undefined&&(r=this._def.defaultValue()),this._def.innerType._parse({data:r,path:t.path,parent:t})}removeDefault(){return this._def.innerType}}eI.create=(e,t)=>new eI({innerType:e,typeName:u.ZodDefault,defaultValue:"function"==typeof t.default?t.default:()=>t.default,...N(t)});class eM extends L{_parse(e){let{ctx:t}=this._processInputParams(e),r={...t,common:{...t.common,issues:[]}},n=this._def.innerType._parse({data:r.data,path:r.path,parent:{...r}});return j(n)?n.then(e=>({status:"valid",value:"valid"===e.status?e.value:this._def.catchValue({get error(){return new m(r.common.issues)},input:r.data})})):{status:"valid",value:"valid"===n.status?n.value:this._def.catchValue({get error(){return new m(r.common.issues)},input:r.data})}}removeCatch(){return this._def.innerType}}eM.create=(e,t)=>new eM({innerType:e,typeName:u.ZodCatch,catchValue:"function"==typeof t.catch?t.catch:()=>t.catch,...N(t)});class eN extends L{_parse(e){if(this._getType(e)!==c.nan){let t=this._getOrReturnCtx(e);return E(t,{code:p.invalid_type,expected:c.nan,received:t.parsedType}),P}return{status:"valid",value:e.data}}}eN.create=e=>new eN({typeName:u.ZodNaN,...N(e)});let eL=Symbol("zod_brand");class eU extends L{_parse(e){let{ctx:t}=this._processInputParams(e),r=t.data;return this._def.type._parse({data:r,path:t.path,parent:t})}unwrap(){return this._def.type}}class eD extends L{_parse(e){let{status:t,ctx:r}=this._processInputParams(e);if(r.common.async)return(async()=>{let e=await this._def.in._parseAsync({data:r.data,path:r.path,parent:r});return"aborted"===e.status?P:"dirty"===e.status?(t.dirty(),x(e.value)):this._def.out._parseAsync({data:e.value,path:r.path,parent:r})})();{let e=this._def.in._parseSync({data:r.data,path:r.path,parent:r});return"aborted"===e.status?P:"dirty"===e.status?(t.dirty(),{status:"dirty",value:e.value}):this._def.out._parseSync({data:e.value,path:r.path,parent:r})}}static create(e,t){return new eD({in:e,out:t,typeName:u.ZodPipeline})}}class eF extends L{_parse(e){let t=this._def.innerType._parse(e),r=e=>(O(e)&&(e.value=Object.freeze(e.value)),e);return j(t)?t.then(e=>r(e)):r(t)}unwrap(){return this._def.innerType}}function eB(e,t){let r="function"==typeof e?e(t):"string"==typeof e?{message:e}:e;return"string"==typeof r?{message:r}:r}function eW(e,t={},r){return e?eu.create().superRefine((n,i)=>{let a=e(n);if(a instanceof Promise)return a.then(e=>{if(!e){let e=eB(t,n),a=e.fatal??r??!0;i.addIssue({code:"custom",...e,fatal:a})}});if(!a){let e=eB(t,n),a=e.fatal??r??!0;i.addIssue({code:"custom",...e,fatal:a})}}):eu.create()}eF.create=(e,t)=>new eF({innerType:e,typeName:u.ZodReadonly,...N(t)});let ez={object:ef.lazycreate};!function(e){e.ZodString="ZodString",e.ZodNumber="ZodNumber",e.ZodNaN="ZodNaN",e.ZodBigInt="ZodBigInt",e.ZodBoolean="ZodBoolean",e.ZodDate="ZodDate",e.ZodSymbol="ZodSymbol",e.ZodUndefined="ZodUndefined",e.ZodNull="ZodNull",e.ZodAny="ZodAny",e.ZodUnknown="ZodUnknown",e.ZodNever="ZodNever",e.ZodVoid="ZodVoid",e.ZodArray="ZodArray",e.ZodObject="ZodObject",e.ZodUnion="ZodUnion",e.ZodDiscriminatedUnion="ZodDiscriminatedUnion",e.ZodIntersection="ZodIntersection",e.ZodTuple="ZodTuple",e.ZodRecord="ZodRecord",e.ZodMap="ZodMap",e.ZodSet="ZodSet",e.ZodFunction="ZodFunction",e.ZodLazy="ZodLazy",e.ZodLiteral="ZodLiteral",e.ZodEnum="ZodEnum",e.ZodEffects="ZodEffects",e.ZodNativeEnum="ZodNativeEnum",e.ZodOptional="ZodOptional",e.ZodNullable="ZodNullable",e.ZodDefault="ZodDefault",e.ZodCatch="ZodCatch",e.ZodPromise="ZodPromise",e.ZodBranded="ZodBranded",e.ZodPipeline="ZodPipeline",e.ZodReadonly="ZodReadonly"}(u||(u={}));let eZ=(e,t={message:`Input not instance of ${e.name}`})=>eW(t=>t instanceof e,t),eK=et.create,eV=er.create,eH=eN.create,e$=en.create,eX=ei.create,eG=ea.create,eq=eo.create,eY=es.create,eJ=el.create,eQ=eu.create,e0=ed.create,e1=ec.create,e2=eh.create,e4=ep.create,e5=ef.create,e9=ef.strictCreate,e3=em.create,e7=ev.create,e6=e_.create,e8=ey.create,te=eb.create,tt=ew.create,tr=eE.create,tn=ek.create,ti=eP.create,ta=ex.create,to=eR.create,ts=eC.create,tl=eO.create,tu=ej.create,td=eT.create,tc=eA.create,th=ej.createWithPreprocess,tp=eD.create,tf=()=>eK().optional(),tm=()=>eV().optional(),tg=()=>eX().optional(),tv={string:e=>et.create({...e,coerce:!0}),number:e=>er.create({...e,coerce:!0}),boolean:e=>ei.create({...e,coerce:!0}),bigint:e=>en.create({...e,coerce:!0}),date:e=>ea.create({...e,coerce:!0})},t_=P},74876:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return i}});let n=r(53854)._(r(25084));function i(e,t){var r;let i={};"function"==typeof e&&(i.loader=e);let a={...i,...t};return(0,n.default)({...a,modules:null==(r=a.loadableGenerated)?void 0:r.modules})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},74934:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizeLocalePath",{enumerable:!0,get:function(){return n}});let r=new WeakMap;function n(e,t){let n;if(!t)return{pathname:e};let i=r.get(t);i||(i=t.map(e=>e.toLowerCase()),r.set(t,i));let a=e.split("/",2);if(!a[1])return{pathname:e};let o=a[1].toLowerCase(),s=i.indexOf(o);return s<0?{pathname:e}:(n=t[s],{pathname:e=e.slice(n.length+1)||"/",detectedLocale:n})}},75447:(e,t,r)=>{"use strict";r.d(t,{UC:()=>eX,q7:()=>eq,JU:()=>eG,ZL:()=>e$,bL:()=>eV,wv:()=>eY,l9:()=>eH});var n=r(50628),i=r(13859),a=r(98064),o=r(48733),s=r(17691),l=r(64826),u=r(3057),d=r(85532),c=r(78557),h=r(16279),p=r(9665),f=r(29823),m=r(77222),g=r(4844),v=r(64714),_=r(1524),y=r(89840),b=r(72336),w=r(11712),E=r(10345),k=r(6024),P=["Enter"," "],x=["ArrowUp","PageDown","End"],S=["ArrowDown","PageUp","Home",...x],R={ltr:[...P,"ArrowRight"],rtl:[...P,"ArrowLeft"]},C={ltr:["ArrowLeft"],rtl:["ArrowRight"]},O="Menu",[j,T,A]=(0,u.N)(O),[I,M]=(0,o.A)(O,[A,m.Bk,_.RG]),N=(0,m.Bk)(),L=(0,_.RG)(),[U,D]=I(O),[F,B]=I(O),W=e=>{let{__scopeMenu:t,open:r=!1,children:i,dir:a,onOpenChange:o,modal:s=!0}=e,l=N(t),[u,c]=n.useState(null),h=n.useRef(!1),p=(0,b.c)(o),f=(0,d.jH)(a);return n.useEffect(()=>{let e=()=>{h.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>h.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),(0,k.jsx)(m.bL,{...l,children:(0,k.jsx)(U,{scope:t,open:r,onOpenChange:p,content:u,onContentChange:c,children:(0,k.jsx)(F,{scope:t,onClose:n.useCallback(()=>p(!1),[p]),isUsingKeyboardRef:h,dir:f,modal:s,children:i})})})};W.displayName=O;var z=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e,i=N(r);return(0,k.jsx)(m.Mz,{...i,...n,ref:t})});z.displayName="MenuAnchor";var Z="MenuPortal",[K,V]=I(Z,{forceMount:void 0}),H=e=>{let{__scopeMenu:t,forceMount:r,children:n,container:i}=e,a=D(Z,t);return(0,k.jsx)(K,{scope:t,forceMount:r,children:(0,k.jsx)(v.C,{present:r||a.open,children:(0,k.jsx)(g.Z,{asChild:!0,container:i,children:n})})})};H.displayName=Z;var $="MenuContent",[X,G]=I($),q=n.forwardRef((e,t)=>{let r=V($,e.__scopeMenu),{forceMount:n=r.forceMount,...i}=e,a=D($,e.__scopeMenu),o=B($,e.__scopeMenu);return(0,k.jsx)(j.Provider,{scope:e.__scopeMenu,children:(0,k.jsx)(v.C,{present:n||a.open,children:(0,k.jsx)(j.Slot,{scope:e.__scopeMenu,children:o.modal?(0,k.jsx)(Y,{...i,ref:t}):(0,k.jsx)(J,{...i,ref:t})})})})}),Y=n.forwardRef((e,t)=>{let r=D($,e.__scopeMenu),o=n.useRef(null),s=(0,a.s)(t,o);return n.useEffect(()=>{let e=o.current;if(e)return(0,w.Eq)(e)},[]),(0,k.jsx)(ee,{...e,ref:s,trapFocus:r.open,disableOutsidePointerEvents:r.open,disableOutsideScroll:!0,onFocusOutside:(0,i.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>r.onOpenChange(!1)})}),J=n.forwardRef((e,t)=>{let r=D($,e.__scopeMenu);return(0,k.jsx)(ee,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>r.onOpenChange(!1)})}),Q=(0,y.TL)("MenuContent.ScrollLock"),ee=n.forwardRef((e,t)=>{let{__scopeMenu:r,loop:o=!1,trapFocus:s,onOpenAutoFocus:l,onCloseAutoFocus:u,disableOutsidePointerEvents:d,onEntryFocus:f,onEscapeKeyDown:g,onPointerDownOutside:v,onFocusOutside:y,onInteractOutside:b,onDismiss:w,disableOutsideScroll:P,...R}=e,C=D($,r),O=B($,r),j=N(r),A=L(r),I=T(r),[M,U]=n.useState(null),F=n.useRef(null),W=(0,a.s)(t,F,C.onContentChange),z=n.useRef(0),Z=n.useRef(""),K=n.useRef(0),V=n.useRef(null),H=n.useRef("right"),G=n.useRef(0),q=P?E.A:n.Fragment,Y=e=>{var t,r;let n=Z.current+e,i=I().filter(e=>!e.disabled),a=document.activeElement,o=null==(t=i.find(e=>e.ref.current===a))?void 0:t.textValue,s=function(e,t,r){var n;let i=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,a=r?e.indexOf(r):-1,o=(n=Math.max(a,0),e.map((t,r)=>e[(n+r)%e.length]));1===i.length&&(o=o.filter(e=>e!==r));let s=o.find(e=>e.toLowerCase().startsWith(i.toLowerCase()));return s!==r?s:void 0}(i.map(e=>e.textValue),n,o),l=null==(r=i.find(e=>e.textValue===s))?void 0:r.ref.current;!function e(t){Z.current=t,window.clearTimeout(z.current),""!==t&&(z.current=window.setTimeout(()=>e(""),1e3))}(n),l&&setTimeout(()=>l.focus())};n.useEffect(()=>()=>window.clearTimeout(z.current),[]),(0,h.Oh)();let J=n.useCallback(e=>{var t,r;return H.current===(null==(t=V.current)?void 0:t.side)&&function(e,t){return!!t&&function(e,t){let{x:r,y:n}=e,i=!1;for(let e=0,a=t.length-1;e<t.length;a=e++){let o=t[e],s=t[a],l=o.x,u=o.y,d=s.x,c=s.y;u>n!=c>n&&r<(d-l)*(n-u)/(c-u)+l&&(i=!i)}return i}({x:e.clientX,y:e.clientY},t)}(e,null==(r=V.current)?void 0:r.area)},[]);return(0,k.jsx)(X,{scope:r,searchRef:Z,onItemEnter:n.useCallback(e=>{J(e)&&e.preventDefault()},[J]),onItemLeave:n.useCallback(e=>{var t;J(e)||(null==(t=F.current)||t.focus(),U(null))},[J]),onTriggerLeave:n.useCallback(e=>{J(e)&&e.preventDefault()},[J]),pointerGraceTimerRef:K,onPointerGraceIntentChange:n.useCallback(e=>{V.current=e},[]),children:(0,k.jsx)(q,{...P?{as:Q,allowPinchZoom:!0}:void 0,children:(0,k.jsx)(p.n,{asChild:!0,trapped:s,onMountAutoFocus:(0,i.m)(l,e=>{var t;e.preventDefault(),null==(t=F.current)||t.focus({preventScroll:!0})}),onUnmountAutoFocus:u,children:(0,k.jsx)(c.qW,{asChild:!0,disableOutsidePointerEvents:d,onEscapeKeyDown:g,onPointerDownOutside:v,onFocusOutside:y,onInteractOutside:b,onDismiss:w,children:(0,k.jsx)(_.bL,{asChild:!0,...A,dir:O.dir,orientation:"vertical",loop:o,currentTabStopId:M,onCurrentTabStopIdChange:U,onEntryFocus:(0,i.m)(f,e=>{O.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,k.jsx)(m.UC,{role:"menu","aria-orientation":"vertical","data-state":eS(C.open),"data-radix-menu-content":"",dir:O.dir,...j,...R,ref:W,style:{outline:"none",...R.style},onKeyDown:(0,i.m)(R.onKeyDown,e=>{let t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,r=e.ctrlKey||e.altKey||e.metaKey,n=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!r&&n&&Y(e.key));let i=F.current;if(e.target!==i||!S.includes(e.key))return;e.preventDefault();let a=I().filter(e=>!e.disabled).map(e=>e.ref.current);x.includes(e.key)&&a.reverse(),function(e){let t=document.activeElement;for(let r of e)if(r===t||(r.focus(),document.activeElement!==t))return}(a)}),onBlur:(0,i.m)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(z.current),Z.current="")}),onPointerMove:(0,i.m)(e.onPointerMove,eO(e=>{let t=e.target,r=G.current!==e.clientX;e.currentTarget.contains(t)&&r&&(H.current=e.clientX>G.current?"right":"left",G.current=e.clientX)}))})})})})})})});q.displayName=$;var et=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,k.jsx)(l.sG.div,{role:"group",...n,ref:t})});et.displayName="MenuGroup";var er=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,k.jsx)(l.sG.div,{...n,ref:t})});er.displayName="MenuLabel";var en="MenuItem",ei="menu.itemSelect",ea=n.forwardRef((e,t)=>{let{disabled:r=!1,onSelect:o,...s}=e,u=n.useRef(null),d=B(en,e.__scopeMenu),c=G(en,e.__scopeMenu),h=(0,a.s)(t,u),p=n.useRef(!1);return(0,k.jsx)(eo,{...s,ref:h,disabled:r,onClick:(0,i.m)(e.onClick,()=>{let e=u.current;if(!r&&e){let t=new CustomEvent(ei,{bubbles:!0,cancelable:!0});e.addEventListener(ei,e=>null==o?void 0:o(e),{once:!0}),(0,l.hO)(e,t),t.defaultPrevented?p.current=!1:d.onClose()}}),onPointerDown:t=>{var r;null==(r=e.onPointerDown)||r.call(e,t),p.current=!0},onPointerUp:(0,i.m)(e.onPointerUp,e=>{var t;p.current||null==(t=e.currentTarget)||t.click()}),onKeyDown:(0,i.m)(e.onKeyDown,e=>{let t=""!==c.searchRef.current;r||t&&" "===e.key||P.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});ea.displayName=en;var eo=n.forwardRef((e,t)=>{let{__scopeMenu:r,disabled:o=!1,textValue:s,...u}=e,d=G(en,r),c=L(r),h=n.useRef(null),p=(0,a.s)(t,h),[f,m]=n.useState(!1),[g,v]=n.useState("");return n.useEffect(()=>{let e=h.current;if(e){var t;v((null!=(t=e.textContent)?t:"").trim())}},[u.children]),(0,k.jsx)(j.ItemSlot,{scope:r,disabled:o,textValue:null!=s?s:g,children:(0,k.jsx)(_.q7,{asChild:!0,...c,focusable:!o,children:(0,k.jsx)(l.sG.div,{role:"menuitem","data-highlighted":f?"":void 0,"aria-disabled":o||void 0,"data-disabled":o?"":void 0,...u,ref:p,onPointerMove:(0,i.m)(e.onPointerMove,eO(e=>{o?d.onItemLeave(e):(d.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,i.m)(e.onPointerLeave,eO(e=>d.onItemLeave(e))),onFocus:(0,i.m)(e.onFocus,()=>m(!0)),onBlur:(0,i.m)(e.onBlur,()=>m(!1))})})})}),es=n.forwardRef((e,t)=>{let{checked:r=!1,onCheckedChange:n,...a}=e;return(0,k.jsx)(em,{scope:e.__scopeMenu,checked:r,children:(0,k.jsx)(ea,{role:"menuitemcheckbox","aria-checked":eR(r)?"mixed":r,...a,ref:t,"data-state":eC(r),onSelect:(0,i.m)(a.onSelect,()=>null==n?void 0:n(!!eR(r)||!r),{checkForDefaultPrevented:!1})})})});es.displayName="MenuCheckboxItem";var el="MenuRadioGroup",[eu,ed]=I(el,{value:void 0,onValueChange:()=>{}}),ec=n.forwardRef((e,t)=>{let{value:r,onValueChange:n,...i}=e,a=(0,b.c)(n);return(0,k.jsx)(eu,{scope:e.__scopeMenu,value:r,onValueChange:a,children:(0,k.jsx)(et,{...i,ref:t})})});ec.displayName=el;var eh="MenuRadioItem",ep=n.forwardRef((e,t)=>{let{value:r,...n}=e,a=ed(eh,e.__scopeMenu),o=r===a.value;return(0,k.jsx)(em,{scope:e.__scopeMenu,checked:o,children:(0,k.jsx)(ea,{role:"menuitemradio","aria-checked":o,...n,ref:t,"data-state":eC(o),onSelect:(0,i.m)(n.onSelect,()=>{var e;return null==(e=a.onValueChange)?void 0:e.call(a,r)},{checkForDefaultPrevented:!1})})})});ep.displayName=eh;var ef="MenuItemIndicator",[em,eg]=I(ef,{checked:!1}),ev=n.forwardRef((e,t)=>{let{__scopeMenu:r,forceMount:n,...i}=e,a=eg(ef,r);return(0,k.jsx)(v.C,{present:n||eR(a.checked)||!0===a.checked,children:(0,k.jsx)(l.sG.span,{...i,ref:t,"data-state":eC(a.checked)})})});ev.displayName=ef;var e_=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,k.jsx)(l.sG.div,{role:"separator","aria-orientation":"horizontal",...n,ref:t})});e_.displayName="MenuSeparator";var ey=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e,i=N(r);return(0,k.jsx)(m.i3,{...i,...n,ref:t})});ey.displayName="MenuArrow";var[eb,ew]=I("MenuSub"),eE="MenuSubTrigger",ek=n.forwardRef((e,t)=>{let r=D(eE,e.__scopeMenu),o=B(eE,e.__scopeMenu),s=ew(eE,e.__scopeMenu),l=G(eE,e.__scopeMenu),u=n.useRef(null),{pointerGraceTimerRef:d,onPointerGraceIntentChange:c}=l,h={__scopeMenu:e.__scopeMenu},p=n.useCallback(()=>{u.current&&window.clearTimeout(u.current),u.current=null},[]);return n.useEffect(()=>p,[p]),n.useEffect(()=>{let e=d.current;return()=>{window.clearTimeout(e),c(null)}},[d,c]),(0,k.jsx)(z,{asChild:!0,...h,children:(0,k.jsx)(eo,{id:s.triggerId,"aria-haspopup":"menu","aria-expanded":r.open,"aria-controls":s.contentId,"data-state":eS(r.open),...e,ref:(0,a.t)(t,s.onTriggerChange),onClick:t=>{var n;null==(n=e.onClick)||n.call(e,t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),r.open||r.onOpenChange(!0))},onPointerMove:(0,i.m)(e.onPointerMove,eO(t=>{l.onItemEnter(t),!t.defaultPrevented&&(e.disabled||r.open||u.current||(l.onPointerGraceIntentChange(null),u.current=window.setTimeout(()=>{r.onOpenChange(!0),p()},100)))})),onPointerLeave:(0,i.m)(e.onPointerLeave,eO(e=>{var t,n;p();let i=null==(t=r.content)?void 0:t.getBoundingClientRect();if(i){let t=null==(n=r.content)?void 0:n.dataset.side,a="right"===t,o=i[a?"left":"right"],s=i[a?"right":"left"];l.onPointerGraceIntentChange({area:[{x:e.clientX+(a?-5:5),y:e.clientY},{x:o,y:i.top},{x:s,y:i.top},{x:s,y:i.bottom},{x:o,y:i.bottom}],side:t}),window.clearTimeout(d.current),d.current=window.setTimeout(()=>l.onPointerGraceIntentChange(null),300)}else{if(l.onTriggerLeave(e),e.defaultPrevented)return;l.onPointerGraceIntentChange(null)}})),onKeyDown:(0,i.m)(e.onKeyDown,t=>{let n=""!==l.searchRef.current;if(!e.disabled&&(!n||" "!==t.key)&&R[o.dir].includes(t.key)){var i;r.onOpenChange(!0),null==(i=r.content)||i.focus(),t.preventDefault()}})})})});ek.displayName=eE;var eP="MenuSubContent",ex=n.forwardRef((e,t)=>{let r=V($,e.__scopeMenu),{forceMount:o=r.forceMount,...s}=e,l=D($,e.__scopeMenu),u=B($,e.__scopeMenu),d=ew(eP,e.__scopeMenu),c=n.useRef(null),h=(0,a.s)(t,c);return(0,k.jsx)(j.Provider,{scope:e.__scopeMenu,children:(0,k.jsx)(v.C,{present:o||l.open,children:(0,k.jsx)(j.Slot,{scope:e.__scopeMenu,children:(0,k.jsx)(ee,{id:d.contentId,"aria-labelledby":d.triggerId,...s,ref:h,align:"start",side:"rtl"===u.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{var t;u.isUsingKeyboardRef.current&&(null==(t=c.current)||t.focus()),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,i.m)(e.onFocusOutside,e=>{e.target!==d.trigger&&l.onOpenChange(!1)}),onEscapeKeyDown:(0,i.m)(e.onEscapeKeyDown,e=>{u.onClose(),e.preventDefault()}),onKeyDown:(0,i.m)(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),r=C[u.dir].includes(e.key);if(t&&r){var n;l.onOpenChange(!1),null==(n=d.trigger)||n.focus(),e.preventDefault()}})})})})})});function eS(e){return e?"open":"closed"}function eR(e){return"indeterminate"===e}function eC(e){return eR(e)?"indeterminate":e?"checked":"unchecked"}function eO(e){return t=>"mouse"===t.pointerType?e(t):void 0}ex.displayName=eP;var ej="DropdownMenu",[eT,eA]=(0,o.A)(ej,[M]),eI=M(),[eM,eN]=eT(ej),eL=e=>{let{__scopeDropdownMenu:t,children:r,dir:i,open:a,defaultOpen:o,onOpenChange:l,modal:u=!0}=e,d=eI(t),c=n.useRef(null),[h,p]=(0,s.i)({prop:a,defaultProp:null!=o&&o,onChange:l,caller:ej});return(0,k.jsx)(eM,{scope:t,triggerId:(0,f.B)(),triggerRef:c,contentId:(0,f.B)(),open:h,onOpenChange:p,onOpenToggle:n.useCallback(()=>p(e=>!e),[p]),modal:u,children:(0,k.jsx)(W,{...d,open:h,onOpenChange:p,dir:i,modal:u,children:r})})};eL.displayName=ej;var eU="DropdownMenuTrigger",eD=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,disabled:n=!1,...o}=e,s=eN(eU,r),u=eI(r);return(0,k.jsx)(z,{asChild:!0,...u,children:(0,k.jsx)(l.sG.button,{type:"button",id:s.triggerId,"aria-haspopup":"menu","aria-expanded":s.open,"aria-controls":s.open?s.contentId:void 0,"data-state":s.open?"open":"closed","data-disabled":n?"":void 0,disabled:n,...o,ref:(0,a.t)(t,s.triggerRef),onPointerDown:(0,i.m)(e.onPointerDown,e=>{!n&&0===e.button&&!1===e.ctrlKey&&(s.onOpenToggle(),s.open||e.preventDefault())}),onKeyDown:(0,i.m)(e.onKeyDown,e=>{!n&&(["Enter"," "].includes(e.key)&&s.onOpenToggle(),"ArrowDown"===e.key&&s.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});eD.displayName=eU;var eF=e=>{let{__scopeDropdownMenu:t,...r}=e,n=eI(t);return(0,k.jsx)(H,{...n,...r})};eF.displayName="DropdownMenuPortal";var eB="DropdownMenuContent",eW=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...a}=e,o=eN(eB,r),s=eI(r),l=n.useRef(!1);return(0,k.jsx)(q,{id:o.contentId,"aria-labelledby":o.triggerId,...s,...a,ref:t,onCloseAutoFocus:(0,i.m)(e.onCloseAutoFocus,e=>{var t;l.current||null==(t=o.triggerRef.current)||t.focus(),l.current=!1,e.preventDefault()}),onInteractOutside:(0,i.m)(e.onInteractOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey,n=2===t.button||r;(!o.modal||n)&&(l.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});eW.displayName=eB,n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,i=eI(r);return(0,k.jsx)(et,{...i,...n,ref:t})}).displayName="DropdownMenuGroup";var ez=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,i=eI(r);return(0,k.jsx)(er,{...i,...n,ref:t})});ez.displayName="DropdownMenuLabel";var eZ=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,i=eI(r);return(0,k.jsx)(ea,{...i,...n,ref:t})});eZ.displayName="DropdownMenuItem",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,i=eI(r);return(0,k.jsx)(es,{...i,...n,ref:t})}).displayName="DropdownMenuCheckboxItem",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,i=eI(r);return(0,k.jsx)(ec,{...i,...n,ref:t})}).displayName="DropdownMenuRadioGroup",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,i=eI(r);return(0,k.jsx)(ep,{...i,...n,ref:t})}).displayName="DropdownMenuRadioItem",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,i=eI(r);return(0,k.jsx)(ev,{...i,...n,ref:t})}).displayName="DropdownMenuItemIndicator";var eK=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,i=eI(r);return(0,k.jsx)(e_,{...i,...n,ref:t})});eK.displayName="DropdownMenuSeparator",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,i=eI(r);return(0,k.jsx)(ey,{...i,...n,ref:t})}).displayName="DropdownMenuArrow",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,i=eI(r);return(0,k.jsx)(ek,{...i,...n,ref:t})}).displayName="DropdownMenuSubTrigger",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,i=eI(r);return(0,k.jsx)(ex,{...i,...n,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})}).displayName="DropdownMenuSubContent";var eV=eL,eH=eD,e$=eF,eX=eW,eG=ez,eq=eZ,eY=eK},76227:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(45707).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},77955:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getNextPathnameInfo",{enumerable:!0,get:function(){return o}});let n=r(74934),i=r(53208),a=r(28552);function o(e,t){var r,o;let{basePath:s,i18n:l,trailingSlash:u}=null!=(r=t.nextConfig)?r:{},d={pathname:e,trailingSlash:"/"!==e?e.endsWith("/"):u};s&&(0,a.pathHasPrefix)(d.pathname,s)&&(d.pathname=(0,i.removePathPrefix)(d.pathname,s),d.basePath=s);let c=d.pathname;if(d.pathname.startsWith("/_next/data/")&&d.pathname.endsWith(".json")){let e=d.pathname.replace(/^\/_next\/data\//,"").replace(/\.json$/,"").split("/");d.buildId=e[0],c="index"!==e[1]?"/"+e.slice(1).join("/"):"/",!0===t.parseData&&(d.pathname=c)}if(l){let e=t.i18nProvider?t.i18nProvider.analyze(d.pathname):(0,n.normalizeLocalePath)(d.pathname,l.locales);d.locale=e.detectedLocale,d.pathname=null!=(o=e.pathname)?o:d.pathname,!e.detectedLocale&&d.buildId&&(e=t.i18nProvider?t.i18nProvider.analyze(c):(0,n.normalizeLocalePath)(c,l.locales)).detectedLocale&&(d.locale=e.detectedLocale)}return d}},78931:(e,t)=>{"use strict";function r(){let e=Object.create(null);return{on(t,r){(e[t]||(e[t]=[])).push(r)},off(t,r){e[t]&&e[t].splice(e[t].indexOf(r)>>>0,1)},emit(t){for(var r=arguments.length,n=Array(r>1?r-1:0),i=1;i<r;i++)n[i-1]=arguments[i];(e[t]||[]).slice().map(e=>{e(...n)})}}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return r}})},79715:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.experimental_createTheme=void 0,t.experimental_createTheme=e=>({...e,__type:"prebuilt_appearance"})},83031:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"denormalizePagePath",{enumerable:!0,get:function(){return a}});let n=r(86325),i=r(5527);function a(e){let t=(0,i.normalizePathSep)(e);return t.startsWith("/index/")&&!(0,n.isDynamicRoute)(t)?t.slice(6):"/index"!==t?t:"/"}},83424:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.neobrutalism=void 0;let n=r(79715),i={boxShadow:"3px 3px 0px #000",border:"2px solid #000","&:focus":{boxShadow:"4px 4px 0px #000",border:"2px solid #000",transform:"scale(1.01)"},"&:active":{boxShadow:"2px 2px 0px #000",transform:"translate(1px)"}},a={boxShadow:"3px 3px 0px #000",border:"2px solid #000"};t.neobrutalism=(0,n.experimental_createTheme)({simpleStyles:!0,variables:{colorPrimary:"#DF1B1B",colorShimmer:"rgba(255,255,255,0.64)",fontWeight:{normal:500,medium:600,bold:700}},elements:{cardBox:{boxShadow:"7px 7px 0px #000",border:"3px solid #000"},card:{borderRadius:"0"},headerSubtitle:{color:"#212126"},alternativeMethodsBlockButton:i,socialButtonsIconButton:{...i},selectButton:{...i,...a,transition:"all 0.2s ease-in-out","&:focus":{boxShadow:"4px 4px 0px #000",border:"2px solid #000",transform:"scale(1.01)"}},socialButtonsBlockButton:{...i,color:"#212126"},profileSectionPrimaryButton:i,profileSectionItem:{color:"#212126"},avatarImageActionsUpload:i,menuButton:a,menuList:a,formButtonPrimary:i,navbarButton:i,formFieldAction:{fontWeight:"700"},formFieldInput:{...a,transition:"all 0.2s ease-in-out","&:focus":{boxShadow:"4px 4px 0px #000",border:"2px solid #000",transform:"scale(1.01)"},"&:hover":{...a,transform:"scale(1.01)"}},table:a,tableHead:{color:"#212126"},dividerLine:{background:"#000"},dividerText:{fontWeight:"700",color:"#212126"},footer:{background:"#fff","& div":{color:"#212126"}},footerActionText:{color:"#212126"},footerActionLink:{fontWeight:"700",borderBottom:"3px solid","&:focus":{boxShadow:"none"}},actionCard:{...a},badge:{border:"1px solid #000",background:"#fff",color:"#212126"}}})},83824:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab="//");var t={};(()=>{function e(e,t){void 0===t&&(t={});for(var r=function(e){for(var t=[],r=0;r<e.length;){var n=e[r];if("*"===n||"+"===n||"?"===n){t.push({type:"MODIFIER",index:r,value:e[r++]});continue}if("\\"===n){t.push({type:"ESCAPED_CHAR",index:r++,value:e[r++]});continue}if("{"===n){t.push({type:"OPEN",index:r,value:e[r++]});continue}if("}"===n){t.push({type:"CLOSE",index:r,value:e[r++]});continue}if(":"===n){for(var i="",a=r+1;a<e.length;){var o=e.charCodeAt(a);if(o>=48&&o<=57||o>=65&&o<=90||o>=97&&o<=122||95===o){i+=e[a++];continue}break}if(!i)throw TypeError("Missing parameter name at "+r);t.push({type:"NAME",index:r,value:i}),r=a;continue}if("("===n){var s=1,l="",a=r+1;if("?"===e[a])throw TypeError('Pattern cannot start with "?" at '+a);for(;a<e.length;){if("\\"===e[a]){l+=e[a++]+e[a++];continue}if(")"===e[a]){if(0==--s){a++;break}}else if("("===e[a]&&(s++,"?"!==e[a+1]))throw TypeError("Capturing groups are not allowed at "+a);l+=e[a++]}if(s)throw TypeError("Unbalanced pattern at "+r);if(!l)throw TypeError("Missing pattern at "+r);t.push({type:"PATTERN",index:r,value:l}),r=a;continue}t.push({type:"CHAR",index:r,value:e[r++]})}return t.push({type:"END",index:r,value:""}),t}(e),n=t.prefixes,a=void 0===n?"./":n,o="[^"+i(t.delimiter||"/#?")+"]+?",s=[],l=0,u=0,d="",c=function(e){if(u<r.length&&r[u].type===e)return r[u++].value},h=function(e){var t=c(e);if(void 0!==t)return t;var n=r[u];throw TypeError("Unexpected "+n.type+" at "+n.index+", expected "+e)},p=function(){for(var e,t="";e=c("CHAR")||c("ESCAPED_CHAR");)t+=e;return t};u<r.length;){var f=c("CHAR"),m=c("NAME"),g=c("PATTERN");if(m||g){var v=f||"";-1===a.indexOf(v)&&(d+=v,v=""),d&&(s.push(d),d=""),s.push({name:m||l++,prefix:v,suffix:"",pattern:g||o,modifier:c("MODIFIER")||""});continue}var _=f||c("ESCAPED_CHAR");if(_){d+=_;continue}if(d&&(s.push(d),d=""),c("OPEN")){var v=p(),y=c("NAME")||"",b=c("PATTERN")||"",w=p();h("CLOSE"),s.push({name:y||(b?l++:""),pattern:y&&!b?o:b,prefix:v,suffix:w,modifier:c("MODIFIER")||""});continue}h("END")}return s}function r(e,t){void 0===t&&(t={});var r=a(t),n=t.encode,i=void 0===n?function(e){return e}:n,o=t.validate,s=void 0===o||o,l=e.map(function(e){if("object"==typeof e)return RegExp("^(?:"+e.pattern+")$",r)});return function(t){for(var r="",n=0;n<e.length;n++){var a=e[n];if("string"==typeof a){r+=a;continue}var o=t?t[a.name]:void 0,u="?"===a.modifier||"*"===a.modifier,d="*"===a.modifier||"+"===a.modifier;if(Array.isArray(o)){if(!d)throw TypeError('Expected "'+a.name+'" to not repeat, but got an array');if(0===o.length){if(u)continue;throw TypeError('Expected "'+a.name+'" to not be empty')}for(var c=0;c<o.length;c++){var h=i(o[c],a);if(s&&!l[n].test(h))throw TypeError('Expected all "'+a.name+'" to match "'+a.pattern+'", but got "'+h+'"');r+=a.prefix+h+a.suffix}continue}if("string"==typeof o||"number"==typeof o){var h=i(String(o),a);if(s&&!l[n].test(h))throw TypeError('Expected "'+a.name+'" to match "'+a.pattern+'", but got "'+h+'"');r+=a.prefix+h+a.suffix;continue}if(!u){var p=d?"an array":"a string";throw TypeError('Expected "'+a.name+'" to be '+p)}}return r}}function n(e,t,r){void 0===r&&(r={});var n=r.decode,i=void 0===n?function(e){return e}:n;return function(r){var n=e.exec(r);if(!n)return!1;for(var a=n[0],o=n.index,s=Object.create(null),l=1;l<n.length;l++)!function(e){if(void 0!==n[e]){var r=t[e-1];"*"===r.modifier||"+"===r.modifier?s[r.name]=n[e].split(r.prefix+r.suffix).map(function(e){return i(e,r)}):s[r.name]=i(n[e],r)}}(l);return{path:a,index:o,params:s}}}function i(e){return e.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function a(e){return e&&e.sensitive?"":"i"}function o(e,t,r){void 0===r&&(r={});for(var n=r.strict,o=void 0!==n&&n,s=r.start,l=r.end,u=r.encode,d=void 0===u?function(e){return e}:u,c="["+i(r.endsWith||"")+"]|$",h="["+i(r.delimiter||"/#?")+"]",p=void 0===s||s?"^":"",f=0;f<e.length;f++){var m=e[f];if("string"==typeof m)p+=i(d(m));else{var g=i(d(m.prefix)),v=i(d(m.suffix));if(m.pattern)if(t&&t.push(m),g||v)if("+"===m.modifier||"*"===m.modifier){var _="*"===m.modifier?"?":"";p+="(?:"+g+"((?:"+m.pattern+")(?:"+v+g+"(?:"+m.pattern+"))*)"+v+")"+_}else p+="(?:"+g+"("+m.pattern+")"+v+")"+m.modifier;else p+="("+m.pattern+")"+m.modifier;else p+="(?:"+g+v+")"+m.modifier}}if(void 0===l||l)o||(p+=h+"?"),p+=r.endsWith?"(?="+c+")":"$";else{var y=e[e.length-1],b="string"==typeof y?h.indexOf(y[y.length-1])>-1:void 0===y;o||(p+="(?:"+h+"(?="+c+"))?"),b||(p+="(?="+h+"|"+c+")")}return new RegExp(p,a(r))}function s(t,r,n){if(t instanceof RegExp){if(!r)return t;var i=t.source.match(/\((?!\?)/g);if(i)for(var l=0;l<i.length;l++)r.push({name:l,prefix:"",suffix:"",modifier:"",pattern:""});return t}return Array.isArray(t)?RegExp("(?:"+t.map(function(e){return s(e,r,n).source}).join("|")+")",a(n)):o(e(t,n),r,n)}Object.defineProperty(t,"__esModule",{value:!0}),t.parse=e,t.compile=function(t,n){return r(e(t,n),n)},t.tokensToFunction=r,t.match=function(e,t){var r=[];return n(s(e,r,t),r,t)},t.regexpToFunction=n,t.tokensToRegexp=o,t.pathToRegexp=s})(),e.exports=t})()},83901:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"BloomFilter",{enumerable:!0,get:function(){return r}});class r{static from(e,t){void 0===t&&(t=1e-4);let n=new r(e.length,t);for(let t of e)n.add(t);return n}export(){return{numItems:this.numItems,errorRate:this.errorRate,numBits:this.numBits,numHashes:this.numHashes,bitArray:this.bitArray}}import(e){this.numItems=e.numItems,this.errorRate=e.errorRate,this.numBits=e.numBits,this.numHashes=e.numHashes,this.bitArray=e.bitArray}add(e){this.getHashValues(e).forEach(e=>{this.bitArray[e]=1})}contains(e){return this.getHashValues(e).every(e=>this.bitArray[e])}getHashValues(e){let t=[];for(let r=1;r<=this.numHashes;r++){let n=function(e){let t=0;for(let r=0;r<e.length;r++)t=Math.imul(t^e.charCodeAt(r),0x5bd1e995),t^=t>>>13,t=Math.imul(t,0x5bd1e995);return t>>>0}(""+e+r)%this.numBits;t.push(n)}return t}constructor(e,t=1e-4){this.numItems=e,this.errorRate=t,this.numBits=Math.ceil(-(e*Math.log(t))/(Math.log(2)*Math.log(2))),this.numHashes=Math.ceil(this.numBits/e*Math.log(2)),this.bitArray=Array(this.numBits).fill(0)}}},84179:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getRouteMatcher",{enumerable:!0,get:function(){return i}});let n=r(61074);function i(e){let{re:t,groups:r}=e;return e=>{let i=t.exec(e);if(!i)return!1;let a=e=>{try{return decodeURIComponent(e)}catch(e){throw Object.defineProperty(new n.DecodeError("failed to decode param"),"__NEXT_ERROR_CODE",{value:"E528",enumerable:!1,configurable:!0})}},o={};for(let[e,t]of Object.entries(r)){let r=i[t.pos];void 0!==r&&(t.repeat?o[e]=r.split("/").map(e=>a(e)):o[e]=a(r))}return o}}},84541:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"interpolateAs",{enumerable:!0,get:function(){return a}});let n=r(84179),i=r(91246);function a(e,t,r){let a="",o=(0,i.getRouteRegex)(e),s=o.groups,l=(t!==e?(0,n.getRouteMatcher)(o)(t):"")||r;a=e;let u=Object.keys(s);return u.every(e=>{let t=l[e]||"",{repeat:r,optional:n}=s[e],i="["+(r?"...":"")+e+"]";return n&&(i=(t?"":"/")+"["+i+"]"),r&&!Array.isArray(t)&&(t=[t]),(n||e in l)&&(a=a.replace(i,r?t.map(e=>encodeURIComponent(e)).join("/"):encodeURIComponent(t))||"/")})||(a=""),{params:u,result:a}}},84905:(e,t,r)=>{"use strict";function n(e){return function(){let{cookie:t}=e;if(!t)return{};let{parse:n}=r(71646);return n(Array.isArray(t)?t.join("; "):t)}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getCookieParser",{enumerable:!0,get:function(){return n}})},86325:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getSortedRouteObjects:function(){return n.getSortedRouteObjects},getSortedRoutes:function(){return n.getSortedRoutes},isDynamicRoute:function(){return i.isDynamicRoute}});let n=r(17115),i=r(93687)},88224:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return _},handleClientScriptLoad:function(){return m},initScriptLoader:function(){return g}});let n=r(53854),i=r(59543),a=r(6024),o=n._(r(6341)),s=i._(r(50628)),l=r(36035),u=r(97255),d=r(59235),c=new Map,h=new Set,p=e=>{if(o.default.preinit)return void e.forEach(e=>{o.default.preinit(e,{as:"style"})});{let t=document.head;e.forEach(e=>{let r=document.createElement("link");r.type="text/css",r.rel="stylesheet",r.href=e,t.appendChild(r)})}},f=e=>{let{src:t,id:r,onLoad:n=()=>{},onReady:i=null,dangerouslySetInnerHTML:a,children:o="",strategy:s="afterInteractive",onError:l,stylesheets:d}=e,f=r||t;if(f&&h.has(f))return;if(c.has(t)){h.add(f),c.get(t).then(n,l);return}let m=()=>{i&&i(),h.add(f)},g=document.createElement("script"),v=new Promise((e,t)=>{g.addEventListener("load",function(t){e(),n&&n.call(this,t),m()}),g.addEventListener("error",function(e){t(e)})}).catch(function(e){l&&l(e)});a?(g.innerHTML=a.__html||"",m()):o?(g.textContent="string"==typeof o?o:Array.isArray(o)?o.join(""):"",m()):t&&(g.src=t,c.set(t,v)),(0,u.setAttributesFromProps)(g,e),"worker"===s&&g.setAttribute("type","text/partytown"),g.setAttribute("data-nscript",s),d&&p(d),document.body.appendChild(g)};function m(e){let{strategy:t="afterInteractive"}=e;"lazyOnload"===t?window.addEventListener("load",()=>{(0,d.requestIdleCallback)(()=>f(e))}):f(e)}function g(e){e.forEach(m),[...document.querySelectorAll('[data-nscript="beforeInteractive"]'),...document.querySelectorAll('[data-nscript="beforePageRender"]')].forEach(e=>{let t=e.id||e.getAttribute("src");h.add(t)})}function v(e){let{id:t,src:r="",onLoad:n=()=>{},onReady:i=null,strategy:u="afterInteractive",onError:c,stylesheets:p,...m}=e,{updateScripts:g,scripts:v,getIsSsr:_,appDir:y,nonce:b}=(0,s.useContext)(l.HeadManagerContext),w=(0,s.useRef)(!1);(0,s.useEffect)(()=>{let e=t||r;w.current||(i&&e&&h.has(e)&&i(),w.current=!0)},[i,t,r]);let E=(0,s.useRef)(!1);if((0,s.useEffect)(()=>{if(!E.current){if("afterInteractive"===u)f(e);else"lazyOnload"===u&&("complete"===document.readyState?(0,d.requestIdleCallback)(()=>f(e)):window.addEventListener("load",()=>{(0,d.requestIdleCallback)(()=>f(e))}));E.current=!0}},[e,u]),("beforeInteractive"===u||"worker"===u)&&(g?(v[u]=(v[u]||[]).concat([{id:t,src:r,onLoad:n,onReady:i,onError:c,...m}]),g(v)):_&&_()?h.add(t||r):_&&!_()&&f(e)),y){if(p&&p.forEach(e=>{o.default.preinit(e,{as:"style"})}),"beforeInteractive"===u)if(!r)return m.dangerouslySetInnerHTML&&(m.children=m.dangerouslySetInnerHTML.__html,delete m.dangerouslySetInnerHTML),(0,a.jsx)("script",{nonce:b,dangerouslySetInnerHTML:{__html:"(self.__next_s=self.__next_s||[]).push("+JSON.stringify([0,{...m,id:t}])+")"}});else return o.default.preload(r,m.integrity?{as:"script",integrity:m.integrity,nonce:b,crossOrigin:m.crossOrigin}:{as:"script",nonce:b,crossOrigin:m.crossOrigin}),(0,a.jsx)("script",{nonce:b,dangerouslySetInnerHTML:{__html:"(self.__next_s=self.__next_s||[]).push("+JSON.stringify([r,{...m,id:t}])+")"}});"afterInteractive"===u&&r&&o.default.preload(r,m.integrity?{as:"script",integrity:m.integrity,nonce:b,crossOrigin:m.crossOrigin}:{as:"script",nonce:b,crossOrigin:m.crossOrigin})}return null}Object.defineProperty(v,"__nextScript",{value:!0});let _=v;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},89959:(e,t,r)=>{"use strict";var n=r(50628),i="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},a=n.useState,o=n.useEffect,s=n.useLayoutEffect,l=n.useDebugValue;function u(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!i(e,r)}catch(e){return!0}}var d="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var r=t(),n=a({inst:{value:r,getSnapshot:t}}),i=n[0].inst,d=n[1];return s(function(){i.value=r,i.getSnapshot=t,u(i)&&d({inst:i})},[e,r,t]),o(function(){return u(i)&&d({inst:i}),e(function(){u(i)&&d({inst:i})})},[e]),l(r),r};t.useSyncExternalStore=void 0!==n.useSyncExternalStore?n.useSyncExternalStore:d},90949:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return u}});let n=r(74320),i=r(5343),a=r(44390),o=r(74934),s=r(16369),l=r(95765);function u(e,t,r,u,d,c){let h,p=!1,f=!1,m=(0,l.parseRelativeUrl)(e),g=(0,a.removeTrailingSlash)((0,o.normalizeLocalePath)((0,s.removeBasePath)(m.pathname),c).pathname),v=r=>{let l=(0,n.getPathMatch)(r.source+"",{removeUnnamedParams:!0,strict:!0})(m.pathname);if((r.has||r.missing)&&l){let e=(0,i.matchHas)({headers:{host:document.location.hostname,"user-agent":navigator.userAgent},cookies:document.cookie.split("; ").reduce((e,t)=>{let[r,...n]=t.split("=");return e[r]=n.join("="),e},{})},m.query,r.has,r.missing);e?Object.assign(l,e):l=!1}if(l){if(!r.destination)return f=!0,!0;let n=(0,i.prepareDestination)({appendParamsToQuery:!0,destination:r.destination,params:l,query:u});if(m=n.parsedDestination,e=n.newUrl,Object.assign(u,n.parsedDestination.query),g=(0,a.removeTrailingSlash)((0,o.normalizeLocalePath)((0,s.removeBasePath)(e),c).pathname),t.includes(g))return p=!0,h=g,!0;if((h=d(g))!==e&&t.includes(h))return p=!0,!0}},_=!1;for(let e=0;e<r.beforeFiles.length;e++)v(r.beforeFiles[e]);if(!(p=t.includes(g))){if(!_){for(let e=0;e<r.afterFiles.length;e++)if(v(r.afterFiles[e])){_=!0;break}}if(_||(h=d(g),_=p=t.includes(h)),!_){for(let e=0;e<r.fallback.length;e++)if(v(r.fallback[e])){_=!0;break}}}return{asPath:e,parsedAs:m,matchedPage:p,resolvedHref:h,externalDest:f}}},91028:(e,t,r)=>{"use strict";r.d(t,{w:()=>o});var n=r(37811);function i(e,t){if(e instanceof Promise)throw Error(t)}var a=r(37811);function o(e){let t="object"==typeof e.client?e.client:{},r="object"==typeof e.server?e.server:{},o=e.shared,s=e.runtimeEnv?e.runtimeEnv:{...a.env,...e.experimental__runtimeEnv};return function(e){let t=e.runtimeEnvStrict??e.runtimeEnv??n.env;if(e.emptyStringAsUndefined)for(let[e,r]of Object.entries(t))""===r&&delete t[e];if(e.skipValidation)return t;let r="object"==typeof e.client?e.client:{},a="object"==typeof e.server?e.server:{},o="object"==typeof e.shared?e.shared:{},s=e.isServer??("undefined"==typeof window||"Deno"in window),l=s?{...a,...o,...r}:{...r,...o},u=e.createFinalSchema?.(l,s)["~standard"].validate(t)??function(e,t){let r={},n=[];for(let a in e){let o=e[a]["~standard"].validate(t[a]);if(i(o,`Validation must be synchronous, but ${a} returned a Promise.`),o.issues){n.push(...o.issues.map(e=>({...e,path:[a,...e.path??[]]})));continue}r[a]=o.value}return n.length?{issues:n}:{value:r}}(l,t);i(u,"Validation must be synchronous");let d=e.onValidationError??(e=>{throw console.error("❌ Invalid environment variables:",e),Error("Invalid environment variables")}),c=e.onInvalidAccess??(()=>{throw Error("❌ Attempted to access a server-side environment variable on the client")});if(u.issues)return d(u.issues);let h=t=>!e.clientPrefix||!t.startsWith(e.clientPrefix)&&!(t in o),p=e=>s||!h(e),f=e=>"__esModule"===e||"$$typeof"===e;return new Proxy(Object.assign((e.extends??[]).reduce((e,t)=>Object.assign(e,t),{}),u.value),{get(e,t){if("string"==typeof t&&!f(t))return p(t)?Reflect.get(e,t):c(t)}})}({...e,shared:o,client:t,server:r,clientPrefix:"NEXT_PUBLIC_",runtimeEnv:s})}},91246:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getNamedMiddlewareRegex:function(){return m},getNamedRouteRegex:function(){return f},getRouteRegex:function(){return c},parseParameter:function(){return l}});let n=r(14110),i=r(25336),a=r(94472),o=r(44390),s=/^([^[]*)\[((?:\[[^\]]*\])|[^\]]+)\](.*)$/;function l(e){let t=e.match(s);return t?u(t[2]):u(e)}function u(e){let t=e.startsWith("[")&&e.endsWith("]");t&&(e=e.slice(1,-1));let r=e.startsWith("...");return r&&(e=e.slice(3)),{key:e,repeat:r,optional:t}}function d(e,t,r){let n={},l=1,d=[];for(let c of(0,o.removeTrailingSlash)(e).slice(1).split("/")){let e=i.INTERCEPTION_ROUTE_MARKERS.find(e=>c.startsWith(e)),o=c.match(s);if(e&&o&&o[2]){let{key:t,optional:r,repeat:i}=u(o[2]);n[t]={pos:l++,repeat:i,optional:r},d.push("/"+(0,a.escapeStringRegexp)(e)+"([^/]+?)")}else if(o&&o[2]){let{key:e,repeat:t,optional:i}=u(o[2]);n[e]={pos:l++,repeat:t,optional:i},r&&o[1]&&d.push("/"+(0,a.escapeStringRegexp)(o[1]));let s=t?i?"(?:/(.+?))?":"/(.+?)":"/([^/]+?)";r&&o[1]&&(s=s.substring(1)),d.push(s)}else d.push("/"+(0,a.escapeStringRegexp)(c));t&&o&&o[3]&&d.push((0,a.escapeStringRegexp)(o[3]))}return{parameterizedRoute:d.join(""),groups:n}}function c(e,t){let{includeSuffix:r=!1,includePrefix:n=!1,excludeOptionalTrailingSlash:i=!1}=void 0===t?{}:t,{parameterizedRoute:a,groups:o}=d(e,r,n),s=a;return i||(s+="(?:/)?"),{re:RegExp("^"+s+"$"),groups:o}}function h(e){let t,{interceptionMarker:r,getSafeRouteKey:n,segment:i,routeKeys:o,keyPrefix:s,backreferenceDuplicateKeys:l}=e,{key:d,optional:c,repeat:h}=u(i),p=d.replace(/\W/g,"");s&&(p=""+s+p);let f=!1;(0===p.length||p.length>30)&&(f=!0),isNaN(parseInt(p.slice(0,1)))||(f=!0),f&&(p=n());let m=p in o;s?o[p]=""+s+d:o[p]=d;let g=r?(0,a.escapeStringRegexp)(r):"";return t=m&&l?"\\k<"+p+">":h?"(?<"+p+">.+?)":"(?<"+p+">[^/]+?)",c?"(?:/"+g+t+")?":"/"+g+t}function p(e,t,r,l,u){let d,c=(d=0,()=>{let e="",t=++d;for(;t>0;)e+=String.fromCharCode(97+(t-1)%26),t=Math.floor((t-1)/26);return e}),p={},f=[];for(let d of(0,o.removeTrailingSlash)(e).slice(1).split("/")){let e=i.INTERCEPTION_ROUTE_MARKERS.some(e=>d.startsWith(e)),o=d.match(s);if(e&&o&&o[2])f.push(h({getSafeRouteKey:c,interceptionMarker:o[1],segment:o[2],routeKeys:p,keyPrefix:t?n.NEXT_INTERCEPTION_MARKER_PREFIX:void 0,backreferenceDuplicateKeys:u}));else if(o&&o[2]){l&&o[1]&&f.push("/"+(0,a.escapeStringRegexp)(o[1]));let e=h({getSafeRouteKey:c,segment:o[2],routeKeys:p,keyPrefix:t?n.NEXT_QUERY_PARAM_PREFIX:void 0,backreferenceDuplicateKeys:u});l&&o[1]&&(e=e.substring(1)),f.push(e)}else f.push("/"+(0,a.escapeStringRegexp)(d));r&&o&&o[3]&&f.push((0,a.escapeStringRegexp)(o[3]))}return{namedParameterizedRoute:f.join(""),routeKeys:p}}function f(e,t){var r,n,i;let a=p(e,t.prefixRouteKeys,null!=(r=t.includeSuffix)&&r,null!=(n=t.includePrefix)&&n,null!=(i=t.backreferenceDuplicateKeys)&&i),o=a.namedParameterizedRoute;return t.excludeOptionalTrailingSlash||(o+="(?:/)?"),{...c(e,t),namedRegex:"^"+o+"$",routeKeys:a.routeKeys}}function m(e,t){let{parameterizedRoute:r}=d(e,!1,!1),{catchAll:n=!0}=t;if("/"===r)return{namedRegex:"^/"+(n?".*":"")+"$"};let{namedParameterizedRoute:i}=p(e,!1,!1,!1,!1);return{namedRegex:"^"+i+(n?"(?:(/.*)?)":"")+"$"}}},92004:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"detectDomainLocale",{enumerable:!0,get:function(){return r}});let r=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r]};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},92144:(e,t,r)=>{"use strict";e.exports=r(89959)},93181:(e,t,r)=>{"use strict";let n;Object.defineProperty(t,"__esModule",{value:!0}),t.GoogleAnalytics=function(e){let{gaId:t,debugMode:r,dataLayerName:s="dataLayer",nonce:l}=e;return void 0===n&&(n=s),(0,a.useEffect)(()=>{performance.mark("mark_feature_usage",{detail:{feature:"next-third-parties-ga"}})},[]),(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(o.default,{id:"_next-ga-init",dangerouslySetInnerHTML:{__html:"\n          window['".concat(s,"'] = window['").concat(s,"'] || [];\n          function gtag(){window['").concat(s,"'].push(arguments);}\n          gtag('js', new Date());\n\n          gtag('config', '").concat(t,"' ").concat(r?",{ 'debug_mode': true }":"",");")},nonce:l}),(0,i.jsx)(o.default,{id:"_next-ga",src:"https://www.googletagmanager.com/gtag/js?id=".concat(t),nonce:l})]})},t.sendGAEvent=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];if(void 0===n)return void console.warn("@next/third-parties: GA has not been initialized");window[n]?window[n].push(arguments):console.warn("@next/third-parties: GA dataLayer ".concat(n," does not exist"))};let i=r(6024),a=r(50628),o=function(e){return e&&e.__esModule?e:{default:e}}(r(70787))},93571:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addLocale",{enumerable:!0,get:function(){return n}}),r(7749);let n=function(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},93687:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isDynamicRoute",{enumerable:!0,get:function(){return o}});let n=r(25336),i=/\/[^/]*\[[^/]+\][^/]*(?=\/|$)/,a=/\/\[[^/]+\](?=\/|$)/;function o(e,t){return(void 0===t&&(t=!0),(0,n.isInterceptionRouteAppPath)(e)&&(e=(0,n.extractInterceptionRouteInformation)(e).interceptedRoute),t)?a.test(e):i.test(e)}},94134:(e,t,r)=>{"use strict";var n,i;r.d(t,{LocalhostController:()=>s}),r(50628);function a(e,t){if(!n)return()=>{};let r=e=>{try{let r=e.data&&"string"==typeof e.data?JSON.parse(e.data):void 0;r&&t(r)}catch(e){}};return n.addEventListener(e,r),()=>{null==n||n.removeEventListener(e,r)}}async function o(e,t){if(!i)return{error:"No toolbar server"};try{let r=await fetch("".concat(i).concat(e),t?{method:"POST",body:JSON.stringify(t)}:void 0),n=await r.json();return r.ok?{result:n}:{error:n.error}}catch(e){return{error:"".concat(e)}}}function s(){return null}},94472:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"escapeStringRegexp",{enumerable:!0,get:function(){return i}});let r=/[|\\{}()[\]^$+*?.-]/,n=/[|\\{}()[\]^$+*?.-]/g;function i(e){return r.test(e)?e.replace(n,"\\$&"):e}},95765:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseRelativeUrl",{enumerable:!0,get:function(){return a}});let n=r(61074),i=r(78756);function a(e,t,r){void 0===r&&(r=!0);let a=new URL((0,n.getLocationOrigin)()),o=t?new URL(t,a):e.startsWith(".")?new URL(window.location.href):a,{pathname:s,searchParams:l,search:u,hash:d,href:c,origin:h}=new URL(e,o);if(h!==a.origin)throw Object.defineProperty(Error("invariant: invalid relative URL, router received "+e),"__NEXT_ERROR_CODE",{value:"E159",enumerable:!1,configurable:!0});return{pathname:s,query:r?(0,i.searchParamsToUrlQuery)(l):void 0,search:u,hash:d,href:c.slice(h.length)}}},95774:(e,t)=>{"use strict";function r(e,t){let r={};return Object.keys(e).forEach(n=>{t.includes(n)||(r[n]=e[n])}),r}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"omit",{enumerable:!0,get:function(){return r}})},97255:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"setAttributesFromProps",{enumerable:!0,get:function(){return a}});let r={acceptCharset:"accept-charset",className:"class",htmlFor:"for",httpEquiv:"http-equiv",noModule:"noModule"},n=["onLoad","onReady","dangerouslySetInnerHTML","children","onError","strategy","stylesheets"];function i(e){return["async","defer","noModule"].includes(e)}function a(e,t){for(let[a,o]of Object.entries(t)){if(!t.hasOwnProperty(a)||n.includes(a)||void 0===o)continue;let s=r[a]||a.toLowerCase();"SCRIPT"===e.tagName&&i(s)?e[s]=!!o:e.setAttribute(s,String(o)),(!1===o||"SCRIPT"===e.tagName&&i(s)&&(!o||"false"===o))&&(e.setAttribute(s,""),e.removeAttribute(s))}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},99008:(e,t,r)=>{"use strict";function n(e,t){return e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeLocale",{enumerable:!0,get:function(){return n}}),r(36550),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},99512:(e,t,r)=>{"use strict";var n=r(50628),i="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},a=n.useState,o=n.useEffect,s=n.useLayoutEffect,l=n.useDebugValue;function u(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!i(e,r)}catch(e){return!0}}var d="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var r=t(),n=a({inst:{value:r,getSnapshot:t}}),i=n[0].inst,d=n[1];return s(function(){i.value=r,i.getSnapshot=t,u(i)&&d({inst:i})},[e,r,t]),o(function(){return u(i)&&d({inst:i}),e(function(){u(i)&&d({inst:i})})},[e]),l(r),r};t.useSyncExternalStore=void 0!==n.useSyncExternalStore?n.useSyncExternalStore:d}}]);