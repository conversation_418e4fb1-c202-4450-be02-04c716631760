"use strict";exports.id=2200,exports.ids=[1127,2200,3508,6365,8478],exports.modules={28478:(e,n,t)=>{t.r(n),t.d(n,{default:()=>i});var a=t(14187);let r=Object.freeze({displayName:"Lua",name:"lua",patterns:[{begin:"\\b(?:(local)\\s+)?(function)\\b(?![,:])",beginCaptures:{1:{name:"keyword.local.lua"},2:{name:"keyword.control.lua"}},end:"(?<=[)\\-{}\\[\\]\"'])",name:"meta.function.lua",patterns:[{include:"#comment"},{begin:"(\\()",beginCaptures:{1:{name:"punctuation.definition.parameters.begin.lua"}},end:"(\\))|(?=[\\-\\.{}\\[\\]\"'])",endCaptures:{1:{name:"punctuation.definition.parameters.finish.lua"}},name:"meta.parameter.lua",patterns:[{include:"#comment"},{match:"[a-zA-Z_]\\w*",name:"variable.parameter.function.lua"},{match:",",name:"punctuation.separator.arguments.lua"},{begin:":",beginCaptures:{0:{name:"punctuation.separator.arguments.lua"}},end:"(?=[),])",patterns:[{include:"#emmydoc.type"}]}]},{match:"\\b([a-zA-Z_]\\w*)\\b\\s*(?=:)",name:"entity.name.class.lua"},{match:"\\b([a-zA-Z_]\\w*)\\b",name:"entity.name.function.lua"}]},{match:"(?<![\\w\\d.])0[xX][0-9A-Fa-f]+(\\.[0-9A-Fa-f]*)?([eE]-?\\d*)?([pP][-+]\\d+)?",name:"constant.numeric.float.hexadecimal.lua"},{match:"(?<![\\w\\d.])0[xX]\\.[0-9A-Fa-f]+([eE]-?\\d*)?([pP][-+]\\d+)?",name:"constant.numeric.float.hexadecimal.lua"},{match:"(?<![\\w\\d.])0[xX][0-9A-Fa-f]+(?![pPeE.0-9])",name:"constant.numeric.integer.hexadecimal.lua"},{match:"(?<![\\w\\d.])\\d+(\\.\\d*)?([eE]-?\\d*)?",name:"constant.numeric.float.lua"},{match:"(?<![\\w\\d.])\\.\\d+([eE]-?\\d*)?",name:"constant.numeric.float.lua"},{match:"(?<![\\w\\d.])\\d+(?![pPeE.0-9])",name:"constant.numeric.integer.lua"},{include:"#string"},{captures:{1:{name:"punctuation.definition.comment.lua"}},match:"\\A(#!).*$\\n?",name:"comment.line.shebang.lua"},{include:"#comment"},{captures:{1:{name:"keyword.control.goto.lua"},2:{name:"string.tag.lua"}},match:"\\b(goto)\\s+([a-zA-Z_]\\w*)"},{captures:{1:{name:"punctuation.section.embedded.begin.lua"},2:{name:"punctuation.section.embedded.end.lua"}},match:"(::)\\s*[a-zA-Z_]\\w*\\s*(::)",name:"string.tag.lua"},{captures:{0:{name:"storage.type.attribute.lua"}},match:"<\\s*(const|close)\\s*>"},{match:"<[a-zA-Z_\\*][a-zA-Z0-9_\\.\\*\\-]*>",name:"storage.type.generic.lua"},{match:"\\b(break|do|else|for|if|elseif|goto|return|then|repeat|while|until|end|in)\\b",name:"keyword.control.lua"},{match:"\\b(local)\\b",name:"keyword.local.lua"},{match:"\\b(function)\\b(?![,:])",name:"keyword.control.lua"},{match:"(?<![^.]\\.|:)\\b(false|nil(?!:)|true|_ENV|_G|_VERSION|math\\.(pi|huge|maxinteger|mininteger)|utf8\\.charpattern|io\\.(stdin|stdout|stderr)|package\\.(config|cpath|loaded|loaders|path|preload|searchers))\\b|(?<![.])\\.{3}(?!\\.)",name:"constant.language.lua"},{match:"(?<![^.]\\.|:)\\b(self)\\b",name:"variable.language.self.lua"},{match:"(?<![^.]\\.|:)\\b(assert|collectgarbage|dofile|error|getfenv|getmetatable|ipairs|load|loadfile|loadstring|module|next|pairs|pcall|print|rawequal|rawget|rawlen|rawset|require|select|setfenv|setmetatable|tonumber|tostring|type|unpack|xpcall)\\b(?!\\s*=(?!=))",name:"support.function.lua"},{match:"(?<![^.]\\.|:)\\b(async)\\b(?!\\s*=(?!=))",name:"entity.name.tag.lua"},{match:"(?<![^.]\\.|:)\\b(coroutine\\.(create|isyieldable|close|resume|running|status|wrap|yield)|string\\.(byte|char|dump|find|format|gmatch|gsub|len|lower|match|pack|packsize|rep|reverse|sub|unpack|upper)|table\\.(concat|insert|maxn|move|pack|remove|sort|unpack)|math\\.(abs|acos|asin|atan2?|ceil|cosh?|deg|exp|floor|fmod|frexp|ldexp|log|log10|max|min|modf|pow|rad|random|randomseed|sinh?|sqrt|tanh?|tointeger|type)|io\\.(close|flush|input|lines|open|output|popen|read|tmpfile|type|write)|os\\.(clock|date|difftime|execute|exit|getenv|remove|rename|setlocale|time|tmpname)|package\\.(loadlib|seeall|searchpath)|debug\\.(debug|[gs]etfenv|[gs]ethook|getinfo|[gs]etlocal|[gs]etmetatable|getregistry|[gs]etupvalue|[gs]etuservalue|set[Cc]stacklimit|traceback|upvalueid|upvaluejoin)|bit32\\.(arshift|band|bnot|bor|btest|bxor|extract|replace|lrotate|lshift|rrotate|rshift)|utf8\\.(char|codes|codepoint|len|offset))\\b(?!\\s*=(?!=))",name:"support.function.library.lua"},{match:"\\b(and|or|not|\\|\\||\\&\\&|!)\\b",name:"keyword.operator.lua"},{match:"\\b([a-zA-Z_]\\w*)\\b(?=\\s*(?:[({\"']|\\[\\[))",name:"support.function.any-method.lua"},{match:"\\b([a-zA-Z_]\\w*)\\b(?=\\s*\\??:)",name:"entity.name.class.lua"},{match:"(?<=[^.]\\.|:)\\b([a-zA-Z_]\\w*)\\b(?!\\s*=\\s*\\b(function)\\b)",name:"entity.other.attribute.lua"},{match:"\\b([a-zA-Z_]\\w*)\\b(?!\\s*=\\s*\\b(function)\\b)",name:"variable.other.lua"},{match:"\\b([a-zA-Z_]\\w*)\\b(?=\\s*=\\s*\\b(function)\\b)",name:"entity.name.function.lua"},{match:"\\+|-|%|#|\\*|\\/|\\^|==?|~=|!=|<=?|>=?|(?<!\\.)\\.{2}(?!\\.)",name:"keyword.operator.lua"}],repository:{comment:{patterns:[{begin:"(^[ \\t]+)?(?=--)",beginCaptures:{1:{name:"punctuation.whitespace.comment.leading.lua"}},end:"(?!\\G)((?!^)[ \\t]+\\n)?",endCaptures:{1:{name:"punctuation.whitespace.comment.trailing.lua"}},patterns:[{begin:"--\\[(=*)\\[@@@",beginCaptures:{0:{name:"punctuation.definition.comment.begin.lua"}},end:"(--)?\\]\\1\\]",endCaptures:{0:{name:"punctuation.definition.comment.end.lua"}},name:"",patterns:[{include:"source.lua"}]},{begin:"--\\[(=*)\\[",beginCaptures:{0:{name:"punctuation.definition.comment.begin.lua"}},end:"(--)?\\]\\1\\]",endCaptures:{0:{name:"punctuation.definition.comment.end.lua"}},name:"comment.block.lua",patterns:[{include:"#emmydoc"},{include:"#ldoc_tag"}]},{begin:"----",beginCaptures:{0:{name:"punctuation.definition.comment.lua"}},end:"\\n",name:"comment.line.double-dash.lua"},{begin:"---",beginCaptures:{0:{name:"punctuation.definition.comment.lua"}},end:"\\n",name:"comment.line.double-dash.documentation.lua",patterns:[{include:"#emmydoc"},{include:"#ldoc_tag"}]},{begin:"--",beginCaptures:{0:{name:"punctuation.definition.comment.lua"}},end:"\\n",name:"comment.line.double-dash.lua",patterns:[{include:"#ldoc_tag"}]}]},{begin:"\\/\\*",beginCaptures:{0:{name:"punctuation.definition.comment.begin.lua"}},end:"\\*\\/",endCaptures:{0:{name:"punctuation.definition.comment.end.lua"}},name:"comment.block.lua",patterns:[{include:"#emmydoc"},{include:"#ldoc_tag"}]}]},emmydoc:{patterns:[{begin:"(?<=---)[ \\t]*@class",beginCaptures:{0:{name:"storage.type.annotation.lua"}},end:"(?=[\\n@#])",patterns:[{match:"\\b([a-zA-Z_\\*][a-zA-Z0-9_\\.\\*\\-]*)",name:"support.class.lua"},{match:":|,",name:"keyword.operator.lua"}]},{begin:"(?<=---)[ \\t]*@enum",beginCaptures:{0:{name:"storage.type.annotation.lua"}},end:"(?=[\\n@#])",patterns:[{begin:"\\b([a-zA-Z_\\*][a-zA-Z0-9_\\.\\*\\-]*)",beginCaptures:{0:{name:"variable.lua"}},end:"(?=\\n)"}]},{begin:"(?<=---)[ \\t]*@type",beginCaptures:{0:{name:"storage.type.annotation.lua"}},end:"(?=[\\n@#])",patterns:[{include:"#emmydoc.type"}]},{begin:"(?<=---)[ \\t]*@alias",beginCaptures:{0:{name:"storage.type.annotation.lua"}},end:"(?=[\\n@#])",patterns:[{begin:"\\b([a-zA-Z_\\*][a-zA-Z0-9_\\.\\*\\-]*)",beginCaptures:{0:{name:"variable.lua"}},end:"(?=[\\n#])",patterns:[{include:"#emmydoc.type"}]}]},{begin:"(?<=---)[ \\t]*(@operator)\\s*(\\b[a-z]+)?",beginCaptures:{1:{name:"storage.type.annotation.lua"},2:{name:"support.function.library.lua"}},end:"(?=[\\n@#])",patterns:[{include:"#emmydoc.type"}]},{begin:"(?<=---)[ \\t]*@cast",beginCaptures:{0:{name:"storage.type.annotation.lua"}},end:"(?=[\\n@#])",patterns:[{begin:"\\b([a-zA-Z_\\*][a-zA-Z0-9_\\.\\*\\-]*)",beginCaptures:{0:{name:"variable.other.lua"}},end:"(?=\\n)",patterns:[{include:"#emmydoc.type"},{match:"([+-|])",name:"keyword.operator.lua"}]}]},{begin:"(?<=---)[ \\t]*@param",beginCaptures:{0:{name:"storage.type.annotation.lua"}},end:"(?=[\\n@#])",patterns:[{begin:"\\b([a-zA-Z_]\\w*)\\b(\\??)",beginCaptures:{1:{name:"entity.name.variable.lua"},2:{name:"keyword.operator.lua"}},end:"(?=[\\n#])",patterns:[{include:"#emmydoc.type"}]}]},{begin:"(?<=---)[ \\t]*@return",beginCaptures:{0:{name:"storage.type.annotation.lua"}},end:"(?=[\\n@#])",patterns:[{match:"\\?",name:"keyword.operator.lua"},{include:"#emmydoc.type"}]},{begin:"(?<=---)[ \\t]*@field",beginCaptures:{0:{name:"storage.type.annotation.lua"}},end:"(?=[\\n@#])",patterns:[{begin:"(\\b([a-zA-Z_]\\w*)\\b|(\\[))(\\??)",beginCaptures:{2:{name:"entity.name.variable.lua"},3:{name:"keyword.operator.lua"}},end:"(?=[\\n#])",patterns:[{include:"#string"},{include:"#emmydoc.type"},{match:"\\]",name:"keyword.operator.lua"}]}]},{begin:"(?<=---)[ \\t]*@generic",beginCaptures:{0:{name:"storage.type.annotation.lua"}},end:"(?=[\\n@#])",patterns:[{begin:"\\b([a-zA-Z_]\\w*)\\b",beginCaptures:{0:{name:"storage.type.generic.lua"}},end:"(?=\\n)|(,)",endCaptures:{0:{name:"keyword.operator.lua"}},patterns:[{match:":",name:"keyword.operator.lua"},{include:"#emmydoc.type"}]}]},{begin:"(?<=---)[ \\t]*@vararg",beginCaptures:{0:{name:"storage.type.annotation.lua"}},end:"(?=[\\n@#])",patterns:[{include:"#emmydoc.type"}]},{begin:"(?<=---)[ \\t]*@overload",beginCaptures:{0:{name:"storage.type.annotation.lua"}},end:"(?=[\\n@#])",patterns:[{include:"#emmydoc.type"}]},{begin:"(?<=---)[ \\t]*@deprecated",beginCaptures:{0:{name:"storage.type.annotation.lua"}},end:"(?=[\\n@#])"},{begin:"(?<=---)[ \\t]*@meta",beginCaptures:{0:{name:"storage.type.annotation.lua"}},end:"(?=[\\n@#])"},{begin:"(?<=---)[ \\t]*@private",beginCaptures:{0:{name:"storage.type.annotation.lua"}},end:"(?=[\\n@#])"},{begin:"(?<=---)[ \\t]*@protected",beginCaptures:{0:{name:"storage.type.annotation.lua"}},end:"(?=[\\n@#])"},{begin:"(?<=---)[ \\t]*@package",beginCaptures:{0:{name:"storage.type.annotation.lua"}},end:"(?=[\\n@#])"},{begin:"(?<=---)[ \\t]*@version",beginCaptures:{0:{name:"storage.type.annotation.lua"}},end:"(?=[\\n@#])",patterns:[{match:"\\b(5\\.1|5\\.2|5\\.3|5\\.4|JIT)\\b",name:"support.class.lua"},{match:",|>|<",name:"keyword.operator.lua"}]},{begin:"(?<=---)[ \\t]*@see",beginCaptures:{0:{name:"storage.type.annotation.lua"}},end:"(?=[\\n@#])",patterns:[{match:"\\b([a-zA-Z_\\*][a-zA-Z0-9_\\.\\*\\-]*)",name:"support.class.lua"},{match:"#",name:"keyword.operator.lua"}]},{begin:"(?<=---)[ \\t]*@diagnostic",beginCaptures:{0:{name:"storage.type.annotation.lua"}},end:"(?=[\\n@#])",patterns:[{begin:"([a-zA-Z_\\-0-9]+)[ \\t]*(:)?",beginCaptures:{1:{name:"keyword.other.unit"},2:{name:"keyword.operator.unit"}},end:"(?=\\n)",patterns:[{match:"\\b([a-zA-Z_\\*][a-zA-Z0-9_\\-]*)",name:"support.class.lua"},{match:",",name:"keyword.operator.lua"}]}]},{begin:"(?<=---)[ \\t]*@module",beginCaptures:{0:{name:"storage.type.annotation.lua"}},end:"(?=[\\n@#])",patterns:[{include:"#string"}]},{match:"(?<=---)[ \\t]*@(async|nodiscard)",name:"storage.type.annotation.lua"},{begin:"(?<=---)\\|\\s*[>+]?",beginCaptures:{0:{name:"storage.type.annotation.lua"}},end:"(?=[\\n@#])",patterns:[{include:"#string"}]}]},"emmydoc.type":{patterns:[{begin:"\\bfun\\b",beginCaptures:{0:{name:"keyword.control.lua"}},end:"(?=[\\s#])",patterns:[{match:"[(),:?][ \\t]*",name:"keyword.operator.lua"},{match:"([a-zA-Z_][a-zA-Z0-9_\\.\\*\\[\\]<>\\,\\-]*)(?<!,)[ \\t]*(?=\\??:)",name:"entity.name.variable.lua"},{include:"#emmydoc.type"},{include:"#string"}]},{match:"<[a-zA-Z_\\*][a-zA-Z0-9_\\.\\*\\-]*>",name:"storage.type.generic.lua"},{match:"\\basync\\b",name:"entity.name.tag.lua"},{match:"[{}:\\,?\\|\\`][ \\t]*",name:"keyword.operator.lua"},{begin:"(?=[a-zA-Z_\\.\\*\"'\\[])",end:"(?=[\\s)\\,?:}\\|#])",patterns:[{match:"([a-zA-Z0-9_\\.\\*\\[\\]<>\\,\\-]+)(?<!,)[ \\t]*",name:"support.type.lua"},{match:"(\\.\\.\\.)[ \\t]*",name:"constant.language.lua"},{include:"#string"}]}]},escaped_char:{patterns:[{match:`\\\\[abfnrtv\\\\"'\\n]`,name:"constant.character.escape.lua"},{match:"\\\\z[\\n\\t ]*",name:"constant.character.escape.lua"},{match:"\\\\\\d{1,3}",name:"constant.character.escape.byte.lua"},{match:"\\\\x[0-9A-Fa-f][0-9A-Fa-f]",name:"constant.character.escape.byte.lua"},{match:"\\\\u\\{[0-9A-Fa-f]+\\}",name:"constant.character.escape.unicode.lua"},{match:"\\\\.",name:"invalid.illegal.character.escape.lua"}]},ldoc_tag:{captures:{1:{name:"punctuation.definition.block.tag.ldoc"},2:{name:"storage.type.class.ldoc"}},match:"\\G[ \\t]*(@)(alias|annotation|author|charset|class|classmod|comment|constructor|copyright|description|example|export|factory|field|file|fixme|function|include|lfunction|license|local|module|name|param|pragma|private|raise|release|return|script|section|see|set|static|submodule|summary|tfield|thread|tparam|treturn|todo|topic|type|usage|warning|within)\\b"},string:{patterns:[{begin:"'",beginCaptures:{0:{name:"punctuation.definition.string.begin.lua"}},end:"'[ \\t]*|(?=\\n)",endCaptures:{0:{name:"punctuation.definition.string.end.lua"}},name:"string.quoted.single.lua",patterns:[{include:"#escaped_char"}]},{begin:'"',beginCaptures:{0:{name:"punctuation.definition.string.begin.lua"}},end:'"[ \\t]*|(?=\\n)',endCaptures:{0:{name:"punctuation.definition.string.end.lua"}},name:"string.quoted.double.lua",patterns:[{include:"#escaped_char"}]},{begin:"`",beginCaptures:{0:{name:"punctuation.definition.string.begin.lua"}},end:"`[ \\t]*|(?=\\n)",endCaptures:{0:{name:"punctuation.definition.string.end.lua"}},name:"string.quoted.double.lua"},{begin:"(?<=\\.cdef)\\s*(\\[(=*)\\[)",beginCaptures:{0:{name:"string.quoted.other.multiline.lua"},1:{name:"punctuation.definition.string.begin.lua"}},contentName:"meta.embedded.lua",end:"(\\]\\2\\])[ \\t]*",endCaptures:{0:{name:"string.quoted.other.multiline.lua"},1:{name:"punctuation.definition.string.end.lua"}},patterns:[{include:"source.c"}]},{begin:"(?\x3c!--)\\[(=*)\\[",beginCaptures:{0:{name:"punctuation.definition.string.begin.lua"}},end:"\\]\\1\\][ \\t]*",endCaptures:{0:{name:"punctuation.definition.string.end.lua"}},name:"string.quoted.other.multiline.lua"}]}},scopeName:"source.lua",embeddedLangs:["c"]});var i=[...a.default,r]},31127:(e,n,t)=>{t.r(n),t.d(n,{default:()=>i});var a=t(96736);let r=Object.freeze({displayName:"XML",name:"xml",patterns:[{begin:"(<\\?)\\s*([-_a-zA-Z0-9]+)",captures:{1:{name:"punctuation.definition.tag.xml"},2:{name:"entity.name.tag.xml"}},end:"(\\?>)",name:"meta.tag.preprocessor.xml",patterns:[{match:" ([a-zA-Z-]+)",name:"entity.other.attribute-name.xml"},{include:"#doublequotedString"},{include:"#singlequotedString"}]},{begin:"(<!)(DOCTYPE)\\s+([:a-zA-Z_][:a-zA-Z0-9_.-]*)",captures:{1:{name:"punctuation.definition.tag.xml"},2:{name:"keyword.other.doctype.xml"},3:{name:"variable.language.documentroot.xml"}},end:"\\s*(>)",name:"meta.tag.sgml.doctype.xml",patterns:[{include:"#internalSubset"}]},{include:"#comments"},{begin:"(<)((?:([-_a-zA-Z0-9]+)(:))?([-_a-zA-Z0-9:]+))(?=(\\s[^>]*)?></\\2>)",beginCaptures:{1:{name:"punctuation.definition.tag.xml"},2:{name:"entity.name.tag.xml"},3:{name:"entity.name.tag.namespace.xml"},4:{name:"punctuation.separator.namespace.xml"},5:{name:"entity.name.tag.localname.xml"}},end:"(>)(</)((?:([-_a-zA-Z0-9]+)(:))?([-_a-zA-Z0-9:]+))(>)",endCaptures:{1:{name:"punctuation.definition.tag.xml"},2:{name:"punctuation.definition.tag.xml"},3:{name:"entity.name.tag.xml"},4:{name:"entity.name.tag.namespace.xml"},5:{name:"punctuation.separator.namespace.xml"},6:{name:"entity.name.tag.localname.xml"},7:{name:"punctuation.definition.tag.xml"}},name:"meta.tag.no-content.xml",patterns:[{include:"#tagStuff"}]},{begin:"(</?)(?:([-\\w\\.]+)((:)))?([-\\w\\.:]+)",captures:{1:{name:"punctuation.definition.tag.xml"},2:{name:"entity.name.tag.namespace.xml"},3:{name:"entity.name.tag.xml"},4:{name:"punctuation.separator.namespace.xml"},5:{name:"entity.name.tag.localname.xml"}},end:"(/?>)",name:"meta.tag.xml",patterns:[{include:"#tagStuff"}]},{include:"#entity"},{include:"#bare-ampersand"},{begin:"<%@",beginCaptures:{0:{name:"punctuation.section.embedded.begin.xml"}},end:"%>",endCaptures:{0:{name:"punctuation.section.embedded.end.xml"}},name:"source.java-props.embedded.xml",patterns:[{match:"page|include|taglib",name:"keyword.other.page-props.xml"}]},{begin:"<%[!=]?(?!--)",beginCaptures:{0:{name:"punctuation.section.embedded.begin.xml"}},end:"(?!--)%>",endCaptures:{0:{name:"punctuation.section.embedded.end.xml"}},name:"source.java.embedded.xml",patterns:[{include:"source.java"}]},{begin:"<!\\[CDATA\\[",beginCaptures:{0:{name:"punctuation.definition.string.begin.xml"}},end:"]]>",endCaptures:{0:{name:"punctuation.definition.string.end.xml"}},name:"string.unquoted.cdata.xml"}],repository:{EntityDecl:{begin:"(<!)(ENTITY)\\s+(%\\s+)?([:a-zA-Z_][:a-zA-Z0-9_.-]*)(\\s+(?:SYSTEM|PUBLIC)\\s+)?",captures:{1:{name:"punctuation.definition.tag.xml"},2:{name:"keyword.other.entity.xml"},3:{name:"punctuation.definition.entity.xml"},4:{name:"variable.language.entity.xml"},5:{name:"keyword.other.entitytype.xml"}},end:"(>)",patterns:[{include:"#doublequotedString"},{include:"#singlequotedString"}]},"bare-ampersand":{match:"&",name:"invalid.illegal.bad-ampersand.xml"},comments:{patterns:[{begin:"<%--",captures:{0:{name:"punctuation.definition.comment.xml"},end:"--%>",name:"comment.block.xml"}},{begin:"\x3c!--",captures:{0:{name:"punctuation.definition.comment.xml"}},end:"--\x3e",name:"comment.block.xml",patterns:[{begin:"--(?!>)",captures:{0:{name:"invalid.illegal.bad-comments-or-CDATA.xml"}}}]}]},doublequotedString:{begin:'"',beginCaptures:{0:{name:"punctuation.definition.string.begin.xml"}},end:'"',endCaptures:{0:{name:"punctuation.definition.string.end.xml"}},name:"string.quoted.double.xml",patterns:[{include:"#entity"},{include:"#bare-ampersand"}]},entity:{captures:{1:{name:"punctuation.definition.constant.xml"},3:{name:"punctuation.definition.constant.xml"}},match:"(&)([:a-zA-Z_][:a-zA-Z0-9_.-]*|#\\d+|#x[0-9a-fA-F]+)(;)",name:"constant.character.entity.xml"},internalSubset:{begin:"(\\[)",captures:{1:{name:"punctuation.definition.constant.xml"}},end:"(\\])",name:"meta.internalsubset.xml",patterns:[{include:"#EntityDecl"},{include:"#parameterEntity"},{include:"#comments"}]},parameterEntity:{captures:{1:{name:"punctuation.definition.constant.xml"},3:{name:"punctuation.definition.constant.xml"}},match:"(%)([:a-zA-Z_][:a-zA-Z0-9_.-]*)(;)",name:"constant.character.parameter-entity.xml"},singlequotedString:{begin:"'",beginCaptures:{0:{name:"punctuation.definition.string.begin.xml"}},end:"'",endCaptures:{0:{name:"punctuation.definition.string.end.xml"}},name:"string.quoted.single.xml",patterns:[{include:"#entity"},{include:"#bare-ampersand"}]},tagStuff:{patterns:[{captures:{1:{name:"entity.other.attribute-name.namespace.xml"},2:{name:"entity.other.attribute-name.xml"},3:{name:"punctuation.separator.namespace.xml"},4:{name:"entity.other.attribute-name.localname.xml"}},match:"(?:^|\\s+)(?:([-\\w.]+)((:)))?([-\\w.:]+)\\s*="},{include:"#doublequotedString"},{include:"#singlequotedString"}]}},scopeName:"text.xml",embeddedLangs:["java"]});var i=[...a.default,r]},92200:(e,n,t)=>{t.r(n),t.d(n,{default:()=>l});var a=t(14055),r=t(31127),i=t(21516),u=t(98013),s=t(14187),c=t(63069),o=t(31767),d=t(28478);let m=Object.freeze({displayName:"Ruby",name:"ruby",patterns:[{captures:{1:{name:"keyword.control.class.ruby"},2:{name:"entity.name.type.class.ruby"},3:{name:"keyword.operator.other.ruby"},4:{name:"entity.other.inherited-class.ruby"},5:{name:"keyword.operator.other.ruby"},6:{name:"variable.other.object.ruby"}},match:"^\\s*(class)\\s+(?:([.a-zA-Z0-9_:]+)(?:\\s*(<)\\s*([.a-zA-Z0-9_:]+))?|(<<)\\s*([.a-zA-Z0-9_:]+))",name:"meta.class.ruby"},{captures:{1:{name:"keyword.control.module.ruby"},2:{name:"entity.name.type.module.ruby"},3:{name:"entity.other.inherited-class.module.first.ruby"},4:{name:"punctuation.separator.inheritance.ruby"},5:{name:"entity.other.inherited-class.module.second.ruby"},6:{name:"punctuation.separator.inheritance.ruby"},7:{name:"entity.other.inherited-class.module.third.ruby"},8:{name:"punctuation.separator.inheritance.ruby"}},match:"^\\s*(module)\\s+(([A-Z]\\w*(::))?([A-Z]\\w*(::))?([A-Z]\\w*(::))*[A-Z]\\w*)",name:"meta.module.ruby"},{comment:"else if is a common mistake carried over from other languages. it works if you put in a second end, but it’s never what you want.",match:"(?<!\\.)\\belse(\\s)+if\\b",name:"invalid.deprecated.ruby"},{captures:{1:{name:"punctuation.definition.constant.ruby"}},comment:"symbols as hash key (1.9 syntax)",match:"(?>[a-zA-Z_]\\w*(?>[?!])?)(:)(?!:)",name:"constant.other.symbol.hashkey.ruby"},{captures:{1:{name:"punctuation.definition.constant.ruby"}},comment:"symbols as hash key (1.8 syntax)",match:"(?<!:)(:)(?>[a-zA-Z_]\\w*(?>[?!])?)(?=\\s*=>)",name:"constant.other.symbol.hashkey.ruby"},{comment:"everything being a reserved word, not a value and needing a 'end' is a..",match:"(?<!\\.)\\b(BEGIN|begin|case|class|else|elsif|END|end|ensure|for|if|in|module|rescue|then|unless|until|when|while)\\b(?![?!])",name:"keyword.control.ruby"},{comment:"contextual smart pair support for block parameters",match:"(?<!\\.)\\bdo\\b",name:"keyword.control.start-block.ruby"},{comment:"contextual smart pair support",match:"(?<=\\{)(\\s+)",name:"meta.syntax.ruby.start-block"},{match:"(?<!\\.)\\b(alias|alias_method|block_given[?]|break|defined[?]|iterator[?]|next|redo|retry|return|super|undef|yield)(\\b|(?<=[?]))(?![?!])",name:"keyword.control.pseudo-method.ruby"},{match:"\\b(nil|true|false)\\b(?![?!])",name:"constant.language.ruby"},{match:"\\b(__(dir|FILE|LINE)__)\\b(?![?!])",name:"variable.language.ruby"},{begin:"^__END__\\n",captures:{0:{name:"string.unquoted.program-block.ruby"}},comment:"__END__ marker",contentName:"text.plain",end:"(?=not)impossible",patterns:[{begin:"(?=<?xml|<(?i:html\\b)|!DOCTYPE (?i:html\\b))",end:"(?=not)impossible",name:"text.html.embedded.ruby",patterns:[{include:"text.html.basic"}]}]},{match:"\\b(self)\\b(?![?!])",name:"variable.language.self.ruby"},{comment:" everything being a method but having a special function is a..",match:"\\b(initialize|new|loop|include|extend|prepend|fail|raise|attr_reader|attr_writer|attr_accessor|attr|catch|throw|private|private_class_method|module_function|public|public_class_method|protected|refine|using)\\b(?![?!])",name:"keyword.other.special-method.ruby"},{begin:"\\b(?<!\\.|::)(require|require_relative)\\b",captures:{1:{name:"keyword.other.special-method.ruby"}},end:"$|(?=#|\\})",name:"meta.require.ruby",patterns:[{include:"$self"}]},{captures:{1:{name:"punctuation.definition.variable.ruby"}},match:"(@)[a-zA-Z_]\\w*",name:"variable.other.readwrite.instance.ruby"},{captures:{1:{name:"punctuation.definition.variable.ruby"}},match:"(@@)[a-zA-Z_]\\w*",name:"variable.other.readwrite.class.ruby"},{captures:{1:{name:"punctuation.definition.variable.ruby"}},match:"(\\$)[a-zA-Z_]\\w*",name:"variable.other.readwrite.global.ruby"},{captures:{1:{name:"punctuation.definition.variable.ruby"}},match:"(\\$)(!|@|&|`|'|\\+|\\d+|~|=|/|\\\\|,|;|\\.|<|>|_|\\*|\\$|\\?|:|\"|-[0adFiIlpvw])",name:"variable.other.readwrite.global.pre-defined.ruby"},{begin:"\\b(ENV)\\[",beginCaptures:{1:{name:"variable.other.constant.ruby"}},end:"\\]",name:"meta.environment-variable.ruby",patterns:[{include:"$self"}]},{match:"\\b[A-Z]\\w*(?=((\\.|::)[A-Za-z]|\\[))",name:"support.class.ruby"},{match:"\\b(abort|at_exit|autoload[?]?|binding|callcc|caller|caller_locations|chomp|chop|eval|exec|exit|exit!|fork|format|gets|global_variables|gsub|lambda|load|local_variables|open|p|print|printf|proc|putc|puts|rand|readline|readlines|select|set_trace_func|sleep|spawn|sprintf|srand|sub|syscall|system|test|trace_var|trap|untrace_var|warn)(\\b|(?<=[?!]))(?![?!])",name:"support.function.kernel.ruby"},{match:"\\b[A-Z]\\w*\\b",name:"variable.other.constant.ruby"},{begin:"(?=def\\b)(?<=^|\\s)(def)\\s+((?>[a-zA-Z_]\\w*(?>\\.|::))?(?>[a-zA-Z_]\\w*(?>[?!]|=(?!>))?|===?|!=|!~|>[>=]?|<=>|<[<=]?|[%&`/\\|^]|\\*\\*?|=?~|[-+]@?|\\[\\]=?))\\s*(\\()",beginCaptures:{1:{name:"keyword.control.def.ruby"},2:{name:"entity.name.function.ruby"},3:{name:"punctuation.definition.parameters.ruby"}},comment:"the method pattern comes from the symbol pattern, see there for a explaination",end:"\\)",endCaptures:{0:{name:"punctuation.definition.parameters.ruby"}},name:"meta.function.method.with-arguments.ruby",patterns:[{begin:"(?=[&*_a-zA-Z])",end:"(?=[,)])",patterns:[{captures:{1:{name:"storage.type.variable.ruby"},2:{name:"constant.other.symbol.hashkey.parameter.function.ruby"},3:{name:"punctuation.definition.constant.ruby"},4:{name:"variable.parameter.function.ruby"}},match:"\\G([&*]?)(?:([_a-zA-Z]\\w*(:))|([_a-zA-Z]\\w*))"},{include:"#parens"},{include:"#braces"},{include:"$self"}]}],repository:{braces:{begin:"\\{",beginCaptures:{0:{name:"punctuation.section.function.begin.ruby"}},end:"\\}",endCaptures:{0:{name:"punctuation.section.function.end.ruby"}},patterns:[{include:"#parens"},{include:"#braces"},{include:"$self"}]},parens:{begin:"\\(",beginCaptures:{0:{name:"punctuation.section.function.begin.ruby"}},end:"\\)",endCaptures:{0:{name:"punctuation.section.function.end.ruby"}},patterns:[{include:"#parens"},{include:"#braces"},{include:"$self"}]}}},{begin:"(?=def\\b)(?<=^|\\s)(def)\\s+((?>[a-zA-Z_]\\w*(?>\\.|::))?(?>[a-zA-Z_]\\w*(?>[?!]|=(?!>))?|===?|!=|!~|>[>=]?|<=>|<[<=]?|[%&`/\\|^]|\\*\\*?|=?~|[-+]@?|\\[\\]=?))[ \\t](?=[ \\t]*[^\\s#;])",beginCaptures:{1:{name:"keyword.control.def.ruby"},2:{name:"entity.name.function.ruby"}},comment:"same as the previous rule, but without parentheses around the arguments",end:"$",name:"meta.function.method.with-arguments.ruby",patterns:[{begin:"(?![\\s,])",end:"(?=,|$)",patterns:[{captures:{1:{name:"storage.type.variable.ruby"},2:{name:"constant.other.symbol.hashkey.parameter.function.ruby"},3:{name:"punctuation.definition.constant.ruby"},4:{name:"variable.parameter.function.ruby"}},match:"\\G([&*]?)(?:([_a-zA-Z]\\w*(:))|([_a-zA-Z]\\w*))",name:"variable.parameter.function.ruby"},{include:"$self"}]}]},{captures:{1:{name:"keyword.control.def.ruby"},3:{name:"entity.name.function.ruby"}},comment:" the optional name is just to catch the def also without a method-name",match:"(?=def\\b)(?<=^|\\s)(def)\\b(\\s+((?>[a-zA-Z_]\\w*(?>\\.|::))?(?>[a-zA-Z_]\\w*(?>[?!]|=(?!>))?|===?|!=|!~|>[>=]?|<=>|<[<=]?|[%&`/\\|^]|\\*\\*?|=?~|[-+]@?|\\[\\]=?)))?",name:"meta.function.method.without-arguments.ruby"},{match:"\\b\\d(?>_?\\d)*(?=\\.\\d|[eE])(\\.\\d(?>_?\\d)*)?([eE][-+]?\\d(?>_?\\d)*)?r?i?\\b",name:"constant.numeric.float.ruby"},{match:"\\b(0|(0[dD]\\d|[1-9])(?>_?\\d)*)r?i?\\b",name:"constant.numeric.integer.ruby"},{match:"\\b0[xX]\\h(?>_?\\h)*r?i?\\b",name:"constant.numeric.hex.ruby"},{match:"\\b0[bB][01](?>_?[01])*r?i?\\b",name:"constant.numeric.binary.ruby"},{match:"\\b0([oO]?[0-7](?>_?[0-7])*)?r?i?\\b",name:"constant.numeric.octal.ruby"},{begin:":'",captures:{0:{name:"punctuation.definition.constant.ruby"}},end:"'",name:"constant.other.symbol.single-quoted.ruby",patterns:[{match:"\\\\['\\\\]",name:"constant.character.escape.ruby"}]},{begin:':"',captures:{0:{name:"punctuation.definition.constant.ruby"}},end:'"',name:"constant.other.symbol.double-quoted.ruby",patterns:[{include:"#interpolated_ruby"},{include:"#escaped_char"}]},{comment:"Needs higher precedence than regular expressions.",match:"(?<!\\()/=",name:"keyword.operator.assignment.augmented.ruby"},{begin:"'",beginCaptures:{0:{name:"punctuation.definition.string.begin.ruby"}},comment:"single quoted string (does not allow interpolation)",end:"'",endCaptures:{0:{name:"punctuation.definition.string.end.ruby"}},name:"string.quoted.single.ruby",patterns:[{match:"\\\\'|\\\\\\\\",name:"constant.character.escape.ruby"}]},{begin:'"',beginCaptures:{0:{name:"punctuation.definition.string.begin.ruby"}},comment:"double quoted string (allows for interpolation)",end:'"',endCaptures:{0:{name:"punctuation.definition.string.end.ruby"}},name:"string.quoted.double.ruby",patterns:[{include:"#interpolated_ruby"},{include:"#escaped_char"}]},{begin:"`",beginCaptures:{0:{name:"punctuation.definition.string.begin.ruby"}},comment:"execute string (allows for interpolation)",end:"`",endCaptures:{0:{name:"punctuation.definition.string.end.ruby"}},name:"string.interpolated.ruby",patterns:[{include:"#interpolated_ruby"},{include:"#escaped_char"}]},{include:"#percent_literals"},{begin:"(?:^|(?<=[=>~(?:\\[,|&;]|[\\s;]if\\s|[\\s;]elsif\\s|[\\s;]while\\s|[\\s;]unless\\s|[\\s;]when\\s|[\\s;]assert_match\\s|[\\s;]or\\s|[\\s;]and\\s|[\\s;]not\\s|[\\s.]index\\s|[\\s.]scan\\s|[\\s.]sub\\s|[\\s.]sub!\\s|[\\s.]gsub\\s|[\\s.]gsub!\\s|[\\s.]match\\s)|(?<=^when\\s|^if\\s|^elsif\\s|^while\\s|^unless\\s))\\s*((/))(?![*+{}?])",captures:{1:{name:"string.regexp.classic.ruby"},2:{name:"punctuation.definition.string.ruby"}},comment:"regular expressions (normal)\n			we only start a regexp if the character before it (excluding whitespace)\n			is what we think is before a regexp\n			",contentName:"string.regexp.classic.ruby",end:"((/[eimnosux]*))",patterns:[{include:"#regex_sub"}]},{captures:{1:{name:"punctuation.definition.constant.ruby"}},comment:"symbols",match:"(?<!:)(:)(?>[a-zA-Z_]\\w*(?>[?!]|=(?![>=]))?|===?|>[>=]?|<=>|<[<=]?|[%&`/\\|]|\\*\\*?|=?~|[-+]@?|\\[\\]=?|(@@?|\\$)[a-zA-Z_]\\w*)",name:"constant.other.symbol.ruby"},{begin:"^=begin",captures:{0:{name:"punctuation.definition.comment.ruby"}},comment:"multiline comments",end:"^=end",name:"comment.block.documentation.ruby"},{begin:"(^[ \\t]+)?(?=#)",beginCaptures:{1:{name:"punctuation.whitespace.comment.leading.ruby"}},end:"(?!\\G)",patterns:[{begin:"#",beginCaptures:{0:{name:"punctuation.definition.comment.ruby"}},end:"\\n",name:"comment.line.number-sign.ruby"}]},{comment:'\n			matches questionmark-letters.\n\n			examples (1st alternation = hex):\n			?\\x1     ?\\x61\n\n			examples (2nd alternation = octal):\n			?\\0      ?\\07     ?\\017\n\n			examples (3rd alternation = escaped):\n			?\\n      ?\\b\n\n			examples (4th alternation = meta-ctrl):\n			?\\C-a    ?\\M-a    ?\\C-\\M-\\C-\\M-a\n\n			examples (4th alternation = normal):\n			?a       ?A       ?0 \n			?*       ?"       ?( \n			?.       ?#\n			\n			\n			the negative lookbehind prevents against matching\n			p(42.tainted?)\n			',match:"(?<!\\w)\\?(\\\\(x\\h{1,2}(?!\\h)\\b|0[0-7]{0,2}(?![0-7])\\b|[^x0MC])|(\\\\[MC]-)+\\w|[^\\s\\\\])",name:"constant.numeric.ruby"},{begin:'(?=(?><<[-~]("?)((?:[_\\w]+_|)HTML)\\b\\1))',comment:"Heredoc with embedded html",end:"(?!\\G)",name:"meta.embedded.block.html",patterns:[{begin:'(?><<[-~]("?)((?:[_\\w]+_|)HTML)\\b\\1)',beginCaptures:{0:{name:"punctuation.definition.string.begin.ruby"}},contentName:"text.html",end:"\\s*\\2$\\n?",endCaptures:{0:{name:"punctuation.definition.string.end.ruby"}},name:"string.unquoted.heredoc.ruby",patterns:[{include:"#heredoc"},{include:"#interpolated_ruby"},{include:"text.html.basic"},{include:"#escaped_char"}]}]},{begin:'(?=(?><<[-~]("?)((?:[_\\w]+_|)XML)\\b\\1))',comment:"Heredoc with embedded xml",end:"(?!\\G)",name:"meta.embedded.block.xml",patterns:[{begin:'(?><<[-~]("?)((?:[_\\w]+_|)XML)\\b\\1)',beginCaptures:{0:{name:"punctuation.definition.string.begin.ruby"}},contentName:"text.xml",end:"\\s*\\2$\\n?",endCaptures:{0:{name:"punctuation.definition.string.end.ruby"}},name:"string.unquoted.heredoc.ruby",patterns:[{include:"#heredoc"},{include:"#interpolated_ruby"},{include:"text.xml"},{include:"#escaped_char"}]}]},{begin:'(?=(?><<[-~]("?)((?:[_\\w]+_|)SQL)\\b\\1))',comment:"Heredoc with embedded sql",end:"(?!\\G)",name:"meta.embedded.block.sql",patterns:[{begin:'(?><<[-~]("?)((?:[_\\w]+_|)SQL)\\b\\1)',beginCaptures:{0:{name:"punctuation.definition.string.begin.ruby"}},contentName:"source.sql",end:"\\s*\\2$\\n?",endCaptures:{0:{name:"punctuation.definition.string.end.ruby"}},name:"string.unquoted.heredoc.ruby",patterns:[{include:"#heredoc"},{include:"#interpolated_ruby"},{include:"source.sql"},{include:"#escaped_char"}]}]},{begin:'(?=(?><<[-~]("?)((?:[_\\w]+_|)CSS)\\b\\1))',comment:"Heredoc with embedded css",end:"(?!\\G)",name:"meta.embedded.block.css",patterns:[{begin:'(?><<[-~]("?)((?:[_\\w]+_|)CSS)\\b\\1)',beginCaptures:{0:{name:"punctuation.definition.string.begin.ruby"}},contentName:"source.css",end:"\\s*\\2$\\n?",endCaptures:{0:{name:"punctuation.definition.string.end.ruby"}},name:"string.unquoted.heredoc.ruby",patterns:[{include:"#heredoc"},{include:"#interpolated_ruby"},{include:"source.css"},{include:"#escaped_char"}]}]},{begin:'(?=(?><<[-~]("?)((?:[_\\w]+_|)CPP)\\b\\1))',comment:"Heredoc with embedded c++",end:"(?!\\G)",name:"meta.embedded.block.c++",patterns:[{begin:'(?><<[-~]("?)((?:[_\\w]+_|)CPP)\\b\\1)',beginCaptures:{0:{name:"punctuation.definition.string.begin.ruby"}},contentName:"source.c++",end:"\\s*\\2$\\n?",endCaptures:{0:{name:"punctuation.definition.string.end.ruby"}},name:"string.unquoted.heredoc.ruby",patterns:[{include:"#heredoc"},{include:"#interpolated_ruby"},{include:"source.c++"},{include:"#escaped_char"}]}]},{begin:'(?=(?><<[-~]("?)((?:[_\\w]+_|)C)\\b\\1))',comment:"Heredoc with embedded c",end:"(?!\\G)",name:"meta.embedded.block.c",patterns:[{begin:'(?><<[-~]("?)((?:[_\\w]+_|)C)\\b\\1)',beginCaptures:{0:{name:"punctuation.definition.string.begin.ruby"}},contentName:"source.c",end:"\\s*\\2$\\n?",endCaptures:{0:{name:"punctuation.definition.string.end.ruby"}},name:"string.unquoted.heredoc.ruby",patterns:[{include:"#heredoc"},{include:"#interpolated_ruby"},{include:"source.c"},{include:"#escaped_char"}]}]},{begin:'(?=(?><<[-~]("?)((?:[_\\w]+_|)(?:JS|JAVASCRIPT))\\b\\1))',comment:"Heredoc with embedded javascript",end:"(?!\\G)",name:"meta.embedded.block.js",patterns:[{begin:'(?><<[-~]("?)((?:[_\\w]+_|)(?:JS|JAVASCRIPT))\\b\\1)',beginCaptures:{0:{name:"punctuation.definition.string.begin.ruby"}},contentName:"source.js",end:"\\s*\\2$\\n?",endCaptures:{0:{name:"punctuation.definition.string.end.ruby"}},name:"string.unquoted.heredoc.ruby",patterns:[{include:"#heredoc"},{include:"#interpolated_ruby"},{include:"source.js"},{include:"#escaped_char"}]}]},{begin:'(?=(?><<[-~]("?)((?:[_\\w]+_|)JQUERY)\\b\\1))',comment:"Heredoc with embedded jQuery javascript",end:"(?!\\G)",name:"meta.embedded.block.js.jquery",patterns:[{begin:'(?><<[-~]("?)((?:[_\\w]+_|)JQUERY)\\b\\1)',beginCaptures:{0:{name:"punctuation.definition.string.begin.ruby"}},contentName:"source.js.jquery",end:"\\s*\\2$\\n?",endCaptures:{0:{name:"punctuation.definition.string.end.ruby"}},name:"string.unquoted.heredoc.ruby",patterns:[{include:"#heredoc"},{include:"#interpolated_ruby"},{include:"source.js.jquery"},{include:"#escaped_char"}]}]},{begin:'(?=(?><<[-~]("?)((?:[_\\w]+_|)(?:SH|SHELL))\\b\\1))',comment:"Heredoc with embedded shell",end:"(?!\\G)",name:"meta.embedded.block.shell",patterns:[{begin:'(?><<[-~]("?)((?:[_\\w]+_|)(?:SH|SHELL))\\b\\1)',beginCaptures:{0:{name:"punctuation.definition.string.begin.ruby"}},contentName:"source.shell",end:"\\s*\\2$\\n?",endCaptures:{0:{name:"punctuation.definition.string.end.ruby"}},name:"string.unquoted.heredoc.ruby",patterns:[{include:"#heredoc"},{include:"#interpolated_ruby"},{include:"source.shell"},{include:"#escaped_char"}]}]},{begin:'(?=(?><<[-~]("?)((?:[_\\w]+_|)LUA)\\b\\1))',comment:"Heredoc with embedded lua",end:"(?!\\G)",name:"meta.embedded.block.lua",patterns:[{begin:'(?><<[-~]("?)((?:[_\\w]+_|)LUA)\\b\\1)',beginCaptures:{0:{name:"punctuation.definition.string.begin.ruby"}},contentName:"source.lua",end:"\\s*\\2$\\n?",endCaptures:{0:{name:"punctuation.definition.string.end.ruby"}},name:"string.unquoted.heredoc.ruby",patterns:[{include:"#heredoc"},{include:"#interpolated_ruby"},{include:"source.lua"},{include:"#escaped_char"}]}]},{begin:'(?=(?><<[-~]("?)((?:[_\\w]+_|)RUBY)\\b\\1))',comment:"Heredoc with embedded ruby",end:"(?!\\G)",name:"meta.embedded.block.ruby",patterns:[{begin:'(?><<[-~]("?)((?:[_\\w]+_|)RUBY)\\b\\1)',beginCaptures:{0:{name:"punctuation.definition.string.begin.ruby"}},contentName:"source.ruby",end:"\\s*\\2$\\n?",endCaptures:{0:{name:"punctuation.definition.string.end.ruby"}},name:"string.unquoted.heredoc.ruby",patterns:[{include:"#heredoc"},{include:"#interpolated_ruby"},{include:"source.ruby"},{include:"#escaped_char"}]}]},{begin:"(?>=\\s*<<(\\w+))",beginCaptures:{0:{name:"punctuation.definition.string.begin.ruby"}},end:"^\\1$",endCaptures:{0:{name:"punctuation.definition.string.end.ruby"}},name:"string.unquoted.heredoc.ruby",patterns:[{include:"#heredoc"},{include:"#interpolated_ruby"},{include:"#escaped_char"}]},{begin:"(?><<[-~](\\w+))",beginCaptures:{0:{name:"punctuation.definition.string.begin.ruby"}},comment:"heredoc with indented terminator",end:"\\s*\\1$",endCaptures:{0:{name:"punctuation.definition.string.end.ruby"}},name:"string.unquoted.heredoc.ruby",patterns:[{include:"#heredoc"},{include:"#interpolated_ruby"},{include:"#escaped_char"}]},{begin:"(?<=\\{|do|\\{\\s|do\\s)(\\|)",captures:{1:{name:"punctuation.separator.arguments.ruby"}},end:"(?<!\\|)(\\|)(?!\\|)",patterns:[{include:"$self"},{match:"[_a-zA-Z][_a-zA-Z0-9]*",name:"variable.other.block.ruby"},{match:",",name:"punctuation.separator.variable.ruby"}]},{match:"=>",name:"punctuation.separator.key-value"},{match:"->",name:"support.function.kernel.lambda.ruby"},{match:"<<=|%=|&{1,2}=|\\*=|\\*\\*=|\\+=|-=|\\^=|\\|{1,2}=|<<",name:"keyword.operator.assignment.augmented.ruby"},{match:"<=>|<(?!<|=)|>(?!<|=|>)|<=|>=|===|==|=~|!=|!~|(?<=[ \\t])\\?",name:"keyword.operator.comparison.ruby"},{match:"(?<!\\.)\\b(and|not|or)\\b(?![?!])",name:"keyword.operator.logical.ruby"},{comment:"Make sure this goes after assignment and comparison",match:"(?<=^|[ \\t])!|&&|\\|\\||\\^",name:"keyword.operator.logical.ruby"},{captures:{1:{name:"punctuation.separator.method.ruby"}},comment:"Safe navigation operator - Added in 2.3",match:"(&\\.)\\s*(?![A-Z])"},{match:"(%|&|\\*\\*|\\*|\\+|-|/)",name:"keyword.operator.arithmetic.ruby"},{match:"=",name:"keyword.operator.assignment.ruby"},{match:"\\||~|>>",name:"keyword.operator.other.ruby"},{match:";",name:"punctuation.separator.statement.ruby"},{match:",",name:"punctuation.separator.object.ruby"},{captures:{1:{name:"punctuation.separator.namespace.ruby"}},comment:"Mark as namespace separator if double colons followed by capital letter",match:"(::)\\s*(?=[A-Z])"},{captures:{1:{name:"punctuation.separator.method.ruby"}},comment:"Mark as method separator if double colons not followed by capital letter",match:"(\\.|::)\\s*(?![A-Z])"},{comment:"Must come after method and constant separators to prefer double colons",match:":",name:"punctuation.separator.other.ruby"},{match:"\\{",name:"punctuation.section.scope.begin.ruby"},{match:"\\}",name:"punctuation.section.scope.end.ruby"},{match:"\\[",name:"punctuation.section.array.begin.ruby"},{match:"\\]",name:"punctuation.section.array.end.ruby"},{match:"\\(|\\)",name:"punctuation.section.function.ruby"}],repository:{escaped_char:{match:"\\\\(?:[0-7]{1,3}|x[\\da-fA-F]{1,2}|.)",name:"constant.character.escape.ruby"},heredoc:{begin:"^<<[-~]?\\w+",end:"$",patterns:[{include:"$self"}]},interpolated_ruby:{patterns:[{begin:"#\\{",beginCaptures:{0:{name:"punctuation.section.embedded.begin.ruby"}},contentName:"source.ruby",end:"(\\})",endCaptures:{0:{name:"punctuation.section.embedded.end.ruby"},1:{name:"source.ruby"}},name:"meta.embedded.line.ruby",patterns:[{include:"#nest_curly_and_self"},{include:"$self"}],repository:{nest_curly_and_self:{patterns:[{begin:"\\{",captures:{0:{name:"punctuation.section.scope.ruby"}},end:"\\}",patterns:[{include:"#nest_curly_and_self"}]},{include:"$self"}]}}},{captures:{1:{name:"punctuation.definition.variable.ruby"}},match:"(#@)[a-zA-Z_]\\w*",name:"variable.other.readwrite.instance.ruby"},{captures:{1:{name:"punctuation.definition.variable.ruby"}},match:"(#@@)[a-zA-Z_]\\w*",name:"variable.other.readwrite.class.ruby"},{captures:{1:{name:"punctuation.definition.variable.ruby"}},match:"(#\\$)[a-zA-Z_]\\w*",name:"variable.other.readwrite.global.ruby"}]},percent_literals:{patterns:[{begin:"%i(?:([(\\[{<])|([^\\w\\s]|_))",beginCaptures:{0:{name:"punctuation.section.array.begin.ruby"}},end:"[)\\]}>]\\2|\\1\\2",endCaptures:{0:{name:"punctuation.section.array.end.ruby"}},name:"meta.array.symbol.ruby",patterns:[{begin:"\\G(?<=\\()(?!\\))",end:"(?=\\))",patterns:[{include:"#parens"},{include:"#symbol"}]},{begin:"\\G(?<=\\[)(?!\\])",end:"(?=\\])",patterns:[{include:"#brackets"},{include:"#symbol"}]},{begin:"\\G(?<=\\{)(?!\\})",end:"(?=\\})",patterns:[{include:"#braces"},{include:"#symbol"}]},{begin:"\\G(?<=<)(?!>)",end:"(?=>)",patterns:[{include:"#angles"},{include:"#symbol"}]},{include:"#symbol"}],repository:{angles:{patterns:[{captures:{0:{name:"constant.character.escape.ruby"}},match:"\\\\<|\\\\>",name:"constant.other.symbol.ruby"},{begin:"<",captures:{0:{name:"constant.other.symbol.ruby"}},end:">",patterns:[{include:"#angles"},{include:"#symbol"}]}]},braces:{patterns:[{captures:{0:{name:"constant.character.escape.ruby"}},match:"\\\\\\{|\\\\\\}",name:"constant.other.symbol.ruby"},{begin:"\\{",captures:{0:{name:"constant.other.symbol.ruby"}},end:"\\}",patterns:[{include:"#braces"},{include:"#symbol"}]}]},brackets:{patterns:[{captures:{0:{name:"constant.character.escape.ruby"}},match:"\\\\\\[|\\\\\\]",name:"constant.other.symbol.ruby"},{begin:"\\[",captures:{0:{name:"constant.other.symbol.ruby"}},end:"\\]",patterns:[{include:"#brackets"},{include:"#symbol"}]}]},parens:{patterns:[{captures:{0:{name:"constant.character.escape.ruby"}},match:"\\\\\\(|\\\\\\)",name:"constant.other.symbol.ruby"},{begin:"\\(",captures:{0:{name:"constant.other.symbol.ruby"}},end:"\\)",patterns:[{include:"#parens"},{include:"#symbol"}]}]},symbol:{patterns:[{captures:{0:{name:"constant.character.escape.ruby"}},match:"\\\\\\\\|\\\\[ ]",name:"constant.other.symbol.ruby"},{match:"\\S\\w*",name:"constant.other.symbol.ruby"}]}}},{begin:"%I(?:([(\\[{<])|([^\\w\\s]|_))",beginCaptures:{0:{name:"punctuation.section.array.begin.ruby"}},end:"[)\\]}>]\\2|\\1\\2",endCaptures:{0:{name:"punctuation.section.array.end.ruby"}},name:"meta.array.symbol.interpolated.ruby",patterns:[{begin:"\\G(?<=\\()(?!\\))",end:"(?=\\))",patterns:[{include:"#parens"},{include:"#symbol"}]},{begin:"\\G(?<=\\[)(?!\\])",end:"(?=\\])",patterns:[{include:"#brackets"},{include:"#symbol"}]},{begin:"\\G(?<=\\{)(?!\\})",end:"(?=\\})",patterns:[{include:"#braces"},{include:"#symbol"}]},{begin:"\\G(?<=<)(?!>)",end:"(?=>)",patterns:[{include:"#angles"},{include:"#symbol"}]},{include:"#symbol"}],repository:{angles:{patterns:[{begin:"<",captures:{0:{name:"constant.other.symbol.ruby"}},end:">",patterns:[{include:"#angles"},{include:"#symbol"}]}]},braces:{patterns:[{begin:"\\{",captures:{0:{name:"constant.other.symbol.ruby"}},end:"\\}",patterns:[{include:"#braces"},{include:"#symbol"}]}]},brackets:{patterns:[{begin:"\\[",captures:{0:{name:"constant.other.symbol.ruby"}},end:"\\]",patterns:[{include:"#brackets"},{include:"#symbol"}]}]},parens:{patterns:[{begin:"\\(",captures:{0:{name:"constant.other.symbol.ruby"}},end:"\\)",patterns:[{include:"#parens"},{include:"#symbol"}]}]},symbol:{patterns:[{begin:"(?=\\\\|#\\{)",end:"(?!\\G)",name:"constant.other.symbol.ruby",patterns:[{include:"#escaped_char"},{include:"#interpolated_ruby"}]},{match:"\\S\\w*",name:"constant.other.symbol.ruby"}]}}},{begin:"%q(?:([(\\[{<])|([^\\w\\s]|_))",beginCaptures:{0:{name:"punctuation.definition.string.begin.ruby"}},end:"[)\\]}>]\\2|\\1\\2",endCaptures:{0:{name:"punctuation.definition.string.end.ruby"}},name:"string.quoted.other.ruby",patterns:[{begin:"\\G(?<=\\()(?!\\))",end:"(?=\\))",patterns:[{include:"#parens"}]},{begin:"\\G(?<=\\[)(?!\\])",end:"(?=\\])",patterns:[{include:"#brackets"}]},{begin:"\\G(?<=\\{)(?!\\})",end:"(?=\\})",patterns:[{include:"#braces"}]},{begin:"\\G(?<=<)(?!>)",end:"(?=>)",patterns:[{include:"#angles"}]}],repository:{angles:{patterns:[{match:"\\\\<|\\\\>|\\\\\\\\",name:"constant.character.escape.ruby"},{begin:"<",end:">",patterns:[{include:"#angles"}]}]},braces:{patterns:[{match:"\\\\\\{|\\\\\\}|\\\\\\\\",name:"constant.character.escape.ruby"},{begin:"\\{",end:"\\}",patterns:[{include:"#braces"}]}]},brackets:{patterns:[{match:"\\\\\\[|\\\\\\]|\\\\\\\\",name:"constant.character.escape.ruby"},{begin:"\\[",end:"\\]",patterns:[{include:"#brackets"}]}]},parens:{patterns:[{match:"\\\\\\(|\\\\\\)|\\\\\\\\",name:"constant.character.escape.ruby"},{begin:"\\(",end:"\\)",patterns:[{include:"#parens"}]}]}}},{begin:"%Q?(?:([(\\[{<])|([^\\w\\s=]|_))",beginCaptures:{0:{name:"punctuation.definition.string.begin.ruby"}},end:"[)\\]}>]\\2|\\1\\2",endCaptures:{0:{name:"punctuation.definition.string.end.ruby"}},name:"string.quoted.other.interpolated.ruby",patterns:[{begin:"\\G(?<=\\()(?!\\))",end:"(?=\\))",patterns:[{include:"#parens"}]},{begin:"\\G(?<=\\[)(?!\\])",end:"(?=\\])",patterns:[{include:"#brackets"}]},{begin:"\\G(?<=\\{)(?!\\})",end:"(?=\\})",patterns:[{include:"#braces"}]},{begin:"\\G(?<=<)(?!>)",end:"(?=>)",patterns:[{include:"#angles"}]},{include:"#escaped_char"},{include:"#interpolated_ruby"}],repository:{angles:{patterns:[{include:"#escaped_char"},{include:"#interpolated_ruby"},{begin:"<",end:">",patterns:[{include:"#angles"}]}]},braces:{patterns:[{include:"#escaped_char"},{include:"#interpolated_ruby"},{begin:"\\{",end:"\\}",patterns:[{include:"#braces"}]}]},brackets:{patterns:[{include:"#escaped_char"},{include:"#interpolated_ruby"},{begin:"\\[",end:"\\]",patterns:[{include:"#brackets"}]}]},parens:{patterns:[{include:"#escaped_char"},{include:"#interpolated_ruby"},{begin:"\\(",end:"\\)",patterns:[{include:"#parens"}]}]}}},{begin:"%r(?:([(\\[{<])|([^\\w\\s]|_))",beginCaptures:{0:{name:"punctuation.definition.string.begin.ruby"}},end:"([)\\]}>]\\2|\\1\\2)[eimnosux]*",endCaptures:{0:{name:"punctuation.definition.string.end.ruby"}},name:"string.regexp.percent.ruby",patterns:[{begin:"\\G(?<=\\()(?!\\))",end:"(?=\\))",patterns:[{include:"#parens"}]},{begin:"\\G(?<=\\[)(?!\\])",end:"(?=\\])",patterns:[{include:"#brackets"}]},{begin:"\\G(?<=\\{)(?!\\})",end:"(?=\\})",patterns:[{include:"#braces"}]},{begin:"\\G(?<=<)(?!>)",end:"(?=>)",patterns:[{include:"#angles"}]},{include:"#regex_sub"}],repository:{angles:{patterns:[{include:"#regex_sub"},{begin:"<",end:">",patterns:[{include:"#angles"}]}]},braces:{patterns:[{include:"#regex_sub"},{begin:"\\{",end:"\\}",patterns:[{include:"#braces"}]}]},brackets:{patterns:[{include:"#regex_sub"},{begin:"\\[",end:"\\]",patterns:[{include:"#brackets"}]}]},parens:{patterns:[{include:"#regex_sub"},{begin:"\\(",end:"\\)",patterns:[{include:"#parens"}]}]}}},{begin:"%s(?:([(\\[{<])|([^\\w\\s]|_))",beginCaptures:{0:{name:"punctuation.definition.constant.begin.ruby"}},end:"[)\\]}>]\\2|\\1\\2",endCaptures:{0:{name:"punctuation.definition.constant.end.ruby"}},name:"constant.other.symbol.percent.ruby",patterns:[{begin:"\\G(?<=\\()(?!\\))",end:"(?=\\))",patterns:[{include:"#parens"}]},{begin:"\\G(?<=\\[)(?!\\])",end:"(?=\\])",patterns:[{include:"#brackets"}]},{begin:"\\G(?<=\\{)(?!\\})",end:"(?=\\})",patterns:[{include:"#braces"}]},{begin:"\\G(?<=<)(?!>)",end:"(?=>)",patterns:[{include:"#angles"}]}],repository:{angles:{patterns:[{match:"\\\\<|\\\\>|\\\\\\\\",name:"constant.character.escape.ruby"},{begin:"<",end:">",patterns:[{include:"#angles"}]}]},braces:{patterns:[{match:"\\\\\\{|\\\\\\}|\\\\\\\\",name:"constant.character.escape.ruby"},{begin:"\\{",end:"\\}",patterns:[{include:"#braces"}]}]},brackets:{patterns:[{match:"\\\\\\[|\\\\\\]|\\\\\\\\",name:"constant.character.escape.ruby"},{begin:"\\[",end:"\\]",patterns:[{include:"#brackets"}]}]},parens:{patterns:[{match:"\\\\\\(|\\\\\\)|\\\\\\\\",name:"constant.character.escape.ruby"},{begin:"\\(",end:"\\)",patterns:[{include:"#parens"}]}]}}},{begin:"%w(?:([(\\[{<])|([^\\w\\s]|_))",beginCaptures:{0:{name:"punctuation.section.array.begin.ruby"}},end:"[)\\]}>]\\2|\\1\\2",endCaptures:{0:{name:"punctuation.section.array.end.ruby"}},name:"meta.array.string.ruby",patterns:[{begin:"\\G(?<=\\()(?!\\))",end:"(?=\\))",patterns:[{include:"#parens"},{include:"#string"}]},{begin:"\\G(?<=\\[)(?!\\])",end:"(?=\\])",patterns:[{include:"#brackets"},{include:"#string"}]},{begin:"\\G(?<=\\{)(?!\\})",end:"(?=\\})",patterns:[{include:"#braces"},{include:"#string"}]},{begin:"\\G(?<=<)(?!>)",end:"(?=>)",patterns:[{include:"#angles"},{include:"#string"}]},{include:"#string"}],repository:{angles:{patterns:[{captures:{0:{name:"constant.character.escape.ruby"}},match:"\\\\<|\\\\>",name:"string.other.ruby"},{begin:"<",captures:{0:{name:"string.other.ruby"}},end:">",patterns:[{include:"#angles"},{include:"#string"}]}]},braces:{patterns:[{captures:{0:{name:"constant.character.escape.ruby"}},match:"\\\\\\{|\\\\\\}",name:"string.other.ruby"},{begin:"\\{",captures:{0:{name:"string.other.ruby"}},end:"\\}",patterns:[{include:"#braces"},{include:"#string"}]}]},brackets:{patterns:[{captures:{0:{name:"constant.character.escape.ruby"}},match:"\\\\\\[|\\\\\\]",name:"string.other.ruby"},{begin:"\\[",captures:{0:{name:"string.other.ruby"}},end:"\\]",patterns:[{include:"#brackets"},{include:"#string"}]}]},parens:{patterns:[{captures:{0:{name:"constant.character.escape.ruby"}},match:"\\\\\\(|\\\\\\)",name:"string.other.ruby"},{begin:"\\(",captures:{0:{name:"string.other.ruby"}},end:"\\)",patterns:[{include:"#parens"},{include:"#string"}]}]},string:{patterns:[{captures:{0:{name:"constant.character.escape.ruby"}},match:"\\\\\\\\|\\\\[ ]",name:"string.other.ruby"},{match:"\\S\\w*",name:"string.other.ruby"}]}}},{begin:"%W(?:([(\\[{<])|([^\\w\\s]|_))",beginCaptures:{0:{name:"punctuation.section.array.begin.ruby"}},end:"[)\\]}>]\\2|\\1\\2",endCaptures:{0:{name:"punctuation.section.array.end.ruby"}},name:"meta.array.string.interpolated.ruby",patterns:[{begin:"\\G(?<=\\()(?!\\))",end:"(?=\\))",patterns:[{include:"#parens"},{include:"#string"}]},{begin:"\\G(?<=\\[)(?!\\])",end:"(?=\\])",patterns:[{include:"#brackets"},{include:"#string"}]},{begin:"\\G(?<=\\{)(?!\\})",end:"(?=\\})",patterns:[{include:"#braces"},{include:"#string"}]},{begin:"\\G(?<=<)(?!>)",end:"(?=>)",patterns:[{include:"#angles"},{include:"#string"}]},{include:"#string"}],repository:{angles:{patterns:[{begin:"<",captures:{0:{name:"string.other.ruby"}},end:">",patterns:[{include:"#angles"},{include:"#string"}]}]},braces:{patterns:[{begin:"\\{",captures:{0:{name:"string.other.ruby"}},end:"\\}",patterns:[{include:"#braces"},{include:"#string"}]}]},brackets:{patterns:[{begin:"\\[",captures:{0:{name:"string.other.ruby"}},end:"\\]",patterns:[{include:"#brackets"},{include:"#string"}]}]},parens:{patterns:[{begin:"\\(",captures:{0:{name:"string.other.ruby"}},end:"\\)",patterns:[{include:"#parens"},{include:"#string"}]}]},string:{patterns:[{begin:"(?=\\\\|#\\{)",end:"(?!\\G)",name:"string.other.ruby",patterns:[{include:"#escaped_char"},{include:"#interpolated_ruby"}]},{match:"\\S\\w*",name:"string.other.ruby"}]}}},{begin:"%x(?:([(\\[{<])|([^\\w\\s]|_))",beginCaptures:{0:{name:"punctuation.definition.string.begin.ruby"}},end:"[)\\]}>]\\2|\\1\\2",endCaptures:{0:{name:"punctuation.definition.string.end.ruby"}},name:"string.interpolated.percent.ruby",patterns:[{begin:"\\G(?<=\\()(?!\\))",end:"(?=\\))",patterns:[{include:"#parens"}]},{begin:"\\G(?<=\\[)(?!\\])",end:"(?=\\])",patterns:[{include:"#brackets"}]},{begin:"\\G(?<=\\{)(?!\\})",end:"(?=\\})",patterns:[{include:"#braces"}]},{begin:"\\G(?<=<)(?!>)",end:"(?=>)",patterns:[{include:"#angles"}]},{include:"#escaped_char"},{include:"#interpolated_ruby"}],repository:{angles:{patterns:[{include:"#escaped_char"},{include:"#interpolated_ruby"},{begin:"<",end:">",patterns:[{include:"#angles"}]}]},braces:{patterns:[{include:"#escaped_char"},{include:"#interpolated_ruby"},{begin:"\\{",end:"\\}",patterns:[{include:"#braces"}]}]},brackets:{patterns:[{include:"#escaped_char"},{include:"#interpolated_ruby"},{begin:"\\[",end:"\\]",patterns:[{include:"#brackets"}]}]},parens:{patterns:[{include:"#escaped_char"},{include:"#interpolated_ruby"},{begin:"\\(",end:"\\)",patterns:[{include:"#parens"}]}]}}}]},regex_sub:{patterns:[{include:"#interpolated_ruby"},{include:"#escaped_char"},{captures:{1:{name:"punctuation.definition.quantifier.begin.ruby"},3:{name:"punctuation.definition.quantifier.end.ruby"}},match:"(\\{)\\d+(,\\d+)?(\\})",name:"keyword.operator.quantifier.ruby"},{begin:"\\[\\^?",beginCaptures:{0:{name:"punctuation.definition.character-class.begin.ruby"}},end:"\\]",endCaptures:{0:{name:"punctuation.definition.character-class.end.ruby"}},name:"constant.other.character-class.set.ruby",patterns:[{include:"#escaped_char"}]},{begin:"\\(\\?#",beginCaptures:{0:{name:"punctuation.definition.comment.begin.ruby"}},end:"\\)",endCaptures:{0:{name:"punctuation.definition.comment.end.ruby"}},name:"comment.line.number-sign.ruby",patterns:[{include:"#escaped_char"}]},{begin:"\\(",captures:{0:{name:"punctuation.definition.group.ruby"}},end:"\\)",name:"meta.group.regexp.ruby",patterns:[{include:"#regex_sub"}]},{begin:"(?<=^|\\s)(#)\\s(?=[[a-zA-Z0-9,. \\t?!-][^\\x{00}-\\x{7F}]]*$)",beginCaptures:{1:{name:"punctuation.definition.comment.ruby"}},comment:"We are restrictive in what we allow to go after the comment character to avoid false positives, since the availability of comments depend on regexp flags.",end:"$\\n?",name:"comment.line.number-sign.ruby"}]}},scopeName:"source.ruby",embeddedLangs:["html","xml","sql","css","c","javascript","shellscript","lua"],aliases:["rb"]});var l=[...a.default,...r.default,...i.default,...u.default,...s.default,...c.default,...o.default,...d.default,m]}};