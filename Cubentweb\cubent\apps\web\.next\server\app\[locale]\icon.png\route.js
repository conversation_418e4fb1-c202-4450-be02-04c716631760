"use strict";(()=>{var e={};e.id=2470,e.ids=[2470],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},26142:(e,r,t)=>{e.exports=t(44870)},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},56966:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>C,routeModule:()=>A,serverHooks:()=>x,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>l});var n={};t.r(n),t.d(n,{GET:()=>u,dynamic:()=>c});var a=t(26142),o=t(94327),s=t(34862),i=t(26239);let p=Buffer.from("iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAQAAADZc7J/AAAAJ0lEQVR42mNgaGD4jxfWMxACo0aMGjFqxPAwYjS4Ro0YNWLUCJgRAGR5/hC+uib5AAAAAElFTkSuQmCC","base64");function u(){return new i.NextResponse(p,{headers:{"Content-Type":"image/png","Cache-Control":"public, immutable, no-transform, max-age=31536000"}})}let c="force-static",A=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/[locale]/icon.png/route",pathname:"/[locale]/icon.png",filename:"icon",bundlePath:"app/[locale]/icon.png/route"},resolvedPagePath:"next-metadata-route-loader?filePath=C%3A%5CUsers%5Canwar%5CDocuments%5C2%20FOLDERS%20FOR%20CUBENT%5CCubentweb%5Ccubent%5Capps%5Cweb%5Capp%5C%5Blocale%5D%5Cicon.png&isDynamicRouteExtension=0!?__next_metadata_route__",nextConfigOutput:"",userland:n}),{workAsyncStorage:d,workUnitAsyncStorage:l,serverHooks:x}=A;function C(){return(0,s.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:l})}},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[5319,6239],()=>t(56966));module.exports=n})();