(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[972],{15880:(e,t,s)=>{"use strict";s.d(t,{OpenSource:()=>f});var n=s(6024),i=s(19246),r=s(52313),a=s(70477),l=s(35685),o=s.n(l);let c={src:"/_next/static/media/unkey-github.8490178e.svg",height:24,width:24,blurWidth:0,blurHeight:0};var d=s(19953),x=s(82757);let u=e=>(0,n.jsx)(x.default,{...e,placeholder:"blur",blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8+e1bKQAJMQNc5W2CQwAAAABJRU5ErkJggg=="});s(50628);let p=e=>{let{className:t}=e;return(0,n.jsxs)("svg",{className:t,viewBox:"0 0 200 100",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,n.jsxs)("defs",{children:[(0,n.jsxs)("linearGradient",{id:"ossGradient",x1:"0%",y1:"0%",x2:"100%",y2:"100%",children:[(0,n.jsx)("stop",{offset:"0%",stopColor:"#3b82f6",stopOpacity:"0.8"}),(0,n.jsx)("stop",{offset:"50%",stopColor:"#8b5cf6",stopOpacity:"0.6"}),(0,n.jsx)("stop",{offset:"100%",stopColor:"#06b6d4",stopOpacity:"0.4"})]}),(0,n.jsxs)("filter",{id:"glow",children:[(0,n.jsx)("feGaussianBlur",{stdDeviation:"3",result:"coloredBlur"}),(0,n.jsxs)("feMerge",{children:[(0,n.jsx)("feMergeNode",{in:"coloredBlur"}),(0,n.jsx)("feMergeNode",{in:"SourceGraphic"})]})]})]}),(0,n.jsx)("rect",{x:"10",y:"20",width:"180",height:"60",rx:"30",fill:"url(#ossGradient)",filter:"url(#glow)",opacity:"0.7"}),(0,n.jsx)("path",{d:"M30 50 L50 50 L50 30 L80 30 L80 50 L120 50 L120 70 L170 70",stroke:"currentColor",strokeWidth:"2",fill:"none",opacity:"0.6"}),(0,n.jsx)("circle",{cx:"30",cy:"50",r:"3",fill:"currentColor",opacity:"0.8"}),(0,n.jsx)("circle",{cx:"80",cy:"30",r:"3",fill:"currentColor",opacity:"0.8"}),(0,n.jsx)("circle",{cx:"120",cy:"50",r:"3",fill:"currentColor",opacity:"0.8"}),(0,n.jsx)("circle",{cx:"170",cy:"70",r:"3",fill:"currentColor",opacity:"0.8"}),(0,n.jsx)("rect",{x:"60",y:"45",width:"10",height:"10",rx:"2",fill:"currentColor",opacity:"0.5"}),(0,n.jsx)("rect",{x:"140",y:"35",width:"8",height:"8",rx:"1",fill:"currentColor",opacity:"0.5"})]})};function h(e){let{className:t}=e;return(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"472",height:"638",viewBox:"0 0 472 638",fill:"none",className:t,children:[(0,n.jsxs)("g",{opacity:"0.4",children:[(0,n.jsx)("g",{style:{mixBlendMode:"lighten"},filter:"url(#filter0_f_825_3716)",children:(0,n.jsx)("ellipse",{cx:"184.597",cy:"353.647",rx:"16.3892",ry:"146.673",transform:"rotate(15.0538 184.597 353.647)",fill:"url(#paint0_linear_825_3716)",fillOpacity:"0.5"})}),(0,n.jsx)("g",{style:{mixBlendMode:"color-dodge"},filter:"url(#filter1_f_825_3716)",children:(0,n.jsx)("ellipse",{cx:"237.5",cy:"343.125",rx:"13.25",ry:"146.625",fill:"url(#paint1_linear_825_3716)",fillOpacity:"0.5"})}),(0,n.jsx)("g",{style:{mixBlendMode:"lighten"},filter:"url(#filter2_f_825_3716)",children:(0,n.jsx)("ellipse",{cx:"289.17",cy:"378.792",rx:"11.1897",ry:"190.642",transform:"rotate(-15 289.17 378.792)",fill:"url(#paint2_linear_825_3716)",fillOpacity:"0.5"})}),(0,n.jsx)("g",{style:{mixBlendMode:"lighten"},filter:"url(#filter3_f_825_3716)",children:(0,n.jsx)("ellipse",{cx:"263.208",cy:"281.902",rx:"11.1897",ry:"90.3336",transform:"rotate(-15 263.208 281.902)",fill:"url(#paint3_linear_825_3716)",fillOpacity:"0.5"})}),(0,n.jsx)("g",{style:{mixBlendMode:"lighten"},filter:"url(#filter4_f_825_3716)",children:(0,n.jsx)("ellipse",{cx:"235.875",cy:"402.5",rx:"11.125",ry:"190.75",fill:"url(#paint4_linear_825_3716)",fillOpacity:"0.5"})}),(0,n.jsx)("g",{style:{mixBlendMode:"lighten"},filter:"url(#filter5_f_825_3716)",children:(0,n.jsx)("ellipse",{cx:"235.75",cy:"290.25",rx:"160.75",ry:"93.75",fill:"url(#paint5_linear_825_3716)",fillOpacity:"0.5"})}),(0,n.jsx)("g",{style:{mixBlendMode:"lighten"},filter:"url(#filter6_f_825_3716)",children:(0,n.jsx)("ellipse",{cx:"235.75",cy:"244.25",rx:"80.25",ry:"47.75",fill:"url(#paint6_linear_825_3716)",fillOpacity:"0.5"})}),(0,n.jsx)("g",{style:{mixBlendMode:"lighten"},filter:"url(#filter7_f_825_3716)",children:(0,n.jsx)("ellipse",{cx:"235.75",cy:"247.875",rx:"67.5",ry:"40.125",fill:"url(#paint7_linear_825_3716)",fillOpacity:"0.5"})})]}),(0,n.jsx)("mask",{id:"path-9-inside-1_825_3716",fill:"white",children:(0,n.jsx)("path",{d:"M204 161H212V593H204V161Z"})}),(0,n.jsx)("path",{d:"M211.5 161V593H212.5V161H211.5ZM204.5 593V161H203.5V593H204.5Z",fill:"url(#paint8_angular_825_3716)",fillOpacity:"0.5",mask:"url(#path-9-inside-1_825_3716)"}),(0,n.jsx)("mask",{id:"path-11-inside-2_825_3716",fill:"white",children:(0,n.jsx)("path",{d:"M180 51H188V483H180V51Z"})}),(0,n.jsx)("path",{d:"M187.5 51V483H188.5V51H187.5ZM180.5 483V51H179.5V483H180.5Z",fill:"url(#paint9_angular_825_3716)",fillOpacity:"0.2",mask:"url(#path-11-inside-2_825_3716)"}),(0,n.jsx)("mask",{id:"path-13-inside-3_825_3716",fill:"white",children:(0,n.jsx)("path",{d:"M228 101H236V533H228V101Z"})}),(0,n.jsx)("path",{d:"M235.5 101V533H236.5V101H235.5ZM228.5 533V101H227.5V533H228.5Z",fill:"url(#paint10_angular_825_3716)",fillOpacity:"0.3",mask:"url(#path-13-inside-3_825_3716)"}),(0,n.jsx)("mask",{id:"path-15-inside-4_825_3716",fill:"white",children:(0,n.jsx)("path",{d:"M252 191H260V623H252V191Z"})}),(0,n.jsx)("path",{d:"M259.5 191V623H260.5V191H259.5ZM252.5 623V191H251.5V623H252.5Z",fill:"url(#paint11_angular_825_3716)",fillOpacity:"0.8",mask:"url(#path-15-inside-4_825_3716)"}),(0,n.jsx)("mask",{id:"path-17-inside-5_825_3716",fill:"white",children:(0,n.jsx)("path",{d:"M276 1H284V433H276V1Z"})}),(0,n.jsx)("path",{d:"M283.5 1V433H284.5V1H283.5ZM276.5 433V1H275.5V433H276.5Z",fill:"url(#paint12_angular_825_3716)",fillOpacity:"0.1",mask:"url(#path-17-inside-5_825_3716)"}),(0,n.jsxs)("defs",{children:[(0,n.jsxs)("filter",{id:"filter0_f_825_3716",x:"98.835",y:"167.442",width:"171.524",height:"372.409",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB",children:[(0,n.jsx)("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),(0,n.jsx)("feBlend",{mode:"normal",in:"SourceGraphic",in2:"BackgroundImageFix",result:"shape"}),(0,n.jsx)("feGaussianBlur",{stdDeviation:"22.25",result:"effect1_foregroundBlur_825_3716"})]}),(0,n.jsxs)("filter",{id:"filter1_f_825_3716",x:"179.75",y:"152",width:"115.5",height:"382.25",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB",children:[(0,n.jsx)("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),(0,n.jsx)("feBlend",{mode:"normal",in:"SourceGraphic",in2:"BackgroundImageFix",result:"shape"}),(0,n.jsx)("feGaussianBlur",{stdDeviation:"22.25",result:"effect1_foregroundBlur_825_3716"})]}),(0,n.jsxs)("filter",{id:"filter2_f_825_3716",x:"194.147",y:"150.123",width:"190.045",height:"457.338",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB",children:[(0,n.jsx)("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),(0,n.jsx)("feBlend",{mode:"normal",in:"SourceGraphic",in2:"BackgroundImageFix",result:"shape"}),(0,n.jsx)("feGaussianBlur",{stdDeviation:"22.25",result:"effect1_foregroundBlur_825_3716"})]}),(0,n.jsxs)("filter",{id:"filter3_f_825_3716",x:"192.944",y:"150.097",width:"140.527",height:"263.609",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB",children:[(0,n.jsx)("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),(0,n.jsx)("feBlend",{mode:"normal",in:"SourceGraphic",in2:"BackgroundImageFix",result:"shape"}),(0,n.jsx)("feGaussianBlur",{stdDeviation:"22.25",result:"effect1_foregroundBlur_825_3716"})]}),(0,n.jsxs)("filter",{id:"filter4_f_825_3716",x:"180.25",y:"167.25",width:"111.25",height:"470.5",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB",children:[(0,n.jsx)("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),(0,n.jsx)("feBlend",{mode:"normal",in:"SourceGraphic",in2:"BackgroundImageFix",result:"shape"}),(0,n.jsx)("feGaussianBlur",{stdDeviation:"22.25",result:"effect1_foregroundBlur_825_3716"})]}),(0,n.jsxs)("filter",{id:"filter5_f_825_3716",x:"7.62939e-06",y:"121.5",width:"471.5",height:"337.5",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB",children:[(0,n.jsx)("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),(0,n.jsx)("feBlend",{mode:"normal",in:"SourceGraphic",in2:"BackgroundImageFix",result:"shape"}),(0,n.jsx)("feGaussianBlur",{stdDeviation:"37.5",result:"effect1_foregroundBlur_825_3716"})]}),(0,n.jsxs)("filter",{id:"filter6_f_825_3716",x:"80.5",y:"121.5",width:"310.5",height:"245.5",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB",children:[(0,n.jsx)("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),(0,n.jsx)("feBlend",{mode:"normal",in:"SourceGraphic",in2:"BackgroundImageFix",result:"shape"}),(0,n.jsx)("feGaussianBlur",{stdDeviation:"37.5",result:"effect1_foregroundBlur_825_3716"})]}),(0,n.jsxs)("filter",{id:"filter7_f_825_3716",x:"93.25",y:"132.75",width:"285",height:"230.25",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB",children:[(0,n.jsx)("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),(0,n.jsx)("feBlend",{mode:"normal",in:"SourceGraphic",in2:"BackgroundImageFix",result:"shape"}),(0,n.jsx)("feGaussianBlur",{stdDeviation:"37.5",result:"effect1_foregroundBlur_825_3716"})]}),(0,n.jsxs)("linearGradient",{id:"paint0_linear_825_3716",x1:"184.597",y1:"206.974",x2:"184.597",y2:"500.319",gradientUnits:"userSpaceOnUse",children:[(0,n.jsx)("stop",{stopColor:"white"}),(0,n.jsx)("stop",{offset:"1",stopColor:"white",stopOpacity:"0"})]}),(0,n.jsxs)("linearGradient",{id:"paint1_linear_825_3716",x1:"237.5",y1:"196.5",x2:"237.5",y2:"489.75",gradientUnits:"userSpaceOnUse",children:[(0,n.jsx)("stop",{stopColor:"white"}),(0,n.jsx)("stop",{offset:"1",stopColor:"white",stopOpacity:"0"})]}),(0,n.jsxs)("linearGradient",{id:"paint2_linear_825_3716",x1:"289.17",y1:"188.151",x2:"289.17",y2:"569.434",gradientUnits:"userSpaceOnUse",children:[(0,n.jsx)("stop",{stopColor:"white"}),(0,n.jsx)("stop",{offset:"1",stopColor:"white",stopOpacity:"0"})]}),(0,n.jsxs)("linearGradient",{id:"paint3_linear_825_3716",x1:"263.208",y1:"191.568",x2:"263.208",y2:"372.236",gradientUnits:"userSpaceOnUse",children:[(0,n.jsx)("stop",{stopColor:"white"}),(0,n.jsx)("stop",{offset:"1",stopColor:"white",stopOpacity:"0"})]}),(0,n.jsxs)("linearGradient",{id:"paint4_linear_825_3716",x1:"235.875",y1:"211.75",x2:"235.875",y2:"593.251",gradientUnits:"userSpaceOnUse",children:[(0,n.jsx)("stop",{stopColor:"white"}),(0,n.jsx)("stop",{offset:"1",stopColor:"white",stopOpacity:"0"})]}),(0,n.jsxs)("linearGradient",{id:"paint5_linear_825_3716",x1:"235.75",y1:"196.5",x2:"235.75",y2:"384.001",gradientUnits:"userSpaceOnUse",children:[(0,n.jsx)("stop",{stopColor:"white"}),(0,n.jsx)("stop",{offset:"1",stopColor:"white",stopOpacity:"0"})]}),(0,n.jsxs)("linearGradient",{id:"paint6_linear_825_3716",x1:"235.75",y1:"196.5",x2:"235.75",y2:"292",gradientUnits:"userSpaceOnUse",children:[(0,n.jsx)("stop",{stopColor:"white"}),(0,n.jsx)("stop",{offset:"1",stopColor:"white",stopOpacity:"0"})]}),(0,n.jsxs)("linearGradient",{id:"paint7_linear_825_3716",x1:"235.75",y1:"207.75",x2:"235.75",y2:"288",gradientUnits:"userSpaceOnUse",children:[(0,n.jsx)("stop",{stopColor:"white"}),(0,n.jsx)("stop",{offset:"1",stopColor:"white",stopOpacity:"0"})]}),(0,n.jsxs)("radialGradient",{id:"paint8_angular_825_3716",cx:"0",cy:"0",r:"1",gradientUnits:"userSpaceOnUse",gradientTransform:"translate(208 481) scale(32 185)",children:[(0,n.jsx)("stop",{stopColor:"white"}),(0,n.jsx)("stop",{offset:"0.0001",stopColor:"white",stopOpacity:"0"}),(0,n.jsx)("stop",{offset:"0.784842",stopColor:"white",stopOpacity:"0"})]}),(0,n.jsxs)("radialGradient",{id:"paint9_angular_825_3716",cx:"0",cy:"0",r:"1",gradientUnits:"userSpaceOnUse",gradientTransform:"translate(184 371) scale(32 185)",children:[(0,n.jsx)("stop",{stopColor:"white"}),(0,n.jsx)("stop",{offset:"0.0001",stopColor:"white",stopOpacity:"0"}),(0,n.jsx)("stop",{offset:"0.784842",stopColor:"white",stopOpacity:"0"})]}),(0,n.jsxs)("radialGradient",{id:"paint10_angular_825_3716",cx:"0",cy:"0",r:"1",gradientUnits:"userSpaceOnUse",gradientTransform:"translate(232 421) scale(32 185)",children:[(0,n.jsx)("stop",{stopColor:"white"}),(0,n.jsx)("stop",{offset:"0.0001",stopColor:"white",stopOpacity:"0"}),(0,n.jsx)("stop",{offset:"0.784842",stopColor:"white",stopOpacity:"0"})]}),(0,n.jsxs)("radialGradient",{id:"paint11_angular_825_3716",cx:"0",cy:"0",r:"1",gradientUnits:"userSpaceOnUse",gradientTransform:"translate(256 511) scale(32 185)",children:[(0,n.jsx)("stop",{stopColor:"white"}),(0,n.jsx)("stop",{offset:"0.0001",stopColor:"white",stopOpacity:"0"}),(0,n.jsx)("stop",{offset:"0.784842",stopColor:"white",stopOpacity:"0"})]}),(0,n.jsxs)("radialGradient",{id:"paint12_angular_825_3716",cx:"0",cy:"0",r:"1",gradientUnits:"userSpaceOnUse",gradientTransform:"translate(280 321) scale(32 185)",children:[(0,n.jsx)("stop",{stopColor:"white"}),(0,n.jsx)("stop",{offset:"0.0001",stopColor:"white",stopOpacity:"0"}),(0,n.jsx)("stop",{offset:"0.784842",stopColor:"white",stopOpacity:"0"})]})]})]})}var m=s(25059);let f=()=>(0,n.jsxs)("div",{className:"pt-[00px] flex items-center flex-col md:flex-row relative",children:[(0,n.jsxs)("div",{className:"absolute top-[-320px] md:top-[-480px] xl:right-[120px] -z-[10]",children:[(0,n.jsx)(h,{className:"scale-[2]"}),(0,n.jsxs)("div",{className:"absolute right-[270px] top-[250px] -z-50",children:[(0,n.jsx)(m.Aj,{className:"ml-2 fade-in-0",delay:2,number:1}),(0,n.jsx)(m.Aj,{className:"ml-10 fade-in-40",number:1,delay:0}),(0,n.jsx)(m.Aj,{className:"ml-16 fade-in-100",delay:4,number:1})]})]}),(0,n.jsxs)("div",{className:"flex flex-col items-center justify-center w-full xl:flex-row xl:justify-between",children:[(0,n.jsx)(r.P.div,{initial:{opacity:0},whileInView:{opacity:1},transition:{duration:1,ease:"easeInOut"},viewport:{once:!0,amount:.5},children:(0,n.jsx)(i._,{align:"left",title:"Open-source",text:"We believe in the power of open source. Read through our codebase, understand our development process, and help us build something amazing all on GitHub.",children:(0,n.jsx)("div",{className:"flex mt-10 space-x-6",children:(0,n.jsx)(o(),{href:"https://go.unkey.com/github",className:"group",children:(0,n.jsx)(d.j,{IconLeft:a.A,label:"Star us on GitHub",shiny:!0})})})})}),(0,n.jsx)("div",{className:"relative",children:(0,n.jsxs)(r.P.div,{initial:{opacity:0},whileInView:{opacity:1},viewport:{once:!0,amount:.5},transition:{duration:1,ease:"easeInOut"},children:[(0,n.jsx)(u,{alt:"Github logo",src:c,className:"mt-24"}),(0,n.jsx)("div",{className:"absolute -z-50 top-[150px] left-[-50px] lg:w-[1000px] lg:h-[400px] lg:top-[400px] lg:left-[150px]",children:(0,n.jsx)(p,{className:"flex"})})]})})]})]})},19179:(e,t,s)=>{"use strict";s.d(t,{UsageBento:()=>d});var n={};s.r(n);var i=s(6024),r=s(50628),a=s(95183),l=s(52313),o=s(43097);let c=e=>{let{items:t,className:s,itemClassName:n,delay:c=.1,duration:d=.5}=e,[x,u]=(0,r.useState)([]);return(0,r.useEffect)(()=>(t.forEach((e,t)=>{setTimeout(()=>{u(t=>[...t,e])},t*c*1e3)}),()=>u([])),[t,c]),(0,i.jsx)("div",{className:(0,o.cn)("space-y-2",s),children:(0,i.jsx)(a.N,{children:x.map((e,t)=>(0,i.jsx)(l.P.div,{initial:{opacity:0,y:20,scale:.95},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:-20,scale:.95},transition:{duration:d,delay:t*c,ease:"easeOut"},className:(0,o.cn)("w-full",n),children:e.content},e.id))})})};function d(){return(0,i.jsxs)("div",{className:"w-full overflow-hidden relative border-[.75px] h-[576px] rounded-[32px] usage-bento-bg-gradient border-[#ffffff]/10",children:[(0,i.jsx)(n.UsageSparkles,{className:"absolute top-0"}),(0,i.jsx)("div",{className:"relative ",children:(0,i.jsxs)(c,{className:"w-full",children:[(0,i.jsx)(f,{icon:(0,i.jsx)(x,{}),text:"Unkey created API key",latency:"3 s"}),(0,i.jsx)(f,{icon:(0,i.jsx)(p,{}),text:"User verified key and logged usage",latency:"1 s"}),(0,i.jsx)(f,{icon:(0,i.jsx)(u,{}),text:"Andreas enabled automatic billing",latency:"8 ms"}),(0,i.jsx)(f,{icon:(0,i.jsx)(h,{}),text:"Unkey sent invoice to customer",latency:"1 s"}),(0,i.jsx)(f,{icon:(0,i.jsx)(m,{}),text:"Andreas collected payments",latency:"2 s"})]})}),(0,i.jsx)(y,{})]})}let x=()=>(0,i.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",children:[(0,i.jsx)("path",{d:"M2.5 4.5H13.5M13.5 4.5L10.5 1.5M13.5 4.5L10.5 7.5",stroke:"white"}),(0,i.jsx)("path",{d:"M13.5 11.5H2.5M2.5 11.5L5.5 8.5M2.5 11.5L5.5 14.5",stroke:"white"})]}),u=()=>(0,i.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:[(0,i.jsx)("path",{d:"M5 8.5H9",stroke:"white",strokeLinejoin:"round"}),(0,i.jsx)("path",{d:"M5 15.5H13",stroke:"white",strokeLinejoin:"round"}),(0,i.jsx)("path",{d:"M12 8.5H19",stroke:"white",strokeLinejoin:"round"}),(0,i.jsx)("path",{d:"M16 15.5H19",stroke:"white",strokeLinejoin:"round"}),(0,i.jsx)("path",{d:"M10.5 10.5C11.6046 10.5 12.5 9.60457 12.5 8.5C12.5 7.39543 11.6046 6.5 10.5 6.5C9.39543 6.5 8.5 7.39543 8.5 8.5C8.5 9.60457 9.39543 10.5 10.5 10.5Z",stroke:"white",strokeLinejoin:"round"}),(0,i.jsx)("path",{d:"M14.5 17.5C15.6046 17.5 16.5 16.6046 16.5 15.5C16.5 14.3954 15.6046 13.5 14.5 13.5C13.3954 13.5 12.5 14.3954 12.5 15.5C12.5 16.6046 13.3954 17.5 14.5 17.5Z",stroke:"white",strokeLinejoin:"round"})]}),p=()=>(0,i.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:[(0,i.jsx)("path",{d:"M9 12L11.5 14L15.5 9.5",stroke:"white"}),(0,i.jsx)("path",{d:"M5.10608 7.24025L11.1227 4.66171C11.3716 4.55502 11.6397 4.5 11.9105 4.5H12.0895C12.3603 4.5 12.6284 4.55502 12.8773 4.66171L18.8939 7.24025C19.2616 7.39783 19.5 7.75937 19.5 8.1594V8.5L19.0821 11.6346C18.7148 14.3888 17.096 16.819 14.6958 18.2191L12.9319 19.2481C12.649 19.4131 12.3275 19.5 12 19.5C11.6725 19.5 11.351 19.4131 11.0681 19.2481L9.30415 18.2191C6.90403 16.819 5.28517 14.3888 4.91794 11.6346L4.5 8.5V8.1594C4.5 7.75937 4.7384 7.39783 5.10608 7.24025Z",stroke:"white"})]}),h=()=>(0,i.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:[(0,i.jsx)("path",{d:"M14.5 9.5H11.5M9.5 15.5H12.5M12.5 15.5H13C13.8284 15.5 14.5 14.8284 14.5 14V14C14.5 13.1716 13.8284 12.5 13 12.5H11C10.1716 12.5 9.5 11.8284 9.5 11V11C9.5 10.1716 10.1716 9.5 11 9.5H11.5M12.5 15.5V17M11.5 9.5V8",stroke:"white"}),(0,i.jsx)("path",{d:"M5.5 18V6C5.5 5.17157 6.17157 4.5 7 4.5H13.8787C14.2765 4.5 14.658 4.65803 14.9393 4.93934L18.0607 8.06066C18.342 8.34196 18.5 8.7235 18.5 9.12132V18C18.5 18.8284 17.8284 19.5 17 19.5H7C6.17157 19.5 5.5 18.8284 5.5 18Z",stroke:"white"})]}),m=()=>(0,i.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",children:[(0,i.jsx)("path",{d:"M0.5 12V4C0.5 3.17157 1.17157 2.5 2 2.5H14C14.8284 2.5 15.5 3.17157 15.5 4V12C15.5 12.8284 14.8284 13.5 14 13.5H2C1.17157 13.5 0.5 12.8284 0.5 12Z",stroke:"white"}),(0,i.jsx)("circle",{cx:"8",cy:"8",r:"2.25",fill:"white",fillOpacity:"0.99"}),(0,i.jsx)("path",{d:"M1 3H3.5C3.5 4.38071 2.38071 5.5 1 5.5V3Z",fill:"white"}),(0,i.jsx)("path",{d:"M1 10.5C2.38071 10.5 3.5 11.6193 3.5 13H1V10.5Z",fill:"white"}),(0,i.jsx)("path",{d:"M12.5 3H15V5.5C13.6193 5.5 12.5 4.38071 12.5 3Z",fill:"white"}),(0,i.jsx)("path",{d:"M12.5 13C12.5 11.6193 13.6193 10.5 15 10.5V13H12.5Z",fill:"white"})]});function f(e){let{className:t,icon:s,text:n,latency:r}=e,[a,...l]=n.split(" ");return l=l.join(" "),(0,i.jsxs)("div",{className:"flex relative -top-7 left-14 md:left-0 rounded-xl border-[.75px] w-[440px] usage-item-gradient border-white/20 mt-4 md:ml-5 lg:ml-0 flex items-center py-[12px] px-[16px] ".concat(t),children:[(0,i.jsx)("div",{className:"rounded-full bg-gray-500 flex items-center justify-center h-8 w-8 border-.75px border-white/20 bg-white/10",children:s}),(0,i.jsxs)("p",{className:"flex items-center ml-6 text-sm text-white",children:[a,(0,i.jsx)("span",{className:"ml-2 text-white/40",children:l}),(0,i.jsx)("svg",{className:"inline-flex ml-2",xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:(0,i.jsx)("path",{d:"M5 13L8 15.5L13.5 8.5M11.5 14L13.5 15.5L19.5 8.5",stroke:"#3CEEAE"})})]}),(0,i.jsxs)("div",{className:"flex items-center h-full ml-auto",children:[(0,i.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",children:[(0,i.jsx)("circle",{cx:"8",cy:"8",r:"5.5",stroke:"white",strokeOpacity:"0.25"}),(0,i.jsx)("path",{d:"M8.5 5V8L10.5 9.5",stroke:"white",strokeOpacity:"0.25"})]}),(0,i.jsx)("p",{className:"ml-2 text-sm text-white/20",children:r})]})]})}function y(){return(0,i.jsxs)("div",{className:"flex flex-col text-white absolute left-[20px] sm:left-[40px] xl:left-[40px] bottom-[40px] max-w-[3300px]",children:[(0,i.jsxs)("div",{className:"flex items-center w-full",children:[(0,i.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:(0,i.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M15.8536 1.85359L14.7047 3.00245C19.3045 3.11116 23 6.87404 23 11.5C23 16.1945 19.1944 20 14.5 20H14V19H14.5C18.6421 19 22 15.6422 22 11.5C22 7.42813 18.755 4.11412 14.71 4.00292L15.8536 5.14648L15.1464 5.85359L13.1464 3.85359L12.7929 3.50004L13.1464 3.14648L15.1464 1.14648L15.8536 1.85359ZM9.5 4.00004C5.35786 4.00004 2 7.3579 2 11.5C2 15.5719 5.24497 18.886 9.29001 18.9972L8.14645 17.8536L8.85355 17.1465L10.8536 19.1465L11.2071 19.5L10.8536 19.8536L8.85355 21.8536L8.14645 21.1465L9.29531 19.9976C4.69545 19.8889 1 16.126 1 11.5C1 6.80562 4.80558 3.00004 9.5 3.00004H10V4.00004H9.5ZM12 8.00004V7.00004H11V8.00004C9.89543 8.00004 9 8.89547 9 10C9 11.1046 9.89543 12 11 12H13C13.5523 12 14 12.4478 14 13C14 13.5523 13.5523 14 13 14H12.5H9.5V15H12V16H13V15C14.1046 15 15 14.1046 15 13C15 11.8955 14.1046 11 13 11H11C10.4477 11 10 10.5523 10 10C10 9.44775 10.4477 9.00004 11 9.00004H11.5H14.5V8.00004H12Z",fill:"white",fillOpacity:"0.4"})}),(0,i.jsx)("h3",{className:"relative z-50 ml-4 text-lg font-medium text-white bg-transparent",children:"Monetize your API"})]}),(0,i.jsx)("p",{className:"mt-4 text-white/60 leading-6 max-w-[350px]",children:"Unkey tracks all user actions in your API, making it straightforward to bill users based on their usage."})]})}},19246:(e,t,s)=>{"use strict";s.d(t,{_:()=>r});var n=s(6024),i=s(43097);function r(e){let{label:t,title:s,text:r,align:a="left",children:l,className:o}=e;return(0,n.jsxs)("div",{className:(0,i.cn)("flex flex-col items-center",{"xl:items-start":"left"===a},o),children:[(0,n.jsx)("span",{className:(0,i.cn)("font-mono text-sm md:text-md text-white/50 text-center",{"xl:text-left":"left"===a}),children:t}),(0,n.jsx)("h2",{className:(0,i.cn)("text-[28px] sm:pb-3 sm:text-[52px] sm:leading-[64px] text-white text-pretty max-w-sm md:max-w-md lg:max-w-2xl xl:max-w-4xl via-30/%  pt-4 font-medium bg-gradient-to-br text-transparent bg-gradient-stop bg-clip-text from-white via-white to-white/30 text-center leading-none",{"xl:text-left":"left"===a}),children:s}),r&&(0,n.jsx)("p",{className:(0,i.cn)("text-sm md:text-base text-white/70 leading-7 py-6 text-center max-w-sm md:max-w-md lg:max-w-xl xl:max-w-4xl text-balance",{"xl:text-left xl:max-w-xl":"left"===a}),children:r}),l]})}},19611:(e,t,s)=>{"use strict";s.d(t,{HashedKeys:()=>m});var n=s(6024),i=s(50628),r=s(52313),a=s(95183),l=s(43097),o=s(32236),c=s(27038),d=s(64354),x=s(3671),u=s(17500),p=s(36220);let h=e=>{let{keyData:t,className:s}=e,[a,p]=(0,i.useState)(!1),[h,m]=(0,i.useState)(!1),f=async()=>{try{await navigator.clipboard.writeText(t.hash),m(!0),setTimeout(()=>m(!1),2e3)}catch(e){console.error("Failed to copy:",e)}},y=a?t.hash:t.prefix+"•".repeat(32)+t.hash.slice(-4);return(0,n.jsx)(r.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},className:(0,l.cn)("flex items-center justify-between rounded-lg border border-white/10 bg-white/5 p-4 backdrop-blur-sm",s),children:(0,n.jsxs)("div",{className:"flex items-center space-x-3 flex-1 min-w-0",children:[(0,n.jsx)("div",{className:"flex-shrink-0",children:(0,n.jsx)(o.A,{className:"h-5 w-5 text-blue-400"})}),(0,n.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)("p",{className:"text-sm font-medium text-white truncate",children:t.name}),(0,n.jsx)("span",{className:(0,l.cn)("px-2 py-1 text-xs font-medium rounded-full border",(()=>{switch(t.status){case"active":return"text-green-400 bg-green-400/10 border-green-400/20";case"revoked":return"text-red-400 bg-red-400/10 border-red-400/20";case"expired":return"text-yellow-400 bg-yellow-400/10 border-yellow-400/20";default:return"text-gray-400 bg-gray-400/10 border-gray-400/20"}})()),children:t.status})]}),(0,n.jsxs)("div",{className:"flex items-center space-x-2 mt-1",children:[(0,n.jsx)("code",{className:"text-xs text-white/70 font-mono bg-black/20 px-2 py-1 rounded truncate max-w-xs",children:y}),(0,n.jsx)("button",{onClick:()=>p(!a),className:"text-white/60 hover:text-white transition-colors",title:a?"Hide key":"Show key",children:a?(0,n.jsx)(c.A,{className:"h-4 w-4"}):(0,n.jsx)(d.A,{className:"h-4 w-4"})}),(0,n.jsx)("button",{onClick:f,className:"text-white/60 hover:text-white transition-colors",title:"Copy key",children:h?(0,n.jsx)(x.A,{className:"h-4 w-4 text-green-400"}):(0,n.jsx)(u.A,{className:"h-4 w-4"})})]}),(0,n.jsxs)("div",{className:"flex items-center space-x-4 mt-2 text-xs text-white/60",children:[(0,n.jsxs)("span",{children:["Created: ",t.created]}),(0,n.jsxs)("span",{children:["Last used: ",t.lastUsed]})]})]})]})})},m=e=>{let{className:t,maxKeys:s=4}=e,[r,c]=(0,i.useState)([]),d=[{id:"1",name:"Production API Key",prefix:"uk_",hash:"uk_1234567890abcdef1234567890abcdef12345678",created:"2 days ago",lastUsed:"5 min ago",status:"active"},{id:"2",name:"Development Key",prefix:"uk_",hash:"uk_abcdef1234567890abcdef1234567890abcdef12",created:"1 week ago",lastUsed:"2 hours ago",status:"active"},{id:"3",name:"Staging Environment",prefix:"uk_",hash:"uk_567890abcdef1234567890abcdef1234567890ab",created:"3 days ago",lastUsed:"1 day ago",status:"expired"},{id:"4",name:"Legacy Integration",prefix:"uk_",hash:"uk_cdef1234567890abcdef1234567890abcdef1234",created:"2 weeks ago",lastUsed:"1 week ago",status:"revoked"}];return(0,i.useEffect)(()=>{let e=setTimeout(()=>{c(d.slice(0,s))},500);return()=>clearTimeout(e)},[s]),(0,n.jsxs)("div",{className:(0,l.cn)("space-y-3",t),children:[(0,n.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,n.jsx)(p.A,{className:"h-5 w-5 text-blue-400"}),(0,n.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Hashed API Keys"}),(0,n.jsx)("div",{className:"flex-1"}),(0,n.jsxs)("div",{className:"text-xs text-white/60",children:[r.length," keys"]})]}),(0,n.jsx)("div",{className:"space-y-2",children:(0,n.jsx)(a.N,{mode:"popLayout",children:r.map(e=>(0,n.jsx)(h,{keyData:e},e.id))})}),0===r.length&&(0,n.jsxs)("div",{className:"text-center py-8 text-white/60",children:[(0,n.jsx)(o.A,{className:"h-8 w-8 mx-auto mb-2 opacity-50"}),(0,n.jsx)("p",{className:"text-sm",children:"Loading API keys..."})]}),(0,n.jsx)("div",{className:"mt-4 p-3 rounded-lg bg-blue-400/10 border border-blue-400/20",children:(0,n.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,n.jsx)(p.A,{className:"h-4 w-4 text-blue-400 mt-0.5 flex-shrink-0"}),(0,n.jsxs)("div",{className:"text-xs text-blue-300",children:[(0,n.jsx)("p",{className:"font-medium mb-1",children:"One-way hashed keys"}),(0,n.jsx)("p",{className:"text-blue-300/80",children:"Keys are hashed using SHA-256 and cannot be reversed. Only the hash is stored in our database for maximum security."})]})]})})]})}},19953:(e,t,s)=>{"use strict";s.d(t,{j:()=>r,t:()=>a});var n=s(6024),i=s(43097);let r=e=>{let{className:t,IconLeft:s,label:r,IconRight:a,shiny:l=!1}=e;return(0,n.jsxs)("div",{className:"relative group/button",children:[(0,n.jsx)("div",{"aria-hidden":!0,className:"absolute -inset-0.5 bg-white rounded-lg blur-2xl group-hover/button:opacity-30 transition duration-300  opacity-0 "}),(0,n.jsxs)("div",{className:(0,i.cn)("relative flex items-center px-4 gap-2 text-sm font-semibold text-black group-hover:bg-white/90 duration-1000 rounded-lg h-10",{"bg-white":!l,"bg-gradient-to-r from-white/80 to-white":l},t),children:[s?(0,n.jsx)(s,{className:"w-4 h-4"}):null,r,a?(0,n.jsx)(a,{className:"w-4 h-4"}):null,l&&(0,n.jsx)("div",{"aria-hidden":!0,className:"pointer-events-none absolute inset-0 opacity-0 group-hover/button:[animation-delay:.2s] group-hover/button:animate-button-shine rounded-[inherit] bg-[length:200%_100%] bg-[linear-gradient(110deg,transparent,35%,rgba(255,255,255,.7),75%,transparent)]"})]})]})},a=e=>{let{className:t,IconLeft:s,label:r,IconRight:a}=e;return(0,n.jsxs)("div",{className:(0,i.cn)("items-center gap-2 px-4 duration-500 text-white/70 hover:text-white h-10 flex",t),children:[s?(0,n.jsx)(s,{className:"w-4 h-4"}):null,r,a?(0,n.jsx)(a,{className:"w-4 h-4"}):null]})}},25059:(e,t,s)=>{"use strict";s.d(t,{Aj:()=>o});var n=s(6024),i=s(50628),r=s(52313),a=s(43097);let l=e=>{let{className:t,delay:s=0,duration:i=2}=e;return(0,n.jsx)(r.P.div,{className:(0,a.cn)("absolute h-0.5 w-0.5 rounded-full bg-slate-500 shadow-[0_0_0_1px_#ffffff10]",t),initial:{opacity:0,x:-100,y:-100},animate:{opacity:[0,1,0],x:[0,300],y:[0,300]},transition:{duration:i,delay:s,repeat:1/0,repeatDelay:5*Math.random()+2,ease:"linear"},style:{background:"linear-gradient(90deg, #64748b, transparent)"}})},o=e=>{let{number:t=20,className:s}=e,[r,o]=(0,i.useState)([]);return(0,i.useEffect)(()=>{o(Array.from({length:t},(e,t)=>({id:t,delay:5*Math.random(),duration:2*Math.random()+1})))},[t]),(0,n.jsx)("div",{className:(0,a.cn)("absolute inset-0 overflow-hidden",s),children:r.map(e=>(0,n.jsx)(l,{delay:e.delay,duration:e.duration,className:"meteor-line"},e.id))})}},28025:(e,t,s)=>{Promise.resolve().then(s.bind(s,94124)),Promise.resolve().then(s.bind(s,41926)),Promise.resolve().then(s.bind(s,74001)),Promise.resolve().then(s.bind(s,19611)),Promise.resolve().then(s.bind(s,90230)),Promise.resolve().then(s.bind(s,15880)),Promise.resolve().then(s.bind(s,19179)),Promise.resolve().then(s.bind(s,84155)),Promise.resolve().then(s.bind(s,28707)),Promise.resolve().then(s.bind(s,62244)),Promise.resolve().then(s.t.bind(s,35685,23)),Promise.resolve().then(s.t.bind(s,82394,23))},28707:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});let n={src:"/_next/static/media/mainboard.9232efd0.svg",height:546,width:1512,blurWidth:0,blurHeight:0}},41926:(e,t,s)=>{"use strict";s.d(t,{AuditLogs:()=>p});var n=s(6024),i=s(50628),r=s(52313),a=s(95183),l=s(43097),o=s(71499),c=s(26794),d=s(35512),x=s(36220);let u=e=>{let{entry:t,className:s}=e;return(0,n.jsxs)(r.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},className:(0,l.cn)("flex items-start space-x-3 rounded-lg border p-3 backdrop-blur-sm",(()=>{switch(t.status){case"success":return"border-green-400/20 bg-green-400/5";case"warning":return"border-yellow-400/20 bg-yellow-400/5";case"error":return"border-red-400/20 bg-red-400/5";default:return"border-blue-400/20 bg-blue-400/5"}})(),s),children:[(0,n.jsx)("div",{className:"flex-shrink-0 mt-0.5",children:(()=>{switch(t.status){case"success":return(0,n.jsx)(o.A,{className:"h-4 w-4 text-green-400"});case"warning":return(0,n.jsx)(c.A,{className:"h-4 w-4 text-yellow-400"});case"error":return(0,n.jsx)(d.A,{className:"h-4 w-4 text-red-400"});default:return(0,n.jsx)(x.A,{className:"h-4 w-4 text-blue-400"})}})()}),(0,n.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsx)("p",{className:"text-sm font-medium text-white truncate",children:t.action}),(0,n.jsx)("p",{className:"text-xs text-white/60 flex-shrink-0 ml-2",children:t.timestamp})]}),(0,n.jsxs)("p",{className:"text-xs text-white/70 mt-1",children:[t.user," • ",t.resource]}),(0,n.jsx)("p",{className:"text-xs text-white/60 mt-1",children:t.details})]})]})},p=e=>{let{className:t,maxEntries:s=5}=e,[r,o]=(0,i.useState)([]),c=[{id:"1",timestamp:"2 min ago",action:"API Key Created",user:"<EMAIL>",resource:"prod-api-key-123",status:"success",details:"New production API key generated with full permissions"},{id:"2",timestamp:"5 min ago",action:"Rate Limit Exceeded",user:"api-user-456",resource:"endpoint:/api/v1/users",status:"warning",details:"Rate limit of 1000 req/min exceeded, requests throttled"},{id:"3",timestamp:"8 min ago",action:"Authentication Failed",user:"unknown",resource:"api-key-789",status:"error",details:"Invalid API key used for authentication attempt"},{id:"4",timestamp:"12 min ago",action:"Permission Updated",user:"<EMAIL>",resource:"user-permissions",status:"success",details:"Updated API access permissions for development team"},{id:"5",timestamp:"15 min ago",action:"Key Revoked",user:"<EMAIL>",resource:"compromised-key-101",status:"warning",details:"API key revoked due to security policy violation"}];return(0,i.useEffect)(()=>{let e=0,t=setInterval(()=>{e<c.length?(o(t=>[c[e],...t.slice(0,s-1)]),e++):(e=0,o([]))},2e3);return()=>clearInterval(t)},[s]),(0,n.jsxs)("div",{className:(0,l.cn)("space-y-3",t),children:[(0,n.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,n.jsx)(x.A,{className:"h-5 w-5 text-blue-400"}),(0,n.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Audit Logs"}),(0,n.jsx)("div",{className:"flex-1"}),(0,n.jsx)("div",{className:"text-xs text-white/60",children:"Live updates"})]}),(0,n.jsx)("div",{className:"space-y-2 max-h-80 overflow-hidden",children:(0,n.jsx)(a.N,{mode:"popLayout",children:r.map(e=>(0,n.jsx)(u,{entry:e},e.id))})}),0===r.length&&(0,n.jsxs)("div",{className:"text-center py-8 text-white/60",children:[(0,n.jsx)(x.A,{className:"h-8 w-8 mx-auto mb-2 opacity-50"}),(0,n.jsx)("p",{className:"text-sm",children:"Loading audit logs..."})]})]})}},43097:(e,t,s)=>{"use strict";s.d(t,{cn:()=>r});var n=s(49973),i=s(22928);function r(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,i.QP)((0,n.$)(t))}},62244:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});let n={src:"/_next/static/media/map.aa68879d.svg",height:440,width:960,blurWidth:0,blurHeight:0}},74001:(e,t,s)=>{"use strict";s.d(t,{CTA:()=>d});var n=s(6024),i=s(19246),r=s(59109),a=s(77755),l=s(35685),o=s.n(l),c=s(19953);let d=()=>(0,n.jsx)("div",{className:"w-full h-full overflow-hidden",children:(0,n.jsxs)("div",{className:"relative pb-40 pt-14 ",children:[(0,n.jsx)(x,{className:"absolute inset-x-0 w-full mx-auto pointer-events-none -bottom-80 max-sm:w-8"}),(0,n.jsx)(i._,{align:"center",title:(0,n.jsxs)(n.Fragment,{children:["Protect your API.",(0,n.jsx)("br",{})," Start today."]}),children:(0,n.jsxs)("div",{className:"flex flex-col items-center justify-center gap-6 mt-2 sm:mt-5 sm:flex-row",children:[(0,n.jsx)(o(),{target:"_blank",href:"https://cal.com/team/unkey/user-interview?utm_source=banner&utm_campaign=oss",children:(0,n.jsx)(c.t,{label:"Chat with us",IconRight:r.A})}),(0,n.jsx)(o(),{href:"https://app.unkey.com",children:(0,n.jsx)(c.j,{shiny:!0,label:"Start Now",IconRight:a.A})})]})}),(0,n.jsx)("div",{className:"mt-8 sm:mt-10 text-balance",children:(0,n.jsx)("p",{className:"w-full mx-auto text-sm leading-6 text-center text-white/60 max-w-[500px]",children:"150,000 requests per month. No CC required."})})]})}),x=e=>{let{className:t}=e;return(0,n.jsxs)("svg",{className:t,width:"944",height:"1033",viewBox:"0 0 944 1033",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,n.jsxs)("g",{opacity:"0.3",children:[(0,n.jsx)("g",{style:{mixBlendMode:"lighten"},filter:"url(#filter0_f_2076_3208)",children:(0,n.jsx)("ellipse",{cx:"574.307",cy:"568.208",rx:"32.7783",ry:"293.346",transform:"rotate(-164.946 574.307 568.208)",fill:"url(#paint0_linear_2076_3208)",fillOpacity:"0.5"})}),(0,n.jsx)("g",{style:{mixBlendMode:"color-dodge"},filter:"url(#filter1_f_2076_3208)",children:(0,n.jsx)("ellipse",{cx:"468.5",cy:"589.25",rx:"26.5",ry:"293.25",transform:"rotate(180 468.5 589.25)",fill:"url(#paint1_linear_2076_3208)",fillOpacity:"0.5"})})]}),(0,n.jsxs)("defs",{children:[(0,n.jsxs)("filter",{id:"filter0_f_2076_3208",x:"402.782",y:"195.799",width:"343.05",height:"744.818",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB",children:[(0,n.jsx)("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),(0,n.jsx)("feBlend",{mode:"normal",in:"SourceGraphic",in2:"BackgroundImageFix",result:"shape"}),(0,n.jsx)("feGaussianBlur",{stdDeviation:"44.5",result:"effect1_foregroundBlur_2076_3208"})]}),(0,n.jsxs)("filter",{id:"filter1_f_2076_3208",x:"353",y:"207",width:"231",height:"764.5",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB",children:[(0,n.jsx)("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),(0,n.jsx)("feBlend",{mode:"normal",in:"SourceGraphic",in2:"BackgroundImageFix",result:"shape"}),(0,n.jsx)("feGaussianBlur",{stdDeviation:"44.5",result:"effect1_foregroundBlur_2076_3208"})]}),(0,n.jsxs)("linearGradient",{id:"paint0_linear_2076_3208",x1:"574.307",y1:"274.862",x2:"574.307",y2:"861.554",gradientUnits:"userSpaceOnUse",children:[(0,n.jsx)("stop",{stopColor:"white"}),(0,n.jsx)("stop",{offset:"1",stopColor:"white",stopOpacity:"0"})]}),(0,n.jsxs)("linearGradient",{id:"paint1_linear_2076_3208",x1:"468.5",y1:"296",x2:"468.5",y2:"882.5",gradientUnits:"userSpaceOnUse",children:[(0,n.jsx)("stop",{stopColor:"white"}),(0,n.jsx)("stop",{offset:"1",stopColor:"white",stopOpacity:"0"})]})]})]})}},84155:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});let n={src:"/_next/static/media/ip.5d4ed2b5.svg",height:840,width:840,blurWidth:0,blurHeight:0}},90230:(e,t,s)=>{"use strict";s.d(t,{Hero:()=>m});var n=s(6024),i=s(52313),r=s(82757),a=s(35685),l=s.n(a),o=s(19953),c=s(89639),d=s(25159),x=s(77755);function u(){return(0,n.jsxs)("div",{className:"relative flex flex-col items-center text-center ",children:[(0,n.jsx)("h1",{className:"bg-gradient-to-br text-balance text-transparent bg-gradient-stop bg-clip-text from-white via-white via-30% to-white/30  font-medium text-6xl leading-none xl:text-[82px] tracking-tighter",children:"The Developer Platform for Modern APIs"}),(0,n.jsx)("p",{className:"mt-6 sm:mt-8 bg-gradient-to-br text-transparent text-balance bg-gradient-stop bg-clip-text max-w-sm sm:max-w-lg xl:max-w-4xl from-white/70 via-white/70 via-40% to-white/30 text-base md:text-lg",children:"Easily integrate necessary API features like API keys, rate limiting, and usage analytics, ensuring your API is ready to scale."}),(0,n.jsxs)("div",{className:"flex items-center gap-6 mt-16",children:[(0,n.jsx)(l(),{href:"https://app.unkey.com",className:"group",children:(0,n.jsx)(o.j,{shiny:!0,IconLeft:c.A,label:"Get started",className:"h-10"})}),(0,n.jsx)(l(),{href:"/docs",className:"hidden sm:flex",children:(0,n.jsx)(o.t,{IconLeft:d.A,label:"Documentation",IconRight:x.A})})]})]})}var p=s(28707);let h=e=>{let{className:t}=e;return(0,n.jsx)("div",{className:t})},m=()=>(0,n.jsxs)(i.P.div,{className:"relative w-full flex flex-col items-center justify-between mt-48",variants:{hidden:{},visible:{transition:{staggerChildren:.3}}},initial:"hidden",animate:"visible",children:[(0,n.jsx)(i.P.div,{variants:{hidden:{opacity:0,y:25},visible:{opacity:1,y:0,transition:{duration:.6,ease:"easeOut"}}},children:(0,n.jsx)(u,{})}),(0,n.jsx)("div",{children:(0,n.jsx)(r.default,{src:p.default,alt:"Animated SVG showing computer circuits lighting up",className:"absolute hidden xl:right-32 xl:flex -z-10 xl:-top-56",style:{transform:"scale(2)"},priority:!0})}),(0,n.jsx)(h,{className:"absolute hidden md:flex left-1/2 -translate-x-[calc(50%+85px)] -bottom-[224px] -z-10"})]})},94124:(e,t,s)=>{"use strict";s.d(t,{CodeExamples:()=>L});var n={};s.r(n),s.d(n,{SJ:()=>o,Pc:()=>c,WL:()=>d,un:()=>x,I:()=>u,NY:()=>p});var i=s(6024),r=s(19953),a=s(19246),l=s(50628);let o=e=>{let{className:t}=e;return(0,i.jsxs)("svg",{className:t,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,i.jsx)("path",{d:"M12 2L2 7L12 12L22 7L12 2Z",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),(0,i.jsx)("path",{d:"M2 17L12 22L22 17",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),(0,i.jsx)("path",{d:"M2 12L12 17L22 12",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})]})},c=e=>{let{className:t}=e;return(0,i.jsxs)("svg",{className:t,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,i.jsx)("path",{d:"M12 2C12 2 8 6 8 12C8 16.4183 10.2386 18 12 18C13.7614 18 16 16.4183 16 12C16 6 12 2 12 2Z",fill:"currentColor"}),(0,i.jsx)("path",{d:"M12 18C12 18 16 14 16 8C16 3.58172 13.7614 2 12 2C10.2386 2 8 3.58172 8 8C8 14 12 18 12 18Z",fill:"currentColor",opacity:"0.6"})]})},d=e=>{let{className:t}=e;return(0,i.jsxs)("svg",{className:t,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,i.jsx)("path",{d:"M3 12H21M12 3V21",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),(0,i.jsx)("circle",{cx:"12",cy:"12",r:"9",stroke:"currentColor",strokeWidth:"2",fill:"none"})]})},x=e=>{let{className:t}=e;return(0,i.jsxs)("svg",{className:t,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,i.jsx)("path",{d:"M12 2C14.2091 2 16 3.79086 16 6V10H8V12H18C20.2091 12 22 13.7909 22 16C22 18.2091 20.2091 20 18 20H16V18C16 15.7909 14.2091 14 12 14H8C5.79086 14 4 12.2091 4 10V6C4 3.79086 5.79086 2 8 2H12Z",fill:"currentColor",opacity:"0.6"}),(0,i.jsx)("circle",{cx:"10",cy:"6",r:"1",fill:"currentColor"}),(0,i.jsx)("circle",{cx:"14",cy:"18",r:"1",fill:"currentColor"})]})},u=e=>{let{className:t}=e;return(0,i.jsxs)("svg",{className:t,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,i.jsx)("path",{d:"M12 2L15.09 8.26L22 9L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9L8.91 8.26L12 2Z",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",fill:"none"}),(0,i.jsx)("circle",{cx:"12",cy:"12",r:"3",stroke:"currentColor",strokeWidth:"2",fill:"none"})]})},p=e=>{let{className:t}=e;return(0,i.jsxs)("svg",{className:t,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,i.jsx)("rect",{x:"2",y:"2",width:"20",height:"20",rx:"2",fill:"currentColor",opacity:"0.1"}),(0,i.jsx)("path",{d:"M8 8H16M12 8V16",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),(0,i.jsx)("path",{d:"M16 12H18C19.1046 12 20 12.8954 20 14C20 15.1046 19.1046 16 18 16H16",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})]})};var h=s(43097);let m=e=>{let{children:t,className:s}=e;return(0,i.jsxs)("div",{className:(0,h.cn)("relative overflow-hidden rounded-lg border border-white/10 bg-black/50 backdrop-blur-sm",s),children:[(0,i.jsxs)("div",{className:"flex items-center justify-between border-b border-white/10 px-4 py-2",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)("div",{className:"h-3 w-3 rounded-full bg-red-500"}),(0,i.jsx)("div",{className:"h-3 w-3 rounded-full bg-yellow-500"}),(0,i.jsx)("div",{className:"h-3 w-3 rounded-full bg-green-500"})]}),(0,i.jsx)("div",{className:"text-xs text-white/60",children:"Terminal"})]}),(0,i.jsx)("div",{className:"p-4 font-mono text-sm",children:t})]})};var f=s(3671),y=s(17500);let g=e=>{let{code:t,className:s}=e,[n,r]=(0,l.useState)(!1),a=async()=>{try{await navigator.clipboard.writeText(t),r(!0),setTimeout(()=>r(!1),2e3)}catch(e){console.error("Failed to copy code:",e)}};return(0,i.jsx)("button",{onClick:a,className:(0,h.cn)("inline-flex items-center justify-center rounded-md p-2 text-white/60 transition-colors hover:bg-white/10 hover:text-white focus:outline-none focus:ring-2 focus:ring-white/20",s),title:n?"Copied!":"Copy code",children:n?(0,i.jsx)(f.A,{className:"h-4 w-4 text-green-400"}):(0,i.jsx)(y.A,{className:"h-4 w-4"})})};var j=s(25059),w=s(41095),v=s(77755),k=s(35685),_=s.n(k);let b=w.bL,N={plain:{color:"#F8F8F2",backgroundColor:"#282A36"},styles:[{types:["keyword"],style:{color:"#9D72FF"}},{types:["function"],style:{color:"#FB3186"}},{types:["string"],style:{color:"#3CEEAE"}},{types:["string-property"],style:{color:"#9D72FF"}},{types:["number"],style:{color:"#FB3186"}},{types:["comment"],style:{color:"#4D4D4D"}}]},C='curl --request POST \\\n  --url https://api.unkey.dev/v1/keys.createKey \\\n  --header \'Authorization: Bearer <UNKEY_ROOT_KEY>\' \\\n  --header \'Content-Type: application/json\' \\\n  --data \'{\n    "apiId": "api_123",\n    "ownerId": "user_123",\n    "expires": '.concat(Date.now()+6048e5,',\n    "ratelimit": {\n      "type": "fast",\n      "limit": 10,\n      "duration": 60_000\n    },\n  }\''),I={Typescript:[{name:"Typescript",Icon:p,codeBlock:'import { verifyKey } from \'@unkey/api\';\n\nconst { result, error } = await verifyKey({\n  apiId: "api_123",\n  key: "xyz_123"\n})\n\nif ( error ) {\n  // handle network error\n}\n\nif ( !result.valid ) {\n  // reject unauthorized request\n}\n\n// handle request',editorLanguage:"tsx"},{name:"Next.js",Icon:p,codeBlock:"import { withUnkey } from '@unkey/nextjs';\nexport const POST = withUnkey(async (req) => {\n  // Process the request here\n  // You have access to the typed verification response using `req.unkey`\n  console.log(req.unkey);\n  return new Response('Your API key is valid!');\n});",editorLanguage:"tsx"},{name:"Nuxt",codeBlock:'export default defineEventHandler(async (event) => {\n  if (!event.context.unkey.valid) {\n    throw createError({ statusCode: 403, message: "Invalid API key" })\n  }\n\n  // return authorised information\n  return {\n    // ...\n  };\n});',Icon:p,editorLanguage:"tsx"},{name:"Hono",Icon:p,codeBlock:'import { Hono } from "hono"\nimport { UnkeyContext, unkey } from "@unkey/hono";\n\nconst app = new Hono<{ Variables: { unkey: UnkeyContext } }>();\napp.use("*", unkey());\n\napp.get("/somewhere", (c) => {\n  // access the unkey response here to get metadata of the key etc\n  const unkey = c.get("unkey")\n return c.text("yo")\n})',editorLanguage:"tsx"},{name:"Ratelimiting",Icon:p,codeBlock:'import { Ratelimit } from "@unkey/ratelimit"\n\nconst unkey = new Ratelimit({\n  rootKey: process.env.UNKEY_ROOT_KEY,\n  namespace: "my-app",\n  limit: 10,\n  duration: "30s",\n  async: true\n})\n\n// elsewhere\nasync function handler(request) {\n  const identifier = request.getUserId() // or ip or anything else you want\n\n  const ratelimit = await unkey.limit(identifier)\n  if (!ratelimit.success){\n    return new Response("try again later", { status: 429 })\n  }\n\n  // handle the request here\n\n}',editorLanguage:"tsx"}],Python:[{name:"Python",Icon:x,codeBlock:'import asyncio\nimport os\nimport unkey\n\nasync def main() -> None:\n  client = unkey.Client(api_key=os.environ["API_KEY"])\n  await client.start()\n\n  result = await client.keys.verify_key("prefix_abc123")\n\n if result.is_ok:\n   print(data.valid)\n else:\n   print(result.unwrap_err())',editorLanguage:"python"},{name:"FastAPI",Icon:x,codeBlock:'import os\nfrom typing import Any, Dict, Optional\n\nimport fastapi  # pip install fastapi\nimport unkey  # pip install unkey.py\nimport uvicorn  # pip install uvicorn\n\napp = fastapi.FastAPI()\n\n\ndef key_extractor(*args: Any, **kwargs: Any) -> Optional[str]:\n    if isinstance(auth := kwargs.get("authorization"), str):\n        return auth.split(" ")[-1]\n\n    return None\n\n\<EMAIL>("/protected")\<EMAIL>(os.environ["UNKEY_API_ID"], key_extractor)\nasync def protected_route(\n    *,\n    authorization: str = fastapi.Header(None),\n    unkey_verification: Any = None,\n) -> Dict[str, Optional[str]]:\n    assert isinstance(unkey_verification, unkey.ApiKeyVerification)\n    assert unkey_verification.valid\n    print(unkey_verification.owner_id)\n    return {"message": "protected!"}\n\n\nif __name__ == "__main__":\n    uvicorn.run(app)\n',editorLanguage:"python"}],Golang:[{name:"Verify key",Icon:d,codeBlock:'package main\n\nimport(\n	unkeygo "github.com/unkeyed/unkey-go"\n	"context"\n	"github.com/unkeyed/unkey-go/models/components"\n	"log"\n)\n\nfunc main() {\n    s := unkeygo.New(\n        unkeygo.WithSecurity("<YOUR_BEARER_TOKEN_HERE>"),\n    )\n\n    ctx := context.Background()\n    res, err := s.Keys.VerifyKey(ctx, components.V1KeysVerifyKeyRequest{\n        APIID: unkeygo.String("api_1234"),\n        Key: "sk_1234",\n        Ratelimits: []components.Ratelimits{\n            components.Ratelimits{\n                Name: "tokens",\n                Limit: unkeygo.Int64(500),\n                Duration: unkeygo.Int64(3600000),\n            },\n            components.Ratelimits{\n                Name: "tokens",\n                Limit: unkeygo.Int64(20000),\n                Duration: unkeygo.Int64(86400000),\n            },\n        },\n    })\n    if err != nil {\n        log.Fatal(err)\n    }\n    if res.V1KeysVerifyKeyResponse != nil {\n        // handle response\n    }\n}',editorLanguage:"go"},{name:"Create key",Icon:d,codeBlock:'package main\n\nimport(\n	unkeygo "github.com/unkeyed/unkey-go"\n	"context"\n	"github.com/unkeyed/unkey-go/models/operations"\n	"log"\n)\n\nfunc main() {\n    s := unkeygo.New(\n        unkeygo.WithSecurity("<YOUR_BEARER_TOKEN_HERE>"),\n    )\n\n    ctx := context.Background()\n    res, err := s.Keys.CreateKey(ctx, operations.CreateKeyRequestBody{\n        APIID: "api_123",\n        Name: unkeygo.String("my key"),\n        ExternalID: unkeygo.String("team_123"),\n        Meta: map[string]any{\n            "billingTier": "PRO",\n            "trialEnds": "2023-06-16T17:16:37.161Z",\n        },\n        Roles: []string{\n            "admin",\n            "finance",\n        },\n        Permissions: []string{\n            "domains.create_record",\n            "say_hello",\n        },\n        Expires: unkeygo.Int64(1623869797161),\n        Remaining: unkeygo.Int64(1000),\n        Refill: &operations.Refill{\n            Interval: operations.IntervalDaily,\n            Amount: 100,\n        },\n        Ratelimit: &operations.Ratelimit{\n            Type: operations.TypeFast.ToPointer(),\n            Limit: 10,\n            Duration: unkeygo.Int64(60000),\n        },\n        Enabled: unkeygo.Bool(false),\n    })\n    if err != nil {\n        log.Fatal(err)\n    }\n    if res.Object != nil {\n        // handle response\n    }\n}\n\n',editorLanguage:"go"}],Java:[{name:"Verify key",Icon:n.JavaIcon,codeBlock:'package com.example.myapp;\nimport com.unkey.unkeysdk.dto.KeyVerifyRequest;\nimport com.unkey.unkeysdk.dto.KeyVerifyResponse;\n\n@RestController\npublic class APIController {\n\n    private static IKeyService keyService = new KeyService();\n\n    @PostMapping("/verify")\n    public KeyVerifyResponse verifyKey(\n        @RequestBody KeyVerifyRequest keyVerifyRequest) {\n        // Delegate the creation of the key to the KeyService from the SDK\n        return keyService.verifyKey(keyVerifyRequest);\n    }\n}',editorLanguage:"tsx"},{name:"Create key",Icon:n.JavaIcon,codeBlock:'package com.example.myapp;\n\nimport com.unkey.unkeysdk.dto.KeyCreateResponse;\nimport com.unkey.unkeysdk.dto.KeyCreateRequest;\n\n@RestController\npublic class APIController {\n\n    private static IKeyService keyService = new KeyService();\n\n    @PostMapping("/createKey")\n    public KeyCreateResponse createKey(\n            @RequestBody KeyCreateRequest keyCreateRequest,\n            @RequestHeader("Authorization") String authToken) {\n        // Delegate the creation of the key to the KeyService from the SDK\n        return keyService.createKey(keyCreateRequest, authToken);\n    }\n}\n\n',editorLanguage:"tsx"}],Elixir:[{name:"Verify key",Icon:c,codeBlock:'UnkeyElixirSdk.verify_key("xyz_AS5HDkXXPot2MMoPHD8jnL")\n# returns\n%{"valid" => true,\n  "ownerId" => "chronark",\n  "meta" => %{\n    "hello" => "world"\n  }}',editorLanguage:"tsx"}],Rust:[{name:"Verify key",Icon:u,codeBlock:'use unkey::models::{VerifyKeyRequest, Wrapped};\nuse unkey::Client;\n\nasync fn verify_key() {\n    let api_key = env::var("UNKEY_API_KEY").expect("Environment variable UNKEY_API_KEY not found");\n    let c = Client::new(&api_key);\n    let req = VerifyKeyRequest::new("test_req", "api_458vdYdbwut5LWABzXZP3Z8jPVas");\n\n    match c.verify_key(req).await {\n        Wrapped::Ok(res) => println!("{res:?}"),\n        Wrapped::Err(err) => eprintln!("{err:?}"),\n    }\n}',editorLanguage:"rust"}],Curl:[{name:"Verify key",Icon:o,codeBlock:'curl --request POST \\\n  --url https://api.unkey.dev/v1/keys.verifyKey \\\n  --header \'Content-Type: application/json\' \\\n  --data \'{\n    "apiId": "api_1234",\n    "key": "sk_1234",\n  }\'',editorLanguage:"tsx"},{name:"Create key",Icon:o,codeBlock:C,editorLanguage:"tsx"},{name:"Ratelimit",Icon:o,codeBlock:'curl --request POST   --url https://api.unkey.dev/v1/ratelimits.limit   --header \'Authorization: Bearer <token>\'   --header \'Content-Type: application/json\'   --data \'{\n    "namespace": "email.outbound",\n    "identifier": "user_123",\n    "limit": 10,\n    "duration": 60000,\n    "async": true\n}\'',editorLanguage:"tsx"}]},B=[{name:"Typescript",Icon:p},{name:"Python",Icon:x},{name:"Rust",Icon:u},{name:"Golang",Icon:d},{name:"Curl",Icon:o},{name:"Elixir",Icon:c},{name:"Java",Icon:n.JavaIcon}],A=l.forwardRef((e,t)=>{let{className:s,value:n,...r}=e;return(0,i.jsx)(w.l9,{ref:t,value:n,className:(0,h.cn)("inline-flex items-center gap-1 justify-center whitespace-nowrap rounded-t-lg px-3  py-1.5 text-sm transition-all hover:text-white/80 disabled:pointer-events-none disabled:opacity-50 bg-gradient-to-t from-black to-black data-[state=active]:from-white/10 border border-b-0 text-white/30 data-[state=active]:text-white border-[#454545] font-light",s),...r})});A.displayName=w.l9.displayName;let L=e=>{let{className:t}=e,[s,n]=(0,l.useState)("Typescript"),[o,c]=(0,l.useState)("Typescript"),[d,x]=(0,l.useState)("Typescript");function u(e){let{language:t,framework:s}=e,n=I[t].find(e=>e.name===s);return(null==n?void 0:n.codeBlock)||""}return(0,l.useEffect)(()=>{c(I[s].at(0).name)},[s]),(0,i.jsxs)("section",{className:t,children:[(0,i.jsxs)(a._,{title:"Any language, any framework, always secure",text:"Simplify API security and access control with Unkey's developer-friendly platform. Our SDKs, intuitive REST API, and public OpenAPI spec make it easy to secure your APIs without complex configurations.",align:"center",className:"relative",children:[(0,i.jsxs)("div",{className:"absolute bottom-32 left-[-50px]",children:[(0,i.jsx)(j.Aj,{className:"ml-2 fade-in-0",delay:3,number:1}),(0,i.jsx)(j.Aj,{className:"ml-10 fade-in-40",delay:0,number:1}),(0,i.jsx)(j.Aj,{className:"ml-16 fade-in-100",delay:5,number:1})]}),(0,i.jsxs)("div",{className:"absolute bottom-32 right-[200px]",children:[(0,i.jsx)(j.Aj,{className:"ml-2 fade-in-0",delay:4,number:1}),(0,i.jsx)(j.Aj,{className:"ml-10 fade-in-40",delay:0,number:1}),(0,i.jsx)(j.Aj,{className:"ml-16 fade-in-100",delay:2,number:1})]}),(0,i.jsx)("div",{className:"mt-10",children:(0,i.jsxs)("div",{className:"flex gap-6 pb-14",children:[(0,i.jsx)(_(),{href:"https://app.unkey.com",children:(0,i.jsx)(r.j,{shiny:!0,label:"Get Started",IconRight:v.A})},"get-started"),(0,i.jsx)(_(),{href:"/docs",children:(0,i.jsx)(r.t,{label:"Visit the docs",IconRight:v.A})},"docs")]})})]}),(0,i.jsxs)("div",{className:"relative w-full rounded-4xl border-[.75px] border-white/10 bg-gradient-to-b from-[#111111] to-black border-t-[.75px] border-t-white/20",children:[(0,i.jsx)("div",{"aria-hidden":!0,className:"absolute pointer-events-none inset-x-16 h-[432px] bottom-[calc(100%-2rem)] bg-[radial-gradient(94.69%_94.69%_at_50%_100%,rgba(255,255,255,0.20)_0%,rgba(255,255,255,0)_55.45%)]"}),(0,i.jsx)(b,{defaultValue:s,onValueChange:e=>n(e),className:"relative flex items-end h-16 px-4 border rounded-tr-3xl rounded-tl-3xl border-white/10 editor-top-gradient",children:(0,i.jsx)(w.B8,{className:"flex items-end gap-4 overflow-x-auto scrollbar-hidden",children:B.map(e=>{let{name:t,Icon:n}=e;return(0,i.jsxs)(A,{onMouseEnter:()=>x(t),onMouseLeave:()=>x(s),value:t,children:[(0,i.jsx)(n,{active:d===t||s===t}),t]},t)})})}),(0,i.jsxs)("div",{className:"flex flex-col sm:flex-row overflow-x-auto scrollbar-hidden sm:h-[520px]",children:[(0,i.jsx)(O,{frameworks:I[s],currentFramework:o,setFramework:c}),(0,i.jsxs)("div",{className:"relative flex w-full pt-4 pb-8 pl-8 font-mono text-xs text-white sm:text-sm",children:[(0,i.jsx)(m,{language:function(e){let{language:t,framework:s}=e,n=I[t].find(e=>e.name===s);return(null==n?void 0:n.editorLanguage)||"tsx"}({language:s,framework:o}),theme:N,codeBlock:u({language:s,framework:o})}),(0,i.jsx)(g,{textToCopy:u({language:s,framework:o}),className:"absolute hidden cursor-pointer top-5 right-5 lg:flex"})]})]})]})]})};function O(e){let{frameworks:t,currentFramework:s,setFramework:n}=e;return(0,i.jsx)("div",{className:"flex flex-col justify-between sm:w-[216px] text-white text-sm pt-6 px-4 font-mono md:border-r md:border-white/10",children:(0,i.jsx)("div",{className:"flex items-center space-x-2 sm:flex-col sm:space-x-0 sm:space-y-2",children:t.map(e=>(0,i.jsx)("button",{type:"button",onClick:()=>{n(e.name)},className:(0,h.cn)("flex items-center cursor-pointer hover:bg-white/10 py-1 px-2 rounded-lg w-[184px] ",{"bg-white/10 text-white":s===e.name,"text-white/40":s!==e.name}),children:(0,i.jsx)("div",{children:e.name})},e.name))})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[685,325,757,762,524,260,913,499,358],()=>t(28025)),_N_E=e.O()}]);