"use strict";exports.id=8393,exports.ids=[349,1449,1623,1650,3588,3760,4184,4591,5562,5846,6831,8050,8393,9093,9809],exports.modules={3588:(e,t,n)=>{n.r(t),n.d(t,{default:()=>a});var a=[Object.freeze({displayName:"Dockerfile",name:"docker",patterns:[{captures:{1:{name:"keyword.other.special-method.dockerfile"},2:{name:"keyword.other.special-method.dockerfile"}},match:"^\\s*\\b(?i:(FROM))\\b.*?\\b(?i:(AS))\\b"},{captures:{1:{name:"keyword.control.dockerfile"},2:{name:"keyword.other.special-method.dockerfile"}},match:"^\\s*(?i:(ONBUILD)\\s+)?(?i:(ADD|ARG|CMD|COPY|ENTRYPOINT|ENV|EXPOSE|FROM|HEALTHCHECK|LABEL|MAINTAINER|RUN|SHELL|STOPSIGNAL|USER|VOLUME|WORKDIR))\\s"},{captures:{1:{name:"keyword.operator.dockerfile"},2:{name:"keyword.other.special-method.dockerfile"}},match:"^\\s*(?i:(ONBUILD)\\s+)?(?i:(CMD|ENTRYPOINT))\\s"},{begin:'"',beginCaptures:{1:{name:"punctuation.definition.string.begin.dockerfile"}},end:'"',endCaptures:{1:{name:"punctuation.definition.string.end.dockerfile"}},name:"string.quoted.double.dockerfile",patterns:[{match:"\\\\.",name:"constant.character.escaped.dockerfile"}]},{begin:"'",beginCaptures:{1:{name:"punctuation.definition.string.begin.dockerfile"}},end:"'",endCaptures:{1:{name:"punctuation.definition.string.end.dockerfile"}},name:"string.quoted.single.dockerfile",patterns:[{match:"\\\\.",name:"constant.character.escaped.dockerfile"}]},{captures:{1:{name:"punctuation.whitespace.comment.leading.dockerfile"},2:{name:"comment.line.number-sign.dockerfile"},3:{name:"punctuation.definition.comment.dockerfile"}},comment:"comment.line",match:"^(\\s*)((#).*$\\n?)"}],scopeName:"source.dockerfile",aliases:["dockerfile"]})]},9093:(e,t,n)=>{n.r(t),n.d(t,{default:()=>r});var a=n(14055);let i=Object.freeze({displayName:"Elixir",fileTypes:["ex","exs"],firstLineMatch:"^#!/.*\\belixir",foldingStartMarker:"(after|else|catch|rescue|->|\\{|\\[|do)\\s*$",foldingStopMarker:"^\\s*((\\}|\\]|after|else|catch|rescue)\\s*$|end\\b)",name:"elixir",patterns:[{begin:"\\b(fn)\\b(?!.*->)",beginCaptures:{1:{name:"keyword.control.elixir"}},end:"$",patterns:[{include:"#core_syntax"}]},{captures:{1:{name:"entity.name.type.class.elixir"},2:{name:"punctuation.separator.method.elixir"},3:{name:"entity.name.function.elixir"}},match:"([A-Z]\\w+)\\s*(\\.)\\s*([a-z_]\\w*[!?]?)"},{captures:{1:{name:"constant.other.symbol.elixir"},2:{name:"punctuation.separator.method.elixir"},3:{name:"entity.name.function.elixir"}},match:"(:\\w+)\\s*(\\.)\\s*([_]?\\w*[!?]?)"},{captures:{1:{name:"keyword.operator.other.elixir"},2:{name:"entity.name.function.elixir"}},match:"(\\|>)\\s*([a-z_]\\w*[!?]?)"},{match:"\\b[a-z_]\\w*[!?]?(?=\\s*\\.?\\s*\\()",name:"entity.name.function.elixir"},{begin:"\\b(fn)\\b(?=.*->)",beginCaptures:{1:{name:"keyword.control.elixir"}},end:"(?>(->)|(when)|(\\)))",endCaptures:{1:{name:"keyword.operator.other.elixir"},2:{name:"keyword.control.elixir"},3:{name:"punctuation.section.function.elixir"}},patterns:[{include:"#core_syntax"}]},{include:"#core_syntax"},{begin:"^(?=.*->)((?![^\"']*(\"|')[^\"']*->)|(?=.*->[^\"']*(\"|')[^\"']*->))((?!.*\\([^)]*->)|(?=[^()]*->)|(?=\\s*\\(.*\\).*->))((?!.*\\b(fn)\\b)|(?=.*->.*\\bfn\\b))",beginCaptures:{1:{name:"keyword.control.elixir"}},end:"(?>(->)|(when)|(\\)))",endCaptures:{1:{name:"keyword.operator.other.elixir"},2:{name:"keyword.control.elixir"},3:{name:"punctuation.section.function.elixir"}},patterns:[{include:"#core_syntax"}]}],repository:{core_syntax:{patterns:[{begin:"^\\s*(defmodule)\\b",beginCaptures:{1:{name:"keyword.control.module.elixir"}},end:"\\b(do)\\b",endCaptures:{1:{name:"keyword.control.module.elixir"}},name:"meta.module.elixir",patterns:[{match:"\\b[A-Z]\\w*(?=\\.)",name:"entity.other.inherited-class.elixir"},{match:"\\b[A-Z]\\w*\\b",name:"entity.name.type.class.elixir"}]},{begin:"^\\s*(defprotocol)\\b",beginCaptures:{1:{name:"keyword.control.protocol.elixir"}},end:"\\b(do)\\b",endCaptures:{1:{name:"keyword.control.protocol.elixir"}},name:"meta.protocol_declaration.elixir",patterns:[{match:"\\b[A-Z]\\w*\\b",name:"entity.name.type.protocol.elixir"}]},{begin:"^\\s*(defimpl)\\b",beginCaptures:{1:{name:"keyword.control.protocol.elixir"}},end:"\\b(do)\\b",endCaptures:{1:{name:"keyword.control.protocol.elixir"}},name:"meta.protocol_implementation.elixir",patterns:[{match:"\\b[A-Z]\\w*\\b",name:"entity.name.type.protocol.elixir"}]},{begin:"^\\s*(def|defmacro|defdelegate|defguard)\\s+((?>[a-zA-Z_]\\w*(?>\\.|::))?(?>[a-zA-Z_]\\w*(?>[?!]|=(?!>))?|===?|>[>=]?|<=>|<[<=]?|[%&`/\\|]|\\*\\*?|=?~|[-+]@?|\\[\\]=?))((\\()|\\s*)",beginCaptures:{1:{name:"keyword.control.module.elixir"},2:{name:"entity.name.function.public.elixir"},4:{name:"punctuation.section.function.elixir"}},end:"(\\bdo:)|(\\bdo\\b)|(?=\\s+(def|defn|defmacro|defdelegate|defguard)\\b)",endCaptures:{1:{name:"constant.other.keywords.elixir"},2:{name:"keyword.control.module.elixir"}},name:"meta.function.public.elixir",patterns:[{include:"$self"},{begin:"\\s(\\\\\\\\)",beginCaptures:{1:{name:"keyword.operator.other.elixir"}},end:",|\\)|$",patterns:[{include:"$self"}]},{match:"\\b(is_atom|is_binary|is_bitstring|is_boolean|is_float|is_function|is_integer|is_list|is_map|is_nil|is_number|is_pid|is_port|is_record|is_reference|is_tuple|is_exception|abs|bit_size|byte_size|div|elem|hd|length|map_size|node|rem|round|tl|trunc|tuple_size)\\b",name:"keyword.control.elixir"}]},{begin:"^\\s*(defp|defnp|defmacrop|defguardp)\\s+((?>[a-zA-Z_]\\w*(?>\\.|::))?(?>[a-zA-Z_]\\w*(?>[?!]|=(?!>))?|===?|>[>=]?|<=>|<[<=]?|[%&`/\\|]|\\*\\*?|=?~|[-+]@?|\\[\\]=?))((\\()|\\s*)",beginCaptures:{1:{name:"keyword.control.module.elixir"},2:{name:"entity.name.function.private.elixir"},4:{name:"punctuation.section.function.elixir"}},end:"(\\bdo:)|(\\bdo\\b)|(?=\\s+(defp|defmacrop|defguardp)\\b)",endCaptures:{1:{name:"constant.other.keywords.elixir"},2:{name:"keyword.control.module.elixir"}},name:"meta.function.private.elixir",patterns:[{include:"$self"},{begin:"\\s(\\\\\\\\)",beginCaptures:{1:{name:"keyword.operator.other.elixir"}},end:",|\\)|$",patterns:[{include:"$self"}]},{match:"\\b(is_atom|is_binary|is_bitstring|is_boolean|is_float|is_function|is_integer|is_list|is_map|is_nil|is_number|is_pid|is_port|is_record|is_reference|is_tuple|is_exception|abs|bit_size|byte_size|div|elem|hd|length|map_size|node|rem|round|tl|trunc|tuple_size)\\b",name:"keyword.control.elixir"}]},{begin:'\\s*~L"""',comment:"Leex Sigil",end:'\\s*"""',name:"sigil.leex",patterns:[{include:"text.elixir"},{include:"text.html.basic"}]},{begin:'\\s*~H"""',comment:"HEEx Sigil",end:'\\s*"""',name:"sigil.heex",patterns:[{include:"text.elixir"},{include:"text.html.basic"}]},{begin:'@(module|type)?doc (~[a-z])?"""',comment:"@doc with heredocs is treated as documentation",end:'\\s*"""',name:"comment.block.documentation.heredoc",patterns:[{include:"#interpolated_elixir"},{include:"#escaped_char"}]},{begin:'@(module|type)?doc ~[A-Z]"""',comment:"@doc with heredocs is treated as documentation",end:'\\s*"""',name:"comment.block.documentation.heredoc"},{begin:"@(module|type)?doc (~[a-z])?'''",comment:"@doc with heredocs is treated as documentation",end:"\\s*'''",name:"comment.block.documentation.heredoc",patterns:[{include:"#interpolated_elixir"},{include:"#escaped_char"}]},{begin:"@(module|type)?doc ~[A-Z]'''",comment:"@doc with heredocs is treated as documentation",end:"\\s*'''",name:"comment.block.documentation.heredoc"},{comment:"@doc false is treated as documentation",match:"@(module|type)?doc false",name:"comment.block.documentation.false"},{begin:'@(module|type)?doc "',comment:"@doc with string is treated as documentation",end:'"',name:"comment.block.documentation.string",patterns:[{include:"#interpolated_elixir"},{include:"#escaped_char"}]},{match:"(?<!\\.)\\b(do|end|case|bc|lc|for|if|cond|unless|try|receive|fn|defmodule|defp?|defprotocol|defimpl|defrecord|defstruct|defnp?|defmacrop?|defguardp?|defdelegate|defexception|defoverridable|exit|after|rescue|catch|else|raise|reraise|throw|import|require|alias|use|quote|unquote|super|with)\\b(?![?!:])",name:"keyword.control.elixir"},{comment:" as above, just doesn't need a 'end' and does a logic operation",match:"(?<!\\.)\\b(and|not|or|when|xor|in)\\b",name:"keyword.operator.elixir"},{match:"\\b[A-Z]\\w*\\b",name:"entity.name.type.class.elixir"},{match:"\\b(nil|true|false)\\b(?![?!])",name:"constant.language.elixir"},{match:"\\b(__(CALLER|ENV|MODULE|DIR|STACKTRACE)__)\\b(?![?!])",name:"variable.language.elixir"},{captures:{1:{name:"punctuation.definition.variable.elixir"}},match:"(@)[a-zA-Z_]\\w*",name:"variable.other.readwrite.module.elixir"},{captures:{1:{name:"punctuation.definition.variable.elixir"}},match:"(&)\\d+",name:"variable.other.anonymous.elixir"},{match:"&(?![&])",name:"variable.other.anonymous.elixir"},{captures:{1:{name:"punctuation.definition.variable.elixir"}},match:"\\^[a-z_]\\w*",name:"variable.other.capture.elixir"},{match:"\\b0x[0-9A-Fa-f](?>_?[0-9A-Fa-f])*\\b",name:"constant.numeric.hex.elixir"},{match:"\\b\\d(?>_?\\d)*(\\.(?![^\\s\\d])(?>_?\\d)+)([eE][-+]?\\d(?>_?\\d)*)?\\b",name:"constant.numeric.float.elixir"},{match:"\\b\\d(?>_?\\d)*\\b",name:"constant.numeric.integer.elixir"},{match:"\\b0b[01](?>_?[01])*\\b",name:"constant.numeric.binary.elixir"},{match:"\\b0o[0-7](?>_?[0-7])*\\b",name:"constant.numeric.octal.elixir"},{begin:":'",captures:{0:{name:"punctuation.definition.constant.elixir"}},end:"'",name:"constant.other.symbol.single-quoted.elixir",patterns:[{include:"#interpolated_elixir"},{include:"#escaped_char"}]},{begin:':"',captures:{0:{name:"punctuation.definition.constant.elixir"}},end:'"',name:"constant.other.symbol.double-quoted.elixir",patterns:[{include:"#interpolated_elixir"},{include:"#escaped_char"}]},{begin:"(?>''')",beginCaptures:{0:{name:"punctuation.definition.string.begin.elixir"}},comment:"Single-quoted heredocs",end:"^\\s*'''",endCaptures:{0:{name:"punctuation.definition.string.end.elixir"}},name:"string.quoted.single.heredoc.elixir",patterns:[{include:"#interpolated_elixir"},{include:"#escaped_char"}]},{begin:"'",beginCaptures:{0:{name:"punctuation.definition.string.begin.elixir"}},comment:"single quoted string (allows for interpolation)",end:"'",endCaptures:{0:{name:"punctuation.definition.string.end.elixir"}},name:"string.quoted.single.elixir",patterns:[{include:"#interpolated_elixir"},{include:"#escaped_char"}]},{begin:'(?>""")',beginCaptures:{0:{name:"punctuation.definition.string.begin.elixir"}},comment:"Double-quoted heredocs",end:'^\\s*"""',endCaptures:{0:{name:"punctuation.definition.string.end.elixir"}},name:"string.quoted.double.heredoc.elixir",patterns:[{include:"#interpolated_elixir"},{include:"#escaped_char"}]},{begin:'"',beginCaptures:{0:{name:"punctuation.definition.string.begin.elixir"}},comment:"double quoted string (allows for interpolation)",end:'"',endCaptures:{0:{name:"punctuation.definition.string.end.elixir"}},name:"string.quoted.double.elixir",patterns:[{include:"#interpolated_elixir"},{include:"#escaped_char"}]},{begin:'~[a-z](?>""")',beginCaptures:{0:{name:"punctuation.definition.string.begin.elixir"}},comment:"Double-quoted heredocs sigils",end:'^\\s*"""',endCaptures:{0:{name:"punctuation.definition.string.end.elixir"}},name:"string.quoted.other.sigil.heredoc.elixir",patterns:[{include:"#interpolated_elixir"},{include:"#escaped_char"}]},{begin:"~[a-z]\\{",beginCaptures:{0:{name:"punctuation.definition.string.begin.elixir"}},comment:"sigil (allow for interpolation)",end:"\\}[a-z]*",endCaptures:{0:{name:"punctuation.definition.string.end.elixir"}},name:"string.quoted.other.sigil.elixir",patterns:[{include:"#interpolated_elixir"},{include:"#escaped_char"}]},{begin:"~[a-z]\\[",beginCaptures:{0:{name:"punctuation.definition.string.begin.elixir"}},comment:"sigil (allow for interpolation)",end:"\\][a-z]*",endCaptures:{0:{name:"punctuation.definition.string.end.elixir"}},name:"string.quoted.other.sigil.elixir",patterns:[{include:"#interpolated_elixir"},{include:"#escaped_char"}]},{begin:"~[a-z]<",beginCaptures:{0:{name:"punctuation.definition.string.begin.elixir"}},comment:"sigil (allow for interpolation)",end:">[a-z]*",endCaptures:{0:{name:"punctuation.definition.string.end.elixir"}},name:"string.quoted.other.sigil.elixir",patterns:[{include:"#interpolated_elixir"},{include:"#escaped_char"}]},{begin:"~[a-z]\\(",beginCaptures:{0:{name:"punctuation.definition.string.begin.elixir"}},comment:"sigil (allow for interpolation)",end:"\\)[a-z]*",endCaptures:{0:{name:"punctuation.definition.string.end.elixir"}},name:"string.quoted.other.sigil.elixir",patterns:[{include:"#interpolated_elixir"},{include:"#escaped_char"}]},{begin:"~[a-z]([^\\w])",beginCaptures:{0:{name:"punctuation.definition.string.begin.elixir"}},comment:"sigil (allow for interpolation)",end:"\\1[a-z]*",endCaptures:{0:{name:"punctuation.definition.string.end.elixir"}},name:"string.quoted.other.sigil.elixir",patterns:[{include:"#interpolated_elixir"},{include:"#escaped_char"}]},{begin:'~[A-Z](?>""")',beginCaptures:{0:{name:"punctuation.definition.string.begin.elixir"}},comment:"Double-quoted heredocs sigils",end:'^\\s*"""',endCaptures:{0:{name:"punctuation.definition.string.end.elixir"}},name:"string.quoted.other.sigil.heredoc.literal.elixir"},{begin:"~[A-Z]\\{",beginCaptures:{0:{name:"punctuation.definition.string.begin.elixir"}},comment:"sigil (without interpolation)",end:"\\}[a-z]*",endCaptures:{0:{name:"punctuation.definition.string.end.elixir"}},name:"string.quoted.other.sigil.literal.elixir"},{begin:"~[A-Z]\\[",beginCaptures:{0:{name:"punctuation.definition.string.begin.elixir"}},comment:"sigil (without interpolation)",end:"\\][a-z]*",endCaptures:{0:{name:"punctuation.definition.string.end.elixir"}},name:"string.quoted.other.sigil.literal.elixir"},{begin:"~[A-Z]<",beginCaptures:{0:{name:"punctuation.definition.string.begin.elixir"}},comment:"sigil (without interpolation)",end:">[a-z]*",endCaptures:{0:{name:"punctuation.definition.string.end.elixir"}},name:"string.quoted.other.sigil.literal.elixir"},{begin:"~[A-Z]\\(",beginCaptures:{0:{name:"punctuation.definition.string.begin.elixir"}},comment:"sigil (without interpolation)",end:"\\)[a-z]*",endCaptures:{0:{name:"punctuation.definition.string.end.elixir"}},name:"string.quoted.other.sigil.literal.elixir"},{begin:"~[A-Z]([^\\w])",beginCaptures:{0:{name:"punctuation.definition.string.begin.elixir"}},comment:"sigil (without interpolation)",end:"\\1[a-z]*",endCaptures:{0:{name:"punctuation.definition.string.end.elixir"}},name:"string.quoted.other.sigil.literal.elixir"},{captures:{1:{name:"punctuation.definition.constant.elixir"}},comment:"symbols",match:"(?<!:)(:)(?>[a-zA-Z_][\\w@]*(?>[?!]|=(?![>=]))?|<>|===?|!==?|<<>>|<<<|>>>|~~~|::|<-|\\|>|=>|=~|=|/|\\\\\\\\|\\*\\*?|\\.\\.?\\.?|\\.\\.//|>=?|<=?|&&?&?|\\+\\+?|--?|\\|\\|?\\|?|!|@|\\%?\\{\\}|%|\\[\\]|\\^(\\^\\^)?)",name:"constant.other.symbol.elixir"},{captures:{1:{name:"punctuation.definition.constant.elixir"}},comment:"symbols",match:"(?>[a-zA-Z_][\\w@]*(?>[?!])?)(:)(?!:)",name:"constant.other.keywords.elixir"},{begin:"(^[ \\t]+)?(?=##)",beginCaptures:{1:{name:"punctuation.whitespace.comment.leading.elixir"}},end:"(?!#)",patterns:[{begin:"##",beginCaptures:{0:{name:"punctuation.definition.comment.elixir"}},end:"\\n",name:"comment.line.section.elixir"}]},{begin:"(^[ \\t]+)?(?=#)",beginCaptures:{1:{name:"punctuation.whitespace.comment.leading.elixir"}},end:"(?!#)",patterns:[{begin:"#",beginCaptures:{0:{name:"punctuation.definition.comment.elixir"}},end:"\\n",name:"comment.line.number-sign.elixir"}]},{match:"\\b_([^_][\\w]+[?!]?)",name:"comment.unused.elixir"},{match:"\\b_\\b",name:"comment.wildcard.elixir"},{comment:'\n			matches questionmark-letters.\n\n			examples (1st alternation = hex):\n			?\\x1     ?\\x61\n\n			examples (2rd alternation = escaped):\n			?\\n      ?\\b\n\n			examples (3rd alternation = normal):\n			?a       ?A       ?0\n			?*       ?"       ?(\n			?.       ?#\n\n			the negative lookbehind prevents against matching\n			p(42.tainted?)\n			',match:"(?<!\\w)\\?(\\\\(x[0-9A-Fa-f]{1,2}(?![0-9A-Fa-f])\\b|[^xMC])|[^\\s\\\\])",name:"constant.numeric.elixir"},{match:"\\+\\+|--|<\\|>",name:"keyword.operator.concatenation.elixir"},{match:"\\|>|<~>|<>|<<<|>>>|~>>|<<~|~>|<~|<\\|>",name:"keyword.operator.sigils_1.elixir"},{match:"&&&|&&",name:"keyword.operator.sigils_2.elixir"},{match:"<-|\\\\\\\\",name:"keyword.operator.sigils_3.elixir"},{match:"===?|!==?|<=?|>=?",name:"keyword.operator.comparison.elixir"},{match:"(\\|\\|\\||&&&|\\^\\^\\^|<<<|>>>|~~~)",name:"keyword.operator.bitwise.elixir"},{match:"(?<=[ \\t])!+|\\bnot\\b|&&|\\band\\b|\\|\\||\\bor\\b|\\bxor\\b",name:"keyword.operator.logical.elixir"},{match:"(\\*|\\+|-|/)",name:"keyword.operator.arithmetic.elixir"},{match:"\\||\\+\\+|--|\\*\\*|\\\\\\\\|<-|<>|<<|>>|::|\\.\\.|//|\\|>|~|=>|&",name:"keyword.operator.other.elixir"},{match:"=",name:"keyword.operator.assignment.elixir"},{match:":",name:"punctuation.separator.other.elixir"},{match:"\\;",name:"punctuation.separator.statement.elixir"},{match:",",name:"punctuation.separator.object.elixir"},{match:"\\.",name:"punctuation.separator.method.elixir"},{match:"\\{|\\}",name:"punctuation.section.scope.elixir"},{match:"\\[|\\]",name:"punctuation.section.array.elixir"},{match:"\\(|\\)",name:"punctuation.section.function.elixir"}]},escaped_char:{match:"\\\\(x[\\da-fA-F]{1,2}|.)",name:"constant.character.escaped.elixir"},interpolated_elixir:{begin:"#\\{",beginCaptures:{0:{name:"punctuation.section.embedded.begin.elixir"}},contentName:"source.elixir",end:"\\}",endCaptures:{0:{name:"punctuation.section.embedded.end.elixir"}},name:"meta.embedded.line.elixir",patterns:[{include:"#nest_curly_and_self"},{include:"$self"}]},nest_curly_and_self:{patterns:[{begin:"\\{",captures:{0:{name:"punctuation.section.scope.elixir"}},end:"\\}",patterns:[{include:"#nest_curly_and_self"}]},{include:"$self"}]}},scopeName:"source.elixir",embeddedLangs:["html"]});var r=[...a.default,i]},14069:(e,t,n)=>{n.r(t),n.d(t,{default:()=>r});var a=n(31127);let i=Object.freeze({displayName:"XSL",name:"xsl",patterns:[{begin:"(<)(xsl)((:))(template)",captures:{1:{name:"punctuation.definition.tag.xml"},2:{name:"entity.name.tag.namespace.xml"},3:{name:"entity.name.tag.xml"},4:{name:"punctuation.separator.namespace.xml"},5:{name:"entity.name.tag.localname.xml"}},end:"(>)",name:"meta.tag.xml.template",patterns:[{captures:{1:{name:"entity.other.attribute-name.namespace.xml"},2:{name:"entity.other.attribute-name.xml"},3:{name:"punctuation.separator.namespace.xml"},4:{name:"entity.other.attribute-name.localname.xml"}},match:" (?:([-_a-zA-Z0-9]+)((:)))?([a-zA-Z-]+)"},{include:"#doublequotedString"},{include:"#singlequotedString"}]},{include:"text.xml"}],repository:{doublequotedString:{begin:'"',beginCaptures:{0:{name:"punctuation.definition.string.begin.xml"}},end:'"',endCaptures:{0:{name:"punctuation.definition.string.end.xml"}},name:"string.quoted.double.xml"},singlequotedString:{begin:"'",beginCaptures:{0:{name:"punctuation.definition.string.begin.xml"}},end:"'",endCaptures:{0:{name:"punctuation.definition.string.end.xml"}},name:"string.quoted.single.xml"}},scopeName:"text.xml.xsl",embeddedLangs:["xml"]});var r=[...a.default,i]},18393:(e,t,n)=>{n.r(t),n.d(t,{default:()=>J});var a=n(14055),i=n(98013),r=n(25846),s=n(96736),o=n(28478),m=n(58050),c=n(43055),l=n(20106),d=n(92200),u=n(18044),p=n(21516),b=n(34184),g=n(31127),h=n(14069),f=n(25253),w=n(91449),x=n(63760),k=n(42204),y=n(14187),v=n(73279),C=n(90349),_=n(3588),A=n(74058),E=n(63914),$=n(2632),q=n(63069),N=n(29809),S=n(7395),j=n(56437),L=n(12734),z=n(6028),T=n(26831),R=n(94591),P=n(33374),O=n(44131),Z=n(33034),I=n(97310),D=n(31767),M=n(10861),G=n(37861),F=n(71650),U=n(41623),H=n(86636),B=n(38383),V=n(55334),W=n(9093),K=n(62976),Y=n(45562),X=n(85862);let Q=Object.freeze({displayName:"Wikitext",name:"wikitext",patterns:[{include:"#wikitext"},{include:"text.html.basic"}],repository:{wikitext:{patterns:[{include:"#signature"},{include:"#redirect"},{include:"#magic-words"},{include:"#argument"},{include:"#template"},{include:"#convert"},{include:"#list"},{include:"#table"},{include:"#font-style"},{include:"#internal-link"},{include:"#external-link"},{include:"#heading"},{include:"#break"},{include:"#wikixml"},{include:"#extension-comments"}],repository:{argument:{begin:"({{{)",end:"(}}})",name:"variable.parameter.wikitext",patterns:[{captures:{1:{name:"variable.other.wikitext"},2:{name:"keyword.operator.wikitext"}},match:"(?:^|\\G)([^#:\\|\\[\\]{}\\|]*)(\\|)"},{include:"$self"}]},break:{match:"^-{4,}",name:"markup.changed.wikitext"},convert:{begin:"(-\\{(?!\\{))([a-zA-Z](\\|))?",captures:{1:{name:"punctuation.definition.tag.template.wikitext"},2:{name:"entity.name.function.type.wikitext"},3:{name:"keyword.operator.wikitext"}},end:"(\\}-)",patterns:[{include:"$self"},{captures:{1:{name:"entity.name.tag.language.wikitext"},2:{name:"punctuation.separator.key-value.wikitext"},3:{name:"string.unquoted.text.wikitext",patterns:[{include:"$self"}]},4:{name:"punctuation.terminator.rule.wikitext"}},match:"(?:([a-zA-Z\\-]*)(:))?(.*?)(?:(;)|(?=\\}-))"}]},"extension-comments":{begin:"(<%--)\\s*(\\[)([A-Z_]*)(\\])",beginCaptures:{1:{name:"punctuation.definition.comment.extension.wikitext"},2:{name:"punctuation.definition.tag.extension.wikitext"},3:{name:"storage.type.extension.wikitext"},4:{name:"punctuation.definition.tag.extension.wikitext"}},end:"(\\[)([A-Z_]*)(\\])\\s*(--%>)",endCaptures:{1:{name:"punctuation.definition.tag.extension.wikitext"},2:{name:"storage.type.extension.wikitext"},3:{name:"punctuation.definition.tag.extension.wikitext"},4:{name:"punctuation.definition.comment.extension.wikitext"}},name:"comment.block.documentation.special.extension.wikitext",patterns:[{captures:{0:{name:"meta.object.member.extension.wikitext"},1:{name:"meta.object-literal.key.extension.wikitext"},2:{name:"punctuation.separator.dictionary.key-value.extension.wikitext"},3:{name:"punctuation.definition.string.begin.extension.wikitext"},4:{name:"string.quoted.other.extension.wikitext"},5:{name:"punctuation.definition.string.end.extension.wikitext"}},match:"(\\w*)\\s*(=)\\s*(#)(.*?)(#)"}]},"external-link":{patterns:[{captures:{1:{name:"punctuation.definition.tag.link.external.wikitext"},2:{name:"entity.name.tag.url.wikitext"},3:{name:"string.other.link.external.title.wikitext",patterns:[{include:"$self"}]},4:{name:"punctuation.definition.tag.link.external.wikitext"}},match:"(\\[)((?:(?:(?:http(?:s)?)|(?:ftp(?:s)?)):\\/\\/)[\\w.-]+(?:\\.[\\w\\.-]+)+[\\w\\-\\.~:\\/?#%@!$&'()\\*+,;=.]+)\\s*?([^\\]]*)(\\])",name:"meta.link.external.wikitext"},{captures:{1:{name:"punctuation.definition.tag.link.external.wikitext"},2:{name:"invalid.illegal.bad-url.wikitext"},3:{name:"string.other.link.external.title.wikitext",patterns:[{include:"$self"}]},4:{name:"punctuation.definition.tag.link.external.wikitext"}},match:"(\\[)([\\w.-]+(?:\\.[\\w\\.-]+)+[\\w\\-\\.~:\\/?#%@!$&'()\\*+,;=.]+)\\s*?([^\\]]*)(\\])",name:"invalid.illegal.bad-link.wikitext"}]},"font-style":{patterns:[{include:"#bold"},{include:"#italic"}],repository:{bold:{begin:"(''')",end:"(''')|$",name:"markup.bold.wikitext",patterns:[{include:"#italic"},{include:"$self"}]},italic:{begin:"('')",end:"((?=[^'])|(?=''))''((?=[^'])|(?=''))|$",name:"markup.italic.wikitext",patterns:[{include:"#bold"},{include:"$self"}]}}},heading:{captures:{2:{name:"string.quoted.other.heading.wikitext",patterns:[{include:"$self"}]}},match:"^(={1,6})\\s*(.+?)\\s*(\\1)$",name:"markup.heading.wikitext"},"internal-link":{TODO:"SINGLE LINE",begin:"(\\[\\[)(([^#:\\|\\[\\]{}]*:)*)?([^\\|\\[\\]]*)?",captures:{1:{name:"punctuation.definition.tag.link.internal.wikitext"},2:{name:"entity.name.tag.namespace.wikitext"},4:{name:"entity.other.attribute-name.wikitext"}},end:"(\\]\\])",name:"string.quoted.internal-link.wikitext",patterns:[{include:"$self"},{captures:{1:{name:"keyword.operator.wikitext"},5:{name:"entity.other.attribute-name.localname.wikitext"}},match:"(\\|)|(?:\\s*)(?:([-\\w.]+)((:)))?([-\\w.:]+)\\s*(=)"}]},list:{name:"markup.list.wikitext",patterns:[{captures:{1:{name:"punctuation.definition.list.begin.markdown.wikitext"}},match:"^([#*;:]+)"}]},"magic-words":{patterns:[{include:"#behavior-switches"},{include:"#outdated-behavior-switches"},{include:"#variables"}],repository:{"behavior-switches":{match:"(?i)(__)(NOTOC|FORCETOC|TOC|NOEDITSECTION|NEWSECTIONLINK|NOGALLERY|HIDDENCAT|EXPECTUNUSEDCATEGORY|NOCONTENTCONVERT|NOCC|NOTITLECONVERT|NOTC|INDEX|NOINDEX|STATICREDIRECT|NOGLOBAL|DISAMBIG)(__)",name:"constant.language.behavior-switcher.wikitext"},"outdated-behavior-switches":{match:"(?i)(__)(START|END)(__)",name:"invalid.deprecated.behavior-switcher.wikitext"},variables:{patterns:[{match:"(?i)(\\{\\{)(CURRENTYEAR|CURRENTMONTH|CURRENTMONTH1|CURRENTMONTHNAME|CURRENTMONTHNAMEGEN|CURRENTMONTHABBREV|CURRENTDAY|CURRENTDAY2|CURRENTDOW|CURRENTDAYNAME|CURRENTTIME|CURRENTHOUR|CURRENTWEEK|CURRENTTIMESTAMP|LOCALYEAR|LOCALMONTH|LOCALMONTH1|LOCALMONTHNAME|LOCALMONTHNAMEGEN|LOCALMONTHABBREV|LOCALDAY|LOCALDAY2|LOCALDOW|LOCALDAYNAME|LOCALTIME|LOCALHOUR|LOCALWEEK|LOCALTIMESTAMP)(\\}\\})",name:"constant.language.variables.time.wikitext"},{match:"(?i)(\\{\\{)(SITENAME|SERVER|SERVERNAME|DIRMARK|DIRECTIONMARK|SCRIPTPATH|STYLEPATH|CURRENTVERSION|CONTENTLANGUAGE|CONTENTLANG|PAGEID|PAGELANGUAGE|CASCADINGSOURCES|REVISIONID|REVISIONDAY|REVISIONDAY2|REVISIONMONTH|REVISIONMONTH1|REVISIONYEAR|REVISIONTIMESTAMP|REVISIONUSER|REVISIONSIZE)(\\}\\})",name:"constant.language.variables.metadata.wikitext"},{match:"ISBN\\s+((9[\\-\\s]?7[\\-\\s]?[89][\\-\\s]?)?(\\d[\\-\\s]?){10})",name:"constant.language.variables.isbn.wikitext"},{match:"RFC\\s+\\d+",name:"constant.language.variables.rfc.wikitext"},{match:"PMID\\s+\\d+",name:"constant.language.variables.pmid.wikitext"}]}}},redirect:{patterns:[{captures:{1:{name:"keyword.control.redirect.wikitext"},2:{name:"punctuation.definition.tag.link.internal.begin.wikitext"},3:{name:"entity.name.tag.namespace.wikitext"},4:null,5:{name:"entity.other.attribute-name.wikitext"},6:{name:"invalid.deprecated.ineffective.wikitext"},7:{name:"punctuation.definition.tag.link.internal.end.wikitext"}},match:"(?i)(^\\s*?#REDIRECT)\\s*(\\[\\[)(([^#:\\|\\[\\]{}]*?:)*)?([^\\|\\[\\]]*)?(\\|[^\\[\\]]*?)?(\\]\\])"}]},signature:{patterns:[{match:"~{3,5}",name:"keyword.other.signature.wikitext"}]},table:{patterns:[{begin:"^\\s*(\\{\\|)(.*)$",captures:{1:{name:"punctuation.definition.tag.table.wikitext"},2:{patterns:[{include:"text.html.basic#attribute"}]}},end:"^\\s*(\\|\\})",name:"meta.tag.block.table.wikitext",patterns:[{include:"$self"},{begin:"^\\s*(\\|-)\\s*",beginCaptures:{1:{name:"punctuation.definition.tag.begin.wikitext"}},end:"$",name:"meta.tag.block.table-row.wikitext",patterns:[{include:"$self"},{include:"text.html.basic#attribute"},{match:"\\|.*",name:"invalid.illegal.bad-table-context.wikitext"}]},{begin:"^\\s*(!)(([^\\[]*?)(\\|))?(.*?)(?=(!!)|$)",beginCaptures:{1:{name:"punctuation.definition.tag.begin.wikitext"},2:null,3:{patterns:[{include:"$self"},{include:"text.html.basic#attribute"}]},4:{name:"punctuation.definition.tag.wikitext"},5:{name:"markup.bold.style.wikitext"}},end:"$",name:"meta.tag.block.th.heading",patterns:[{captures:{1:{name:"punctuation.definition.tag.begin.wikitext"},3:{patterns:[{include:"text.html.basic#attribute"},{include:"$self"}]},4:{name:"punctuation.definition.tag.wikitext"},5:{name:"markup.bold.style.wikitext"}},match:"(!!)(([^\\[]*?)(\\|))?(.*?)(?=(!!)|$)",name:"meta.tag.block.th.inline.wikitext"},{include:"$self"}]},{captures:{1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"string.unquoted.caption.wikitext"}},end:"$",match:"^\\s*(\\|\\+)(.*?)$",name:"meta.tag.block.caption.wikitext",patterns:[{include:"$self"}]},{begin:"^\\s*(\\|)",beginCaptures:{1:{name:"punctuation.definition.tag.wikitext"}},end:"$",patterns:[{include:"$self"},{match:"\\|\\|",name:"keyword.operator.wikitext"}]}]}]},template:{begin:"(\\{\\{)\\s*(([^#:\\|\\[\\]{}]*(:))*)\\s*((#[^#:\\|\\[\\]{}]+(:))*)([^#:\\|\\[\\]{}]*)",captures:{1:{name:"punctuation.definition.tag.template.wikitext"},2:{name:"entity.name.tag.local-name.wikitext"},4:{name:"punctuation.separator.namespace.wikitext"},5:{name:"entity.name.function.wikitext"},7:{name:"punctuation.separator.namespace.wikitext"},8:{name:"entity.name.tag.local-name.wikitext"}},end:"(\\}\\})",patterns:[{include:"$self"},{match:"(\\|)",name:"keyword.operator.wikitext"},{captures:{1:{name:"entity.other.attribute-name.namespace.wikitext"},2:{name:"punctuation.separator.namespace.wikitext"},3:{name:"entity.other.attribute-name.local-name.wikitext"},4:{name:"keyword.operator.equal.wikitext"}},match:"(?<=\\|)\\s*(?:([-\\w.]+)(:))?([-\\w\\s\\.:]+)\\s*(=)"}]},wikixml:{patterns:[{include:"#wiki-self-closed-tags"},{include:"#normal-wiki-tags"},{include:"#nowiki"},{include:"#ref"},{include:"#jsonin"},{include:"#math"},{include:"#syntax-highlight"}],repository:{jsonin:{begin:"(?i)(<)(graph|templatedata)(\\s+[^>]+)?\\s*(>)",beginCaptures:{0:{name:"meta.tag.metadata.start.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{patterns:[{include:"text.html.basic#attribute"},{include:"$self"}]},4:{name:"punctuation.definition.tag.end.wikitext"}},contentName:"meta.embedded.block.json",end:"(?i)(</)(\\2)\\s*(>)",endCaptures:{0:{name:"meta.tag.metadata.end.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{name:"punctuation.definition.tag.end.wikitext"}},patterns:[{include:"source.json"}]},math:{begin:"(?i)(<)(math|chem|ce)(\\s+[^>]+)?\\s*(>)",beginCaptures:{0:{name:"meta.tag.metadata.start.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{patterns:[{include:"text.html.basic#attribute"},{include:"$self"}]},4:{name:"punctuation.definition.tag.end.wikitext"}},contentName:"meta.embedded.block.latex",end:"(?i)(</)(\\2)\\s*(>)",endCaptures:{0:{name:"meta.tag.metadata.end.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{name:"punctuation.definition.tag.end.wikitext"}},patterns:[{include:"text.html.markdown.math#math"}]},"normal-wiki-tags":{captures:{1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{patterns:[{include:"text.html.basic#attribute"},{include:"$self"}]},4:{name:"punctuation.definition.tag.end.wikitext"}},match:"(?i)(</?)(includeonly|onlyinclude|noinclude)(\\s+[^>]+)?\\s*(>)",name:"meta.tag.metedata.normal.wikitext"},nowiki:{begin:"(?i)(<)(nowiki)(\\s+[^>]+)?\\s*(>)",beginCaptures:{0:{name:"meta.tag.metadata.nowiki.start.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{patterns:[{include:"text.html.basic#attribute"},{include:"$self"}]},4:{name:"punctuation.definition.tag.end.wikitext"}},contentName:"meta.embedded.block.plaintext",end:"(?i)(</)(nowiki)\\s*(>)",endCaptures:{0:{name:"meta.tag.metadata.nowiki.end.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{name:"punctuation.definition.tag.end.wikitext"}}},ref:{begin:"(?i)(<)(ref)(\\s+[^>]+)?\\s*(>)",beginCaptures:{0:{name:"meta.tag.metadata.ref.start.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{patterns:[{include:"text.html.basic#attribute"},{include:"$self"}]},4:{name:"punctuation.definition.tag.end.wikitext"}},contentName:"meta.block.ref.wikitext",end:"(?i)(</)(ref)\\s*(>)",endCaptures:{0:{name:"meta.tag.metadata.ref.end.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{name:"punctuation.definition.tag.end.wikitext"}},patterns:[{include:"$self"}]},"syntax-highlight":{patterns:[{include:"#hl-css"},{include:"#hl-html"},{include:"#hl-ini"},{include:"#hl-java"},{include:"#hl-lua"},{include:"#hl-makefile"},{include:"#hl-perl"},{include:"#hl-r"},{include:"#hl-ruby"},{include:"#hl-php"},{include:"#hl-sql"},{include:"#hl-vb-net"},{include:"#hl-xml"},{include:"#hl-xslt"},{include:"#hl-yaml"},{include:"#hl-bat"},{include:"#hl-clojure"},{include:"#hl-coffee"},{include:"#hl-c"},{include:"#hl-cpp"},{include:"#hl-diff"},{include:"#hl-dockerfile"},{include:"#hl-go"},{include:"#hl-groovy"},{include:"#hl-pug"},{include:"#hl-js"},{include:"#hl-json"},{include:"#hl-less"},{include:"#hl-objc"},{include:"#hl-swift"},{include:"#hl-scss"},{include:"#hl-perl6"},{include:"#hl-powershell"},{include:"#hl-python"},{include:"#hl-julia"},{include:"#hl-rust"},{include:"#hl-scala"},{include:"#hl-shell"},{include:"#hl-ts"},{include:"#hl-csharp"},{include:"#hl-fsharp"},{include:"#hl-dart"},{include:"#hl-handlebars"},{include:"#hl-markdown"},{include:"#hl-erlang"},{include:"#hl-elixir"},{include:"#hl-latex"},{include:"#hl-bibtex"}],repository:{"hl-bat":{begin:"(?i)(<)(syntaxhighlight)((?:\\s+[^>]+)?(?:\\s+lang=(?:(['\"]?)(['\"]?)(?:batch|bat|dosbatch|winbatch)\\4))(?:\\s+[^>]+)?)\\s*(>)",beginCaptures:{0:{name:"meta.tag.metadata.start.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{patterns:[{include:"text.html.basic#attribute"},{include:"$self"}]},5:{name:"punctuation.definition.tag.end.wikitext"}},end:"(?i)(</)(syntaxhighlight)\\s*(>)",endCaptures:{0:{name:"meta.tag.metadata.end.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{name:"punctuation.definition.tag.end.wikitext"}},patterns:[{begin:"(^|\\G)",contentName:"meta.embedded.block.bat",end:"(?i)(?=</syntaxhighlight\\s*>)",patterns:[{include:"source.batchfile"}]}]},"hl-bibtex":{begin:"(?i)(<)(syntaxhighlight)((?:\\s+[^>]+)?(?:\\s+lang=(?:(['\"]?)(?:bibtex|bib)\\4))(?:\\s+[^>]+)?)\\s*(>)",beginCaptures:{0:{name:"meta.tag.metadata.start.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{patterns:[{include:"text.html.basic#attribute"},{include:"$self"}]},5:{name:"punctuation.definition.tag.end.wikitext"}},end:"(?i)(</)(syntaxhighlight)\\s*(>)",endCaptures:{0:{name:"meta.tag.metadata.end.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{name:"punctuation.definition.tag.end.wikitext"}},patterns:[{begin:"(^|\\G)",contentName:"meta.embedded.block.bibtex",end:"(?i)(?=</syntaxhighlight\\s*>)",patterns:[{include:"text.bibtex"}]}]},"hl-c":{begin:"(?i)(<)(syntaxhighlight)((?:\\s+[^>]+)?(?:\\s+lang=(?:(['\"]?)c\\4))(?:\\s+[^>]+)?)\\s*(>)",beginCaptures:{0:{name:"meta.tag.metadata.start.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{patterns:[{include:"text.html.basic#attribute"},{include:"$self"}]},5:{name:"punctuation.definition.tag.end.wikitext"}},end:"(?i)(</)(syntaxhighlight)\\s*(>)",endCaptures:{0:{name:"meta.tag.metadata.end.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{name:"punctuation.definition.tag.end.wikitext"}},patterns:[{begin:"(^|\\G)",contentName:"meta.embedded.block.c",end:"(?i)(?=</syntaxhighlight\\s*>)",patterns:[{include:"source.c"}]}]},"hl-clojure":{begin:"(?i)(<)(syntaxhighlight)((?:\\s+[^>]+)?(?:\\s+lang=(?:(['\"]?)(?:clojure|clj)\\4))(?:\\s+[^>]+)?)\\s*(>)",beginCaptures:{0:{name:"meta.tag.metadata.start.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{patterns:[{include:"text.html.basic#attribute"},{include:"$self"}]},5:{name:"punctuation.definition.tag.end.wikitext"}},end:"(?i)(</)(syntaxhighlight)\\s*(>)",endCaptures:{0:{name:"meta.tag.metadata.end.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{name:"punctuation.definition.tag.end.wikitext"}},patterns:[{begin:"(^|\\G)",contentName:"meta.embedded.block.clojure",end:"(?i)(?=</syntaxhighlight\\s*>)",patterns:[{include:"source.clojure"}]}]},"hl-coffee":{begin:"(?i)(<)(syntaxhighlight)((?:\\s+[^>]+)?(?:\\s+lang=(?:(['\"]?)(?:coffeescript|coffee-script|coffee)\\4))(?:\\s+[^>]+)?)\\s*(>)",beginCaptures:{0:{name:"meta.tag.metadata.start.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{patterns:[{include:"text.html.basic#attribute"},{include:"$self"}]},5:{name:"punctuation.definition.tag.end.wikitext"}},end:"(?i)(</)(syntaxhighlight)\\s*(>)",endCaptures:{0:{name:"meta.tag.metadata.end.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{name:"punctuation.definition.tag.end.wikitext"}},patterns:[{begin:"(^|\\G)",contentName:"meta.embedded.block.coffee",end:"(?i)(?=</syntaxhighlight\\s*>)",patterns:[{include:"source.coffee"}]}]},"hl-cpp":{begin:"(?i)(<)(syntaxhighlight)((?:\\s+[^>]+)?(?:\\s+lang=(?:(['\"]?)(?:cpp|c\\+\\+)\\4))(?:\\s+[^>]+)?)\\s*(>)",beginCaptures:{0:{name:"meta.tag.metadata.start.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{patterns:[{include:"text.html.basic#attribute"},{include:"$self"}]},5:{name:"punctuation.definition.tag.end.wikitext"}},end:"(?i)(</)(syntaxhighlight)\\s*(>)",endCaptures:{0:{name:"meta.tag.metadata.end.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{name:"punctuation.definition.tag.end.wikitext"}},patterns:[{begin:"(^|\\G)",contentName:"meta.embedded.block.cpp",end:"(?i)(?=</syntaxhighlight\\s*>)",patterns:[{include:"source.cpp"}]}]},"hl-csharp":{begin:"(?i)(<)(syntaxhighlight)((?:\\s+[^>]+)?(?:\\s+lang=(?:(['\"]?)(?:csharp|c#|cs)\\4))(?:\\s+[^>]+)?)\\s*(>)",beginCaptures:{0:{name:"meta.tag.metadata.start.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{patterns:[{include:"text.html.basic#attribute"},{include:"$self"}]},5:{name:"punctuation.definition.tag.end.wikitext"}},end:"(?i)(</)(syntaxhighlight)\\s*(>)",endCaptures:{0:{name:"meta.tag.metadata.end.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{name:"punctuation.definition.tag.end.wikitext"}},patterns:[{begin:"(^|\\G)",contentName:"meta.embedded.block.csharp",end:"(?i)(?=</syntaxhighlight\\s*>)",patterns:[{include:"source.cs"}]}]},"hl-css":{begin:"(?i)(<)(syntaxhighlight)((?:\\s+[^>]+)?(?:\\s+lang=(?:(['\"]?)css\\4))(?:\\s+[^>]+)?)\\s*(>)",beginCaptures:{0:{name:"meta.tag.metadata.start.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{patterns:[{include:"text.html.basic#attribute"},{include:"$self"}]},5:{name:"punctuation.definition.tag.end.wikitext"}},end:"(?i)(</)(syntaxhighlight)\\s*(>)",endCaptures:{0:{name:"meta.tag.metadata.end.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{name:"punctuation.definition.tag.end.wikitext"}},patterns:[{begin:"(^|\\G)",contentName:"meta.embedded.block.css",end:"(?i)(?=</syntaxhighlight\\s*>)",patterns:[{include:"source.css"}]}]},"hl-dart":{begin:"(?i)(<)(syntaxhighlight)((?:\\s+[^>]+)?(?:\\s+lang=(?:(['\"]?)dart\\4))(?:\\s+[^>]+)?)\\s*(>)",beginCaptures:{0:{name:"meta.tag.metadata.start.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{patterns:[{include:"text.html.basic#attribute"},{include:"$self"}]},5:{name:"punctuation.definition.tag.end.wikitext"}},end:"(?i)(</)(syntaxhighlight)\\s*(>)",endCaptures:{0:{name:"meta.tag.metadata.end.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{name:"punctuation.definition.tag.end.wikitext"}},patterns:[{begin:"(^|\\G)",contentName:"meta.embedded.block.dart",end:"(?i)(?=</syntaxhighlight\\s*>)",patterns:[{include:"source.dart"}]}]},"hl-diff":{begin:"(?i)(<)(syntaxhighlight)((?:\\s+[^>]+)?(?:\\s+lang=(?:(['\"]?)(?:diff|udiff)\\4))(?:\\s+[^>]+)?)\\s*(>)",beginCaptures:{0:{name:"meta.tag.metadata.start.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{patterns:[{include:"text.html.basic#attribute"},{include:"$self"}]},5:{name:"punctuation.definition.tag.end.wikitext"}},end:"(?i)(</)(syntaxhighlight)\\s*(>)",endCaptures:{0:{name:"meta.tag.metadata.end.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{name:"punctuation.definition.tag.end.wikitext"}},patterns:[{begin:"(^|\\G)",contentName:"meta.embedded.block.diff",end:"(?i)(?=</syntaxhighlight\\s*>)",patterns:[{include:"source.diff"}]}]},"hl-dockerfile":{begin:"(?i)(<)(syntaxhighlight)((?:\\s+[^>]+)?(?:\\s+lang=(?:(['\"]?)(?:docker|dockerfile)\\4))(?:\\s+[^>]+)?)\\s*(>)",beginCaptures:{0:{name:"meta.tag.metadata.start.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{patterns:[{include:"text.html.basic#attribute"},{include:"$self"}]},5:{name:"punctuation.definition.tag.end.wikitext"}},end:"(?i)(</)(syntaxhighlight)\\s*(>)",endCaptures:{0:{name:"meta.tag.metadata.end.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{name:"punctuation.definition.tag.end.wikitext"}},patterns:[{begin:"(^|\\G)",contentName:"meta.embedded.block.dockerfile",end:"(?i)(?=</syntaxhighlight\\s*>)",patterns:[{include:"source.dockerfile"}]}]},"hl-elixir":{begin:"(?i)(<)(syntaxhighlight)((?:\\s+[^>]+)?(?:\\s+lang=(?:(['\"]?)(?:elixir|ex|exs)\\4))(?:\\s+[^>]+)?)\\s*(>)",beginCaptures:{0:{name:"meta.tag.metadata.start.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{patterns:[{include:"text.html.basic#attribute"},{include:"$self"}]},5:{name:"punctuation.definition.tag.end.wikitext"}},end:"(?i)(</)(syntaxhighlight)\\s*(>)",endCaptures:{0:{name:"meta.tag.metadata.end.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{name:"punctuation.definition.tag.end.wikitext"}},patterns:[{begin:"(^|\\G)",contentName:"meta.embedded.block.elixir",end:"(?i)(?=</syntaxhighlight\\s*>)",patterns:[{include:"source.elixir"}]}]},"hl-erlang":{begin:"(?i)(<)(syntaxhighlight)((?:\\s+[^>]+)?(?:\\s+lang=(?:(['\"]?)erlang\\4))(?:\\s+[^>]+)?)\\s*(>)",beginCaptures:{0:{name:"meta.tag.metadata.start.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{patterns:[{include:"text.html.basic#attribute"},{include:"$self"}]},5:{name:"punctuation.definition.tag.end.wikitext"}},end:"(?i)(</)(syntaxhighlight)\\s*(>)",endCaptures:{0:{name:"meta.tag.metadata.end.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{name:"punctuation.definition.tag.end.wikitext"}},patterns:[{begin:"(^|\\G)",contentName:"meta.embedded.block.erlang",end:"(?i)(?=</syntaxhighlight\\s*>)",patterns:[{include:"source.erlang"}]}]},"hl-fsharp":{begin:"(?i)(<)(syntaxhighlight)((?:\\s+[^>]+)?(?:\\s+lang=(?:(['\"]?)(?:fsharp|f#)\\4))(?:\\s+[^>]+)?)\\s*(>)",beginCaptures:{0:{name:"meta.tag.metadata.start.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{patterns:[{include:"text.html.basic#attribute"},{include:"$self"}]},5:{name:"punctuation.definition.tag.end.wikitext"}},end:"(?i)(</)(syntaxhighlight)\\s*(>)",endCaptures:{0:{name:"meta.tag.metadata.end.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{name:"punctuation.definition.tag.end.wikitext"}},patterns:[{begin:"(^|\\G)",contentName:"meta.embedded.block.fsharp",end:"(?i)(?=</syntaxhighlight\\s*>)",patterns:[{include:"source.fsharp"}]}]},"hl-go":{begin:"(?i)(<)(syntaxhighlight)((?:\\s+[^>]+)?(?:\\s+lang=(?:(['\"]?)(?:go|golang)\\4))(?:\\s+[^>]+)?)\\s*(>)",beginCaptures:{0:{name:"meta.tag.metadata.start.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{patterns:[{include:"text.html.basic#attribute"},{include:"$self"}]},5:{name:"punctuation.definition.tag.end.wikitext"}},end:"(?i)(</)(syntaxhighlight)\\s*(>)",endCaptures:{0:{name:"meta.tag.metadata.end.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{name:"punctuation.definition.tag.end.wikitext"}},patterns:[{begin:"(^|\\G)",contentName:"meta.embedded.block.go",end:"(?i)(?=</syntaxhighlight\\s*>)",patterns:[{include:"source.go"}]}]},"hl-groovy":{begin:"(?i)(<)(syntaxhighlight)((?:\\s+[^>]+)?(?:\\s+lang=(?:(['\"]?)groovy\\4))(?:\\s+[^>]+)?)\\s*(>)",beginCaptures:{0:{name:"meta.tag.metadata.start.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{patterns:[{include:"text.html.basic#attribute"},{include:"$self"}]},5:{name:"punctuation.definition.tag.end.wikitext"}},end:"(?i)(</)(syntaxhighlight)\\s*(>)",endCaptures:{0:{name:"meta.tag.metadata.end.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{name:"punctuation.definition.tag.end.wikitext"}},patterns:[{begin:"(^|\\G)",contentName:"meta.embedded.block.groovy",end:"(?i)(?=</syntaxhighlight\\s*>)",patterns:[{include:"source.groovy"}]}]},"hl-handlebars":{begin:"(?i)(<)(syntaxhighlight)((?:\\s+[^>]+)?(?:\\s+lang=(?:(['\"]?)handlebars\\4))(?:\\s+[^>]+)?)\\s*(>)",beginCaptures:{0:{name:"meta.tag.metadata.start.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{patterns:[{include:"text.html.basic#attribute"},{include:"$self"}]},5:{name:"punctuation.definition.tag.end.wikitext"}},end:"(?i)(</)(syntaxhighlight)\\s*(>)",endCaptures:{0:{name:"meta.tag.metadata.end.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{name:"punctuation.definition.tag.end.wikitext"}},patterns:[{begin:"(^|\\G)",contentName:"meta.embedded.block.handlebars",end:"(?i)(?=</syntaxhighlight\\s*>)",patterns:[{include:"text.html.handlebars"}]}]},"hl-html":{begin:"(?i)(<)(syntaxhighlight)((?:\\s+[^>]+)?(?:\\s+lang=(?:(['\"]?)html\\4))(?:\\s+[^>]+)?)\\s*(>)",beginCaptures:{0:{name:"meta.tag.metadata.start.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{patterns:[{include:"text.html.basic#attribute"},{include:"$self"}]},5:{name:"punctuation.definition.tag.end.wikitext"}},end:"(?i)(</)(syntaxhighlight)\\s*(>)",endCaptures:{0:{name:"meta.tag.metadata.end.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{name:"punctuation.definition.tag.end.wikitext"}},patterns:[{begin:"(^|\\G)",contentName:"meta.embedded.block.html",end:"(?i)(?=</syntaxhighlight\\s*>)",patterns:[{include:"text.html.basic"}]}]},"hl-ini":{begin:"(?i)(<)(syntaxhighlight)((?:\\s+[^>]+)?(?:\\s+lang=(?:(['\"]?)(?:ini|cfg|dosini)\\4))(?:\\s+[^>]+)?)\\s*(>)",beginCaptures:{0:{name:"meta.tag.metadata.start.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{patterns:[{include:"text.html.basic#attribute"},{include:"$self"}]},5:{name:"punctuation.definition.tag.end.wikitext"}},end:"(?i)(</)(syntaxhighlight)\\s*(>)",endCaptures:{0:{name:"meta.tag.metadata.end.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{name:"punctuation.definition.tag.end.wikitext"}},patterns:[{begin:"(^|\\G)",contentName:"meta.embedded.block.ini",end:"(?i)(?=</syntaxhighlight\\s*>)",patterns:[{include:"source.ini"}]}]},"hl-java":{begin:"(?i)(<)(syntaxhighlight)((?:\\s+[^>]+)?(?:\\s+lang=(?:(['\"]?)java\\4))(?:\\s+[^>]+)?)\\s*(>)",beginCaptures:{0:{name:"meta.tag.metadata.start.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{patterns:[{include:"text.html.basic#attribute"},{include:"$self"}]},5:{name:"punctuation.definition.tag.end.wikitext"}},end:"(?i)(</)(syntaxhighlight)\\s*(>)",endCaptures:{0:{name:"meta.tag.metadata.end.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{name:"punctuation.definition.tag.end.wikitext"}},patterns:[{begin:"(^|\\G)",contentName:"meta.embedded.block.java",end:"(?i)(?=</syntaxhighlight\\s*>)",patterns:[{include:"source.java"}]}]},"hl-js":{begin:"(?i)(<)(syntaxhighlight)((?:\\s+[^>]+)?(?:\\s+lang=(?:(['\"]?)(?:javascript|js)\\4))(?:\\s+[^>]+)?)\\s*(>)",beginCaptures:{0:{name:"meta.tag.metadata.start.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{patterns:[{include:"text.html.basic#attribute"},{include:"$self"}]},5:{name:"punctuation.definition.tag.end.wikitext"}},end:"(?i)(</)(syntaxhighlight)\\s*(>)",endCaptures:{0:{name:"meta.tag.metadata.end.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{name:"punctuation.definition.tag.end.wikitext"}},patterns:[{begin:"(^|\\G)",contentName:"meta.embedded.block.js",end:"(?i)(?=</syntaxhighlight\\s*>)",patterns:[{include:"source.js"}]}]},"hl-json":{begin:"(?i)(<)(syntaxhighlight)((?:\\s+[^>]+)?(?:\\s+lang=(?:\"json\"|'json'|\"json-object\"|'json-object'))(?:\\s+[^>]+)?)\\s*(>)",beginCaptures:{0:{name:"meta.tag.metadata.start.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{patterns:[{include:"text.html.basic#attribute"},{include:"$self"}]},4:{name:"punctuation.definition.tag.end.wikitext"}},end:"(?i)(</)(syntaxhighlight)\\s*(>)",endCaptures:{0:{name:"meta.tag.metadata.end.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{name:"punctuation.definition.tag.end.wikitext"}},patterns:[{begin:"(^|\\G)",contentName:"meta.embedded.block.json",end:"(?i)(?=</syntaxhighlight\\s*>)",patterns:[{include:"source.json.comments"}]}]},"hl-julia":{begin:"(?i)(<)(syntaxhighlight)((?:\\s+[^>]+)?(?:\\s+lang=(?:\"julia\"|'julia'|\"jl\"|'jl'))(?:\\s+[^>]+)?)\\s*(>)",beginCaptures:{0:{name:"meta.tag.metadata.start.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{patterns:[{include:"text.html.basic#attribute"},{include:"$self"}]},4:{name:"punctuation.definition.tag.end.wikitext"}},end:"(?i)(</)(syntaxhighlight)\\s*(>)",endCaptures:{0:{name:"meta.tag.metadata.end.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{name:"punctuation.definition.tag.end.wikitext"}},patterns:[{begin:"(^|\\G)",contentName:"meta.embedded.block.julia",end:"(?i)(?=</syntaxhighlight\\s*>)",patterns:[{include:"source.julia"}]}]},"hl-latex":{begin:"(?i)(<)(syntaxhighlight)((?:\\s+[^>]+)?(?:\\s+lang=(?:(['\"]?)(?:tex|latex)\\4))(?:\\s+[^>]+)?)\\s*(>)",beginCaptures:{0:{name:"meta.tag.metadata.start.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{patterns:[{include:"text.html.basic#attribute"},{include:"$self"}]},5:{name:"punctuation.definition.tag.end.wikitext"}},end:"(?i)(</)(syntaxhighlight)\\s*(>)",endCaptures:{0:{name:"meta.tag.metadata.end.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{name:"punctuation.definition.tag.end.wikitext"}},patterns:[{begin:"(^|\\G)",contentName:"meta.embedded.block.latex",end:"(?i)(?=</syntaxhighlight\\s*>)",patterns:[{include:"text.tex.latex"}]}]},"hl-less":{begin:"(?i)(<)(syntaxhighlight)((?:\\s+[^>]+)?(?:\\s+lang=(?:\"less\"|'less'))(?:\\s+[^>]+)?)\\s*(>)",beginCaptures:{0:{name:"meta.tag.metadata.start.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{patterns:[{include:"text.html.basic#attribute"},{include:"$self"}]},4:{name:"punctuation.definition.tag.end.wikitext"}},end:"(?i)(</)(syntaxhighlight)\\s*(>)",endCaptures:{0:{name:"meta.tag.metadata.end.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{name:"punctuation.definition.tag.end.wikitext"}},patterns:[{begin:"(^|\\G)",contentName:"meta.embedded.block.less",end:"(?i)(?=</syntaxhighlight\\s*>)",patterns:[{include:"source.css.less"}]}]},"hl-lua":{begin:"(?i)(<)(syntaxhighlight)((?:\\s+[^>]+)?(?:\\s+lang=(?:(['\"]?)lua\\4))(?:\\s+[^>]+)?)\\s*(>)",beginCaptures:{0:{name:"meta.tag.metadata.start.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{patterns:[{include:"text.html.basic#attribute"},{include:"$self"}]},5:{name:"punctuation.definition.tag.end.wikitext"}},end:"(?i)(</)(syntaxhighlight)\\s*(>)",endCaptures:{0:{name:"meta.tag.metadata.end.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{name:"punctuation.definition.tag.end.wikitext"}},patterns:[{begin:"(^|\\G)",contentName:"meta.embedded.block.lua",end:"(?i)(?=</syntaxhighlight\\s*>)",patterns:[{include:"source.lua"}]}]},"hl-makefile":{begin:"(?i)(<)(syntaxhighlight)((?:\\s+[^>]+)?(?:\\s+lang=(?:(['\"]?)(?:make|makefile|mf|bsdmake)\\4))(?:\\s+[^>]+)?)\\s*(>)",beginCaptures:{0:{name:"meta.tag.metadata.start.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{patterns:[{include:"text.html.basic#attribute"},{include:"$self"}]},5:{name:"punctuation.definition.tag.end.wikitext"}},end:"(?i)(</)(syntaxhighlight)\\s*(>)",endCaptures:{0:{name:"meta.tag.metadata.end.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{name:"punctuation.definition.tag.end.wikitext"}},patterns:[{begin:"(^|\\G)",contentName:"meta.embedded.block.makefile",end:"(?i)(?=</syntaxhighlight\\s*>)",patterns:[{include:"source.makefile"}]}]},"hl-markdown":{begin:"(?i)(<)(syntaxhighlight)((?:\\s+[^>]+)?(?:\\s+lang=(?:(['\"]?)(?:markdown|md)\\4))(?:\\s+[^>]+)?)\\s*(>)",beginCaptures:{0:{name:"meta.tag.metadata.start.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{patterns:[{include:"text.html.basic#attribute"},{include:"$self"}]},5:{name:"punctuation.definition.tag.end.wikitext"}},end:"(?i)(</)(syntaxhighlight)\\s*(>)",endCaptures:{0:{name:"meta.tag.metadata.end.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{name:"punctuation.definition.tag.end.wikitext"}},patterns:[{begin:"(^|\\G)",contentName:"meta.embedded.block.markdown",end:"(?i)(?=</syntaxhighlight\\s*>)",patterns:[{include:"text.html.markdown"}]}]},"hl-objc":{begin:"(?i)(<)(syntaxhighlight)((?:\\s+[^>]+)?(?:\\s+lang=(?:\"objective-c\"|'objective-c'|\"objectivec\"|'objectivec'|\"obj-c\"|'obj-c'|\"objc\"|'objc'))(?:\\s+[^>]+)?)\\s*(>)",beginCaptures:{0:{name:"meta.tag.metadata.start.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{patterns:[{include:"text.html.basic#attribute"},{include:"$self"}]},4:{name:"punctuation.definition.tag.end.wikitext"}},end:"(?i)(</)(syntaxhighlight)\\s*(>)",endCaptures:{0:{name:"meta.tag.metadata.end.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{name:"punctuation.definition.tag.end.wikitext"}},patterns:[{begin:"(^|\\G)",contentName:"meta.embedded.block.objc",end:"(?i)(?=</syntaxhighlight\\s*>)",patterns:[{include:"source.objc"}]}]},"hl-perl":{begin:"(?i)(<)(syntaxhighlight)((?:\\s+[^>]+)?(?:\\s+lang=(?:(['\"]?)(?:perl|ple)\\4))(?:\\s+[^>]+)?)\\s*(>)",beginCaptures:{0:{name:"meta.tag.metadata.start.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{patterns:[{include:"text.html.basic#attribute"},{include:"$self"}]},5:{name:"punctuation.definition.tag.end.wikitext"}},end:"(?i)(</)(syntaxhighlight)\\s*(>)",endCaptures:{0:{name:"meta.tag.metadata.end.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{name:"punctuation.definition.tag.end.wikitext"}},patterns:[{begin:"(^|\\G)",contentName:"meta.embedded.block.perl",end:"(?i)(?=</syntaxhighlight\\s*>)",patterns:[{include:"source.perl"}]}]},"hl-perl6":{begin:"(?i)(<)(syntaxhighlight)((?:\\s+[^>]+)?(?:\\s+lang=(?:\"perl6\"|'perl6'|\"pl6\"|'pl6'|\"raku\"|'raku'))(?:\\s+[^>]+)?)\\s*(>)",beginCaptures:{0:{name:"meta.tag.metadata.start.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{patterns:[{include:"text.html.basic#attribute"},{include:"$self"}]},4:{name:"punctuation.definition.tag.end.wikitext"}},end:"(?i)(</)(syntaxhighlight)\\s*(>)",endCaptures:{0:{name:"meta.tag.metadata.end.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{name:"punctuation.definition.tag.end.wikitext"}},patterns:[{begin:"(^|\\G)",contentName:"meta.embedded.block.perl6",end:"(?i)(?=</syntaxhighlight\\s*>)",patterns:[{include:"source.perl.6"}]}]},"hl-php":{begin:"(?i)(<)(syntaxhighlight)((?:\\s+[^>]+)?(?:\\s+lang=(?:(['\"]?)(?:php|php3|php4|php5)\\4))(?:\\s+[^>]+)?)\\s*(>)",beginCaptures:{0:{name:"meta.tag.metadata.start.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{patterns:[{include:"text.html.basic#attribute"},{include:"$self"}]},5:{name:"punctuation.definition.tag.end.wikitext"}},end:"(?i)(</)(syntaxhighlight)\\s*(>)",endCaptures:{0:{name:"meta.tag.metadata.end.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{name:"punctuation.definition.tag.end.wikitext"}},patterns:[{begin:"(^|\\G)",contentName:"meta.embedded.block.php",end:"(?i)(?=</syntaxhighlight\\s*>)",patterns:[{include:"source.php"}]}]},"hl-powershell":{begin:"(?i)(<)(syntaxhighlight)((?:\\s+[^>]+)?(?:\\s+lang=(?:\"powershell\"|'powershell'|\"pwsh\"|'pwsh'|\"posh\"|'posh'|\"ps1\"|'ps1'|\"psm1\"|'psm1'))(?:\\s+[^>]+)?)\\s*(>)",beginCaptures:{0:{name:"meta.tag.metadata.start.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{patterns:[{include:"text.html.basic#attribute"},{include:"$self"}]},4:{name:"punctuation.definition.tag.end.wikitext"}},end:"(?i)(</)(syntaxhighlight)\\s*(>)",endCaptures:{0:{name:"meta.tag.metadata.end.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{name:"punctuation.definition.tag.end.wikitext"}},patterns:[{begin:"(^|\\G)",contentName:"meta.embedded.block.powershell",end:"(?i)(?=</syntaxhighlight\\s*>)",patterns:[{include:"source.powershell"}]}]},"hl-pug":{begin:"(?i)(<)(syntaxhighlight)((?:\\s+[^>]+)?(?:\\s+lang=(?:(['\"]?)(?:pug|jade)\\4))(?:\\s+[^>]+)?)\\s*(>)",beginCaptures:{0:{name:"meta.tag.metadata.start.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{patterns:[{include:"text.html.basic#attribute"},{include:"$self"}]},5:{name:"punctuation.definition.tag.end.wikitext"}},end:"(?i)(</)(syntaxhighlight)\\s*(>)",endCaptures:{0:{name:"meta.tag.metadata.end.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{name:"punctuation.definition.tag.end.wikitext"}},patterns:[{begin:"(^|\\G)",contentName:"meta.embedded.block.pug",end:"(?i)(?=</syntaxhighlight\\s*>)",patterns:[{include:"text.pug"}]}]},"hl-python":{begin:"(?i)(<)(syntaxhighlight)((?:\\s+[^>]+)?(?:\\s+lang=(?:\"python\"|'python'|\"py\"|'py'|\"sage\"|'sage'|\"python3\"|'python3'|\"py3\"|'py3'))(?:\\s+[^>]+)?)\\s*(>)",beginCaptures:{0:{name:"meta.tag.metadata.start.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{patterns:[{include:"text.html.basic#attribute"},{include:"$self"}]},4:{name:"punctuation.definition.tag.end.wikitext"}},end:"(?i)(</)(syntaxhighlight)\\s*(>)",endCaptures:{0:{name:"meta.tag.metadata.end.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{name:"punctuation.definition.tag.end.wikitext"}},patterns:[{begin:"(^|\\G)",contentName:"meta.embedded.block.python",end:"(?i)(?=</syntaxhighlight\\s*>)",patterns:[{include:"source.python"}]}]},"hl-r":{begin:"(?i)(<)(syntaxhighlight)((?:\\s+[^>]+)?(?:\\s+lang=(?:(['\"]?)(?:splus|s|r)\\4))(?:\\s+[^>]+)?)\\s*(>)",beginCaptures:{0:{name:"meta.tag.metadata.start.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{patterns:[{include:"text.html.basic#attribute"},{include:"$self"}]},5:{name:"punctuation.definition.tag.end.wikitext"}},end:"(?i)(</)(syntaxhighlight)\\s*(>)",endCaptures:{0:{name:"meta.tag.metadata.end.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{name:"punctuation.definition.tag.end.wikitext"}},patterns:[{begin:"(^|\\G)",contentName:"meta.embedded.block.r",end:"(?i)(?=</syntaxhighlight\\s*>)",patterns:[{include:"source.r"}]}]},"hl-ruby":{begin:"(?i)(<)(syntaxhighlight)((?:\\s+[^>]+)?(?:\\s+lang=(?:(['\"]?)(?:ruby|rb|duby)\\4))(?:\\s+[^>]+)?)\\s*(>)",beginCaptures:{0:{name:"meta.tag.metadata.start.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{patterns:[{include:"text.html.basic#attribute"},{include:"$self"}]},5:{name:"punctuation.definition.tag.end.wikitext"}},end:"(?i)(</)(syntaxhighlight)\\s*(>)",endCaptures:{0:{name:"meta.tag.metadata.end.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{name:"punctuation.definition.tag.end.wikitext"}},patterns:[{begin:"(^|\\G)",contentName:"meta.embedded.block.ruby",end:"(?i)(?=</syntaxhighlight\\s*>)",patterns:[{include:"source.ruby"}]}]},"hl-rust":{begin:"(?i)(<)(syntaxhighlight)((?:\\s+[^>]+)?(?:\\s+lang=(?:\"rust\"|'rust'|\"rs\"|'rs'))(?:\\s+[^>]+)?)\\s*(>)",beginCaptures:{0:{name:"meta.tag.metadata.start.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{patterns:[{include:"text.html.basic#attribute"},{include:"$self"}]},4:{name:"punctuation.definition.tag.end.wikitext"}},end:"(?i)(</)(syntaxhighlight)\\s*(>)",endCaptures:{0:{name:"meta.tag.metadata.end.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{name:"punctuation.definition.tag.end.wikitext"}},patterns:[{begin:"(^|\\G)",contentName:null,end:"(?i)(?=</syntaxhighlight\\s*>)",patterns:[{include:"source.rust"}]}]},"hl-scala":{begin:"(?i)(<)(syntaxhighlight)((?:\\s+[^>]+)?(?:\\s+lang=(?:\"scala\"|'scala'))(?:\\s+[^>]+)?)\\s*(>)",beginCaptures:{0:{name:"meta.tag.metadata.start.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{patterns:[{include:"text.html.basic#attribute"},{include:"$self"}]},4:{name:"punctuation.definition.tag.end.wikitext"}},end:"(?i)(</)(syntaxhighlight)\\s*(>)",endCaptures:{0:{name:"meta.tag.metadata.end.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{name:"punctuation.definition.tag.end.wikitext"}},patterns:[{begin:"(^|\\G)",contentName:"meta.embedded.block.scala",end:"(?i)(?=</syntaxhighlight\\s*>)",patterns:[{include:"source.scala"}]}]},"hl-scss":{begin:"(?i)(<)(syntaxhighlight)((?:\\s+[^>]+)?(?:\\s+lang=(?:\"scss\"|'scss'))(?:\\s+[^>]+)?)\\s*(>)",beginCaptures:{0:{name:"meta.tag.metadata.start.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{patterns:[{include:"text.html.basic#attribute"},{include:"$self"}]},4:{name:"punctuation.definition.tag.end.wikitext"}},end:"(?i)(</)(syntaxhighlight)\\s*(>)",endCaptures:{0:{name:"meta.tag.metadata.end.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{name:"punctuation.definition.tag.end.wikitext"}},patterns:[{begin:"(^|\\G)",contentName:"meta.embedded.block.scss",end:"(?i)(?=</syntaxhighlight\\s*>)",patterns:[{include:"source.css.scss"}]}]},"hl-shell":{begin:"(?i)(<)(syntaxhighlight)((?:\\s+[^>]+)?(?:\\s+lang=(?:\"bash\"|'bash'|\"sh\"|'sh'|\"ksh\"|'ksh'|\"zsh\"|'zsh'|\"shell\"|'shell'))(?:\\s+[^>]+)?)\\s*(>)",beginCaptures:{0:{name:"meta.tag.metadata.start.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{patterns:[{include:"text.html.basic#attribute"},{include:"$self"}]},4:{name:"punctuation.definition.tag.end.wikitext"}},end:"(?i)(</)(syntaxhighlight)\\s*(>)",endCaptures:{0:{name:"meta.tag.metadata.end.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{name:"punctuation.definition.tag.end.wikitext"}},patterns:[{begin:"(^|\\G)",contentName:"meta.embedded.block.shell",end:"(?i)(?=</syntaxhighlight\\s*>)",patterns:[{include:"source.shell"}]}]},"hl-sql":{begin:"(?i)(<)(syntaxhighlight)((?:\\s+[^>]+)?(?:\\s+lang=(?:(['\"]?)sql\\4))(?:\\s+[^>]+)?)\\s*(>)",beginCaptures:{0:{name:"meta.tag.metadata.start.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{patterns:[{include:"text.html.basic#attribute"},{include:"$self"}]},5:{name:"punctuation.definition.tag.end.wikitext"}},end:"(?i)(</)(syntaxhighlight)\\s*(>)",endCaptures:{0:{name:"meta.tag.metadata.end.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{name:"punctuation.definition.tag.end.wikitext"}},patterns:[{begin:"(^|\\G)",contentName:"meta.embedded.block.sql",end:"(?i)(?=</syntaxhighlight\\s*>)",patterns:[{include:"source.sql"}]}]},"hl-swift":{begin:"(?i)(<)(syntaxhighlight)((?:\\s+[^>]+)?(?:\\s+lang=(?:\"swift\"|'swift'))(?:\\s+[^>]+)?)\\s*(>)",beginCaptures:{0:{name:"meta.tag.metadata.start.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{patterns:[{include:"text.html.basic#attribute"},{include:"$self"}]},4:{name:"punctuation.definition.tag.end.wikitext"}},end:"(?i)(</)(syntaxhighlight)\\s*(>)",endCaptures:{0:{name:"meta.tag.metadata.end.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{name:"punctuation.definition.tag.end.wikitext"}},patterns:[{begin:"(^|\\G)",contentName:"meta.embedded.block.swift",end:"(?i)(?=</syntaxhighlight\\s*>)",patterns:[{include:"source.swift"}]}]},"hl-ts":{begin:"(?i)(<)(syntaxhighlight)((?:\\s+[^>]+)?(?:\\s+lang=(?:\"typescript\"|'typescript'|\"ts\"|'ts'))(?:\\s+[^>]+)?)\\s*(>)",beginCaptures:{0:{name:"meta.tag.metadata.start.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{patterns:[{include:"text.html.basic#attribute"},{include:"$self"}]},4:{name:"punctuation.definition.tag.end.wikitext"}},end:"(?i)(</)(syntaxhighlight)\\s*(>)",endCaptures:{0:{name:"meta.tag.metadata.end.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{name:"punctuation.definition.tag.end.wikitext"}},patterns:[{begin:"(^|\\G)",contentName:"meta.embedded.block.ts",end:"(?i)(?=</syntaxhighlight\\s*>)",patterns:[{include:"source.ts"}]}]},"hl-vb-net":{begin:"(?i)(<)(syntaxhighlight)((?:\\s+[^>]+)?(?:\\s+lang=(?:(['\"]?)(?:vb\\.net|vbnet|lobas|oobas|sobas)\\4))(?:\\s+[^>]+)?)\\s*(>)",beginCaptures:{0:{name:"meta.tag.metadata.start.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{patterns:[{include:"text.html.basic#attribute"},{include:"$self"}]},5:{name:"punctuation.definition.tag.end.wikitext"}},end:"(?i)(</)(syntaxhighlight)\\s*(>)",endCaptures:{0:{name:"meta.tag.metadata.end.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{name:"punctuation.definition.tag.end.wikitext"}},patterns:[{begin:"(^|\\G)",contentName:"meta.embedded.block.vb-net",end:"(?i)(?=</syntaxhighlight\\s*>)",patterns:[{include:"source.asp.vb.net"}]}]},"hl-xml":{begin:"(?i)(<)(syntaxhighlight)((?:\\s+[^>]+)?(?:\\s+lang=(?:(['\"]?)xml\\4))(?:\\s+[^>]+)?)\\s*(>)",beginCaptures:{0:{name:"meta.tag.metadata.start.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{patterns:[{include:"text.html.basic#attribute"},{include:"$self"}]},5:{name:"punctuation.definition.tag.end.wikitext"}},end:"(?i)(</)(syntaxhighlight)\\s*(>)",endCaptures:{0:{name:"meta.tag.metadata.end.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{name:"punctuation.definition.tag.end.wikitext"}},patterns:[{begin:"(^|\\G)",contentName:"meta.embedded.block.xml",end:"(?i)(?=</syntaxhighlight\\s*>)",patterns:[{include:"text.xml"}]}]},"hl-xslt":{begin:"(?i)(<)(syntaxhighlight)((?:\\s+[^>]+)?(?:\\s+lang=(?:(['\"]?)xslt\\4))(?:\\s+[^>]+)?)\\s*(>)",beginCaptures:{0:{name:"meta.tag.metadata.start.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{patterns:[{include:"text.html.basic#attribute"},{include:"$self"}]},5:{name:"punctuation.definition.tag.end.wikitext"}},end:"(?i)(</)(syntaxhighlight)\\s*(>)",endCaptures:{0:{name:"meta.tag.metadata.end.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{name:"punctuation.definition.tag.end.wikitext"}},patterns:[{begin:"(^|\\G)",contentName:"meta.embedded.block.xslt",end:"(?i)(?=</syntaxhighlight\\s*>)",patterns:[{include:"text.xml.xsl"}]}]},"hl-yaml":{begin:"(?i)(<)(syntaxhighlight)((?:\\s+[^>]+)?(?:\\s+lang=(?:(['\"]?)yaml\\4))(?:\\s+[^>]+)?)\\s*(>)",beginCaptures:{0:{name:"meta.tag.metadata.start.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{patterns:[{include:"text.html.basic#attribute"},{include:"$self"}]},5:{name:"punctuation.definition.tag.end.wikitext"}},end:"(?i)(</)(syntaxhighlight)\\s*(>)",endCaptures:{0:{name:"meta.tag.metadata.end.wikitext"},1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{name:"punctuation.definition.tag.end.wikitext"}},patterns:[{begin:"(^|\\G)",contentName:"meta.embedded.block.yaml",end:"(?i)(?=</syntaxhighlight\\s*>)",patterns:[{include:"source.yaml"}]}]}}},"wiki-self-closed-tags":{captures:{1:{name:"punctuation.definition.tag.begin.wikitext"},2:{name:"entity.name.tag.wikitext"},3:{patterns:[{include:"text.html.basic#attribute"},{include:"$self"}]},4:{name:"punctuation.definition.tag.end.wikitext"}},match:"(?i)(<)(templatestyles|ref|nowiki|onlyinclude|includeonly)(\\s+[^>]+)?\\s*(/>)",name:"meta.tag.metedata.void.wikitext"}}}}}},scopeName:"source.wikitext",embeddedLangs:["html","css","ini","java","lua","make","perl","r","ruby","php","sql","vb","xml","xsl","yaml","bat","clojure","coffee","c","cpp","diff","docker","go","groovy","pug","javascript","jsonc","less","objective-c","swift","scss","raku","powershell","python","julia","rust","scala","shellscript","typescript","csharp","fsharp","dart","handlebars","markdown","erlang","elixir","latex","bibtex","json"],aliases:["mediawiki","wiki"]});var J=[...a.default,...i.default,...r.default,...s.default,...o.default,...m.default,...c.default,...l.default,...d.default,...u.default,...p.default,...b.default,...g.default,...h.default,...f.default,...w.default,...x.default,...k.default,...y.default,...v.default,...C.default,..._.default,...A.default,...E.default,...$.default,...q.default,...N.default,...S.default,...j.default,...L.default,...z.default,...T.default,...R.default,...P.default,...O.default,...Z.default,...I.default,...D.default,...M.default,...G.default,...F.default,...U.default,...H.default,...B.default,...V.default,...W.default,...K.default,...Y.default,...X.default,Q]},25846:(e,t,n)=>{n.r(t),n.d(t,{default:()=>a});var a=[Object.freeze({displayName:"INI",name:"ini",patterns:[{begin:"(^[ \\t]+)?(?=#)",beginCaptures:{1:{name:"punctuation.whitespace.comment.leading.ini"}},end:"(?!\\G)",patterns:[{begin:"#",beginCaptures:{0:{name:"punctuation.definition.comment.ini"}},end:"\\n",name:"comment.line.number-sign.ini"}]},{begin:"(^[ \\t]+)?(?=;)",beginCaptures:{1:{name:"punctuation.whitespace.comment.leading.ini"}},end:"(?!\\G)",patterns:[{begin:";",beginCaptures:{0:{name:"punctuation.definition.comment.ini"}},end:"\\n",name:"comment.line.semicolon.ini"}]},{captures:{1:{name:"keyword.other.definition.ini"},2:{name:"punctuation.separator.key-value.ini"}},match:"\\b([a-zA-Z0-9_.-]+)\\b\\s*(=)"},{captures:{1:{name:"punctuation.definition.entity.ini"},3:{name:"punctuation.definition.entity.ini"}},match:"^(\\[)(.*?)(\\])",name:"entity.name.section.group-title.ini"},{begin:"'",beginCaptures:{0:{name:"punctuation.definition.string.begin.ini"}},end:"'",endCaptures:{0:{name:"punctuation.definition.string.end.ini"}},name:"string.quoted.single.ini",patterns:[{match:"\\\\.",name:"constant.character.escape.ini"}]},{begin:'"',beginCaptures:{0:{name:"punctuation.definition.string.begin.ini"}},end:'"',endCaptures:{0:{name:"punctuation.definition.string.end.ini"}},name:"string.quoted.double.ini"}],scopeName:"source.ini",aliases:["properties"]})]},26831:(e,t,n)=>{n.r(t),n.d(t,{default:()=>a});var a=[Object.freeze({displayName:"Raku",name:"raku",patterns:[{begin:"^=begin",end:"^=end",name:"comment.block.perl"},{begin:"(^[ \\t]+)?(?=#)",beginCaptures:{1:{name:"punctuation.whitespace.comment.leading.perl"}},end:"(?!\\G)",patterns:[{begin:"#",beginCaptures:{0:{name:"punctuation.definition.comment.perl"}},end:"\\n",name:"comment.line.number-sign.perl"}]},{captures:{1:{name:"storage.type.class.perl.6"},3:{name:"entity.name.type.class.perl.6"}},match:"(class|enum|grammar|knowhow|module|package|role|slang|subset)(\\s+)(((?:::|')?(?:([a-zA-Z_\\x{C0}-\\x{FF}$])([a-zA-Z0-9_\\x{C0}-\\x{FF}\\\\$]|[\\-'][a-zA-Z0-9_\\x{C0}-\\x{FF}$])*))+)",name:"meta.class.perl.6"},{begin:"(?<=\\s)'",beginCaptures:{0:{name:"punctuation.definition.string.begin.perl"}},end:"'",endCaptures:{0:{name:"punctuation.definition.string.end.perl"}},name:"string.quoted.single.perl",patterns:[{match:"\\\\['\\\\]",name:"constant.character.escape.perl"}]},{begin:'"',beginCaptures:{0:{name:"punctuation.definition.string.begin.perl"}},end:'"',endCaptures:{0:{name:"punctuation.definition.string.end.perl"}},name:"string.quoted.double.perl",patterns:[{match:'\\\\[abtnfre"\\\\]',name:"constant.character.escape.perl"}]},{begin:"q(q|to|heredoc)*\\s*:?(q|to|heredoc)*\\s*/(.+)/",end:"\\3",name:"string.quoted.single.heredoc.perl"},{begin:"(q|Q)(x|exec|w|words|ww|quotewords|v|val|q|single|qq|double|s|scalar|a|array|h|hash|f|function|c|closure|b|blackslash|regexp|substr|trans|codes|p|path)*\\s*:?(x|exec|w|words|ww|quotewords|v|val|q|single|qq|double|s|scalar|a|array|h|hash|f|function|c|closure|b|blackslash|regexp|substr|trans|codes|p|path)*\\s*{{",end:"}}",name:"string.quoted.double.heredoc.brace.perl",patterns:[{include:"#qq_brace_string_content"}]},{begin:"(q|Q)(x|exec|w|words|ww|quotewords|v|val|q|single|qq|double|s|scalar|a|array|h|hash|f|function|c|closure|b|blackslash|regexp|substr|trans|codes|p|path)*\\s*:?(x|exec|w|words|ww|quotewords|v|val|q|single|qq|double|s|scalar|a|array|h|hash|f|function|c|closure|b|blackslash|regexp|substr|trans|codes|p|path)*\\s*\\(\\(",end:"\\)\\)",name:"string.quoted.double.heredoc.paren.perl",patterns:[{include:"#qq_paren_string_content"}]},{begin:"(q|Q)(x|exec|w|words|ww|quotewords|v|val|q|single|qq|double|s|scalar|a|array|h|hash|f|function|c|closure|b|blackslash|regexp|substr|trans|codes|p|path)*\\s*:?(x|exec|w|words|ww|quotewords|v|val|q|single|qq|double|s|scalar|a|array|h|hash|f|function|c|closure|b|blackslash|regexp|substr|trans|codes|p|path)*\\s*\\[\\[",end:"\\]\\]",name:"string.quoted.double.heredoc.bracket.perl",patterns:[{include:"#qq_bracket_string_content"}]},{begin:"(q|Q)(x|exec|w|words|ww|quotewords|v|val|q|single|qq|double|s|scalar|a|array|h|hash|f|function|c|closure|b|blackslash|regexp|substr|trans|codes|p|path)*\\s*:?(x|exec|w|words|ww|quotewords|v|val|q|single|qq|double|s|scalar|a|array|h|hash|f|function|c|closure|b|blackslash|regexp|substr|trans|codes|p|path)*\\s*{",end:"}",name:"string.quoted.single.heredoc.brace.perl",patterns:[{include:"#qq_brace_string_content"}]},{begin:"(q|Q)(x|exec|w|words|ww|quotewords|v|val|q|single|qq|double|s|scalar|a|array|h|hash|f|function|c|closure|b|blackslash|regexp|substr|trans|codes|p|path)*\\s*:?(x|exec|w|words|ww|quotewords|v|val|q|single|qq|double|s|scalar|a|array|h|hash|f|function|c|closure|b|blackslash|regexp|substr|trans|codes|p|path)*\\s*/",end:"/",name:"string.quoted.single.heredoc.slash.perl",patterns:[{include:"#qq_slash_string_content"}]},{begin:"(q|Q)(x|exec|w|words|ww|quotewords|v|val|q|single|qq|double|s|scalar|a|array|h|hash|f|function|c|closure|b|blackslash|regexp|substr|trans|codes|p|path)*\\s*:?(x|exec|w|words|ww|quotewords|v|val|q|single|qq|double|s|scalar|a|array|h|hash|f|function|c|closure|b|blackslash|regexp|substr|trans|codes|p|path)*\\s*\\(",end:"\\)",name:"string.quoted.single.heredoc.paren.perl",patterns:[{include:"#qq_paren_string_content"}]},{begin:"(q|Q)(x|exec|w|words|ww|quotewords|v|val|q|single|qq|double|s|scalar|a|array|h|hash|f|function|c|closure|b|blackslash|regexp|substr|trans|codes|p|path)*\\s*:?(x|exec|w|words|ww|quotewords|v|val|q|single|qq|double|s|scalar|a|array|h|hash|f|function|c|closure|b|blackslash|regexp|substr|trans|codes|p|path)*\\s*\\[",end:"\\]",name:"string.quoted.single.heredoc.bracket.perl",patterns:[{include:"#qq_bracket_string_content"}]},{begin:"(q|Q)(x|exec|w|words|ww|quotewords|v|val|q|single|qq|double|s|scalar|a|array|h|hash|f|function|c|closure|b|blackslash|regexp|substr|trans|codes|p|path)*\\s*:?(x|exec|w|words|ww|quotewords|v|val|q|single|qq|double|s|scalar|a|array|h|hash|f|function|c|closure|b|blackslash|regexp|substr|trans|codes|p|path)*\\s*'",end:"'",name:"string.quoted.single.heredoc.single.perl",patterns:[{include:"#qq_single_string_content"}]},{begin:'(q|Q)(x|exec|w|words|ww|quotewords|v|val|q|single|qq|double|s|scalar|a|array|h|hash|f|function|c|closure|b|blackslash|regexp|substr|trans|codes|p|path)*\\s*:?(x|exec|w|words|ww|quotewords|v|val|q|single|qq|double|s|scalar|a|array|h|hash|f|function|c|closure|b|blackslash|regexp|substr|trans|codes|p|path)*\\s*"',end:'"',name:"string.quoted.single.heredoc.double.perl",patterns:[{include:"#qq_double_string_content"}]},{match:"\\b\\$\\w+\\b",name:"variable.other.perl"},{match:"\\b(macro|sub|submethod|method|multi|proto|only|rule|token|regex|category)\\b",name:"storage.type.declare.routine.perl"},{match:"\\b(self)\\b",name:"variable.language.perl"},{match:"\\b(use|require)\\b",name:"keyword.other.include.perl"},{match:"\\b(if|else|elsif|unless)\\b",name:"keyword.control.conditional.perl"},{match:"\\b(let|my|our|state|temp|has|constant)\\b",name:"storage.type.variable.perl"},{match:"\\b(for|loop|repeat|while|until|gather|given)\\b",name:"keyword.control.repeat.perl"},{match:"\\b(take|do|when|next|last|redo|return|contend|maybe|defer|default|exit|make|continue|break|goto|leave|async|lift)\\b",name:"keyword.control.flowcontrol.perl"},{match:"\\b(is|as|but|trusts|of|returns|handles|where|augment|supersede)\\b",name:"storage.modifier.type.constraints.perl"},{match:"\\b(BEGIN|CHECK|INIT|START|FIRST|ENTER|LEAVE|KEEP|UNDO|NEXT|LAST|PRE|POST|END|CATCH|CONTROL|TEMP)\\b",name:"meta.function.perl"},{match:"\\b(die|fail|try|warn)\\b",name:"keyword.control.control-handlers.perl"},{match:"\\b(prec|irs|ofs|ors|export|deep|binary|unary|reparsed|rw|parsed|cached|readonly|defequiv|will|ref|copy|inline|tighter|looser|equiv|assoc|required)\\b",name:"storage.modifier.perl"},{match:"\\b(NaN|Inf)\\b",name:"constant.numeric.perl"},{match:"\\b(oo|fatal)\\b",name:"keyword.other.pragma.perl"},{match:"\\b(Object|Any|Junction|Whatever|Capture|MatchSignature|Proxy|Matcher|Package|Module|ClassGrammar|Scalar|Array|Hash|KeyHash|KeySet|KeyBagPair|List|Seq|Range|Set|Bag|Mapping|Void|UndefFailure|Exception|Code|Block|Routine|Sub|MacroMethod|Submethod|Regex|Str|str|Blob|Char|ByteCodepoint|Grapheme|StrPos|StrLen|Version|NumComplex|num|complex|Bit|bit|bool|True|FalseIncreasing|Decreasing|Ordered|Callable|AnyCharPositional|Associative|Ordering|KeyExtractorComparator|OrderingPair|IO|KitchenSink|RoleInt|int|int1|int2|int4|int8|int16|int32|int64Rat|rat|rat1|rat2|rat4|rat8|rat16|rat32|rat64Buf|buf|buf1|buf2|buf4|buf8|buf16|buf32|buf64UInt|uint|uint1|uint2|uint4|uint8|uint16|uint32uint64|Abstraction|utf8|utf16|utf32)\\b",name:"support.type.perl6"},{match:"\\b(div|xx|x|mod|also|leg|cmp|before|after|eq|ne|le|lt|not|gt|ge|eqv|ff|fff|and|andthen|or|xor|orelse|extra|lcm|gcd)\\b",name:"keyword.operator.perl"},{match:"(\\$|@|%|&)(\\*|:|!|\\^|~|=|\\?|(<(?=.+>)))?([a-zA-Z_\\x{C0}-\\x{FF}$])([a-zA-Z0-9_\\x{C0}-\\x{FF}$]|[\\-'][a-zA-Z0-9_\\x{C0}-\\x{FF}$])*",name:"variable.other.identifier.perl.6"},{match:"\\b(eager|hyper|substr|index|rindex|grep|map|sort|join|lines|hints|chmod|split|reduce|min|max|reverse|truncate|zip|cat|roundrobin|classify|first|sum|keys|values|pairs|defined|delete|exists|elems|end|kv|any|all|one|wrap|shape|key|value|name|pop|push|shift|splice|unshift|floor|ceiling|abs|exp|log|log10|rand|sign|sqrt|sin|cos|tan|round|strand|roots|cis|unpolar|polar|atan2|pick|chop|p5chop|chomp|p5chomp|lc|lcfirst|uc|ucfirst|capitalize|normalize|pack|unpack|quotemeta|comb|samecase|sameaccent|chars|nfd|nfc|nfkd|nfkc|printf|sprintf|caller|evalfile|run|runinstead|nothing|want|bless|chr|ord|gmtime|time|eof|localtime|gethost|getpw|chroot|getlogin|getpeername|kill|fork|wait|perl|graphs|codes|bytes|clone|print|open|read|write|readline|say|seek|close|opendir|readdir|slurp|spurt|shell|run|pos|fmt|vec|link|unlink|symlink|uniq|pair|asin|atan|sec|cosec|cotan|asec|acosec|acotan|sinh|cosh|tanh|asinh|done|acos|acosh|atanh|sech|cosech|cotanh|sech|acosech|acotanh|asech|ok|nok|plan_ok|dies_ok|lives_ok|skip|todo|pass|flunk|force_todo|use_ok|isa_ok|diag|is_deeply|isnt|like|skip_rest|unlike|cmp_ok|eval_dies_ok|nok_error|eval_lives_ok|approx|is_approx|throws_ok|version_lt|plan|EVAL|succ|pred|times|nonce|once|signature|new|connect|operator|undef|undefine|sleep|from|to|infix|postfix|prefix|circumfix|postcircumfix|minmax|lazy|count|unwrap|getc|pi|e|context|void|quasi|body|each|contains|rewinddir|subst|can|isa|flush|arity|assuming|rewind|callwith|callsame|nextwith|nextsame|attr|eval_elsewhere|none|srand|trim|trim_start|trim_end|lastcall|WHAT|WHERE|HOW|WHICH|VAR|WHO|WHENCE|ACCEPTS|REJECTS|not|true|iterator|by|re|im|invert|flip|gist|flat|tree|is-prime|throws_like|trans)\\b",name:"support.function.perl"}],repository:{qq_brace_string_content:{begin:"{",end:"}",patterns:[{include:"#qq_brace_string_content"}]},qq_bracket_string_content:{begin:"\\[",end:"\\]",patterns:[{include:"#qq_bracket_string_content"}]},qq_double_string_content:{begin:'"',end:'"',patterns:[{include:"#qq_double_string_content"}]},qq_paren_string_content:{begin:"\\(",end:"\\)",patterns:[{include:"#qq_paren_string_content"}]},qq_single_string_content:{begin:"'",end:"'",patterns:[{include:"#qq_single_string_content"}]},qq_slash_string_content:{begin:"\\\\/",end:"\\\\/",patterns:[{include:"#qq_slash_string_content"}]}},scopeName:"source.perl.6",aliases:["perl6"]})]},29809:(e,t,n)=>{n.r(t),n.d(t,{default:()=>a});var a=[Object.freeze({displayName:"JSON with Comments",name:"jsonc",patterns:[{include:"#value"}],repository:{array:{begin:"\\[",beginCaptures:{0:{name:"punctuation.definition.array.begin.json.comments"}},end:"\\]",endCaptures:{0:{name:"punctuation.definition.array.end.json.comments"}},name:"meta.structure.array.json.comments",patterns:[{include:"#value"},{match:",",name:"punctuation.separator.array.json.comments"},{match:"[^\\s\\]]",name:"invalid.illegal.expected-array-separator.json.comments"}]},comments:{patterns:[{begin:"/\\*\\*(?!/)",captures:{0:{name:"punctuation.definition.comment.json.comments"}},end:"\\*/",name:"comment.block.documentation.json.comments"},{begin:"/\\*",captures:{0:{name:"punctuation.definition.comment.json.comments"}},end:"\\*/",name:"comment.block.json.comments"},{captures:{1:{name:"punctuation.definition.comment.json.comments"}},match:"(//).*$\\n?",name:"comment.line.double-slash.js"}]},constant:{match:"\\b(?:true|false|null)\\b",name:"constant.language.json.comments"},number:{match:"-?(?:0|[1-9]\\d*)(?:(?:\\.\\d+)?(?:[eE][+-]?\\d+)?)?",name:"constant.numeric.json.comments"},object:{begin:"\\{",beginCaptures:{0:{name:"punctuation.definition.dictionary.begin.json.comments"}},end:"\\}",endCaptures:{0:{name:"punctuation.definition.dictionary.end.json.comments"}},name:"meta.structure.dictionary.json.comments",patterns:[{comment:"the JSON object key",include:"#objectkey"},{include:"#comments"},{begin:":",beginCaptures:{0:{name:"punctuation.separator.dictionary.key-value.json.comments"}},end:"(,)|(?=\\})",endCaptures:{1:{name:"punctuation.separator.dictionary.pair.json.comments"}},name:"meta.structure.dictionary.value.json.comments",patterns:[{comment:"the JSON object value",include:"#value"},{match:"[^\\s,]",name:"invalid.illegal.expected-dictionary-separator.json.comments"}]},{match:"[^\\s}]",name:"invalid.illegal.expected-dictionary-separator.json.comments"}]},objectkey:{begin:'"',beginCaptures:{0:{name:"punctuation.support.type.property-name.begin.json.comments"}},end:'"',endCaptures:{0:{name:"punctuation.support.type.property-name.end.json.comments"}},name:"string.json.comments support.type.property-name.json.comments",patterns:[{include:"#stringcontent"}]},string:{begin:'"',beginCaptures:{0:{name:"punctuation.definition.string.begin.json.comments"}},end:'"',endCaptures:{0:{name:"punctuation.definition.string.end.json.comments"}},name:"string.quoted.double.json.comments",patterns:[{include:"#stringcontent"}]},stringcontent:{patterns:[{match:'\\\\(?:["\\\\/bfnrt]|u[0-9a-fA-F]{4})',name:"constant.character.escape.json.comments"},{match:"\\\\.",name:"invalid.illegal.unrecognized-string-escape.json.comments"}]},value:{patterns:[{include:"#constant"},{include:"#number"},{include:"#string"},{include:"#array"},{include:"#object"},{include:"#comments"}]}},scopeName:"source.json.comments"})]},34184:(e,t,n)=>{n.r(t),n.d(t,{default:()=>a});var a=[Object.freeze({displayName:"Visual Basic",name:"vb",patterns:[{match:"\\n",name:"meta.ending-space"},{include:"#round-brackets"},{begin:"^(?=\\t)",end:"(?=[^\\t])",name:"meta.leading-space",patterns:[{captures:{1:{name:"meta.odd-tab.tabs"},2:{name:"meta.even-tab.tabs"}},match:"(\\t)(\\t)?"}]},{begin:"^(?= )",end:"(?=[^ ])",name:"meta.leading-space",patterns:[{captures:{1:{name:"meta.odd-tab.spaces"},2:{name:"meta.even-tab.spaces"}},match:"(  )(  )?"}]},{captures:{1:{name:"storage.type.function.asp"},2:{name:"entity.name.function.asp"},3:{name:"punctuation.definition.parameters.asp"},4:{name:"variable.parameter.function.asp"},5:{name:"punctuation.definition.parameters.asp"}},match:"^\\s*((?i:function|sub))\\s*([a-zA-Z_]\\w*)\\s*(\\()([^)]*)(\\)).*\\n?",name:"meta.function.asp"},{begin:"(^[ \\t]+)?(?=')",beginCaptures:{1:{name:"punctuation.whitespace.comment.leading.asp"}},end:"(?!\\G)",patterns:[{begin:"'",beginCaptures:{0:{name:"punctuation.definition.comment.asp"}},end:"\\n",name:"comment.line.apostrophe.asp"}]},{match:"(?i:\\b(If|Then|Else|ElseIf|Else If|End If|While|Wend|For|To|Each|Case|Select|End Select|Return|Continue|Do|Until|Loop|Next|With|Exit Do|Exit For|Exit Function|Exit Property|Exit Sub|IIf)\\b)",name:"keyword.control.asp"},{match:"(?i:\\b(Mod|And|Not|Or|Xor|as)\\b)",name:"keyword.operator.asp"},{captures:{1:{name:"storage.type.asp"},2:{name:"variable.other.bfeac.asp"},3:{name:"meta.separator.comma.asp"}},match:"(?i:(dim)\\s*(?:(\\b[a-zA-Z_x7f-xff][a-zA-Z0-9_x7f-xff]*?\\b)\\s*(,?)))",name:"variable.other.dim.asp"},{match:"(?i:\\s*\\b(Call|Class|Const|Dim|Redim|Function|Sub|Private Sub|Public Sub|End Sub|End Function|End Class|End Property|Public Property|Private Property|Set|Let|Get|New|Randomize|Option Explicit|On Error Resume Next|On Error GoTo)\\b\\s*)",name:"storage.type.asp"},{match:"(?i:\\b(Private|Public|Default)\\b)",name:"storage.modifier.asp"},{match:"(?i:\\s*\\b(Empty|False|Nothing|Null|True)\\b)",name:"constant.language.asp"},{begin:'"',beginCaptures:{0:{name:"punctuation.definition.string.begin.asp"}},end:'"',endCaptures:{0:{name:"punctuation.definition.string.end.asp"}},name:"string.quoted.double.asp",patterns:[{match:'""',name:"constant.character.escape.apostrophe.asp"}]},{captures:{1:{name:"punctuation.definition.variable.asp"}},match:"(\\$)[a-zA-Z_x7f-xff][a-zA-Z0-9_x7f-xff]*?\\b\\s*",name:"variable.other.asp"},{match:"(?i:\\b(Application|ObjectContext|Request|Response|Server|Session)\\b)",name:"support.class.asp"},{match:"(?i:\\b(Contents|StaticObjects|ClientCertificate|Cookies|Form|QueryString|ServerVariables)\\b)",name:"support.class.collection.asp"},{match:"(?i:\\b(TotalBytes|Buffer|CacheControl|Charset|ContentType|Expires|ExpiresAbsolute|IsClientConnected|PICS|Status|ScriptTimeout|CodePage|LCID|SessionID|Timeout)\\b)",name:"support.constant.asp"},{match:"(?i:\\b(Lock|Unlock|SetAbort|SetComplete|BinaryRead|AddHeader|AppendToLog|BinaryWrite|Clear|End|Flush|Redirect|Write|CreateObject|HTMLEncode|MapPath|URLEncode|Abandon|Convert|Regex)\\b)",name:"support.function.asp"},{match:"(?i:\\b(Application_OnEnd|Application_OnStart|OnTransactionAbort|OnTransactionCommit|Session_OnEnd|Session_OnStart)\\b)",name:"support.function.event.asp"},{match:"(?i:(?<=as )(\\b[a-zA-Z_x7f-xff][a-zA-Z0-9_x7f-xff]*?\\b))",name:"support.type.vb.asp"},{match:"(?i:\\b(Array|Add|Asc|Atn|CBool|CByte|CCur|CDate|CDbl|Chr|CInt|CLng|Conversions|Cos|CreateObject|CSng|CStr|Date|DateAdd|DateDiff|DatePart|DateSerial|DateValue|Day|Derived|Math|Escape|Eval|Exists|Exp|Filter|FormatCurrency|FormatDateTime|FormatNumber|FormatPercent|GetLocale|GetObject|GetRef|Hex|Hour|InputBox|InStr|InStrRev|Int|Fix|IsArray|IsDate|IsEmpty|IsNull|IsNumeric|IsObject|Item|Items|Join|Keys|LBound|LCase|Left|Len|LoadPicture|Log|LTrim|RTrim|Trim|Maths|Mid|Minute|Month|MonthName|MsgBox|Now|Oct|Remove|RemoveAll|Replace|RGB|Right|Rnd|Round|ScriptEngine|ScriptEngineBuildVersion|ScriptEngineMajorVersion|ScriptEngineMinorVersion|Second|SetLocale|Sgn|Sin|Space|Split|Sqr|StrComp|String|StrReverse|Tan|Time|Timer|TimeSerial|TimeValue|TypeName|UBound|UCase|Unescape|VarType|Weekday|WeekdayName|Year)\\b)",name:"support.function.vb.asp"},{match:"-?\\b((0(x|X)[0-9a-fA-F]*)|((\\d+\\.?\\d*)|(\\.\\d+))((e|E)(\\+|-)?\\d+)?)(L|l|UL|ul|u|U|F|f)?\\b",name:"constant.numeric.asp"},{match:"(?i:\\b(vbtrue|vbfalse|vbcr|vbcrlf|vbformfeed|vblf|vbnewline|vbnullchar|vbnullstring|int32|vbtab|vbverticaltab|vbbinarycompare|vbtextcomparevbsunday|vbmonday|vbtuesday|vbwednesday|vbthursday|vbfriday|vbsaturday|vbusesystemdayofweek|vbfirstjan1|vbfirstfourdays|vbfirstfullweek|vbgeneraldate|vblongdate|vbshortdate|vblongtime|vbshorttime|vbobjecterror|vbEmpty|vbNull|vbInteger|vbLong|vbSingle|vbDouble|vbCurrency|vbDate|vbString|vbObject|vbError|vbBoolean|vbVariant|vbDataObject|vbDecimal|vbByte|vbArray)\\b)",name:"support.type.vb.asp"},{captures:{1:{name:"entity.name.function.asp"}},match:"(?i:(\\b[a-zA-Z_x7f-xff][a-zA-Z0-9_x7f-xff]*?\\b)(?=\\(\\)?))",name:"support.function.asp"},{match:"(?i:((?<=(\\+|=|-|\\&|\\\\|/|<|>|\\(|,))\\s*\\b([a-zA-Z_x7f-xff][a-zA-Z0-9_x7f-xff]*?)\\b(?!(\\(|\\.))|\\b([a-zA-Z_x7f-xff][a-zA-Z0-9_x7f-xff]*?)\\b(?=\\s*(\\+|=|-|\\&|\\\\|/|<|>|\\(|\\)))))",name:"variable.other.asp"},{match:"!|\\$|%|&|\\*|--|-|\\+\\+|\\+|~|===|==|=|!=|!==|<=|>=|<<=|>>=|>>>=|<>|<|>|!|&&|\\|\\||\\?:|\\*=|/=|%=|\\+=|-=|&=|\\^=|\\b(in|instanceof|new|delete|typeof|void)\\b",name:"keyword.operator.js"}],repository:{"round-brackets":{begin:"\\(",beginCaptures:{0:{name:"punctuation.section.round-brackets.begin.asp"}},end:"\\)",endCaptures:{0:{name:"punctuation.section.round-brackets.end.asp"}},name:"meta.round-brackets",patterns:[{include:"source.asp.vb.net"}]}},scopeName:"source.asp.vb.net",aliases:["cmd"]})]},41623:(e,t,n)=>{n.r(t),n.d(t,{default:()=>a});var a=[Object.freeze({displayName:"Dart",name:"dart",patterns:[{match:"^(#!.*)$",name:"meta.preprocessor.script.dart"},{begin:"^\\w*\\b(augment\\s+library|library|import\\s+augment|import|part\\s+of|part|export)\\b",beginCaptures:{0:{name:"keyword.other.import.dart"}},end:";",endCaptures:{0:{name:"punctuation.terminator.dart"}},name:"meta.declaration.dart",patterns:[{include:"#strings"},{include:"#comments"},{match:"\\b(as|show|hide)\\b",name:"keyword.other.import.dart"},{match:"\\b(if)\\b",name:"keyword.control.dart"}]},{include:"#comments"},{include:"#punctuation"},{include:"#annotations"},{include:"#keywords"},{include:"#constants-and-special-vars"},{include:"#operators"},{include:"#strings"}],repository:{annotations:{patterns:[{match:"@[a-zA-Z]+",name:"storage.type.annotation.dart"}]},"class-identifier":{patterns:[{match:"(?<!\\$)\\b(bool|num|int|double|dynamic)\\b(?!\\$)",name:"support.class.dart"},{match:"(?<!\\$)\\bvoid\\b(?!\\$)",name:"storage.type.primitive.dart"},{begin:"(?<![a-zA-Z0-9_$])([_$]*[A-Z][a-zA-Z0-9_$]*)\\b",beginCaptures:{1:{name:"support.class.dart"}},end:"(?!<)",patterns:[{include:"#type-args"}]}]},comments:{patterns:[{captures:{0:{name:"punctuation.definition.comment.dart"}},match:"/\\*\\*/",name:"comment.block.empty.dart"},{include:"#comments-doc-oldschool"},{include:"#comments-doc"},{include:"#comments-inline"}]},"comments-block":{patterns:[{begin:"/\\*",end:"\\*/",name:"comment.block.dart",patterns:[{include:"#comments-block"}]}]},"comments-doc":{patterns:[{begin:"///",name:"comment.block.documentation.dart",patterns:[{include:"#dartdoc"}],while:"^\\s*///"}]},"comments-doc-oldschool":{patterns:[{begin:"/\\*\\*",end:"\\*/",name:"comment.block.documentation.dart",patterns:[{include:"#comments-doc-oldschool"},{include:"#comments-block"},{include:"#dartdoc"}]}]},"comments-inline":{patterns:[{include:"#comments-block"},{captures:{1:{name:"comment.line.double-slash.dart"}},match:"((//).*)$"}]},"constants-and-special-vars":{patterns:[{match:"(?<!\\$)\\b(true|false|null)\\b(?!\\$)",name:"constant.language.dart"},{match:"(?<!\\$)\\b(this|super|augmented)\\b(?!\\$)",name:"variable.language.dart"},{match:"(?<!\\$)\\b((0(x|X)[0-9a-fA-F][0-9a-fA-F_]*)|((\\d[0-9_]*\\.?[0-9_]*)|(\\.\\d[0-9_]*))((e|E)(\\+|-)?\\d[0-9_]*)?)\\b(?!\\$)",name:"constant.numeric.dart"},{include:"#class-identifier"},{include:"#function-identifier"}]},dartdoc:{patterns:[{captures:{0:{name:"variable.name.source.dart"}},match:"(\\[.*?\\])"},{captures:{0:{name:"variable.name.source.dart"}},match:"^ {4,}(?![ \\*]).*"},{begin:"```.*?$",contentName:"variable.other.source.dart",end:"```"},{captures:{0:{name:"variable.other.source.dart"}},match:"(`[^`]+?`)"},{captures:{2:{name:"variable.other.source.dart"}},match:"(\\* ((    ).*))$"}]},expression:{patterns:[{include:"#constants-and-special-vars"},{include:"#strings"},{match:"\\w+",name:"variable.parameter.dart"},{begin:"\\{",end:"\\}",patterns:[{include:"#expression"}]}]},"function-identifier":{patterns:[{captures:{1:{name:"entity.name.function.dart"},2:{patterns:[{include:"#type-args"}]}},match:"([_$]*[a-z][a-zA-Z0-9_$]*)(<(?:[a-zA-Z0-9_$<>?]|,\\s*|\\s+extends\\s+)+>)?[!?]?\\("}]},keywords:{patterns:[{match:"(?<!\\$)\\bas\\b(?!\\$)",name:"keyword.cast.dart"},{match:"(?<!\\$)\\b(try|on|catch|finally|throw|rethrow)\\b(?!\\$)",name:"keyword.control.catch-exception.dart"},{match:"(?<!\\$)\\b(break|case|continue|default|do|else|for|if|in|switch|while|when)\\b(?!\\$)",name:"keyword.control.dart"},{match:"(?<!\\$)\\b(sync(\\*)?|async(\\*)?|await|yield(\\*)?)\\b(?!\\$)",name:"keyword.control.dart"},{match:"(?<!\\$)\\bassert\\b(?!\\$)",name:"keyword.control.dart"},{match:"(?<!\\$)\\b(new)\\b(?!\\$)",name:"keyword.control.new.dart"},{match:"(?<!\\$)\\b(return)\\b(?!\\$)",name:"keyword.control.return.dart"},{match:"(?<!\\$)\\b(abstract|sealed|base|interface|class|enum|extends|extension\\s+type|extension|external|factory|implements|get(?![(<])|mixin|native|operator|set(?![(<])|typedef|with|covariant)\\b(?!\\$)",name:"keyword.declaration.dart"},{match:"(?<!\\$)\\b(macro|augment|static|final|const|required|late)\\b(?!\\$)",name:"storage.modifier.dart"},{match:"(?<!\\$)\\b(?:void|var)\\b(?!\\$)",name:"storage.type.primitive.dart"}]},operators:{patterns:[{match:"(?<!\\$)\\b(is!?)\\b(?!\\$)",name:"keyword.operator.dart"},{match:"\\?|:",name:"keyword.operator.ternary.dart"},{match:"(<<|>>>?|~|\\^|\\||&)",name:"keyword.operator.bitwise.dart"},{match:"((&|\\^|\\||<<|>>>?)=)",name:"keyword.operator.assignment.bitwise.dart"},{match:"(=>)",name:"keyword.operator.closure.dart"},{match:"(==|!=|<=?|>=?)",name:"keyword.operator.comparison.dart"},{match:"(([+*/%-]|\\~)=)",name:"keyword.operator.assignment.arithmetic.dart"},{match:"(=)",name:"keyword.operator.assignment.dart"},{match:"(--|\\+\\+)",name:"keyword.operator.increment-decrement.dart"},{match:"(-|\\+|\\*|\\/|\\~\\/|%)",name:"keyword.operator.arithmetic.dart"},{match:"(!|&&|\\|\\|)",name:"keyword.operator.logical.dart"}]},punctuation:{patterns:[{match:",",name:"punctuation.comma.dart"},{match:";",name:"punctuation.terminator.dart"},{match:"\\.",name:"punctuation.dot.dart"}]},"string-interp":{patterns:[{captures:{1:{name:"variable.parameter.dart"}},match:"\\$(\\w+)",name:"meta.embedded.expression.dart"},{begin:"\\$\\{",end:"\\}",name:"meta.embedded.expression.dart",patterns:[{include:"#expression"}]},{match:"\\\\.",name:"constant.character.escape.dart"}]},strings:{patterns:[{begin:'(?<!r)"""',end:'"""(?!")',name:"string.interpolated.triple.double.dart",patterns:[{include:"#string-interp"}]},{begin:"(?<!r)'''",end:"'''(?!')",name:"string.interpolated.triple.single.dart",patterns:[{include:"#string-interp"}]},{begin:'r"""',end:'"""(?!")',name:"string.quoted.triple.double.dart"},{begin:"r'''",end:"'''(?!')",name:"string.quoted.triple.single.dart"},{begin:'(?<!\\|r)"',end:'"',name:"string.interpolated.double.dart",patterns:[{match:"\\n",name:"invalid.string.newline"},{include:"#string-interp"}]},{begin:'r"',end:'"',name:"string.quoted.double.dart",patterns:[{match:"\\n",name:"invalid.string.newline"}]},{begin:"(?<!\\|r)'",end:"'",name:"string.interpolated.single.dart",patterns:[{match:"\\n",name:"invalid.string.newline"},{include:"#string-interp"}]},{begin:"r'",end:"'",name:"string.quoted.single.dart",patterns:[{match:"\\n",name:"invalid.string.newline"}]}]},"type-args":{begin:"(<)",beginCaptures:{1:{name:"other.source.dart"}},end:"(>)",endCaptures:{1:{name:"other.source.dart"}},patterns:[{include:"#class-identifier"},{match:","},{match:"extends",name:"keyword.declaration.dart"},{include:"#comments"}]}},scopeName:"source.dart"})]},45562:(e,t,n)=>{n.r(t),n.d(t,{default:()=>a});var a=[Object.freeze({displayName:"BibTeX",name:"bibtex",patterns:[{captures:{0:{name:"punctuation.definition.comment.bibtex"}},match:"@(?i:comment)(?=[\\s{(])",name:"comment.block.at-sign.bibtex"},{begin:"((@)(?i:preamble))\\s*(\\{)\\s*",beginCaptures:{1:{name:"keyword.other.preamble.bibtex"},2:{name:"punctuation.definition.keyword.bibtex"},3:{name:"punctuation.section.preamble.begin.bibtex"}},end:"\\}",endCaptures:{0:{name:"punctuation.section.preamble.end.bibtex"}},name:"meta.preamble.braces.bibtex",patterns:[{include:"#field_value"}]},{begin:"((@)(?i:preamble))\\s*(\\()\\s*",beginCaptures:{1:{name:"keyword.other.preamble.bibtex"},2:{name:"punctuation.definition.keyword.bibtex"},3:{name:"punctuation.section.preamble.begin.bibtex"}},end:"\\)",endCaptures:{0:{name:"punctuation.section.preamble.end.bibtex"}},name:"meta.preamble.parenthesis.bibtex",patterns:[{include:"#field_value"}]},{begin:"((@)(?i:string))\\s*(\\{)\\s*([a-zA-Z!$&*+\\-./:;<>?@\\[\\\\\\]^_`|~][a-zA-Z0-9!$&*+\\-./:;<>?@\\[\\\\\\]^_`|~]*)",beginCaptures:{1:{name:"keyword.other.string-constant.bibtex"},2:{name:"punctuation.definition.keyword.bibtex"},3:{name:"punctuation.section.string-constant.begin.bibtex"},4:{name:"variable.other.bibtex"}},end:"\\}",endCaptures:{0:{name:"punctuation.section.string-constant.end.bibtex"}},name:"meta.string-constant.braces.bibtex",patterns:[{include:"#field_value"}]},{begin:"((@)(?i:string))\\s*(\\()\\s*([a-zA-Z!$&*+\\-./:;<>?@\\[\\\\\\]^_`|~][a-zA-Z0-9!$&*+\\-./:;<>?@\\[\\\\\\]^_`|~]*)",beginCaptures:{1:{name:"keyword.other.string-constant.bibtex"},2:{name:"punctuation.definition.keyword.bibtex"},3:{name:"punctuation.section.string-constant.begin.bibtex"},4:{name:"variable.other.bibtex"}},end:"\\)",endCaptures:{0:{name:"punctuation.section.string-constant.end.bibtex"}},name:"meta.string-constant.parenthesis.bibtex",patterns:[{include:"#field_value"}]},{begin:"((@)[a-zA-Z!$&*+\\-./:;<>?@\\[\\\\\\]^_`|~][a-zA-Z0-9!$&*+\\-./:;<>?@\\[\\\\\\]^_`|~]*)\\s*(\\{)\\s*([^\\s,}]*)",beginCaptures:{1:{name:"keyword.other.entry-type.bibtex"},2:{name:"punctuation.definition.keyword.bibtex"},3:{name:"punctuation.section.entry.begin.bibtex"},4:{name:"entity.name.type.entry-key.bibtex"}},end:"\\}",endCaptures:{0:{name:"punctuation.section.entry.end.bibtex"}},name:"meta.entry.braces.bibtex",patterns:[{begin:"([a-zA-Z!$&*+\\-./:;<>?@\\[\\\\\\]^_`|~][a-zA-Z0-9!$&*+\\-./:;<>?@\\[\\\\\\]^_`|~]*)\\s*(=)",beginCaptures:{1:{name:"support.function.key.bibtex"},2:{name:"punctuation.separator.key-value.bibtex"}},end:"(?=[,}])",name:"meta.key-assignment.bibtex",patterns:[{include:"#field_value"}]}]},{begin:"((@)[a-zA-Z!$&*+\\-./:;<>?@\\[\\\\\\]^_`|~][a-zA-Z0-9!$&*+\\-./:;<>?@\\[\\\\\\]^_`|~]*)\\s*(\\()\\s*([^\\s,]*)",beginCaptures:{1:{name:"keyword.other.entry-type.bibtex"},2:{name:"punctuation.definition.keyword.bibtex"},3:{name:"punctuation.section.entry.begin.bibtex"},4:{name:"entity.name.type.entry-key.bibtex"}},end:"\\)",endCaptures:{0:{name:"punctuation.section.entry.end.bibtex"}},name:"meta.entry.parenthesis.bibtex",patterns:[{begin:"([a-zA-Z!$&*+\\-./:;<>?@\\[\\\\\\]^_`|~][a-zA-Z0-9!$&*+\\-./:;<>?@\\[\\\\\\]^_`|~]*)\\s*(=)",beginCaptures:{1:{name:"support.function.key.bibtex"},2:{name:"punctuation.separator.key-value.bibtex"}},end:"(?=[,)])",name:"meta.key-assignment.bibtex",patterns:[{include:"#field_value"}]}]},{begin:"[^@\\n]",end:"(?=@)",name:"comment.block.bibtex"}],repository:{field_value:{patterns:[{include:"#string_content"},{include:"#integer"},{include:"#string_var"},{match:"#",name:"keyword.operator.bibtex"}]},integer:{captures:{1:{name:"constant.numeric.bibtex"}},match:"\\s*(\\d+)\\s*"},nested_braces:{begin:"\\{",beginCaptures:{0:{name:"punctuation.definition.group.begin.bibtex"}},end:"\\}",endCaptures:{0:{name:"punctuation.definition.group.end.bibtex"}},patterns:[{include:"#nested_braces"}]},string_content:{patterns:[{begin:"\\{",beginCaptures:{0:{name:"punctuation.definition.string.begin.bibtex"}},end:"\\}",endCaptures:{0:{name:"punctuation.definition.string.end.bibtex"}},patterns:[{include:"#nested_braces"}]},{begin:'"',beginCaptures:{0:{name:"punctuation.definition.string.begin.bibtex"}},end:'"',endCaptures:{0:{name:"punctuation.definition.string.end.bibtex"}},patterns:[{include:"#nested_braces"}]}]},string_var:{captures:{0:{name:"support.variable.bibtex"}},match:"[a-zA-Z!$&*+\\-./:;<>?@\\[\\\\\\]^_`|~][a-zA-Z0-9!$&*+\\-./:;<>?@\\[\\\\\\]^_`|~]*"}},scopeName:"text.bibtex"})]},58050:(e,t,n)=>{n.r(t),n.d(t,{default:()=>a});var a=[Object.freeze({displayName:"Makefile",name:"make",patterns:[{include:"#comment"},{include:"#variables"},{include:"#variable-assignment"},{include:"#directives"},{include:"#recipe"},{include:"#target"}],repository:{"another-variable-braces":{patterns:[{begin:"(?<={)(?!})",end:"(?=}|((?<!\\\\)\\n))",name:"variable.other.makefile",patterns:[{include:"#variables"},{match:"\\\\\\n",name:"constant.character.escape.continuation.makefile"}]}]},"another-variable-parentheses":{patterns:[{begin:"(?<=\\()(?!\\))",end:"(?=\\)|((?<!\\\\)\\n))",name:"variable.other.makefile",patterns:[{include:"#variables"},{match:"\\\\\\n",name:"constant.character.escape.continuation.makefile"}]}]},"braces-interpolation":{begin:"{",end:"}",patterns:[{include:"#variables"},{include:"#interpolation"}]},"builtin-variable-braces":{patterns:[{match:"(?<={)(MAKEFILES|VPATH|SHELL|MAKESHELL|MAKE|MAKELEVEL|MAKEFLAGS|MAKECMDGOALS|CURDIR|SUFFIXES|\\.LIBPATTERNS)(?=\\s*})",name:"variable.language.makefile"}]},"builtin-variable-parentheses":{patterns:[{match:"(?<=\\()(MAKEFILES|VPATH|SHELL|MAKESHELL|MAKE|MAKELEVEL|MAKEFLAGS|MAKECMDGOALS|CURDIR|SUFFIXES|\\.LIBPATTERNS)(?=\\s*\\))",name:"variable.language.makefile"}]},comma:{match:",",name:"punctuation.separator.delimeter.comma.makefile"},comment:{begin:"(^[ ]+)?((?<!\\\\)(\\\\\\\\)*)(?=#)",beginCaptures:{1:{name:"punctuation.whitespace.comment.leading.makefile"}},end:"(?!\\G)",patterns:[{begin:"#",beginCaptures:{0:{name:"punctuation.definition.comment.makefile"}},end:"(?=[^\\\\])$",name:"comment.line.number-sign.makefile",patterns:[{match:"\\\\\\n",name:"constant.character.escape.continuation.makefile"}]}]},directives:{patterns:[{begin:"^[ ]*([s\\-]?include)\\b",beginCaptures:{1:{name:"keyword.control.include.makefile"}},end:"^",patterns:[{include:"#comment"},{include:"#variables"},{match:"%",name:"constant.other.placeholder.makefile"}]},{begin:"^[ ]*(vpath)\\b",beginCaptures:{1:{name:"keyword.control.vpath.makefile"}},end:"^",patterns:[{include:"#comment"},{include:"#variables"},{match:"%",name:"constant.other.placeholder.makefile"}]},{begin:"^\\s*(?:(override)\\s*)?(define)\\s*([^\\s]+)\\s*(=|\\?=|:=|\\+=)?(?=\\s)",captures:{1:{name:"keyword.control.override.makefile"},2:{name:"keyword.control.define.makefile"},3:{name:"variable.other.makefile"},4:{name:"punctuation.separator.key-value.makefile"}},end:"^\\s*(endef)\\b",name:"meta.scope.conditional.makefile",patterns:[{begin:"\\G(?!\\n)",end:"^",patterns:[{include:"#comment"}]},{include:"#variables"},{include:"#directives"}]},{begin:"^[ ]*(export)\\b",beginCaptures:{1:{name:"keyword.control.$1.makefile"}},end:"^",patterns:[{include:"#comment"},{include:"#variable-assignment"},{match:"[^\\s]+",name:"variable.other.makefile"}]},{begin:"^[ ]*(override|private)\\b",beginCaptures:{1:{name:"keyword.control.$1.makefile"}},end:"^",patterns:[{include:"#comment"},{include:"#variable-assignment"}]},{begin:"^[ ]*(unexport|undefine)\\b",beginCaptures:{1:{name:"keyword.control.$1.makefile"}},end:"^",patterns:[{include:"#comment"},{match:"[^\\s]+",name:"variable.other.makefile"}]},{begin:"^\\s*(ifeq|ifneq|ifdef|ifndef)(?=\\s)",captures:{1:{name:"keyword.control.$1.makefile"}},end:"^\\s*(endif)\\b",name:"meta.scope.conditional.makefile",patterns:[{begin:"\\G",end:"^",name:"meta.scope.condition.makefile",patterns:[{include:"#comma"},{include:"#variables"},{include:"#comment"}]},{begin:"^\\s*else(?=\\s)\\s*(ifeq|ifneq|ifdef|ifndef)*(?=\\s)",beginCaptures:{0:{name:"keyword.control.else.makefile"}},end:"^",patterns:[{include:"#comma"},{include:"#variables"},{include:"#comment"}]},{include:"$self"}]}]},"flavor-variable-braces":{patterns:[{begin:"(?<={)(origin|flavor)\\s(?=[^\\s}]+\\s*})",beginCaptures:{1:{name:"support.function.$1.makefile"}},contentName:"variable.other.makefile",end:"(?=})",name:"meta.scope.function-call.makefile",patterns:[{include:"#variables"}]}]},"flavor-variable-parentheses":{patterns:[{begin:"(?<=\\()(origin|flavor)\\s(?=[^\\s)]+\\s*\\))",beginCaptures:{1:{name:"support.function.$1.makefile"}},contentName:"variable.other.makefile",end:"(?=\\))",name:"meta.scope.function-call.makefile",patterns:[{include:"#variables"}]}]},"function-variable-braces":{patterns:[{begin:"(?<={)(subst|patsubst|strip|findstring|filter(-out)?|sort|word(list)?|firstword|lastword|dir|notdir|suffix|basename|addsuffix|addprefix|join|wildcard|realpath|abspath|info|error|warning|shell|foreach|if|or|and|call|eval|value|file|guile)\\s",beginCaptures:{1:{name:"support.function.$1.makefile"}},end:"(?=}|((?<!\\\\)\\n))",name:"meta.scope.function-call.makefile",patterns:[{include:"#comma"},{include:"#variables"},{include:"#interpolation"},{match:"%|\\*",name:"constant.other.placeholder.makefile"},{match:"\\\\\\n",name:"constant.character.escape.continuation.makefile"}]}]},"function-variable-parentheses":{patterns:[{begin:"(?<=\\()(subst|patsubst|strip|findstring|filter(-out)?|sort|word(list)?|firstword|lastword|dir|notdir|suffix|basename|addsuffix|addprefix|join|wildcard|realpath|abspath|info|error|warning|shell|foreach|if|or|and|call|eval|value|file|guile)\\s",beginCaptures:{1:{name:"support.function.$1.makefile"}},end:"(?=\\)|((?<!\\\\)\\n))",name:"meta.scope.function-call.makefile",patterns:[{include:"#comma"},{include:"#variables"},{include:"#interpolation"},{match:"%|\\*",name:"constant.other.placeholder.makefile"},{match:"\\\\\\n",name:"constant.character.escape.continuation.makefile"}]}]},interpolation:{patterns:[{include:"#parentheses-interpolation"},{include:"#braces-interpolation"}]},"parentheses-interpolation":{begin:"\\(",end:"\\)",patterns:[{include:"#variables"},{include:"#interpolation"}]},recipe:{begin:"^\\t([+\\-@]*)",beginCaptures:{1:{name:"keyword.control.$1.makefile"}},end:"[^\\\\]$",name:"meta.scope.recipe.makefile",patterns:[{match:"\\\\\\n",name:"constant.character.escape.continuation.makefile"},{include:"#variables"}]},"simple-variable":{patterns:[{match:"\\$[^(){}]",name:"variable.language.makefile"}]},target:{begin:"^(?!\\t)([^:]*)(:)(?!=)",beginCaptures:{1:{patterns:[{captures:{1:{name:"support.function.target.$1.makefile"}},match:"^\\s*(\\.(PHONY|SUFFIXES|DEFAULT|PRECIOUS|INTERMEDIATE|SECONDARY|SECONDEXPANSION|DELETE_ON_ERROR|IGNORE|LOW_RESOLUTION_TIME|SILENT|EXPORT_ALL_VARIABLES|NOTPARALLEL|ONESHELL|POSIX))\\s*$"},{begin:"(?=\\S)",end:"(?=\\s|$)",name:"entity.name.function.target.makefile",patterns:[{include:"#variables"},{match:"%",name:"constant.other.placeholder.makefile"}]}]},2:{name:"punctuation.separator.key-value.makefile"}},end:"[^\\\\]$",name:"meta.scope.target.makefile",patterns:[{begin:"\\G",end:"(?=[^\\\\])$",name:"meta.scope.prerequisites.makefile",patterns:[{match:"\\\\\\n",name:"constant.character.escape.continuation.makefile"},{match:"%|\\*",name:"constant.other.placeholder.makefile"},{include:"#comment"},{include:"#variables"}]}]},"variable-assignment":{begin:"(^[ ]*|\\G\\s*)([^\\s:#=]+)\\s*((?<![?:+!])=|\\?=|:=|\\+=|!=)",beginCaptures:{2:{name:"variable.other.makefile",patterns:[{include:"#variables"}]},3:{name:"punctuation.separator.key-value.makefile"}},end:"\\n",patterns:[{match:"\\\\\\n",name:"constant.character.escape.continuation.makefile"},{include:"#comment"},{include:"#variables"}]},"variable-braces":{patterns:[{begin:"\\${",captures:{0:{name:"punctuation.definition.variable.makefile"}},end:"}|((?<!\\\\)\\n)",name:"string.interpolated.makefile",patterns:[{include:"#variables"},{include:"#builtin-variable-braces"},{include:"#function-variable-braces"},{include:"#flavor-variable-braces"},{include:"#another-variable-braces"}]}]},"variable-parentheses":{patterns:[{begin:"\\$\\(",captures:{0:{name:"punctuation.definition.variable.makefile"}},end:"\\)|((?<!\\\\)\\n)",name:"string.interpolated.makefile",patterns:[{include:"#variables"},{include:"#builtin-variable-parentheses"},{include:"#function-variable-parentheses"},{include:"#flavor-variable-parentheses"},{include:"#another-variable-parentheses"}]}]},variables:{patterns:[{include:"#simple-variable"},{include:"#variable-parentheses"},{include:"#variable-braces"}]}},scopeName:"source.makefile",aliases:["makefile"]})]},63760:(e,t,n)=>{n.r(t),n.d(t,{default:()=>a});var a=[Object.freeze({displayName:"Clojure",name:"clojure",patterns:[{include:"#comment"},{include:"#shebang-comment"},{include:"#quoted-sexp"},{include:"#sexp"},{include:"#keyfn"},{include:"#string"},{include:"#vector"},{include:"#set"},{include:"#map"},{include:"#regexp"},{include:"#var"},{include:"#constants"},{include:"#dynamic-variables"},{include:"#metadata"},{include:"#namespace-symbol"},{include:"#symbol"}],repository:{comment:{begin:"(?<!\\\\);",beginCaptures:{0:{name:"punctuation.definition.comment.clojure"}},end:"$",name:"comment.line.semicolon.clojure"},constants:{patterns:[{match:"(nil)(?=(\\s|\\)|\\]|\\}))",name:"constant.language.nil.clojure"},{match:"(true|false)",name:"constant.language.boolean.clojure"},{match:"(##(?:Inf|-Inf|NaN))",name:"constant.numeric.symbol.clojure"},{match:"([-+]?\\d+/\\d+)",name:"constant.numeric.ratio.clojure"},{match:"([-+]?(?:(?:3[0-6])|(?:[12]\\d)|[2-9])[rR][0-9A-Za-z]+N?)",name:"constant.numeric.arbitrary-radix.clojure"},{match:"([-+]?0[xX][0-9a-fA-F]+N?)",name:"constant.numeric.hexadecimal.clojure"},{match:"([-+]?0[0-7]+N?)",name:"constant.numeric.octal.clojure"},{match:"([-+]?\\d+(?:(\\.|(?=[eEM]))\\d*([eE][-+]?\\d+)?)M?)",name:"constant.numeric.double.clojure"},{match:"([-+]?\\d+N?)",name:"constant.numeric.long.clojure"},{include:"#keyword"}]},"dynamic-variables":{match:"\\*[\\w\\.\\-_:+=><!?\\d]+\\*",name:"meta.symbol.dynamic.clojure"},keyfn:{patterns:[{match:"(?<=(\\s|\\(|\\[|\\{))(if(-[-\\p{Ll}?]*)?|when(-[-\\p{Ll}]*)?|for(-[-\\p{Ll}]*)?|cond|do|let(-[-\\p{Ll}?]*)?|binding|loop|recur|fn|throw[\\p{Ll}\\-]*|try|catch|finally|([\\p{Ll}]*case))(?=(\\s|\\)|\\]|\\}))",name:"storage.control.clojure"},{match:"(?<=(\\s|\\(|\\[|\\{))(declare-?|(in-)?ns|import|use|require|load|compile|(def[\\p{Ll}\\-]*))(?=(\\s|\\)|\\]|\\}))",name:"keyword.control.clojure"}]},keyword:{match:"(?<=(\\s|\\(|\\[|\\{)):[\\w#\\.\\-_:+=><\\/!?\\*]+(?=(\\s|\\)|\\]|\\}|\\,))",name:"constant.keyword.clojure"},map:{begin:"(\\{)",beginCaptures:{1:{name:"punctuation.section.map.begin.clojure"}},end:"(\\}(?=[}\\])\\s]*(?:;|$)))|(\\})",endCaptures:{1:{name:"punctuation.section.map.end.trailing.clojure"},2:{name:"punctuation.section.map.end.clojure"}},name:"meta.map.clojure",patterns:[{include:"$self"}]},metadata:{patterns:[{begin:"(\\^\\{)",beginCaptures:{1:{name:"punctuation.section.metadata.map.begin.clojure"}},end:"(\\}(?=[}\\])\\s]*(?:;|$)))|(\\})",endCaptures:{1:{name:"punctuation.section.metadata.map.end.trailing.clojure"},2:{name:"punctuation.section.metadata.map.end.clojure"}},name:"meta.metadata.map.clojure",patterns:[{include:"$self"}]},{begin:"(\\^)",end:"(\\s)",name:"meta.metadata.simple.clojure",patterns:[{include:"#keyword"},{include:"$self"}]}]},"namespace-symbol":{patterns:[{captures:{1:{name:"meta.symbol.namespace.clojure"}},match:"([\\p{L}\\.\\-_+=><!?\\*][\\w\\.\\-_:+=><!?\\*\\d]*)/"}]},"quoted-sexp":{begin:"(['``]\\()",beginCaptures:{1:{name:"punctuation.section.expression.begin.clojure"}},end:"(\\))$|(\\)(?=[}\\])\\s]*(?:;|$)))|(\\))",endCaptures:{1:{name:"punctuation.section.expression.end.trailing.clojure"},2:{name:"punctuation.section.expression.end.trailing.clojure"},3:{name:"punctuation.section.expression.end.clojure"}},name:"meta.quoted-expression.clojure",patterns:[{include:"$self"}]},regexp:{begin:'#"',beginCaptures:{0:{name:"punctuation.definition.regexp.begin.clojure"}},end:'"',endCaptures:{0:{name:"punctuation.definition.regexp.end.clojure"}},name:"string.regexp.clojure",patterns:[{include:"#regexp_escaped_char"}]},regexp_escaped_char:{match:"\\\\.",name:"constant.character.escape.clojure"},set:{begin:"(\\#\\{)",beginCaptures:{1:{name:"punctuation.section.set.begin.clojure"}},end:"(\\}(?=[}\\])\\s]*(?:;|$)))|(\\})",endCaptures:{1:{name:"punctuation.section.set.end.trailing.clojure"},2:{name:"punctuation.section.set.end.clojure"}},name:"meta.set.clojure",patterns:[{include:"$self"}]},sexp:{begin:"(\\()",beginCaptures:{1:{name:"punctuation.section.expression.begin.clojure"}},end:"(\\))$|(\\)(?=[}\\])\\s]*(?:;|$)))|(\\))",endCaptures:{1:{name:"punctuation.section.expression.end.trailing.clojure"},2:{name:"punctuation.section.expression.end.trailing.clojure"},3:{name:"punctuation.section.expression.end.clojure"}},name:"meta.expression.clojure",patterns:[{begin:"(?<=\\()(ns|declare|def[\\w\\d._:+=><!?*-]*|[\\w._:+=><!?*-][\\w\\d._:+=><!?*-]*/def[\\w\\d._:+=><!?*-]*)\\s+",beginCaptures:{1:{name:"keyword.control.clojure"}},end:"(?=\\))",name:"meta.definition.global.clojure",patterns:[{include:"#metadata"},{include:"#dynamic-variables"},{match:"([\\p{L}\\.\\-_+=><!?\\*][\\w\\.\\-_:+=><!?\\*\\d]*)",name:"entity.global.clojure"},{include:"$self"}]},{include:"#keyfn"},{include:"#constants"},{include:"#vector"},{include:"#map"},{include:"#set"},{include:"#sexp"},{captures:{1:{name:"entity.name.function.clojure"}},match:"(?<=\\()(.+?)(?=\\s|\\))",patterns:[{include:"$self"}]},{include:"$self"}]},"shebang-comment":{begin:"^(#!)",beginCaptures:{1:{name:"punctuation.definition.comment.shebang.clojure"}},end:"$",name:"comment.line.shebang.clojure"},string:{begin:'(?<!\\\\)(")',beginCaptures:{1:{name:"punctuation.definition.string.begin.clojure"}},end:'(")',endCaptures:{1:{name:"punctuation.definition.string.end.clojure"}},name:"string.quoted.double.clojure",patterns:[{match:"\\\\.",name:"constant.character.escape.clojure"}]},symbol:{patterns:[{match:"([\\p{L}\\.\\-_+=><!?\\*][\\w\\.\\-_:+=><!?\\*\\d]*)",name:"meta.symbol.clojure"}]},var:{match:"(?<=(\\s|\\(|\\[|\\{)\\#)'[\\w\\.\\-_:+=><\\/!?\\*]+(?=(\\s|\\)|\\]|\\}))",name:"meta.var.clojure"},vector:{begin:"(\\[)",beginCaptures:{1:{name:"punctuation.section.vector.begin.clojure"}},end:"(\\](?=[}\\])\\s]*(?:;|$)))|(\\])",endCaptures:{1:{name:"punctuation.section.vector.end.trailing.clojure"},2:{name:"punctuation.section.vector.end.clojure"}},name:"meta.vector.clojure",patterns:[{include:"$self"}]}},scopeName:"source.clojure",aliases:["clj"]})]},71650:(e,t,n)=>{n.r(t),n.d(t,{default:()=>r});var a=n(38383);let i=Object.freeze({displayName:"F#",name:"fsharp",patterns:[{include:"#compiler_directives"},{include:"#comments"},{include:"#constants"},{include:"#strings"},{include:"#chars"},{include:"#double_tick"},{include:"#definition"},{include:"#abstract_definition"},{include:"#attributes"},{include:"#modules"},{include:"#anonymous_functions"},{include:"#du_declaration"},{include:"#record_declaration"},{include:"#records"},{include:"#strp_inlined"},{include:"#keywords"},{include:"#cexprs"},{include:"#text"}],repository:{abstract_definition:{begin:"\\b(static)?\\s+(abstract)\\s+(member)?(\\s+\\[<.*>\\])?\\s*([_A-Za-z0-9,\\._`\\s]+)(<)?",beginCaptures:{1:{name:"keyword.fsharp"},2:{name:"keyword.fsharp"},3:{name:"keyword.fsharp"},4:{name:"support.function.attribute.fsharp"},5:{name:"keyword.symbol.fsharp"}},end:"\\s*(with)\\b|=|$",endCaptures:{1:{name:"keyword.fsharp"}},name:"abstract.definition.fsharp",patterns:[{include:"#comments"},{include:"#common_declaration"},{captures:{1:{name:"keyword.symbol.fsharp"},2:{name:"variable.parameter.fsharp"},3:{name:"keyword.symbol.fsharp"},4:{name:"entity.name.type.fsharp"}},match:"(\\?{0,1})([A-Za-z0-9'`^._ ]+)\\s*(:)((?!with\\b)\\b([\\w0-9'`^._ ]+)){0,1}"},{captures:{1:{name:"entity.name.type.fsharp"}},comments:"Here we need the \\w modifier in order to check that the words isn't blacklisted",match:"(?!with|get|set\\b)\\s*([\\w0-9'`^._]+)"},{include:"#keywords"}]},anonymous_functions:{patterns:[{begin:"\\b(fun)\\b",beginCaptures:{1:{name:"keyword.fsharp"}},end:"(->)",endCaptures:{1:{name:"keyword.symbol.arrow.fsharp"}},name:"function.anonymous",patterns:[{include:"#comments"},{begin:"(\\()",beginCaptures:{1:{name:"keyword.symbol.fsharp"}},end:"\\s*(?=(->))",endCaptures:{1:{name:"keyword.symbol.arrow.fsharp"}},patterns:[{include:"#member_declaration"}]},{include:"#variables"}]}]},anonymous_record_declaration:{begin:"(\\{\\|)",beginCaptures:{1:{name:"keyword.symbol.fsharp"}},end:"(\\|\\})",endCaptures:{1:{name:"keyword.symbol.fsharp"}},patterns:[{captures:{1:{name:"keyword.symbol.fsharp"}},match:"[A-Za-z0-9'`^_ ]+(:)"},{captures:{1:{name:"entity.name.type.fsharp"}},match:"([A-Za-z0-9'`^_ ]+)"},{include:"#anonymous_record_declaration"},{include:"#keywords"}]},attributes:{patterns:[{begin:"\\[<",end:">\\]|\\]",name:"support.function.attribute.fsharp",patterns:[{include:"$self"}]}]},cexprs:{patterns:[{captures:{0:{name:"keyword.fsharp"}},match:"\\b(async|seq|promise|task|maybe|asyncMaybe|controller|scope|application|pipeline)(?=\\s*\\{)",name:"cexpr.fsharp"}]},chars:{patterns:[{captures:{1:{name:"string.quoted.single.fsharp"}},match:"('\\\\?.')",name:"char.fsharp"}]},comments:{patterns:[{beginCaptures:{1:{name:"comment.block.fsharp"}},match:"(\\(\\*{3}.*\\*{3}\\))",name:"comment.literate.command.fsharp"},{begin:"^\\s*(\\(\\*\\*(?!\\)))((?!\\*\\)).)*$",beginCaptures:{1:{name:"comment.block.fsharp"}},endCaptures:{1:{name:"comment.block.fsharp"}},name:"comment.block.markdown.fsharp",patterns:[{include:"text.html.markdown"}],while:"^(?!\\s*(\\*)+\\)\\s*$)"},{begin:"(\\(\\*(?!\\)))",beginCaptures:{1:{name:"comment.block.fsharp"}},end:"(\\*+\\))",endCaptures:{1:{name:"comment.block.fsharp"}},name:"comment.block.fsharp",patterns:[{comments:"Capture // when inside of (* *) like that the rule which capture comments starting by // is not trigger. See https://github.com/ionide/ionide-fsgrammar/issues/155",match:"//",name:"fast-capture.comment.line.double-slash.fsharp"},{comments:"Capture (*) when inside of (* *) so that it doesn't prematurely end the comment block.",match:"\\(\\*\\)",name:"fast-capture.comment.line.mul-operator.fsharp"},{include:"#comments"}]},{captures:{1:{name:"comment.block.fsharp"}},match:"((?<!\\()(\\*)+\\))",name:"comment.block.markdown.fsharp.end"},{begin:"(?<![!%&+-.<=>?@^|/])///(?!/)",name:"comment.line.markdown.fsharp",patterns:[{include:"text.html.markdown"}],while:"(?<![!%&+-.<=>?@^|/])///(?!/)"},{match:"(?<![!%&+-.<=>?@^|/])//(.*$)",name:"comment.line.double-slash.fsharp"}]},common_binding_definition:{patterns:[{include:"#comments"},{include:"#attributes"},{begin:"(:)\\s*(\\()\\s*(static member|member)",beginCaptures:{1:{name:"keyword.symbol.fsharp"},2:{name:"keyword.symbol.fsharp"},3:{name:"keyword.fsharp"}},comments:"SRTP syntax support",end:"(\\))\\s*((?=,)|(?==))",endCaptures:{1:{name:"keyword.symbol.fsharp"}},patterns:[{captures:{1:{name:"entity.name.type.fsharp"}},match:"(\\^[A-Za-z0-9'._]+)"},{include:"#variables"},{include:"#keywords"}]},{begin:"(:)\\s*(\\()",beginCaptures:{1:{name:"keyword.symbol.fsharp"},2:{name:"keyword.symbol.fsharp"}},end:"(\\)\\s*(([?A-Za-z0-9'`^._ ]*)))",endCaptures:{1:{name:"keyword.symbol.fsharp"},2:{name:"entity.name.type.fsharp"}},patterns:[{include:"#tuple_signature"}]},{begin:"(:)\\s*(\\^[A-Za-z0-9'._]+)\\s*(when)",beginCaptures:{1:{name:"keyword.symbol.fsharp"},2:{name:"entity.name.type.fsharp"},3:{name:"keyword.fsharp"}},end:"(?=:)",endCaptures:{1:{name:"keyword.symbol.fsharp"}},patterns:[{match:"\\b(and|when|or)\\b",name:"keyword.fsharp"},{captures:{1:{name:"entity.name.type.fsharp"}},comment:"Because we first capture the keywords, we can capture what looks like a word and assume it's an entity definition",match:"([A-Za-z0-9'^._]+)"},{match:"(\\(|\\))",name:"keyword.symbol.fsharp"}]},{captures:{1:{name:"keyword.symbol.fsharp"},2:{name:"entity.name.type.fsharp"}},match:"(:)\\s*([?A-Za-z0-9'`^._ ]+)"},{captures:{1:{name:"keyword.symbol.arrow.fsharp"},2:{name:"keyword.symbol.fsharp"},3:{name:"entity.name.type.fsharp"}},match:"(->)\\s*(\\()?\\s*([?A-Za-z0-9'`^._ ]+)*"},{begin:"(\\*)\\s*(\\()",beginCaptures:{1:{name:"keyword.symbol.fsharp"},2:{name:"keyword.symbol.fsharp"}},end:"(\\)\\s*(([?A-Za-z0-9'`^._ ]+))*)",endCaptures:{1:{name:"keyword.symbol.fsharp"},2:{name:"entity.name.type.fsharp"}},patterns:[{include:"#tuple_signature"}]},{begin:"(\\*)(\\s*([?A-Za-z0-9'`^._ ]+))*",beginCaptures:{1:{name:"keyword.symbol.fsharp"},2:{name:"entity.name.type.fsharp"}},end:"(?==)|(?=\\))",endCaptures:{1:{name:"keyword.symbol.fsharp"}},patterns:[{include:"#tuple_signature"}]},{begin:"(<+(?![\\s]*\\)))",beginCaptures:{1:{name:"keyword.symbol.fsharp"}},beginComment:"The group (?![[:space:]]*\\) is for protection against overload operator. static member (<)",end:"((?<!:)>|\\))",endCaptures:{1:{name:"keyword.symbol.fsharp"}},endComment:"The group (?<!:) prevent us from stopping on :> when using SRTP synthax",patterns:[{include:"#generic_declaration"}]},{include:"#anonymous_record_declaration"},{begin:"({)",beginCaptures:{1:{name:"keyword.symbol.fsharp"}},end:"(})",endCaptures:{1:{name:"keyword.symbol.fsharp"}},patterns:[{include:"#record_signature"}]},{include:"#definition"},{include:"#variables"},{include:"#keywords"}]},common_declaration:{patterns:[{begin:"\\s*(->)\\s*([A-Za-z0-9'`^._ ]+)(<)",beginCaptures:{1:{name:"keyword.symbol.arrow.fsharp"},2:{name:"entity.name.type.fsharp"},3:{name:"keyword.symbol.fsharp"}},end:"(>)",endCaptures:{1:{name:"keyword.symbol.fsharp"}},patterns:[{captures:{1:{name:"entity.name.type.fsharp"}},match:"([A-Za-z0-9'`^._ ]+)"},{include:"#keywords"}]},{captures:{1:{name:"keyword.symbol.arrow.fsharp"},2:{name:"entity.name.type.fsharp"}},match:"\\s*(->)\\s*(?!with|get|set\\b)\\b([\\w0-9'`^._]+)"},{include:"#anonymous_record_declaration"},{begin:"(\\?{0,1})([A-Za-z0-9'`^._ ]+)\\s*(:)(\\s*([?A-Za-z0-9'`^._ ]+)(<))",beginCaptures:{1:{name:"keyword.symbol.fsharp"},2:{name:"variable.parameter.fsharp"},3:{name:"keyword.symbol.fsharp"},4:{name:"keyword.symbol.fsharp"},5:{name:"entity.name.type.fsharp"}},end:"(>)",endCaptures:{1:{name:"keyword.symbol.fsharp"}},patterns:[{captures:{1:{name:"entity.name.type.fsharp"}},match:"([A-Za-z0-9'`^._ ]+)"},{include:"#keywords"}]}]},compiler_directives:{patterns:[{captures:{},match:"\\s?(#if|#elif|#elseif|#else|#endif|#light|#nowarn)",name:"keyword.control.directive.fsharp"}]},constants:{patterns:[{match:"\\(\\)",name:"keyword.symbol.fsharp"},{match:"\\b-?\\d[0-9_]*((\\.(?!\\.)(\\d[0-9_]*([eE][+-]??\\d[0-9_]*)?)?)|([eE][+-]??\\d[0-9_]*))",name:"constant.numeric.float.fsharp"},{match:"\\b(-?((0(x|X)[0-9a-fA-F][0-9a-fA-F_]*)|(0(o|O)[0-7][0-7_]*)|(0(b|B)[01][01_]*)|(\\d[0-9_]*)))",name:"constant.numeric.integer.nativeint.fsharp"},{match:"\\b(true|false)\\b",name:"constant.language.boolean.fsharp"},{match:"\\b(null|void)\\b",name:"constant.other.fsharp"}]},definition:{patterns:[{begin:"\\b(let mutable|static let mutable|static let|let inline|let|and|member val|member inline|static member inline|static member val|static member|default|member|override|let!)(\\s+rec|mutable)?(\\s+\\[<.*>\\])?\\s*(private|internal|public)?\\s+(\\[[^-=]*\\]|[_A-Za-z]([_A-Za-z0-9\\._]+)*|``[_A-Za-z]([_A-Za-z0-9\\._`\\s]+|(?<=,)\\s)*)?",beginCaptures:{1:{name:"keyword.fsharp"},2:{name:"keyword.fsharp"},3:{name:"support.function.attribute.fsharp"},4:{name:"storage.modifier.fsharp"},5:{name:"variable.fsharp"}},end:"\\s*((with\\b)|(=|\\n+=|(?<==)))",endCaptures:{2:{name:"keyword.fsharp"},3:{name:"keyword.symbol.fsharp"}},name:"binding.fsharp",patterns:[{include:"#common_binding_definition"}]},{begin:"\\b(use|use!|and|and!)\\s+(\\[[^-=]*\\]|[_A-Za-z]([_A-Za-z0-9\\._]+)*|``[_A-Za-z]([_A-Za-z0-9\\._`\\s]+|(?<=,)\\s)*)?",beginCaptures:{1:{name:"keyword.fsharp"}},end:"\\s*(=)",endCaptures:{1:{name:"keyword.symbol.fsharp"}},name:"binding.fsharp",patterns:[{include:"#common_binding_definition"}]},{begin:"(?<=with|and)\\s*\\b((get|set)\\s*(?=\\())(\\[[^-=]*\\]|[_A-Za-z]([_A-Za-z0-9\\._]+)*|``[_A-Za-z]([_A-Za-z0-9\\._`\\s]+|(?<=,)\\s)*)?",beginCaptures:{4:{name:"variable.fsharp"}},end:"\\s*(=|\\n+=|(?<==))",endCaptures:{1:{name:"keyword.symbol.fsharp"}},name:"binding.fsharp",patterns:[{include:"#common_binding_definition"}]},{begin:"\\b(static val mutable|val mutable|val inline|val)(\\s+rec|mutable)?(\\s+\\[<.*>\\])?\\s*(private|internal|public)?\\s+(\\[[^-=]*\\]|[_A-Za-z]([_A-Za-z0-9,\\._]+)*|``[_A-Za-z]([_A-Za-z0-9,\\._`\\s]+|(?<=,)\\s)*)?",beginCaptures:{1:{name:"keyword.fsharp"},2:{name:"keyword.fsharp"},3:{name:"support.function.attribute.fsharp"},4:{name:"storage.modifier.fsharp"},5:{name:"variable.fsharp"}},end:"\\n$",name:"binding.fsharp",patterns:[{include:"#common_binding_definition"}]},{begin:"\\b(new)\\b\\s+(\\()",beginCaptures:{1:{name:"keyword.fsharp"},2:{name:"keyword.symbol.fsharp"}},end:"(\\))",endCaptures:{1:{name:"keyword.symbol.fsharp"}},name:"binding.fsharp",patterns:[{include:"#common_binding_definition"}]}]},double_tick:{patterns:[{captures:{1:{name:"string.quoted.single.fsharp"},2:{name:"variable.other.binding.fsharp"},3:{name:"string.quoted.single.fsharp"}},match:"(``)([^`]*)(``)",name:"variable.other.binding.fsharp"}]},du_declaration:{patterns:[{begin:"\\b(of)\\b",beginCaptures:{1:{name:"keyword.fsharp"}},end:"$|(\\|)",endCaptures:{1:{name:"keyword.symbol.fsharp"}},name:"du_declaration.fsharp",patterns:[{include:"#comments"},{captures:{1:{name:"variable.parameter.fsharp"},2:{name:"keyword.symbol.fsharp"},3:{name:"entity.name.type.fsharp"}},match:"([A-Za-z0-9'`<>^._]+|``[A-Za-z0-9' <>^._]+``)\\s*(:)\\s*([A-Za-z0-9'`<>^._]+|``[A-Za-z0-9' <>^._]+``)"},{captures:{1:{name:"entity.name.type.fsharp"}},match:"(``([A-Za-z0-9'^._ ]+)``|[A-Za-z0-9'`^._]+)"},{include:"#anonymous_record_declaration"},{include:"#keywords"}]}]},generic_declaration:{patterns:[{begin:"(:)\\s*(\\()\\s*(static member|member)",beginCaptures:{1:{name:"keyword.symbol.fsharp"},2:{name:"keyword.symbol.fsharp"},3:{name:"keyword.fsharp"}},comments:"SRTP syntax support",end:"(\\))",endCaptures:{1:{name:"keyword.symbol.fsharp"}},patterns:[{begin:"(\\()",beginCaptures:{1:{name:"keyword.symbol.fsharp"}},end:"(\\))",endCaptures:{1:{name:"keyword.symbol.fsharp"}},patterns:[{include:"#member_declaration"}]},{captures:{1:{name:"entity.name.type.fsharp"}},match:"(('|\\^)[A-Za-z0-9'._]+)"},{include:"#variables"},{include:"#keywords"}]},{match:"\\b(private|to|public|internal|function|yield!|yield|class|exception|match|delegate|of|new|in|as|if|then|else|elif|for|begin|end|inherit|do|let!|return!|return|interface|with|abstract|enum|member|try|finally|and|when|or|use|use!|struct|while|mutable|assert|base|done|downcast|downto|extern|fixed|global|lazy|upcast|not)(?!')\\b",name:"keyword.fsharp"},{match:":",name:"keyword.symbol.fsharp"},{include:"#constants"},{captures:{1:{name:"entity.name.type.fsharp"}},match:"(('|\\^)[A-Za-z0-9'._]+)"},{begin:"(<)",beginCaptures:{1:{name:"keyword.symbol.fsharp"}},end:"(>)",endCaptures:{1:{name:"keyword.symbol.fsharp"}},patterns:[{captures:{1:{name:"entity.name.type.fsharp"}},match:"(('|\\^)[A-Za-z0-9'._]+)"},{include:"#tuple_signature"},{include:"#generic_declaration"}]},{begin:"(\\()",beginCaptures:{1:{name:"keyword.symbol.fsharp"}},end:"(\\))",endCaptures:{1:{name:"keyword.symbol.fsharp"}},patterns:[{captures:{1:{name:"entity.name.type.fsharp"}},match:"(([?A-Za-z0-9'`^._ ]+))+"},{include:"#tuple_signature"}]},{captures:{1:{name:"entity.name.type.fsharp"}},comments:"Here we need the \\w modifier in order to check that the words are allowed",match:"(?!when|and|or\\b)\\b([\\w0-9'`^._]+)"},{captures:{1:{name:"keyword.symbol.fsharp"}},comments:"Prevent captures of `|>` as a keyword when defining custom operator like `<|>`",match:"(\\|)"},{include:"#keywords"}]},keywords:{patterns:[{match:"\\b(private|public|internal)\\b",name:"storage.modifier"},{match:"\\b(private|to|public|internal|function|class|exception|delegate|of|new|as|begin|end|inherit|let!|interface|abstract|enum|member|and|when|or|use|use!|struct|mutable|assert|base|done|downcast|downto|extern|fixed|global|lazy|upcast|not)(?!')\\b",name:"keyword.fsharp"},{match:"\\b(match|yield|yield!|with|if|then|else|elif|for|in|return!|return|try|finally|while|do)(?!')\\b",name:"keyword.control"},{match:"(->|<-)",name:"keyword.symbol.arrow.fsharp"},{match:"[.?]*(&&&|\\|\\|\\||\\^\\^\\^|~~~|~\\+|~-|<<<|>>>|\\|>|:>|:\\?>|:|\\[|\\]|\\;|<>|=|@|\\|\\||&&|&|%|{|}|\\||_|\\.\\.|\\,|\\+|-|\\*|\\/|\\^|!|>|>=|>>|<|<=|\\(|\\)|<<)[.?]*",name:"keyword.symbol.fsharp"}]},member_declaration:{patterns:[{include:"#comments"},{include:"#common_declaration"},{begin:"(:)\\s*(\\()\\s*(static member|member)",beginCaptures:{1:{name:"keyword.symbol.fsharp"},2:{name:"keyword.symbol.fsharp"},3:{name:"keyword.fsharp"}},comments:"SRTP syntax support",end:"(\\))\\s*((?=,)|(?==))",endCaptures:{1:{name:"keyword.symbol.fsharp"}},patterns:[{begin:"(\\()",beginCaptures:{1:{name:"keyword.symbol.fsharp"}},end:"(\\))",endCaptures:{1:{name:"keyword.symbol.fsharp"}},patterns:[{include:"#member_declaration"}]},{captures:{1:{name:"entity.name.type.fsharp"}},match:"(\\^[A-Za-z0-9'._]+)"},{include:"#variables"},{include:"#keywords"}]},{captures:{1:{name:"entity.name.type.fsharp"}},match:"(\\^[A-Za-z0-9'._]+)"},{match:"\\b(and|when|or)\\b",name:"keyword.fsharp"},{match:"(\\(|\\))",name:"keyword.symbol.fsharp"},{captures:{1:{name:"keyword.symbol.fsharp"},2:{name:"variable.parameter.fsharp"},3:{name:"keyword.symbol.fsharp"},4:{name:"entity.name.type.fsharp"}},match:"(\\?{0,1})([A-Za-z0-9'`^._]+|``[A-Za-z0-9'`^:,._ ]+``)\\s*(:{0,1})(\\s*([?A-Za-z0-9'`<>._ ]+)){0,1}"},{include:"#keywords"}]},modules:{patterns:[{begin:"\\b(namespace global)|\\b(namespace|module)\\s*(public|internal|private|rec)?\\s+([A-Za-z|``][A-Za-z0-9'_. ]*)",beginCaptures:{1:{name:"keyword.fsharp"},2:{name:"keyword.fsharp"},3:{name:"storage.modifier.fsharp"},4:{name:"entity.name.section.fsharp"}},end:"(\\s?=|\\s|$)",endCaptures:{1:{name:"keyword.symbol.fsharp"}},name:"entity.name.section.fsharp",patterns:[{captures:{1:{name:"punctuation.separator.namespace-reference.fsharp"},2:{name:"entity.name.section.fsharp"}},match:"(\\.)([A-Z][A-Za-z0-9'_]*)",name:"entity.name.section.fsharp"}]},{begin:"\\b(open type|open)\\s+([A-Za-z|``][A-Za-z0-9'_]*)(?=(\\.[A-Z][A-Za-z0-9_]*)*)",beginCaptures:{1:{name:"keyword.fsharp"},2:{name:"entity.name.section.fsharp"}},end:"(\\s|$)",name:"namespace.open.fsharp",patterns:[{captures:{1:{name:"punctuation.separator.namespace-reference.fsharp"},2:{name:"entity.name.section.fsharp"}},match:"(\\.)([A-Za-z][A-Za-z0-9'_]*)",name:"entity.name.section.fsharp"},{include:"#comments"}]},{begin:"^\\s*(module)\\s+([A-Z][A-Za-z0-9'_]*)\\s*(=)\\s*([A-Z][A-Za-z0-9'_]*)",beginCaptures:{1:{name:"keyword.fsharp"},2:{name:"entity.name.type.namespace.fsharp"},3:{name:"keyword.symbol.fsharp"},4:{name:"entity.name.section.fsharp"}},end:"(\\s|$)",name:"namespace.alias.fsharp",patterns:[{captures:{1:{name:"punctuation.separator.namespace-reference.fsharp"},2:{name:"entity.name.section.fsharp"}},match:"(\\.)([A-Z][A-Za-z0-9'_]*)",name:"entity.name.section.fsharp"}]}]},record_declaration:{patterns:[{begin:"(\\{)",beginCaptures:{1:{name:"keyword.symbol.fsharp"}},end:"(?<=\\})",patterns:[{include:"#comments"},{begin:"(((mutable)\\s[A-Za-z]+)|[A-Za-z0-9'`<>^._]*)\\s*((?<!:):(?!:))\\s*",beginCaptures:{3:{name:"keyword.fsharp"},4:{name:"keyword.symbol.fsharp"}},end:"$|(;|\\})",endCaptures:{1:{name:"keyword.symbol.fsharp"}},patterns:[{include:"#comments"},{captures:{1:{name:"entity.name.type.fsharp"}},match:"([A-Za-z0-9'`^_ ]+)"},{include:"#keywords"}]},{include:"#compiler_directives"},{include:"#constants"},{include:"#strings"},{include:"#chars"},{include:"#double_tick"},{include:"#definition"},{include:"#attributes"},{include:"#anonymous_functions"},{include:"#keywords"},{include:"#cexprs"},{include:"#text"}]}]},record_signature:{patterns:[{captures:{1:{name:"keyword.symbol.fsharp"},2:{name:"variable.parameter.fsharp"}},match:"[A-Za-z0-9'`^_ ]+(=)([A-Za-z0-9'`^_ ]+)"},{begin:"({)",beginCaptures:{1:{name:"keyword.symbol.fsharp"}},end:"(})",endCaptures:{1:{name:"keyword.symbol.fsharp"}},patterns:[{captures:{1:{name:"keyword.symbol.fsharp"},2:{name:"variable.parameter.fsharp"}},match:"[A-Za-z0-9'`^_ ]+(=)([A-Za-z0-9'`^_ ]+)"},{include:"#record_signature"}]},{include:"#keywords"}]},records:{patterns:[{begin:"\\b(type)[\\s]+(private|internal|public)?\\s*",beginCaptures:{1:{name:"keyword.fsharp"},2:{name:"storage.modifier.fsharp"}},end:"\\s*((with)|((as)\\s+([A-Za-z0-9']+))|(=)|[\\n=]|(\\(\\)))",endCaptures:{2:{name:"keyword.fsharp"},3:{name:"keyword.fsharp"},4:{name:"keyword.fsharp"},5:{name:"variable.parameter.fsharp"},6:{name:"keyword.symbol.fsharp"},7:{name:"keyword.symbol.fsharp"}},name:"record.fsharp",patterns:[{include:"#comments"},{include:"#attributes"},{captures:{1:{name:"entity.name.type.fsharp"}},match:"([A-Za-z0-9'^._]+|``[A-Za-z0-9'`^:,._ ]+``)"},{begin:"(<)",beginCaptures:{1:{name:"keyword.symbol.fsharp"}},end:"((?<!:)>)",endCaptures:{1:{name:"keyword.symbol.fsharp"}},patterns:[{captures:{1:{name:"entity.name.type.fsharp"}},match:"(('|\\^)``[A-Za-z0-9`^:,._ ]+``|('|\\^)[A-Za-z0-9`^:._]+)"},{match:"\\b(interface|with|abstract|and|when|or|not|struct|equality|comparison|unmanaged|delegate|enum)\\b",name:"keyword.fsharp"},{begin:"(\\()",beginCaptures:{1:{name:"keyword.symbol.fsharp"}},end:"(\\))",endCaptures:{1:{name:"keyword.symbol.fsharp"}},patterns:[{captures:{1:{name:"keyword.fsharp"}},match:"(static member|member|new)"},{include:"#common_binding_definition"}]},{captures:{1:{name:"entity.name.type.fsharp"}},comments:"Here we need the \\w modifier in order to check that the words isn't blacklisted",match:"([\\w0-9'`^._]+)"},{include:"#keywords"}]},{captures:{1:{name:"storage.modifier.fsharp"}},match:"\\s*(private|internal|public)"},{begin:"(\\()",beginCaptures:{1:{name:"keyword.symbol.fsharp"}},end:"\\s*(?=(=)|[\\n=]|(\\(\\))|(as))",endCaptures:{1:{name:"keyword.symbol.fsharp"}},patterns:[{include:"#member_declaration"}]},{include:"#keywords"}]}]},string_formatter:{patterns:[{captures:{1:{name:"keyword.format.specifier.fsharp"}},match:"(%0?-?(\\d+)?((a|t)|(\\.\\d+)?(f|F|e|E|g|G|M)|(b|c|s|d|i|x|X|o|u)|(s|b|O)|(\\+?A)))",name:"entity.name.type.format.specifier.fsharp"}]},strings:{patterns:[{begin:'(?=[^\\\\])(@")',beginCaptures:{1:{name:"punctuation.definition.string.begin.fsharp"}},end:'(")(?!")',endCaptures:{1:{name:"punctuation.definition.string.end.fsharp"}},name:"string.quoted.literal.fsharp",patterns:[{match:'"(")',name:"constant.character.string.escape.fsharp"}]},{begin:'(?=[^\\\\])(""")',beginCaptures:{1:{name:"punctuation.definition.string.begin.fsharp"}},end:'(""")',endCaptures:{1:{name:"punctuation.definition.string.end.fsharp"}},name:"string.quoted.triple.fsharp",patterns:[{include:"#string_formatter"}]},{begin:'(?=[^\\\\])(")',beginCaptures:{1:{name:"punctuation.definition.string.begin.fsharp"}},end:'(")',endCaptures:{1:{name:"punctuation.definition.string.end.fsharp"}},name:"string.quoted.double.fsharp",patterns:[{match:"\\\\$[ \\t]*",name:"punctuation.separator.string.ignore-eol.fsharp"},{match:"\\\\(['\"\\\\abfnrtv]|([01]\\d\\d|2[0-4]\\d|25[0-5])|(x[0-9a-fA-F]{2})|(u[0-9a-fA-F]{4})|(U00(0[0-9a-fA-F]|10)[0-9a-fA-F]{4}))",name:"constant.character.string.escape.fsharp"},{match:"\\\\((\\d{1,3})|(x[^\\s]{0,2})|(u[^\\s]{0,4})|(U[^\\s]{0,8})|[^\\s])",name:"invalid.illegal.character.string.fsharp"},{include:"#string_formatter"}]}]},strp_inlined:{patterns:[{begin:"(\\()",beginCaptures:{1:{name:"keyword.symbol.fsharp"}},end:"(\\))",endCaptures:{1:{name:"keyword.symbol.fsharp"}},patterns:[{include:"#strp_inlined_body"}]}]},strp_inlined_body:{patterns:[{include:"#comments"},{include:"#anonymous_functions"},{captures:{1:{name:"entity.name.type.fsharp"}},match:"(\\^[A-Za-z0-9'._]+)"},{match:"\\b(and|when|or)\\b",name:"keyword.fsharp"},{begin:"(\\()",beginCaptures:{1:{name:"keyword.symbol.fsharp"}},end:"(\\))",endCaptures:{1:{name:"keyword.symbol.fsharp"}},patterns:[{include:"#strp_inlined_body"}]},{captures:{1:{name:"keyword.fsharp"},2:{name:"variable.fsharp"},3:{name:"keyword.symbol.fsharp"}},match:"(static member|member)\\s*([A-Za-z0-9'`<>^._]+|``[A-Za-z0-9' <>^._]+``)\\s*(:)"},{include:"#compiler_directives"},{include:"#constants"},{include:"#strings"},{include:"#chars"},{include:"#double_tick"},{include:"#keywords"},{include:"#text"},{include:"#definition"},{include:"#attributes"},{include:"#keywords"},{include:"#cexprs"},{include:"#text"}]},text:{patterns:[{match:"\\\\",name:"text.fsharp"}]},tuple_signature:{patterns:[{captures:{1:{name:"entity.name.type.fsharp"}},match:"(([?A-Za-z0-9'`^._ ]+))+"},{begin:"(\\()",beginCaptures:{1:{name:"keyword.symbol.fsharp"}},end:"(\\))",endCaptures:{1:{name:"keyword.symbol.fsharp"}},patterns:[{captures:{1:{name:"entity.name.type.fsharp"}},match:"(([?A-Za-z0-9'`^._ ]+))+"},{include:"#tuple_signature"}]},{include:"#keywords"}]},variables:{patterns:[{match:"\\(\\)",name:"keyword.symbol.fsharp"},{captures:{1:{name:"keyword.symbol.fsharp"},2:{name:"variable.parameter.fsharp"}},match:"(\\?{0,1})(``[A-Za-z0-9'`^:,._ ]+``|(?!private|struct\\b)\\b[\\wA-Za-z0-9'`<>^._ ]+)"}]}},scopeName:"source.fsharp",embeddedLangs:["markdown"],aliases:["f#","fs"]});var r=[...a.default,i]},86636:(e,t,n)=>{n.r(t),n.d(t,{default:()=>m});var a=n(14055),i=n(98013),r=n(63069),s=n(25253);let o=Object.freeze({displayName:"Handlebars",name:"handlebars",patterns:[{include:"#yfm"},{include:"#extends"},{include:"#block_comments"},{include:"#comments"},{include:"#block_helper"},{include:"#end_block"},{include:"#else_token"},{include:"#partial_and_var"},{include:"#inline_script"},{include:"#html_tags"},{include:"text.html.basic"}],repository:{block_comments:{patterns:[{begin:"\\{\\{!--",end:"--\\}\\}",name:"comment.block.handlebars",patterns:[{match:"@\\w*",name:"keyword.annotation.handlebars"},{include:"#comments"}]},{begin:"\x3c!--",captures:{0:{name:"punctuation.definition.comment.html"}},end:"-{2,3}\\s*>",name:"comment.block.html",patterns:[{match:"--",name:"invalid.illegal.bad-comments-or-CDATA.html"}]}]},block_helper:{begin:"(\\{\\{)(~?\\#)([-a-zA-Z0-9_\\./>]+)\\s?(@?[-a-zA-Z0-9_\\./]+)*\\s?(@?[-a-zA-Z0-9_\\./]+)*\\s?(@?[-a-zA-Z0-9_\\./]+)*",beginCaptures:{1:{name:"support.constant.handlebars"},2:{name:"support.constant.handlebars keyword.control"},3:{name:"support.constant.handlebars keyword.control"},4:{name:"variable.parameter.handlebars"},5:{name:"support.constant.handlebars"},6:{name:"variable.parameter.handlebars"},7:{name:"support.constant.handlebars"}},end:"(~?\\}\\})",endCaptures:{1:{name:"support.constant.handlebars"}},name:"meta.function.block.start.handlebars",patterns:[{include:"#string"},{include:"#handlebars_attribute"}]},comments:{patterns:[{begin:"\\{\\{!",end:"\\}\\}",name:"comment.block.handlebars",patterns:[{match:"@\\w*",name:"keyword.annotation.handlebars"},{include:"#comments"}]},{begin:"\x3c!--",captures:{0:{name:"punctuation.definition.comment.html"}},end:"-{2,3}\\s*>",name:"comment.block.html",patterns:[{match:"--",name:"invalid.illegal.bad-comments-or-CDATA.html"}]}]},else_token:{begin:"(\\{\\{)(~?else)(@?\\s(if)\\s([-a-zA-Z0-9_\\.(\\s)/]+))?",beginCaptures:{1:{name:"support.constant.handlebars"},2:{name:"support.constant.handlebars keyword.control"},3:{name:"support.constant.handlebars"},4:{name:"variable.parameter.handlebars"}},end:"(~?\\}\\}\\}*)",endCaptures:{1:{name:"support.constant.handlebars"}},name:"meta.function.inline.else.handlebars"},end_block:{begin:"(\\{\\{)(~?/)([a-zA-Z0-9/_\\.-]+)\\s*",beginCaptures:{1:{name:"support.constant.handlebars"},2:{name:"support.constant.handlebars keyword.control"},3:{name:"support.constant.handlebars keyword.control"}},end:"(~?\\}\\})",endCaptures:{1:{name:"support.constant.handlebars"}},name:"meta.function.block.end.handlebars",patterns:[]},entities:{patterns:[{captures:{1:{name:"punctuation.definition.entity.html"},3:{name:"punctuation.definition.entity.html"}},match:"(&)([a-zA-Z0-9]+|#\\d+|#x[0-9a-fA-F]+)(;)",name:"constant.character.entity.html"},{match:"&",name:"invalid.illegal.bad-ampersand.html"}]},"escaped-double-quote":{match:'\\\\"',name:"constant.character.escape.js"},"escaped-single-quote":{match:"\\\\'",name:"constant.character.escape.js"},extends:{patterns:[{begin:"(\\{\\{!<)\\s([-a-zA-Z0-9_\\./]+)",beginCaptures:{1:{name:"support.function.handlebars"},2:{name:"support.class.handlebars"}},end:"(\\}\\})",endCaptures:{1:{name:"support.function.handlebars"}},name:"meta.preprocessor.handlebars"}]},handlebars_attribute:{patterns:[{include:"#handlebars_attribute_name"},{include:"#handlebars_attribute_value"}]},handlebars_attribute_name:{begin:"\\b([-a-zA-Z0-9_\\.]+)\\b=",captures:{1:{name:"variable.parameter.handlebars"}},end:"(?='|\"|)",name:"entity.other.attribute-name.handlebars"},handlebars_attribute_value:{begin:"([-a-zA-Z0-9_\\./]+)\\b",captures:{1:{name:"variable.parameter.handlebars"}},end:"('|\"|)",name:"entity.other.attribute-value.handlebars",patterns:[{include:"#string"}]},html_tags:{patterns:[{begin:"(<)([a-zA-Z0-9:-]+)(?=[^>]*></\\2>)",beginCaptures:{1:{name:"punctuation.definition.tag.html"},2:{name:"entity.name.tag.html"}},end:"(>(<)/)(\\2)(>)",endCaptures:{1:{name:"punctuation.definition.tag.html"},2:{name:"meta.scope.between-tag-pair.html"},3:{name:"entity.name.tag.html"},4:{name:"punctuation.definition.tag.html"}},name:"meta.tag.any.html",patterns:[{include:"#tag-stuff"}]},{begin:"(<\\?)(xml)",captures:{1:{name:"punctuation.definition.tag.html"},2:{name:"entity.name.tag.xml.html"}},end:"(\\?>)",name:"meta.tag.preprocessor.xml.html",patterns:[{include:"#tag_generic_attribute"},{include:"#string"}]},{begin:"\x3c!--",captures:{0:{name:"punctuation.definition.comment.html"}},end:"--\\s*>",name:"comment.block.html",patterns:[{match:"--",name:"invalid.illegal.bad-comments-or-CDATA.html"}]},{begin:"<!",captures:{0:{name:"punctuation.definition.tag.html"}},end:">",name:"meta.tag.sgml.html",patterns:[{begin:"(DOCTYPE|doctype)",captures:{1:{name:"entity.name.tag.doctype.html"}},end:"(?=>)",name:"meta.tag.sgml.doctype.html",patterns:[{match:'"[^">]*"',name:"string.quoted.double.doctype.identifiers-and-DTDs.html"}]},{begin:"\\[CDATA\\[",end:"]](?=>)",name:"constant.other.inline-data.html"},{match:"(\\s*)(?!--|>)\\S(\\s*)",name:"invalid.illegal.bad-comments-or-CDATA.html"}]},{begin:"(?:^\\s+)?(<)((?i:style))\\b(?![^>]*/>)",captures:{1:{name:"punctuation.definition.tag.html"},2:{name:"entity.name.tag.style.html"},3:{name:"punctuation.definition.tag.html"}},end:"(</)((?i:style))(>)(?:\\s*\\n)?",name:"source.css.embedded.html",patterns:[{include:"#tag-stuff"},{begin:"(>)",beginCaptures:{1:{name:"punctuation.definition.tag.html"}},end:"(?=</(?i:style))",patterns:[{include:"source.css"}]}]},{begin:"(?:^\\s+)?(<)((?i:script))\\b(?![^>]*/>)",beginCaptures:{1:{name:"punctuation.definition.tag.html"},2:{name:"entity.name.tag.script.html"}},end:"(?<=</(script|SCRIPT))(>)(?:\\s*\\n)?",endCaptures:{2:{name:"punctuation.definition.tag.html"}},name:"source.js.embedded.html",patterns:[{include:"#tag-stuff"},{begin:"(?<!</(?:script|SCRIPT))(>)",captures:{1:{name:"punctuation.definition.tag.html"},2:{name:"entity.name.tag.script.html"}},end:"(</)((?i:script))",patterns:[{captures:{1:{name:"punctuation.definition.comment.js"}},match:"(//).*?((?=</script)|$\\n?)",name:"comment.line.double-slash.js"},{begin:"/\\*",captures:{0:{name:"punctuation.definition.comment.js"}},end:"\\*/|(?=</script)",name:"comment.block.js"},{include:"source.js"}]}]},{begin:"(</?)((?i:body|head|html)\\b)",captures:{1:{name:"punctuation.definition.tag.html"},2:{name:"entity.name.tag.structure.any.html"}},end:"(>)",name:"meta.tag.structure.any.html",patterns:[{include:"#tag-stuff"}]},{begin:"(</?)((?i:address|blockquote|dd|div|header|section|footer|aside|nav|dl|dt|fieldset|form|frame|frameset|h1|h2|h3|h4|h5|h6|iframe|noframes|object|ol|p|ul|applet|center|dir|hr|menu|pre)\\b)",captures:{1:{name:"punctuation.definition.tag.html"},2:{name:"entity.name.tag.block.any.html"}},end:"(>)",name:"meta.tag.block.any.html",patterns:[{include:"#tag-stuff"}]},{begin:"(</?)((?i:a|abbr|acronym|area|b|base|basefont|bdo|big|br|button|caption|cite|code|col|colgroup|del|dfn|em|font|head|html|i|img|input|ins|isindex|kbd|label|legend|li|link|map|meta|noscript|optgroup|option|param|q|s|samp|script|select|small|span|strike|strong|style|sub|sup|table|tbody|td|textarea|tfoot|th|thead|title|tr|tt|u|var)\\b)",captures:{1:{name:"punctuation.definition.tag.html"},2:{name:"entity.name.tag.inline.any.html"}},end:"((?: ?/)?>)",name:"meta.tag.inline.any.html",patterns:[{include:"#tag-stuff"}]},{begin:"(</?)([a-zA-Z0-9:-]+)",captures:{1:{name:"punctuation.definition.tag.html"},2:{name:"entity.name.tag.other.html"}},end:"(>)",name:"meta.tag.other.html",patterns:[{include:"#tag-stuff"}]},{begin:"(</?)([a-zA-Z0-9{}:-]+)",captures:{1:{name:"punctuation.definition.tag.html"},2:{name:"entity.name.tag.tokenised.html"}},end:"(>)",name:"meta.tag.tokenised.html",patterns:[{include:"#tag-stuff"}]},{include:"#entities"},{match:"<>",name:"invalid.illegal.incomplete.html"},{match:"<",name:"invalid.illegal.bad-angle-bracket.html"}]},inline_script:{begin:"(?:^\\s+)?(<)((?i:script))\\b(?:.*(type)=([\"'](?:text/x-handlebars-template|text/x-handlebars|text/template|x-tmpl-handlebars)[\"']))(?![^>]*/>)",beginCaptures:{1:{name:"punctuation.definition.tag.html"},2:{name:"entity.name.tag.script.html"},3:{name:"entity.other.attribute-name.html"},4:{name:"string.quoted.double.html"}},end:"(?<=</(script|SCRIPT))(>)(?:\\s*\\n)?",endCaptures:{2:{name:"punctuation.definition.tag.html"}},name:"source.handlebars.embedded.html",patterns:[{include:"#tag-stuff"},{begin:"(?<!</(?:script|SCRIPT))(>)",captures:{1:{name:"punctuation.definition.tag.html"},2:{name:"entity.name.tag.script.html"}},end:"(</)((?i:script))",patterns:[{include:"#block_comments"},{include:"#comments"},{include:"#block_helper"},{include:"#end_block"},{include:"#else_token"},{include:"#partial_and_var"},{include:"#html_tags"},{include:"text.html.basic"}]}]},partial_and_var:{begin:"(\\{\\{~?\\{*(>|!<)*)\\s*(@?[-a-zA-Z0-9$_\\./]+)*",beginCaptures:{1:{name:"support.constant.handlebars"},3:{name:"variable.parameter.handlebars"}},end:"(~?\\}\\}\\}*)",endCaptures:{1:{name:"support.constant.handlebars"}},name:"meta.function.inline.other.handlebars",patterns:[{include:"#string"},{include:"#handlebars_attribute"}]},string:{patterns:[{include:"#string-single-quoted"},{include:"#string-double-quoted"}]},"string-double-quoted":{begin:'"',beginCaptures:{0:{name:"punctuation.definition.string.begin.html"}},end:'"',endCaptures:{0:{name:"punctuation.definition.string.end.html"}},name:"string.quoted.double.handlebars",patterns:[{include:"#escaped-double-quote"},{include:"#block_comments"},{include:"#comments"},{include:"#block_helper"},{include:"#else_token"},{include:"#end_block"},{include:"#partial_and_var"}]},"string-single-quoted":{begin:"'",beginCaptures:{0:{name:"punctuation.definition.string.begin.html"}},end:"'",endCaptures:{0:{name:"punctuation.definition.string.end.html"}},name:"string.quoted.single.handlebars",patterns:[{include:"#escaped-single-quote"},{include:"#block_comments"},{include:"#comments"},{include:"#block_helper"},{include:"#else_token"},{include:"#end_block"},{include:"#partial_and_var"}]},"tag-stuff":{patterns:[{include:"#tag_id_attribute"},{include:"#tag_generic_attribute"},{include:"#string"},{include:"#block_comments"},{include:"#comments"},{include:"#block_helper"},{include:"#end_block"},{include:"#else_token"},{include:"#partial_and_var"}]},tag_generic_attribute:{begin:"\\b([a-zA-Z0-9_-]+)\\b\\s*(=)",captures:{1:{name:"entity.other.attribute-name.generic.html"},2:{name:"punctuation.separator.key-value.html"}},end:"(?<='|\"|)",name:"entity.other.attribute-name.html",patterns:[{include:"#string"}]},tag_id_attribute:{begin:"\\b(id)\\b\\s*(=)",captures:{1:{name:"entity.other.attribute-name.id.html"},2:{name:"punctuation.separator.key-value.html"}},end:"(?<='|\"|)",name:"meta.attribute-with-value.id.html",patterns:[{include:"#string"}]},yfm:{patterns:[{begin:"(?<!\\s)---\\n$",end:"^---\\s",name:"markup.raw.yaml.front-matter",patterns:[{include:"source.yaml"}]}]}},scopeName:"text.html.handlebars",embeddedLangs:["html","css","javascript","yaml"],aliases:["hbs"]});var m=[...a.default,...i.default,...r.default,...s.default,o]},90349:(e,t,n)=>{n.r(t),n.d(t,{default:()=>a});var a=[Object.freeze({displayName:"Diff",name:"diff",patterns:[{captures:{1:{name:"punctuation.definition.separator.diff"}},match:"^((\\*{15})|(={67})|(-{3}))$\\n?",name:"meta.separator.diff"},{match:"^\\d+(,\\d+)*(a|d|c)\\d+(,\\d+)*$\\n?",name:"meta.diff.range.normal"},{captures:{1:{name:"punctuation.definition.range.diff"},2:{name:"meta.toc-list.line-number.diff"},3:{name:"punctuation.definition.range.diff"}},match:"^(@@)\\s*(.+?)\\s*(@@)($\\n?)?",name:"meta.diff.range.unified"},{captures:{3:{name:"punctuation.definition.range.diff"},4:{name:"punctuation.definition.range.diff"},6:{name:"punctuation.definition.range.diff"},7:{name:"punctuation.definition.range.diff"}},match:"^(((-{3}) .+ (-{4}))|((\\*{3}) .+ (\\*{4})))$\\n?",name:"meta.diff.range.context"},{match:"^diff --git a/.*$\\n?",name:"meta.diff.header.git"},{match:"^diff (-|\\S+\\s+\\S+).*$\\n?",name:"meta.diff.header.command"},{captures:{4:{name:"punctuation.definition.from-file.diff"},6:{name:"punctuation.definition.from-file.diff"},7:{name:"punctuation.definition.from-file.diff"}},match:"(^(((-{3}) .+)|((\\*{3}) .+))$\\n?|^(={4}) .+(?= - ))",name:"meta.diff.header.from-file"},{captures:{2:{name:"punctuation.definition.to-file.diff"},3:{name:"punctuation.definition.to-file.diff"},4:{name:"punctuation.definition.to-file.diff"}},match:"(^(\\+{3}) .+$\\n?| (-) .* (={4})$\\n?)",name:"meta.diff.header.to-file"},{captures:{3:{name:"punctuation.definition.inserted.diff"},6:{name:"punctuation.definition.inserted.diff"}},match:"^(((>)( .*)?)|((\\+).*))$\\n?",name:"markup.inserted.diff"},{captures:{1:{name:"punctuation.definition.changed.diff"}},match:"^(!).*$\\n?",name:"markup.changed.diff"},{captures:{3:{name:"punctuation.definition.deleted.diff"},6:{name:"punctuation.definition.deleted.diff"}},match:"^(((<)( .*)?)|((-).*))$\\n?",name:"markup.deleted.diff"},{begin:"^(#)",captures:{1:{name:"punctuation.definition.comment.diff"}},comment:'Git produces unified diffs with embedded comments"',end:"\\n",name:"comment.line.number-sign.diff"},{match:"^index [0-9a-f]{7,40}\\.\\.[0-9a-f]{7,40}.*$\\n?",name:"meta.diff.index.git"},{captures:{1:{name:"punctuation.separator.key-value.diff"},2:{name:"meta.toc-list.file-name.diff"}},match:"^Index(:) (.+)$\\n?",name:"meta.diff.index"},{match:"^Only in .*: .*$\\n?",name:"meta.diff.only-in"}],scopeName:"source.diff"})]},91449:(e,t,n)=>{n.r(t),n.d(t,{default:()=>a});var a=[Object.freeze({displayName:"Batch File",injections:{"L:meta.block.repeat.batchfile":{patterns:[{include:"#repeatParameter"}]}},name:"bat",patterns:[{include:"#commands"},{include:"#comments"},{include:"#constants"},{include:"#controls"},{include:"#escaped_characters"},{include:"#labels"},{include:"#numbers"},{include:"#operators"},{include:"#parens"},{include:"#strings"},{include:"#variables"}],repository:{command_set:{patterns:[{begin:"(?<=^|[\\s@])(?i:SET)(?=$|\\s)",beginCaptures:{0:{name:"keyword.command.batchfile"}},end:"(?=$\\n|[&|><)])",patterns:[{include:"#command_set_inside"}]}]},command_set_group:{patterns:[{begin:"\\(",beginCaptures:{0:{name:"punctuation.section.group.begin.batchfile"}},end:"\\)",endCaptures:{0:{name:"punctuation.section.group.end.batchfile"}},patterns:[{include:"#command_set_inside_arithmetic"}]}]},command_set_inside:{patterns:[{include:"#escaped_characters"},{include:"#variables"},{include:"#numbers"},{include:"#parens"},{include:"#command_set_strings"},{include:"#strings"},{begin:"([^ ][^=]*)(=)",beginCaptures:{1:{name:"variable.other.readwrite.batchfile"},2:{name:"keyword.operator.assignment.batchfile"}},end:"(?=$\\n|[&|><)])",patterns:[{include:"#escaped_characters"},{include:"#variables"},{include:"#numbers"},{include:"#parens"},{include:"#strings"}]},{begin:"\\s+/[aA]\\s+",end:"(?=$\\n|[&|><)])",name:"meta.expression.set.batchfile",patterns:[{begin:'"',beginCaptures:{0:{name:"punctuation.definition.string.begin.batchfile"}},end:'"',endCaptures:{0:{name:"punctuation.definition.string.end.batchfile"}},name:"string.quoted.double.batchfile",patterns:[{include:"#command_set_inside_arithmetic"},{include:"#command_set_group"},{include:"#variables"}]},{include:"#command_set_inside_arithmetic"},{include:"#command_set_group"}]},{begin:"\\s+/[pP]\\s+",end:"(?=$\\n|[&|><)])",patterns:[{include:"#command_set_strings"},{begin:"([^ ][^=]*)(=)",beginCaptures:{1:{name:"variable.other.readwrite.batchfile"},2:{name:"keyword.operator.assignment.batchfile"}},end:"(?=$\\n|[&|><)])",name:"meta.prompt.set.batchfile",patterns:[{include:"#strings"}]}]}]},command_set_inside_arithmetic:{patterns:[{include:"#command_set_operators"},{include:"#numbers"},{match:",",name:"punctuation.separator.batchfile"}]},command_set_operators:{patterns:[{captures:{1:{name:"variable.other.readwrite.batchfile"},2:{name:"keyword.operator.assignment.augmented.batchfile"}},match:"([^ ]*)(\\+=|-=|\\*=|\\/=|%%=|&=|\\|=|\\^=|<<=|>>=)"},{match:"\\+|-|/|\\*|%%|\\||&|\\^|<<|>>|~",name:"keyword.operator.arithmetic.batchfile"},{match:"!",name:"keyword.operator.logical.batchfile"},{captures:{1:{name:"variable.other.readwrite.batchfile"},2:{name:"keyword.operator.assignment.batchfile"}},match:"([^ =]*)(=)"}]},command_set_strings:{patterns:[{begin:'(")\\s*([^ ][^=]*)(=)',beginCaptures:{1:{name:"punctuation.definition.string.begin.batchfile"},2:{name:"variable.other.readwrite.batchfile"},3:{name:"keyword.operator.assignment.batchfile"}},end:'"',endCaptures:{0:{name:"punctuation.definition.string.end.batchfile"}},name:"string.quoted.double.batchfile",patterns:[{include:"#variables"},{include:"#numbers"},{include:"#escaped_characters"}]}]},commands:{patterns:[{match:"(?<=^|[\\s@])(?i:adprep|append|arp|assoc|at|atmadm|attrib|auditpol|autochk|autoconv|autofmt|bcdboot|bcdedit|bdehdcfg|bitsadmin|bootcfg|brea|cacls|cd|certreq|certutil|change|chcp|chdir|chglogon|chgport|chgusr|chkdsk|chkntfs|choice|cipher|clip|cls|clscluadmin|cluster|cmd|cmdkey|cmstp|color|comp|compact|convert|copy|cprofile|cscript|csvde|date|dcdiag|dcgpofix|dcpromo|defra|del|dfscmd|dfsdiag|dfsrmig|diantz|dir|dirquota|diskcomp|diskcopy|diskpart|diskperf|diskraid|diskshadow|dispdiag|doin|dnscmd|doskey|driverquery|dsacls|dsadd|dsamain|dsdbutil|dsget|dsmgmt|dsmod|dsmove|dsquery|dsrm|edit|endlocal|eraseesentutl|eventcreate|eventquery|eventtriggers|evntcmd|expand|extract|fc|filescrn|find|findstr|finger|flattemp|fonde|forfiles|format|freedisk|fsutil|ftp|ftype|fveupdate|getmac|gettype|gpfixup|gpresult|gpupdate|graftabl|hashgen|hep|helpctr|hostname|icacls|iisreset|inuse|ipconfig|ipxroute|irftp|ismserv|jetpack|klist|ksetup|ktmutil|ktpass|label|ldifd|ldp|lodctr|logman|logoff|lpq|lpr|macfile|makecab|manage-bde|mapadmin|md|mkdir|mklink|mmc|mode|more|mount|mountvol|move|mqbup|mqsvc|mqtgsvc|msdt|msg|msiexec|msinfo32|mstsc|nbtstat|net computer|net group|net localgroup|net print|net session|net share|net start|net stop|net use|net user|net view|net|netcfg|netdiag|netdom|netsh|netstat|nfsadmin|nfsshare|nfsstat|nlb|nlbmgr|nltest|nslookup|ntackup|ntcmdprompt|ntdsutil|ntfrsutl|openfiles|pagefileconfig|path|pathping|pause|pbadmin|pentnt|perfmon|ping|pnpunatten|pnputil|popd|powercfg|powershell|powershell_ise|print|prncnfg|prndrvr|prnjobs|prnmngr|prnport|prnqctl|prompt|pubprn|pushd|pushprinterconnections|pwlauncher|qappsrv|qprocess|query|quser|qwinsta|rasdial|rcp|rd|rdpsign|regentc|recover|redircmp|redirusr|reg|regini|regsvr32|relog|ren|rename|rendom|repadmin|repair-bde|replace|reset session|rxec|risetup|rmdir|robocopy|route|rpcinfo|rpcping|rsh|runas|rundll32|rwinsta|sc|schtasks|scp|scwcmd|secedit|serverceipoptin|servrmanagercmd|serverweroptin|setspn|setx|sfc|sftp|shadow|shift|showmount|shutdown|sort|ssh|ssh-add|ssh-agent|ssh-keygen|ssh-keyscan|start|storrept|subst|sxstrace|ysocmgr|systeminfo|takeown|tapicfg|taskkill|tasklist|tcmsetup|telnet|tftp|time|timeout|title|tlntadmn|tpmvscmgr|tpmvscmgr|tacerpt|tracert|tree|tscon|tsdiscon|tsecimp|tskill|tsprof|type|typeperf|tzutil|uddiconfig|umount|unlodctr|ver|verifier|verif|vol|vssadmin|w32tm|waitfor|wbadmin|wdsutil|wecutil|wevtutil|where|whoami|winnt|winnt32|winpop|winrm|winrs|winsat|wlbs|wmic|wscript|wsl|xcopy)(?=$|\\s)",name:"keyword.command.batchfile"},{begin:"(?i)(?<=^|[\\s@])(echo)(?:(?=$|\\.|:)|\\s+(?:(on|off)(?=\\s*$))?)",beginCaptures:{1:{name:"keyword.command.batchfile"},2:{name:"keyword.other.special-method.batchfile"}},end:"(?=$\\n|[&|><)])",patterns:[{include:"#escaped_characters"},{include:"#variables"},{include:"#numbers"},{include:"#strings"}]},{captures:{1:{name:"keyword.command.batchfile"},2:{name:"keyword.other.special-method.batchfile"}},match:"(?i)(?<=^|[\\s@])(setlocal)(?:\\s*$|\\s+(EnableExtensions|DisableExtensions|EnableDelayedExpansion|DisableDelayedExpansion)(?=\\s*$))"},{include:"#command_set"}]},comments:{patterns:[{begin:"(?:^|(&))\\s*(?=((?::[+=,;: ])))",beginCaptures:{1:{name:"keyword.operator.conditional.batchfile"}},end:"\\n",patterns:[{begin:"((?::[+=,;: ]))",beginCaptures:{1:{name:"punctuation.definition.comment.batchfile"}},end:"(?=\\n)",name:"comment.line.colon.batchfile"}]},{begin:"(?<=^|[\\s@])(?i)(REM)(\\.)",beginCaptures:{1:{name:"keyword.command.rem.batchfile"},2:{name:"punctuation.separator.batchfile"}},end:"(?=$\\n|[&|><)])",name:"comment.line.rem.batchfile"},{begin:"(?<=^|[\\s@])(?i:rem)\\b",beginCaptures:{0:{name:"keyword.command.rem.batchfile"}},end:"\\n",name:"comment.line.rem.batchfile",patterns:[{match:"[><|]",name:"invalid.illegal.unexpected-character.batchfile"}]}]},constants:{patterns:[{match:"\\b(?i:NUL)\\b",name:"constant.language.batchfile"}]},controls:{patterns:[{match:"(?i)(?<=^|\\s)(?:call|exit(?=$|\\s)|goto(?=$|\\s|:))",name:"keyword.control.statement.batchfile"},{captures:{1:{name:"keyword.control.conditional.batchfile"},2:{name:"keyword.operator.logical.batchfile"},3:{name:"keyword.other.special-method.batchfile"}},match:"(?<=^|\\s)(?i)(if)\\s+(?:(not)\\s+)?(exist|defined|errorlevel|cmdextversion)(?=\\s)"},{match:"(?<=^|\\s)(?i)(?:if|else)(?=$|\\s)",name:"keyword.control.conditional.batchfile"},{begin:"(?<=^|[\\s(&^])(?i)for(?=\\s)",beginCaptures:{0:{name:"keyword.control.repeat.batchfile"}},end:"\\n",name:"meta.block.repeat.batchfile",patterns:[{begin:"(?<=[\\s^])(?i)in(?=\\s)",beginCaptures:{0:{name:"keyword.control.repeat.in.batchfile"}},end:"(?<=[\\s)^])(?i)do(?=\\s)|\\n",endCaptures:{0:{name:"keyword.control.repeat.do.batchfile"}},patterns:[{include:"$self"}]},{include:"$self"}]}]},escaped_characters:{patterns:[{match:"%%|\\^\\^!|\\^(?=.)|\\^\\n",name:"constant.character.escape.batchfile"}]},labels:{patterns:[{captures:{1:{name:"punctuation.separator.batchfile"},2:{name:"keyword.other.special-method.batchfile"}},match:"(?i)(?:^\\s*|(?<=call|goto)\\s*)(:)([^+=,;:\\s]\\S*)"}]},numbers:{patterns:[{match:"(?<=^|\\s|=)(0[xX][0-9A-Fa-f]*|[+-]?\\d+)(?=$|\\s|<|>)",name:"constant.numeric.batchfile"}]},operators:{patterns:[{match:"@(?=\\S)",name:"keyword.operator.at.batchfile"},{match:"(?<=\\s)(?i:EQU|NEQ|LSS|LEQ|GTR|GEQ)(?=\\s)|==",name:"keyword.operator.comparison.batchfile"},{match:"(?<=\\s)(?i)(NOT)(?=\\s)",name:"keyword.operator.logical.batchfile"},{match:"(?<!\\^)&&?|\\|\\|",name:"keyword.operator.conditional.batchfile"},{match:"(?<!\\^)\\|",name:"keyword.operator.pipe.batchfile"},{match:"<&?|>[&>]?",name:"keyword.operator.redirection.batchfile"}]},parens:{patterns:[{begin:"\\(",beginCaptures:{0:{name:"punctuation.section.group.begin.batchfile"}},end:"\\)",endCaptures:{0:{name:"punctuation.section.group.end.batchfile"}},name:"meta.group.batchfile",patterns:[{match:",|;",name:"punctuation.separator.batchfile"},{include:"$self"}]}]},repeatParameter:{patterns:[{captures:{1:{name:"punctuation.definition.variable.batchfile"}},match:"(%%)(?:(?i:~[fdpnxsatz]*(?:\\$PATH:)?)?[a-zA-Z])",name:"variable.parameter.repeat.batchfile"}]},strings:{patterns:[{begin:'"',beginCaptures:{0:{name:"punctuation.definition.string.begin.batchfile"}},end:'(")|(\\n)',endCaptures:{1:{name:"punctuation.definition.string.end.batchfile"},2:{name:"invalid.illegal.newline.batchfile"}},name:"string.quoted.double.batchfile",patterns:[{match:"%%",name:"constant.character.escape.batchfile"},{include:"#variables"}]}]},variable:{patterns:[{begin:"%(?=[^%]+%)",beginCaptures:{0:{name:"punctuation.definition.variable.begin.batchfile"}},end:"(%)|\\n",endCaptures:{1:{name:"punctuation.definition.variable.end.batchfile"}},name:"variable.other.readwrite.batchfile",patterns:[{begin:":~",beginCaptures:{0:{name:"punctuation.separator.batchfile"}},end:"(?=%|\\n)",name:"meta.variable.substring.batchfile",patterns:[{include:"#variable_substring"}]},{begin:":",beginCaptures:{0:{name:"punctuation.separator.batchfile"}},end:"(?=%|\\n)",name:"meta.variable.substitution.batchfile",patterns:[{include:"#variable_replace"},{begin:"=",beginCaptures:{0:{name:"punctuation.separator.batchfile"}},end:"(?=%|\\n)",patterns:[{include:"#variable_delayed_expansion"},{match:"[^%]+",name:"string.unquoted.batchfile"}]}]}]}]},variable_delayed_expansion:{patterns:[{begin:"!(?=[^!]+!)",beginCaptures:{0:{name:"punctuation.definition.variable.begin.batchfile"}},end:"(!)|\\n",endCaptures:{1:{name:"punctuation.definition.variable.end.batchfile"}},name:"variable.other.readwrite.batchfile",patterns:[{begin:":~",beginCaptures:{0:{name:"punctuation.separator.batchfile"}},end:"(?=!|\\n)",name:"meta.variable.substring.batchfile",patterns:[{include:"#variable_substring"}]},{begin:":",beginCaptures:{0:{name:"punctuation.separator.batchfile"}},end:"(?=!|\\n)",name:"meta.variable.substitution.batchfile",patterns:[{include:"#escaped_characters"},{include:"#variable_replace"},{include:"#variable"},{begin:"=",beginCaptures:{0:{name:"punctuation.separator.batchfile"}},end:"(?=!|\\n)",patterns:[{include:"#variable"},{match:"[^!]+",name:"string.unquoted.batchfile"}]}]}]}]},variable_replace:{patterns:[{match:"[^=%!\\n]+",name:"string.unquoted.batchfile"}]},variable_substring:{patterns:[{captures:{1:{name:"constant.numeric.batchfile"},2:{name:"punctuation.separator.batchfile"},3:{name:"constant.numeric.batchfile"}},match:"([+-]?\\d+)(?:(,)([+-]?\\d+))?"}]},variables:{patterns:[{captures:{1:{name:"punctuation.definition.variable.batchfile"}},match:"(%)(?:(?i:~[fdpnxsatz]*(?:\\$PATH:)?)?\\d|\\*)",name:"variable.parameter.batchfile"},{include:"#variable"},{include:"#variable_delayed_expansion"}]}},scopeName:"source.batchfile",aliases:["batch"]})]},94591:(e,t,n)=>{n.r(t),n.d(t,{default:()=>a});var a=[Object.freeze({displayName:"PowerShell",name:"powershell",patterns:[{begin:"<#",beginCaptures:{0:{name:"punctuation.definition.comment.block.begin.powershell"}},end:"#>",endCaptures:{0:{name:"punctuation.definition.comment.block.end.powershell"}},name:"comment.block.powershell",patterns:[{include:"#commentEmbeddedDocs"}]},{match:"[2-6]>&1|>>|>|<<|<|>|>\\||[1-6]>|[1-6]>>",name:"keyword.operator.redirection.powershell"},{include:"#commands"},{include:"#commentLine"},{include:"#variable"},{include:"#subexpression"},{include:"#function"},{include:"#attribute"},{include:"#UsingDirective"},{include:"#type"},{include:"#hashtable"},{include:"#doubleQuotedString"},{include:"#scriptblock"},{comment:"Needed to parse stuff correctly in 'argument mode'. (See about_parsing.)",include:"#doubleQuotedStringEscapes"},{applyEndPatternLast:!0,begin:"['\\x{2018}-\\x{201B}]",beginCaptures:{0:{name:"punctuation.definition.string.begin.powershell"}},end:"['\\x{2018}-\\x{201B}]",endCaptures:{0:{name:"punctuation.definition.string.end.powershell"}},name:"string.quoted.single.powershell",patterns:[{match:"['\\x{2018}-\\x{201B}]{2}",name:"constant.character.escape.powershell"}]},{begin:'(@["\\x{201C}-\\x{201E}])\\s*$',beginCaptures:{1:{name:"punctuation.definition.string.begin.powershell"}},end:'^["\\x{201C}-\\x{201E}]@',endCaptures:{0:{name:"punctuation.definition.string.end.powershell"}},name:"string.quoted.double.heredoc.powershell",patterns:[{include:"#variableNoProperty"},{include:"#doubleQuotedStringEscapes"},{include:"#interpolation"}]},{begin:"(@['\\x{2018}-\\x{201B}])\\s*$",beginCaptures:{1:{name:"punctuation.definition.string.begin.powershell"}},end:"^['\\x{2018}-\\x{201B}]@",endCaptures:{0:{name:"punctuation.definition.string.end.powershell"}},name:"string.quoted.single.heredoc.powershell"},{include:"#numericConstant"},{begin:"(@)(\\()",beginCaptures:{1:{name:"keyword.other.array.begin.powershell"},2:{name:"punctuation.section.group.begin.powershell"}},end:"\\)",endCaptures:{0:{name:"punctuation.section.group.end.powershell"}},name:"meta.group.array-expression.powershell",patterns:[{include:"$self"}]},{begin:"((\\$))(\\()",beginCaptures:{1:{name:"keyword.other.substatement.powershell"},2:{name:"punctuation.definition.subexpression.powershell"},3:{name:"punctuation.section.group.begin.powershell"}},comment:"TODO: move to repo; make recursive.",end:"\\)",endCaptures:{0:{name:"punctuation.section.group.end.powershell"}},name:"meta.group.complex.subexpression.powershell",patterns:[{include:"$self"}]},{match:"(\\b(([A-Za-z0-9\\-_\\.]+)\\.(?i:exe|com|cmd|bat))\\b)",name:"support.function.powershell"},{match:"(?<!\\w|-|\\.)((?i:begin|break|catch|clean|continue|data|default|define|do|dynamicparam|else|elseif|end|exit|finally|for|from|if|in|inlinescript|parallel|param|process|return|sequence|switch|throw|trap|try|until|var|while)|%|\\?)(?!\\w)",name:"keyword.control.powershell"},{match:"(?<!\\w|-|[^)]\\.)((?i:(foreach|where)(?!-object))|%|\\?)(?!\\w)",name:"keyword.control.powershell"},{begin:"(?<!\\w)(--%)(?!\\w)",beginCaptures:{1:{name:"keyword.control.powershell"}},comment:"This should be moved to the repository at some point.",end:"$",patterns:[{match:".+",name:"string.unquoted.powershell"}]},{comment:"This should only be relevant inside a class but will require a rework of how classes are matched. This is a temp fix.",match:"(?<!\\w)((?i:hidden|static))(?!\\w)",name:"storage.modifier.powershell"},{captures:{1:{name:"storage.type.powershell"},2:{name:"entity.name.function"}},comment:"capture should be entity.name.type, but it doesn't provide a good color in the default schema.",match:"(?<!\\w|-)((?i:class)|%|\\?)(?:\\s)+((?:\\p{L}|\\d|_|-|)+)\\b"},{match:"(?<!\\w)-(?i:is(?:not)?|as)\\b",name:"keyword.operator.comparison.powershell"},{match:"(?<!\\w)-(?i:[ic]?(?:eq|ne|[gl][te]|(?:not)?(?:like|match|contains|in)|replace))(?!\\p{L})",name:"keyword.operator.comparison.powershell"},{match:"(?<!\\w)-(?i:join|split)(?!\\p{L})|!",name:"keyword.operator.unary.powershell"},{match:"(?<!\\w)-(?i:and|or|not|xor)(?!\\p{L})|!",name:"keyword.operator.logical.powershell"},{match:"(?<!\\w)-(?i:band|bor|bnot|bxor|shl|shr)(?!\\p{L})",name:"keyword.operator.bitwise.powershell"},{match:"(?<!\\w)-(?i:f)(?!\\p{L})",name:"keyword.operator.string-format.powershell"},{match:"[+%*/-]?=|[+/*%-]",name:"keyword.operator.assignment.powershell"},{match:"\\|{2}|&{2}|;",name:"punctuation.terminator.statement.powershell"},{match:"&|(?<!\\w)\\.(?= )|`|,|\\|",name:"keyword.operator.other.powershell"},{comment:"This is very imprecise, is there a syntax for 'must come after...' ",match:"(?<!\\s|^)\\.\\.(?=-?\\d|\\(|\\$)",name:"keyword.operator.range.powershell"}],repository:{RequiresDirective:{begin:"(?<=#)(?i:(requires))\\s",beginCaptures:{0:{name:"keyword.control.requires.powershell"}},end:"$",name:"meta.requires.powershell",patterns:[{match:"-(?i:Modules|PSSnapin|RunAsAdministrator|ShellId|Version|Assembly|PSEdition)",name:"keyword.other.powershell"},{match:"(?<!-)\\b\\p{L}+|\\d+(?:\\.\\d+)*",name:"variable.parameter.powershell"},{include:"#hashtable"}]},UsingDirective:{captures:{1:{name:"keyword.control.using.powershell"},2:{name:"keyword.other.powershell"},3:{name:"variable.parameter.powershell"}},match:"(?<!\\w)(?i:(using))\\s+(?i:(namespace|module))\\s+(?i:((?:\\w+(?:\\.)?)+))"},attribute:{begin:"(\\[)\\s*\\b(?i)(cmdletbinding|alias|outputtype|parameter|validatenotnull|validatenotnullorempty|validatecount|validateset|allownull|allowemptycollection|allowemptystring|validatescript|validaterange|validatepattern|validatelength|supportswildcards)\\b",beginCaptures:{1:{name:"punctuation.section.bracket.begin.powershell"},2:{name:"support.function.attribute.powershell"}},end:"(\\])",endCaptures:{1:{name:"punctuation.section.bracket.end.powershell"}},name:"meta.attribute.powershell",patterns:[{begin:"\\(",beginCaptures:{0:{name:"punctuation.section.group.begin.powershell"}},end:"\\)",endCaptures:{0:{name:"punctuation.section.group.end.powershell"}},patterns:[{include:"$self"},{captures:{1:{name:"variable.parameter.attribute.powershell"},2:{name:"keyword.operator.assignment.powershell"}},match:"(?i)\\b(mandatory|valuefrompipeline|valuefrompipelinebypropertyname|valuefromremainingarguments|position|parametersetname|defaultparametersetname|supportsshouldprocess|supportspaging|positionalbinding|helpuri|confirmimpact|helpmessage)\\b(?:\\s+)?(=)?"}]}]},commands:{patterns:[{comment:"Verb-Noun pattern:",match:"(?:(\\p{L}|\\d|_|-|\\\\|:)*\\\\)?\\b(?i:Add|Approve|Assert|Backup|Block|Build|Checkpoint|Clear|Close|Compare|Complete|Compress|Confirm|Connect|Convert|ConvertFrom|ConvertTo|Copy|Debug|Deny|Deploy|Disable|Disconnect|Dismount|Edit|Enable|Enter|Exit|Expand|Export|Find|Format|Get|Grant|Group|Hide|Import|Initialize|Install|Invoke|Join|Limit|Lock|Measure|Merge|Mount|Move|New|Open|Optimize|Out|Ping|Pop|Protect|Publish|Push|Read|Receive|Redo|Register|Remove|Rename|Repair|Request|Reset|Resize|Resolve|Restart|Restore|Resume|Revoke|Save|Search|Select|Send|Set|Show|Skip|Split|Start|Step|Stop|Submit|Suspend|Switch|Sync|Test|Trace|Unblock|Undo|Uninstall|Unlock|Unprotect|Unpublish|Unregister|Update|Use|Wait|Watch|Write)-.+?(?:\\.(?i:exe|cmd|bat|ps1))?\\b",name:"support.function.powershell"},{comment:"Builtin cmdlets with reserved verbs",match:"(?<!\\w)(?i:foreach-object)(?!\\w)",name:"support.function.powershell"},{comment:"Builtin cmdlets with reserved verbs",match:"(?<!\\w)(?i:where-object)(?!\\w)",name:"support.function.powershell"},{comment:"Builtin cmdlets with reserved verbs",match:"(?<!\\w)(?i:sort-object)(?!\\w)",name:"support.function.powershell"},{comment:"Builtin cmdlets with reserved verbs",match:"(?<!\\w)(?i:tee-object)(?!\\w)",name:"support.function.powershell"}]},commentEmbeddedDocs:{patterns:[{captures:{1:{name:"constant.string.documentation.powershell"},2:{name:"keyword.operator.documentation.powershell"}},comment:"these embedded doc keywords do not support arguments, must be the only thing on the line",match:"(?:^|\\G)(?i:\\s*(\\.)(COMPONENT|DESCRIPTION|EXAMPLE|FUNCTIONALITY|INPUTS|LINK|NOTES|OUTPUTS|ROLE|SYNOPSIS))\\s*$",name:"comment.documentation.embedded.powershell"},{captures:{1:{name:"constant.string.documentation.powershell"},2:{name:"keyword.operator.documentation.powershell"},3:{name:"keyword.operator.documentation.powershell"}},comment:"these embedded doc keywords require arguments though the type required may be inconsistent, they may not all be able to use the same argument match",match:"(?:^|\\G)(?i:\\s*(\\.)(EXTERNALHELP|FORWARDHELP(?:CATEGORY|TARGETNAME)|PARAMETER|REMOTEHELPRUNSPACE))\\s+(.+?)\\s*$",name:"comment.documentation.embedded.powershell"}]},commentLine:{begin:"(?<![`\\\\-])(#)#*",captures:{1:{name:"punctuation.definition.comment.powershell"}},end:"$\\n?",name:"comment.line.powershell",patterns:[{include:"#commentEmbeddedDocs"},{include:"#RequiresDirective"}]},doubleQuotedString:{applyEndPatternLast:!0,begin:'["\\x{201C}-\\x{201E}]',beginCaptures:{0:{name:"punctuation.definition.string.begin.powershell"}},end:'["\\x{201C}-\\x{201E}]',endCaptures:{0:{name:"punctuation.definition.string.end.powershell"}},name:"string.quoted.double.powershell",patterns:[{match:"(?i)\\b[A-Z0-9._%+-]+@[A-Z0-9.-]+\\.[A-Z]{2,64}\\b"},{include:"#variableNoProperty"},{include:"#doubleQuotedStringEscapes"},{match:'["\\x{201C}-\\x{201E}]{2}',name:"constant.character.escape.powershell"},{include:"#interpolation"},{match:"`\\s*$",name:"keyword.other.powershell"}]},doubleQuotedStringEscapes:{patterns:[{match:"`[`0abefnrtv'\"\\x{2018}-\\x{201E}$]",name:"constant.character.escape.powershell"},{include:"#unicodeEscape"}]},function:{begin:"^(?:\\s*+)(?i)(function|filter|configuration|workflow)\\s+(?:(global|local|script|private):)?((?:\\p{L}|\\d|_|-|\\.)+)",beginCaptures:{0:{name:"meta.function.powershell"},1:{name:"storage.type.powershell"},2:{name:"storage.modifier.scope.powershell"},3:{name:"entity.name.function.powershell"}},end:"(?=\\{|\\()",patterns:[{include:"#commentLine"}]},hashtable:{begin:"(@)(\\{)",beginCaptures:{1:{name:"keyword.other.hashtable.begin.powershell"},2:{name:"punctuation.section.braces.begin.powershell"}},end:"(\\})",endCaptures:{1:{name:"punctuation.section.braces.end.powershell"}},name:"meta.hashtable.powershell",patterns:[{captures:{1:{name:"punctuation.definition.string.begin.powershell"},2:{name:"variable.other.readwrite.powershell"},3:{name:"punctuation.definition.string.end.powershell"},4:{name:"keyword.operator.assignment.powershell"}},match:"\\b((?:\\'|\\\")?)(\\w+)((?:\\'|\\\")?)(?:\\s+)?(=)(?:\\s+)?",name:"meta.hashtable.assignment.powershell"},{include:"#scriptblock"},{include:"$self"}]},interpolation:{begin:"(((\\$)))((\\())",beginCaptures:{1:{name:"keyword.other.substatement.powershell"},2:{name:"punctuation.definition.substatement.powershell"},3:{name:"punctuation.section.embedded.substatement.begin.powershell"},4:{name:"punctuation.section.group.begin.powershell"},5:{name:"punctuation.section.embedded.substatement.begin.powershell"}},contentName:"interpolated.complex.source.powershell",end:"(\\))",endCaptures:{0:{name:"punctuation.section.group.end.powershell"},1:{name:"punctuation.section.embedded.substatement.end.powershell"}},name:"meta.embedded.substatement.powershell",patterns:[{include:"$self"}]},numericConstant:{patterns:[{captures:{1:{name:"constant.numeric.hex.powershell"},2:{name:"keyword.other.powershell"}},match:"(?<!\\w)([-+]?0(?:x|X)[0-9a-fA-F_]+(?:U|u|L|l|UL|Ul|uL|ul|LU|Lu|lU|lu)?)((?i:[kmgtp]b)?)\\b"},{captures:{1:{name:"constant.numeric.integer.powershell"},2:{name:"keyword.other.powershell"}},match:"(?<!\\w)([-+]?(?:[0-9_]+)?\\.[0-9_]+(?:(?:e|E)\\d+)?(?:F|f|D|d|M|m)?)((?i:[kmgtp]b)?)\\b"},{captures:{1:{name:"constant.numeric.octal.powershell"},2:{name:"keyword.other.powershell"}},match:"(?<!\\w)([-+]?0(?:b|B)[01_]+(?:U|u|L|l|UL|Ul|uL|ul|LU|Lu|lU|lu)?)((?i:[kmgtp]b)?)\\b"},{captures:{1:{name:"constant.numeric.integer.powershell"},2:{name:"keyword.other.powershell"}},match:"(?<!\\w)([-+]?[0-9_]+(?:e|E)(?:[0-9_])?+(?:F|f|D|d|M|m)?)((?i:[kmgtp]b)?)\\b"},{captures:{1:{name:"constant.numeric.integer.powershell"},2:{name:"keyword.other.powershell"}},match:"(?<!\\w)([-+]?[0-9_]+\\.(?:e|E)(?:[0-9_])?+(?:F|f|D|d|M|m)?)((?i:[kmgtp]b)?)\\b"},{captures:{1:{name:"constant.numeric.integer.powershell"},2:{name:"keyword.other.powershell"}},match:"(?<!\\w)([-+]?[0-9_]+[\\.]?(?:F|f|D|d|M|m))((?i:[kmgtp]b)?)\\b"},{captures:{1:{name:"constant.numeric.integer.powershell"},2:{name:"keyword.other.powershell"}},match:"(?<!\\w)([-+]?[0-9_]+[\\.]?(?:U|u|L|l|UL|Ul|uL|ul|LU|Lu|lU|lu)?)((?i:[kmgtp]b)?)\\b"}]},scriptblock:{begin:"\\{",beginCaptures:{0:{name:"punctuation.section.braces.begin.powershell"}},end:"\\}",endCaptures:{0:{name:"punctuation.section.braces.end.powershell"}},name:"meta.scriptblock.powershell",patterns:[{include:"$self"}]},subexpression:{begin:"\\(",beginCaptures:{0:{name:"punctuation.section.group.begin.powershell"}},end:"\\)",endCaptures:{0:{name:"punctuation.section.group.end.powershell"}},name:"meta.group.simple.subexpression.powershell",patterns:[{include:"$self"}]},type:{begin:"\\[",beginCaptures:{0:{name:"punctuation.section.bracket.begin.powershell"}},end:"\\]",endCaptures:{0:{name:"punctuation.section.bracket.end.powershell"}},patterns:[{match:"(?!\\d+|\\.)(?:\\p{L}|\\p{N}|\\.)+",name:"storage.type.powershell"},{include:"$self"}]},unicodeEscape:{comment:"`u{xxxx} added in PowerShell 6.0",patterns:[{match:"`u\\{(?:(?:10)?([0-9a-fA-F]){1,4}|0?\\g<1>{1,5})}",name:"constant.character.escape.powershell"},{match:"`u(?:\\{[0-9a-fA-F]{,6}.)?",name:"invalid.character.escape.powershell"}]},variable:{patterns:[{captures:{0:{name:"constant.language.powershell"},1:{name:"punctuation.definition.variable.powershell"}},comment:"These are special constants.",match:"(\\$)(?i:(False|Null|True))\\b"},{captures:{0:{name:"support.constant.variable.powershell"},1:{name:"punctuation.definition.variable.powershell"},3:{name:"variable.other.member.powershell"}},comment:"These are the other built-in constants.",match:"(\\$)(?i:(Error|ExecutionContext|Host|Home|PID|PsHome|PsVersionTable|ShellID))((?:\\.(?:\\p{L}|\\d|_)+)*\\b)?\\b"},{captures:{0:{name:"support.variable.automatic.powershell"},1:{name:"punctuation.definition.variable.powershell"},3:{name:"variable.other.member.powershell"}},comment:"Automatic variables are not constants, but they are read-only. In monokai (default) color schema support.variable doesn't have color, so we use constant.",match:"(\\$)((?:[$^?])|(?i:_|Args|ConsoleFileName|Event|EventArgs|EventSubscriber|ForEach|Input|LastExitCode|Matches|MyInvocation|NestedPromptLevel|Profile|PSBoundParameters|PsCmdlet|PsCulture|PSDebugContext|PSItem|PSCommandPath|PSScriptRoot|PsUICulture|Pwd|Sender|SourceArgs|SourceEventArgs|StackTrace|Switch|This)\\b)((?:\\.(?:\\p{L}|\\d|_)+)*\\b)?"},{captures:{0:{name:"variable.language.powershell"},1:{name:"punctuation.definition.variable.powershell"},3:{name:"variable.other.member.powershell"}},comment:"Style preference variables as language variables so that they stand out.",match:"(\\$)(?i:(ConfirmPreference|DebugPreference|ErrorActionPreference|ErrorView|FormatEnumerationLimit|InformationPreference|LogCommandHealthEvent|LogCommandLifecycleEvent|LogEngineHealthEvent|LogEngineLifecycleEvent|LogProviderHealthEvent|LogProviderLifecycleEvent|MaximumAliasCount|MaximumDriveCount|MaximumErrorCount|MaximumFunctionCount|MaximumHistoryCount|MaximumVariableCount|OFS|OutputEncoding|PSCulture|PSDebugContext|PSDefaultParameterValues|PSEmailServer|PSItem|PSModuleAutoLoadingPreference|PSModuleAutoloadingPreference|PSSenderInfo|PSSessionApplicationName|PSSessionConfigurationName|PSSessionOption|ProgressPreference|VerbosePreference|WarningPreference|WhatIfPreference))((?:\\.(?:\\p{L}|\\d|_)+)*\\b)?\\b"},{captures:{0:{name:"variable.other.readwrite.powershell"},1:{name:"punctuation.definition.variable.powershell"},2:{name:"storage.modifier.scope.powershell"},4:{name:"variable.other.member.powershell"}},match:"(?i:(\\$|@)(global|local|private|script|using|workflow):((?:\\p{L}|\\d|_)+))((?:\\.(?:\\p{L}|\\d|_)+)*\\b)?"},{captures:{0:{name:"variable.other.readwrite.powershell"},1:{name:"punctuation.definition.variable.powershell"},2:{name:"punctuation.section.braces.begin.powershell"},3:{name:"storage.modifier.scope.powershell"},5:{name:"punctuation.section.braces.end.powershell"},6:{name:"variable.other.member.powershell"}},match:"(?i:(\\$)(\\{)(global|local|private|script|using|workflow):([^}]*[^}`])(\\}))((?:\\.(?:\\p{L}|\\d|_)+)*\\b)?"},{captures:{0:{name:"variable.other.readwrite.powershell"},1:{name:"punctuation.definition.variable.powershell"},2:{name:"support.variable.drive.powershell"},4:{name:"variable.other.member.powershell"}},match:"(?i:(\\$|@)((?:\\p{L}|\\d|_)+:)?((?:\\p{L}|\\d|_)+))((?:\\.(?:\\p{L}|\\d|_)+)*\\b)?"},{captures:{0:{name:"variable.other.readwrite.powershell"},1:{name:"punctuation.definition.variable.powershell"},2:{name:"punctuation.section.braces.begin.powershell"},3:{name:"support.variable.drive.powershell"},5:{name:"punctuation.section.braces.end.powershell"},6:{name:"variable.other.member.powershell"}},match:"(?i:(\\$)(\\{)((?:\\p{L}|\\d|_)+:)?([^}]*[^}`])(\\}))((?:\\.(?:\\p{L}|\\d|_)+)*\\b)?"}]},variableNoProperty:{patterns:[{captures:{0:{name:"constant.language.powershell"},1:{name:"punctuation.definition.variable.powershell"}},comment:"These are special constants.",match:"(\\$)(?i:(False|Null|True))\\b"},{captures:{0:{name:"support.constant.variable.powershell"},1:{name:"punctuation.definition.variable.powershell"},3:{name:"variable.other.member.powershell"}},comment:"These are the other built-in constants.",match:"(\\$)(?i:(Error|ExecutionContext|Host|Home|PID|PsHome|PsVersionTable|ShellID))\\b"},{captures:{0:{name:"support.variable.automatic.powershell"},1:{name:"punctuation.definition.variable.powershell"},3:{name:"variable.other.member.powershell"}},comment:"Automatic variables are not constants, but they are read-only...",match:"(\\$)((?:[$^?])|(?i:_|Args|ConsoleFileName|Event|EventArgs|EventSubscriber|ForEach|Input|LastExitCode|Matches|MyInvocation|NestedPromptLevel|Profile|PSBoundParameters|PsCmdlet|PsCulture|PSDebugContext|PSItem|PSCommandPath|PSScriptRoot|PsUICulture|Pwd|Sender|SourceArgs|SourceEventArgs|StackTrace|Switch|This)\\b)"},{captures:{0:{name:"variable.language.powershell"},1:{name:"punctuation.definition.variable.powershell"},3:{name:"variable.other.member.powershell"}},comment:"Style preference variables as language variables so that they stand out.",match:"(\\$)(?i:(ConfirmPreference|DebugPreference|ErrorActionPreference|ErrorView|FormatEnumerationLimit|InformationPreference|LogCommandHealthEvent|LogCommandLifecycleEvent|LogEngineHealthEvent|LogEngineLifecycleEvent|LogProviderHealthEvent|LogProviderLifecycleEvent|MaximumAliasCount|MaximumDriveCount|MaximumErrorCount|MaximumFunctionCount|MaximumHistoryCount|MaximumVariableCount|OFS|OutputEncoding|PSCulture|PSDebugContext|PSDefaultParameterValues|PSEmailServer|PSItem|PSModuleAutoLoadingPreference|PSModuleAutoloadingPreference|PSSenderInfo|PSSessionApplicationName|PSSessionConfigurationName|PSSessionOption|ProgressPreference|VerbosePreference|WarningPreference|WhatIfPreference))\\b"},{captures:{0:{name:"variable.other.readwrite.powershell"},1:{name:"punctuation.definition.variable.powershell"},2:{name:"storage.modifier.scope.powershell"},4:{name:"variable.other.member.powershell"}},match:"(?i:(\\$)(global|local|private|script|using|workflow):((?:\\p{L}|\\d|_)+))"},{captures:{0:{name:"variable.other.readwrite.powershell"},1:{name:"punctuation.definition.variable.powershell"},2:{name:"storage.modifier.scope.powershell"},4:{name:"keyword.other.powershell"},5:{name:"variable.other.member.powershell"}},match:"(?i:(\\$)(\\{)(global|local|private|script|using|workflow):([^}]*[^}`])(\\}))"},{captures:{0:{name:"variable.other.readwrite.powershell"},1:{name:"punctuation.definition.variable.powershell"},2:{name:"support.variable.drive.powershell"},4:{name:"variable.other.member.powershell"}},match:"(?i:(\\$)((?:\\p{L}|\\d|_)+:)?((?:\\p{L}|\\d|_)+))"},{captures:{0:{name:"variable.other.readwrite.powershell"},1:{name:"punctuation.definition.variable.powershell"},2:{name:"punctuation.section.braces.begin"},3:{name:"support.variable.drive.powershell"},5:{name:"punctuation.section.braces.end"}},match:"(?i:(\\$)(\\{)((?:\\p{L}|\\d|_)+:)?([^}]*[^}`])(\\}))"}]}},scopeName:"source.powershell",aliases:["ps","ps1"]})]}};