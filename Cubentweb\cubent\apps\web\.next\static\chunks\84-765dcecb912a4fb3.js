"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[84],{7059:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0})},11610:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.useLogger=void 0;let r=n(35062),i=n(73770),o=n(50628),s=n(91614);t.useLogger=function(e={}){let t=(0,r.usePathname)(),n=(0,s.useDeepCompareMemo)(()=>{var n;return Object.assign(Object.assign({},e),{args:Object.assign(Object.assign({},null!=(n=e.args)?n:{}),{path:t})})},[e,t]),l=(0,o.useMemo)(()=>new i.Logger(n),[n]);return(0,o.useEffect)(()=>()=>{l&&l.flush()},[t]),l}},23996:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function s(e){try{a(r.next(e))}catch(e){o(e)}}function l(e){try{a(r.throw(e))}catch(e){o(e)}}function a(e){var t;e.done?i(e.value):((t=e.value)instanceof n?t:new n(function(e){e(t)})).then(s,l)}a((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0}),t.withBetterStack=t.withBetterStackRouteHandler=t.withBetterStackNextConfig=void 0;let i=n(44344),o=n(73770),s=n(4319);function l(e){return Object.assign(Object.assign({},e),{rewrites:()=>r(this,void 0,void 0,function*(){var t;let n=yield null==(t=e.rewrites)?void 0:t.call(e),r=i.config.getIngestURL(s.EndpointType.webVitals),l=i.config.getIngestURL(s.EndpointType.logs);if(!r&&!l){let e=new o.Logger;return e.warn("Envvars not detected. If this is production please see https://betterstack.com/docs/logs/javascript/nextjs/ for help"),e.warn("Sending Web Vitals to /dev/null"),e.warn("Sending logs to console"),n||[]}let a=[{source:`${i.config.proxyPath}/web-vitals`,destination:r,basePath:!1},{source:`${i.config.proxyPath}/logs`,destination:l,basePath:!1}];return n?Array.isArray(n)?n.concat(a):(n.afterFiles=(n.afterFiles||[]).concat(a),n):a})})}function a(e,t){return(n,l)=>r(this,void 0,void 0,function*(){var r,a,u,c;let g="";"geo"in n&&(g=null!=(a=null==(r=n.geo)?void 0:r.region)?a:"");let f="";"nextUrl"in n?f=n.nextUrl.pathname:n instanceof Request&&(f=new URL(n.url).pathname);let h=Array.isArray(null==t?void 0:t.logRequestDetails)||(null==t?void 0:t.logRequestDetails)===!0?yield(0,s.requestToJSON)(n):void 0,d={startTime:new Date().getTime(),endTime:new Date().getTime(),path:f,method:n.method,host:n.headers.get("host"),userAgent:n.headers.get("user-agent"),scheme:n.url.split("://")[0],ip:n.headers.get("x-forwarded-for"),region:g,details:Array.isArray(null==t?void 0:t.logRequestDetails)?Object.fromEntries(Object.entries(h).filter(([e])=>(null==t?void 0:t.logRequestDetails).includes(e))):h},p=new o.Logger({req:d,source:i.isEdgeRuntime?"edge":"lambda"}),v=p.with({});v.config.source=`${i.isEdgeRuntime?"edge":"lambda"}-log`,n.log=v;try{let t=yield e(n,l);return d.endTime=new Date().getTime(),d.statusCode=t.status,d.durationMs=d.endTime-d.startTime,i.isVercel||p.logHttpRequest(o.LogLevel.info,`${n.method} ${d.path} ${d.statusCode} in ${d.endTime-d.startTime}ms`,d,{}),v.attachResponseStatus(t.status),yield p.flush(),t}catch(s){d.endTime=new Date().getTime();let e=500,r=o.LogLevel.error;throw s instanceof Error&&("NEXT_NOT_FOUND"===s.message?(r=null!=(u=null==t?void 0:t.notFoundLogLevel)?u:o.LogLevel.warn,e=404):"NEXT_REDIRECT"===s.message&&(r=null!=(c=null==t?void 0:t.redirectLogLevel)?c:o.LogLevel.info,e=s.digest?parseInt(s.digest.split(";")[3]):307)),d.statusCode=e,d.durationMs=d.endTime-d.startTime,i.isVercel||p.logHttpRequest(r,`${n.method} ${d.path} ${d.statusCode} in ${d.endTime-d.startTime}ms`,d,{}),v.log(r,s.message,{error:s}),v.attachResponseStatus(e),yield p.flush(),s}})}t.withBetterStackNextConfig=l,t.withBetterStackRouteHandler=a,t.withBetterStack=function(e,t){if("function"==typeof e);else if("object"==typeof e)return l(e);return a(e,t)}},57421:(e,t)=>{t.Headers=self.Headers,t.Request=self.Request,t.Response=self.Response,t.fetch=self.fetch},61372:(e,t,n)=>{n.d(t,{j:()=>o});var r=Object.prototype.hasOwnProperty;function i(e,t,n){for(n of e.keys())if(o(n,t))return n}function o(e,t){var n,s,l;if(e===t)return!0;if(e&&t&&(n=e.constructor)===t.constructor){if(n===Date)return e.getTime()===t.getTime();if(n===RegExp)return e.toString()===t.toString();if(n===Array){if((s=e.length)===t.length)for(;s--&&o(e[s],t[s]););return -1===s}if(n===Set){if(e.size!==t.size)return!1;for(s of e)if((l=s)&&"object"==typeof l&&!(l=i(t,l))||!t.has(l))return!1;return!0}if(n===Map){if(e.size!==t.size)return!1;for(s of e)if((l=s[0])&&"object"==typeof l&&!(l=i(t,l))||!o(s[1],t.get(l)))return!1;return!0}if(n===ArrayBuffer)e=new Uint8Array(e),t=new Uint8Array(t);else if(n===DataView){if((s=e.byteLength)===t.byteLength)for(;s--&&e.getInt8(s)===t.getInt8(s););return -1===s}if(ArrayBuffer.isView(e)){if((s=e.byteLength)===t.byteLength)for(;s--&&e[s]===t[s];);return -1===s}if(!n||"object"==typeof e){for(n in s=0,e)if(r.call(e,n)&&++s&&!r.call(t,n)||!(n in t)||!o(e[n],t[n]))return!1;return Object.keys(t).length===s}}return e!=e&&t!=t}},63410:function(e,t,n){var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var i=Object.getOwnPropertyDescriptor(t,n);(!i||("get"in i?!t.__esModule:i.writable||i.configurable))&&(i={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,i)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__exportStar||function(e,t){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(t,n)||r(t,e,n)};Object.defineProperty(t,"__esModule",{value:!0}),t.useLogger=t.withLogtailRouteHandler=t.withLogtailNextConfig=t.withLogtail=t.withBetterStackRouteHandler=t.withBetterStackNextConfig=t.withBetterStack=t.throttle=t.EndpointType=t.LogLevel=t.Logger=t.log=void 0;var o=n(73770);Object.defineProperty(t,"log",{enumerable:!0,get:function(){return o.log}}),Object.defineProperty(t,"Logger",{enumerable:!0,get:function(){return o.Logger}}),Object.defineProperty(t,"LogLevel",{enumerable:!0,get:function(){return o.LogLevel}});var s=n(4319);Object.defineProperty(t,"EndpointType",{enumerable:!0,get:function(){return s.EndpointType}}),Object.defineProperty(t,"throttle",{enumerable:!0,get:function(){return s.throttle}}),i(n(7059),t),i(n(44344),t);var l=n(23996);Object.defineProperty(t,"withBetterStack",{enumerable:!0,get:function(){return l.withBetterStack}}),Object.defineProperty(t,"withBetterStackNextConfig",{enumerable:!0,get:function(){return l.withBetterStackNextConfig}}),Object.defineProperty(t,"withBetterStackRouteHandler",{enumerable:!0,get:function(){return l.withBetterStackRouteHandler}}),Object.defineProperty(t,"withLogtail",{enumerable:!0,get:function(){return l.withBetterStack}}),Object.defineProperty(t,"withLogtailNextConfig",{enumerable:!0,get:function(){return l.withBetterStackNextConfig}}),Object.defineProperty(t,"withLogtailRouteHandler",{enumerable:!0,get:function(){return l.withBetterStackRouteHandler}}),i(n(41912),t);var a=n(11610);Object.defineProperty(t,"useLogger",{enumerable:!0,get:function(){return a.useLogger}})},73770:function(e,t,n){var r,i,o=n(37811),s=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(i,o){function s(e){try{a(r.next(e))}catch(e){o(e)}}function l(e){try{a(r.throw(e))}catch(e){o(e)}}function a(e){var t;e.done?i(e.value):((t=e.value)instanceof n?t:new n(function(e){e(t)})).then(s,l)}a((r=r.apply(e,t||[])).next())})};Object.defineProperty(t,"__esModule",{value:!0}),t.prettyPrint=t.log=t.Logger=t.LogLevel=void 0;let l=n(44344),a=n(4319),u=l.config.getLogsEndpoint(),c=o.env.NEXT_PUBLIC_BETTER_STACK_LOG_LEVEL||"debug";(r=i||(t.LogLevel=i={}))[r.debug=0]="debug",r[r.info=1]="info",r[r.warn=2]="warn",r[r.error=3]="error",r[r.off=100]="off";class g{constructor(e={}){this.initConfig=e,this.logEvents=[],this.throttledSendLogs=(0,a.throttle)(this.sendLogs,1e3),this.children=[],this.logLevel=i.debug,this.config={autoFlush:!0,source:"frontend-log",prettyPrint:h},this.debug=(e,t={})=>{this.log(i.debug,e,t)},this.info=(e,t={})=>{this.log(i.info,e,t)},this.warn=(e,t={})=>{this.log(i.warn,e,t)},this.error=(e,t={})=>{this.log(i.error,e,t)},this.with=e=>{let t=new g(Object.assign(Object.assign({},this.config),{args:Object.assign(Object.assign({},this.config.args),e)}));return this.children.push(t),t},this.withRequest=e=>new g(Object.assign(Object.assign({},this.config),{req:Object.assign(Object.assign({},this.config.req),e)})),this._transformEvent=(e,t,n={})=>{let r={level:i[e].toString(),message:t,dt:new Date(Date.now()).toISOString(),source:this.config.source,fields:this.config.args||{},"@app":{"next-logtail-version":l.Version}};if(n instanceof Error)r.fields=Object.assign(Object.assign({},r.fields),{message:n.message,stack:n.stack,name:n.name});else if("object"==typeof n&&null!==n&&Object.keys(n).length>0){let e=JSON.parse(JSON.stringify(n,d));r.fields=Object.assign(Object.assign({},r.fields),e)}else n&&n.length&&(r.fields=Object.assign(Object.assign({},r.fields),{args:n}));return l.config.injectPlatformMetadata(r,this.config.source),null!=this.config.req&&(r.request=this.config.req,r.platform?r.platform.route=this.config.req.path:r.vercel&&(r.vercel.route=this.config.req.path)),r},this.log=(e,t,n={})=>{if(e<this.logLevel)return;let r=this._transformEvent(e,t,n);this.logEvents.push(r),this.config.autoFlush&&this.throttledSendLogs()},this.attachResponseStatus=e=>{this.logEvents=this.logEvents.map(t=>(t.request&&(t.request.statusCode=e),t))},this.flush=()=>s(this,void 0,void 0,function*(){yield Promise.all([this.sendLogs(),...this.children.map(e=>e.flush())])}),void 0!=this.initConfig.logLevel&&this.initConfig.logLevel>=0?this.logLevel=this.initConfig.logLevel:c&&(this.logLevel=i[c]),this.config=Object.assign(Object.assign({},this.config),e)}logHttpRequest(e,t,n,r){let i=this._transformEvent(e,t,r);i.request=n,this.logEvents.push(i),this.config.autoFlush&&this.throttledSendLogs()}middleware(e,t){var n;let r={ip:e.ip,region:null==(n=e.geo)?void 0:n.region,method:e.method,host:e.nextUrl.hostname,path:e.nextUrl.pathname,scheme:e.nextUrl.protocol.split(":")[0],referer:e.headers.get("Referer"),userAgent:e.headers.get("user-agent")},o=`${e.method} ${e.nextUrl.pathname}`;return(null==t?void 0:t.logRequestDetails)?(0,a.requestToJSON)(e).then(e=>{let n=Object.assign(Object.assign({},r),{details:Array.isArray(t.logRequestDetails)?Object.fromEntries(Object.entries(e).filter(([e])=>t.logRequestDetails.includes(e))):e});return this.logHttpRequest(i.info,o,n,{})}):this.logHttpRequest(i.info,o,r,{})}sendLogs(){return s(this,void 0,void 0,function*(){if(!this.logEvents.length)return;if(!l.config.isEnvVarsSet()){this.logEvents.forEach(e=>this.config.prettyPrint?this.config.prettyPrint(e):h(e)),this.logEvents=[];return}let e=JSON.stringify(this.logEvents);this.logEvents=[];let t={"Content-Type":"application/json","User-Agent":"next-logtail/v"+l.Version};l.config.token&&(t.Authorization=`Bearer ${l.config.token}`);let r={body:e,method:"POST",keepalive:!0,headers:t};function i(){return fetch(u,r).catch(console.error)}try{if("undefined"==typeof fetch)return(yield n(57421))(u,r).catch(console.error);if(!l.isBrowser||!l.isVercel||!navigator.sendBeacon)return i();try{if(!navigator.sendBeacon.bind(navigator)(u,e))return i()}catch(e){return i()}}catch(t){console.warn(`Failed to send logs to BetterStack: ${t}`),this.logEvents=[...this.logEvents,JSON.parse(e)]}})}}t.Logger=g,t.log=new g({});let f={info:{terminal:"32",browser:"lightgreen"},debug:{terminal:"36",browser:"lightblue"},warn:{terminal:"33",browser:"yellow"},error:{terminal:"31",browser:"red"}};function h(e){let t=Object.keys(e.fields).length>0;if(a.isNoPrettyPrint){let n=`${e.level} - ${e.message}`;t&&(n+=" "+JSON.stringify(e.fields)),console.log(n);return}let n="",r=[e.level,e.message];l.isBrowser?(n="%c%s - %s",r=[`color: ${f[e.level].browser};`,...r]):n=`\x1b[${f[e.level].terminal}m%s\x1b[0m - %s`,t&&(n+=" %o",r.push(e.fields)),e.request&&(n+=" %o",r.push(e.request)),console.log.apply(console,[n,...r])}function d(e,t){return t instanceof Error?Object.assign(Object.assign({},t),{name:t.name,message:t.message,stack:t.stack}):t}t.prettyPrint=h},81197:(e,t,n)=>{n.d(t,{F:()=>s});var r=n(49973);let i=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,o=r.$,s=(e,t)=>n=>{var r;if((null==t?void 0:t.variants)==null)return o(e,null==n?void 0:n.class,null==n?void 0:n.className);let{variants:s,defaultVariants:l}=t,a=Object.keys(s).map(e=>{let t=null==n?void 0:n[e],r=null==l?void 0:l[e];if(null===t)return null;let o=i(t)||i(r);return s[e][o]}),u=n&&Object.entries(n).reduce((e,t)=>{let[n,r]=t;return void 0===r||(e[n]=r),e},{});return o(e,a,null==t||null==(r=t.compoundVariants)?void 0:r.reduce((e,t)=>{let{class:n,className:r,...i}=t;return Object.entries(i).every(e=>{let[t,n]=e;return Array.isArray(n)?n.includes({...l,...u}[t]):({...l,...u})[t]===n})?[...e,n,r]:e},[]),null==n?void 0:n.class,null==n?void 0:n.className)}},91614:(e,t,n)=>{n.r(t),n.d(t,{useDeepCompareCallback:()=>s,useDeepCompareEffect:()=>l,useDeepCompareImperativeHandle:()=>a,useDeepCompareLayoutEffect:()=>u,useDeepCompareMemo:()=>c});var r=n(50628),i=n(61372);function o(e){let t=r.useRef(e),n=r.useRef(0);return(0,i.j)(e,t.current)||(t.current=e,n.current+=1),r.useMemo(()=>t.current,[n.current])}function s(e,t){return r.useCallback(e,o(t))}function l(e,t){r.useEffect(e,o(t))}function a(e,t,n){r.useImperativeHandle(e,t,o(n))}function u(e,t){r.useLayoutEffect(e,o(t))}function c(e,t){return r.useMemo(e,o(t))}}}]);