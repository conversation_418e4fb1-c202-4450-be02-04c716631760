"use strict";exports.id=2246,exports.ids=[2246],exports.modules={62246:(a,e,n)=>{n.r(e),n.d(e,{default:()=>i});var i=[Object.freeze({displayName:"Gherkin",fileTypes:["feature"],firstLineMatch:"기능|機能|功能|フィーチャ|خاصية|תכונה|Функціонал|Функционалност|Функционал|Особина|Функция|Функциональность|Свойство|Могућност|\xd6zellik|Właściwość|T\xednh năng|Savybė|Požiadavka|Požadavek|Osobina|Ominaisuus|Omadus|OH HAI|Mogućnost|Mogucnost|Jellemző|Fīča|Funzionalit\xe0|Funktionalit\xe4t|Funkcionalnost|Funkcionalitāte|Funcționalitate|Functionaliteit|Functionalitate|Funcionalitat|Funcionalidade|Fonctionnalit\xe9|Fitur|Ability|Business Need|Feature|Egenskap|Egenskab|Crikey|Caracter\xedstica|Arwedd(.*)",foldingStartMarker:"^\\s*\\b(예|시나리오 개요|시나리오|배경|背景|場景大綱|場景|场景大纲|场景|劇本大綱|劇本|例子|例|テンプレ|シナリオテンプレート|シナリオテンプレ|シナリオアウトライン|シナリオ|サンプル|سيناريو مخطط|سيناريو|امثلة|الخلفية|תרחיש|תבנית תרחיש|רקע|דוגמאות|Тарих|Сценарій|Сценарији|Сценарио|Сценарий структураси|Сценарий|Структура сценарію|Структура сценарија|Структура сценария|Скица|Рамка на сценарий|Примери|Пример|Приклади|Предыстория|Предистория|Позадина|Передумова|Основа|Мисоллар|Концепт|Контекст|Значения|\xd6rnekler|Założenia|Wharrimean is|Voorbeelden|Variantai|T\xecnh huống|The thing of it is|Tausta|Taust|Tapausaihio|Tapaus|Tapaukset|Szenariogrundriss|Szenario|Szablon scenariusza|Stsenaarium|Struktura scenarija|Skica|Skenario konsep|Skenario|Situācija|Senaryo taslağı|Senaryo|Sc\xe9n\xe1ř|Sc\xe9nario|Schema dello scenario|Scenārijs pēc parauga|Scenārijs|Scen\xe1r|Scenariusz|Scenariul de şablon|Scenariul de sablon|Scenariu|Scenarios|Scenario Outline|Scenario Amlinellol|Scenario|Example|Scenarijus|Scenariji|Scenarijaus šablonas|Scenarijai|Scenarij|Scenarie|Rerefons|Raamstsenaarium|Př\xedklady|P\xe9ld\xe1k|Pr\xedklady|Przykłady|Primjeri|Primeri|Primer|Pozad\xed|Pozadina|Pozadie|Plan du sc\xe9nario|Plan du Sc\xe9nario|Piemēri|Pavyzdžiai|Paraugs|Osnova sc\xe9n\xe1ře|Osnova|N\xe1črt Sc\xe9n\xe1ře|N\xe1črt Scen\xe1ru|Mate|MISHUN SRSLY|MISHUN|Kịch bản|Kontext|Konteksts|Kontekstas|Kontekst|Koncept|Khung t\xecnh huống|Khung kịch bản|Juhtumid|H\xe1tt\xe9r|Grundlage|Ge\xe7miş|Forgat\xf3k\xf6nyv v\xe1zlat|Forgat\xf3k\xf6nyv|Exemplos|Exemples|Exemplele|Exempel|Examples|Esquema do Cen\xe1rio|Esquema do Cenario|Esquema del escenario|Esquema de l'escenari|Esempi|Escenario|Escenari|Enghreifftiau|Eksempler|Ejemplos|EXAMPLZ|Dữ liệu|Dis is what went down|Dasar|Contoh|Contexto|Contexte|Contesto|Condiţii|Conditii|Cobber|Cen\xe1rio|Cenario|Cefndir|Bối cảnh|Blokes|Beispiele|Bakgrunn|Bakgrund|Baggrund|Background|B4|Antecedents|Antecedentes|All y'all|Achtergrond|Abstrakt Scenario|Abstract Scenario|Rule|Regla|R\xe8gle|Regel|Regra)",foldingStopMarker:"^\\s*$",name:"gherkin",patterns:[{include:"#feature_element_keyword"},{include:"#feature_keyword"},{include:"#step_keyword"},{include:"#strings_triple_quote"},{include:"#strings_single_quote"},{include:"#strings_double_quote"},{include:"#comments"},{include:"#tags"},{include:"#scenario_outline_variable"},{include:"#table"}],repository:{comments:{captures:{0:{name:"comment.line.number-sign"}},match:"^\\s*(#.*)"},feature_element_keyword:{captures:{1:{name:"keyword.language.gherkin.feature.scenario"},2:{name:"string.language.gherkin.scenario.title.title"}},match:"^\\s*(예|시나리오 개요|시나리오|배경|背景|場景大綱|場景|场景大纲|场景|劇本大綱|劇本|例子|例|テンプレ|シナリオテンプレート|シナリオテンプレ|シナリオアウトライン|シナリオ|サンプル|سيناريو مخطط|سيناريو|امثلة|الخلفية|תרחיש|תבנית תרחיש|רקע|דוגמאות|Тарих|Сценарій|Сценарији|Сценарио|Сценарий структураси|Сценарий|Структура сценарію|Структура сценарија|Структура сценария|Скица|Рамка на сценарий|Примери|Пример|Приклади|Предыстория|Предистория|Позадина|Передумова|Основа|Мисоллар|Концепт|Контекст|Значения|\xd6rnekler|Założenia|Wharrimean is|Voorbeelden|Variantai|T\xecnh huống|The thing of it is|Tausta|Taust|Tapausaihio|Tapaus|Tapaukset|Szenariogrundriss|Szenario|Szablon scenariusza|Stsenaarium|Struktura scenarija|Skica|Skenario konsep|Skenario|Situācija|Senaryo taslağı|Senaryo|Sc\xe9n\xe1ř|Sc\xe9nario|Schema dello scenario|Scenārijs pēc parauga|Scenārijs|Scen\xe1r|Scenariusz|Scenariul de şablon|Scenariul de sablon|Scenariu|Scenarios|Scenario Outline|Scenario Amlinellol|Scenario|Example|Scenarijus|Scenariji|Scenarijaus šablonas|Scenarijai|Scenarij|Scenarie|Rerefons|Raamstsenaarium|Př\xedklady|P\xe9ld\xe1k|Pr\xedklady|Przykłady|Primjeri|Primeri|Primer|Pozad\xed|Pozadina|Pozadie|Plan du sc\xe9nario|Plan du Sc\xe9nario|Piemēri|Pavyzdžiai|Paraugs|Osnova sc\xe9n\xe1ře|Osnova|N\xe1črt Sc\xe9n\xe1ře|N\xe1črt Scen\xe1ru|Mate|MISHUN SRSLY|MISHUN|Kịch bản|Kontext|Konteksts|Kontekstas|Kontekst|Koncept|Khung t\xecnh huống|Khung kịch bản|Juhtumid|H\xe1tt\xe9r|Grundlage|Ge\xe7miş|Forgat\xf3k\xf6nyv v\xe1zlat|Forgat\xf3k\xf6nyv|Exemplos|Exemples|Exemplele|Exempel|Examples|Esquema do Cen\xe1rio|Esquema do Cenario|Esquema del escenario|Esquema de l'escenari|Esempi|Escenario|Escenari|Enghreifftiau|Eksempler|Ejemplos|EXAMPLZ|Dữ liệu|Dis is what went down|Dasar|Contoh|Contexto|Contexte|Contesto|Condiţii|Conditii|Cobber|Cen\xe1rio|Cenario|Cefndir|Bối cảnh|Blokes|Beispiele|Bakgrunn|Bakgrund|Baggrund|Background|B4|Antecedents|Antecedentes|All y'all|Achtergrond|Abstrakt Scenario|Abstract Scenario|Rule|Regla|R\xe8gle|Regel|Regra):(.*)"},feature_keyword:{captures:{1:{name:"keyword.language.gherkin.feature"},2:{name:"string.language.gherkin.feature.title"}},match:"^\\s*(기능|機能|功能|フィーチャ|خاصية|תכונה|Функціонал|Функционалност|Функционал|Особина|Функция|Функциональность|Свойство|Могућност|\xd6zellik|Właściwość|T\xednh năng|Savybė|Požiadavka|Požadavek|Osobina|Ominaisuus|Omadus|OH HAI|Mogućnost|Mogucnost|Jellemző|Fīča|Funzionalit\xe0|Funktionalit\xe4t|Funkcionalnost|Funkcionalitāte|Funcționalitate|Functionaliteit|Functionalitate|Funcionalitat|Funcionalidade|Fonctionnalit\xe9|Fitur|Ability|Business Need|Feature|Ability|Egenskap|Egenskab|Crikey|Caracter\xedstica|Arwedd):(.*)\\b"},scenario_outline_variable:{match:"<[a-zA-Z0-9 _-]*>",name:"variable.other"},step_keyword:{captures:{1:{name:"keyword.language.gherkin.feature.step"}},match:"^\\s*(En |و |Y |E |Եվ |Ya |Too right |Və |Həm |A |И |而且 |并且 |同时 |並且 |同時 |Ak |Epi |A tak\xe9 |Og |\uD83D\uDE02 |And |Kaj |Ja |Et que |Et qu' |Et |და |Und |Και |અને |וגם |और |तथा |\xc9s |Dan |Agus |かつ |Lan |ಮತ್ತು |'ej |latlh |그리고 |AN |Un |Ir |an |a |Мөн |Тэгээд |Ond |7 |ਅਤੇ |Aye |Oraz |Si |Și |Şi |К тому же |Также |An |A tiež |A taktiež |A z\xe1roveň |In |Ter |Och |மேலும் |மற்றும் |Һәм |Вә |మరియు |และ |Ve |І |А також |Та |اور |Ва |V\xe0 |Maar |لكن |Pero |Բայց |Peru |Yeah nah |Amma |Ancaq |Ali |Но |Per\xf2 |但是 |Men |Ale |\uD83D\uDE14 |But |Sed |Kuid |Mutta |Mais que |Mais qu' |Mais |მაგ\xadრამ |Aber |Αλλά |પણ |אבל |पर |परन्तु |किन्तु |De |En |Tapi |Ach |Ma |しかし |但し |ただし |Nanging |Ananging |ಆದರೆ |'ach |'a |하지만 |단 |BUT |Bet |awer |m\xe4 |No |Tetapi |Гэхдээ |Харин |Ac |ਪਰ |اما |Avast! |Mas |Dar |А |Иначе |Buh |Али |Toda |Ampak |Vendar |ஆனால் |Ләкин |Әмма |కాని |แต่ |Fakat |Ama |Але |لیکن |Лекин |Бирок |Аммо |Nhưng |Ond |Dan |اذاً |ثم |Alavez |Allora |Antonces |Ապա |Ent\xf3s |But at the end of the day I reckon |O halda |Zatim |То |Aleshores |Cal |那么 |那麼 |L\xe8 sa a |Le sa a |Onda |Pak |S\xe5 |\uD83D\uDE4F |Then |Do |Siis |Niin |Alors |Ent\xf3n |Logo |მაშინ |Dann |Τότε |પછી |אז |אזי |तब |तदा |Akkor |\xde\xe1 |Maka |Ansin |ならば |Njuk |Banjur |ನಂತರ |vaj |그러면 |DEN |Tad |Tada |dann |Тогаш |Togash |Kemudian |Тэгэхэд |Үүний дараа |Tha |\xdea |\xd0a |Tha the |\xdea \xfee |\xd0a \xf0e |ਤਦ |آنگاه |Let go and haul |Wtedy |Ent\xe3o |Entao |Atunci |Затем |Тогда |Dun |Den youse gotta |Онда |Tak |Potom |Nato |Potem |Takrat |Entonces |அப்பொழுது |Нәтиҗәдә |అప్పుడు |ดังนั้น |O zaman |Тоді |پھر |تب |Унда |Th\xec |Yna |Wanneer |متى |عندما |Cuan |Եթե |Երբ |Cuando |It's just unbelievable |Əgər |Nə vaxt ki |Kada |Когато |Quan |当 |當 |L\xe8 |Le |Kad |Když |N\xe5r |Als |\uD83C\uDFAC |When |Se |Kui |Kun |Quand |Lorsque |Lorsqu' |Cando |როდესაც |Wenn |Όταν |ક્યારે |כאשר |जब |कदा |Majd |Ha |Amikor |\xdeegar |Ketika |Nuair a |Nuair nach |Nuair ba |Nuair n\xe1r |Quando |もし |Manawa |Menawa |ಸ್ಥಿತಿಯನ್ನು |qaSDI' |만일 |만약 |WEN |Ja |Kai |wann |Кога |Koga |Apabila |Хэрэв |Tha |\xdea |\xd0a |ਜਦੋਂ |هنگامی |Blimey! |Jeżeli |Jeśli |Gdy |Kiedy |Cand |C\xe2nd |Когда |Если |Wun |Youse know like when |Када |Кад |Keď |Ak |Ko |Ce |Če |Kadar |N\xe4r |எப்போது |Әгәр |ఈ పరిస్థితిలో |เมื่อ |Eğer ki |Якщо |Коли |جب |Агар |Khi |Pryd |Gegewe |بفرض |Dau |Dada |Daus |Dadas |Դիցուք |D\xe1u |Daos |Daes |Y'know |Tutaq ki |Verilir |Dato |Дадено |Donat |Donada |At\xe8s |Atesa |假如 |假设 |假定 |假設 |Sipoze |Sipoze ke |Sipoze Ke |Zadan |Zadani |Zadano |Pokud |Za předpokladu |Givet |Gegeven |Stel |\uD83D\uDE10 |Given |Donitaĵo |Komence |Eeldades |Oletetaan |Soit |Etant donn\xe9 que |Etant donn\xe9 qu' |Etant donn\xe9 |Etant donn\xe9e |Etant donn\xe9s |Etant donn\xe9es |\xc9tant donn\xe9 que |\xc9tant donn\xe9 qu' |\xc9tant donn\xe9 |\xc9tant donn\xe9e |\xc9tant donn\xe9s |\xc9tant donn\xe9es |Dado |Dados |მოცემული |Angenommen |Gegeben sei |Gegeben seien |Δεδομένου |આપેલ છે |בהינתן |अगर |यदि |चूंकि |Amennyiben |Adott |Ef |Dengan |Cuir i gc\xe1s go |Cuir i gc\xe1s nach |Cuir i gc\xe1s gur |Cuir i gc\xe1s n\xe1r |Data |Dati |Date |前提 |Nalika |Nalikaning |ನೀಡಿದ |ghu' noblu' |DaH ghu' bejlu' |조건 |먼저 |I CAN HAZ |Kad |Duota |ugeholl |Дадена |Dadeno |Dadena |Diberi |Bagi |Өгөгдсөн нь |Анх |Gitt |Thurh |\xdeurh |\xd0urh |ਜੇਕਰ |ਜਿਵੇਂ ਕਿ |با فرض |Gangway! |Zakładając |Mając |Zakładając, że |Date fiind |Dat fiind |Dată fiind |Dati fiind |Dați fiind |Daţi fiind |Допустим |Дано |Пусть |Givun |Youse know when youse got |За дато |За дате |За дати |Za dato |Za date |Za dati |Pokiaľ |Za predpokladu |Dano |Podano |Zaradi |Privzeto |கொடுக்கப்பட்ட |Әйтик |చెప్పబడినది |กำหนดให้ |Diyelim ki |Припустимо |Припустимо, що |Нехай |اگر |بالفرض |فرض کیا |Агар |Biết |Cho |Anrhegedig a |\\* )"},strings_double_quote:{begin:"(?<![a-zA-Z0-9'])\"",end:"\"(?![a-zA-Z0-9'])",name:"string.quoted.double",patterns:[{match:"\\\\.",name:"constant.character.escape.untitled"}]},strings_single_quote:{begin:"(?<![a-zA-Z0-9\"])'",end:"'(?![a-zA-Z0-9\"])",name:"string.quoted.single",patterns:[{match:"\\\\.",name:"constant.character.escape"}]},strings_triple_quote:{begin:'""".*',end:'"""',name:"string.quoted.single"},table:{begin:"^\\s*\\|",end:"\\|\\s*$",name:"keyword.control.cucumber.table",patterns:[{match:"\\w",name:"source"}]},tags:{captures:{0:{name:"entity.name.type.class.tsx"}},match:"(@[^@\\r\\n\\t ]+)"}},scopeName:"text.gherkin.feature"})]}};