"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[836],{4844:(e,t,n)=>{n.d(t,{Z:()=>c});var r=n(50628),o=n(6341),i=n(64826),a=n(84268),l=n(6024),c=r.forwardRef((e,t)=>{var n,c;let{container:u,...s}=e,[f,d]=r.useState(!1);(0,a.N)(()=>d(!0),[]);let p=u||f&&(null==(c=globalThis)||null==(n=c.document)?void 0:n.body);return p?o.createPortal((0,l.jsx)(i.sG.div,{...s,ref:t}),p):null});c.displayName="Portal"},9665:(e,t,n)=>{n.d(t,{n:()=>f});var r=n(50628),o=n(98064),i=n(64826),a=n(72336),l=n(6024),c="focusScope.autoFocusOnMount",u="focusScope.autoFocusOnUnmount",s={bubbles:!1,cancelable:!0},f=r.forwardRef((e,t)=>{let{loop:n=!1,trapped:f=!1,onMountAutoFocus:m,onUnmountAutoFocus:y,...g}=e,[w,b]=r.useState(null),x=(0,a.c)(m),E=(0,a.c)(y),S=r.useRef(null),_=(0,o.s)(t,e=>b(e)),A=r.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;r.useEffect(()=>{if(f){let e=function(e){if(A.paused||!w)return;let t=e.target;w.contains(t)?S.current=t:h(S.current,{select:!0})},t=function(e){if(A.paused||!w)return;let t=e.relatedTarget;null!==t&&(w.contains(t)||h(S.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&h(w)});return w&&n.observe(w,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[f,w,A.paused]),r.useEffect(()=>{if(w){v.add(A);let e=document.activeElement;if(!w.contains(e)){let t=new CustomEvent(c,s);w.addEventListener(c,x),w.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(h(r,{select:t}),document.activeElement!==n)return}(d(w).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&h(w))}return()=>{w.removeEventListener(c,x),setTimeout(()=>{let t=new CustomEvent(u,s);w.addEventListener(u,E),w.dispatchEvent(t),t.defaultPrevented||h(null!=e?e:document.body,{select:!0}),w.removeEventListener(u,E),v.remove(A)},0)}}},[w,x,E,A]);let P=r.useCallback(e=>{if(!n&&!f||A.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,r=document.activeElement;if(t&&r){let t=e.currentTarget,[o,i]=function(e){let t=d(e);return[p(t,e),p(t.reverse(),e)]}(t);o&&i?e.shiftKey||r!==i?e.shiftKey&&r===o&&(e.preventDefault(),n&&h(i,{select:!0})):(e.preventDefault(),n&&h(o,{select:!0})):r===t&&e.preventDefault()}},[n,f,A.paused]);return(0,l.jsx)(i.sG.div,{tabIndex:-1,...g,ref:_,onKeyDown:P})});function d(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function p(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function h(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}f.displayName="FocusScope";var v=function(){let e=[];return{add(t){let n=e[0];t!==n&&(null==n||n.pause()),(e=m(e,t)).unshift(t)},remove(t){var n;null==(n=(e=m(e,t))[0])||n.resume()}}}();function m(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}},10345:(e,t,n)=>{n.d(t,{A:()=>X});var r,o,i=n(88237),a=n(50628),l="right-scroll-bar-position",c="width-before-scroll-bar";function u(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var s="undefined"!=typeof window?a.useLayoutEffect:a.useEffect,f=new WeakMap;function d(e){return e}var p=function(e){void 0===e&&(e={});var t,n,r,o,a=(t=null,void 0===n&&(n=d),r=[],o=!1,{read:function(){if(o)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var t=n(e,o);return r.push(t),function(){r=r.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(o=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){o=!0;var t=[];if(r.length){var n=r;r=[],n.forEach(e),t=r}var i=function(){var n=t;t=[],n.forEach(e)},a=function(){return Promise.resolve().then(i)};a(),r={push:function(e){t.push(e),a()},filter:function(e){return t=t.filter(e),r}}}});return a.options=(0,i.__assign)({async:!0,ssr:!1},e),a}(),h=function(){},v=a.forwardRef(function(e,t){var n,r,o,l,c=a.useRef(null),d=a.useState({onScrollCapture:h,onWheelCapture:h,onTouchMoveCapture:h}),v=d[0],m=d[1],y=e.forwardProps,g=e.children,w=e.className,b=e.removeScrollBar,x=e.enabled,E=e.shards,S=e.sideCar,_=e.noIsolation,A=e.inert,P=e.allowPinchZoom,O=e.as,R=e.gapMode,C=(0,i.__rest)(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),T=(n=[c,t],r=function(e){return n.forEach(function(t){return u(t,e)})},(o=(0,a.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,l=o.facade,s(function(){var e=f.get(l);if(e){var t=new Set(e),r=new Set(n),o=l.current;t.forEach(function(e){r.has(e)||u(e,null)}),r.forEach(function(e){t.has(e)||u(e,o)})}f.set(l,n)},[n]),l),L=(0,i.__assign)((0,i.__assign)({},C),v);return a.createElement(a.Fragment,null,x&&a.createElement(S,{sideCar:p,removeScrollBar:b,shards:E,noIsolation:_,inert:A,setCallbacks:m,allowPinchZoom:!!P,lockRef:c,gapMode:R}),y?a.cloneElement(a.Children.only(g),(0,i.__assign)((0,i.__assign)({},L),{ref:T})):a.createElement(void 0===O?"div":O,(0,i.__assign)({},L,{className:w,ref:T}),g))});v.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},v.classNames={fullWidth:c,zeroRight:l};var m=function(e){var t=e.sideCar,n=(0,i.__rest)(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return a.createElement(r,(0,i.__assign)({},n))};m.isSideCarExport=!0;var y=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=o||n.nc;return t&&e.setAttribute("nonce",t),e}())){var i,a;(i=t).styleSheet?i.styleSheet.cssText=r:i.appendChild(document.createTextNode(r)),a=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(a)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},g=function(){var e=y();return function(t,n){a.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},w=function(){var e=g();return function(t){return e(t.styles,t.dynamic),null}},b={left:0,top:0,right:0,gap:0},x=function(e){return parseInt(e||"",10)||0},E=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[x(n),x(r),x(o)]},S=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return b;var t=E(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},_=w(),A="data-scroll-locked",P=function(e,t,n,r){var o=e.left,i=e.top,a=e.right,u=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(u,"px ").concat(r,";\n  }\n  body[").concat(A,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(a,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(u,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(u,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(l," {\n    right: ").concat(u,"px ").concat(r,";\n  }\n  \n  .").concat(c," {\n    margin-right: ").concat(u,"px ").concat(r,";\n  }\n  \n  .").concat(l," .").concat(l," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(c," .").concat(c," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(A,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(u,"px;\n  }\n")},O=function(){var e=parseInt(document.body.getAttribute(A)||"0",10);return isFinite(e)?e:0},R=function(){a.useEffect(function(){return document.body.setAttribute(A,(O()+1).toString()),function(){var e=O()-1;e<=0?document.body.removeAttribute(A):document.body.setAttribute(A,e.toString())}},[])},C=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;R();var i=a.useMemo(function(){return S(o)},[o]);return a.createElement(_,{styles:P(i,!t,o,n?"":"!important")})},T=!1;if("undefined"!=typeof window)try{var L=Object.defineProperty({},"passive",{get:function(){return T=!0,!0}});window.addEventListener("test",L,L),window.removeEventListener("test",L,L)}catch(e){T=!1}var k=!!T&&{passive:!1},j=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},D=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),M(e,r)){var o=N(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},M=function(e,t){return"v"===e?j(t,"overflowY"):j(t,"overflowX")},N=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},F=function(e,t,n,r,o){var i,a=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),l=a*r,c=n.target,u=t.contains(c),s=!1,f=l>0,d=0,p=0;do{var h=N(e,c),v=h[0],m=h[1]-h[2]-a*v;(v||m)&&M(e,c)&&(d+=m,p+=v),c=c instanceof ShadowRoot?c.host:c.parentNode}while(!u&&c!==document.body||u&&(t.contains(c)||t===c));return f&&(o&&1>Math.abs(d)||!o&&l>d)?s=!0:!f&&(o&&1>Math.abs(p)||!o&&-l>p)&&(s=!0),s},W=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},I=function(e){return[e.deltaX,e.deltaY]},B=function(e){return e&&"current"in e?e.current:e},H=0,z=[];let Y=(r=function(e){var t=a.useRef([]),n=a.useRef([0,0]),r=a.useRef(),o=a.useState(H++)[0],l=a.useState(w)[0],c=a.useRef(e);a.useEffect(function(){c.current=e},[e]),a.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(0,i.__spreadArray)([e.lockRef.current],(e.shards||[]).map(B),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var u=a.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!c.current.allowPinchZoom;var o,i=W(e),a=n.current,l="deltaX"in e?e.deltaX:a[0]-i[0],u="deltaY"in e?e.deltaY:a[1]-i[1],s=e.target,f=Math.abs(l)>Math.abs(u)?"h":"v";if("touches"in e&&"h"===f&&"range"===s.type)return!1;var d=D(f,s);if(!d)return!0;if(d?o=f:(o="v"===f?"h":"v",d=D(f,s)),!d)return!1;if(!r.current&&"changedTouches"in e&&(l||u)&&(r.current=o),!o)return!0;var p=r.current||o;return F(p,t,e,"h"===p?l:u,!0)},[]),s=a.useCallback(function(e){if(z.length&&z[z.length-1]===l){var n="deltaY"in e?I(e):W(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta,r[0]===n[0]&&r[1]===n[1])})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(c.current.shards||[]).map(B).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?u(e,o[0]):!c.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),f=a.useCallback(function(e,n,r,o){var i={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),d=a.useCallback(function(e){n.current=W(e),r.current=void 0},[]),p=a.useCallback(function(t){f(t.type,I(t),t.target,u(t,e.lockRef.current))},[]),h=a.useCallback(function(t){f(t.type,W(t),t.target,u(t,e.lockRef.current))},[]);a.useEffect(function(){return z.push(l),e.setCallbacks({onScrollCapture:p,onWheelCapture:p,onTouchMoveCapture:h}),document.addEventListener("wheel",s,k),document.addEventListener("touchmove",s,k),document.addEventListener("touchstart",d,k),function(){z=z.filter(function(e){return e!==l}),document.removeEventListener("wheel",s,k),document.removeEventListener("touchmove",s,k),document.removeEventListener("touchstart",d,k)}},[]);var v=e.removeScrollBar,m=e.inert;return a.createElement(a.Fragment,null,m?a.createElement(l,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,v?a.createElement(C,{gapMode:e.gapMode}):null)},p.useMedium(r),m);var V=a.forwardRef(function(e,t){return a.createElement(v,(0,i.__assign)({},e,{ref:t,sideCar:Y}))});V.classNames=v.classNames;let X=V},11712:(e,t,n)=>{n.d(t,{Eq:()=>s});var r=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},o=new WeakMap,i=new WeakMap,a={},l=0,c=function(e){return e&&(e.host||c(e.parentNode))},u=function(e,t,n,r){var u=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=c(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});a[n]||(a[n]=new WeakMap);var s=a[n],f=[],d=new Set,p=new Set(u),h=function(e){!e||d.has(e)||(d.add(e),h(e.parentNode))};u.forEach(h);var v=function(e){!e||p.has(e)||Array.prototype.forEach.call(e.children,function(e){if(d.has(e))v(e);else try{var t=e.getAttribute(r),a=null!==t&&"false"!==t,l=(o.get(e)||0)+1,c=(s.get(e)||0)+1;o.set(e,l),s.set(e,c),f.push(e),1===l&&a&&i.set(e,!0),1===c&&e.setAttribute(n,"true"),a||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return v(t),d.clear(),l++,function(){f.forEach(function(e){var t=o.get(e)-1,a=s.get(e)-1;o.set(e,t),s.set(e,a),t||(i.has(e)||e.removeAttribute(r),i.delete(e)),a||e.removeAttribute(n)}),--l||(o=new WeakMap,o=new WeakMap,i=new WeakMap,a={})}},s=function(e,t,n){void 0===n&&(n="data-aria-hidden");var o=Array.from(Array.isArray(e)?e:[e]),i=t||r(e);return i?(o.push.apply(o,Array.from(i.querySelectorAll("[aria-live]"))),u(o,i,n,"aria-hidden")):function(){return null}}},15127:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(45707).A)("move-right",[["path",{d:"M18 8L22 12L18 16",key:"1r0oui"}],["path",{d:"M2 12H22",key:"1m8cig"}]])},16279:(e,t,n)=>{n.d(t,{Oh:()=>i});var r=n(50628),o=0;function i(){r.useEffect(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!=(e=n[0])?e:a()),document.body.insertAdjacentElement("beforeend",null!=(t=n[1])?t:a()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function a(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},77222:(e,t,n)=>{n.d(t,{Mz:()=>eZ,i3:()=>eU,UC:()=>e$,bL:()=>eq,Bk:()=>ek});var r=n(50628);let o=["top","right","bottom","left"],i=Math.min,a=Math.max,l=Math.round,c=Math.floor,u=e=>({x:e,y:e}),s={left:"right",right:"left",bottom:"top",top:"bottom"},f={start:"end",end:"start"};function d(e,t){return"function"==typeof e?e(t):e}function p(e){return e.split("-")[0]}function h(e){return e.split("-")[1]}function v(e){return"x"===e?"y":"x"}function m(e){return"y"===e?"height":"width"}function y(e){return["top","bottom"].includes(p(e))?"y":"x"}function g(e){return e.replace(/start|end/g,e=>f[e])}function w(e){return e.replace(/left|right|bottom|top/g,e=>s[e])}function b(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function x(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function E(e,t,n){let r,{reference:o,floating:i}=e,a=y(t),l=v(y(t)),c=m(l),u=p(t),s="y"===a,f=o.x+o.width/2-i.width/2,d=o.y+o.height/2-i.height/2,g=o[c]/2-i[c]/2;switch(u){case"top":r={x:f,y:o.y-i.height};break;case"bottom":r={x:f,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:d};break;case"left":r={x:o.x-i.width,y:d};break;default:r={x:o.x,y:o.y}}switch(h(t)){case"start":r[l]-=g*(n&&s?-1:1);break;case"end":r[l]+=g*(n&&s?-1:1)}return r}let S=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:a}=n,l=i.filter(Boolean),c=await (null==a.isRTL?void 0:a.isRTL(t)),u=await a.getElementRects({reference:e,floating:t,strategy:o}),{x:s,y:f}=E(u,r,c),d=r,p={},h=0;for(let n=0;n<l.length;n++){let{name:i,fn:v}=l[n],{x:m,y:y,data:g,reset:w}=await v({x:s,y:f,initialPlacement:r,placement:d,strategy:o,middlewareData:p,rects:u,platform:a,elements:{reference:e,floating:t}});s=null!=m?m:s,f=null!=y?y:f,p={...p,[i]:{...p[i],...g}},w&&h<=50&&(h++,"object"==typeof w&&(w.placement&&(d=w.placement),w.rects&&(u=!0===w.rects?await a.getElementRects({reference:e,floating:t,strategy:o}):w.rects),{x:s,y:f}=E(u,d,c)),n=-1)}return{x:s,y:f,placement:d,strategy:o,middlewareData:p}};async function _(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:i,rects:a,elements:l,strategy:c}=e,{boundary:u="clippingAncestors",rootBoundary:s="viewport",elementContext:f="floating",altBoundary:p=!1,padding:h=0}=d(t,e),v=b(h),m=l[p?"floating"===f?"reference":"floating":f],y=x(await i.getClippingRect({element:null==(n=await (null==i.isElement?void 0:i.isElement(m)))||n?m:m.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(l.floating)),boundary:u,rootBoundary:s,strategy:c})),g="floating"===f?{x:r,y:o,width:a.floating.width,height:a.floating.height}:a.reference,w=await (null==i.getOffsetParent?void 0:i.getOffsetParent(l.floating)),E=await (null==i.isElement?void 0:i.isElement(w))&&await (null==i.getScale?void 0:i.getScale(w))||{x:1,y:1},S=x(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:g,offsetParent:w,strategy:c}):g);return{top:(y.top-S.top+v.top)/E.y,bottom:(S.bottom-y.bottom+v.bottom)/E.y,left:(y.left-S.left+v.left)/E.x,right:(S.right-y.right+v.right)/E.x}}function A(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function P(e){return o.some(t=>e[t]>=0)}async function O(e,t){let{placement:n,platform:r,elements:o}=e,i=await (null==r.isRTL?void 0:r.isRTL(o.floating)),a=p(n),l=h(n),c="y"===y(n),u=["left","top"].includes(a)?-1:1,s=i&&c?-1:1,f=d(t,e),{mainAxis:v,crossAxis:m,alignmentAxis:g}="number"==typeof f?{mainAxis:f,crossAxis:0,alignmentAxis:null}:{mainAxis:f.mainAxis||0,crossAxis:f.crossAxis||0,alignmentAxis:f.alignmentAxis};return l&&"number"==typeof g&&(m="end"===l?-1*g:g),c?{x:m*s,y:v*u}:{x:v*u,y:m*s}}function R(){return"undefined"!=typeof window}function C(e){return k(e)?(e.nodeName||"").toLowerCase():"#document"}function T(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function L(e){var t;return null==(t=(k(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function k(e){return!!R()&&(e instanceof Node||e instanceof T(e).Node)}function j(e){return!!R()&&(e instanceof Element||e instanceof T(e).Element)}function D(e){return!!R()&&(e instanceof HTMLElement||e instanceof T(e).HTMLElement)}function M(e){return!!R()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof T(e).ShadowRoot)}function N(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=H(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function F(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function W(e){let t=I(),n=j(e)?H(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function I(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function B(e){return["html","body","#document"].includes(C(e))}function H(e){return T(e).getComputedStyle(e)}function z(e){return j(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function Y(e){if("html"===C(e))return e;let t=e.assignedSlot||e.parentNode||M(e)&&e.host||L(e);return M(t)?t.host:t}function V(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=Y(t);return B(n)?t.ownerDocument?t.ownerDocument.body:t.body:D(n)&&N(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),a=T(o);if(i){let e=X(a);return t.concat(a,a.visualViewport||[],N(o)?o:[],e&&n?V(e):[])}return t.concat(o,V(o,[],n))}function X(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function G(e){let t=H(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=D(e),i=o?e.offsetWidth:n,a=o?e.offsetHeight:r,c=l(n)!==i||l(r)!==a;return c&&(n=i,r=a),{width:n,height:r,$:c}}function K(e){return j(e)?e:e.contextElement}function q(e){let t=K(e);if(!D(t))return u(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:i}=G(t),a=(i?l(n.width):n.width)/r,c=(i?l(n.height):n.height)/o;return a&&Number.isFinite(a)||(a=1),c&&Number.isFinite(c)||(c=1),{x:a,y:c}}let Z=u(0);function $(e){let t=T(e);return I()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:Z}function U(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let i=e.getBoundingClientRect(),a=K(e),l=u(1);t&&(r?j(r)&&(l=q(r)):l=q(e));let c=(void 0===(o=n)&&(o=!1),r&&(!o||r===T(a))&&o)?$(a):u(0),s=(i.left+c.x)/l.x,f=(i.top+c.y)/l.y,d=i.width/l.x,p=i.height/l.y;if(a){let e=T(a),t=r&&j(r)?T(r):r,n=e,o=X(n);for(;o&&r&&t!==n;){let e=q(o),t=o.getBoundingClientRect(),r=H(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,a=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;s*=e.x,f*=e.y,d*=e.x,p*=e.y,s+=i,f+=a,o=X(n=T(o))}}return x({width:d,height:p,x:s,y:f})}function J(e,t){let n=z(e).scrollLeft;return t?t.left+n:U(L(e)).left+n}function Q(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:J(e,r)),y:r.top+t.scrollTop}}function ee(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=T(e),r=L(e),o=n.visualViewport,i=r.clientWidth,a=r.clientHeight,l=0,c=0;if(o){i=o.width,a=o.height;let e=I();(!e||e&&"fixed"===t)&&(l=o.offsetLeft,c=o.offsetTop)}return{width:i,height:a,x:l,y:c}}(e,n);else if("document"===t)r=function(e){let t=L(e),n=z(e),r=e.ownerDocument.body,o=a(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=a(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),l=-n.scrollLeft+J(e),c=-n.scrollTop;return"rtl"===H(r).direction&&(l+=a(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:l,y:c}}(L(e));else if(j(t))r=function(e,t){let n=U(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=D(e)?q(e):u(1),a=e.clientWidth*i.x,l=e.clientHeight*i.y;return{width:a,height:l,x:o*i.x,y:r*i.y}}(t,n);else{let n=$(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return x(r)}function et(e){return"static"===H(e).position}function en(e,t){if(!D(e)||"fixed"===H(e).position)return null;if(t)return t(e);let n=e.offsetParent;return L(e)===n&&(n=n.ownerDocument.body),n}function er(e,t){let n=T(e);if(F(e))return n;if(!D(e)){let t=Y(e);for(;t&&!B(t);){if(j(t)&&!et(t))return t;t=Y(t)}return n}let r=en(e,t);for(;r&&["table","td","th"].includes(C(r))&&et(r);)r=en(r,t);return r&&B(r)&&et(r)&&!W(r)?n:r||function(e){let t=Y(e);for(;D(t)&&!B(t);){if(W(t))return t;if(F(t))break;t=Y(t)}return null}(e)||n}let eo=async function(e){let t=this.getOffsetParent||er,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=D(t),o=L(t),i="fixed"===n,a=U(e,!0,i,t),l={scrollLeft:0,scrollTop:0},c=u(0);if(r||!r&&!i)if(("body"!==C(t)||N(o))&&(l=z(t)),r){let e=U(t,!0,i,t);c.x=e.x+t.clientLeft,c.y=e.y+t.clientTop}else o&&(c.x=J(o));let s=!o||r||i?u(0):Q(o,l);return{x:a.left+l.scrollLeft-c.x-s.x,y:a.top+l.scrollTop-c.y-s.y,width:a.width,height:a.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},ei={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,i="fixed"===o,a=L(r),l=!!t&&F(t.floating);if(r===a||l&&i)return n;let c={scrollLeft:0,scrollTop:0},s=u(1),f=u(0),d=D(r);if((d||!d&&!i)&&(("body"!==C(r)||N(a))&&(c=z(r)),D(r))){let e=U(r);s=q(r),f.x=e.x+r.clientLeft,f.y=e.y+r.clientTop}let p=!a||d||i?u(0):Q(a,c,!0);return{width:n.width*s.x,height:n.height*s.y,x:n.x*s.x-c.scrollLeft*s.x+f.x+p.x,y:n.y*s.y-c.scrollTop*s.y+f.y+p.y}},getDocumentElement:L,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,l=[..."clippingAncestors"===n?F(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=V(e,[],!1).filter(e=>j(e)&&"body"!==C(e)),o=null,i="fixed"===H(e).position,a=i?Y(e):e;for(;j(a)&&!B(a);){let t=H(a),n=W(a);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&!!o&&["absolute","fixed"].includes(o.position)||N(a)&&!n&&function e(t,n){let r=Y(t);return!(r===n||!j(r)||B(r))&&("fixed"===H(r).position||e(r,n))}(e,a))?r=r.filter(e=>e!==a):o=t,a=Y(a)}return t.set(e,r),r}(t,this._c):[].concat(n),r],c=l[0],u=l.reduce((e,n)=>{let r=ee(t,n,o);return e.top=a(r.top,e.top),e.right=i(r.right,e.right),e.bottom=i(r.bottom,e.bottom),e.left=a(r.left,e.left),e},ee(t,c,o));return{width:u.right-u.left,height:u.bottom-u.top,x:u.left,y:u.top}},getOffsetParent:er,getElementRects:eo,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=G(e);return{width:t,height:n}},getScale:q,isElement:j,isRTL:function(e){return"rtl"===H(e).direction}};function ea(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let el=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:l,platform:c,elements:u,middlewareData:s}=t,{element:f,padding:p=0}=d(e,t)||{};if(null==f)return{};let g=b(p),w={x:n,y:r},x=v(y(o)),E=m(x),S=await c.getDimensions(f),_="y"===x,A=_?"clientHeight":"clientWidth",P=l.reference[E]+l.reference[x]-w[x]-l.floating[E],O=w[x]-l.reference[x],R=await (null==c.getOffsetParent?void 0:c.getOffsetParent(f)),C=R?R[A]:0;C&&await (null==c.isElement?void 0:c.isElement(R))||(C=u.floating[A]||l.floating[E]);let T=C/2-S[E]/2-1,L=i(g[_?"top":"left"],T),k=i(g[_?"bottom":"right"],T),j=C-S[E]-k,D=C/2-S[E]/2+(P/2-O/2),M=a(L,i(D,j)),N=!s.arrow&&null!=h(o)&&D!==M&&l.reference[E]/2-(D<L?L:k)-S[E]/2<0,F=N?D<L?D-L:D-j:0;return{[x]:w[x]+F,data:{[x]:M,centerOffset:D-M-F,...N&&{alignmentOffset:F}},reset:N}}}),ec=(e,t,n)=>{let r=new Map,o={platform:ei,...n},i={...o.platform,_c:r};return S(e,t,{...o,platform:i})};var eu=n(6341),es="undefined"!=typeof document?r.useLayoutEffect:r.useEffect;function ef(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!ef(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!ef(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function ed(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function ep(e,t){let n=ed(e);return Math.round(t*n)/n}function eh(e){let t=r.useRef(e);return es(()=>{t.current=e}),t}let ev=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?el({element:n.current,padding:r}).fn(t):{}:n?el({element:n,padding:r}).fn(t):{}}}),em=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:i,placement:a,middlewareData:l}=t,c=await O(t,e);return a===(null==(n=l.offset)?void 0:n.placement)&&null!=(r=l.arrow)&&r.alignmentOffset?{}:{x:o+c.x,y:i+c.y,data:{...c,placement:a}}}}}(e),options:[e,t]}),ey=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:o}=t,{mainAxis:l=!0,crossAxis:c=!1,limiter:u={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...s}=d(e,t),f={x:n,y:r},h=await _(t,s),m=y(p(o)),g=v(m),w=f[g],b=f[m];if(l){let e="y"===g?"top":"left",t="y"===g?"bottom":"right",n=w+h[e],r=w-h[t];w=a(n,i(w,r))}if(c){let e="y"===m?"top":"left",t="y"===m?"bottom":"right",n=b+h[e],r=b-h[t];b=a(n,i(b,r))}let x=u.fn({...t,[g]:w,[m]:b});return{...x,data:{x:x.x-n,y:x.y-r,enabled:{[g]:l,[m]:c}}}}}}(e),options:[e,t]}),eg=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:i,middlewareData:a}=t,{offset:l=0,mainAxis:c=!0,crossAxis:u=!0}=d(e,t),s={x:n,y:r},f=y(o),h=v(f),m=s[h],g=s[f],w=d(l,t),b="number"==typeof w?{mainAxis:w,crossAxis:0}:{mainAxis:0,crossAxis:0,...w};if(c){let e="y"===h?"height":"width",t=i.reference[h]-i.floating[e]+b.mainAxis,n=i.reference[h]+i.reference[e]-b.mainAxis;m<t?m=t:m>n&&(m=n)}if(u){var x,E;let e="y"===h?"width":"height",t=["top","left"].includes(p(o)),n=i.reference[f]-i.floating[e]+(t&&(null==(x=a.offset)?void 0:x[f])||0)+(t?0:b.crossAxis),r=i.reference[f]+i.reference[e]+(t?0:(null==(E=a.offset)?void 0:E[f])||0)-(t?b.crossAxis:0);g<n?g=n:g>r&&(g=r)}return{[h]:m,[f]:g}}}}(e),options:[e,t]}),ew=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,i,a;let{placement:l,middlewareData:c,rects:u,initialPlacement:s,platform:f,elements:b}=t,{mainAxis:x=!0,crossAxis:E=!0,fallbackPlacements:S,fallbackStrategy:A="bestFit",fallbackAxisSideDirection:P="none",flipAlignment:O=!0,...R}=d(e,t);if(null!=(n=c.arrow)&&n.alignmentOffset)return{};let C=p(l),T=y(s),L=p(s)===s,k=await (null==f.isRTL?void 0:f.isRTL(b.floating)),j=S||(L||!O?[w(s)]:function(e){let t=w(e);return[g(e),t,g(t)]}(s)),D="none"!==P;!S&&D&&j.push(...function(e,t,n,r){let o=h(e),i=function(e,t,n){let r=["left","right"],o=["right","left"];switch(e){case"top":case"bottom":if(n)return t?o:r;return t?r:o;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(p(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(g)))),i}(s,O,P,k));let M=[s,...j],N=await _(t,R),F=[],W=(null==(r=c.flip)?void 0:r.overflows)||[];if(x&&F.push(N[C]),E){let e=function(e,t,n){void 0===n&&(n=!1);let r=h(e),o=v(y(e)),i=m(o),a="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(a=w(a)),[a,w(a)]}(l,u,k);F.push(N[e[0]],N[e[1]])}if(W=[...W,{placement:l,overflows:F}],!F.every(e=>e<=0)){let e=((null==(o=c.flip)?void 0:o.index)||0)+1,t=M[e];if(t)return{data:{index:e,overflows:W},reset:{placement:t}};let n=null==(i=W.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(A){case"bestFit":{let e=null==(a=W.filter(e=>{if(D){let t=y(e.placement);return t===T||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:a[0];e&&(n=e);break}case"initialPlacement":n=s}if(l!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),eb=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let o,l,{placement:c,rects:u,platform:s,elements:f}=t,{apply:v=()=>{},...m}=d(e,t),g=await _(t,m),w=p(c),b=h(c),x="y"===y(c),{width:E,height:S}=u.floating;"top"===w||"bottom"===w?(o=w,l=b===(await (null==s.isRTL?void 0:s.isRTL(f.floating))?"start":"end")?"left":"right"):(l=w,o="end"===b?"top":"bottom");let A=S-g.top-g.bottom,P=E-g.left-g.right,O=i(S-g[o],A),R=i(E-g[l],P),C=!t.middlewareData.shift,T=O,L=R;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(L=P),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(T=A),C&&!b){let e=a(g.left,0),t=a(g.right,0),n=a(g.top,0),r=a(g.bottom,0);x?L=E-2*(0!==e||0!==t?e+t:a(g.left,g.right)):T=S-2*(0!==n||0!==r?n+r:a(g.top,g.bottom))}await v({...t,availableWidth:L,availableHeight:T});let k=await s.getDimensions(f.floating);return E!==k.width||S!==k.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),ex=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=d(e,t);switch(r){case"referenceHidden":{let e=A(await _(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:P(e)}}}case"escaped":{let e=A(await _(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:P(e)}}}default:return{}}}}}(e),options:[e,t]}),eE=(e,t)=>({...ev(e),options:[e,t]});var eS=n(64826),e_=n(6024),eA=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,e_.jsx)(eS.sG.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,e_.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eA.displayName="Arrow";var eP=n(98064),eO=n(48733),eR=n(72336),eC=n(84268),eT="Popper",[eL,ek]=(0,eO.A)(eT),[ej,eD]=eL(eT),eM=e=>{let{__scopePopper:t,children:n}=e,[o,i]=r.useState(null);return(0,e_.jsx)(ej,{scope:t,anchor:o,onAnchorChange:i,children:n})};eM.displayName=eT;var eN="PopperAnchor",eF=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:o,...i}=e,a=eD(eN,n),l=r.useRef(null),c=(0,eP.s)(t,l);return r.useEffect(()=>{a.onAnchorChange((null==o?void 0:o.current)||l.current)}),o?null:(0,e_.jsx)(eS.sG.div,{...i,ref:c})});eF.displayName=eN;var eW="PopperContent",[eI,eB]=eL(eW),eH=r.forwardRef((e,t)=>{var n,o,l,u,s,f,d,p;let{__scopePopper:h,side:v="bottom",sideOffset:m=0,align:y="center",alignOffset:g=0,arrowPadding:w=0,avoidCollisions:b=!0,collisionBoundary:x=[],collisionPadding:E=0,sticky:S="partial",hideWhenDetached:_=!1,updatePositionStrategy:A="optimized",onPlaced:P,...O}=e,R=eD(eW,h),[C,T]=r.useState(null),k=(0,eP.s)(t,e=>T(e)),[j,D]=r.useState(null),M=function(e){let[t,n]=r.useState(void 0);return(0,eC.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}(j),N=null!=(d=null==M?void 0:M.width)?d:0,F=null!=(p=null==M?void 0:M.height)?p:0,W="number"==typeof E?E:{top:0,right:0,bottom:0,left:0,...E},I=Array.isArray(x)?x:[x],B=I.length>0,H={padding:W,boundary:I.filter(eX),altBoundary:B},{refs:z,floatingStyles:Y,placement:X,isPositioned:G,middlewareData:q}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:o=[],platform:i,elements:{reference:a,floating:l}={},transform:c=!0,whileElementsMounted:u,open:s}=e,[f,d]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,h]=r.useState(o);ef(p,o)||h(o);let[v,m]=r.useState(null),[y,g]=r.useState(null),w=r.useCallback(e=>{e!==S.current&&(S.current=e,m(e))},[]),b=r.useCallback(e=>{e!==_.current&&(_.current=e,g(e))},[]),x=a||v,E=l||y,S=r.useRef(null),_=r.useRef(null),A=r.useRef(f),P=null!=u,O=eh(u),R=eh(i),C=eh(s),T=r.useCallback(()=>{if(!S.current||!_.current)return;let e={placement:t,strategy:n,middleware:p};R.current&&(e.platform=R.current),ec(S.current,_.current,e).then(e=>{let t={...e,isPositioned:!1!==C.current};L.current&&!ef(A.current,t)&&(A.current=t,eu.flushSync(()=>{d(t)}))})},[p,t,n,R,C]);es(()=>{!1===s&&A.current.isPositioned&&(A.current.isPositioned=!1,d(e=>({...e,isPositioned:!1})))},[s]);let L=r.useRef(!1);es(()=>(L.current=!0,()=>{L.current=!1}),[]),es(()=>{if(x&&(S.current=x),E&&(_.current=E),x&&E){if(O.current)return O.current(x,E,T);T()}},[x,E,T,O,P]);let k=r.useMemo(()=>({reference:S,floating:_,setReference:w,setFloating:b}),[w,b]),j=r.useMemo(()=>({reference:x,floating:E}),[x,E]),D=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!j.floating)return e;let t=ep(j.floating,f.x),r=ep(j.floating,f.y);return c?{...e,transform:"translate("+t+"px, "+r+"px)",...ed(j.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,c,j.floating,f.x,f.y]);return r.useMemo(()=>({...f,update:T,refs:k,elements:j,floatingStyles:D}),[f,T,k,j,D])}({strategy:"fixed",placement:v+("center"!==y?"-"+y:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:l=!0,ancestorResize:u=!0,elementResize:s="function"==typeof ResizeObserver,layoutShift:f="function"==typeof IntersectionObserver,animationFrame:d=!1}=r,p=K(e),h=l||u?[...p?V(p):[],...V(t)]:[];h.forEach(e=>{l&&e.addEventListener("scroll",n,{passive:!0}),u&&e.addEventListener("resize",n)});let v=p&&f?function(e,t){let n,r=null,o=L(e);function l(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function u(s,f){void 0===s&&(s=!1),void 0===f&&(f=1),l();let d=e.getBoundingClientRect(),{left:p,top:h,width:v,height:m}=d;if(s||t(),!v||!m)return;let y=c(h),g=c(o.clientWidth-(p+v)),w={rootMargin:-y+"px "+-g+"px "+-c(o.clientHeight-(h+m))+"px "+-c(p)+"px",threshold:a(0,i(1,f))||1},b=!0;function x(t){let r=t[0].intersectionRatio;if(r!==f){if(!b)return u();r?u(!1,r):n=setTimeout(()=>{u(!1,1e-7)},1e3)}1!==r||ea(d,e.getBoundingClientRect())||u(),b=!1}try{r=new IntersectionObserver(x,{...w,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(x,w)}r.observe(e)}(!0),l}(p,n):null,m=-1,y=null;s&&(y=new ResizeObserver(e=>{let[r]=e;r&&r.target===p&&y&&(y.unobserve(t),cancelAnimationFrame(m),m=requestAnimationFrame(()=>{var e;null==(e=y)||e.observe(t)})),n()}),p&&!d&&y.observe(p),y.observe(t));let g=d?U(e):null;return d&&function t(){let r=U(e);g&&!ea(g,r)&&n(),g=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;h.forEach(e=>{l&&e.removeEventListener("scroll",n),u&&e.removeEventListener("resize",n)}),null==v||v(),null==(e=y)||e.disconnect(),y=null,d&&cancelAnimationFrame(o)}}(...t,{animationFrame:"always"===A})},elements:{reference:R.anchor},middleware:[em({mainAxis:m+F,alignmentAxis:g}),b&&ey({mainAxis:!0,crossAxis:!1,limiter:"partial"===S?eg():void 0,...H}),b&&ew({...H}),eb({...H,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:o}=e,{width:i,height:a}=n.reference,l=t.floating.style;l.setProperty("--radix-popper-available-width","".concat(r,"px")),l.setProperty("--radix-popper-available-height","".concat(o,"px")),l.setProperty("--radix-popper-anchor-width","".concat(i,"px")),l.setProperty("--radix-popper-anchor-height","".concat(a,"px"))}}),j&&eE({element:j,padding:w}),eG({arrowWidth:N,arrowHeight:F}),_&&ex({strategy:"referenceHidden",...H})]}),[Z,$]=eK(X),J=(0,eR.c)(P);(0,eC.N)(()=>{G&&(null==J||J())},[G,J]);let Q=null==(n=q.arrow)?void 0:n.x,ee=null==(o=q.arrow)?void 0:o.y,et=(null==(l=q.arrow)?void 0:l.centerOffset)!==0,[en,er]=r.useState();return(0,eC.N)(()=>{C&&er(window.getComputedStyle(C).zIndex)},[C]),(0,e_.jsx)("div",{ref:z.setFloating,"data-radix-popper-content-wrapper":"",style:{...Y,transform:G?Y.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:en,"--radix-popper-transform-origin":[null==(u=q.transformOrigin)?void 0:u.x,null==(s=q.transformOrigin)?void 0:s.y].join(" "),...(null==(f=q.hide)?void 0:f.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,e_.jsx)(eI,{scope:h,placedSide:Z,onArrowChange:D,arrowX:Q,arrowY:ee,shouldHideArrow:et,children:(0,e_.jsx)(eS.sG.div,{"data-side":Z,"data-align":$,...O,ref:k,style:{...O.style,animation:G?void 0:"none"}})})})});eH.displayName=eW;var ez="PopperArrow",eY={top:"bottom",right:"left",bottom:"top",left:"right"},eV=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=eB(ez,n),i=eY[o.placedSide];return(0,e_.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,e_.jsx)(eA,{...r,ref:t,style:{...r.style,display:"block"}})})});function eX(e){return null!==e}eV.displayName=ez;var eG=e=>({name:"transformOrigin",options:e,fn(t){var n,r,o,i,a;let{placement:l,rects:c,middlewareData:u}=t,s=(null==(n=u.arrow)?void 0:n.centerOffset)!==0,f=s?0:e.arrowWidth,d=s?0:e.arrowHeight,[p,h]=eK(l),v={start:"0%",center:"50%",end:"100%"}[h],m=(null!=(i=null==(r=u.arrow)?void 0:r.x)?i:0)+f/2,y=(null!=(a=null==(o=u.arrow)?void 0:o.y)?a:0)+d/2,g="",w="";return"bottom"===p?(g=s?v:"".concat(m,"px"),w="".concat(-d,"px")):"top"===p?(g=s?v:"".concat(m,"px"),w="".concat(c.floating.height+d,"px")):"right"===p?(g="".concat(-d,"px"),w=s?v:"".concat(y,"px")):"left"===p&&(g="".concat(c.floating.width+d,"px"),w=s?v:"".concat(y,"px")),{data:{x:g,y:w}}}});function eK(e){let[t,n="center"]=e.split("-");return[t,n]}var eq=eM,eZ=eF,e$=eH,eU=eV},78557:(e,t,n)=>{n.d(t,{qW:()=>d});var r,o=n(50628),i=n(13859),a=n(64826),l=n(98064),c=n(72336),u=n(6024),s="dismissableLayer.update",f=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),d=o.forwardRef((e,t)=>{var n,d;let{disableOutsidePointerEvents:v=!1,onEscapeKeyDown:m,onPointerDownOutside:y,onFocusOutside:g,onInteractOutside:w,onDismiss:b,...x}=e,E=o.useContext(f),[S,_]=o.useState(null),A=null!=(d=null==S?void 0:S.ownerDocument)?d:null==(n=globalThis)?void 0:n.document,[,P]=o.useState({}),O=(0,l.s)(t,e=>_(e)),R=Array.from(E.layers),[C]=[...E.layersWithOutsidePointerEventsDisabled].slice(-1),T=R.indexOf(C),L=S?R.indexOf(S):-1,k=E.layersWithOutsidePointerEventsDisabled.size>0,j=L>=T,D=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,c.c)(e),i=o.useRef(!1),a=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!i.current){let t=function(){h("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",a.current),a.current=t,n.addEventListener("click",a.current,{once:!0})):t()}else n.removeEventListener("click",a.current);i.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",a.current)}},[n,r]),{onPointerDownCapture:()=>i.current=!0}}(e=>{let t=e.target,n=[...E.branches].some(e=>e.contains(t));j&&!n&&(null==y||y(e),null==w||w(e),e.defaultPrevented||null==b||b())},A),M=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,c.c)(e),i=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!i.current&&h("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>i.current=!0,onBlurCapture:()=>i.current=!1}}(e=>{let t=e.target;![...E.branches].some(e=>e.contains(t))&&(null==g||g(e),null==w||w(e),e.defaultPrevented||null==b||b())},A);return!function(e,t=globalThis?.document){let n=(0,c.c)(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{L===E.layers.size-1&&(null==m||m(e),!e.defaultPrevented&&b&&(e.preventDefault(),b()))},A),o.useEffect(()=>{if(S)return v&&(0===E.layersWithOutsidePointerEventsDisabled.size&&(r=A.body.style.pointerEvents,A.body.style.pointerEvents="none"),E.layersWithOutsidePointerEventsDisabled.add(S)),E.layers.add(S),p(),()=>{v&&1===E.layersWithOutsidePointerEventsDisabled.size&&(A.body.style.pointerEvents=r)}},[S,A,v,E]),o.useEffect(()=>()=>{S&&(E.layers.delete(S),E.layersWithOutsidePointerEventsDisabled.delete(S),p())},[S,E]),o.useEffect(()=>{let e=()=>P({});return document.addEventListener(s,e),()=>document.removeEventListener(s,e)},[]),(0,u.jsx)(a.sG.div,{...x,ref:O,style:{pointerEvents:k?j?"auto":"none":void 0,...e.style},onFocusCapture:(0,i.m)(e.onFocusCapture,M.onFocusCapture),onBlurCapture:(0,i.m)(e.onBlurCapture,M.onBlurCapture),onPointerDownCapture:(0,i.m)(e.onPointerDownCapture,D.onPointerDownCapture)})});function p(){let e=new CustomEvent(s);document.dispatchEvent(e)}function h(e,t,n,r){let{discrete:o}=r,i=n.originalEvent.target,l=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&i.addEventListener(e,t,{once:!0}),o?(0,a.hO)(i,l):i.dispatchEvent(l)}d.displayName="DismissableLayer",o.forwardRef((e,t)=>{let n=o.useContext(f),r=o.useRef(null),i=(0,l.s)(t,r);return o.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,u.jsx)(a.sG.div,{...e,ref:i})}).displayName="DismissableLayerBranch"},88237:(e,t,n)=>{n.r(t),n.d(t,{__addDisposableResource:()=>M,__assign:()=>i,__asyncDelegator:()=>A,__asyncGenerator:()=>_,__asyncValues:()=>P,__await:()=>S,__awaiter:()=>h,__classPrivateFieldGet:()=>k,__classPrivateFieldIn:()=>D,__classPrivateFieldSet:()=>j,__createBinding:()=>m,__decorate:()=>l,__disposeResources:()=>F,__esDecorate:()=>u,__exportStar:()=>y,__extends:()=>o,__generator:()=>v,__importDefault:()=>L,__importStar:()=>T,__makeTemplateObject:()=>O,__metadata:()=>p,__param:()=>c,__propKey:()=>f,__read:()=>w,__rest:()=>a,__rewriteRelativeImportExtension:()=>W,__runInitializers:()=>s,__setFunctionName:()=>d,__spread:()=>b,__spreadArray:()=>E,__spreadArrays:()=>x,__values:()=>g,default:()=>I});var r=function(e,t){return(r=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)};function o(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}var i=function(){return(i=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function a(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}function l(e,t,n,r){var o,i=arguments.length,a=i<3?t:null===r?r=Object.getOwnPropertyDescriptor(t,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,n,r);else for(var l=e.length-1;l>=0;l--)(o=e[l])&&(a=(i<3?o(a):i>3?o(t,n,a):o(t,n))||a);return i>3&&a&&Object.defineProperty(t,n,a),a}function c(e,t){return function(n,r){t(n,r,e)}}function u(e,t,n,r,o,i){function a(e){if(void 0!==e&&"function"!=typeof e)throw TypeError("Function expected");return e}for(var l,c=r.kind,u="getter"===c?"get":"setter"===c?"set":"value",s=!t&&e?r.static?e:e.prototype:null,f=t||(s?Object.getOwnPropertyDescriptor(s,r.name):{}),d=!1,p=n.length-1;p>=0;p--){var h={};for(var v in r)h[v]="access"===v?{}:r[v];for(var v in r.access)h.access[v]=r.access[v];h.addInitializer=function(e){if(d)throw TypeError("Cannot add initializers after decoration has completed");i.push(a(e||null))};var m=(0,n[p])("accessor"===c?{get:f.get,set:f.set}:f[u],h);if("accessor"===c){if(void 0===m)continue;if(null===m||"object"!=typeof m)throw TypeError("Object expected");(l=a(m.get))&&(f.get=l),(l=a(m.set))&&(f.set=l),(l=a(m.init))&&o.unshift(l)}else(l=a(m))&&("field"===c?o.unshift(l):f[u]=l)}s&&Object.defineProperty(s,r.name,f),d=!0}function s(e,t,n){for(var r=arguments.length>2,o=0;o<t.length;o++)n=r?t[o].call(e,n):t[o].call(e);return r?n:void 0}function f(e){return"symbol"==typeof e?e:"".concat(e)}function d(e,t,n){return"symbol"==typeof t&&(t=t.description?"[".concat(t.description,"]"):""),Object.defineProperty(e,"name",{configurable:!0,value:n?"".concat(n," ",t):t})}function p(e,t){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(e,t)}function h(e,t,n,r){return new(n||(n=Promise))(function(o,i){function a(e){try{c(r.next(e))}catch(e){i(e)}}function l(e){try{c(r.throw(e))}catch(e){i(e)}}function c(e){var t;e.done?o(e.value):((t=e.value)instanceof n?t:new n(function(e){e(t)})).then(a,l)}c((r=r.apply(e,t||[])).next())})}function v(e,t){var n,r,o,i={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]},a=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return a.next=l(0),a.throw=l(1),a.return=l(2),"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function l(l){return function(c){var u=[l,c];if(n)throw TypeError("Generator is already executing.");for(;a&&(a=0,u[0]&&(i=0)),i;)try{if(n=1,r&&(o=2&u[0]?r.return:u[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,u[1])).done)return o;switch(r=0,o&&(u=[2&u[0],o.value]),u[0]){case 0:case 1:o=u;break;case 4:return i.label++,{value:u[1],done:!1};case 5:i.label++,r=u[1],u=[0];continue;case 7:u=i.ops.pop(),i.trys.pop();continue;default:if(!(o=(o=i.trys).length>0&&o[o.length-1])&&(6===u[0]||2===u[0])){i=0;continue}if(3===u[0]&&(!o||u[1]>o[0]&&u[1]<o[3])){i.label=u[1];break}if(6===u[0]&&i.label<o[1]){i.label=o[1],o=u;break}if(o&&i.label<o[2]){i.label=o[2],i.ops.push(u);break}o[2]&&i.ops.pop(),i.trys.pop();continue}u=t.call(e,i)}catch(e){u=[6,e],r=0}finally{n=o=0}if(5&u[0])throw u[1];return{value:u[0]?u[1]:void 0,done:!0}}}}var m=Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);(!o||("get"in o?!t.__esModule:o.writable||o.configurable))&&(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]};function y(e,t){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(t,n)||m(t,e,n)}function g(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function w(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)a.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return a}function b(){for(var e=[],t=0;t<arguments.length;t++)e=e.concat(w(arguments[t]));return e}function x(){for(var e=0,t=0,n=arguments.length;t<n;t++)e+=arguments[t].length;for(var r=Array(e),o=0,t=0;t<n;t++)for(var i=arguments[t],a=0,l=i.length;a<l;a++,o++)r[o]=i[a];return r}function E(e,t,n){if(n||2==arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))}function S(e){return this instanceof S?(this.v=e,this):new S(e)}function _(e,t,n){if(!Symbol.asyncIterator)throw TypeError("Symbol.asyncIterator is not defined.");var r,o=n.apply(e,t||[]),i=[];return r=Object.create(("function"==typeof AsyncIterator?AsyncIterator:Object).prototype),a("next"),a("throw"),a("return",function(e){return function(t){return Promise.resolve(t).then(e,u)}}),r[Symbol.asyncIterator]=function(){return this},r;function a(e,t){o[e]&&(r[e]=function(t){return new Promise(function(n,r){i.push([e,t,n,r])>1||l(e,t)})},t&&(r[e]=t(r[e])))}function l(e,t){try{var n;(n=o[e](t)).value instanceof S?Promise.resolve(n.value.v).then(c,u):s(i[0][2],n)}catch(e){s(i[0][3],e)}}function c(e){l("next",e)}function u(e){l("throw",e)}function s(e,t){e(t),i.shift(),i.length&&l(i[0][0],i[0][1])}}function A(e){var t,n;return t={},r("next"),r("throw",function(e){throw e}),r("return"),t[Symbol.iterator]=function(){return this},t;function r(r,o){t[r]=e[r]?function(t){return(n=!n)?{value:S(e[r](t)),done:!1}:o?o(t):t}:o}}function P(e){if(!Symbol.asyncIterator)throw TypeError("Symbol.asyncIterator is not defined.");var t,n=e[Symbol.asyncIterator];return n?n.call(e):(e=g(e),t={},r("next"),r("throw"),r("return"),t[Symbol.asyncIterator]=function(){return this},t);function r(n){t[n]=e[n]&&function(t){return new Promise(function(r,o){var i,a,l;i=r,a=o,l=(t=e[n](t)).done,Promise.resolve(t.value).then(function(e){i({value:e,done:l})},a)})}}}function O(e,t){return Object.defineProperty?Object.defineProperty(e,"raw",{value:t}):e.raw=t,e}var R=Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t},C=function(e){return(C=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t})(e)};function T(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=C(e),r=0;r<n.length;r++)"default"!==n[r]&&m(t,e,n[r]);return R(t,e),t}function L(e){return e&&e.__esModule?e:{default:e}}function k(e,t,n,r){if("a"===n&&!r)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!r:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===n?r:"a"===n?r.call(e):r?r.value:t.get(e)}function j(e,t,n,r,o){if("m"===r)throw TypeError("Private method is not writable");if("a"===r&&!o)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!o:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===r?o.call(e,n):o?o.value=n:t.set(e,n),n}function D(e,t){if(null===t||"object"!=typeof t&&"function"!=typeof t)throw TypeError("Cannot use 'in' operator on non-object");return"function"==typeof e?t===e:e.has(t)}function M(e,t,n){if(null!=t){var r,o;if("object"!=typeof t&&"function"!=typeof t)throw TypeError("Object expected.");if(n){if(!Symbol.asyncDispose)throw TypeError("Symbol.asyncDispose is not defined.");r=t[Symbol.asyncDispose]}if(void 0===r){if(!Symbol.dispose)throw TypeError("Symbol.dispose is not defined.");r=t[Symbol.dispose],n&&(o=r)}if("function"!=typeof r)throw TypeError("Object not disposable.");o&&(r=function(){try{o.call(this)}catch(e){return Promise.reject(e)}}),e.stack.push({value:t,dispose:r,async:n})}else n&&e.stack.push({async:!0});return t}var N="function"==typeof SuppressedError?SuppressedError:function(e,t,n){var r=Error(n);return r.name="SuppressedError",r.error=e,r.suppressed=t,r};function F(e){function t(t){e.error=e.hasError?new N(t,e.error,"An error was suppressed during disposal."):t,e.hasError=!0}var n,r=0;return function o(){for(;n=e.stack.pop();)try{if(!n.async&&1===r)return r=0,e.stack.push(n),Promise.resolve().then(o);if(n.dispose){var i=n.dispose.call(n.value);if(n.async)return r|=2,Promise.resolve(i).then(o,function(e){return t(e),o()})}else r|=1}catch(e){t(e)}if(1===r)return e.hasError?Promise.reject(e.error):Promise.resolve();if(e.hasError)throw e.error}()}function W(e,t){return"string"==typeof e&&/^\.\.?\//.test(e)?e.replace(/\.(tsx)$|((?:\.d)?)((?:\.[^./]+?)?)\.([cm]?)ts$/i,function(e,n,r,o,i){return n?t?".jsx":".js":!r||o&&i?r+o+"."+i.toLowerCase()+"js":e}):e}let I={__extends:o,__assign:i,__rest:a,__decorate:l,__param:c,__esDecorate:u,__runInitializers:s,__propKey:f,__setFunctionName:d,__metadata:p,__awaiter:h,__generator:v,__createBinding:m,__exportStar:y,__values:g,__read:w,__spread:b,__spreadArrays:x,__spreadArray:E,__await:S,__asyncGenerator:_,__asyncDelegator:A,__asyncValues:P,__makeTemplateObject:O,__importStar:T,__importDefault:L,__classPrivateFieldGet:k,__classPrivateFieldSet:j,__classPrivateFieldIn:D,__addDisposableResource:M,__disposeResources:F,__rewriteRelativeImportExtension:W}}}]);