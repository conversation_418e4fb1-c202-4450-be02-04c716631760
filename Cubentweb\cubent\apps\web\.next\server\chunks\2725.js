"use strict";exports.id=2725,exports.ids=[763,1726,2725,3481],exports.modules={763:(e,t,a)=>{a.r(t),a.d(t,{default:()=>n});var n=[Object.freeze({displayName:"PostCSS",fileTypes:["pcss","postcss"],foldingStartMarker:"/\\*|^#|^\\*|^\\b|^\\.",foldingStopMarker:"\\*/|^\\s*$",name:"postcss",patterns:[{begin:"/\\*",end:"\\*/",name:"comment.block.postcss",patterns:[{include:"#comment-tag"}]},{include:"#double-slash"},{include:"#double-quoted"},{include:"#single-quoted"},{include:"#interpolation"},{include:"#placeholder-selector"},{include:"#variable"},{include:"#variable-root-css"},{include:"#numeric"},{include:"#unit"},{include:"#flag"},{include:"#dotdotdot"},{begin:"@include",captures:{0:{name:"keyword.control.at-rule.css.postcss"}},end:"(?=\\n|\\(|{|;)",name:"support.function.name.postcss.library"},{begin:"@mixin|@function",captures:{0:{name:"keyword.control.at-rule.css.postcss"}},end:"$\\n?|(?=\\(|{)",name:"support.function.name.postcss.no-completions",patterns:[{match:"[\\w-]+",name:"entity.name.function"}]},{match:"(?<=@import)\\s[\\w/.*-]+",name:"string.quoted.double.css.postcss"},{begin:"@",end:"$\\n?|\\s(?!(all|braille|embossed|handheld|print|projection|screen|speech|tty|tv|if|only|not)(\\s|,))|(?=;)",name:"keyword.control.at-rule.css.postcss"},{begin:"#",end:"$\\n?|(?=\\s|,|;|\\(|\\)|\\.|\\[|{|>)",name:"entity.other.attribute-name.id.css.postcss",patterns:[{include:"#interpolation"},{include:"#pseudo-class"}]},{begin:"\\.|(?<=&)(-|_)",end:"$\\n?|(?=\\s|,|;|\\(|\\)|\\[|{|>)",name:"entity.other.attribute-name.class.css.postcss",patterns:[{include:"#interpolation"},{include:"#pseudo-class"}]},{begin:"\\[",end:"\\]",name:"entity.other.attribute-selector.postcss",patterns:[{include:"#double-quoted"},{include:"#single-quoted"},{match:"\\^|\\$|\\*|~",name:"keyword.other.regex.postcss"}]},{match:"(?<=\\]|\\)|not\\(|\\*|>|>\\s):[a-z:-]+|(::|:-)[a-z:-]+",name:"entity.other.attribute-name.pseudo-class.css.postcss"},{begin:":",end:"$\\n?|(?=;|\\s\\(|and\\(|{|}|\\),)",name:"meta.property-list.css.postcss",patterns:[{include:"#double-slash"},{include:"#double-quoted"},{include:"#single-quoted"},{include:"#interpolation"},{include:"#variable"},{include:"#rgb-value"},{include:"#numeric"},{include:"#unit"},{include:"#flag"},{include:"#function"},{include:"#function-content"},{include:"#function-content-var"},{include:"#operator"},{include:"#parent-selector"},{include:"#property-value"}]},{include:"#rgb-value"},{include:"#function"},{include:"#function-content"},{begin:"(?<!-|\\()\\b(a|abbr|acronym|address|applet|area|article|aside|audio|b|base|big|blockquote|body|br|button|canvas|caption|cite|code|col|colgroup|datalist|dd|del|details|dfn|dialog|div|dl|dt|em|embed|eventsource|fieldset|figure|figcaption|footer|form|frame|frameset|(h[1-6])|head|header|hgroup|hr|html|i|iframe|img|input|ins|kbd|label|legend|li|link|map|mark|menu|meta|meter|nav|noframes|noscript|object|ol|optgroup|option|output|p|param|picture|pre|progress|q|samp|script|section|select|small|source|span|strike|strong|style|sub|summary|sup|table|tbody|td|textarea|tfoot|th|thead|time|title|tr|tt|ul|var|video|main|svg|rect|ruby|center|circle|ellipse|line|polyline|polygon|path|text|u|x)\\b(?!-|\\)|:\\s)|&",end:"(?=\\s|,|;|\\(|\\)|\\.|\\[|{|>|-|_)",name:"entity.name.tag.css.postcss.symbol",patterns:[{include:"#interpolation"},{include:"#pseudo-class"}]},{include:"#operator"},{match:"[a-z-]+((?=:|#{))",name:"support.type.property-name.css.postcss"},{include:"#reserved-words"},{include:"#property-value"}],repository:{"comment-tag":{begin:"{{",end:"}}",name:"comment.tags.postcss",patterns:[{match:"[\\w-]+",name:"comment.tag.postcss"}]},dotdotdot:{match:"\\.{3}",name:"variable.other"},"double-quoted":{begin:'"',end:'"',name:"string.quoted.double.css.postcss",patterns:[{include:"#quoted-interpolation"}]},"double-slash":{begin:"//",end:"$",name:"comment.line.postcss",patterns:[{include:"#comment-tag"}]},flag:{match:"!(important|default|optional|global)",name:"keyword.other.important.css.postcss"},function:{match:"(?<=[\\s|(|,|:])(?!url|format|attr)[\\w-][\\w-]*(?=\\()",name:"support.function.name.postcss"},"function-content":{match:"(?<=url\\(|format\\(|attr\\().+?(?=\\))",name:"string.quoted.double.css.postcss"},"function-content-var":{match:"(?<=var\\()[\\w-]+(?=\\))",name:"variable.parameter.postcss"},interpolation:{begin:"#{",end:"}",name:"support.function.interpolation.postcss",patterns:[{include:"#variable"},{include:"#numeric"},{include:"#operator"},{include:"#unit"},{include:"#double-quoted"},{include:"#single-quoted"}]},numeric:{match:"(-|\\.)?\\d+(\\.\\d+)?",name:"constant.numeric.css.postcss"},operator:{match:"\\+|\\s-\\s|\\s-(?=\\$)|(?<=\\()-(?=\\$)|\\s-(?=\\()|\\*|/|%|=|!|<|>|~",name:"keyword.operator.postcss"},"parent-selector":{match:"&",name:"entity.name.tag.css.postcss"},"placeholder-selector":{begin:"(?<!\\d)%(?!\\d)",end:"$\\n?|\\s|(?=;|{)",name:"entity.other.attribute-name.placeholder-selector.postcss"},"property-value":{match:"[\\w-]+",name:"meta.property-value.css.postcss, support.constant.property-value.css.postcss"},"pseudo-class":{match:":[a-z:-]+",name:"entity.other.attribute-name.pseudo-class.css.postcss"},"quoted-interpolation":{begin:"#{",end:"}",name:"support.function.interpolation.postcss",patterns:[{include:"#variable"},{include:"#numeric"},{include:"#operator"},{include:"#unit"}]},"reserved-words":{match:"\\b(false|from|in|not|null|through|to|true)\\b",name:"support.type.property-name.css.postcss"},"rgb-value":{match:"(#)([0-9a-fA-F]{3}|[0-9a-fA-F]{6})\\b",name:"constant.other.color.rgb-value.css.postcss"},"single-quoted":{begin:"'",end:"'",name:"string.quoted.single.css.postcss",patterns:[{include:"#quoted-interpolation"}]},unit:{match:"(?<=[\\d]|})(ch|cm|deg|dpcm|dpi|dppx|em|ex|grad|Hz|in|kHz|mm|ms|pc|pt|px|rad|rem|s|turn|vh|vmax|vmin|vw|%)",name:"keyword.other.unit.css.postcss"},variable:{match:"\\$[\\w-]+",name:"variable.parameter.postcss"},"variable-root-css":{match:"(?<!&)--[\\w-]+",name:"variable.parameter.postcss"}},scopeName:"source.css.postcss"})]},12725:(e,t,a)=>{a.r(t),a.d(t,{default:()=>g});var n=a(85862),r=a(63069),s=a(10861),o=a(47350),i=a(91726),c=a(98013),l=a(6028),u=a(7395),d=a(763),m=a(86181);let p=Object.freeze({displayName:"Astro",fileTypes:["astro"],injections:{"L:(meta.script.astro) (meta.lang.js | meta.lang.javascript | meta.lang.partytown | meta.lang.node) - (meta source)":{patterns:[{begin:"(?<=>)(?!</)",contentName:"source.js",end:"(?=</)",name:"meta.embedded.block.astro",patterns:[{include:"source.js"}]}]},"L:(meta.script.astro) (meta.lang.json) - (meta source)":{patterns:[{begin:"(?<=>)(?!</)",contentName:"source.json",end:"(?=</)",name:"meta.embedded.block.astro",patterns:[{include:"source.json"}]}]},"L:(meta.script.astro) (meta.lang.ts | meta.lang.typescript) - (meta source)":{patterns:[{begin:"(?<=>)(?!</)",contentName:"source.ts",end:"(?=</)",name:"meta.embedded.block.astro",patterns:[{include:"source.ts"}]}]},"L:meta.script.astro - meta.lang - (meta source)":{patterns:[{begin:"(?<=>)(?!</)",contentName:"source.js",end:"(?=</)",name:"meta.embedded.block.astro",patterns:[{include:"source.js"}]}]},"L:meta.style.astro - meta.lang - (meta source)":{patterns:[{begin:"(?<=>)(?!</)",contentName:"source.css",end:"(?=</)",name:"meta.embedded.block.astro",patterns:[{include:"source.css"}]}]},"L:meta.style.astro meta.lang.css - (meta source)":{patterns:[{begin:"(?<=>)(?!</)",contentName:"source.css",end:"(?=</)",name:"meta.embedded.block.astro",patterns:[{include:"source.css"}]}]},"L:meta.style.astro meta.lang.less - (meta source)":{patterns:[{begin:"(?<=>)(?!</)",contentName:"source.css.less",end:"(?=</)",name:"meta.embedded.block.astro",patterns:[{include:"source.css.less"}]}]},"L:meta.style.astro meta.lang.postcss - (meta source)":{patterns:[{begin:"(?<=>)(?!</)",contentName:"source.css.postcss",end:"(?=</)",name:"meta.embedded.block.astro",patterns:[{include:"source.css.postcss"}]}]},"L:meta.style.astro meta.lang.sass - (meta source)":{patterns:[{begin:"(?<=>)(?!</)",contentName:"source.sass",end:"(?=</)",name:"meta.embedded.block.astro",patterns:[{include:"source.sass"}]}]},"L:meta.style.astro meta.lang.scss - (meta source)":{patterns:[{begin:"(?<=>)(?!</)",contentName:"source.css.scss",end:"(?=</)",name:"meta.embedded.block.astro",patterns:[{include:"source.css.scss"}]}]},"L:meta.style.astro meta.lang.stylus - (meta source)":{patterns:[{begin:"(?<=>)(?!</)",contentName:"source.stylus",end:"(?=</)",name:"meta.embedded.block.astro",patterns:[{include:"source.stylus"}]}]}},name:"astro",patterns:[{include:"#scope"},{include:"#frontmatter"},{include:"#text"}],repository:{"attribute-literal":{begin:"(`)",end:"\\1",name:"string.template.astro",patterns:[{include:"source.tsx#template-substitution-element"},{include:"source.tsx#string-character-escape"}]},attributes:{patterns:[{include:"#attributes-events"},{include:"#attributes-keyvalue"},{include:"#attributes-interpolated"}]},"attributes-events":{begin:"(on(s(croll|t(orage|alled)|u(spend|bmit)|e(curitypolicyviolation|ek(ing|ed)|lect))|hashchange|c(hange|o(ntextmenu|py)|u(t|echange)|l(ick|ose)|an(cel|play(through)?))|t(imeupdate|oggle)|in(put|valid)|o(nline|ffline)|d(urationchange|r(op|ag(start|over|e(n(ter|d)|xit)|leave)?)|blclick)|un(handledrejection|load)|p(opstate|lay(ing)?|a(ste|use|ge(show|hide))|rogress)|e(nded|rror|mptied)|volumechange|key(down|up|press)|focus|w(heel|aiting)|l(oad(start|e(nd|d(data|metadata)))?|anguagechange)|a(uxclick|fterprint|bort)|r(e(s(ize|et)|jectionhandled)|atechange)|m(ouse(o(ut|ver)|down|up|enter|leave|move)|essage(error)?)|b(efore(unload|print)|lur)))(?![\\\\w:-])",beginCaptures:{0:{patterns:[{match:".*",name:"entity.other.attribute-name.astro"}]}},end:"(?=\\s*+[^=\\s])",name:"meta.attribute.$1.astro",patterns:[{begin:"=",beginCaptures:{0:{name:"punctuation.separator.key-value.astro"}},end:"(?<=[^\\s=])(?!\\s*=)|(?=/?>)",patterns:[{include:"#interpolation"},{include:"#attribute-literal"},{begin:"(?=[^\\s=<>`/]|/(?!>))",end:"(?!\\G)",name:"meta.embedded.line.js",patterns:[{captures:{0:{name:"source.js"},1:{patterns:[{include:"source.js"}]}},match:"(([^\\s\\\"'=<>`/]|/(?!>))+)",name:"string.unquoted.astro"},{begin:'(["])',beginCaptures:{0:{name:"punctuation.definition.string.begin.astro"}},end:"\\1",endCaptures:{0:{name:"punctuation.definition.string.end.astro"}},name:"string.quoted.astro",patterns:[{captures:{0:{patterns:[{include:"source.js"}]}},match:'([^\\n\\"/]|/(?![/*]))+'},{begin:"//",beginCaptures:{0:{name:"punctuation.definition.comment.js"}},end:'(?=\\")|\\n',name:"comment.line.double-slash.js"},{begin:"/\\*",beginCaptures:{0:{name:"punctuation.definition.comment.begin.js"}},end:'(?=\\")|\\*/',endCaptures:{0:{name:"punctuation.definition.comment.end.js"}},name:"comment.block.js"}]},{begin:"(['])",beginCaptures:{0:{name:"punctuation.definition.string.begin.astro"}},end:"\\1",endCaptures:{0:{name:"punctuation.definition.string.end.astro"}},name:"string.quoted.astro",patterns:[{captures:{0:{patterns:[{include:"source.js"}]}},match:"([^\\n\\'/]|/(?![/*]))+"},{begin:"//",beginCaptures:{0:{name:"punctuation.definition.comment.js"}},end:"(?=\\')|\\n",name:"comment.line.double-slash.js"},{begin:"/\\*",beginCaptures:{0:{name:"punctuation.definition.comment.begin.js"}},end:"(?=\\')|\\*/",endCaptures:{0:{name:"punctuation.definition.comment.end.js"}},name:"comment.block.js"}]}]}]}]},"attributes-interpolated":{begin:"(?<!:|=)\\s*({)",contentName:"meta.embedded.expression.astro source.tsx",end:"(\\})",patterns:[{include:"source.tsx"}]},"attributes-keyvalue":{begin:"([_@$A-Za-z][:._\\-$0-9A-Za-z]*)",beginCaptures:{0:{patterns:[{match:".*",name:"entity.other.attribute-name.astro"}]}},end:"(?=\\s*+[^=\\s])",name:"meta.attribute.$1.astro",patterns:[{begin:"=",beginCaptures:{0:{name:"punctuation.separator.key-value.astro"}},end:"(?<=[^\\s=])(?!\\s*=)|(?=/?>)",patterns:[{include:"#attributes-value"}]}]},"attributes-value":{patterns:[{include:"#interpolation"},{match:"([^\\s\"'=<>`/]|/(?!>))+",name:"string.unquoted.astro"},{begin:"(['\"])",beginCaptures:{0:{name:"punctuation.definition.string.begin.astro"}},end:"\\1",endCaptures:{0:{name:"punctuation.definition.string.end.astro"}},name:"string.quoted.astro"},{include:"#attribute-literal"}]},comments:{begin:"\x3c!--",captures:{0:{name:"punctuation.definition.comment.astro"}},end:"--\x3e",name:"comment.block.astro",patterns:[{match:"\\G-?>|\x3c!--(?!>)|<!-(?=--\x3e)|--!>",name:"invalid.illegal.characters-not-allowed-here.astro"}]},entities:{patterns:[{captures:{1:{name:"punctuation.definition.entity.astro"},912:{name:"punctuation.definition.entity.astro"}},match:"(&)(?=[a-zA-Z])((a(s(ymp(eq)?|cr|t)|n(d(slope|d|v|and)?|g(s(t|ph)|zarr|e|le|rt(vb(d)?)?|msd(a(h|c|d|e|f|a|g|b))?)?)|c(y|irc|d|ute|E)?|tilde|o(pf|gon)|uml|p(id|os|prox(eq)?|e|E|acir)?|elig|f(r)?|w(conint|int)|l(pha|e(ph|fsym))|acute|ring|grave|m(p|a(cr|lg))|breve)|A(s(sign|cr)|nd|MP|c(y|irc)|tilde|o(pf|gon)|uml|pplyFunction|fr|Elig|lpha|acute|ring|grave|macr|breve))|(B(scr|cy|opf|umpeq|e(cause|ta|rnoullis)|fr|a(ckslash|r(v|wed))|reve)|b(s(cr|im(e)?|ol(hsub|b)?|emi)|n(ot|e(quiv)?)|c(y|ong)|ig(s(tar|qcup)|c(irc|up|ap)|triangle(down|up)|o(times|dot|plus)|uplus|vee|wedge)|o(t(tom)?|pf|wtie|x(h(d|u|D|U)?|times|H(d|u|D|U)?|d(R|l|r|L)|u(R|l|r|L)|plus|D(R|l|r|L)|v(R|h|H|l|r|L)?|U(R|l|r|L)|V(R|h|H|l|r|L)?|minus|box))|Not|dquo|u(ll(et)?|mp(e(q)?|E)?)|prime|e(caus(e)?|t(h|ween|a)|psi|rnou|mptyv)|karow|fr|l(ock|k(1(2|4)|34)|a(nk|ck(square|triangle(down|left|right)?|lozenge)))|a(ck(sim(eq)?|cong|prime|epsilon)|r(vee|wed(ge)?))|r(eve|vbar)|brk(tbrk)?))|(c(s(cr|u(p(e)?|b(e)?))|h(cy|i|eck(mark)?)|ylcty|c(irc|ups(sm)?|edil|a(ps|ron))|tdot|ir(scir|c(eq|le(d(R|circ|S|dash|ast)|arrow(left|right)))?|e|fnint|E|mid)?|o(n(int|g(dot)?)|p(y(sr)?|f|rod)|lon(e(q)?)?|m(p(fn|le(xes|ment))?|ma(t)?))|dot|u(darr(l|r)|p(s|c(up|ap)|or|dot|brcap)?|e(sc|pr)|vee|wed|larr(p)?|r(vearrow(left|right)|ly(eq(succ|prec)|vee|wedge)|arr(m)?|ren))|e(nt(erdot)?|dil|mptyv)|fr|w(conint|int)|lubs(uit)?|a(cute|p(s|c(up|ap)|dot|and|brcup)?|r(on|et))|r(oss|arr))|C(scr|hi|c(irc|onint|edil|aron)|ircle(Minus|Times|Dot|Plus)|Hcy|o(n(tourIntegral|int|gruent)|unterClockwiseContourIntegral|p(f|roduct)|lon(e)?)|dot|up(Cap)?|OPY|e(nterDot|dilla)|fr|lo(seCurly(DoubleQuote|Quote)|ckwiseContourIntegral)|a(yleys|cute|p(italDifferentialD)?)|ross))|(d(s(c(y|r)|trok|ol)|har(l|r)|c(y|aron)|t(dot|ri(f)?)|i(sin|e|v(ide(ontimes)?|onx)?|am(s|ond(suit)?)?|gamma)|Har|z(cy|igrarr)|o(t(square|plus|eq(dot)?|minus)?|ublebarwedge|pf|wn(harpoon(left|right)|downarrows|arrow)|llar)|d(otseq|a(rr|gger))?|u(har|arr)|jcy|e(lta|g|mptyv)|f(isht|r)|wangle|lc(orn|rop)|a(sh(v)?|leth|rr|gger)|r(c(orn|rop)|bkarow)|b(karow|lac)|Arr)|D(s(cr|trok)|c(y|aron)|Scy|i(fferentialD|a(critical(Grave|Tilde|Do(t|ubleAcute)|Acute)|mond))|o(t(Dot|Equal)?|uble(Right(Tee|Arrow)|ContourIntegral|Do(t|wnArrow)|Up(DownArrow|Arrow)|VerticalBar|L(ong(RightArrow|Left(RightArrow|Arrow))|eft(RightArrow|Tee|Arrow)))|pf|wn(Right(TeeVector|Vector(Bar)?)|Breve|Tee(Arrow)?|arrow|Left(RightVector|TeeVector|Vector(Bar)?)|Arrow(Bar|UpArrow)?))|Zcy|el(ta)?|D(otrahd)?|Jcy|fr|a(shv|rr|gger)))|(e(s(cr|im|dot)|n(sp|g)|c(y|ir(c)?|olon|aron)|t(h|a)|o(pf|gon)|dot|u(ro|ml)|p(si(v|lon)?|lus|ar(sl)?)|e|D(ot|Dot)|q(s(im|lant(less|gtr))|c(irc|olon)|u(iv(DD)?|est|als)|vparsl)|f(Dot|r)|l(s(dot)?|inters|l)?|a(ster|cute)|r(Dot|arr)|g(s(dot)?|rave)?|x(cl|ist|p(onentiale|ectation))|m(sp(1(3|4))?|pty(set|v)?|acr))|E(s(cr|im)|c(y|irc|aron)|ta|o(pf|gon)|NG|dot|uml|TH|psilon|qu(ilibrium|al(Tilde)?)|fr|lement|acute|grave|x(ists|ponentialE)|m(pty(SmallSquare|VerySmallSquare)|acr)))|(f(scr|nof|cy|ilig|o(pf|r(k(v)?|all))|jlig|partint|emale|f(ilig|l(ig|lig)|r)|l(tns|lig|at)|allingdotseq|r(own|a(sl|c(1(2|8|3|4|5|6)|78|2(3|5)|3(8|4|5)|45|5(8|6)))))|F(scr|cy|illed(SmallSquare|VerySmallSquare)|o(uriertrf|pf|rAll)|fr))|(G(scr|c(y|irc|edil)|t|opf|dot|T|Jcy|fr|amma(d)?|reater(Greater|SlantEqual|Tilde|Equal(Less)?|FullEqual|Less)|g|breve)|g(s(cr|im(e|l)?)|n(sim|e(q(q)?)?|E|ap(prox)?)|c(y|irc)|t(c(c|ir)|dot|quest|lPar|r(sim|dot|eq(qless|less)|less|a(pprox|rr)))?|imel|opf|dot|jcy|e(s(cc|dot(o(l)?)?|l(es)?)?|q(slant|q)?|l)?|v(nE|ertneqq)|fr|E(l)?|l(j|E|a)?|a(cute|p|mma(d)?)|rave|g(g)?|breve))|(h(s(cr|trok|lash)|y(phen|bull)|circ|o(ok(leftarrow|rightarrow)|pf|arr|rbar|mtht)|e(llip|arts(uit)?|rcon)|ks(earow|warow)|fr|a(irsp|lf|r(dcy|r(cir|w)?)|milt)|bar|Arr)|H(s(cr|trok)|circ|ilbertSpace|o(pf|rizontalLine)|ump(DownHump|Equal)|fr|a(cek|t)|ARDcy))|(i(s(cr|in(s(v)?|dot|v|E)?)|n(care|t(cal|prod|e(rcal|gers)|larhk)?|odot|fin(tie)?)?|c(y|irc)?|t(ilde)?|i(nfin|i(nt|int)|ota)?|o(cy|ta|pf|gon)|u(kcy|ml)|jlig|prod|e(cy|xcl)|quest|f(f|r)|acute|grave|m(of|ped|a(cr|th|g(part|e|line))))|I(scr|n(t(e(rsection|gral))?|visible(Comma|Times))|c(y|irc)|tilde|o(ta|pf|gon)|dot|u(kcy|ml)|Ocy|Jlig|fr|Ecy|acute|grave|m(plies|a(cr|ginaryI))?))|(j(s(cr|ercy)|c(y|irc)|opf|ukcy|fr|math)|J(s(cr|ercy)|c(y|irc)|opf|ukcy|fr))|(k(scr|hcy|c(y|edil)|opf|jcy|fr|appa(v)?|green)|K(scr|c(y|edil)|Hcy|opf|Jcy|fr|appa))|(l(s(h|cr|trok|im(e|g)?|q(uo(r)?|b)|aquo)|h(ar(d|u(l)?)|blk)|n(sim|e(q(q)?)?|E|ap(prox)?)|c(y|ub|e(il|dil)|aron)|Barr|t(hree|c(c|ir)|imes|dot|quest|larr|r(i(e|f)?|Par))?|Har|o(ng(left(arrow|rightarrow)|rightarrow|mapsto)|times|z(enge|f)?|oparrow(left|right)|p(f|lus|ar)|w(ast|bar)|a(ng|rr)|brk)|d(sh|ca|quo(r)?|r(dhar|ushar))|ur(dshar|uhar)|jcy|par(lt)?|e(s(s(sim|dot|eq(qgtr|gtr)|approx|gtr)|cc|dot(o(r)?)?|g(es)?)?|q(slant|q)?|ft(harpoon(down|up)|threetimes|leftarrows|arrow(tail)?|right(squigarrow|harpoons|arrow(s)?))|g)?|v(nE|ertneqq)|f(isht|loor|r)|E(g)?|l(hard|corner|tri|arr)?|a(ng(d|le)?|cute|t(e(s)?|ail)?|p|emptyv|quo|rr(sim|hk|tl|pl|fs|lp|b(fs)?)?|gran|mbda)|r(har(d)?|corner|tri|arr|m)|g(E)?|m(idot|oust(ache)?)|b(arr|r(k(sl(d|u)|e)|ac(e|k))|brk)|A(tail|arr|rr))|L(s(h|cr|trok)|c(y|edil|aron)|t|o(ng(RightArrow|left(arrow|rightarrow)|rightarrow|Left(RightArrow|Arrow))|pf|wer(RightArrow|LeftArrow))|T|e(ss(Greater|SlantEqual|Tilde|EqualGreater|FullEqual|Less)|ft(Right(Vector|Arrow)|Ceiling|T(ee(Vector|Arrow)?|riangle(Bar|Equal)?)|Do(ubleBracket|wn(TeeVector|Vector(Bar)?))|Up(TeeVector|DownVector|Vector(Bar)?)|Vector(Bar)?|arrow|rightarrow|Floor|A(ngleBracket|rrow(RightArrow|Bar)?)))|Jcy|fr|l(eftarrow)?|a(ng|cute|placetrf|rr|mbda)|midot))|(M(scr|cy|inusPlus|opf|u|e(diumSpace|llintrf)|fr|ap)|m(s(cr|tpos)|ho|nplus|c(y|omma)|i(nus(d(u)?|b)?|cro|d(cir|dot|ast)?)|o(dels|pf)|dash|u(ltimap|map)?|p|easuredangle|DDot|fr|l(cp|dr)|a(cr|p(sto(down|up|left)?)?|l(t(ese)?|e)|rker)))|(n(s(hort(parallel|mid)|c(cue|e|r)?|im(e(q)?)?|u(cc(eq)?|p(set(eq(q)?)?|e|E)?|b(set(eq(q)?)?|e|E)?)|par|qsu(pe|be)|mid)|Rightarrow|h(par|arr|Arr)|G(t(v)?|g)|c(y|ong(dot)?|up|edil|a(p|ron))|t(ilde|lg|riangle(left(eq)?|right(eq)?)|gl)|i(s(d)?|v)?|o(t(ni(v(c|a|b))?|in(dot|v(c|a|b)|E)?)?|pf)|dash|u(m(sp|ero)?)?|jcy|p(olint|ar(sl|t|allel)?|r(cue|e(c(eq)?)?)?)|e(s(im|ear)|dot|quiv|ar(hk|r(ow)?)|xist(s)?|Arr)?|v(sim|infin|Harr|dash|Dash|l(t(rie)?|e|Arr)|ap|r(trie|Arr)|g(t|e))|fr|w(near|ar(hk|r(ow)?)|Arr)|V(dash|Dash)|l(sim|t(ri(e)?)?|dr|e(s(s)?|q(slant|q)?|ft(arrow|rightarrow))?|E|arr|Arr)|a(ng|cute|tur(al(s)?)?|p(id|os|prox|E)?|bla)|r(tri(e)?|ightarrow|arr(c|w)?|Arr)|g(sim|t(r)?|e(s|q(slant|q)?)?|E)|mid|L(t(v)?|eft(arrow|rightarrow)|l)|b(sp|ump(e)?))|N(scr|c(y|edil|aron)|tilde|o(nBreakingSpace|Break|t(R(ightTriangle(Bar|Equal)?|everseElement)|Greater(Greater|SlantEqual|Tilde|Equal|FullEqual|Less)?|S(u(cceeds(SlantEqual|Tilde|Equal)?|perset(Equal)?|bset(Equal)?)|quareSu(perset(Equal)?|bset(Equal)?))|Hump(DownHump|Equal)|Nested(GreaterGreater|LessLess)|C(ongruent|upCap)|Tilde(Tilde|Equal|FullEqual)?|DoubleVerticalBar|Precedes(SlantEqual|Equal)?|E(qual(Tilde)?|lement|xists)|VerticalBar|Le(ss(Greater|SlantEqual|Tilde|Equal|Less)?|ftTriangle(Bar|Equal)?))?|pf)|u|e(sted(GreaterGreater|LessLess)|wLine|gative(MediumSpace|Thi(nSpace|ckSpace)|VeryThinSpace))|Jcy|fr|acute))|(o(s(cr|ol|lash)|h(m|bar)|c(y|ir(c)?)|ti(lde|mes(as)?)|S|int|opf|d(sold|iv|ot|ash|blac)|uml|p(erp|lus|ar)|elig|vbar|f(cir|r)|l(c(ir|ross)|t|ine|arr)|a(st|cute)|r(slope|igof|or|d(er(of)?|f|m)?|v|arr)?|g(t|on|rave)|m(i(nus|cron|d)|ega|acr))|O(s(cr|lash)|c(y|irc)|ti(lde|mes)|opf|dblac|uml|penCurly(DoubleQuote|Quote)|ver(B(ar|rac(e|ket))|Parenthesis)|fr|Elig|acute|r|grave|m(icron|ega|acr)))|(p(s(cr|i)|h(i(v)?|one|mmat)|cy|i(tchfork|v)?|o(intint|und|pf)|uncsp|er(cnt|tenk|iod|p|mil)|fr|l(us(sim|cir|two|d(o|u)|e|acir|mn|b)?|an(ck(h)?|kv))|ar(s(im|l)|t|a(llel)?)?|r(sim|n(sim|E|ap)|cue|ime(s)?|o(d|p(to)?|f(surf|line|alar))|urel|e(c(sim|n(sim|eqq|approx)|curlyeq|eq|approx)?)?|E|ap)?|m)|P(s(cr|i)|hi|cy|i|o(incareplane|pf)|fr|lusMinus|artialD|r(ime|o(duct|portion(al)?)|ecedes(SlantEqual|Tilde|Equal)?)?))|(q(scr|int|opf|u(ot|est(eq)?|at(int|ernions))|prime|fr)|Q(scr|opf|UOT|fr))|(R(s(h|cr)|ho|c(y|edil|aron)|Barr|ight(Ceiling|T(ee(Vector|Arrow)?|riangle(Bar|Equal)?)|Do(ubleBracket|wn(TeeVector|Vector(Bar)?))|Up(TeeVector|DownVector|Vector(Bar)?)|Vector(Bar)?|arrow|Floor|A(ngleBracket|rrow(Bar|LeftArrow)?))|o(undImplies|pf)|uleDelayed|e(verse(UpEquilibrium|E(quilibrium|lement)))?|fr|EG|a(ng|cute|rr(tl)?)|rightarrow)|r(s(h|cr|q(uo(r)?|b)|aquo)|h(o(v)?|ar(d|u(l)?))|nmid|c(y|ub|e(il|dil)|aron)|Barr|t(hree|imes|ri(e|f|ltri)?)|i(singdotseq|ng|ght(squigarrow|harpoon(down|up)|threetimes|left(harpoons|arrows)|arrow(tail)?|rightarrows))|Har|o(times|p(f|lus|ar)|a(ng|rr)|brk)|d(sh|ca|quo(r)?|ldhar)|uluhar|p(polint|ar(gt)?)|e(ct|al(s|ine|part)?|g)|f(isht|loor|r)|l(har|arr|m)|a(ng(d|e|le)?|c(ute|e)|t(io(nals)?|ail)|dic|emptyv|quo|rr(sim|hk|c|tl|pl|fs|w|lp|ap|b(fs)?)?)|rarr|x|moust(ache)?|b(arr|r(k(sl(d|u)|e)|ac(e|k))|brk)|A(tail|arr|rr)))|(s(s(cr|tarf|etmn|mile)|h(y|c(hcy|y)|ort(parallel|mid)|arp)|c(sim|y|n(sim|E|ap)|cue|irc|polint|e(dil)?|E|a(p|ron))?|t(ar(f)?|r(ns|aight(phi|epsilon)))|i(gma(v|f)?|m(ne|dot|plus|e(q)?|l(E)?|rarr|g(E)?)?)|zlig|o(pf|ftcy|l(b(ar)?)?)|dot(e|b)?|u(ng|cc(sim|n(sim|eqq|approx)|curlyeq|eq|approx)?|p(s(im|u(p|b)|et(neq(q)?|eq(q)?)?)|hs(ol|ub)|1|n(e|E)|2|d(sub|ot)|3|plus|e(dot)?|E|larr|mult)?|m|b(s(im|u(p|b)|et(neq(q)?|eq(q)?)?)|n(e|E)|dot|plus|e(dot)?|E|rarr|mult)?)|pa(des(uit)?|r)|e(swar|ct|tm(n|inus)|ar(hk|r(ow)?)|xt|mi|Arr)|q(su(p(set(eq)?|e)?|b(set(eq)?|e)?)|c(up(s)?|ap(s)?)|u(f|ar(e|f))?)|fr(own)?|w(nwar|ar(hk|r(ow)?)|Arr)|larr|acute|rarr|m(t(e(s)?)?|i(d|le)|eparsl|a(shp|llsetminus))|bquo)|S(scr|hort(RightArrow|DownArrow|UpArrow|LeftArrow)|c(y|irc|edil|aron)?|tar|igma|H(cy|CHcy)|opf|u(c(hThat|ceeds(SlantEqual|Tilde|Equal)?)|p(set|erset(Equal)?)?|m|b(set(Equal)?)?)|OFTcy|q(uare(Su(perset(Equal)?|bset(Equal)?)|Intersection|Union)?|rt)|fr|acute|mallCircle))|(t(s(hcy|c(y|r)|trok)|h(i(nsp|ck(sim|approx))|orn|e(ta(sym|v)?|re(4|fore))|k(sim|ap))|c(y|edil|aron)|i(nt|lde|mes(d|b(ar)?)?)|o(sa|p(cir|f(ork)?|bot)?|ea)|dot|prime|elrec|fr|w(ixt|ohead(leftarrow|rightarrow))|a(u|rget)|r(i(sb|time|dot|plus|e|angle(down|q|left(eq)?|right(eq)?)?|minus)|pezium|ade)|brk)|T(s(cr|trok)|RADE|h(i(nSpace|ckSpace)|e(ta|refore))|c(y|edil|aron)|S(cy|Hcy)|ilde(Tilde|Equal|FullEqual)?|HORN|opf|fr|a(u|b)|ripleDot))|(u(scr|h(ar(l|r)|blk)|c(y|irc)|t(ilde|dot|ri(f)?)|Har|o(pf|gon)|d(har|arr|blac)|u(arr|ml)|p(si(h|lon)?|harpoon(left|right)|downarrow|uparrows|lus|arrow)|f(isht|r)|wangle|l(c(orn(er)?|rop)|tri)|a(cute|rr)|r(c(orn(er)?|rop)|tri|ing)|grave|m(l|acr)|br(cy|eve)|Arr)|U(scr|n(ion(Plus)?|der(B(ar|rac(e|ket))|Parenthesis))|c(y|irc)|tilde|o(pf|gon)|dblac|uml|p(si(lon)?|downarrow|Tee(Arrow)?|per(RightArrow|LeftArrow)|DownArrow|Equilibrium|arrow|Arrow(Bar|DownArrow)?)|fr|a(cute|rr(ocir)?)|ring|grave|macr|br(cy|eve)))|(v(s(cr|u(pn(e|E)|bn(e|E)))|nsu(p|b)|cy|Bar(v)?|zigzag|opf|dash|prop|e(e(eq|bar)?|llip|r(t|bar))|Dash|fr|ltri|a(ngrt|r(s(igma|u(psetneq(q)?|bsetneq(q)?))|nothing|t(heta|riangle(left|right))|p(hi|i|ropto)|epsilon|kappa|r(ho)?))|rtri|Arr)|V(scr|cy|opf|dash(l)?|e(e|r(yThinSpace|t(ical(Bar|Separator|Tilde|Line))?|bar))|Dash|vdash|fr|bar))|(w(scr|circ|opf|p|e(ierp|d(ge(q)?|bar))|fr|r(eath)?)|W(scr|circ|opf|edge|fr))|(X(scr|i|opf|fr)|x(s(cr|qcup)|h(arr|Arr)|nis|c(irc|up|ap)|i|o(time|dot|p(f|lus))|dtri|u(tri|plus)|vee|fr|wedge|l(arr|Arr)|r(arr|Arr)|map))|(y(scr|c(y|irc)|icy|opf|u(cy|ml)|en|fr|ac(y|ute))|Y(scr|c(y|irc)|opf|uml|Icy|Ucy|fr|acute|Acy))|(z(scr|hcy|c(y|aron)|igrarr|opf|dot|e(ta|etrf)|fr|w(nj|j)|acute)|Z(scr|c(y|aron)|Hcy|opf|dot|e(ta|roWidthSpace)|fr|acute)))(;)",name:"constant.character.entity.named.$2.astro"},{captures:{1:{name:"punctuation.definition.entity.astro"},3:{name:"punctuation.definition.entity.astro"}},match:"(&)#\\d+(;)",name:"constant.character.entity.numeric.decimal.astro"},{captures:{1:{name:"punctuation.definition.entity.astro"},3:{name:"punctuation.definition.entity.astro"}},match:"(&)#[xX][0-9a-fA-F]+(;)",name:"constant.character.entity.numeric.hexadecimal.astro"},{match:"&(?=[a-zA-Z0-9]+;)",name:"invalid.illegal.ambiguous-ampersand.astro"}]},frontmatter:{begin:"\\A(-{3})\\s*$",beginCaptures:{1:{name:"comment"}},contentName:"source.ts",end:"(^|\\G)(-{3})|\\.{3}\\s*$",endCaptures:{2:{name:"comment"}},patterns:[{include:"source.ts"}]},interpolation:{patterns:[{begin:"\\{",beginCaptures:{0:{name:"punctuation.section.embedded.begin.astro"}},contentName:"meta.embedded.expression.astro source.tsx",end:"\\}",endCaptures:{0:{name:"punctuation.section.embedded.end.astro"}},patterns:[{begin:"\\G\\s*(?={)",end:"(?<=})",patterns:[{include:"source.tsx#object-literal"}]},{include:"source.tsx"}]}]},scope:{patterns:[{include:"#comments"},{include:"#tags"},{include:"#interpolation"},{include:"#entities"}]},tags:{patterns:[{include:"#tags-raw"},{include:"#tags-lang"},{include:"#tags-void"},{include:"#tags-general-end"},{include:"#tags-general-start"}]},"tags-end-node":{captures:{1:{name:"meta.tag.end.astro punctuation.definition.tag.begin.astro"},2:{name:"meta.tag.end.astro",patterns:[{include:"#tags-name"}]},3:{name:"meta.tag.end.astro punctuation.definition.tag.end.astro"},4:{name:"meta.tag.start.astro punctuation.definition.tag.end.astro"}},match:"(</)(.*?)\\s*(>)|(/>)"},"tags-general-end":{begin:"(</)([^/\\s>]*)",beginCaptures:{1:{name:"meta.tag.end.astro punctuation.definition.tag.begin.astro"},2:{name:"meta.tag.end.astro",patterns:[{include:"#tags-name"}]}},end:"(>)",endCaptures:{1:{name:"meta.tag.end.astro punctuation.definition.tag.end.astro"}},name:"meta.scope.tag.$2.astro"},"tags-general-start":{begin:"(<)([^/\\s>/]*)",beginCaptures:{0:{patterns:[{include:"#tags-start-node"}]}},end:"(/?>)",endCaptures:{1:{name:"meta.tag.start.astro punctuation.definition.tag.end.astro"}},name:"meta.scope.tag.$2.astro",patterns:[{include:"#tags-start-attributes"}]},"tags-lang":{begin:"<(script|style)",beginCaptures:{0:{patterns:[{include:"#tags-start-node"}]}},end:"</\\1\\s*>|/>",endCaptures:{0:{patterns:[{include:"#tags-end-node"}]}},name:"meta.scope.tag.$1.astro meta.$1.astro",patterns:[{begin:"\\G(?=\\s*[^>]*?(type|lang)\\s*=\\s*(['\"]|)(?:text\\/)?(application\\/ld\\+json)\\2)",end:"(?=</|/>)",name:"meta.lang.json.astro",patterns:[{include:"#tags-lang-start-attributes"}]},{begin:"\\G(?=\\s*[^>]*?(type|lang)\\s*=\\s*(['\"]|)(module)\\2)",end:"(?=</|/>)",name:"meta.lang.javascript.astro",patterns:[{include:"#tags-lang-start-attributes"}]},{begin:"\\G(?=\\s*[^>]*?(type|lang)\\s*=\\s*(['\"]|)(?:text/|application/)?([\\w\\/+]+)\\2)",end:"(?=</|/>)",name:"meta.lang.$3.astro",patterns:[{include:"#tags-lang-start-attributes"}]},{include:"#tags-lang-start-attributes"}]},"tags-lang-start-attributes":{begin:"\\G",end:"(?=/>)|>",endCaptures:{0:{name:"punctuation.definition.tag.end.astro"}},name:"meta.tag.start.astro",patterns:[{include:"#attributes"}]},"tags-name":{patterns:[{match:"[A-Z]\\w*",name:"support.class.component.astro"},{match:"[a-z][\\w0-9:]*-[\\w0-9:-]*",name:"meta.tag.custom.astro entity.name.tag.astro"},{match:"[a-z][\\w0-9:-]*",name:"entity.name.tag.astro"}]},"tags-raw":{begin:"<([^/?!\\s<>]+)(?=[^>]+is:raw).*?",beginCaptures:{0:{patterns:[{include:"#tags-start-node"}]}},contentName:"source.unknown",end:"</\\1\\s*>|/>",endCaptures:{0:{patterns:[{include:"#tags-end-node"}]}},name:"meta.scope.tag.$1.astro meta.raw.astro",patterns:[{include:"#tags-lang-start-attributes"}]},"tags-start-attributes":{begin:"\\G",end:"(?=/?>)",name:"meta.tag.start.astro",patterns:[{include:"#attributes"}]},"tags-start-node":{captures:{1:{name:"punctuation.definition.tag.begin.astro"},2:{patterns:[{include:"#tags-name"}]}},match:"(<)([^/\\s>/]*)",name:"meta.tag.start.astro"},"tags-void":{begin:"(<)(area|base|br|col|embed|hr|img|input|link|meta|param|source|track|wbr)(?=\\s|/?>)",beginCaptures:{1:{name:"punctuation.definition.tag.begin.astro"},2:{name:"entity.name.tag.astro"}},end:"/?>",endCaptures:{0:{name:"punctuation.definition.tag.begin.astro"}},name:"meta.tag.void.astro",patterns:[{include:"#attributes"}]},text:{patterns:[{begin:"(?<=^|---|>|})",end:"(?=<|{|$)",name:"text.astro",patterns:[{include:"#entities"}]}]}},scopeName:"source.astro",embeddedLangs:["json","javascript","typescript","stylus","sass","css","scss","less","postcss","tsx"]});var g=[...n.default,...r.default,...s.default,...o.default,...i.default,...c.default,...l.default,...u.default,...d.default,...m.default,p]},85862:(e,t,a)=>{a.r(t),a.d(t,{default:()=>n});var n=[Object.freeze({displayName:"JSON",name:"json",patterns:[{include:"#value"}],repository:{array:{begin:"\\[",beginCaptures:{0:{name:"punctuation.definition.array.begin.json"}},end:"\\]",endCaptures:{0:{name:"punctuation.definition.array.end.json"}},name:"meta.structure.array.json",patterns:[{include:"#value"},{match:",",name:"punctuation.separator.array.json"},{match:"[^\\s\\]]",name:"invalid.illegal.expected-array-separator.json"}]},comments:{patterns:[{begin:"/\\*\\*(?!/)",captures:{0:{name:"punctuation.definition.comment.json"}},end:"\\*/",name:"comment.block.documentation.json"},{begin:"/\\*",captures:{0:{name:"punctuation.definition.comment.json"}},end:"\\*/",name:"comment.block.json"},{captures:{1:{name:"punctuation.definition.comment.json"}},match:"(//).*$\\n?",name:"comment.line.double-slash.js"}]},constant:{match:"\\b(?:true|false|null)\\b",name:"constant.language.json"},number:{match:"-?(?:0|[1-9]\\d*)(?:(?:\\.\\d+)?(?:[eE][+-]?\\d+)?)?",name:"constant.numeric.json"},object:{begin:"\\{",beginCaptures:{0:{name:"punctuation.definition.dictionary.begin.json"}},end:"\\}",endCaptures:{0:{name:"punctuation.definition.dictionary.end.json"}},name:"meta.structure.dictionary.json",patterns:[{comment:"the JSON object key",include:"#objectkey"},{include:"#comments"},{begin:":",beginCaptures:{0:{name:"punctuation.separator.dictionary.key-value.json"}},end:"(,)|(?=\\})",endCaptures:{1:{name:"punctuation.separator.dictionary.pair.json"}},name:"meta.structure.dictionary.value.json",patterns:[{comment:"the JSON object value",include:"#value"},{match:"[^\\s,]",name:"invalid.illegal.expected-dictionary-separator.json"}]},{match:"[^\\s}]",name:"invalid.illegal.expected-dictionary-separator.json"}]},objectkey:{begin:'"',beginCaptures:{0:{name:"punctuation.support.type.property-name.begin.json"}},end:'"',endCaptures:{0:{name:"punctuation.support.type.property-name.end.json"}},name:"string.json support.type.property-name.json",patterns:[{include:"#stringcontent"}]},string:{begin:'"',beginCaptures:{0:{name:"punctuation.definition.string.begin.json"}},end:'"',endCaptures:{0:{name:"punctuation.definition.string.end.json"}},name:"string.quoted.double.json",patterns:[{include:"#stringcontent"}]},stringcontent:{patterns:[{match:'\\\\(?:["\\\\/bfnrt]|u[0-9a-fA-F]{4})',name:"constant.character.escape.json"},{match:"\\\\.",name:"invalid.illegal.unrecognized-string-escape.json"}]},value:{patterns:[{include:"#constant"},{include:"#number"},{include:"#string"},{include:"#array"},{include:"#object"},{include:"#comments"}]}},scopeName:"source.json"})]},91726:(e,t,a)=>{a.r(t),a.d(t,{default:()=>n});var n=[Object.freeze({displayName:"Sass",fileTypes:["sass"],foldingStartMarker:"/\\*|^#|^\\*|^\\b|*#?region|^\\.",foldingStopMarker:"\\*/|*#?endregion|^\\s*$",name:"sass",patterns:[{begin:"^(\\s*)(/\\*)",end:"(\\*/)|^(?!\\s\\1)",name:"comment.block.sass",patterns:[{include:"#comment-tag"},{include:"#comment-param"}]},{match:"^[\\t ]*/?//[\\t ]*[SRI][\\t ]*$",name:"keyword.other.sass.formatter.action"},{begin:"^[\\t ]*//[\\t ]*(import)[\\t ]*(css-variables)[\\t ]*(from)",captures:{1:{name:"keyword.control"},2:{name:"variable"},3:{name:"keyword.control"}},end:"$\\n?",name:"comment.import.css.variables",patterns:[{include:"#import-quotes"}]},{include:"#double-slash"},{include:"#double-quoted"},{include:"#single-quoted"},{include:"#interpolation"},{include:"#curly-brackets"},{include:"#placeholder-selector"},{begin:"\\$[a-zA-Z0-9_-]+(?=:)",captures:{0:{name:"variable.other.name"}},end:"$\\n?|(?=\\)\\s\\)|\\)\\n)",name:"sass.script.maps",patterns:[{include:"#double-slash"},{include:"#double-quoted"},{include:"#single-quoted"},{include:"#interpolation"},{include:"#variable"},{include:"#rgb-value"},{include:"#numeric"},{include:"#unit"},{include:"#flag"},{include:"#comma"},{include:"#function"},{include:"#function-content"},{include:"#operator"},{include:"#reserved-words"},{include:"#parent-selector"},{include:"#property-value"},{include:"#semicolon"},{include:"#dotdotdot"}]},{include:"#variable-root"},{include:"#numeric"},{include:"#unit"},{include:"#flag"},{include:"#comma"},{include:"#semicolon"},{include:"#dotdotdot"},{begin:"@include|\\+(?!\\W|\\d)",captures:{0:{name:"keyword.control.at-rule.css.sass"}},end:"(?=\\n|\\()",name:"support.function.name.sass.library"},{begin:"^(@use)",captures:{0:{name:"keyword.control.at-rule.css.sass.use"}},end:"(?=\\n)",name:"sass.use",patterns:[{match:"as|with",name:"support.type.css.sass"},{include:"#numeric"},{include:"#unit"},{include:"#variable-root"},{include:"#rgb-value"},{include:"#comma"},{include:"#parenthesis-open"},{include:"#parenthesis-close"},{include:"#colon"},{include:"#import-quotes"}]},{begin:"^@import(.*?)( as.*)?$",captures:{1:{name:"constant.character.css.sass"},2:{name:"invalid"}},end:"(?=\\n)",name:"keyword.control.at-rule.use"},{begin:"@mixin|^[\\t ]*=|@function",captures:{0:{name:"keyword.control.at-rule.css.sass"}},end:"$\\n?|(?=\\()",name:"support.function.name.sass",patterns:[{match:"[\\w-]+",name:"entity.name.function"}]},{begin:"@",end:"$\\n?|\\s(?!(all|braille|embossed|handheld|print|projection|screen|speech|tty|tv|if|only|not)(\\s|,))",name:"keyword.control.at-rule.css.sass"},{begin:"(?<!-|\\()\\b(a|abbr|acronym|address|applet|area|article|aside|audio|b|base|big|blockquote|body|br|button|canvas|caption|cite|code|col|colgroup|datalist|dd|del|details|dfn|dialog|div|dl|dt|em|embed|eventsource|fieldset|figure|figcaption|footer|form|frame|frameset|(h[1-6])|head|header|hgroup|hr|html|i|iframe|img|input|ins|kbd|label|legend|li|link|map|mark|menu|meta|meter|nav|noframes|noscript|object|ol|optgroup|option|output|p|param|picture|pre|progress|q|samp|script|section|select|small|source|span|strike|strong|style|sub|summary|sup|table|tbody|td|textarea|tfoot|th|thead|time|title|tr|tt|ul|var|video|main|svg|rect|ruby|center|circle|ellipse|line|polyline|polygon|path|text|u|slot)\\b(?!-|\\)|:\\s)|&",end:"$\\n?|(?=\\s|,|\\(|\\)|\\.|\\#|\\[|>|-|_)",name:"entity.name.tag.css.sass.symbol",patterns:[{include:"#interpolation"},{include:"#pseudo-class"}]},{begin:"#",end:"$\\n?|(?=\\s|,|\\(|\\)|\\.|\\[|>)",name:"entity.other.attribute-name.id.css.sass",patterns:[{include:"#interpolation"},{include:"#pseudo-class"}]},{begin:"\\.|(?<=&)(-|_)",end:"$\\n?|(?=\\s|,|\\(|\\)|\\[|>)",name:"entity.other.attribute-name.class.css.sass",patterns:[{include:"#interpolation"},{include:"#pseudo-class"}]},{begin:"\\[",end:"\\]",name:"entity.other.attribute-selector.sass",patterns:[{include:"#double-quoted"},{include:"#single-quoted"},{match:"\\^|\\$|\\*|~",name:"keyword.other.regex.sass"}]},{match:"^((?<=\\]|\\)|not\\(|\\*|>|>\\s)|\n*):[a-z:-]+|(::|:-)[a-z:-]+",name:"entity.other.attribute-name.pseudo-class.css.sass"},{include:"#module"},{match:"[\\w-]*\\(",name:"entity.name.function"},{match:"\\)",name:"entity.name.function.close"},{begin:":",end:"$\\n?|(?=\\s\\(|and\\(|\\),)",name:"meta.property-list.css.sass.prop",patterns:[{match:"(?<=:)[a-z-]+\\s",name:"support.type.property-name.css.sass.prop.name"},{include:"#double-slash"},{include:"#double-quoted"},{include:"#single-quoted"},{include:"#interpolation"},{include:"#curly-brackets"},{include:"#variable"},{include:"#rgb-value"},{include:"#numeric"},{include:"#unit"},{include:"#module"},{match:"--.+?(?=\\))",name:"variable.css"},{match:"[\\w-]*\\(",name:"entity.name.function"},{match:"\\)",name:"entity.name.function.close"},{include:"#flag"},{include:"#comma"},{include:"#semicolon"},{include:"#function"},{include:"#function-content"},{include:"#operator"},{include:"#parent-selector"},{include:"#property-value"}]},{include:"#rgb-value"},{include:"#function"},{include:"#function-content"},{begin:"(?<=})(?!\\n|\\(|\\)|[a-zA-Z0-9_-]+:)",end:"\\s|(?=,|\\.|\\[|\\)|\\n)",name:"entity.name.tag.css.sass",patterns:[{include:"#interpolation"},{include:"#pseudo-class"}]},{include:"#operator"},{match:"[a-z-]+((?=:|#{))",name:"support.type.property-name.css.sass.prop.name"},{include:"#reserved-words"},{include:"#property-value"}],repository:{colon:{match:":",name:"meta.property-list.css.sass.colon"},comma:{match:"\\band\\b|\\bor\\b|,",name:"comment.punctuation.comma.sass"},"comment-param":{match:"\\@(\\w+)",name:"storage.type.class.jsdoc"},"comment-tag":{begin:"(?<={{)",end:"(?=}})",name:"comment.tag.sass"},"curly-brackets":{match:"{|}",name:"invalid"},dotdotdot:{match:"\\.\\.\\.",name:"variable.other"},"double-quoted":{begin:'"',end:'"',name:"string.quoted.double.css.sass",patterns:[{include:"#quoted-interpolation"}]},"double-slash":{begin:"//",end:"$\\n?",name:"comment.line.sass",patterns:[{include:"#comment-tag"}]},flag:{match:"!(important|default|optional|global)",name:"keyword.other.important.css.sass"},function:{match:"(?<=[\\s|(|,|:])(?!url|format|attr)[a-zA-Z0-9_-][\\w-]*(?=\\()",name:"support.function.name.sass"},"function-content":{begin:"(?<=url\\(|format\\(|attr\\()",end:".(?=\\))",name:"string.quoted.double.css.sass"},"import-quotes":{match:"[\"']?\\.{0,2}[\\w/]+[\"']?",name:"constant.character.css.sass"},interpolation:{begin:"#{",end:"}",name:"support.function.interpolation.sass",patterns:[{include:"#variable"},{include:"#numeric"},{include:"#operator"},{include:"#unit"},{include:"#comma"},{include:"#double-quoted"},{include:"#single-quoted"}]},module:{captures:{1:{name:"constant.character.module.name"},2:{name:"constant.numeric.module.dot"}},match:"([\\w-]+?)(\\.)",name:"constant.character.module"},numeric:{match:"(-|\\.)?\\d+(\\.\\d+)?",name:"constant.numeric.css.sass"},operator:{match:"\\+|\\s-\\s|\\s-(?=\\$)|(?<=\\()-(?=\\$)|\\s-(?=\\()|\\*|/|%|=|!|<|>|~",name:"keyword.operator.sass"},"parent-selector":{match:"&",name:"entity.name.tag.css.sass"},"parenthesis-close":{match:"\\)",name:"entity.name.function.parenthesis.close"},"parenthesis-open":{match:"\\(",name:"entity.name.function.parenthesis.open"},"placeholder-selector":{begin:"(?<!\\d)%(?!\\d)",end:"$\\n?|\\s",name:"entity.other.inherited-class.placeholder-selector.css.sass"},"property-value":{match:"[a-zA-Z0-9_-]+",name:"meta.property-value.css.sass support.constant.property-value.css.sass"},"pseudo-class":{match:":[a-z:-]+",name:"entity.other.attribute-name.pseudo-class.css.sass"},"quoted-interpolation":{begin:"#{",end:"}",name:"support.function.interpolation.sass",patterns:[{include:"#variable"},{include:"#numeric"},{include:"#operator"},{include:"#unit"},{include:"#comma"}]},"reserved-words":{match:"\\b(false|from|in|not|null|through|to|true)\\b",name:"support.type.property-name.css.sass"},"rgb-value":{match:"(#)([0-9a-fA-F]{3,4}|[0-9a-fA-F]{6}|[0-9a-fA-F]{8})\\b",name:"constant.language.color.rgb-value.css.sass"},semicolon:{match:";",name:"invalid"},"single-quoted":{begin:"'",end:"'",name:"string.quoted.single.css.sass",patterns:[{include:"#quoted-interpolation"}]},unit:{match:"(?<=[\\d]|})(ch|cm|deg|dpcm|dpi|dppx|em|ex|grad|Hz|in|kHz|mm|ms|pc|pt|px|rad|rem|s|turn|vh|vmax|vmin|vw|fr|%)",name:"keyword.control.unit.css.sass"},variable:{match:"\\$[a-zA-Z0-9_-]+",name:"variable.other.value"},"variable-root":{match:"\\$[a-zA-Z0-9_-]+",name:"variable.other.root"}},scopeName:"source.sass"})]}};