(()=>{var t={};t.id=1972,t.ids=[1972],t.modules={2150:(t,e,i)=>{let{createProxy:s}=i(20867);t.exports=s("C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\node_modules\\.pnpm\\next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f\\node_modules\\next\\dist\\client\\image-component.js")},3295:t=>{"use strict";t.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:t=>{"use strict";t.exports=require("node:buffer")},8086:t=>{"use strict";t.exports=require("module")},10846:t=>{"use strict";t.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19063:t=>{"use strict";t.exports=require("require-in-the-middle")},19121:t=>{"use strict";t.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19257:(t,e,i)=>{"use strict";i.r(e),i.d(e,{showBetaFeature:()=>l});var s=i(51153);let r=(0,i(28364).H)(),n=r.NEXT_PUBLIC_POSTHOG_KEY&&r.NEXT_PUBLIC_POSTHOG_HOST?new s.f2(r.NEXT_PUBLIC_POSTHOG_KEY,{host:r.NEXT_PUBLIC_POSTHOG_HOST,flushAt:1,flushInterval:0}):null;var o=i(18639),a=i(8392);let l=(t=>(0,a.Jt)({key:t,defaultValue:!1,async decide(){let{userId:e}=await (0,o.j)();return e?(n?await n.isFeatureEnabled(t,e):null)??this.defaultValue:this.defaultValue}}))("showBetaFeature")},21820:t=>{"use strict";t.exports=require("os")},23132:(t,e,i)=>{"use strict";i.r(e),i.d(e,{"00e578e70edcb55dedc8be97f84cdf9c4ce9b9fbb2":()=>s.z2,"405efdbdcc87c20941759ae1bb1e4f3629f6226400":()=>r.x,"6040ce2583939275b9b79a23c761210186958f33ca":()=>s.qr,"60ac4068834bf9e1b32327478a0425a5daded1d553":()=>s.xK,"60d7e646f4b8f4f2b31c38fcbc9963402a4f1620ad":()=>s.q2});var s=i(22589),r=i(18362)},23616:(t,e,i)=>{"use strict";i.r(e),i.d(e,{default:()=>y,generateMetadata:()=>g});var s=i(94752),r=i(52912),n=i(57752),o=i(48410);let a=({children:t,className:e})=>(0,s.jsx)("section",{className:function(...t){return(0,o.QP)((0,n.$)(t))}(e),children:t}),l=()=>(0,s.jsxs)("svg",{className:"absolute top-0 left-0 hidden pointer-events-none md:flex 2xl:hidden",width:"579",height:"511",viewBox:"0 0 579 511",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,s.jsx)("g",{style:{mixBlendMode:"lighten"},filter:"url(#filter0_f_574_3059)",children:(0,s.jsx)("ellipse",{cx:"-5.14484",cy:"-64.8282",rx:"32.7783",ry:"293.346",transform:"rotate(20.0538 -5.14484 -64.8282)",fill:"url(#paint0_linear_574_3059)",fillOpacity:"0.5"})}),(0,s.jsx)("g",{style:{mixBlendMode:"lighten"},filter:"url(#filter2_f_574_3059)",children:(0,s.jsx)("ellipse",{cx:"198.822",cy:"3.50066",rx:"22.3794",ry:"381.284",transform:"rotate(-10 198.822 3.50066)",fill:"url(#paint2_linear_574_3059)",fillOpacity:"0.5"})}),(0,s.jsx)("g",{style:{mixBlendMode:"lighten"},filter:"url(#filter3_f_574_3059)",children:(0,s.jsx)("ellipse",{cx:"163.986",cy:"-194.068",rx:"22.3794",ry:"180.667",transform:"rotate(-10 163.986 -194.068)",fill:"url(#paint3_linear_574_3059)",fillOpacity:"0.5"})}),(0,s.jsx)("g",{style:{mixBlendMode:"lighten"},filter:"url(#filter4_f_574_3059)",children:(0,s.jsx)("ellipse",{cx:"88.5057",cy:"41.4464",rx:"22.25",ry:"381.5",transform:"rotate(5 88.5057 41.4464)",fill:"url(#paint4_linear_574_3059)",fillOpacity:"0.5"})}),(0,s.jsx)("g",{style:{mixBlendMode:"lighten"},filter:"url(#filter5_f_574_3059)",children:(0,s.jsx)("ellipse",{cx:"107.823",cy:"-182.221",rx:"321.5",ry:"187.5",transform:"rotate(5 107.823 -182.221)",fill:"url(#paint5_linear_574_3059)",fillOpacity:"0.5"})}),(0,s.jsxs)("defs",{children:[(0,s.jsxs)("filter",{id:"filter0_f_574_3059",x:"-199.369",y:"-429.622",width:"388.449",height:"729.588",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB",children:[(0,s.jsx)("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),(0,s.jsx)("feBlend",{mode:"normal",in:"SourceGraphic",in2:"BackgroundImageFix",result:"shape"}),(0,s.jsx)("feGaussianBlur",{stdDeviation:"44.5",result:"effect1_foregroundBlur_574_3059"})]}),(0,s.jsxs)("filter",{id:"filter1_f_574_3059",x:"-23.6509",y:"-457.712",width:"251.489",height:"762.287",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB",children:[(0,s.jsx)("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),(0,s.jsx)("feBlend",{mode:"normal",in:"SourceGraphic",in2:"BackgroundImageFix",result:"shape"}),(0,s.jsx)("feGaussianBlur",{stdDeviation:"44.5",result:"effect1_foregroundBlur_574_3059"})]}),(0,s.jsxs)("filter",{id:"filter2_f_574_3059",x:"40.0224",y:"-461.011",width:"317.6",height:"929.023",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB",children:[(0,s.jsx)("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),(0,s.jsx)("feBlend",{mode:"normal",in:"SourceGraphic",in2:"BackgroundImageFix",result:"shape"}),(0,s.jsx)("feGaussianBlur",{stdDeviation:"44.5",result:"effect1_foregroundBlur_574_3059"})]}),(0,s.jsxs)("filter",{id:"filter3_f_574_3059",x:"36.6421",y:"-461.034",width:"254.687",height:"533.932",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB",children:[(0,s.jsx)("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),(0,s.jsx)("feBlend",{mode:"normal",in:"SourceGraphic",in2:"BackgroundImageFix",result:"shape"}),(0,s.jsx)("feGaussianBlur",{stdDeviation:"44.5",result:"effect1_foregroundBlur_574_3059"})]}),(0,s.jsxs)("filter",{id:"filter4_f_574_3059",x:"-40.4595",y:"-427.607",width:"257.93",height:"938.107",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB",children:[(0,s.jsx)("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),(0,s.jsx)("feBlend",{mode:"normal",in:"SourceGraphic",in2:"BackgroundImageFix",result:"shape"}),(0,s.jsx)("feGaussianBlur",{stdDeviation:"44.5",result:"effect1_foregroundBlur_574_3059"})]}),(0,s.jsxs)("filter",{id:"filter5_f_574_3059",x:"-362.878",y:"-521.123",width:"941.402",height:"677.805",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB",children:[(0,s.jsx)("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),(0,s.jsx)("feBlend",{mode:"normal",in:"SourceGraphic",in2:"BackgroundImageFix",result:"shape"}),(0,s.jsx)("feGaussianBlur",{stdDeviation:"75",result:"effect1_foregroundBlur_574_3059"})]}),(0,s.jsxs)("linearGradient",{id:"paint0_linear_574_3059",x1:"-5.14484",y1:"-358.174",x2:"-5.14484",y2:"228.517",gradientUnits:"userSpaceOnUse",children:[(0,s.jsx)("stop",{stopColor:"white"}),(0,s.jsx)("stop",{offset:"1",stopColor:"white",stopOpacity:"0"})]}),(0,s.jsxs)("linearGradient",{id:"paint1_linear_574_3059",x1:"102.094",y1:"-369.819",x2:"102.094",y2:"216.681",gradientUnits:"userSpaceOnUse",children:[(0,s.jsx)("stop",{stopColor:"white"}),(0,s.jsx)("stop",{offset:"1",stopColor:"white",stopOpacity:"0"})]}),(0,s.jsxs)("linearGradient",{id:"paint2_linear_574_3059",x1:"198.822",y1:"-377.783",x2:"198.822",y2:"384.784",gradientUnits:"userSpaceOnUse",children:[(0,s.jsx)("stop",{stopColor:"white"}),(0,s.jsx)("stop",{offset:"1",stopColor:"white",stopOpacity:"0"})]}),(0,s.jsxs)("linearGradient",{id:"paint3_linear_574_3059",x1:"163.986",y1:"-374.736",x2:"163.986",y2:"-13.4011",gradientUnits:"userSpaceOnUse",children:[(0,s.jsx)("stop",{stopColor:"white"}),(0,s.jsx)("stop",{offset:"1",stopColor:"white",stopOpacity:"0"})]}),(0,s.jsxs)("linearGradient",{id:"paint4_linear_574_3059",x1:"88.5057",y1:"-340.054",x2:"88.5057",y2:"422.946",gradientUnits:"userSpaceOnUse",children:[(0,s.jsx)("stop",{stopColor:"white"}),(0,s.jsx)("stop",{offset:"1",stopColor:"white",stopOpacity:"0"})]}),(0,s.jsxs)("linearGradient",{id:"paint5_linear_574_3059",x1:"107.823",y1:"-369.721",x2:"107.823",y2:"5.27896",gradientUnits:"userSpaceOnUse",children:[(0,s.jsx)("stop",{stopColor:"white"}),(0,s.jsx)("stop",{offset:"1",stopColor:"white",stopOpacity:"0"})]})]})]}),u=()=>(0,s.jsxs)("svg",{className:"absolute top-0 right-0 hidden pointer-events-none md:flex 2xl:hidden",width:"445",height:"699",viewBox:"0 0 445 699",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,s.jsx)("g",{style:{mixBlendMode:"lighten"},filter:"url(#filter0_f_574_3050)",children:(0,s.jsx)("ellipse",{cx:"336.291",cy:"119.962",rx:"32.7783",ry:"293.346",transform:"rotate(30.0538 336.291 119.962)",fill:"url(#paint0_linear_574_3050)",fillOpacity:"0.5"})}),(0,s.jsx)("g",{style:{mixBlendMode:"lighten"},filter:"url(#filter2_f_574_3050)",children:(0,s.jsx)("ellipse",{cx:"525.295",cy:"222.671",rx:"22.3794",ry:"381.284",fill:"url(#paint2_linear_574_3050)",fillOpacity:"0.5"})}),(0,s.jsx)("g",{style:{mixBlendMode:"lighten"},filter:"url(#filter3_f_574_3050)",children:(0,s.jsx)("ellipse",{cx:"525.295",cy:"22.0542",rx:"22.3794",ry:"180.667",fill:"url(#paint3_linear_574_3050)",fillOpacity:"0.5"})}),(0,s.jsx)("g",{style:{mixBlendMode:"lighten"},filter:"url(#filter4_f_574_3050)",children:(0,s.jsx)("ellipse",{cx:"410.065",cy:"240.884",rx:"22.25",ry:"381.5",transform:"rotate(15 410.065 240.884)",fill:"url(#paint4_linear_574_3050)",fillOpacity:"0.5"})}),(0,s.jsx)("g",{style:{mixBlendMode:"lighten"},filter:"url(#filter5_f_574_3050)",children:(0,s.jsx)("ellipse",{cx:"467.928",cy:"23.9689",rx:"321.5",ry:"187.5",transform:"rotate(15 467.928 23.9689)",fill:"url(#paint5_linear_574_3050)",fillOpacity:"0.5"})}),(0,s.jsx)("g",{style:{mixBlendMode:"lighten"},filter:"url(#filter6_f_574_3050)",children:(0,s.jsx)("ellipse",{cx:"491.74",cy:"-64.8963",rx:"160.5",ry:"95.5",transform:"rotate(15 491.74 -64.8963)",fill:"url(#paint6_linear_574_3050)",fillOpacity:"0.5"})}),(0,s.jsx)("g",{style:{mixBlendMode:"lighten"},filter:"url(#filter7_f_574_3050)",children:(0,s.jsx)("ellipse",{cx:"489.863",cy:"-57.8934",rx:"135",ry:"80.25",transform:"rotate(15 489.863 -57.8934)",fill:"url(#paint7_linear_574_3050)",fillOpacity:"0.5"})}),(0,s.jsxs)("defs",{children:[(0,s.jsxs)("filter",{id:"filter0_f_574_3050",x:"97.6377",y:"-223.485",width:"477.308",height:"686.892",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB",children:[(0,s.jsx)("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),(0,s.jsx)("feBlend",{mode:"normal",in:"SourceGraphic",in2:"BackgroundImageFix",result:"shape"}),(0,s.jsx)("feGaussianBlur",{stdDeviation:"44.5",result:"effect1_foregroundBlur_574_3050"})]}),(0,s.jsxs)("filter",{id:"filter1_f_574_3050",x:"274.818",y:"-245.321",width:"338.241",height:"744.685",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB",children:[(0,s.jsx)("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),(0,s.jsx)("feBlend",{mode:"normal",in:"SourceGraphic",in2:"BackgroundImageFix",result:"shape"}),(0,s.jsx)("feGaussianBlur",{stdDeviation:"44.5",result:"effect1_foregroundBlur_574_3050"})]}),(0,s.jsxs)("filter",{id:"filter2_f_574_3050",x:"413.916",y:"-247.613",width:"222.759",height:"940.567",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB",children:[(0,s.jsx)("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),(0,s.jsx)("feBlend",{mode:"normal",in:"SourceGraphic",in2:"BackgroundImageFix",result:"shape"}),(0,s.jsx)("feGaussianBlur",{stdDeviation:"44.5",result:"effect1_foregroundBlur_574_3050"})]}),(0,s.jsxs)("filter",{id:"filter3_f_574_3050",x:"413.916",y:"-247.613",width:"222.759",height:"539.335",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB",children:[(0,s.jsx)("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),(0,s.jsx)("feBlend",{mode:"normal",in:"SourceGraphic",in2:"BackgroundImageFix",result:"shape"}),(0,s.jsx)("feGaussianBlur",{stdDeviation:"44.5",result:"effect1_foregroundBlur_574_3050"})]}),(0,s.jsxs)("filter",{id:"filter4_f_574_3050",x:"219.992",y:"-216.663",width:"380.146",height:"915.093",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB",children:[(0,s.jsx)("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),(0,s.jsx)("feBlend",{mode:"normal",in:"SourceGraphic",in2:"BackgroundImageFix",result:"shape"}),(0,s.jsx)("feGaussianBlur",{stdDeviation:"44.5",result:"effect1_foregroundBlur_574_3050"})]}),(0,s.jsxs)("filter",{id:"filter5_f_574_3050",x:"3.56885",y:"-325.391",width:"928.719",height:"698.72",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB",children:[(0,s.jsx)("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),(0,s.jsx)("feBlend",{mode:"normal",in:"SourceGraphic",in2:"BackgroundImageFix",result:"shape"}),(0,s.jsx)("feGaussianBlur",{stdDeviation:"75",result:"effect1_foregroundBlur_574_3050"})]}),(0,s.jsxs)("filter",{id:"filter6_f_574_3050",x:"184.728",y:"-316.089",width:"614.024",height:"502.385",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB",children:[(0,s.jsx)("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),(0,s.jsx)("feBlend",{mode:"normal",in:"SourceGraphic",in2:"BackgroundImageFix",result:"shape"}),(0,s.jsx)("feGaussianBlur",{stdDeviation:"75",result:"effect1_foregroundBlur_574_3050"})]}),(0,s.jsxs)("filter",{id:"filter7_f_574_3050",x:"207.8",y:"-292.941",width:"564.126",height:"470.095",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB",children:[(0,s.jsx)("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),(0,s.jsx)("feBlend",{mode:"normal",in:"SourceGraphic",in2:"BackgroundImageFix",result:"shape"}),(0,s.jsx)("feGaussianBlur",{stdDeviation:"75",result:"effect1_foregroundBlur_574_3050"})]}),(0,s.jsxs)("linearGradient",{id:"paint0_linear_574_3050",x1:"336.291",y1:"-173.384",x2:"336.291",y2:"413.307",gradientUnits:"userSpaceOnUse",children:[(0,s.jsx)("stop",{stopColor:"white"}),(0,s.jsx)("stop",{offset:"1",stopColor:"white",stopOpacity:"0"})]}),(0,s.jsxs)("linearGradient",{id:"paint1_linear_574_3050",x1:"443.939",y1:"-166.229",x2:"443.939",y2:"420.271",gradientUnits:"userSpaceOnUse",children:[(0,s.jsx)("stop",{stopColor:"white"}),(0,s.jsx)("stop",{offset:"1",stopColor:"white",stopOpacity:"0"})]}),(0,s.jsxs)("linearGradient",{id:"paint2_linear_574_3050",x1:"525.295",y1:"-158.613",x2:"525.295",y2:"603.955",gradientUnits:"userSpaceOnUse",children:[(0,s.jsx)("stop",{stopColor:"white"}),(0,s.jsx)("stop",{offset:"1",stopColor:"white",stopOpacity:"0"})]}),(0,s.jsxs)("linearGradient",{id:"paint3_linear_574_3050",x1:"525.295",y1:"-158.613",x2:"525.295",y2:"202.721",gradientUnits:"userSpaceOnUse",children:[(0,s.jsx)("stop",{stopColor:"white"}),(0,s.jsx)("stop",{offset:"1",stopColor:"white",stopOpacity:"0"})]}),(0,s.jsxs)("linearGradient",{id:"paint4_linear_574_3050",x1:"410.065",y1:"-140.616",x2:"410.065",y2:"622.384",gradientUnits:"userSpaceOnUse",children:[(0,s.jsx)("stop",{stopColor:"white"}),(0,s.jsx)("stop",{offset:"1",stopColor:"white",stopOpacity:"0"})]}),(0,s.jsxs)("linearGradient",{id:"paint5_linear_574_3050",x1:"467.928",y1:"-163.531",x2:"467.928",y2:"211.469",gradientUnits:"userSpaceOnUse",children:[(0,s.jsx)("stop",{stopColor:"white"}),(0,s.jsx)("stop",{offset:"1",stopColor:"white",stopOpacity:"0"})]}),(0,s.jsxs)("linearGradient",{id:"paint6_linear_574_3050",x1:"491.74",y1:"-160.396",x2:"491.74",y2:"30.6037",gradientUnits:"userSpaceOnUse",children:[(0,s.jsx)("stop",{stopColor:"white"}),(0,s.jsx)("stop",{offset:"1",stopColor:"white",stopOpacity:"0"})]}),(0,s.jsxs)("linearGradient",{id:"paint7_linear_574_3050",x1:"489.863",y1:"-138.143",x2:"489.863",y2:"22.3566",gradientUnits:"userSpaceOnUse",children:[(0,s.jsx)("stop",{stopColor:"white"}),(0,s.jsx)("stop",{offset:"1",stopColor:"white",stopOpacity:"0"})]})]})]});var h=i(83528),d=i.n(h),c=i(76842),p=i(19257),m=i(77422),f=i(56450);let g=async({params:t})=>{let{locale:e}=await t,i=await (0,m.T)(e);return(0,f.w)(i.web.home.meta)},y=async({params:t})=>{let{locale:e}=await t;await (0,m.T)(e);let i=await (0,p.showBetaFeature)();return(0,s.jsxs)(s.Fragment,{children:[i&&(0,s.jsx)("div",{className:"w-full bg-black py-2 text-center text-white",children:"Beta feature now available"}),(0,s.jsx)(u,{}),(0,s.jsx)(l,{}),(0,s.jsxs)("div",{className:"relative w-full pt-6 overflow-hidden",children:[(0,s.jsx)("div",{className:"container relative mx-auto",children:(0,s.jsx)(d(),{src:c.default,alt:"Animated SVG showing computer circuits lighting up",className:"absolute inset-x-0 flex  xl:hidden -z-10 scale-[2]",priority:!0})}),(0,s.jsx)("div",{className:"container relative flex flex-col mx-auto",children:(0,s.jsx)(a,{children:(0,s.jsx)(r.Hero,{})})})]})]})}},25741:(t,e,i)=>{"use strict";i.d(e,{A:()=>s});let s=(0,i(19161).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},26634:(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"getImgProps",{enumerable:!0,get:function(){return l}}),i(33159);let s=i(82049),r=i(56829),n=["-moz-initial","fill","none","scale-down",void 0];function o(t){return void 0!==t.default}function a(t){return void 0===t?t:"number"==typeof t?Number.isFinite(t)?t:NaN:"string"==typeof t&&/^[0-9]+$/.test(t)?parseInt(t,10):NaN}function l(t,e){var i,l;let u,h,d,{src:c,sizes:p,unoptimized:m=!1,priority:f=!1,loading:g,className:y,quality:v,width:x,height:w,fill:b=!1,style:S,overrideSrc:T,onLoad:P,onLoadingComplete:j,placeholder:_="empty",blurDataURL:A,fetchPriority:C,decoding:E="async",layout:M,objectFit:D,objectPosition:k,lazyBoundary:B,lazyRoot:V,...O}=t,{imgConf:R,showAltText:F,blurComplete:L,defaultLoader:I}=e,U=R||r.imageConfigDefault;if("allSizes"in U)u=U;else{let t=[...U.deviceSizes,...U.imageSizes].sort((t,e)=>t-e),e=U.deviceSizes.sort((t,e)=>t-e),s=null==(i=U.qualities)?void 0:i.sort((t,e)=>t-e);u={...U,allSizes:t,deviceSizes:e,qualities:s}}if(void 0===I)throw Object.defineProperty(Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config"),"__NEXT_ERROR_CODE",{value:"E163",enumerable:!1,configurable:!0});let N=O.loader||I;delete O.loader,delete O.srcSet;let G="__next_img_default"in N;if(G){if("custom"===u.loader)throw Object.defineProperty(Error('Image with src "'+c+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader'),"__NEXT_ERROR_CODE",{value:"E252",enumerable:!1,configurable:!0})}else{let t=N;N=e=>{let{config:i,...s}=e;return t(s)}}if(M){"fill"===M&&(b=!0);let t={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[M];t&&(S={...S,...t});let e={responsive:"100vw",fill:"100vw"}[M];e&&!p&&(p=e)}let q="",$=a(x),W=a(w);if((l=c)&&"object"==typeof l&&(o(l)||void 0!==l.src)){let t=o(c)?c.default:c;if(!t.src)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(t)),"__NEXT_ERROR_CODE",{value:"E460",enumerable:!1,configurable:!0});if(!t.height||!t.width)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(t)),"__NEXT_ERROR_CODE",{value:"E48",enumerable:!1,configurable:!0});if(h=t.blurWidth,d=t.blurHeight,A=A||t.blurDataURL,q=t.src,!b)if($||W){if($&&!W){let e=$/t.width;W=Math.round(t.height*e)}else if(!$&&W){let e=W/t.height;$=Math.round(t.width*e)}}else $=t.width,W=t.height}let z=!f&&("lazy"===g||void 0===g);(!(c="string"==typeof c?c:q)||c.startsWith("data:")||c.startsWith("blob:"))&&(m=!0,z=!1),u.unoptimized&&(m=!0),G&&!u.dangerouslyAllowSVG&&c.split("?",1)[0].endsWith(".svg")&&(m=!0);let H=a(v),X=Object.assign(b?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:D,objectPosition:k}:{},F?{}:{color:"transparent"},S),Y=L||"empty"===_?null:"blur"===_?'url("data:image/svg+xml;charset=utf-8,'+(0,s.getImageBlurSvg)({widthInt:$,heightInt:W,blurWidth:h,blurHeight:d,blurDataURL:A||"",objectFit:X.objectFit})+'")':'url("'+_+'")',K=n.includes(X.objectFit)?"fill"===X.objectFit?"100% 100%":"cover":X.objectFit,Z=Y?{backgroundSize:K,backgroundPosition:X.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:Y}:{},J=function(t){let{config:e,src:i,unoptimized:s,width:r,quality:n,sizes:o,loader:a}=t;if(s)return{src:i,srcSet:void 0,sizes:void 0};let{widths:l,kind:u}=function(t,e,i){let{deviceSizes:s,allSizes:r}=t;if(i){let t=/(^|\s)(1?\d?\d)vw/g,e=[];for(let s;s=t.exec(i);)e.push(parseInt(s[2]));if(e.length){let t=.01*Math.min(...e);return{widths:r.filter(e=>e>=s[0]*t),kind:"w"}}return{widths:r,kind:"w"}}return"number"!=typeof e?{widths:s,kind:"w"}:{widths:[...new Set([e,2*e].map(t=>r.find(e=>e>=t)||r[r.length-1]))],kind:"x"}}(e,r,o),h=l.length-1;return{sizes:o||"w"!==u?o:"100vw",srcSet:l.map((t,s)=>a({config:e,src:i,quality:n,width:t})+" "+("w"===u?t:s+1)+u).join(", "),src:a({config:e,src:i,quality:n,width:l[h]})}}({config:u,src:c,unoptimized:m,width:$,quality:H,sizes:p,loader:N});return{props:{...O,loading:z?"lazy":g,fetchPriority:C,width:$,height:W,decoding:E,className:y,style:{...X,...Z},sizes:J.sizes,srcSet:J.srcSet,src:T||J.src},meta:{unoptimized:m,priority:f,placeholder:_,fill:b}}}},27910:t=>{"use strict";t.exports=require("stream")},28354:t=>{"use strict";t.exports=require("util")},29021:t=>{"use strict";t.exports=require("fs")},29294:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:t=>{"use strict";t.exports=require("node:child_process")},33159:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"warnOnce",{enumerable:!0,get:function(){return i}});let i=t=>{}},33873:t=>{"use strict";t.exports=require("path")},36686:t=>{"use strict";t.exports=require("diagnostics_channel")},37067:t=>{"use strict";t.exports=require("node:http")},38522:t=>{"use strict";t.exports=require("node:zlib")},39890:(t,e,i)=>{"use strict";i.r(e),i.d(e,{default:()=>s});let s={src:"/_next/static/media/mainboard.9232efd0.svg",height:546,width:1512,blurWidth:0,blurHeight:0}},41692:t=>{"use strict";t.exports=require("node:tls")},42152:t=>{"use strict";t.exports=require("process")},44708:t=>{"use strict";t.exports=require("node:https")},48161:t=>{"use strict";t.exports=require("node:os")},48592:(t,e,i)=>{Promise.resolve().then(i.bind(i,52912)),Promise.resolve().then(i.bind(i,76842)),Promise.resolve().then(i.t.bind(i,2150,23))},52912:(t,e,i)=>{"use strict";i.d(e,{Hero:()=>s});let s=(0,i(6340).registerClientReference)(function(){throw Error("Attempted to call Hero() from the server but Hero is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\web\\components\\hero\\hero.tsx","Hero")},53053:t=>{"use strict";t.exports=require("node:diagnostics_channel")},55511:t=>{"use strict";t.exports=require("crypto")},56450:(t,e,i)=>{"use strict";i.d(e,{w:()=>l});var s=i(81121),r=i.n(s);let n="next-forge",o={name:"Vercel",url:"https://vercel.com/"},a=process.env.VERCEL_PROJECT_PRODUCTION_URL,l=({title:t,description:e,image:i,...s})=>{let l=`${t} | ${n}`,u={title:l,description:e,applicationName:n,metadataBase:a?new URL(`https://${a}`):void 0,authors:[o],creator:o.name,formatDetection:{telephone:!1},appleWebApp:{capable:!0,statusBarStyle:"default",title:l},openGraph:{title:l,description:e,type:"website",siteName:n,locale:"en_US"},publisher:"Vercel",twitter:{card:"summary_large_image",creator:"@vercel"}},h=r()(u,s);return i&&h.openGraph&&(h.openGraph.images=[{url:i,width:1200,height:630,alt:t}]),h}},56801:t=>{"use strict";t.exports=require("import-in-the-middle")},56829:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{VALID_LOADERS:function(){return i},imageConfigDefault:function(){return s}});let i=["default","imgix","cloudinary","akamai","custom"],s={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},57075:t=>{"use strict";t.exports=require("node:stream")},57975:t=>{"use strict";t.exports=require("node:util")},58320:(t,e,i)=>{Promise.resolve().then(i.bind(i,59467)),Promise.resolve().then(i.bind(i,39890)),Promise.resolve().then(i.t.bind(i,53320,23))},59467:(t,e,i)=>{"use strict";let s;i.d(e,{Hero:()=>nN});var r,n,o=i(99730);function a(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}function l(t){let e=[{},{}];return t?.values.forEach((t,i)=>{e[0][i]=t.get(),e[1][i]=t.getVelocity()}),e}function u(t,e,i,s){if("function"==typeof e){let[r,n]=l(s);e=e(void 0!==i?i:t.custom,r,n)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){let[r,n]=l(s);e=e(void 0!==i?i:t.custom,r,n)}return e}function h(t,e,i){let s=t.getProps();return u(s,e,void 0!==i?i:s.custom,t)}function d(t,e){return t?.[e]??t?.default??t}let c=t=>t,p={},m=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],f={value:null,addProjectionMetrics:null};function g(t,e){let i=!1,s=!0,r={delta:0,timestamp:0,isProcessing:!1},n=()=>i=!0,o=m.reduce((t,i)=>(t[i]=function(t,e){let i=new Set,s=new Set,r=!1,n=!1,o=new WeakSet,a={delta:0,timestamp:0,isProcessing:!1},l=0;function u(e){o.has(e)&&(h.schedule(e),t()),l++,e(a)}let h={schedule:(t,e=!1,n=!1)=>{let a=n&&r?i:s;return e&&o.add(t),a.has(t)||a.add(t),t},cancel:t=>{s.delete(t),o.delete(t)},process:t=>{if(a=t,r){n=!0;return}r=!0,[i,s]=[s,i],i.forEach(u),e&&f.value&&f.value.frameloop[e].push(l),l=0,i.clear(),r=!1,n&&(n=!1,h.process(t))}};return h}(n,e?i:void 0),t),{}),{setup:a,read:l,resolveKeyframes:u,preUpdate:h,update:d,preRender:c,render:g,postRender:y}=o,v=()=>{let n=p.useManualTiming?r.timestamp:performance.now();i=!1,p.useManualTiming||(r.delta=s?1e3/60:Math.max(Math.min(n-r.timestamp,40),1)),r.timestamp=n,r.isProcessing=!0,a.process(r),l.process(r),u.process(r),h.process(r),d.process(r),c.process(r),g.process(r),y.process(r),r.isProcessing=!1,i&&e&&(s=!1,t(v))},x=()=>{i=!0,s=!0,r.isProcessing||t(v)};return{schedule:m.reduce((t,e)=>{let s=o[e];return t[e]=(t,e=!1,r=!1)=>(i||x(),s.schedule(t,e,r)),t},{}),cancel:t=>{for(let e=0;e<m.length;e++)o[m[e]].cancel(t)},state:r,steps:o}}let{schedule:y,cancel:v,state:x,steps:w}=g("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:c,!0),b=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],S=new Set(b),T=new Set(["width","height","top","left","right","bottom",...b]);function P(t,e){-1===t.indexOf(e)&&t.push(e)}function j(t,e){let i=t.indexOf(e);i>-1&&t.splice(i,1)}class _{constructor(){this.subscriptions=[]}add(t){return P(this.subscriptions,t),()=>j(this.subscriptions,t)}notify(t,e,i){let s=this.subscriptions.length;if(s)if(1===s)this.subscriptions[0](t,e,i);else for(let r=0;r<s;r++){let s=this.subscriptions[r];s&&s(t,e,i)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function A(){s=void 0}let C={now:()=>(void 0===s&&C.set(x.isProcessing||p.useManualTiming?x.timestamp:performance.now()),s),set:t=>{s=t,queueMicrotask(A)}},E=t=>!isNaN(parseFloat(t)),M={current:void 0};class D{constructor(t,e={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{let i=C.now();if(this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let t of this.dependents)t.dirty();e&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){this.current=t,this.updatedAt=C.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=E(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new _);let i=this.events[t].add(e);return"change"===t?()=>{i(),y.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,i){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-i}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return M.current&&M.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var t;let e=C.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||e-this.updatedAt>30)return 0;let i=Math.min(this.updatedAt-this.prevUpdatedAt,30);return t=parseFloat(this.current)-parseFloat(this.prevFrameValue),i?1e3/i*t:0}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function k(t,e){return new D(t,e)}let B=t=>Array.isArray(t),V=t=>!!(t&&t.getVelocity);function O(t,e){let i=t.getValue("willChange");if(V(i)&&i.add)return i.add(e);if(!i&&p.WillChange){let i=new p.WillChange("auto");t.addValue("willChange",i),i.add(e)}}let R=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),F="data-"+R("framerAppearId"),L=(t,e)=>i=>e(t(i)),I=(...t)=>t.reduce(L),U=(t,e,i)=>i>e?e:i<t?t:i,N=t=>1e3*t,G=t=>t/1e3,q={layout:0,mainThread:0,waapi:0},$=()=>{},W=()=>{},z=t=>e=>"string"==typeof e&&e.startsWith(t),H=z("--"),X=z("var(--"),Y=t=>!!X(t)&&K.test(t.split("/*")[0].trim()),K=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,Z={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},J={...Z,transform:t=>U(0,1,t)},Q={...Z,default:1},tt=t=>Math.round(1e5*t)/1e5,te=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,ti=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,ts=(t,e)=>i=>!!("string"==typeof i&&ti.test(i)&&i.startsWith(t)||e&&null!=i&&Object.prototype.hasOwnProperty.call(i,e)),tr=(t,e,i)=>s=>{if("string"!=typeof s)return s;let[r,n,o,a]=s.match(te);return{[t]:parseFloat(r),[e]:parseFloat(n),[i]:parseFloat(o),alpha:void 0!==a?parseFloat(a):1}},tn=t=>U(0,255,t),to={...Z,transform:t=>Math.round(tn(t))},ta={test:ts("rgb","red"),parse:tr("red","green","blue"),transform:({red:t,green:e,blue:i,alpha:s=1})=>"rgba("+to.transform(t)+", "+to.transform(e)+", "+to.transform(i)+", "+tt(J.transform(s))+")"},tl={test:ts("#"),parse:function(t){let e="",i="",s="",r="";return t.length>5?(e=t.substring(1,3),i=t.substring(3,5),s=t.substring(5,7),r=t.substring(7,9)):(e=t.substring(1,2),i=t.substring(2,3),s=t.substring(3,4),r=t.substring(4,5),e+=e,i+=i,s+=s,r+=r),{red:parseInt(e,16),green:parseInt(i,16),blue:parseInt(s,16),alpha:r?parseInt(r,16)/255:1}},transform:ta.transform},tu=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),th=tu("deg"),td=tu("%"),tc=tu("px"),tp=tu("vh"),tm=tu("vw"),tf={...td,parse:t=>td.parse(t)/100,transform:t=>td.transform(100*t)},tg={test:ts("hsl","hue"),parse:tr("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:i,alpha:s=1})=>"hsla("+Math.round(t)+", "+td.transform(tt(e))+", "+td.transform(tt(i))+", "+tt(J.transform(s))+")"},ty={test:t=>ta.test(t)||tl.test(t)||tg.test(t),parse:t=>ta.test(t)?ta.parse(t):tg.test(t)?tg.parse(t):tl.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?ta.transform(t):tg.transform(t),getAnimatableNone:t=>{let e=ty.parse(t);return e.alpha=0,ty.transform(e)}},tv=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,tx="number",tw="color",tb=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function tS(t){let e=t.toString(),i=[],s={color:[],number:[],var:[]},r=[],n=0,o=e.replace(tb,t=>(ty.test(t)?(s.color.push(n),r.push(tw),i.push(ty.parse(t))):t.startsWith("var(")?(s.var.push(n),r.push("var"),i.push(t)):(s.number.push(n),r.push(tx),i.push(parseFloat(t))),++n,"${}")).split("${}");return{values:i,split:o,indexes:s,types:r}}function tT(t){return tS(t).values}function tP(t){let{split:e,types:i}=tS(t),s=e.length;return t=>{let r="";for(let n=0;n<s;n++)if(r+=e[n],void 0!==t[n]){let e=i[n];e===tx?r+=tt(t[n]):e===tw?r+=ty.transform(t[n]):r+=t[n]}return r}}let tj=t=>"number"==typeof t?0:ty.test(t)?ty.getAnimatableNone(t):t,t_={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(te)?.length||0)+(t.match(tv)?.length||0)>0},parse:tT,createTransformer:tP,getAnimatableNone:function(t){let e=tT(t);return tP(t)(e.map(tj))}};function tA(t,e,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?t+(e-t)*6*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}function tC(t,e){return i=>i>0?e:t}let tE=(t,e,i)=>t+(e-t)*i,tM=(t,e,i)=>{let s=t*t,r=i*(e*e-s)+s;return r<0?0:Math.sqrt(r)},tD=[tl,ta,tg],tk=t=>tD.find(e=>e.test(t));function tB(t){let e=tk(t);if($(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`),!e)return!1;let i=e.parse(t);return e===tg&&(i=function({hue:t,saturation:e,lightness:i,alpha:s}){t/=360,i/=100;let r=0,n=0,o=0;if(e/=100){let s=i<.5?i*(1+e):i+e-i*e,a=2*i-s;r=tA(a,s,t+1/3),n=tA(a,s,t),o=tA(a,s,t-1/3)}else r=n=o=i;return{red:Math.round(255*r),green:Math.round(255*n),blue:Math.round(255*o),alpha:s}}(i)),i}let tV=(t,e)=>{let i=tB(t),s=tB(e);if(!i||!s)return tC(t,e);let r={...i};return t=>(r.red=tM(i.red,s.red,t),r.green=tM(i.green,s.green,t),r.blue=tM(i.blue,s.blue,t),r.alpha=tE(i.alpha,s.alpha,t),ta.transform(r))},tO=new Set(["none","hidden"]);function tR(t,e){return i=>tE(t,e,i)}function tF(t){return"number"==typeof t?tR:"string"==typeof t?Y(t)?tC:ty.test(t)?tV:tU:Array.isArray(t)?tL:"object"==typeof t?ty.test(t)?tV:tI:tC}function tL(t,e){let i=[...t],s=i.length,r=t.map((t,i)=>tF(t)(t,e[i]));return t=>{for(let e=0;e<s;e++)i[e]=r[e](t);return i}}function tI(t,e){let i={...t,...e},s={};for(let r in i)void 0!==t[r]&&void 0!==e[r]&&(s[r]=tF(t[r])(t[r],e[r]));return t=>{for(let e in s)i[e]=s[e](t);return i}}let tU=(t,e)=>{let i=t_.createTransformer(e),s=tS(t),r=tS(e);return s.indexes.var.length===r.indexes.var.length&&s.indexes.color.length===r.indexes.color.length&&s.indexes.number.length>=r.indexes.number.length?tO.has(t)&&!r.values.length||tO.has(e)&&!s.values.length?function(t,e){return tO.has(t)?i=>i<=0?t:e:i=>i>=1?e:t}(t,e):I(tL(function(t,e){let i=[],s={color:0,var:0,number:0};for(let r=0;r<e.values.length;r++){let n=e.types[r],o=t.indexes[n][s[n]],a=t.values[o]??0;i[r]=a,s[n]++}return i}(s,r),r.values),i):($(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),tC(t,e))};function tN(t,e,i){return"number"==typeof t&&"number"==typeof e&&"number"==typeof i?tE(t,e,i):tF(t)(t,e)}let tG=t=>{let e=({timestamp:e})=>t(e);return{start:(t=!0)=>y.update(e,t),stop:()=>v(e),now:()=>x.isProcessing?x.timestamp:C.now()}},tq=(t,e,i=10)=>{let s="",r=Math.max(Math.round(e/i),2);for(let e=0;e<r;e++)s+=Math.round(1e4*t(e/(r-1)))/1e4+", ";return`linear(${s.substring(0,s.length-2)})`};function t$(t){let e=0,i=t.next(e);for(;!i.done&&e<2e4;)e+=50,i=t.next(e);return e>=2e4?1/0:e}function tW(t,e,i){var s,r;let n=Math.max(e-5,0);return s=i-t(n),(r=e-n)?1e3/r*s:0}let tz={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function tH(t,e){return t*Math.sqrt(1-e*e)}let tX=["duration","bounce"],tY=["stiffness","damping","mass"];function tK(t,e){return e.some(e=>void 0!==t[e])}function tZ(t=tz.visualDuration,e=tz.bounce){let i,s="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t,{restSpeed:r,restDelta:n}=s,o=s.keyframes[0],a=s.keyframes[s.keyframes.length-1],l={done:!1,value:o},{stiffness:u,damping:h,mass:d,duration:c,velocity:p,isResolvedFromDuration:m}=function(t){let e={velocity:tz.velocity,stiffness:tz.stiffness,damping:tz.damping,mass:tz.mass,isResolvedFromDuration:!1,...t};if(!tK(t,tY)&&tK(t,tX))if(t.visualDuration){let i=2*Math.PI/(1.2*t.visualDuration),s=i*i,r=2*U(.05,1,1-(t.bounce||0))*Math.sqrt(s);e={...e,mass:tz.mass,stiffness:s,damping:r}}else{let i=function({duration:t=tz.duration,bounce:e=tz.bounce,velocity:i=tz.velocity,mass:s=tz.mass}){let r,n;$(t<=N(tz.maxDuration),"Spring duration must be 10 seconds or less");let o=1-e;o=U(tz.minDamping,tz.maxDamping,o),t=U(tz.minDuration,tz.maxDuration,G(t)),o<1?(r=e=>{let s=e*o,r=s*t;return .001-(s-i)/tH(e,o)*Math.exp(-r)},n=e=>{let s=e*o*t,n=Math.pow(o,2)*Math.pow(e,2)*t,a=Math.exp(-s),l=tH(Math.pow(e,2),o);return(s*i+i-n)*a*(-r(e)+.001>0?-1:1)/l}):(r=e=>-.001+Math.exp(-e*t)*((e-i)*t+1),n=e=>t*t*(i-e)*Math.exp(-e*t));let a=function(t,e,i){let s=i;for(let i=1;i<12;i++)s-=t(s)/e(s);return s}(r,n,5/t);if(t=N(t),isNaN(a))return{stiffness:tz.stiffness,damping:tz.damping,duration:t};{let e=Math.pow(a,2)*s;return{stiffness:e,damping:2*o*Math.sqrt(s*e),duration:t}}}(t);(e={...e,...i,mass:tz.mass}).isResolvedFromDuration=!0}return e}({...s,velocity:-G(s.velocity||0)}),f=p||0,g=h/(2*Math.sqrt(u*d)),y=a-o,v=G(Math.sqrt(u/d)),x=5>Math.abs(y);if(r||(r=x?tz.restSpeed.granular:tz.restSpeed.default),n||(n=x?tz.restDelta.granular:tz.restDelta.default),g<1){let t=tH(v,g);i=e=>a-Math.exp(-g*v*e)*((f+g*v*y)/t*Math.sin(t*e)+y*Math.cos(t*e))}else if(1===g)i=t=>a-Math.exp(-v*t)*(y+(f+v*y)*t);else{let t=v*Math.sqrt(g*g-1);i=e=>{let i=Math.exp(-g*v*e),s=Math.min(t*e,300);return a-i*((f+g*v*y)*Math.sinh(s)+t*y*Math.cosh(s))/t}}let w={calculatedDuration:m&&c||null,next:t=>{let e=i(t);if(m)l.done=t>=c;else{let s=0===t?f:0;g<1&&(s=0===t?N(f):tW(i,t,e));let o=Math.abs(a-e)<=n;l.done=Math.abs(s)<=r&&o}return l.value=l.done?a:e,l},toString:()=>{let t=Math.min(t$(w),2e4),e=tq(e=>w.next(t*e).value,t,30);return t+"ms "+e},toTransition:()=>{}};return w}function tJ({keyframes:t,velocity:e=0,power:i=.8,timeConstant:s=325,bounceDamping:r=10,bounceStiffness:n=500,modifyTarget:o,min:a,max:l,restDelta:u=.5,restSpeed:h}){let d,c,p=t[0],m={done:!1,value:p},f=t=>void 0!==a&&t<a||void 0!==l&&t>l,g=t=>void 0===a?l:void 0===l||Math.abs(a-t)<Math.abs(l-t)?a:l,y=i*e,v=p+y,x=void 0===o?v:o(v);x!==v&&(y=x-p);let w=t=>-y*Math.exp(-t/s),b=t=>x+w(t),S=t=>{let e=w(t),i=b(t);m.done=Math.abs(e)<=u,m.value=m.done?x:i},T=t=>{f(m.value)&&(d=t,c=tZ({keyframes:[m.value,g(m.value)],velocity:tW(b,t,m.value),damping:r,stiffness:n,restDelta:u,restSpeed:h}))};return T(0),{calculatedDuration:null,next:t=>{let e=!1;return(c||void 0!==d||(e=!0,S(t),T(t)),void 0!==d&&t>=d)?c.next(t-d):(e||S(t),m)}}}tZ.applyToOptions=t=>{let e=function(t,e=100,i){let s=i({...t,keyframes:[0,e]}),r=Math.min(t$(s),2e4);return{type:"keyframes",ease:t=>s.next(r*t).value/e,duration:G(r)}}(t,100,tZ);return t.ease=e.ease,t.duration=N(e.duration),t.type="keyframes",t};let tQ=(t,e,i)=>(((1-3*i+3*e)*t+(3*i-6*e))*t+3*e)*t;function t0(t,e,i,s){if(t===e&&i===s)return c;let r=e=>(function(t,e,i,s,r){let n,o,a=0;do(n=tQ(o=e+(i-e)/2,s,r)-t)>0?i=o:e=o;while(Math.abs(n)>1e-7&&++a<12);return o})(e,0,1,t,i);return t=>0===t||1===t?t:tQ(r(t),e,s)}let t1=t0(.42,0,1,1),t5=t0(0,0,.58,1),t2=t0(.42,0,.58,1),t3=t=>Array.isArray(t)&&"number"!=typeof t[0],t4=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,t9=t=>e=>1-t(1-e),t7=t0(.33,1.53,.69,.99),t6=t9(t7),t8=t4(t6),et=t=>(t*=2)<1?.5*t6(t):.5*(2-Math.pow(2,-10*(t-1))),ee=t=>1-Math.sin(Math.acos(t)),ei=t9(ee),es=t4(ee),er=t=>Array.isArray(t)&&"number"==typeof t[0],en={linear:c,easeIn:t1,easeInOut:t2,easeOut:t5,circIn:ee,circInOut:es,circOut:ei,backIn:t6,backInOut:t8,backOut:t7,anticipate:et},eo=t=>"string"==typeof t,ea=t=>{if(er(t)){W(4===t.length,"Cubic bezier arrays must contain four numerical values.");let[e,i,s,r]=t;return t0(e,i,s,r)}return eo(t)?(W(void 0!==en[t],`Invalid easing type '${t}'`),en[t]):t},el=(t,e,i)=>{let s=e-t;return 0===s?1:(i-t)/s};function eu({duration:t=300,keyframes:e,times:i,ease:s="easeInOut"}){var r;let n=t3(s)?s.map(ea):ea(s),o={done:!1,value:e[0]},a=function(t,e,{clamp:i=!0,ease:s,mixer:r}={}){let n=t.length;if(W(n===e.length,"Both input and output ranges must be the same length"),1===n)return()=>e[0];if(2===n&&e[0]===e[1])return()=>e[1];let o=t[0]===t[1];t[0]>t[n-1]&&(t=[...t].reverse(),e=[...e].reverse());let a=function(t,e,i){let s=[],r=i||p.mix||tN,n=t.length-1;for(let i=0;i<n;i++){let n=r(t[i],t[i+1]);e&&(n=I(Array.isArray(e)?e[i]||c:e,n)),s.push(n)}return s}(e,s,r),l=a.length,u=i=>{if(o&&i<t[0])return e[0];let s=0;if(l>1)for(;s<t.length-2&&!(i<t[s+1]);s++);let r=el(t[s],t[s+1],i);return a[s](r)};return i?e=>u(U(t[0],t[n-1],e)):u}((r=i&&i.length===e.length?i:function(t){let e=[0];return!function(t,e){let i=t[t.length-1];for(let s=1;s<=e;s++){let r=el(0,e,s);t.push(tE(i,1,r))}}(e,t.length-1),e}(e),r.map(e=>e*t)),e,{ease:Array.isArray(n)?n:e.map(()=>n||t2).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(o.value=a(e),o.done=e>=t,o)}}let eh=t=>null!==t;function ed(t,{repeat:e,repeatType:i="loop"},s,r=1){let n=t.filter(eh),o=r<0||e&&"loop"!==i&&e%2==1?0:n.length-1;return o&&void 0!==s?s:n[o]}let ec={decay:tJ,inertia:tJ,tween:eu,keyframes:eu,spring:tZ};function ep(t){"string"==typeof t.type&&(t.type=ec[t.type])}class em{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}let ef=t=>t/100;class eg extends em{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:t}=this.options;t&&t.updatedAt!==C.now()&&this.tick(C.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},q.mainThread++,this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){let{options:t}=this;ep(t);let{type:e=eu,repeat:i=0,repeatDelay:s=0,repeatType:r,velocity:n=0}=t,{keyframes:o}=t,a=e||eu;a!==eu&&"number"!=typeof o[0]&&(this.mixKeyframes=I(ef,tN(o[0],o[1])),o=[0,100]);let l=a({...t,keyframes:o});"mirror"===r&&(this.mirroredGenerator=a({...t,keyframes:[...o].reverse(),velocity:-n})),null===l.calculatedDuration&&(l.calculatedDuration=t$(l));let{calculatedDuration:u}=l;this.calculatedDuration=u,this.resolvedDuration=u+s,this.totalDuration=this.resolvedDuration*(i+1)-s,this.generator=l}updateTime(t){let e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,e=!1){let{generator:i,totalDuration:s,mixKeyframes:r,mirroredGenerator:n,resolvedDuration:o,calculatedDuration:a}=this;if(null===this.startTime)return i.next(0);let{delay:l=0,keyframes:u,repeat:h,repeatType:d,repeatDelay:c,type:p,onUpdate:m,finalKeyframe:f}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-s/this.speed,this.startTime)),e?this.currentTime=t:this.updateTime(t);let g=this.currentTime-l*(this.playbackSpeed>=0?1:-1),y=this.playbackSpeed>=0?g<0:g>s;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=s);let v=this.currentTime,x=i;if(h){let t=Math.min(this.currentTime,s)/o,e=Math.floor(t),i=t%1;!i&&t>=1&&(i=1),1===i&&e--,(e=Math.min(e,h+1))%2&&("reverse"===d?(i=1-i,c&&(i-=c/o)):"mirror"===d&&(x=n)),v=U(0,1,i)*o}let w=y?{done:!1,value:u[0]}:x.next(v);r&&(w.value=r(w.value));let{done:b}=w;y||null===a||(b=this.playbackSpeed>=0?this.currentTime>=s:this.currentTime<=0);let S=null===this.holdTime&&("finished"===this.state||"running"===this.state&&b);return S&&p!==tJ&&(w.value=ed(u,this.options,f,this.speed)),m&&m(w.value),S&&this.finish(),w}then(t,e){return this.finished.then(t,e)}get duration(){return G(this.calculatedDuration)}get time(){return G(this.currentTime)}set time(t){t=N(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(C.now());let e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=G(this.currentTime))}play(){if(this.isStopped)return;let{driver:t=tG,startTime:e}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),this.options.onPlay?.();let i=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=i):null!==this.holdTime?this.startTime=i-this.holdTime:this.startTime||(this.startTime=e??i),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(C.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,q.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),t.observe(this)}}let ey=t=>180*t/Math.PI,ev=t=>ew(ey(Math.atan2(t[1],t[0]))),ex={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:ev,rotateZ:ev,skewX:t=>ey(Math.atan(t[1])),skewY:t=>ey(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},ew=t=>((t%=360)<0&&(t+=360),t),eb=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),eS=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),eT={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:eb,scaleY:eS,scale:t=>(eb(t)+eS(t))/2,rotateX:t=>ew(ey(Math.atan2(t[6],t[5]))),rotateY:t=>ew(ey(Math.atan2(-t[2],t[0]))),rotateZ:ev,rotate:ev,skewX:t=>ey(Math.atan(t[4])),skewY:t=>ey(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function eP(t){return+!!t.includes("scale")}function ej(t,e){let i,s;if(!t||"none"===t)return eP(e);let r=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(r)i=eT,s=r;else{let e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=ex,s=e}if(!s)return eP(e);let n=i[e],o=s[1].split(",").map(eA);return"function"==typeof n?n(o):o[n]}let e_=(t,e)=>{let{transform:i="none"}=getComputedStyle(t);return ej(i,e)};function eA(t){return parseFloat(t.trim())}let eC=t=>t===Z||t===tc,eE=new Set(["x","y","z"]),eM=b.filter(t=>!eE.has(t)),eD={width:({x:t},{paddingLeft:e="0",paddingRight:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),height:({y:t},{paddingTop:e="0",paddingBottom:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>ej(e,"x"),y:(t,{transform:e})=>ej(e,"y")};eD.translateX=eD.x,eD.translateY=eD.y;let ek=new Set,eB=!1,eV=!1,eO=!1;function eR(){if(eV){let t=Array.from(ek).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),i=new Map;e.forEach(t=>{let e=function(t){let e=[];return eM.forEach(i=>{let s=t.getValue(i);void 0!==s&&(e.push([i,s.get()]),s.set(+!!i.startsWith("scale")))}),e}(t);e.length&&(i.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();let e=i.get(t);e&&e.forEach(([e,i])=>{t.getValue(e)?.set(i)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}eV=!1,eB=!1,ek.forEach(t=>t.complete(eO)),ek.clear()}function eF(){ek.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(eV=!0)})}class eL{constructor(t,e,i,s,r,n=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=i,this.motionValue=s,this.element=r,this.isAsync=n}scheduleResolve(){this.state="scheduled",this.isAsync?(ek.add(this),eB||(eB=!0,y.read(eF),y.resolveKeyframes(eR))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:t,name:e,element:i,motionValue:s}=this;if(null===t[0]){let r=s?.get(),n=t[t.length-1];if(void 0!==r)t[0]=r;else if(i&&e){let s=i.readValue(e,n);null!=s&&(t[0]=s)}void 0===t[0]&&(t[0]=n),s&&void 0===r&&s.set(t[0])}for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),ek.delete(this)}cancel(){"scheduled"===this.state&&(ek.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}let eI=t=>t.startsWith("--");function eU(t){let e;return()=>(void 0===e&&(e=t()),e)}let eN=eU(()=>void 0!==window.ScrollTimeline),eG={},eq=function(t,e){let i=eU(t);return()=>eG[e]??i()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),e$=([t,e,i,s])=>`cubic-bezier(${t}, ${e}, ${i}, ${s})`,eW={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:e$([0,.65,.55,1]),circOut:e$([.55,0,1,.45]),backIn:e$([.31,.01,.66,-.59]),backOut:e$([.33,1.53,.69,.99])};function ez(t){return"function"==typeof t&&"applyToOptions"in t}class eH extends em{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;let{element:e,name:i,keyframes:s,pseudoElement:r,allowFlatten:n=!1,finalKeyframe:o,onComplete:a}=t;this.isPseudoElement=!!r,this.allowFlatten=n,this.options=t,W("string"!=typeof t.type,'animateMini doesn\'t support "type" as a string. Did you mean to import { spring } from "motion"?');let l=function({type:t,...e}){return ez(t)&&eq()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}(t);this.animation=function(t,e,i,{delay:s=0,duration:r=300,repeat:n=0,repeatType:o="loop",ease:a="easeOut",times:l}={},u){let h={[e]:i};l&&(h.offset=l);let d=function t(e,i){if(e)return"function"==typeof e?eq()?tq(e,i):"ease-out":er(e)?e$(e):Array.isArray(e)?e.map(e=>t(e,i)||eW.easeOut):eW[e]}(a,r);Array.isArray(d)&&(h.easing=d),f.value&&q.waapi++;let c={delay:s,duration:r,easing:Array.isArray(d)?"linear":d,fill:"both",iterations:n+1,direction:"reverse"===o?"alternate":"normal"};u&&(c.pseudoElement=u);let p=t.animate(h,c);return f.value&&p.finished.finally(()=>{q.waapi--}),p}(e,i,s,l,r),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!r){let t=ed(s,this.options,o,this.speed);this.updateMotionValue?this.updateMotionValue(t):function(t,e,i){eI(e)?t.style.setProperty(e,i):t.style[e]=i}(e,i,t),this.animation.cancel()}a?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){return G(Number(this.animation.effect?.getComputedTiming?.().duration||0))}get time(){return G(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=N(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&eN())?(this.animation.timeline=t,c):e(this)}}let eX={anticipate:et,backInOut:t8,circInOut:es};class eY extends eH{constructor(t){!function(t){"string"==typeof t.ease&&t.ease in eX&&(t.ease=eX[t.ease])}(t),ep(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){let{motionValue:e,onUpdate:i,onComplete:s,element:r,...n}=this.options;if(!e)return;if(void 0!==t)return void e.set(t);let o=new eg({...n,autoplay:!1}),a=N(this.finishedTime??this.time);e.setWithVelocity(o.sample(a-10).value,o.sample(a).value,10),o.stop()}}let eK=(t,e)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(t_.test(t)||"0"===t)&&!t.startsWith("url("));function eZ(t){return"object"==typeof t&&null!==t}function eJ(t){return eZ(t)&&"offsetHeight"in t}let eQ=new Set(["opacity","clipPath","filter","transform"]),e0=eU(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class e1 extends em{constructor({autoplay:t=!0,delay:e=0,type:i="keyframes",repeat:s=0,repeatDelay:r=0,repeatType:n="loop",keyframes:o,name:a,motionValue:l,element:u,...h}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=C.now();let d={autoplay:t,delay:e,type:i,repeat:s,repeatDelay:r,repeatType:n,name:a,motionValue:l,element:u,...h},c=u?.KeyframeResolver||eL;this.keyframeResolver=new c(o,(t,e,i)=>this.onKeyframesResolved(t,e,d,!i),a,l,u),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,e,i,s){this.keyframeResolver=void 0;let{name:r,type:n,velocity:o,delay:a,isHandoff:l,onUpdate:u}=i;this.resolvedAt=C.now(),!function(t,e,i,s){let r=t[0];if(null===r)return!1;if("display"===e||"visibility"===e)return!0;let n=t[t.length-1],o=eK(r,e),a=eK(n,e);return $(o===a,`You are trying to animate ${e} from "${r}" to "${n}". ${r} is not an animatable value - to enable this animation set ${r} to a value animatable to ${n} via the \`style\` property.`),!!o&&!!a&&(function(t){let e=t[0];if(1===t.length)return!0;for(let i=0;i<t.length;i++)if(t[i]!==e)return!0}(t)||("spring"===i||ez(i))&&s)}(t,r,n,o)&&((p.instantAnimations||!a)&&u?.(ed(t,i,e)),t[0]=t[t.length-1],i.duration=0,i.repeat=0);let h={startTime:s?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:e,...i,keyframes:t},d=!l&&function(t){let{motionValue:e,name:i,repeatDelay:s,repeatType:r,damping:n,type:o}=t;if(!eJ(e?.owner?.current))return!1;let{onUpdate:a,transformTemplate:l}=e.owner.getProps();return e0()&&i&&eQ.has(i)&&("transform"!==i||!l)&&!a&&!s&&"mirror"!==r&&0!==n&&"inertia"!==o}(h)?new eY({...h,element:h.motionValue.owner.current}):new eg(h);d.finished.then(()=>this.notifyFinished()).catch(c),this.pendingTimeline&&(this.stopTimeline=d.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=d}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),eO=!0,eF(),eR(),eO=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let e5=t=>null!==t,e2={type:"spring",stiffness:500,damping:25,restSpeed:10},e3=t=>({type:"spring",stiffness:550,damping:0===t?2*Math.sqrt(550):30,restSpeed:10}),e4={type:"keyframes",duration:.8},e9={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},e7=(t,{keyframes:e})=>e.length>2?e4:S.has(t)?t.startsWith("scale")?e3(e[1]):e2:e9,e6=(t,e,i,s={},r,n)=>o=>{let a=d(s,t)||{},l=a.delay||s.delay||0,{elapsed:u=0}=s;u-=N(l);let h={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-u,onUpdate:t=>{e.set(t),a.onUpdate&&a.onUpdate(t)},onComplete:()=>{o(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:n?void 0:r};!function({when:t,delay:e,delayChildren:i,staggerChildren:s,staggerDirection:r,repeat:n,repeatType:o,repeatDelay:a,from:l,elapsed:u,...h}){return!!Object.keys(h).length}(a)&&Object.assign(h,e7(t,h)),h.duration&&(h.duration=N(h.duration)),h.repeatDelay&&(h.repeatDelay=N(h.repeatDelay)),void 0!==h.from&&(h.keyframes[0]=h.from);let c=!1;if(!1!==h.type&&(0!==h.duration||h.repeatDelay)||(h.duration=0,0===h.delay&&(c=!0)),(p.instantAnimations||p.skipAnimations)&&(c=!0,h.duration=0,h.delay=0),h.allowFlatten=!a.type&&!a.ease,c&&!n&&void 0!==e.get()){let t=function(t,{repeat:e,repeatType:i="loop"},s){let r=t.filter(e5),n=e&&"loop"!==i&&e%2==1?0:r.length-1;return r[n]}(h.keyframes,a);if(void 0!==t)return void y.update(()=>{h.onUpdate(t),h.onComplete()})}return a.isSync?new eg(h):new e1(h)};function e8(t,e,{delay:i=0,transitionOverride:s,type:r}={}){let{transition:n=t.getDefaultTransition(),transitionEnd:o,...a}=e;s&&(n=s);let l=[],u=r&&t.animationState&&t.animationState.getState()[r];for(let e in a){let s=t.getValue(e,t.latestValues[e]??null),r=a[e];if(void 0===r||u&&function({protectedKeys:t,needsAnimating:e},i){let s=t.hasOwnProperty(i)&&!0!==e[i];return e[i]=!1,s}(u,e))continue;let o={delay:i,...d(n||{},e)},h=s.get();if(void 0!==h&&!s.isAnimating&&!Array.isArray(r)&&r===h&&!o.velocity)continue;let c=!1;if(window.MotionHandoffAnimation){let i=t.props[F];if(i){let t=window.MotionHandoffAnimation(i,e,y);null!==t&&(o.startTime=t,c=!0)}}O(t,e),s.start(e6(e,s,r,t.shouldReduceMotion&&T.has(e)?{type:!1}:o,t,c));let p=s.animation;p&&l.push(p)}return o&&Promise.all(l).then(()=>{y.update(()=>{o&&function(t,e){let{transitionEnd:i={},transition:s={},...r}=h(t,e)||{};for(let e in r={...r,...i}){var n;let i=B(n=r[e])?n[n.length-1]||0:n;t.hasValue(e)?t.getValue(e).set(i):t.addValue(e,k(i))}}(t,o)})}),l}function it(t,e,i={}){let s=h(t,e,"exit"===i.type?t.presenceContext?.custom:void 0),{transition:r=t.getDefaultTransition()||{}}=s||{};i.transitionOverride&&(r=i.transitionOverride);let n=s?()=>Promise.all(e8(t,s,i)):()=>Promise.resolve(),o=t.variantChildren&&t.variantChildren.size?(s=0)=>{let{delayChildren:n=0,staggerChildren:o,staggerDirection:a}=r;return function(t,e,i=0,s=0,r=1,n){let o=[],a=(t.variantChildren.size-1)*s,l=1===r?(t=0)=>t*s:(t=0)=>a-t*s;return Array.from(t.variantChildren).sort(ie).forEach((t,s)=>{t.notify("AnimationStart",e),o.push(it(t,e,{...n,delay:i+l(s)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(o)}(t,e,n+s,o,a,i)}:()=>Promise.resolve(),{when:a}=r;if(!a)return Promise.all([n(),o(i.delay)]);{let[t,e]="beforeChildren"===a?[n,o]:[o,n];return t().then(()=>e())}}function ie(t,e){return t.sortNodePosition(e)}function ii(t,e){if(!Array.isArray(e))return!1;let i=e.length;if(i!==t.length)return!1;for(let s=0;s<i;s++)if(e[s]!==t[s])return!1;return!0}function is(t){return"string"==typeof t||Array.isArray(t)}let ir=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],io=["initial",...ir],ia=io.length,il=[...ir].reverse(),iu=ir.length;function ih(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function id(){return{animate:ih(!0),whileInView:ih(),whileHover:ih(),whileTap:ih(),whileDrag:ih(),whileFocus:ih(),exit:ih()}}class ic{constructor(t){this.isMounted=!1,this.node=t}update(){}}class ip extends ic{constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(({animation:e,options:i})=>(function(t,e,i={}){let s;if(t.notify("AnimationStart",e),Array.isArray(e))s=Promise.all(e.map(e=>it(t,e,i)));else if("string"==typeof e)s=it(t,e,i);else{let r="function"==typeof e?h(t,e,i.custom):e;s=Promise.all(e8(t,r,i))}return s.then(()=>{t.notify("AnimationComplete",e)})})(t,e,i))),i=id(),s=!0,r=e=>(i,s)=>{let r=h(t,s,"exit"===e?t.presenceContext?.custom:void 0);if(r){let{transition:t,transitionEnd:e,...s}=r;i={...i,...s,...e}}return i};function n(n){let{props:o}=t,l=function t(e){if(!e)return;if(!e.isControllingVariants){let i=e.parent&&t(e.parent)||{};return void 0!==e.props.initial&&(i.initial=e.props.initial),i}let i={};for(let t=0;t<ia;t++){let s=io[t],r=e.props[s];(is(r)||!1===r)&&(i[s]=r)}return i}(t.parent)||{},u=[],d=new Set,c={},p=1/0;for(let e=0;e<iu;e++){var m,f;let h=il[e],g=i[h],y=void 0!==o[h]?o[h]:l[h],v=is(y),x=h===n?g.isActive:null;!1===x&&(p=e);let w=y===l[h]&&y!==o[h]&&v;if(w&&s&&t.manuallyAnimateOnMount&&(w=!1),g.protectedKeys={...c},!g.isActive&&null===x||!y&&!g.prevProp||a(y)||"boolean"==typeof y)continue;let b=(m=g.prevProp,"string"==typeof(f=y)?f!==m:!!Array.isArray(f)&&!ii(f,m)),S=b||h===n&&g.isActive&&!w&&v||e>p&&v,T=!1,P=Array.isArray(y)?y:[y],j=P.reduce(r(h),{});!1===x&&(j={});let{prevResolvedValues:_={}}=g,A={..._,...j},C=e=>{S=!0,d.has(e)&&(T=!0,d.delete(e)),g.needsAnimating[e]=!0;let i=t.getValue(e);i&&(i.liveStyle=!1)};for(let t in A){let e=j[t],i=_[t];if(c.hasOwnProperty(t))continue;let s=!1;(B(e)&&B(i)?ii(e,i):e===i)?void 0!==e&&d.has(t)?C(t):g.protectedKeys[t]=!0:null!=e?C(t):d.add(t)}g.prevProp=y,g.prevResolvedValues=j,g.isActive&&(c={...c,...j}),s&&t.blockInitialAnimation&&(S=!1);let E=!(w&&b)||T;S&&E&&u.push(...P.map(t=>({animation:t,options:{type:h}})))}if(d.size){let e={};if("boolean"!=typeof o.initial){let i=h(t,Array.isArray(o.initial)?o.initial[0]:o.initial);i&&i.transition&&(e.transition=i.transition)}d.forEach(i=>{let s=t.getBaseTarget(i),r=t.getValue(i);r&&(r.liveStyle=!0),e[i]=s??null}),u.push({animation:e})}let g=!!u.length;return s&&(!1===o.initial||o.initial===o.animate)&&!t.manuallyAnimateOnMount&&(g=!1),s=!1,g?e(u):Promise.resolve()}return{animateChanges:n,setActive:function(e,s){if(i[e].isActive===s)return Promise.resolve();t.variantChildren?.forEach(t=>t.animationState?.setActive(e,s)),i[e].isActive=s;let r=n(e);for(let t in i)i[t].protectedKeys={};return r},setAnimateFunction:function(i){e=i(t)},getState:()=>i,reset:()=>{i=id(),s=!0}}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();a(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let im=0;class ig extends ic{constructor(){super(...arguments),this.id=im++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;let s=this.node.animationState.setActive("exit",!t);e&&!t&&s.then(()=>{e(this.id)})}mount(){let{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}let iy={x:!1,y:!1};function iv(t,e,i,s={passive:!0}){return t.addEventListener(e,i,s),()=>t.removeEventListener(e,i)}let ix=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary;function iw(t){return{point:{x:t.pageX,y:t.pageY}}}let ib=t=>e=>ix(e)&&t(e,iw(e));function iS(t,e,i,s){return iv(t,e,ib(i),s)}function iT({top:t,left:e,right:i,bottom:s}){return{x:{min:e,max:i},y:{min:t,max:s}}}function iP(t){return t.max-t.min}function ij(t,e,i,s=.5){t.origin=s,t.originPoint=tE(e.min,e.max,t.origin),t.scale=iP(i)/iP(e),t.translate=tE(i.min,i.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function i_(t,e,i,s){ij(t.x,e.x,i.x,s?s.originX:void 0),ij(t.y,e.y,i.y,s?s.originY:void 0)}function iA(t,e,i){t.min=i.min+e.min,t.max=t.min+iP(e)}function iC(t,e,i){t.min=e.min-i.min,t.max=t.min+iP(e)}function iE(t,e,i){iC(t.x,e.x,i.x),iC(t.y,e.y,i.y)}let iM=()=>({translate:0,scale:1,origin:0,originPoint:0}),iD=()=>({x:iM(),y:iM()}),ik=()=>({min:0,max:0}),iB=()=>({x:ik(),y:ik()});function iV(t){return[t("x"),t("y")]}function iO(t){return void 0===t||1===t}function iR({scale:t,scaleX:e,scaleY:i}){return!iO(t)||!iO(e)||!iO(i)}function iF(t){return iR(t)||iL(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function iL(t){var e,i;return(e=t.x)&&"0%"!==e||(i=t.y)&&"0%"!==i}function iI(t,e,i,s,r){return void 0!==r&&(t=s+r*(t-s)),s+i*(t-s)+e}function iU(t,e=0,i=1,s,r){t.min=iI(t.min,e,i,s,r),t.max=iI(t.max,e,i,s,r)}function iN(t,{x:e,y:i}){iU(t.x,e.translate,e.scale,e.originPoint),iU(t.y,i.translate,i.scale,i.originPoint)}function iG(t,e){t.min=t.min+e,t.max=t.max+e}function iq(t,e,i,s,r=.5){let n=tE(t.min,t.max,r);iU(t,e,i,n,s)}function i$(t,e){iq(t.x,e.x,e.scaleX,e.scale,e.originX),iq(t.y,e.y,e.scaleY,e.scale,e.originY)}function iW(t,e){return iT(function(t,e){if(!e)return t;let i=e({x:t.left,y:t.top}),s=e({x:t.right,y:t.bottom});return{top:i.y,left:i.x,bottom:s.y,right:s.x}}(t.getBoundingClientRect(),e))}let iz=({current:t})=>t?t.ownerDocument.defaultView:null;function iH(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}let iX=(t,e)=>Math.abs(t-e);class iY{constructor(t,e,{transformPagePoint:i,contextWindow:s,dragSnapToOrigin:r=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let t=iJ(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,i=function(t,e){return Math.sqrt(iX(t.x,e.x)**2+iX(t.y,e.y)**2)}(t.offset,{x:0,y:0})>=3;if(!e&&!i)return;let{point:s}=t,{timestamp:r}=x;this.history.push({...s,timestamp:r});let{onStart:n,onMove:o}=this.handlers;e||(n&&n(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),o&&o(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=iK(e,this.transformPagePoint),y.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:i,onSessionEnd:s,resumeAnimation:r}=this.handlers;if(this.dragSnapToOrigin&&r&&r(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let n=iJ("pointercancel"===t.type?this.lastMoveEventInfo:iK(e,this.transformPagePoint),this.history);this.startEvent&&i&&i(t,n),s&&s(t,n)},!ix(t))return;this.dragSnapToOrigin=r,this.handlers=e,this.transformPagePoint=i,this.contextWindow=s||window;let n=iK(iw(t),this.transformPagePoint),{point:o}=n,{timestamp:a}=x;this.history=[{...o,timestamp:a}];let{onSessionStart:l}=e;l&&l(t,iJ(n,this.history)),this.removeListeners=I(iS(this.contextWindow,"pointermove",this.handlePointerMove),iS(this.contextWindow,"pointerup",this.handlePointerUp),iS(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),v(this.updatePoint)}}function iK(t,e){return e?{point:e(t.point)}:t}function iZ(t,e){return{x:t.x-e.x,y:t.y-e.y}}function iJ({point:t},e){return{point:t,delta:iZ(t,iQ(e)),offset:iZ(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let i=t.length-1,s=null,r=iQ(t);for(;i>=0&&(s=t[i],!(r.timestamp-s.timestamp>N(.1)));)i--;if(!s)return{x:0,y:0};let n=G(r.timestamp-s.timestamp);if(0===n)return{x:0,y:0};let o={x:(r.x-s.x)/n,y:(r.y-s.y)/n};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}(e,.1)}}function iQ(t){return t[t.length-1]}function i0(t,e,i){return{min:void 0!==e?t.min+e:void 0,max:void 0!==i?t.max+i-(t.max-t.min):void 0}}function i1(t,e){let i=e.min-t.min,s=e.max-t.max;return e.max-e.min<t.max-t.min&&([i,s]=[s,i]),{min:i,max:s}}function i5(t,e,i){return{min:i2(t,e),max:i2(t,i)}}function i2(t,e){return"number"==typeof t?t:t[e]||0}let i3=new WeakMap;class i4{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=iB(),this.visualElement=t}start(t,{snapToCursor:e=!1}={}){let{presenceContext:i}=this.visualElement;if(i&&!1===i.isPresent)return;let{dragSnapToOrigin:s}=this.getProps();this.panSession=new iY(t,{onSessionStart:t=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(iw(t).point)},onStart:(t,e)=>{let{drag:i,dragPropagation:s,onDragStart:r}=this.getProps();if(i&&!s&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(t){if("x"===t||"y"===t)if(iy[t])return null;else return iy[t]=!0,()=>{iy[t]=!1};return iy.x||iy.y?null:(iy.x=iy.y=!0,()=>{iy.x=iy.y=!1})}(i),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),iV(t=>{let e=this.getAxisMotionValue(t).get()||0;if(td.test(e)){let{projection:i}=this.visualElement;if(i&&i.layout){let s=i.layout.layoutBox[t];s&&(e=iP(s)*(parseFloat(e)/100))}}this.originPoint[t]=e}),r&&y.postRender(()=>r(t,e)),O(this.visualElement,"transform");let{animationState:n}=this.visualElement;n&&n.setActive("whileDrag",!0)},onMove:(t,e)=>{let{dragPropagation:i,dragDirectionLock:s,onDirectionLock:r,onDrag:n}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:o}=e;if(s&&null===this.currentDirection){this.currentDirection=function(t,e=10){let i=null;return Math.abs(t.y)>e?i="y":Math.abs(t.x)>e&&(i="x"),i}(o),null!==this.currentDirection&&r&&r(this.currentDirection);return}this.updateAxis("x",e.point,o),this.updateAxis("y",e.point,o),this.visualElement.render(),n&&n(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>iV(t=>"paused"===this.getAnimationState(t)&&this.getAxisMotionValue(t).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:s,contextWindow:iz(this.visualElement)})}stop(t,e){let i=this.isDragging;if(this.cancel(),!i)return;let{velocity:s}=e;this.startAnimation(s);let{onDragEnd:r}=this.getProps();r&&y.postRender(()=>r(t,e))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,i){let{drag:s}=this.getProps();if(!i||!i9(t,s,this.currentDirection))return;let r=this.getAxisMotionValue(t),n=this.originPoint[t]+i[t];this.constraints&&this.constraints[t]&&(n=function(t,{min:e,max:i},s){return void 0!==e&&t<e?t=s?tE(e,t,s.min):Math.max(t,e):void 0!==i&&t>i&&(t=s?tE(i,t,s.max):Math.min(t,i)),t}(n,this.constraints[t],this.elastic[t])),r.set(n)}resolveConstraints(){let{dragConstraints:t,dragElastic:e}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,s=this.constraints;t&&iH(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&i?this.constraints=function(t,{top:e,left:i,bottom:s,right:r}){return{x:i0(t.x,i,r),y:i0(t.y,e,s)}}(i.layoutBox,t):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:i5(t,"left","right"),y:i5(t,"top","bottom")}}(e),s!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&iV(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let i={};return void 0!==e.min&&(i.min=e.min-t.min),void 0!==e.max&&(i.max=e.max-t.min),i}(i.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:i}=this.getProps();if(!e||!iH(e))return!1;let s=e.current;W(null!==s,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:r}=this.visualElement;if(!r||!r.layout)return!1;let n=function(t,e,i){let s=iW(t,i),{scroll:r}=e;return r&&(iG(s.x,r.offset.x),iG(s.y,r.offset.y)),s}(s,r.root,this.visualElement.getTransformPagePoint()),o=(t=r.layout.layoutBox,{x:i1(t.x,n.x),y:i1(t.y,n.y)});if(i){let t=i(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(o));this.hasMutatedConstraints=!!t,t&&(o=iT(t))}return o}startAnimation(t){let{drag:e,dragMomentum:i,dragElastic:s,dragTransition:r,dragSnapToOrigin:n,onDragTransitionEnd:o}=this.getProps(),a=this.constraints||{};return Promise.all(iV(o=>{if(!i9(o,e,this.currentDirection))return;let l=a&&a[o]||{};n&&(l={min:0,max:0});let u={type:"inertia",velocity:i?t[o]:0,bounceStiffness:s?200:1e6,bounceDamping:s?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...r,...l};return this.startAxisValueAnimation(o,u)})).then(o)}startAxisValueAnimation(t,e){let i=this.getAxisMotionValue(t);return O(this.visualElement,t),i.start(e6(t,i,0,e,this.visualElement,!1))}stopAnimation(){iV(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){iV(t=>this.getAxisMotionValue(t).animation?.pause())}getAnimationState(t){return this.getAxisMotionValue(t).animation?.state}getAxisMotionValue(t){let e=`_drag${t.toUpperCase()}`,i=this.visualElement.getProps();return i[e]||this.visualElement.getValue(t,(i.initial?i.initial[t]:void 0)||0)}snapToCursor(t){iV(e=>{let{drag:i}=this.getProps();if(!i9(e,i,this.currentDirection))return;let{projection:s}=this.visualElement,r=this.getAxisMotionValue(e);if(s&&s.layout){let{min:i,max:n}=s.layout.layoutBox[e];r.set(t[e]-tE(i,n,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:i}=this.visualElement;if(!iH(e)||!i||!this.constraints)return;this.stopAnimation();let s={x:0,y:0};iV(t=>{let e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){let i=e.get();s[t]=function(t,e){let i=.5,s=iP(t),r=iP(e);return r>s?i=el(e.min,e.max-s,t.min):s>r&&(i=el(t.min,t.max-r,e.min)),U(0,1,i)}({min:i,max:i},this.constraints[t])}});let{transformTemplate:r}=this.visualElement.getProps();this.visualElement.current.style.transform=r?r({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),iV(e=>{if(!i9(e,t,null))return;let i=this.getAxisMotionValue(e),{min:r,max:n}=this.constraints[e];i.set(tE(r,n,s[e]))})}addListeners(){if(!this.visualElement.current)return;i3.set(this.visualElement,this);let t=iS(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:i=!0}=this.getProps();e&&i&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();iH(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,s=i.addEventListener("measure",e);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),y.read(e);let r=iv(window,"resize",()=>this.scalePositionWithinConstraints()),n=i.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(iV(e=>{let i=this.getAxisMotionValue(e);i&&(this.originPoint[e]+=t[e].translate,i.set(i.get()+t[e].translate))}),this.visualElement.render())});return()=>{r(),t(),s(),n&&n()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:i=!1,dragPropagation:s=!1,dragConstraints:r=!1,dragElastic:n=.35,dragMomentum:o=!0}=t;return{...t,drag:e,dragDirectionLock:i,dragPropagation:s,dragConstraints:r,dragElastic:n,dragMomentum:o}}}function i9(t,e,i){return(!0===e||e===t)&&(null===i||i===t)}class i7 extends ic{constructor(t){super(t),this.removeGroupControls=c,this.removeListeners=c,this.controls=new i4(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||c}unmount(){this.removeGroupControls(),this.removeListeners()}}let i6=t=>(e,i)=>{t&&y.postRender(()=>t(e,i))};class i8 extends ic{constructor(){super(...arguments),this.removePointerDownListener=c}onPointerDown(t){this.session=new iY(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:iz(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:i,onPanEnd:s}=this.node.getProps();return{onSessionStart:i6(t),onStart:i6(e),onMove:i,onEnd:(t,e)=>{delete this.session,s&&y.postRender(()=>s(t,e))}}}mount(){this.removePointerDownListener=iS(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}let{schedule:st}=g(queueMicrotask,!1);var se=i(35371);let si=(0,se.createContext)(null),ss=(0,se.createContext)({}),sr=(0,se.createContext)({}),sn={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function so(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let sa={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t)if(!tc.test(t))return t;else t=parseFloat(t);let i=so(t,e.target.x),s=so(t,e.target.y);return`${i}% ${s}%`}},sl={};class su extends se.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i,layoutId:s}=this.props,{projection:r}=t;for(let t in sd)sl[t]=sd[t],H(t)&&(sl[t].isCSSVariable=!0);r&&(e.group&&e.group.add(r),i&&i.register&&s&&i.register(r),r.root.didUpdate(),r.addEventListener("animationComplete",()=>{this.safeToRemove()}),r.setOptions({...r.options,onExitComplete:()=>this.safeToRemove()})),sn.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:i,drag:s,isPresent:r}=this.props,{projection:n}=i;return n&&(n.isPresent=r,s||t.layoutDependency!==e||void 0===e||t.isPresent!==r?n.willUpdate():this.safeToRemove(),t.isPresent!==r&&(r?n.promote():n.relegate()||y.postRender(()=>{let t=n.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),st.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i}=this.props,{projection:s}=t;s&&(s.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(s),i&&i.deregister&&i.deregister(s))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function sh(t){let[e,i]=function(t=!0){let e=(0,se.useContext)(si);if(null===e)return[!0,null];let{isPresent:i,onExitComplete:s,register:r}=e,n=(0,se.useId)();(0,se.useEffect)(()=>{if(t)return r(n)},[t]);let o=(0,se.useCallback)(()=>t&&s&&s(n),[n,s,t]);return!i&&s?[!1,o]:[!0]}(),s=(0,se.useContext)(ss);return(0,o.jsx)(su,{...t,layoutGroup:s,switchLayoutGroup:(0,se.useContext)(sr),isPresent:e,safeToRemove:i})}let sd={borderRadius:{...sa,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:sa,borderTopRightRadius:sa,borderBottomLeftRadius:sa,borderBottomRightRadius:sa,boxShadow:{correct:(t,{treeScale:e,projectionDelta:i})=>{let s=t_.parse(t);if(s.length>5)return t;let r=t_.createTransformer(t),n=+("number"!=typeof s[0]),o=i.x.scale*e.x,a=i.y.scale*e.y;s[0+n]/=o,s[1+n]/=a;let l=tE(o,a,.5);return"number"==typeof s[2+n]&&(s[2+n]/=l),"number"==typeof s[3+n]&&(s[3+n]/=l),r(s)}}};function sc(t){return eZ(t)&&"ownerSVGElement"in t}let sp=(t,e)=>t.depth-e.depth;class sm{constructor(){this.children=[],this.isDirty=!1}add(t){P(this.children,t),this.isDirty=!0}remove(t){j(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(sp),this.isDirty=!1,this.children.forEach(t)}}function sf(t){return V(t)?t.get():t}let sg=["TopLeft","TopRight","BottomLeft","BottomRight"],sy=sg.length,sv=t=>"string"==typeof t?parseFloat(t):t,sx=t=>"number"==typeof t||tc.test(t);function sw(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let sb=sT(0,.5,ei),sS=sT(.5,.95,c);function sT(t,e,i){return s=>s<t?0:s>e?1:i(el(t,e,s))}function sP(t,e){t.min=e.min,t.max=e.max}function sj(t,e){sP(t.x,e.x),sP(t.y,e.y)}function s_(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function sA(t,e,i,s,r){return t-=e,t=s+1/i*(t-s),void 0!==r&&(t=s+1/r*(t-s)),t}function sC(t,e,[i,s,r],n,o){!function(t,e=0,i=1,s=.5,r,n=t,o=t){if(td.test(e)&&(e=parseFloat(e),e=tE(o.min,o.max,e/100)-o.min),"number"!=typeof e)return;let a=tE(n.min,n.max,s);t===n&&(a-=e),t.min=sA(t.min,e,i,a,r),t.max=sA(t.max,e,i,a,r)}(t,e[i],e[s],e[r],e.scale,n,o)}let sE=["x","scaleX","originX"],sM=["y","scaleY","originY"];function sD(t,e,i,s){sC(t.x,e,sE,i?i.x:void 0,s?s.x:void 0),sC(t.y,e,sM,i?i.y:void 0,s?s.y:void 0)}function sk(t){return 0===t.translate&&1===t.scale}function sB(t){return sk(t.x)&&sk(t.y)}function sV(t,e){return t.min===e.min&&t.max===e.max}function sO(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function sR(t,e){return sO(t.x,e.x)&&sO(t.y,e.y)}function sF(t){return iP(t.x)/iP(t.y)}function sL(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class sI{constructor(){this.members=[]}add(t){P(this.members,t),t.scheduleRender()}remove(t){if(j(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e,i=this.members.findIndex(e=>t===e);if(0===i)return!1;for(let t=i;t>=0;t--){let i=this.members[t];if(!1!==i.isPresent){e=i;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,e&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:s}=t.options;!1===s&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:i}=t;e.onExitComplete&&e.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let sU={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},sN=["","X","Y","Z"],sG={visibility:"hidden"},sq=0;function s$(t,e,i,s){let{latestValues:r}=e;r[t]&&(i[t]=r[t],e.setStaticValue(t,0),s&&(s[t]=0))}function sW({attachResizeListener:t,defaultParent:e,measureScroll:i,checkIsScrollRoot:s,resetTransform:r}){return class{constructor(t={},i=e?.()){this.id=sq++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,f.value&&(sU.nodes=sU.calculatedTargetDeltas=sU.calculatedProjections=0),this.nodes.forEach(sX),this.nodes.forEach(s1),this.nodes.forEach(s5),this.nodes.forEach(sY),f.addProjectionMetrics&&f.addProjectionMetrics(sU)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new sm)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new _),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let i=this.eventHandlers.get(t);i&&i.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e){if(this.instance)return;this.isSVG=sc(e)&&!(sc(e)&&"svg"===e.tagName),this.instance=e;let{layoutId:i,layout:s,visualElement:r}=this.options;if(r&&!r.current&&r.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(s||i)&&(this.isLayoutDirty=!0),t){let i,s=()=>this.root.updateBlockedByResize=!1;t(e,()=>{this.root.updateBlockedByResize=!0,i&&i(),i=function(t,e){let i=C.now(),s=({timestamp:r})=>{let n=r-i;n>=250&&(v(s),t(n-e))};return y.setup(s,!0),()=>v(s)}(s,250),sn.hasAnimatedSinceResize&&(sn.hasAnimatedSinceResize=!1,this.nodes.forEach(s0))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&r&&(i||s)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeLayoutChanged:i,layout:s})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let n=this.options.transition||r.getDefaultTransition()||s6,{onLayoutAnimationStart:o,onLayoutAnimationComplete:a}=r.getProps(),l=!this.targetLayout||!sR(this.targetLayout,s),u=!e&&i;if(this.options.layoutRoot||this.resumeFrom||u||e&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let e={...d(n,"layout"),onPlay:o,onComplete:a};(r.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e),this.setAnimationOrigin(t,u)}else e||s0(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=s})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),v(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(s2),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;let{visualElement:i}=e.options;if(!i)return;let s=i.props[F];if(window.MotionHasOptimisedAnimation(s,"transform")){let{layout:t,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(s,"transform",y,!(t||i))}let{parent:r}=e;r&&!r.hasCheckedOptimisedAppear&&t(r)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:i}=this.options;if(void 0===e&&!i)return;let s=this.getTransformTemplate();this.prevTransformTemplateValue=s?s(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(sZ);return}if(this.animationId<=this.animationCommitId)return void this.nodes.forEach(sJ);this.isUpdating||this.nodes.forEach(sJ),this.animationCommitId=this.animationId,this.isUpdating=!1,this.nodes.forEach(sQ),this.nodes.forEach(sz),this.nodes.forEach(sH),this.clearAllSnapshots();let t=C.now();x.delta=U(0,1e3/60,t-x.timestamp),x.timestamp=t,x.isProcessing=!0,w.update.process(x),w.preRender.process(x),w.render.process(x),x.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,st.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(sK),this.sharedNodes.forEach(s3)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,y.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){y.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||iP(this.snapshot.measuredBox.x)||iP(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=iB(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&this.instance){let e=s(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!r)return;let t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!sB(this.projectionDelta),i=this.getTransformTemplate(),s=i?i(this.latestValues,""):void 0,n=s!==this.prevTransformTemplateValue;t&&this.instance&&(e||iF(this.latestValues)||n)&&(r(this.instance,s),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let i=this.measurePageBox(),s=this.removeElementScroll(i);return t&&(s=this.removeTransform(s)),re((e=s).x),re(e.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:s,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:t}=this.options;if(!t)return iB();let e=t.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(rs))){let{scroll:t}=this.root;t&&(iG(e.x,t.offset.x),iG(e.y,t.offset.y))}return e}removeElementScroll(t){let e=iB();if(sj(e,t),this.scroll?.wasRoot)return e;for(let i=0;i<this.path.length;i++){let s=this.path[i],{scroll:r,options:n}=s;s!==this.root&&r&&n.layoutScroll&&(r.wasRoot&&sj(e,t),iG(e.x,r.offset.x),iG(e.y,r.offset.y))}return e}applyTransform(t,e=!1){let i=iB();sj(i,t);for(let t=0;t<this.path.length;t++){let s=this.path[t];!e&&s.options.layoutScroll&&s.scroll&&s!==s.root&&i$(i,{x:-s.scroll.offset.x,y:-s.scroll.offset.y}),iF(s.latestValues)&&i$(i,s.latestValues)}return iF(this.latestValues)&&i$(i,this.latestValues),i}removeTransform(t){let e=iB();sj(e,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];if(!i.instance||!iF(i.latestValues))continue;iR(i.latestValues)&&i.updateSnapshot();let s=iB();sj(s,i.measurePageBox()),sD(e,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,s)}return iF(this.latestValues)&&sD(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==x.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){let e=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=e.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=e.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=e.isSharedProjectionDirty);let i=!!this.resumingFrom||this!==e;if(!(t||i&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:s,layoutId:r}=this.options;if(this.layout&&(s||r)){if(this.resolvedRelativeTargetAt=x.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=iB(),this.relativeTargetOrigin=iB(),iE(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),sj(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=iB(),this.targetWithTransforms=iB()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var n,o,a;this.forceRelativeParentToResolveTarget(),n=this.target,o=this.relativeTarget,a=this.relativeParent.target,iA(n.x,o.x,a.x),iA(n.y,o.y,a.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):sj(this.target,this.layout.layoutBox),iN(this.target,this.targetDelta)):sj(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=iB(),this.relativeTargetOrigin=iB(),iE(this.relativeTargetOrigin,this.target,t.target),sj(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}f.value&&sU.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||iR(this.parent.latestValues)||iL(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let t=this.getLead(),e=!!this.resumingFrom||this!==t,i=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(i=!1),e&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===x.timestamp&&(i=!1),i)return;let{layout:s,layoutId:r}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(s||r))return;sj(this.layoutCorrected,this.layout.layoutBox);let n=this.treeScale.x,o=this.treeScale.y;!function(t,e,i,s=!1){let r,n,o=i.length;if(o){e.x=e.y=1;for(let a=0;a<o;a++){n=(r=i[a]).projectionDelta;let{visualElement:o}=r.options;(!o||!o.props.style||"contents"!==o.props.style.display)&&(s&&r.options.layoutScroll&&r.scroll&&r!==r.root&&i$(t,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),n&&(e.x*=n.x.scale,e.y*=n.y.scale,iN(t,n)),s&&iF(r.latestValues)&&i$(t,r.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}}(this.layoutCorrected,this.treeScale,this.path,e),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox,t.targetWithTransforms=iB());let{target:a}=t;if(!a){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(s_(this.prevProjectionDelta.x,this.projectionDelta.x),s_(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),i_(this.projectionDelta,this.layoutCorrected,a,this.latestValues),this.treeScale.x===n&&this.treeScale.y===o&&sL(this.projectionDelta.x,this.prevProjectionDelta.x)&&sL(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",a)),f.value&&sU.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.visualElement?.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=iD(),this.projectionDelta=iD(),this.projectionDeltaWithTransform=iD()}setAnimationOrigin(t,e=!1){let i,s=this.snapshot,r=s?s.latestValues:{},n={...this.latestValues},o=iD();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let a=iB(),l=(s?s.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),h=!u||u.members.length<=1,d=!!(l&&!h&&!0===this.options.crossfade&&!this.path.some(s7));this.animationProgress=0,this.mixTargetDelta=e=>{let s=e/1e3;if(s4(o.x,t.x,s),s4(o.y,t.y,s),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,c,p,m,f,g;iE(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,f=a,g=s,s9(p.x,m.x,f.x,g),s9(p.y,m.y,f.y,g),i&&(u=this.relativeTarget,c=i,sV(u.x,c.x)&&sV(u.y,c.y))&&(this.isProjectionDirty=!1),i||(i=iB()),sj(i,this.relativeTarget)}l&&(this.animationValues=n,function(t,e,i,s,r,n){r?(t.opacity=tE(0,i.opacity??1,sb(s)),t.opacityExit=tE(e.opacity??1,0,sS(s))):n&&(t.opacity=tE(e.opacity??1,i.opacity??1,s));for(let r=0;r<sy;r++){let n=`border${sg[r]}Radius`,o=sw(e,n),a=sw(i,n);(void 0!==o||void 0!==a)&&(o||(o=0),a||(a=0),0===o||0===a||sx(o)===sx(a)?(t[n]=Math.max(tE(sv(o),sv(a),s),0),(td.test(a)||td.test(o))&&(t[n]+="%")):t[n]=a)}(e.rotate||i.rotate)&&(t.rotate=tE(e.rotate||0,i.rotate||0,s))}(n,r,this.latestValues,s,d,h)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=s},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(v(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=y.update(()=>{sn.hasAnimatedSinceResize=!0,q.layout++,this.motionValue||(this.motionValue=k(0)),this.currentAnimation=function(t,e,i){let s=V(t)?t:k(t);return s.start(e6("",s,e,i)),s.animation}(this.motionValue,[0,1e3],{...t,velocity:0,isSync:!0,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{q.layout--},onComplete:()=>{q.layout--,t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:i,layout:s,latestValues:r}=t;if(e&&i&&s){if(this!==t&&this.layout&&s&&ri(this.options.animationType,this.layout.layoutBox,s.layoutBox)){i=this.target||iB();let e=iP(this.layout.layoutBox.x);i.x.min=t.target.x.min,i.x.max=i.x.min+e;let s=iP(this.layout.layoutBox.y);i.y.min=t.target.y.min,i.y.max=i.y.min+s}sj(e,i),i$(e,r),i_(this.projectionDeltaWithTransform,this.layoutCorrected,e,r)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new sI),this.sharedNodes.get(t).add(e);let i=e.options.initialPromotionConfig;e.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){let{layoutId:t}=this.options;return t&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:t}=this.options;return t?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:i}={}){let s=this.getStack();s&&s.promote(this,i),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:i}=t;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(e=!0),!e)return;let s={};i.z&&s$("z",t,s,this.animationValues);for(let e=0;e<sN.length;e++)s$(`rotate${sN[e]}`,t,s,this.animationValues),s$(`skew${sN[e]}`,t,s,this.animationValues);for(let e in t.render(),s)t.setStaticValue(e,s[e]),this.animationValues&&(this.animationValues[e]=s[e]);t.scheduleRender()}getProjectionStyles(t){if(!this.instance||this.isSVG)return;if(!this.isVisible)return sG;let e={visibility:""},i=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,e.opacity="",e.pointerEvents=sf(t?.pointerEvents)||"",e.transform=i?i(this.latestValues,""):"none",e;let s=this.getLead();if(!this.projectionDelta||!this.layout||!s.target){let e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=sf(t?.pointerEvents)||""),this.hasProjected&&!iF(this.latestValues)&&(e.transform=i?i({},""):"none",this.hasProjected=!1),e}let r=s.animationValues||s.latestValues;this.applyTransformsToTarget(),e.transform=function(t,e,i){let s="",r=t.x.translate/e.x,n=t.y.translate/e.y,o=i?.z||0;if((r||n||o)&&(s=`translate3d(${r}px, ${n}px, ${o}px) `),(1!==e.x||1!==e.y)&&(s+=`scale(${1/e.x}, ${1/e.y}) `),i){let{transformPerspective:t,rotate:e,rotateX:r,rotateY:n,skewX:o,skewY:a}=i;t&&(s=`perspective(${t}px) ${s}`),e&&(s+=`rotate(${e}deg) `),r&&(s+=`rotateX(${r}deg) `),n&&(s+=`rotateY(${n}deg) `),o&&(s+=`skewX(${o}deg) `),a&&(s+=`skewY(${a}deg) `)}let a=t.x.scale*e.x,l=t.y.scale*e.y;return(1!==a||1!==l)&&(s+=`scale(${a}, ${l})`),s||"none"}(this.projectionDeltaWithTransform,this.treeScale,r),i&&(e.transform=i(r,e.transform));let{x:n,y:o}=this.projectionDelta;for(let t in e.transformOrigin=`${100*n.origin}% ${100*o.origin}% 0`,s.animationValues?e.opacity=s===this?r.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:r.opacityExit:e.opacity=s===this?void 0!==r.opacity?r.opacity:"":void 0!==r.opacityExit?r.opacityExit:0,sl){if(void 0===r[t])continue;let{correct:i,applyTo:n,isCSSVariable:o}=sl[t],a="none"===e.transform?r[t]:i(r[t],s);if(n){let t=n.length;for(let i=0;i<t;i++)e[n[i]]=a}else o?this.options.visualElement.renderState.vars[t]=a:e[t]=a}return this.options.layoutId&&(e.pointerEvents=s===this?sf(t?.pointerEvents)||"":"none"),e}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>t.currentAnimation?.stop()),this.root.nodes.forEach(sZ),this.root.sharedNodes.clear()}}}function sz(t){t.updateLayout()}function sH(t){let e=t.resumeFrom?.snapshot||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){let{layoutBox:i,measuredBox:s}=t.layout,{animationType:r}=t.options,n=e.source!==t.layout.source;"size"===r?iV(t=>{let s=n?e.measuredBox[t]:e.layoutBox[t],r=iP(s);s.min=i[t].min,s.max=s.min+r}):ri(r,e.layoutBox,i)&&iV(s=>{let r=n?e.measuredBox[s]:e.layoutBox[s],o=iP(i[s]);r.max=r.min+o,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[s].max=t.relativeTarget[s].min+o)});let o=iD();i_(o,i,e.layoutBox);let a=iD();n?i_(a,t.applyTransform(s,!0),e.measuredBox):i_(a,i,e.layoutBox);let l=!sB(o),u=!1;if(!t.resumeFrom){let s=t.getClosestProjectingParent();if(s&&!s.resumeFrom){let{snapshot:r,layout:n}=s;if(r&&n){let o=iB();iE(o,e.layoutBox,r.layoutBox);let a=iB();iE(a,i,n.layoutBox),sR(o,a)||(u=!0),s.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=o,t.relativeParent=s)}}}t.notifyListeners("didUpdate",{layout:i,snapshot:e,delta:a,layoutDelta:o,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function sX(t){f.value&&sU.nodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function sY(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function sK(t){t.clearSnapshot()}function sZ(t){t.clearMeasurements()}function sJ(t){t.isLayoutDirty=!1}function sQ(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function s0(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function s1(t){t.resolveTargetDelta()}function s5(t){t.calcProjection()}function s2(t){t.resetSkewAndRotation()}function s3(t){t.removeLeadSnapshot()}function s4(t,e,i){t.translate=tE(e.translate,0,i),t.scale=tE(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function s9(t,e,i,s){t.min=tE(e.min,i.min,s),t.max=tE(e.max,i.max,s)}function s7(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let s6={duration:.45,ease:[.4,0,.1,1]},s8=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),rt=s8("applewebkit/")&&!s8("chrome/")?Math.round:c;function re(t){t.min=rt(t.min),t.max=rt(t.max)}function ri(t,e,i){return"position"===t||"preserve-aspect"===t&&!(.2>=Math.abs(sF(e)-sF(i)))}function rs(t){return t!==t.root&&t.scroll?.wasRoot}let rr=sW({attachResizeListener:(t,e)=>iv(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),rn={current:void 0},ro=sW({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!rn.current){let t=new rr({});t.mount(window),t.setOptions({layoutScroll:!0}),rn.current=t}return rn.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position});function ra(t,e){let i=function(t,e,i){if(t instanceof EventTarget)return[t];if("string"==typeof t){let e=document,i=(void 0)??e.querySelectorAll(t);return i?Array.from(i):[]}return Array.from(t)}(t),s=new AbortController;return[i,{passive:!0,...e,signal:s.signal},()=>s.abort()]}function rl(t){return!("touch"===t.pointerType||iy.x||iy.y)}function ru(t,e,i){let{props:s}=t;t.animationState&&s.whileHover&&t.animationState.setActive("whileHover","Start"===i);let r=s["onHover"+i];r&&y.postRender(()=>r(e,iw(e)))}class rh extends ic{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[s,r,n]=ra(t,i),o=t=>{if(!rl(t))return;let{target:i}=t,s=e(i,t);if("function"!=typeof s||!i)return;let n=t=>{rl(t)&&(s(t),i.removeEventListener("pointerleave",n))};i.addEventListener("pointerleave",n,r)};return s.forEach(t=>{t.addEventListener("pointerenter",o,r)}),n}(t,(t,e)=>(ru(this.node,e,"Start"),t=>ru(this.node,t,"End"))))}unmount(){}}class rd extends ic{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=I(iv(this.node.current,"focus",()=>this.onFocus()),iv(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let rc=(t,e)=>!!e&&(t===e||rc(t,e.parentElement)),rp=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),rm=new WeakSet;function rf(t){return e=>{"Enter"===e.key&&t(e)}}function rg(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}let ry=(t,e)=>{let i=t.currentTarget;if(!i)return;let s=rf(()=>{if(rm.has(i))return;rg(i,"down");let t=rf(()=>{rg(i,"up")});i.addEventListener("keyup",t,e),i.addEventListener("blur",()=>rg(i,"cancel"),e)});i.addEventListener("keydown",s,e),i.addEventListener("blur",()=>i.removeEventListener("keydown",s),e)};function rv(t){return ix(t)&&!(iy.x||iy.y)}function rx(t,e,i){let{props:s}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&s.whileTap&&t.animationState.setActive("whileTap","Start"===i);let r=s["onTap"+("End"===i?"":i)];r&&y.postRender(()=>r(e,iw(e)))}class rw extends ic{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[s,r,n]=ra(t,i),o=t=>{let s=t.currentTarget;if(!rv(t))return;rm.add(s);let n=e(s,t),o=(t,e)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),rm.has(s)&&rm.delete(s),rv(t)&&"function"==typeof n&&n(t,{success:e})},a=t=>{o(t,s===window||s===document||i.useGlobalTarget||rc(s,t.target))},l=t=>{o(t,!1)};window.addEventListener("pointerup",a,r),window.addEventListener("pointercancel",l,r)};return s.forEach(t=>{((i.useGlobalTarget?window:t).addEventListener("pointerdown",o,r),eJ(t))&&(t.addEventListener("focus",t=>ry(t,r)),rp.has(t.tagName)||-1!==t.tabIndex||t.hasAttribute("tabindex")||(t.tabIndex=0))}),n}(t,(t,e)=>(rx(this.node,e,"Start"),(t,{success:e})=>rx(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let rb=new WeakMap,rS=new WeakMap,rT=t=>{let e=rb.get(t.target);e&&e(t)},rP=t=>{t.forEach(rT)},rj={some:0,all:1};class r_ extends ic{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:i,amount:s="some",once:r}=t,n={root:e?e.current:void 0,rootMargin:i,threshold:"number"==typeof s?s:rj[s]};return function(t,e,i){let s=function({root:t,...e}){let i=t||document;rS.has(i)||rS.set(i,{});let s=rS.get(i),r=JSON.stringify(e);return s[r]||(s[r]=new IntersectionObserver(rP,{root:t,...e})),s[r]}(e);return rb.set(t,i),s.observe(t),()=>{rb.delete(t),s.unobserve(t)}}(this.node.current,n,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,r&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:i,onViewportLeave:s}=this.node.getProps(),n=e?i:s;n&&n(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return i=>t[i]!==e[i]}(t,e))&&this.startObserver()}unmount(){}}let rA=(0,se.createContext)({strict:!1}),rC=(0,se.createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"}),rE=(0,se.createContext)({});function rM(t){return a(t.animate)||io.some(e=>is(t[e]))}function rD(t){return!!(rM(t)||t.variants)}function rk(t){return Array.isArray(t)?t.join(" "):t}let rB="undefined"!=typeof window,rV={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},rO={};for(let t in rV)rO[t]={isEnabled:e=>rV[t].some(t=>!!e[t])};let rR=Symbol.for("motionComponentSymbol"),rF=rB?se.useLayoutEffect:se.useEffect;function rL(t,{layout:e,layoutId:i}){return S.has(t)||t.startsWith("origin")||(e||void 0!==i)&&(!!sl[t]||"opacity"===t)}let rI=(t,e)=>e&&"number"==typeof t?e.transform(t):t,rU={...Z,transform:Math.round},rN={borderWidth:tc,borderTopWidth:tc,borderRightWidth:tc,borderBottomWidth:tc,borderLeftWidth:tc,borderRadius:tc,radius:tc,borderTopLeftRadius:tc,borderTopRightRadius:tc,borderBottomRightRadius:tc,borderBottomLeftRadius:tc,width:tc,maxWidth:tc,height:tc,maxHeight:tc,top:tc,right:tc,bottom:tc,left:tc,padding:tc,paddingTop:tc,paddingRight:tc,paddingBottom:tc,paddingLeft:tc,margin:tc,marginTop:tc,marginRight:tc,marginBottom:tc,marginLeft:tc,backgroundPositionX:tc,backgroundPositionY:tc,rotate:th,rotateX:th,rotateY:th,rotateZ:th,scale:Q,scaleX:Q,scaleY:Q,scaleZ:Q,skew:th,skewX:th,skewY:th,distance:tc,translateX:tc,translateY:tc,translateZ:tc,x:tc,y:tc,z:tc,perspective:tc,transformPerspective:tc,opacity:J,originX:tf,originY:tf,originZ:tc,zIndex:rU,fillOpacity:J,strokeOpacity:J,numOctaves:rU},rG={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},rq=b.length;function r$(t,e,i){let{style:s,vars:r,transformOrigin:n}=t,o=!1,a=!1;for(let t in e){let i=e[t];if(S.has(t)){o=!0;continue}if(H(t)){r[t]=i;continue}{let e=rI(i,rN[t]);t.startsWith("origin")?(a=!0,n[t]=e):s[t]=e}}if(!e.transform&&(o||i?s.transform=function(t,e,i){let s="",r=!0;for(let n=0;n<rq;n++){let o=b[n],a=t[o];if(void 0===a)continue;let l=!0;if(!(l="number"==typeof a?a===+!!o.startsWith("scale"):0===parseFloat(a))||i){let t=rI(a,rN[o]);if(!l){r=!1;let e=rG[o]||o;s+=`${e}(${t}) `}i&&(e[o]=t)}}return s=s.trim(),i?s=i(e,r?"":s):r&&(s="none"),s}(e,t.transform,i):s.transform&&(s.transform="none")),a){let{originX:t="50%",originY:e="50%",originZ:i=0}=n;s.transformOrigin=`${t} ${e} ${i}`}}let rW=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function rz(t,e,i){for(let s in e)V(e[s])||rL(s,i)||(t[s]=e[s])}let rH={offset:"stroke-dashoffset",array:"stroke-dasharray"},rX={offset:"strokeDashoffset",array:"strokeDasharray"};function rY(t,{attrX:e,attrY:i,attrScale:s,pathLength:r,pathSpacing:n=1,pathOffset:o=0,...a},l,u,h){if(r$(t,a,u),l){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:d,style:c}=t;d.transform&&(c.transform=d.transform,delete d.transform),(c.transform||d.transformOrigin)&&(c.transformOrigin=d.transformOrigin??"50% 50%",delete d.transformOrigin),c.transform&&(c.transformBox=h?.transformBox??"fill-box",delete d.transformBox),void 0!==e&&(d.x=e),void 0!==i&&(d.y=i),void 0!==s&&(d.scale=s),void 0!==r&&function(t,e,i=1,s=0,r=!0){t.pathLength=1;let n=r?rH:rX;t[n.offset]=tc.transform(-s);let o=tc.transform(e),a=tc.transform(i);t[n.array]=`${o} ${a}`}(d,r,n,o,!1)}let rK=()=>({...rW(),attrs:{}}),rZ=t=>"string"==typeof t&&"svg"===t.toLowerCase(),rJ=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function rQ(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||rJ.has(t)}let r0=t=>!rQ(t);try{!function(t){"function"==typeof t&&(r0=e=>e.startsWith("on")?!rQ(e):t(e))}(require("@emotion/is-prop-valid").default)}catch{}let r1=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function r5(t){if("string"!=typeof t||t.includes("-"));else if(r1.indexOf(t)>-1||/[A-Z]/u.test(t))return!0;return!1}let r2=t=>(e,i)=>{let s=(0,se.useContext)(rE),r=(0,se.useContext)(si),n=()=>(function({scrapeMotionValuesFromProps:t,createRenderState:e},i,s,r){return{latestValues:function(t,e,i,s){let r={},n=s(t,{});for(let t in n)r[t]=sf(n[t]);let{initial:o,animate:l}=t,h=rM(t),d=rD(t);e&&d&&!h&&!1!==t.inherit&&(void 0===o&&(o=e.initial),void 0===l&&(l=e.animate));let c=!!i&&!1===i.initial,p=(c=c||!1===o)?l:o;if(p&&"boolean"!=typeof p&&!a(p)){let e=Array.isArray(p)?p:[p];for(let i=0;i<e.length;i++){let s=u(t,e[i]);if(s){let{transitionEnd:t,transition:e,...i}=s;for(let t in i){let e=i[t];if(Array.isArray(e)){let t=c?e.length-1:0;e=e[t]}null!==e&&(r[t]=e)}for(let e in t)r[e]=t[e]}}}return r}(i,s,r,t),renderState:e()}})(t,e,s,r);return i?n():function(t){let e=(0,se.useRef)(null);return null===e.current&&(e.current=t()),e.current}(n)};function r3(t,e,i){let{style:s}=t,r={};for(let n in s)(V(s[n])||e.style&&V(e.style[n])||rL(n,t)||i?.getValue(n)?.liveStyle!==void 0)&&(r[n]=s[n]);return r}let r4={useVisualState:r2({scrapeMotionValuesFromProps:r3,createRenderState:rW})};function r9(t,e,i){let s=r3(t,e,i);for(let i in t)(V(t[i])||V(e[i]))&&(s[-1!==b.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=t[i]);return s}let r7={useVisualState:r2({scrapeMotionValuesFromProps:r9,createRenderState:rK})},r6=t=>e=>e.test(t),r8=[Z,tc,td,th,tm,tp,{test:t=>"auto"===t,parse:t=>t}],nt=t=>r8.find(r6(t)),ne=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),ni=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,ns=t=>/^0[^.\s]+$/u.test(t),nr=new Set(["brightness","contrast","saturate","opacity"]);function nn(t){let[e,i]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[s]=i.match(te)||[];if(!s)return t;let r=i.replace(s,""),n=+!!nr.has(e);return s!==i&&(n*=100),e+"("+n+r+")"}let no=/\b([a-z-]*)\(.*?\)/gu,na={...t_,getAnimatableNone:t=>{let e=t.match(no);return e?e.map(nn).join(" "):t}},nl={...rN,color:ty,backgroundColor:ty,outlineColor:ty,fill:ty,stroke:ty,borderColor:ty,borderTopColor:ty,borderRightColor:ty,borderBottomColor:ty,borderLeftColor:ty,filter:na,WebkitFilter:na},nu=t=>nl[t];function nh(t,e){let i=nu(t);return i!==na&&(i=t_),i.getAnimatableNone?i.getAnimatableNone(e):void 0}let nd=new Set(["auto","none","0"]);class nc extends eL{constructor(t,e,i,s,r){super(t,e,i,s,r,!0)}readKeyframes(){let{unresolvedKeyframes:t,element:e,name:i}=this;if(!e||!e.current)return;super.readKeyframes();for(let i=0;i<t.length;i++){let s=t[i];if("string"==typeof s&&Y(s=s.trim())){let r=function t(e,i,s=1){W(s<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`);let[r,n]=function(t){let e=ni.exec(t);if(!e)return[,];let[,i,s,r]=e;return[`--${i??s}`,r]}(e);if(!r)return;let o=window.getComputedStyle(i).getPropertyValue(r);if(o){let t=o.trim();return ne(t)?parseFloat(t):t}return Y(n)?t(n,i,s+1):n}(s,e.current);void 0!==r&&(t[i]=r),i===t.length-1&&(this.finalKeyframe=s)}}if(this.resolveNoneKeyframes(),!T.has(i)||2!==t.length)return;let[s,r]=t,n=nt(s),o=nt(r);if(n!==o)if(eC(n)&&eC(o))for(let e=0;e<t.length;e++){let i=t[e];"string"==typeof i&&(t[e]=parseFloat(i))}else eD[i]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:t,name:e}=this,i=[];for(let e=0;e<t.length;e++){var s;(null===t[e]||("number"==typeof(s=t[e])?0===s:null===s||"none"===s||"0"===s||ns(s)))&&i.push(e)}i.length&&function(t,e,i){let s,r=0;for(;r<t.length&&!s;){let e=t[r];"string"==typeof e&&!nd.has(e)&&tS(e).values.length&&(s=t[r]),r++}if(s&&i)for(let r of e)t[r]=nh(i,s)}(t,i,e)}measureInitialState(){let{element:t,unresolvedKeyframes:e,name:i}=this;if(!t||!t.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=eD[i](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;let s=e[e.length-1];void 0!==s&&t.getValue(i,s).jump(s,!1)}measureEndState(){let{element:t,name:e,unresolvedKeyframes:i}=this;if(!t||!t.current)return;let s=t.getValue(e);s&&s.jump(this.measuredOrigin,!1);let r=i.length-1,n=i[r];i[r]=eD[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==n&&void 0===this.finalKeyframe&&(this.finalKeyframe=n),this.removedTransforms?.length&&this.removedTransforms.forEach(([e,i])=>{t.getValue(e).set(i)}),this.resolveNoneKeyframes()}}let np=[...r8,ty,t_],nm=t=>np.find(r6(t)),nf={current:null},ng={current:!1},ny=new WeakMap,nv=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class nx{scrapeMotionValuesFromProps(t,e,i){return{}}constructor({parent:t,props:e,presenceContext:i,reducedMotionConfig:s,blockInitialAnimation:r,visualState:n},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=eL,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let t=C.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,y.render(this.render,!1,!0))};let{latestValues:a,renderState:l}=n;this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=i,this.depth=t?t.depth+1:0,this.reducedMotionConfig=s,this.options=o,this.blockInitialAnimation=!!r,this.isControllingVariants=rM(e),this.isVariantNode=rD(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:u,...h}=this.scrapeMotionValuesFromProps(e,{},this);for(let t in h){let e=h[t];void 0!==a[t]&&V(e)&&e.set(a[t],!1)}}mount(t){this.current=t,ny.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),ng.current||function(){if(ng.current=!0,rB)if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>nf.current=t.matches;t.addListener(e),e()}else nf.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||nf.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in this.projection&&this.projection.unmount(),v(this.notifyUpdate),v(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[t].clear();for(let t in this.features){let e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){let i;this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();let s=S.has(t);s&&this.onBindTransform&&this.onBindTransform();let r=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&y.preRender(this.notifyUpdate),s&&this.projection&&(this.projection.isTransformDirty=!0)}),n=e.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{r(),n(),i&&i(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in rO){let e=rO[t];if(!e)continue;let{isEnabled:i,Feature:s}=e;if(!this.features[t]&&s&&i(this.props)&&(this.features[t]=new s(this)),this.features[t]){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):iB()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<nv.length;e++){let i=nv[e];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let s=t["on"+i];s&&(this.propEventSubscriptions[i]=this.on(i,s))}this.prevMotionValues=function(t,e,i){for(let s in e){let r=e[s],n=i[s];if(V(r))t.addValue(s,r);else if(V(n))t.addValue(s,k(r,{owner:t}));else if(n!==r)if(t.hasValue(s)){let e=t.getValue(s);!0===e.liveStyle?e.jump(r):e.hasAnimated||e.set(r)}else{let e=t.getStaticValue(s);t.addValue(s,k(void 0!==e?e:r,{owner:t}))}}for(let s in i)void 0===e[s]&&t.removeValue(s);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){let i=this.values.get(t);e!==i&&(i&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let i=this.values.get(t);return void 0===i&&void 0!==e&&(i=k(null===e?void 0:e,{owner:this}),this.addValue(t,i)),i}readValue(t,e){let i=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];return null!=i&&("string"==typeof i&&(ne(i)||ns(i))?i=parseFloat(i):!nm(i)&&t_.test(e)&&(i=nh(t,e)),this.setBaseTarget(t,V(i)?i.get():i)),V(i)?i.get():i}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){let e,{initial:i}=this.props;if("string"==typeof i||"object"==typeof i){let s=u(this.props,i,this.presenceContext?.custom);s&&(e=s[t])}if(i&&void 0!==e)return e;let s=this.getBaseTargetFromProps(this.props,t);return void 0===s||V(s)?void 0!==this.initialValues[t]&&void 0===e?void 0:this.baseTarget[t]:s}on(t,e){return this.events[t]||(this.events[t]=new _),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class nw extends nx{constructor(){super(...arguments),this.KeyframeResolver=nc}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:i}){delete e[t],delete i[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;V(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}function nb(t,{style:e,vars:i},s,r){for(let n in Object.assign(t.style,e,r&&r.getProjectionStyles(s)),i)t.style.setProperty(n,i[n])}class nS extends nw{constructor(){super(...arguments),this.type="html",this.renderInstance=nb}readValueFromInstance(t,e){if(S.has(e))return this.projection?.isProjecting?eP(e):e_(t,e);{let i=window.getComputedStyle(t),s=(H(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof s?s.trim():s}}measureInstanceViewportBox(t,{transformPagePoint:e}){return iW(t,e)}build(t,e,i){r$(t,e,i.transformTemplate)}scrapeMotionValuesFromProps(t,e,i){return r3(t,e,i)}}let nT=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class nP extends nw{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=iB}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(S.has(e)){let t=nu(e);return t&&t.default||0}return e=nT.has(e)?e:R(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,i){return r9(t,e,i)}build(t,e,i){rY(t,e,this.isSVGTag,i.transformTemplate,i.style)}renderInstance(t,e,i,s){for(let i in nb(t,e,void 0,s),e.attrs)t.setAttribute(nT.has(i)?i:R(i),e.attrs[i])}mount(t){this.isSVGTag=rZ(t.tagName),super.mount(t)}}let nj=function(t){if("undefined"==typeof Proxy)return t;let e=new Map;return new Proxy((...e)=>t(...e),{get:(i,s)=>"create"===s?t:(e.has(s)||e.set(s,t(s)),e.get(s))})}((r={animation:{Feature:ip},exit:{Feature:ig},inView:{Feature:r_},tap:{Feature:rw},focus:{Feature:rd},hover:{Feature:rh},pan:{Feature:i8},drag:{Feature:i7,ProjectionNode:ro,MeasureLayout:sh},layout:{ProjectionNode:ro,MeasureLayout:sh}},n=(t,e)=>r5(t)?new nP(e):new nS(e,{allowProjection:t!==se.Fragment}),function(t,{forwardMotionProps:e}={forwardMotionProps:!1}){return function({preloadedFeatures:t,createVisualElement:e,useRender:i,useVisualState:s,Component:r}){function n(t,n){var a,l,u;let h,d={...(0,se.useContext)(rC),...t,layoutId:function({layoutId:t}){let e=(0,se.useContext)(ss).id;return e&&void 0!==t?e+"-"+t:t}(t)},{isStatic:c}=d,p=function(t){let{initial:e,animate:i}=function(t,e){if(rM(t)){let{initial:e,animate:i}=t;return{initial:!1===e||is(e)?e:void 0,animate:is(i)?i:void 0}}return!1!==t.inherit?e:{}}(t,(0,se.useContext)(rE));return(0,se.useMemo)(()=>({initial:e,animate:i}),[rk(e),rk(i)])}(t),m=s(t,c);if(!c&&rB){l=0,u=0,(0,se.useContext)(rA).strict;let t=function(t){let{drag:e,layout:i}=rO;if(!e&&!i)return{};let s={...e,...i};return{MeasureLayout:e?.isEnabled(t)||i?.isEnabled(t)?s.MeasureLayout:void 0,ProjectionNode:s.ProjectionNode}}(d);h=t.MeasureLayout,p.visualElement=function(t,e,i,s,r){let{visualElement:n}=(0,se.useContext)(rE),o=(0,se.useContext)(rA),a=(0,se.useContext)(si),l=(0,se.useContext)(rC).reducedMotion,u=(0,se.useRef)(null);s=s||o.renderer,!u.current&&s&&(u.current=s(t,{visualState:e,parent:n,props:i,presenceContext:a,blockInitialAnimation:!!a&&!1===a.initial,reducedMotionConfig:l}));let h=u.current,d=(0,se.useContext)(sr);h&&!h.projection&&r&&("html"===h.type||"svg"===h.type)&&function(t,e,i,s){let{layoutId:r,layout:n,drag:o,dragConstraints:a,layoutScroll:l,layoutRoot:u,layoutCrossfade:h}=e;t.projection=new i(t.latestValues,e["data-framer-portal-id"]?void 0:function t(e){if(e)return!1!==e.options.allowProjection?e.projection:t(e.parent)}(t.parent)),t.projection.setOptions({layoutId:r,layout:n,alwaysMeasureLayout:!!o||a&&iH(a),visualElement:t,animationType:"string"==typeof n?n:"both",initialPromotionConfig:s,crossfade:h,layoutScroll:l,layoutRoot:u})}(u.current,i,r,d);let c=(0,se.useRef)(!1);(0,se.useInsertionEffect)(()=>{h&&c.current&&h.update(i,a)});let p=i[F],m=(0,se.useRef)(!!p&&!window.MotionHandoffIsComplete?.(p)&&window.MotionHasOptimisedAnimation?.(p));return rF(()=>{h&&(c.current=!0,window.MotionIsMounted=!0,h.updateFeatures(),st.render(h.render),m.current&&h.animationState&&h.animationState.animateChanges())}),(0,se.useEffect)(()=>{h&&(!m.current&&h.animationState&&h.animationState.animateChanges(),m.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(p)}),m.current=!1))}),h}(r,m,d,e,t.ProjectionNode)}return(0,o.jsxs)(rE.Provider,{value:p,children:[h&&p.visualElement?(0,o.jsx)(h,{visualElement:p.visualElement,...d}):null,i(r,t,(a=p.visualElement,(0,se.useCallback)(t=>{t&&m.onMount&&m.onMount(t),a&&(t?a.mount(t):a.unmount()),n&&("function"==typeof n?n(t):iH(n)&&(n.current=t))},[a])),m,c,p.visualElement)]})}t&&function(t){for(let e in t)rO[e]={...rO[e],...t[e]}}(t),n.displayName=`motion.${"string"==typeof r?r:`create(${r.displayName??r.name??""})`}`;let a=(0,se.forwardRef)(n);return a[rR]=r,a}({...r5(t)?r7:r4,preloadedFeatures:r,useRender:function(t=!1){return(e,i,s,{latestValues:r},n)=>{let o=(r5(e)?function(t,e,i,s){let r=(0,se.useMemo)(()=>{let i=rK();return rY(i,e,rZ(s),t.transformTemplate,t.style),{...i.attrs,style:{...i.style}}},[e]);if(t.style){let e={};rz(e,t.style,t),r.style={...e,...r.style}}return r}:function(t,e){let i={},s=function(t,e){let i=t.style||{},s={};return rz(s,i,t),Object.assign(s,function({transformTemplate:t},e){return(0,se.useMemo)(()=>{let i=rW();return r$(i,e,t),Object.assign({},i.vars,i.style)},[e])}(t,e)),s}(t,e);return t.drag&&!1!==t.dragListener&&(i.draggable=!1,s.userSelect=s.WebkitUserSelect=s.WebkitTouchCallout="none",s.touchAction=!0===t.drag?"none":`pan-${"x"===t.drag?"y":"x"}`),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(i.tabIndex=0),i.style=s,i})(i,r,n,e),a=function(t,e,i){let s={};for(let r in t)("values"!==r||"object"!=typeof t.values)&&(r0(r)||!0===i&&rQ(r)||!e&&!rQ(r)||t.draggable&&r.startsWith("onDrag"))&&(s[r]=t[r]);return s}(i,"string"==typeof e,t),l=e!==se.Fragment?{...a,...o,ref:s}:{},{children:u}=i,h=(0,se.useMemo)(()=>V(u)?u.get():u,[u]);return(0,se.createElement)(e,{...l,children:h})}}(e),createVisualElement:n,Component:t})}));var n_=i(48473),nA=i(41265),nC=i.n(nA),nE=i(29085),nM=i(63632);function nD(...t){return(0,nM.QP)((0,nE.$)(t))}let nk=({className:t,IconLeft:e,label:i,IconRight:s,shiny:r=!1})=>(0,o.jsxs)("div",{className:"relative group/button",children:[(0,o.jsx)("div",{"aria-hidden":!0,className:"absolute -inset-0.5 bg-white rounded-lg blur-2xl group-hover/button:opacity-30 transition duration-300  opacity-0 "}),(0,o.jsxs)("div",{className:nD("relative flex items-center px-4 gap-2 text-sm font-semibold text-black group-hover:bg-white/90 duration-1000 rounded-lg h-10",{"bg-white":!r,"bg-gradient-to-r from-white/80 to-white":r},t),children:[e?(0,o.jsx)(e,{className:"w-4 h-4"}):null,i,s?(0,o.jsx)(s,{className:"w-4 h-4"}):null,r&&(0,o.jsx)("div",{"aria-hidden":!0,className:"pointer-events-none absolute inset-0 opacity-0 group-hover/button:[animation-delay:.2s] group-hover/button:animate-button-shine rounded-[inherit] bg-[length:200%_100%] bg-[linear-gradient(110deg,transparent,35%,rgba(255,255,255,.7),75%,transparent)]"})]})]}),nB=({className:t,IconLeft:e,label:i,IconRight:s})=>(0,o.jsxs)("div",{className:nD("items-center gap-2 px-4 duration-500 text-white/70 hover:text-white h-10 flex",t),children:[e?(0,o.jsx)(e,{className:"w-4 h-4"}):null,i,s?(0,o.jsx)(s,{className:"w-4 h-4"}):null]});var nV=i(19161);let nO=(0,nV.A)("log-in",[["path",{d:"m10 17 5-5-5-5",key:"1bsop3"}],["path",{d:"M15 12H3",key:"6jk70r"}],["path",{d:"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4",key:"u53s6r"}]]),nR=(0,nV.A)("book-open",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]]);var nF=i(25741);function nL(){return(0,o.jsxs)("div",{className:"relative flex flex-col items-center text-center ",children:[(0,o.jsx)("h1",{className:"bg-gradient-to-br text-balance text-transparent bg-gradient-stop bg-clip-text from-white via-white via-30% to-white/30  font-medium text-6xl leading-none xl:text-[82px] tracking-tighter",children:"The Developer Platform for Modern APIs"}),(0,o.jsx)("p",{className:"mt-6 sm:mt-8 bg-gradient-to-br text-transparent text-balance bg-gradient-stop bg-clip-text max-w-sm sm:max-w-lg xl:max-w-4xl from-white/70 via-white/70 via-40% to-white/30 text-base md:text-lg",children:"Easily integrate necessary API features like API keys, rate limiting, and usage analytics, ensuring your API is ready to scale."}),(0,o.jsxs)("div",{className:"flex items-center gap-6 mt-16",children:[(0,o.jsx)(nC(),{href:"https://app.unkey.com",className:"group",children:(0,o.jsx)(nk,{shiny:!0,IconLeft:nO,label:"Get started",className:"h-10"})}),(0,o.jsx)(nC(),{href:"/docs",className:"hidden sm:flex",children:(0,o.jsx)(nB,{IconLeft:nR,label:"Documentation",IconRight:nF.A})})]})]})}var nI=i(39890);let nU=({className:t})=>(0,o.jsx)("div",{className:t}),nN=()=>(0,o.jsxs)(nj.div,{className:"relative w-full flex flex-col items-center justify-between mt-48",variants:{hidden:{},visible:{transition:{staggerChildren:.3}}},initial:"hidden",animate:"visible",children:[(0,o.jsx)(nj.div,{variants:{hidden:{opacity:0,y:25},visible:{opacity:1,y:0,transition:{duration:.6}}},children:(0,o.jsx)(nL,{})}),(0,o.jsx)("div",{children:(0,o.jsx)(n_.default,{src:nI.default,alt:"Animated SVG showing computer circuits lighting up",className:"absolute hidden xl:right-32 xl:flex -z-10 xl:-top-56",style:{transform:"scale(2)"},priority:!0})}),(0,o.jsx)(nU,{className:"absolute hidden md:flex left-1/2 -translate-x-[calc(50%+85px)] -bottom-[224px] -z-10"})]})},63033:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64426:(t,e,i)=>{"use strict";i.r(e),i.d(e,{GlobalError:()=>o.a,__next_app__:()=>d,pages:()=>h,routeModule:()=>c,tree:()=>u});var s=i(57864),r=i(94327),n=i(73391),o=i.n(n),a=i(17984),l={};for(let t in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(t)&&(l[t]=()=>a[t]);i.d(e,l);let u={children:["",{children:["[locale]",{children:["(home)",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(i.bind(i,23616)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\web\\app\\[locale]\\(home)\\page.tsx"]}]},{metadata:{icon:[async t=>(await Promise.resolve().then(i.bind(i,15190))).default(t)],apple:[async t=>(await Promise.resolve().then(i.bind(i,7820))).default(t)],openGraph:[async t=>(await Promise.resolve().then(i.bind(i,39440))).default(t)],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(i.bind(i,37919)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\web\\app\\[locale]\\layout.tsx"],"global-error":[()=>Promise.resolve().then(i.bind(i,84641)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\web\\app\\[locale]\\global-error.tsx"]}]},{"not-found":[()=>Promise.resolve().then(i.t.bind(i,47837,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(i.t.bind(i,47168,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(i.t.bind(i,52945,23)),"next/dist/client/components/unauthorized-error"]}]}.children,h=["C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\web\\app\\[locale]\\(home)\\page.tsx"],d={require:i,loadChunk:()=>Promise.resolve()},c=new s.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/[locale]/(home)/page",pathname:"/[locale]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},73024:t=>{"use strict";t.exports=require("node:fs")},73566:t=>{"use strict";t.exports=require("worker_threads")},74075:t=>{"use strict";t.exports=require("zlib")},74998:t=>{"use strict";t.exports=require("perf_hooks")},75919:t=>{"use strict";t.exports=require("node:worker_threads")},76760:t=>{"use strict";t.exports=require("node:path")},76842:(t,e,i)=>{"use strict";i.r(e),i.d(e,{default:()=>s});let s={src:"/_next/static/media/mainboard.9232efd0.svg",height:546,width:1512,blurWidth:0,blurHeight:0}},77030:t=>{"use strict";t.exports=require("node:net")},77598:t=>{"use strict";t.exports=require("node:crypto")},79551:t=>{"use strict";t.exports=require("url")},79646:t=>{"use strict";t.exports=require("child_process")},80481:t=>{"use strict";t.exports=require("node:readline")},82049:(t,e)=>{"use strict";function i(t){let{widthInt:e,heightInt:i,blurWidth:s,blurHeight:r,blurDataURL:n,objectFit:o}=t,a=s?40*s:e,l=r?40*r:i,u=a&&l?"viewBox='0 0 "+a+" "+l+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+u+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(u?"none":"contain"===o?"xMidYMid":"cover"===o?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+n+"'/%3E%3C/svg%3E"}Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"getImageBlurSvg",{enumerable:!0,get:function(){return i}})},83528:(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{default:function(){return l},getImageProps:function(){return a}});let s=i(32446),r=i(26634),n=i(2150),o=s._(i(85692));function a(t){let{props:e}=(0,r.getImgProps)(t,{defaultLoader:o.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[t,i]of Object.entries(e))void 0===i&&delete e[t];return{props:e}}let l=n.Image},83997:t=>{"use strict";t.exports=require("tty")},84297:t=>{"use strict";t.exports=require("async_hooks")},85692:(t,e)=>{"use strict";function i(t){var e;let{config:i,src:s,width:r,quality:n}=t,o=n||(null==(e=i.qualities)?void 0:e.reduce((t,e)=>Math.abs(e-75)<Math.abs(t-75)?e:t))||75;return i.path+"?url="+encodeURIComponent(s)+"&w="+r+"&q="+o+(s.startsWith("/_next/static/media/"),"")}Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"default",{enumerable:!0,get:function(){return s}}),i.__next_img_default=!0;let s=i},86592:t=>{"use strict";t.exports=require("node:inspector")},94735:t=>{"use strict";t.exports=require("events")}};var e=require("../../../webpack-runtime.js");e.C(t);var i=t=>e(e.s=t),s=e.X(0,[5319,3396,415,2644,6784,6239,1121,7170,9752,4788],()=>i(64426));module.exports=s})();