(()=>{var e={};e.id=1972,e.ids=[1972],e.modules={2150:(e,t,i)=>{let{createProxy:s}=i(20867);e.exports=s("C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\node_modules\\.pnpm\\next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f\\node_modules\\next\\dist\\client\\image-component.js")},2391:(e,t,i)=>{"use strict";i.d(t,{HashedKeys:()=>r});var s=i(6340);let r=(0,s.registerClientReference)(function(){throw Error("Attempted to call HashedKeys() from the server but HashedKeys is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\web\\components\\hashed-keys.tsx","HashedKeys");(0,s.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\2 FOLDERS FOR CUBENT\\\\Cubentweb\\\\cubent\\\\apps\\\\web\\\\components\\\\hashed-keys.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\web\\components\\hashed-keys.tsx","default")},2659:(e,t,i)=>{"use strict";i.d(t,{AuditLogs:()=>f});var s=i(99730),r=i(35371),n=i(70022),a=i(48190),o=i(26647),l=i(19161);let h=(0,l.A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]),d=(0,l.A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]),c=(0,l.A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]);var u=i(81700);let p=({entry:e,className:t})=>(0,s.jsxs)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},className:(0,o.cn)("flex items-start space-x-3 rounded-lg border p-3 backdrop-blur-sm",(()=>{switch(e.status){case"success":return"border-green-400/20 bg-green-400/5";case"warning":return"border-yellow-400/20 bg-yellow-400/5";case"error":return"border-red-400/20 bg-red-400/5";default:return"border-blue-400/20 bg-blue-400/5"}})(),t),children:[(0,s.jsx)("div",{className:"flex-shrink-0 mt-0.5",children:(()=>{switch(e.status){case"success":return(0,s.jsx)(h,{className:"h-4 w-4 text-green-400"});case"warning":return(0,s.jsx)(d,{className:"h-4 w-4 text-yellow-400"});case"error":return(0,s.jsx)(c,{className:"h-4 w-4 text-red-400"});default:return(0,s.jsx)(u.A,{className:"h-4 w-4 text-blue-400"})}})()}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-white truncate",children:e.action}),(0,s.jsx)("p",{className:"text-xs text-white/60 flex-shrink-0 ml-2",children:e.timestamp})]}),(0,s.jsxs)("p",{className:"text-xs text-white/70 mt-1",children:[e.user," • ",e.resource]}),(0,s.jsx)("p",{className:"text-xs text-white/60 mt-1",children:e.details})]})]}),f=({className:e,maxEntries:t=5})=>{let[i,n]=(0,r.useState)([]),l=[{id:"1",timestamp:"2 min ago",action:"API Key Created",user:"<EMAIL>",resource:"prod-api-key-123",status:"success",details:"New production API key generated with full permissions"},{id:"2",timestamp:"5 min ago",action:"Rate Limit Exceeded",user:"api-user-456",resource:"endpoint:/api/v1/users",status:"warning",details:"Rate limit of 1000 req/min exceeded, requests throttled"},{id:"3",timestamp:"8 min ago",action:"Authentication Failed",user:"unknown",resource:"api-key-789",status:"error",details:"Invalid API key used for authentication attempt"},{id:"4",timestamp:"12 min ago",action:"Permission Updated",user:"<EMAIL>",resource:"user-permissions",status:"success",details:"Updated API access permissions for development team"},{id:"5",timestamp:"15 min ago",action:"Key Revoked",user:"<EMAIL>",resource:"compromised-key-101",status:"warning",details:"API key revoked due to security policy violation"}];return(0,r.useEffect)(()=>{let e=0,i=setInterval(()=>{e<l.length?(n(i=>[l[e],...i.slice(0,t-1)]),e++):(e=0,n([]))},2e3);return()=>clearInterval(i)},[t]),(0,s.jsxs)("div",{className:(0,o.cn)("space-y-3",e),children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,s.jsx)(u.A,{className:"h-5 w-5 text-blue-400"}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Audit Logs"}),(0,s.jsx)("div",{className:"flex-1"}),(0,s.jsx)("div",{className:"text-xs text-white/60",children:"Live updates"})]}),(0,s.jsx)("div",{className:"space-y-2 max-h-80 overflow-hidden",children:(0,s.jsx)(a.N,{mode:"popLayout",children:i.map(e=>(0,s.jsx)(p,{entry:e},e.id))})}),0===i.length&&(0,s.jsxs)("div",{className:"text-center py-8 text-white/60",children:[(0,s.jsx)(u.A,{className:"h-8 w-8 mx-auto mb-2 opacity-50"}),(0,s.jsx)("p",{className:"text-sm",children:"Loading audit logs..."})]})]})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3922:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>s});let s={src:"/_next/static/media/ip.5d4ed2b5.svg",height:840,width:840,blurWidth:0,blurHeight:0}},4573:e=>{"use strict";e.exports=require("node:buffer")},8086:e=>{"use strict";e.exports=require("module")},9081:(e,t,i)=>{"use strict";i.d(t,{CodeExamples:()=>eu});var s={};i.r(s),i.d(s,{SJ:()=>h,Pc:()=>d,WL:()=>c,un:()=>u,I:()=>p,NY:()=>f});var r=i(99730),n=i(42849),a=i(11252),o=i(35371),l=i.n(o);let h=({className:e})=>(0,r.jsxs)("svg",{className:e,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,r.jsx)("path",{d:"M12 2L2 7L12 12L22 7L12 2Z",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),(0,r.jsx)("path",{d:"M2 17L12 22L22 17",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),(0,r.jsx)("path",{d:"M2 12L12 17L22 12",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})]}),d=({className:e})=>(0,r.jsxs)("svg",{className:e,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,r.jsx)("path",{d:"M12 2C12 2 8 6 8 12C8 16.4183 10.2386 18 12 18C13.7614 18 16 16.4183 16 12C16 6 12 2 12 2Z",fill:"currentColor"}),(0,r.jsx)("path",{d:"M12 18C12 18 16 14 16 8C16 3.58172 13.7614 2 12 2C10.2386 2 8 3.58172 8 8C8 14 12 18 12 18Z",fill:"currentColor",opacity:"0.6"})]}),c=({className:e})=>(0,r.jsxs)("svg",{className:e,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,r.jsx)("path",{d:"M3 12H21M12 3V21",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),(0,r.jsx)("circle",{cx:"12",cy:"12",r:"9",stroke:"currentColor",strokeWidth:"2",fill:"none"})]}),u=({className:e})=>(0,r.jsxs)("svg",{className:e,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,r.jsx)("path",{d:"M12 2C14.2091 2 16 3.79086 16 6V10H8V12H18C20.2091 12 22 13.7909 22 16C22 18.2091 20.2091 20 18 20H16V18C16 15.7909 14.2091 14 12 14H8C5.79086 14 4 12.2091 4 10V6C4 3.79086 5.79086 2 8 2H12Z",fill:"currentColor",opacity:"0.6"}),(0,r.jsx)("circle",{cx:"10",cy:"6",r:"1",fill:"currentColor"}),(0,r.jsx)("circle",{cx:"14",cy:"18",r:"1",fill:"currentColor"})]}),p=({className:e})=>(0,r.jsxs)("svg",{className:e,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,r.jsx)("path",{d:"M12 2L15.09 8.26L22 9L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9L8.91 8.26L12 2Z",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",fill:"none"}),(0,r.jsx)("circle",{cx:"12",cy:"12",r:"3",stroke:"currentColor",strokeWidth:"2",fill:"none"})]}),f=({className:e})=>(0,r.jsxs)("svg",{className:e,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,r.jsx)("rect",{x:"2",y:"2",width:"20",height:"20",rx:"2",fill:"currentColor",opacity:"0.1"}),(0,r.jsx)("path",{d:"M8 8H16M12 8V16",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),(0,r.jsx)("path",{d:"M16 12H18C19.1046 12 20 12.8954 20 14C20 15.1046 19.1046 16 18 16H16",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})]});var m=i(26647);let x=({children:e,className:t})=>(0,r.jsxs)("div",{className:(0,m.cn)("relative overflow-hidden rounded-lg border border-white/10 bg-black/50 backdrop-blur-sm",t),children:[(0,r.jsxs)("div",{className:"flex items-center justify-between border-b border-white/10 px-4 py-2",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"h-3 w-3 rounded-full bg-red-500"}),(0,r.jsx)("div",{className:"h-3 w-3 rounded-full bg-yellow-500"}),(0,r.jsx)("div",{className:"h-3 w-3 rounded-full bg-green-500"})]}),(0,r.jsx)("div",{className:"text-xs text-white/60",children:"Terminal"})]}),(0,r.jsx)("div",{className:"p-4 font-mono text-sm",children:e})]});var g=i(44669),y=i(60628);let v=({code:e,className:t})=>{let[i,s]=(0,o.useState)(!1),n=async()=>{try{await navigator.clipboard.writeText(e),s(!0),setTimeout(()=>s(!1),2e3)}catch(e){console.error("Failed to copy code:",e)}};return(0,r.jsx)("button",{onClick:n,className:(0,m.cn)("inline-flex items-center justify-center rounded-md p-2 text-white/60 transition-colors hover:bg-white/10 hover:text-white focus:outline-none focus:ring-2 focus:ring-white/20",t),title:i?"Copied!":"Copy code",children:i?(0,r.jsx)(g.A,{className:"h-4 w-4 text-green-400"}):(0,r.jsx)(y.A,{className:"h-4 w-4"})})};var w=i(56827),j=i(46769),b=i(1493),_=i(74794),k=i(86552),C=i(56750),S=i(44804),M=i(58785),O=i(42703),V="Tabs",[A,B]=(0,b.A)(V,[_.RG]),P=(0,_.RG)(),[L,N]=A(V),T=o.forwardRef((e,t)=>{let{__scopeTabs:i,value:s,onValueChange:n,defaultValue:a,orientation:o="horizontal",dir:l,activationMode:h="automatic",...d}=e,c=(0,S.jH)(l),[u,p]=(0,M.i)({prop:s,onChange:n,defaultProp:a??"",caller:V});return(0,r.jsx)(L,{scope:i,baseId:(0,O.B)(),value:u,onValueChange:p,orientation:o,dir:c,activationMode:h,children:(0,r.jsx)(C.sG.div,{dir:c,"data-orientation":o,...d,ref:t})})});T.displayName=V;var E="TabsList",R=o.forwardRef((e,t)=>{let{__scopeTabs:i,loop:s=!0,...n}=e,a=N(E,i),o=P(i);return(0,r.jsx)(_.bL,{asChild:!0,...o,orientation:a.orientation,dir:a.dir,loop:s,children:(0,r.jsx)(C.sG.div,{role:"tablist","aria-orientation":a.orientation,...n,ref:t})})});R.displayName=E;var I="TabsTrigger",F=o.forwardRef((e,t)=>{let{__scopeTabs:i,value:s,disabled:n=!1,...a}=e,o=N(I,i),l=P(i),h=D(o.baseId,s),d=H(o.baseId,s),c=s===o.value;return(0,r.jsx)(_.q7,{asChild:!0,...l,focusable:!n,active:c,children:(0,r.jsx)(C.sG.button,{type:"button",role:"tab","aria-selected":c,"aria-controls":d,"data-state":c?"active":"inactive","data-disabled":n?"":void 0,disabled:n,id:h,...a,ref:t,onMouseDown:(0,j.m)(e.onMouseDown,e=>{n||0!==e.button||!1!==e.ctrlKey?e.preventDefault():o.onValueChange(s)}),onKeyDown:(0,j.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&o.onValueChange(s)}),onFocus:(0,j.m)(e.onFocus,()=>{let e="manual"!==o.activationMode;c||n||!e||o.onValueChange(s)})})})});F.displayName=I;var U="TabsContent";function D(e,t){return`${e}-trigger-${t}`}function H(e,t){return`${e}-content-${t}`}o.forwardRef((e,t)=>{let{__scopeTabs:i,value:s,forceMount:n,children:a,...l}=e,h=N(U,i),d=D(h.baseId,s),c=H(h.baseId,s),u=s===h.value,p=o.useRef(u);return o.useEffect(()=>{let e=requestAnimationFrame(()=>p.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,r.jsx)(k.C,{present:n||u,children:({present:i})=>(0,r.jsx)(C.sG.div,{"data-state":u?"active":"inactive","data-orientation":h.orientation,role:"tabpanel","aria-labelledby":d,hidden:!i,id:c,tabIndex:0,...l,ref:t,style:{...e.style,animationDuration:p.current?"0s":void 0},children:i&&a})})}).displayName=U;var G=i(25741),z=i(41265),q=i.n(z);let K={plain:{color:"#F8F8F2",backgroundColor:"#282A36"},styles:[{types:["keyword"],style:{color:"#9D72FF"}},{types:["function"],style:{color:"#FB3186"}},{types:["string"],style:{color:"#3CEEAE"}},{types:["string-property"],style:{color:"#9D72FF"}},{types:["number"],style:{color:"#FB3186"}},{types:["comment"],style:{color:"#4D4D4D"}}]},W=`import { verifyKey } from '@unkey/api';

const { result, error } = await verifyKey({
  apiId: "api_123",
  key: "xyz_123"
})

if ( error ) {
  // handle network error
}

if ( !result.valid ) {
  // reject unauthorized request
}

// handle request`,Z=`import { withUnkey } from '@unkey/nextjs';
export const POST = withUnkey(async (req) => {
  // Process the request here
  // You have access to the typed verification response using \`req.unkey\`
  console.log(req.unkey);
  return new Response('Your API key is valid!');
});`,$=`export default defineEventHandler(async (event) => {
  if (!event.context.unkey.valid) {
    throw createError({ statusCode: 403, message: "Invalid API key" })
  }

  // return authorised information
  return {
    // ...
  };
});`,Y=`import asyncio
import os
import unkey

async def main() -> None:
  client = unkey.Client(api_key=os.environ["API_KEY"])
  await client.start()

  result = await client.keys.verify_key("prefix_abc123")

 if result.is_ok:
   print(data.valid)
 else:
   print(result.unwrap_err())`,X=`import os
from typing import Any, Dict, Optional

import fastapi  # pip install fastapi
import unkey  # pip install unkey.py
import uvicorn  # pip install uvicorn

app = fastapi.FastAPI()


def key_extractor(*args: Any, **kwargs: Any) -> Optional[str]:
    if isinstance(auth := kwargs.get("authorization"), str):
        return auth.split(" ")[-1]

    return None


@app.get("/protected")
@unkey.protected(os.environ["UNKEY_API_ID"], key_extractor)
async def protected_route(
    *,
    authorization: str = fastapi.Header(None),
    unkey_verification: Any = None,
) -> Dict[str, Optional[str]]:
    assert isinstance(unkey_verification, unkey.ApiKeyVerification)
    assert unkey_verification.valid
    print(unkey_verification.owner_id)
    return {"message": "protected!"}


if __name__ == "__main__":
    uvicorn.run(app)
`,J=`import { Hono } from "hono"
import { UnkeyContext, unkey } from "@unkey/hono";

const app = new Hono<{ Variables: { unkey: UnkeyContext } }>();
app.use("*", unkey());

app.get("/somewhere", (c) => {
  // access the unkey response here to get metadata of the key etc
  const unkey = c.get("unkey")
 return c.text("yo")
})`,Q=`import { Ratelimit } from "@unkey/ratelimit"

const unkey = new Ratelimit({
  rootKey: process.env.UNKEY_ROOT_KEY,
  namespace: "my-app",
  limit: 10,
  duration: "30s",
  async: true
})

// elsewhere
async function handler(request) {
  const identifier = request.getUserId() // or ip or anything else you want

  const ratelimit = await unkey.limit(identifier)
  if (!ratelimit.success){
    return new Response("try again later", { status: 429 })
  }

  // handle the request here

}`,ee=`package main

import(
	unkeygo "github.com/unkeyed/unkey-go"
	"context"
	"github.com/unkeyed/unkey-go/models/components"
	"log"
)

func main() {
    s := unkeygo.New(
        unkeygo.WithSecurity("<YOUR_BEARER_TOKEN_HERE>"),
    )

    ctx := context.Background()
    res, err := s.Keys.VerifyKey(ctx, components.V1KeysVerifyKeyRequest{
        APIID: unkeygo.String("api_1234"),
        Key: "sk_1234",
        Ratelimits: []components.Ratelimits{
            components.Ratelimits{
                Name: "tokens",
                Limit: unkeygo.Int64(500),
                Duration: unkeygo.Int64(3600000),
            },
            components.Ratelimits{
                Name: "tokens",
                Limit: unkeygo.Int64(20000),
                Duration: unkeygo.Int64(86400000),
            },
        },
    })
    if err != nil {
        log.Fatal(err)
    }
    if res.V1KeysVerifyKeyResponse != nil {
        // handle response
    }
}`,et=`package main

import(
	unkeygo "github.com/unkeyed/unkey-go"
	"context"
	"github.com/unkeyed/unkey-go/models/operations"
	"log"
)

func main() {
    s := unkeygo.New(
        unkeygo.WithSecurity("<YOUR_BEARER_TOKEN_HERE>"),
    )

    ctx := context.Background()
    res, err := s.Keys.CreateKey(ctx, operations.CreateKeyRequestBody{
        APIID: "api_123",
        Name: unkeygo.String("my key"),
        ExternalID: unkeygo.String("team_123"),
        Meta: map[string]any{
            "billingTier": "PRO",
            "trialEnds": "2023-06-16T17:16:37.161Z",
        },
        Roles: []string{
            "admin",
            "finance",
        },
        Permissions: []string{
            "domains.create_record",
            "say_hello",
        },
        Expires: unkeygo.Int64(1623869797161),
        Remaining: unkeygo.Int64(1000),
        Refill: &operations.Refill{
            Interval: operations.IntervalDaily,
            Amount: 100,
        },
        Ratelimit: &operations.Ratelimit{
            Type: operations.TypeFast.ToPointer(),
            Limit: 10,
            Duration: unkeygo.Int64(60000),
        },
        Enabled: unkeygo.Bool(false),
    })
    if err != nil {
        log.Fatal(err)
    }
    if res.Object != nil {
        // handle response
    }
}

`,ei=`curl --request POST \\
  --url https://api.unkey.dev/v1/keys.verifyKey \\
  --header 'Content-Type: application/json' \\
  --data '{
    "apiId": "api_1234",
    "key": "sk_1234",
  }'`,es=`curl --request POST \\
  --url https://api.unkey.dev/v1/keys.createKey \\
  --header 'Authorization: Bearer <UNKEY_ROOT_KEY>' \\
  --header 'Content-Type: application/json' \\
  --data '{
    "apiId": "api_123",
    "ownerId": "user_123",
    "expires": ${Date.now()+6048e5},
    "ratelimit": {
      "type": "fast",
      "limit": 10,
      "duration": 60_000
    },
  }'`,er=`curl --request POST \
  --url https://api.unkey.dev/v1/ratelimits.limit \
  --header 'Authorization: Bearer <token>' \
  --header 'Content-Type: application/json' \
  --data '{
    "namespace": "email.outbound",
    "identifier": "user_123",
    "limit": 10,
    "duration": 60000,
    "async": true
}'`,en=`UnkeyElixirSdk.verify_key("xyz_AS5HDkXXPot2MMoPHD8jnL")
# returns
%{"valid" => true,
  "ownerId" => "chronark",
  "meta" => %{
    "hello" => "world"
  }}`,ea=`use unkey::models::{VerifyKeyRequest, Wrapped};
use unkey::Client;

async fn verify_key() {
    let api_key = env::var("UNKEY_API_KEY").expect("Environment variable UNKEY_API_KEY not found");
    let c = Client::new(&api_key);
    let req = VerifyKeyRequest::new("test_req", "api_458vdYdbwut5LWABzXZP3Z8jPVas");

    match c.verify_key(req).await {
        Wrapped::Ok(res) => println!("{res:?}"),
        Wrapped::Err(err) => eprintln!("{err:?}"),
    }
}`,eo=`package com.example.myapp;
import com.unkey.unkeysdk.dto.KeyVerifyRequest;
import com.unkey.unkeysdk.dto.KeyVerifyResponse;

@RestController
public class APIController {

    private static IKeyService keyService = new KeyService();

    @PostMapping("/verify")
    public KeyVerifyResponse verifyKey(
        @RequestBody KeyVerifyRequest keyVerifyRequest) {
        // Delegate the creation of the key to the KeyService from the SDK
        return keyService.verifyKey(keyVerifyRequest);
    }
}`,el=`package com.example.myapp;

import com.unkey.unkeysdk.dto.KeyCreateResponse;
import com.unkey.unkeysdk.dto.KeyCreateRequest;

@RestController
public class APIController {

    private static IKeyService keyService = new KeyService();

    @PostMapping("/createKey")
    public KeyCreateResponse createKey(
            @RequestBody KeyCreateRequest keyCreateRequest,
            @RequestHeader("Authorization") String authToken) {
        // Delegate the creation of the key to the KeyService from the SDK
        return keyService.createKey(keyCreateRequest, authToken);
    }
}

`,eh={Typescript:[{name:"Typescript",Icon:f,codeBlock:W,editorLanguage:"tsx"},{name:"Next.js",Icon:f,codeBlock:Z,editorLanguage:"tsx"},{name:"Nuxt",codeBlock:$,Icon:f,editorLanguage:"tsx"},{name:"Hono",Icon:f,codeBlock:J,editorLanguage:"tsx"},{name:"Ratelimiting",Icon:f,codeBlock:Q,editorLanguage:"tsx"}],Python:[{name:"Python",Icon:u,codeBlock:Y,editorLanguage:"python"},{name:"FastAPI",Icon:u,codeBlock:X,editorLanguage:"python"}],Golang:[{name:"Verify key",Icon:c,codeBlock:ee,editorLanguage:"go"},{name:"Create key",Icon:c,codeBlock:et,editorLanguage:"go"}],Java:[{name:"Verify key",Icon:s.JavaIcon,codeBlock:eo,editorLanguage:"tsx"},{name:"Create key",Icon:s.JavaIcon,codeBlock:el,editorLanguage:"tsx"}],Elixir:[{name:"Verify key",Icon:d,codeBlock:en,editorLanguage:"tsx"}],Rust:[{name:"Verify key",Icon:p,codeBlock:ea,editorLanguage:"rust"}],Curl:[{name:"Verify key",Icon:h,codeBlock:ei,editorLanguage:"tsx"},{name:"Create key",Icon:h,codeBlock:es,editorLanguage:"tsx"},{name:"Ratelimit",Icon:h,codeBlock:er,editorLanguage:"tsx"}]},ed=[{name:"Typescript",Icon:f},{name:"Python",Icon:u},{name:"Rust",Icon:p},{name:"Golang",Icon:c},{name:"Curl",Icon:h},{name:"Elixir",Icon:d},{name:"Java",Icon:s.JavaIcon}],ec=l().forwardRef(({className:e,value:t,...i},s)=>(0,r.jsx)(F,{ref:s,value:t,className:(0,m.cn)("inline-flex items-center gap-1 justify-center whitespace-nowrap rounded-t-lg px-3  py-1.5 text-sm transition-all hover:text-white/80 disabled:pointer-events-none disabled:opacity-50 bg-gradient-to-t from-black to-black data-[state=active]:from-white/10 border border-b-0 text-white/30 data-[state=active]:text-white border-[#454545] font-light",e),...i}));ec.displayName=F.displayName;let eu=({className:e})=>{let[t,i]=(0,o.useState)("Typescript"),[s,l]=(0,o.useState)("Typescript"),[h,d]=(0,o.useState)("Typescript");function c({language:e,framework:t}){let i=eh[e].find(e=>e.name===t);return i?.codeBlock||""}return(0,o.useEffect)(()=>{l(eh[t].at(0).name)},[t]),(0,r.jsxs)("section",{className:e,children:[(0,r.jsxs)(a._,{title:"Any language, any framework, always secure",text:"Simplify API security and access control with Unkey's developer-friendly platform. Our SDKs, intuitive REST API, and public OpenAPI spec make it easy to secure your APIs without complex configurations.",align:"center",className:"relative",children:[(0,r.jsxs)("div",{className:"absolute bottom-32 left-[-50px]",children:[(0,r.jsx)(w.Aj,{className:"ml-2 fade-in-0",delay:3,number:1}),(0,r.jsx)(w.Aj,{className:"ml-10 fade-in-40",delay:0,number:1}),(0,r.jsx)(w.Aj,{className:"ml-16 fade-in-100",delay:5,number:1})]}),(0,r.jsxs)("div",{className:"absolute bottom-32 right-[200px]",children:[(0,r.jsx)(w.Aj,{className:"ml-2 fade-in-0",delay:4,number:1}),(0,r.jsx)(w.Aj,{className:"ml-10 fade-in-40",delay:0,number:1}),(0,r.jsx)(w.Aj,{className:"ml-16 fade-in-100",delay:2,number:1})]}),(0,r.jsx)("div",{className:"mt-10",children:(0,r.jsxs)("div",{className:"flex gap-6 pb-14",children:[(0,r.jsx)(q(),{href:"https://app.unkey.com",children:(0,r.jsx)(n.j,{shiny:!0,label:"Get Started",IconRight:G.A})},"get-started"),(0,r.jsx)(q(),{href:"/docs",children:(0,r.jsx)(n.t,{label:"Visit the docs",IconRight:G.A})},"docs")]})})]}),(0,r.jsxs)("div",{className:"relative w-full rounded-4xl border-[.75px] border-white/10 bg-gradient-to-b from-[#111111] to-black border-t-[.75px] border-t-white/20",children:[(0,r.jsx)("div",{"aria-hidden":!0,className:"absolute pointer-events-none inset-x-16 h-[432px] bottom-[calc(100%-2rem)] bg-[radial-gradient(94.69%_94.69%_at_50%_100%,rgba(255,255,255,0.20)_0%,rgba(255,255,255,0)_55.45%)]"}),(0,r.jsx)(T,{defaultValue:t,onValueChange:e=>i(e),className:"relative flex items-end h-16 px-4 border rounded-tr-3xl rounded-tl-3xl border-white/10 editor-top-gradient",children:(0,r.jsx)(R,{className:"flex items-end gap-4 overflow-x-auto scrollbar-hidden",children:ed.map(({name:e,Icon:i})=>(0,r.jsxs)(ec,{onMouseEnter:()=>d(e),onMouseLeave:()=>d(t),value:e,children:[(0,r.jsx)(i,{active:h===e||t===e}),e]},e))})}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row overflow-x-auto scrollbar-hidden sm:h-[520px]",children:[(0,r.jsx)(ep,{frameworks:eh[t],currentFramework:s,setFramework:l}),(0,r.jsxs)("div",{className:"relative flex w-full pt-4 pb-8 pl-8 font-mono text-xs text-white sm:text-sm",children:[(0,r.jsx)(x,{language:function({language:e,framework:t}){let i=eh[e].find(e=>e.name===t);return i?.editorLanguage||"tsx"}({language:t,framework:s}),theme:K,codeBlock:c({language:t,framework:s})}),(0,r.jsx)(v,{textToCopy:c({language:t,framework:s}),className:"absolute hidden cursor-pointer top-5 right-5 lg:flex"})]})]})]})]})};function ep({frameworks:e,currentFramework:t,setFramework:i}){return(0,r.jsx)("div",{className:"flex flex-col justify-between sm:w-[216px] text-white text-sm pt-6 px-4 font-mono md:border-r md:border-white/10",children:(0,r.jsx)("div",{className:"flex items-center space-x-2 sm:flex-col sm:space-x-0 sm:space-y-2",children:e.map(e=>(0,r.jsx)("button",{type:"button",onClick:()=>{i(e.name)},className:(0,m.cn)("flex items-center cursor-pointer hover:bg-white/10 py-1 px-2 rounded-lg w-[184px] ",{"bg-white/10 text-white":t===e.name,"text-white/40":t!==e.name}),children:(0,r.jsx)("div",{children:e.name})},e.name))})})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11252:(e,t,i)=>{"use strict";i.d(t,{_:()=>n});var s=i(99730),r=i(26647);function n({label:e,title:t,text:i,align:n="left",children:a,className:o}){return(0,s.jsxs)("div",{className:(0,r.cn)("flex flex-col items-center",{"xl:items-start":"left"===n},o),children:[(0,s.jsx)("span",{className:(0,r.cn)("font-mono text-sm md:text-md text-white/50 text-center",{"xl:text-left":"left"===n}),children:e}),(0,s.jsx)("h2",{className:(0,r.cn)("text-[28px] sm:pb-3 sm:text-[52px] sm:leading-[64px] text-white text-pretty max-w-sm md:max-w-md lg:max-w-2xl xl:max-w-4xl via-30/%  pt-4 font-medium bg-gradient-to-br text-transparent bg-gradient-stop bg-clip-text from-white via-white to-white/30 text-center leading-none",{"xl:text-left":"left"===n}),children:t}),i&&(0,s.jsx)("p",{className:(0,r.cn)("text-sm md:text-base text-white/70 leading-7 py-6 text-center max-w-sm md:max-w-md lg:max-w-xl xl:max-w-4xl text-balance",{"xl:text-left xl:max-w-xl":"left"===n}),children:i}),a]})}},11737:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>s});let s={src:"/_next/static/media/map.aa68879d.svg",height:440,width:960,blurWidth:0,blurHeight:0}},13964:(e,t,i)=>{"use strict";i.d(t,{CTA:()=>d});var s=i(99730),r=i(11252);let n=(0,i(19161).A)("calendar-days",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}],["path",{d:"M8 14h.01",key:"6423bh"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 18h.01",key:"lrp35t"}],["path",{d:"M12 18h.01",key:"mhygvu"}],["path",{d:"M16 18h.01",key:"kzsmim"}]]);var a=i(25741),o=i(41265),l=i.n(o),h=i(42849);let d=()=>(0,s.jsx)("div",{className:"w-full h-full overflow-hidden",children:(0,s.jsxs)("div",{className:"relative pb-40 pt-14 ",children:[(0,s.jsx)(c,{className:"absolute inset-x-0 w-full mx-auto pointer-events-none -bottom-80 max-sm:w-8"}),(0,s.jsx)(r._,{align:"center",title:(0,s.jsxs)(s.Fragment,{children:["Protect your API.",(0,s.jsx)("br",{})," Start today."]}),children:(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center gap-6 mt-2 sm:mt-5 sm:flex-row",children:[(0,s.jsx)(l(),{target:"_blank",href:"https://cal.com/team/unkey/user-interview?utm_source=banner&utm_campaign=oss",children:(0,s.jsx)(h.t,{label:"Chat with us",IconRight:n})}),(0,s.jsx)(l(),{href:"https://app.unkey.com",children:(0,s.jsx)(h.j,{shiny:!0,label:"Start Now",IconRight:a.A})})]})}),(0,s.jsx)("div",{className:"mt-8 sm:mt-10 text-balance",children:(0,s.jsx)("p",{className:"w-full mx-auto text-sm leading-6 text-center text-white/60 max-w-[500px]",children:"150,000 requests per month. No CC required."})})]})}),c=({className:e})=>(0,s.jsxs)("svg",{className:e,width:"944",height:"1033",viewBox:"0 0 944 1033",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,s.jsxs)("g",{opacity:"0.3",children:[(0,s.jsx)("g",{style:{mixBlendMode:"lighten"},filter:"url(#filter0_f_2076_3208)",children:(0,s.jsx)("ellipse",{cx:"574.307",cy:"568.208",rx:"32.7783",ry:"293.346",transform:"rotate(-164.946 574.307 568.208)",fill:"url(#paint0_linear_2076_3208)",fillOpacity:"0.5"})}),(0,s.jsx)("g",{style:{mixBlendMode:"color-dodge"},filter:"url(#filter1_f_2076_3208)",children:(0,s.jsx)("ellipse",{cx:"468.5",cy:"589.25",rx:"26.5",ry:"293.25",transform:"rotate(180 468.5 589.25)",fill:"url(#paint1_linear_2076_3208)",fillOpacity:"0.5"})})]}),(0,s.jsxs)("defs",{children:[(0,s.jsxs)("filter",{id:"filter0_f_2076_3208",x:"402.782",y:"195.799",width:"343.05",height:"744.818",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB",children:[(0,s.jsx)("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),(0,s.jsx)("feBlend",{mode:"normal",in:"SourceGraphic",in2:"BackgroundImageFix",result:"shape"}),(0,s.jsx)("feGaussianBlur",{stdDeviation:"44.5",result:"effect1_foregroundBlur_2076_3208"})]}),(0,s.jsxs)("filter",{id:"filter1_f_2076_3208",x:"353",y:"207",width:"231",height:"764.5",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB",children:[(0,s.jsx)("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),(0,s.jsx)("feBlend",{mode:"normal",in:"SourceGraphic",in2:"BackgroundImageFix",result:"shape"}),(0,s.jsx)("feGaussianBlur",{stdDeviation:"44.5",result:"effect1_foregroundBlur_2076_3208"})]}),(0,s.jsxs)("linearGradient",{id:"paint0_linear_2076_3208",x1:"574.307",y1:"274.862",x2:"574.307",y2:"861.554",gradientUnits:"userSpaceOnUse",children:[(0,s.jsx)("stop",{stopColor:"white"}),(0,s.jsx)("stop",{offset:"1",stopColor:"white",stopOpacity:"0"})]}),(0,s.jsxs)("linearGradient",{id:"paint1_linear_2076_3208",x1:"468.5",y1:"296",x2:"468.5",y2:"882.5",gradientUnits:"userSpaceOnUse",children:[(0,s.jsx)("stop",{stopColor:"white"}),(0,s.jsx)("stop",{offset:"1",stopColor:"white",stopOpacity:"0"})]})]})]})},15110:(e,t,i)=>{"use strict";i.d(t,{Q:()=>s});let s=(0,i(35371).createContext)({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"})},16156:(e,t,i)=>{"use strict";i.d(t,{AuditLogs:()=>r});var s=i(6340);let r=(0,s.registerClientReference)(function(){throw Error("Attempted to call AuditLogs() from the server but AuditLogs is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\web\\components\\audit\\audit-logs.tsx","AuditLogs");(0,s.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\2 FOLDERS FOR CUBENT\\\\Cubentweb\\\\cubent\\\\apps\\\\web\\\\components\\\\audit\\\\audit-logs.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\web\\components\\audit\\audit-logs.tsx","default")},18836:(e,t,i)=>{"use strict";i.d(t,{s:()=>r});var s=i(90043);function r(e){return(0,s.G)(e)&&"offsetHeight"in e}},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19257:(e,t,i)=>{"use strict";i.r(t),i.d(t,{showBetaFeature:()=>l});var s=i(51153);let r=(0,i(28364).H)(),n=r.NEXT_PUBLIC_POSTHOG_KEY&&r.NEXT_PUBLIC_POSTHOG_HOST?new s.f2(r.NEXT_PUBLIC_POSTHOG_KEY,{host:r.NEXT_PUBLIC_POSTHOG_HOST,flushAt:1,flushInterval:0}):null;var a=i(18639),o=i(8392);let l=(e=>(0,o.Jt)({key:e,defaultValue:!1,async decide(){let{userId:t}=await (0,a.j)();return t?(n?await n.isFeatureEnabled(e,t):null)??this.defaultValue:this.defaultValue}}))("showBetaFeature")},19583:(e,t,i)=>{Promise.resolve().then(i.bind(i,9081)),Promise.resolve().then(i.bind(i,2659)),Promise.resolve().then(i.bind(i,13964)),Promise.resolve().then(i.bind(i,38878)),Promise.resolve().then(i.bind(i,29190)),Promise.resolve().then(i.bind(i,41155)),Promise.resolve().then(i.bind(i,53361)),Promise.resolve().then(i.bind(i,50778)),Promise.resolve().then(i.bind(i,39890)),Promise.resolve().then(i.bind(i,11737)),Promise.resolve().then(i.t.bind(i,41265,23)),Promise.resolve().then(i.t.bind(i,53320,23))},21820:e=>{"use strict";e.exports=require("os")},23132:(e,t,i)=>{"use strict";i.r(t),i.d(t,{"00e578e70edcb55dedc8be97f84cdf9c4ce9b9fbb2":()=>s.z2,"405efdbdcc87c20941759ae1bb1e4f3629f6226400":()=>r.x,"6040ce2583939275b9b79a23c761210186958f33ca":()=>s.qr,"60ac4068834bf9e1b32327478a0425a5daded1d553":()=>s.xK,"60d7e646f4b8f4f2b31c38fcbc9963402a4f1620ad":()=>s.q2});var s=i(22589),r=i(18362)},24761:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>s});let s={src:"/_next/static/media/map.aa68879d.svg",height:440,width:960,blurWidth:0,blurHeight:0}},24767:(e,t,i)=>{"use strict";i.d(t,{A:()=>c});var s=i(23233);let r=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),n=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,i)=>i?i.toUpperCase():t.toLowerCase()),a=e=>{let t=n(e);return t.charAt(0).toUpperCase()+t.slice(1)},o=(...e)=>e.filter((e,t,i)=>!!e&&""!==e.trim()&&i.indexOf(e)===t).join(" ").trim(),l=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var h={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,s.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:i=2,absoluteStrokeWidth:r,className:n="",children:a,iconNode:d,...c},u)=>(0,s.createElement)("svg",{ref:u,...h,width:t,height:t,stroke:e,strokeWidth:r?24*Number(i)/Number(t):i,className:o("lucide",n),...!a&&!l(c)&&{"aria-hidden":"true"},...c},[...d.map(([e,t])=>(0,s.createElement)(e,t)),...Array.isArray(a)?a:[a]])),c=(e,t)=>{let i=(0,s.forwardRef)(({className:i,...n},l)=>(0,s.createElement)(d,{ref:l,iconNode:t,className:o(`lucide-${r(a(e))}`,`lucide-${e}`,i),...n}));return i.displayName=a(e),i}},25741:(e,t,i)=>{"use strict";i.d(t,{A:()=>s});let s=(0,i(19161).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},26634:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImgProps",{enumerable:!0,get:function(){return l}}),i(33159);let s=i(82049),r=i(56829),n=["-moz-initial","fill","none","scale-down",void 0];function a(e){return void 0!==e.default}function o(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function l(e,t){var i,l;let h,d,c,{src:u,sizes:p,unoptimized:f=!1,priority:m=!1,loading:x,className:g,quality:y,width:v,height:w,fill:j=!1,style:b,overrideSrc:_,onLoad:k,onLoadingComplete:C,placeholder:S="empty",blurDataURL:M,fetchPriority:O,decoding:V="async",layout:A,objectFit:B,objectPosition:P,lazyBoundary:L,lazyRoot:N,...T}=e,{imgConf:E,showAltText:R,blurComplete:I,defaultLoader:F}=t,U=E||r.imageConfigDefault;if("allSizes"in U)h=U;else{let e=[...U.deviceSizes,...U.imageSizes].sort((e,t)=>e-t),t=U.deviceSizes.sort((e,t)=>e-t),s=null==(i=U.qualities)?void 0:i.sort((e,t)=>e-t);h={...U,allSizes:e,deviceSizes:t,qualities:s}}if(void 0===F)throw Object.defineProperty(Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config"),"__NEXT_ERROR_CODE",{value:"E163",enumerable:!1,configurable:!0});let D=T.loader||F;delete T.loader,delete T.srcSet;let H="__next_img_default"in D;if(H){if("custom"===h.loader)throw Object.defineProperty(Error('Image with src "'+u+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader'),"__NEXT_ERROR_CODE",{value:"E252",enumerable:!1,configurable:!0})}else{let e=D;D=t=>{let{config:i,...s}=t;return e(s)}}if(A){"fill"===A&&(j=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[A];e&&(b={...b,...e});let t={responsive:"100vw",fill:"100vw"}[A];t&&!p&&(p=t)}let G="",z=o(v),q=o(w);if((l=u)&&"object"==typeof l&&(a(l)||void 0!==l.src)){let e=a(u)?u.default:u;if(!e.src)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E460",enumerable:!1,configurable:!0});if(!e.height||!e.width)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E48",enumerable:!1,configurable:!0});if(d=e.blurWidth,c=e.blurHeight,M=M||e.blurDataURL,G=e.src,!j)if(z||q){if(z&&!q){let t=z/e.width;q=Math.round(e.height*t)}else if(!z&&q){let t=q/e.height;z=Math.round(e.width*t)}}else z=e.width,q=e.height}let K=!m&&("lazy"===x||void 0===x);(!(u="string"==typeof u?u:G)||u.startsWith("data:")||u.startsWith("blob:"))&&(f=!0,K=!1),h.unoptimized&&(f=!0),H&&!h.dangerouslyAllowSVG&&u.split("?",1)[0].endsWith(".svg")&&(f=!0);let W=o(y),Z=Object.assign(j?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:B,objectPosition:P}:{},R?{}:{color:"transparent"},b),$=I||"empty"===S?null:"blur"===S?'url("data:image/svg+xml;charset=utf-8,'+(0,s.getImageBlurSvg)({widthInt:z,heightInt:q,blurWidth:d,blurHeight:c,blurDataURL:M||"",objectFit:Z.objectFit})+'")':'url("'+S+'")',Y=n.includes(Z.objectFit)?"fill"===Z.objectFit?"100% 100%":"cover":Z.objectFit,X=$?{backgroundSize:Y,backgroundPosition:Z.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:$}:{},J=function(e){let{config:t,src:i,unoptimized:s,width:r,quality:n,sizes:a,loader:o}=e;if(s)return{src:i,srcSet:void 0,sizes:void 0};let{widths:l,kind:h}=function(e,t,i){let{deviceSizes:s,allSizes:r}=e;if(i){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let s;s=e.exec(i);)t.push(parseInt(s[2]));if(t.length){let e=.01*Math.min(...t);return{widths:r.filter(t=>t>=s[0]*e),kind:"w"}}return{widths:r,kind:"w"}}return"number"!=typeof t?{widths:s,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>r.find(t=>t>=e)||r[r.length-1]))],kind:"x"}}(t,r,a),d=l.length-1;return{sizes:a||"w"!==h?a:"100vw",srcSet:l.map((e,s)=>o({config:t,src:i,quality:n,width:e})+" "+("w"===h?e:s+1)+h).join(", "),src:o({config:t,src:i,quality:n,width:l[d]})}}({config:h,src:u,unoptimized:f,width:z,quality:W,sizes:p,loader:D});return{props:{...T,loading:K?"lazy":x,fetchPriority:O,width:z,height:q,decoding:V,className:g,style:{...Z,...X},sizes:J.sizes,srcSet:J.srcSet,src:_||J.src},meta:{unoptimized:f,priority:m,placeholder:S,fill:j}}}},26647:(e,t,i)=>{"use strict";i.d(t,{cn:()=>n});var s=i(29085),r=i(63632);function n(...e){return(0,r.QP)((0,s.$)(e))}},26993:(e,t,i)=>{"use strict";i.d(t,{UsageBento:()=>r});var s=i(6340);let r=(0,s.registerClientReference)(function(){throw Error("Attempted to call UsageBento() from the server but UsageBento is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\web\\components\\usage-bento.tsx","UsageBento");(0,s.registerClientReference)(function(){throw Error("Attempted to call BillingItem() from the server but BillingItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\web\\components\\usage-bento.tsx","BillingItem"),(0,s.registerClientReference)(function(){throw Error("Attempted to call UsageText() from the server but UsageText is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\web\\components\\usage-bento.tsx","UsageText")},27356:(e,t,i)=>{"use strict";i.d(t,{xQ:()=>n});var s=i(35371),r=i(62431);function n(e=!0){let t=(0,s.useContext)(r.t);if(null===t)return[!0,null];let{isPresent:i,onExitComplete:a,register:o}=t,l=(0,s.useId)();(0,s.useEffect)(()=>{if(e)return o(l)},[e]);let h=(0,s.useCallback)(()=>e&&a&&a(l),[l,a,e]);return!i&&a?[!1,h]:[!0]}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29190:(e,t,i)=>{"use strict";i.d(t,{Hero:()=>x});var s=i(99730),r=i(70022),n=i(48473),a=i(41265),o=i.n(a),l=i(42849),h=i(19161);let d=(0,h.A)("log-in",[["path",{d:"m10 17 5-5-5-5",key:"1bsop3"}],["path",{d:"M15 12H3",key:"6jk70r"}],["path",{d:"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4",key:"u53s6r"}]]),c=(0,h.A)("book-open",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]]);var u=i(25741);function p(){return(0,s.jsxs)("div",{className:"relative flex flex-col items-center text-center ",children:[(0,s.jsx)("h1",{className:"bg-gradient-to-br text-balance text-transparent bg-gradient-stop bg-clip-text from-white via-white via-30% to-white/30  font-medium text-6xl leading-none xl:text-[82px] tracking-tighter",children:"The Developer Platform for Modern APIs"}),(0,s.jsx)("p",{className:"mt-6 sm:mt-8 bg-gradient-to-br text-transparent text-balance bg-gradient-stop bg-clip-text max-w-sm sm:max-w-lg xl:max-w-4xl from-white/70 via-white/70 via-40% to-white/30 text-base md:text-lg",children:"Easily integrate necessary API features like API keys, rate limiting, and usage analytics, ensuring your API is ready to scale."}),(0,s.jsxs)("div",{className:"flex items-center gap-6 mt-16",children:[(0,s.jsx)(o(),{href:"https://app.unkey.com",className:"group",children:(0,s.jsx)(l.j,{shiny:!0,IconLeft:d,label:"Get started",className:"h-10"})}),(0,s.jsx)(o(),{href:"/docs",className:"hidden sm:flex",children:(0,s.jsx)(l.t,{IconLeft:c,label:"Documentation",IconRight:u.A})})]})]})}var f=i(39890);let m=({className:e})=>(0,s.jsx)("div",{className:e}),x=()=>(0,s.jsxs)(r.P.div,{className:"relative w-full flex flex-col items-center justify-between mt-48",variants:{hidden:{},visible:{transition:{staggerChildren:.3}}},initial:"hidden",animate:"visible",children:[(0,s.jsx)(r.P.div,{variants:{hidden:{opacity:0,y:25},visible:{opacity:1,y:0,transition:{duration:.6,ease:"easeOut"}}},children:(0,s.jsx)(p,{})}),(0,s.jsx)("div",{children:(0,s.jsx)(n.default,{src:f.default,alt:"Animated SVG showing computer circuits lighting up",className:"absolute hidden xl:right-32 xl:flex -z-10 xl:-top-56",style:{transform:"scale(2)"},priority:!0})}),(0,s.jsx)(m,{className:"absolute hidden md:flex left-1/2 -translate-x-[calc(50%+85px)] -bottom-[224px] -z-10"})]})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29317:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>en,generateMetadata:()=>er});var s=i(94752);let r=()=>(0,s.jsx)("div",{className:"relative overflow-hidden bg-gradient-to-br from-gray-900 to-gray-800 rounded-2xl p-8 border border-gray-700",children:(0,s.jsxs)("div",{className:"relative z-10",children:[(0,s.jsx)("h3",{className:"text-2xl font-semibold text-white mb-4",children:"Analytics Dashboard"}),(0,s.jsx)("p",{className:"text-gray-300 mb-6",children:"Monitor your API usage with real-time analytics and insights."}),(0,s.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,s.jsxs)("div",{className:"bg-gray-800 rounded-lg p-4",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-white",children:"1.2M"}),(0,s.jsx)("div",{className:"text-sm text-gray-400",children:"Requests"})]}),(0,s.jsxs)("div",{className:"bg-gray-800 rounded-lg p-4",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-white",children:"99.9%"}),(0,s.jsx)("div",{className:"text-sm text-gray-400",children:"Uptime"})]}),(0,s.jsxs)("div",{className:"bg-gray-800 rounded-lg p-4",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-white",children:"45ms"}),(0,s.jsx)("div",{className:"text-sm text-gray-400",children:"Latency"})]})]})]})});var n=i(16156);function a(){return(0,s.jsxs)("div",{className:"relative group no-scrollbar overflow-hidden w-full xl:mt-10  border-[.75px] h-[520px] rounded-[32px] border-[#ffffff]/10 flex overflow-x-hidden bg-gradient-to-br from-white/10 to-black",children:[(0,s.jsx)(n.AuditLogs,{className:" sm:h-[400px] w-full sm:ml-[40px]"}),(0,s.jsx)("div",{className:"absolute inset-0 w-full h-full duration-500 pointer-events-none bg-gradient-to-tr from-black via-black/40 to-black/0 group-hover:opacity-0 group-hover:backdrop-blur-0"}),(0,s.jsx)("div",{className:"duration-500 group-hover:opacity-0 group-hover:pointer-events-none",children:(0,s.jsx)(o,{})})]})}function o(){return(0,s.jsxs)("div",{className:"flex flex-col text-white absolute left-[20px] sm:left-[40px] xl:left-[40px] bottom-[40px] max-w-[350px]",children:[(0,s.jsxs)("div",{className:"flex items-center w-full",children:[(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:(0,s.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M6.5 3H6.47812C5.7966 3 5.25458 2.99999 4.81729 3.03572C4.36949 3.07231 3.98765 3.14884 3.63803 3.32698C3.07354 3.6146 2.6146 4.07354 2.32698 4.63803C2.14884 4.98765 2.07231 5.36949 2.03572 5.81729C1.99999 6.25458 2 6.7966 2 7.47812V7.5V15.5V15.5219C2 16.2034 1.99999 16.7454 2.03572 17.1827C2.07231 17.6305 2.14884 18.0123 2.32698 18.362C2.6146 18.9265 3.07354 19.3854 3.63803 19.673C3.98765 19.8512 4.36949 19.9277 4.81729 19.9643C5.25457 20 5.79657 20 6.47806 20H6.47811H6.5H17.5H17.5219H17.5219C18.2034 20 18.7454 20 19.1827 19.9643C19.6305 19.9277 20.0123 19.8512 20.362 19.673C20.9265 19.3854 21.3854 18.9265 21.673 18.362C21.8512 18.0123 21.9277 17.6305 21.9643 17.1827C22 16.7454 22 16.2034 22 15.5219V15.5219V15.5V7.5V7.47811V7.47806C22 6.79657 22 6.25457 21.9643 5.81729C21.9277 5.36949 21.8512 4.98765 21.673 4.63803C21.3854 4.07354 20.9265 3.6146 20.362 3.32698C20.0123 3.14884 19.6305 3.07231 19.1827 3.03572C18.7454 2.99999 18.2034 3 17.5219 3H17.5H6.5ZM4.09202 4.21799C4.27717 4.12365 4.51276 4.06393 4.89872 4.0324C5.29052 4.00039 5.79168 4 6.5 4H17.5C18.2083 4 18.7095 4.00039 19.1013 4.0324C19.4872 4.06393 19.7228 4.12365 19.908 4.21799C20.2843 4.40973 20.5903 4.7157 20.782 5.09202C20.8764 5.27717 20.9361 5.51276 20.9676 5.89872C20.9996 6.29052 21 6.79168 21 7.5V15.5C21 16.2083 20.9996 16.7095 20.9676 17.1013C20.9361 17.4872 20.8764 17.7228 20.782 17.908C20.5903 18.2843 20.2843 18.5903 19.908 18.782C19.7228 18.8764 19.4872 18.9361 19.1013 18.9676C18.7095 18.9996 18.2083 19 17.5 19H6.5C5.79168 19 5.29052 18.9996 4.89872 18.9676C4.51276 18.9361 4.27717 18.8764 4.09202 18.782C3.7157 18.5903 3.40973 18.2843 3.21799 17.908C3.12365 17.7228 3.06393 17.4872 3.0324 17.1013C3.00039 16.7095 3 16.2083 3 15.5V7.5C3 6.79168 3.00039 6.29052 3.0324 5.89872C3.06393 5.51276 3.12365 5.27717 3.21799 5.09202C3.40973 4.7157 3.7157 4.40973 4.09202 4.21799ZM10.8841 6.82058L8.38411 9.82058L8.0336 10.2412L7.64645 9.85404L6.14645 8.35404L6.85355 7.64693L7.9664 8.75978L10.1159 6.1804L10.8841 6.82058ZM10.8841 12.8206L8.38411 15.8206L8.0336 16.2412L7.64645 15.854L6.14645 14.354L6.85355 13.6469L7.9664 14.7598L10.1159 12.1804L10.8841 12.8206ZM12.9995 9.00049H17.9995V8.00049H12.9995V9.00049ZM17.9995 15.0005H12.9995V14.0005H17.9995V15.0005Z",fill:"white",fillOpacity:"0.4"})}),(0,s.jsx)("h3",{className:"ml-4 text-lg font-medium text-white",children:"Audit Logs"})]}),(0,s.jsx)("p",{className:"mt-4 leading-6 text-white/60",children:"Audit logs out of the box. Focus on building your product and let us handle security and compliance."})]})}var l=i(57752),h=i(48410);function d(...e){return(0,h.QP)((0,l.$)(e))}let c=({className:e,IconLeft:t,label:i,IconRight:r,shiny:n=!1})=>(0,s.jsxs)("div",{className:"relative group/button",children:[(0,s.jsx)("div",{"aria-hidden":!0,className:"absolute -inset-0.5 bg-white rounded-lg blur-2xl group-hover/button:opacity-30 transition duration-300  opacity-0 "}),(0,s.jsxs)("div",{className:d("relative flex items-center px-4 gap-2 text-sm font-semibold text-black group-hover:bg-white/90 duration-1000 rounded-lg h-10",{"bg-white":!n,"bg-gradient-to-r from-white/80 to-white":n},e),children:[t?(0,s.jsx)(t,{className:"w-4 h-4"}):null,i,r?(0,s.jsx)(r,{className:"w-4 h-4"}):null,n&&(0,s.jsx)("div",{"aria-hidden":!0,className:"pointer-events-none absolute inset-0 opacity-0 group-hover/button:[animation-delay:.2s] group-hover/button:animate-button-shine rounded-[inherit] bg-[length:200%_100%] bg-[linear-gradient(110deg,transparent,35%,rgba(255,255,255,.7),75%,transparent)]"})]})]}),u=({className:e,IconLeft:t,label:i,IconRight:r})=>(0,s.jsxs)("div",{className:d("items-center gap-2 px-4 duration-500 text-white/70 hover:text-white h-10 flex",e),children:[t?(0,s.jsx)(t,{className:"w-4 h-4"}):null,i,r?(0,s.jsx)(r,{className:"w-4 h-4"}):null]});var p=i(39281),f=i(23233);let m=f.forwardRef(({className:e,...t},i)=>(0,s.jsx)("div",{ref:i,className:d("text-left flex flex-col gap-4",e),...t}));m.displayName="Feature";let x=f.forwardRef(({className:e,...t},i)=>(0,s.jsx)("div",{ref:i,className:d("flex items-center gap-2",e),...t}));x.displayName="FeatureHeader";let g=f.forwardRef(({className:e,...t},i)=>(0,s.jsx)("h3",{ref:i,className:d("text-base tracking-tight font-medium leading-6 text-white",e),...t}));g.displayName="FeatureTitle";let y=f.forwardRef(({className:e,iconName:t},i)=>{var r;return(0,s.jsx)("div",{ref:i,className:e,children:(r=t,(0,s.jsx)("div",{className:"w-6 h-6 bg-white/20 rounded-md flex items-center justify-center",children:(0,s.jsx)("span",{className:"text-xs text-white",children:r.slice(0,2).toUpperCase()})}))})});y.displayName="FeatureIcon";let v=f.forwardRef(({className:e,...t},i)=>(0,s.jsx)("div",{ref:i,className:d("text-white/60 font-normal text-sm leading-6",e),...t}));v.displayName="FeatureContent";let w=f.forwardRef(({className:e},t)=>(0,s.jsx)("div",{ref:t,className:d(e),children:(0,s.jsxs)("div",{className:"grid grid-cols-1 gap-12 md:grid-cols-2 xl:grid-cols-3 sm:px-0",children:[(0,s.jsxs)(m,{children:[(0,s.jsxs)(x,{children:[(0,s.jsx)(y,{iconName:"cloud"}),(0,s.jsx)(g,{children:"Multi-Cloud"})]}),(0,s.jsx)(v,{children:"Unkey works with any cloud provider, ensuring a fast global experience regardless of your choice of infrastructure."})]}),(0,s.jsxs)(m,{children:[(0,s.jsxs)(x,{children:[(0,s.jsx)(y,{iconName:"data"}),(0,s.jsx)(g,{children:"Rate limiting"})]}),(0,s.jsx)(v,{children:"Protect your APIs with simple, configurable rate limiting. Unkey's global rate limiting requires zero setup and allows for custom configuration per customer."})]}),(0,s.jsxs)(m,{children:[(0,s.jsxs)(x,{children:[(0,s.jsx)(y,{iconName:"api"}),(0,s.jsx)(g,{children:"API-first / UI-first"})]}),(0,s.jsx)(v,{children:"Unkey is designed to be equally usable via its API and dashboard, ensuring a smooth experience for developers and non-technical users alike."})]}),(0,s.jsxs)(m,{children:[(0,s.jsxs)(x,{children:[(0,s.jsx)(y,{iconName:"role"}),(0,s.jsx)(g,{children:"Role-based access control"})]}),(0,s.jsx)(v,{children:"Granular access privileges with either role or permission-based control. Permission changes are propagated globally in seconds."})]}),(0,s.jsxs)(m,{children:[(0,s.jsxs)(x,{children:[(0,s.jsx)(y,{iconName:"detect"}),(0,s.jsx)(g,{children:"Proactive protection"})]}),(0,s.jsx)(v,{children:"Take immediate control over your system's security with the ability to instantly revoke access , providing swift response to potential threats."})]}),(0,s.jsxs)(m,{children:[(0,s.jsxs)(x,{children:[(0,s.jsx)(y,{iconName:"sdk"}),(0,s.jsx)(g,{children:"SDKs"})]}),(0,s.jsx)(v,{children:"Hit the ground running and accelerate development with SDKs in the language of your choice."})]}),(0,s.jsxs)(m,{children:[(0,s.jsxs)(x,{children:[(0,s.jsx)(y,{iconName:"vercel"}),(0,s.jsx)(g,{children:"Vercel Integration"})]}),(0,s.jsx)(v,{children:"Deploy applications with our official Vercel integration, streamlining the development-to-deployment pipeline."})]}),(0,s.jsxs)(m,{children:[(0,s.jsxs)(x,{children:[(0,s.jsx)(y,{iconName:"automatic"}),(0,s.jsx)(g,{children:"Automatic Key Expiration"})]}),(0,s.jsx)(v,{children:"Simplify key management with automatic key expiration, reducing the risk of unauthorized access over time."})]}),(0,s.jsxs)(m,{children:[(0,s.jsxs)(x,{children:[(0,s.jsx)(y,{iconName:"usage"}),(0,s.jsx)(g,{children:"Usage Limits per Key"})]}),(0,s.jsx)(v,{children:"Create keys with a fixed amount of usage and the ability to refill periodically, limiting the potential for abuse and allowing for usage-based billing with credits."})]})]})}));w.displayName="FeatureGrid";var j=i(2391);function b(){return(0,s.jsxs)("div",{className:"w-full mt-10 relative border-[.75px] h-[520px] rounded-[32px] border-[#ffffff]/10 flex overflow-x-hidden",children:[(0,s.jsx)(j.HashedKeys,{}),(0,s.jsx)(_,{})]})}function _(){return(0,s.jsxs)("div",{className:"flex flex-col text-white absolute left-[20px] sm:left-[40px] xl:left-[40px] bottom-[40px] max-w-[350px]",children:[(0,s.jsxs)("div",{className:"flex items-center w-full",children:[(0,s.jsx)("div",{className:"hashed-keys-bg-gradient",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:(0,s.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M16.7 3C17.5483 3 18.1545 3.00039 18.6297 3.03921C19.099 3.07756 19.3963 3.15089 19.635 3.27248C20.1054 3.51217 20.4878 3.89462 20.7275 4.36502C20.8491 4.60366 20.9224 4.90099 20.9608 5.37032C20.9996 5.84549 21 6.45167 21 7.3V8H22V7.3V7.27781C22 6.45653 22 5.80955 21.9575 5.28889C21.9141 4.75771 21.8239 4.31413 21.6185 3.91103C21.283 3.25247 20.7475 2.71703 20.089 2.38148C19.6859 2.17609 19.2423 2.08593 18.7111 2.04253C18.1905 1.99999 17.5435 2 16.7222 2H16.7H16V3H16.7ZM7.27779 2H7.3H8V3H7.3C6.45167 3 5.84549 3.00039 5.37032 3.03921C4.90099 3.07756 4.60366 3.15089 4.36502 3.27248C3.89462 3.51217 3.51217 3.89462 3.27248 4.36502C3.15089 4.60366 3.07756 4.90099 3.03921 5.37032C3.00039 5.84549 3 6.45167 3 7.3V8H2V7.3V7.27779V7.27779C2 6.45652 1.99999 5.80954 2.04253 5.28889C2.08593 4.75771 2.17609 4.31414 2.38148 3.91103C2.71703 3.25247 3.25247 2.71703 3.91103 2.38148C4.31413 2.17609 4.75771 2.08593 5.28889 2.04253C5.80954 1.99999 6.45652 2 7.27778 2H7.27779ZM2 16.7V16H3V16.7C3 17.5483 3.00039 18.1545 3.03921 18.6297C3.07756 19.099 3.15089 19.3963 3.27248 19.635C3.51217 20.1054 3.89462 20.4878 4.36502 20.7275C4.60366 20.8491 4.90099 20.9224 5.37032 20.9608C5.8455 20.9996 6.45167 21 7.3 21H8V22H7.3H7.27781C6.45653 22 5.80955 22 5.28889 21.9575C4.75771 21.9141 4.31414 21.8239 3.91103 21.6185C3.25247 21.283 2.71703 20.7475 2.38148 20.089C2.17609 19.6859 2.08593 19.2423 2.04253 18.7111C1.99999 18.1905 2 17.5435 2 16.7222V16.7222V16.7ZM22 16V16.7V16.7222C22 17.5435 22 18.1905 21.9575 18.7111C21.9141 19.2423 21.8239 19.6859 21.6185 20.089C21.283 20.7475 20.7475 21.283 20.089 21.6185C19.6859 21.8239 19.2423 21.9141 18.7111 21.9575C18.1905 22 17.5435 22 16.7222 22H16.7H16V21H16.7C17.5483 21 18.1545 20.9996 18.6297 20.9608C19.099 20.9224 19.3963 20.8491 19.635 20.7275C20.1054 20.4878 20.4878 20.1054 20.7275 19.635C20.8491 19.3963 20.9224 19.099 20.9608 18.6297C20.9996 18.1545 21 17.5483 21 16.7V16H22ZM13.8536 5.14645L13.5 4.79289L13.1464 5.14645L9.64645 8.64645L9.29289 9L9.64645 9.35355L10.7929 10.5L6.14645 15.1464L6 15.2929V15.5V17.5V18H6.5H9.5H10V17.5V16H11.5H12V15.5V14.7071L13.5 13.2071L14.6464 14.3536L15 14.7071L15.3536 14.3536L18.8536 10.8536L19.2071 10.5L18.8536 10.1464L13.8536 5.14645ZM11.8536 10.1464L11.5 9.79289L10.7071 9L13.5 6.20711L17.7929 10.5L15 13.2929L14.2071 12.5L13.8536 12.1464L11.8536 10.1464ZM7 15.7071L11.5 11.2071L12.7929 12.5L11.1464 14.1464L11 14.2929V14.5V15H9.5H9V15.5V17H7V15.7071ZM13.1464 9.35355L14.6464 10.8536L15.3536 10.1464L13.8536 8.64645L13.1464 9.35355Z",fill:"white",fillOpacity:"0.4"})})}),(0,s.jsx)("h3",{className:"ml-4 text-lg font-medium text-white",children:"One-way hashed Keys"})]}),(0,s.jsx)("p",{className:"mt-4 leading-6 text-white/60",children:"We store a one-way hash of created keys for maximum security."})]})}var k=i(52912),C=i(83528),S=i.n(C);let M=e=>(0,s.jsx)(S(),{...e,placeholder:"blur",blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8+e1bKQAJMQNc5W2CQwAAAABJRU5ErkJggg=="});var O=i(3922);function V(){return(0,s.jsxs)("div",{className:"w-full mt-5 ip-blur-gradient relative ip-whitelisting-bg-gradient border-[.75px] h-[520px] rounded-[32px] border-[#ffffff]/10 flex overflow-x-hidden",children:[(0,s.jsx)(M,{src:O.default,alt:"animated map",className:"w-full"}),(0,s.jsx)(A,{})]})}function A(){return(0,s.jsxs)("div",{className:"flex flex-col text-white absolute left-[20px] sm:left-[40px] xl:left-[40px] bottom-[40px] max-w-[350px]",children:[(0,s.jsxs)("div",{className:"flex items-center w-full",children:[(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:(0,s.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M19.3732 7.83136L23.3732 3.33136L22.6258 2.66699L18.9781 6.77066L16.8531 4.64562L16.146 5.35273L18.646 7.85273L19.0209 8.22769L19.3732 7.83136ZM3.5 6.0003H3V6.5003V16.5003V17.0003H3.5H6V19.5003V20.0003H6.5H15.5H16V19.5003V17.0003H18.5H19V16.5003V11.0003H18V16.0003H15.5H15V16.5003V19.0003H7V16.5003V16.0003H6.5H4V7.0003H6V12.0003H7V7.0003H9V12.0003H10V7.0003H12V12.0003H13V6.5003V6.0003H12.5H9.5H6.5H3.5ZM15 8.0003V12.0003H16V8.0003H15Z",fill:"white",fillOpacity:"0.4"})}),(0,s.jsx)("h3",{className:"ml-4 text-lg font-medium text-white",children:"IP Whitelisting"})]}),(0,s.jsx)("p",{className:"mt-4 leading-6 text-white/60",children:"Ensure secure access control by allowing only designated IP addresses to interact with your system, adding an extra layer of protection."})]})}var B=i(24761);function P(){return(0,s.jsxs)("div",{className:"w-full relative border-[.75px] h-[576px] rounded-[32px] border-[#ffffff]/10 flex overflow-x-hidden",children:[(0,s.jsx)(M,{src:B.default,alt:"Animated map showing Unkey latency globally",className:"h-full sm:h-auto"}),(0,s.jsx)(L,{})]})}function L(){return(0,s.jsxs)("div",{className:"flex flex-col text-white absolute left-[20px] sm:left-[40px] xl:left-[40px] bottom-[40px] max-w-[330px]",children:[(0,s.jsxs)("div",{className:"flex items-center w-full",children:[(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:(0,s.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M4.26025 9.96741C4.09026 10.6164 3.99976 11.2977 3.99976 12C3.99976 16.0797 7.05355 19.4461 11 19.9381V17.25L9.19996 15.9L8.99996 15.75V15.5V13.5V13.191L9.27635 13.0528L11.2764 12.0528L11.3819 12H11.5H15.5H16V12.5V15V15.1754L15.8904 15.3123L13.9461 17.7427L13.2247 19.9068C17.0615 19.3172 19.9998 16.0017 19.9998 12C19.9998 11.6812 19.9811 11.3669 19.945 11.0582L20.9382 10.9418C20.9789 11.2891 20.9998 11.6422 20.9998 12C20.9998 16.9706 16.9703 21 11.9998 21C7.02919 21 2.99976 16.9706 2.99976 12C2.99976 7.02944 7.02919 3 11.9998 3C13.0689 3 14.0956 3.18664 15.0482 3.52955L14.7095 4.47045C13.864 4.16609 12.9518 4 11.9998 4C11.3408 4 10.7004 4.07968 10.0877 4.22992L11.8123 5.60957L12 5.75969V6V8.5V8.80902L11.7236 8.94721L9.87263 9.87268L8.94716 11.7236L8.80897 12H8.49995H6.49995H6.29284L6.1464 11.8536L4.6464 10.3536L4.26025 9.96741ZM12.14 19.9988C12.0934 19.9996 12.0467 20 12 20V17V16.75L11.8 16.6L9.99996 15.25V13.809L11.618 13H15V14.8246L13.1095 17.1877L13.0538 17.2573L13.0256 17.3419L12.14 19.9988ZM4.61797 8.91091L5.3535 9.64645L6.70706 11H8.19093L9.05274 9.27639L9.12727 9.12732L9.27634 9.05279L11 8.19098V6.24031L8.95124 4.60134C6.99823 5.40693 5.43395 6.96331 4.61797 8.91091ZM18.3735 8.83218L22.3735 4.33218L21.6261 3.66782L17.9783 7.77148L15.8533 5.64645L15.1462 6.35355L17.6462 8.85355L18.0212 9.22852L18.3735 8.83218Z",fill:"white",fillOpacity:"0.4"})}),(0,s.jsx)("h3",{className:"ml-4 text-lg font-medium text-white",children:"Global low latency"})]}),(0,s.jsx)("p",{className:"mt-4 leading-6 text-white/60",children:"Unkey is fast globally, regardless of which cloud providers you're using or where your users are located."})]})}var N=i(58665);function T(){return(0,s.jsxs)("div",{className:"w-full md:mt-5 relative border-[.75px] h-[520px] rounded-[32px] border-[#ffffff]/10 flex overflow-x-hidden rate-limits-background-gradient bg-gradient-to-t backdrop-blur-[1px] from-black/20 via-black/20 via-20% to-transparent",children:[(0,s.jsx)(E,{}),(0,s.jsx)(R,{})]})}function E(){return(0,s.jsxs)("div",{className:"relative mx-[40px] flex w-full flex-col",children:[(0,s.jsxs)("div",{className:"flex h-[200px] w-full ratelimits-editor-bg-gradient rounded-b-xl ",children:[(0,s.jsxs)("div",{className:"flex flex-col font-mono text-sm text-white px-[24px] space-y-3 mt-1 border-r-[.75px] border-[#ffffff]/20",children:[(0,s.jsx)("p",{children:"1"}),(0,s.jsx)("p",{children:"2"}),(0,s.jsx)("p",{children:"3"}),(0,s.jsx)("p",{children:"4"}),(0,s.jsx)("p",{children:"5"}),(0,s.jsx)("p",{children:"6"})]}),(0,s.jsx)("div",{className:"flex w-full pl-8 font-mono text-xs leading-8 text-white whitespace-pre ratelimits-editor-bg-gradient-2 rounded-br-xl",children:JSON.stringify({rateLimit:{limit:10,interval:1e3}},null,2)})]}),(0,s.jsxs)("div",{className:"flex flex-col mt-8 ratelimits-fade-gradient",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsxs)("div",{className:"font-mono text-xs text-white sm:font-sm whitespace-nowrap",children:[(0,s.jsx)("span",{className:"text-[#ffffff]/40",children:"Rate limiting"}),(0,s.jsx)("span",{className:"text-[#ffffff]/40 tracking-[-5px]",children:"..."}),(0,s.jsx)("span",{className:"inline-flex w-[4px] h-[12px] bg-white ratelimits-bar-shadow ml-3 relative top-[1px] flicker"})]}),(0,s.jsxs)("div",{className:"inline-flex items-center overflow-hidden ml-4 h-[36px] text-white font-mono text-sm ratelimits-key-gradient border-[.75px] border-[#ffffff]/20 rounded-xl",children:[(0,s.jsx)("div",{className:"w-[62px] h-[36px]",children:(0,s.jsxs)("svg",{className:"ratelimits-key-icon ",xmlns:"http://www.w3.org/2000/svg",width:"62",height:"36",viewBox:"0 0 62 36",fill:"none",children:[(0,s.jsxs)("g",{filter:"url(#filter0_d_840_1930)",children:[(0,s.jsx)("rect",{x:"8",y:"6",width:"24",height:"24",rx:"6",fill:"#3CEEAE",shapeRendering:"crispEdges"}),(0,s.jsx)("rect",{x:"8",y:"6",width:"24",height:"24",rx:"6",fill:"black",fillOpacity:"0.15",shapeRendering:"crispEdges"}),(0,s.jsx)("rect",{x:"8.375",y:"6.375",width:"23.25",height:"23.25",rx:"5.625",stroke:"white",strokeOpacity:"0.1",strokeWidth:"0.75",shapeRendering:"crispEdges"}),(0,s.jsx)("path",{d:"M21.5 15L23 16.5M14.5 23.5H17.5V21.5H19.5V20.5L21.5 18.5L19.5 16.5L14.5 21.5V23.5ZM18 15L23 20L26.5 16.5L21.5 11.5L18 15Z",stroke:"white"})]}),(0,s.jsx)("defs",{children:(0,s.jsxs)("filter",{id:"filter0_d_840_1930",x:"-22",y:"-24",width:"84",height:"84",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB",children:[(0,s.jsx)("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),(0,s.jsx)("feColorMatrix",{in:"SourceAlpha",type:"matrix",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),(0,s.jsx)("feOffset",{}),(0,s.jsx)("feGaussianBlur",{stdDeviation:"15"}),(0,s.jsx)("feComposite",{in2:"hardAlpha",operator:"out"}),(0,s.jsx)("feColorMatrix",{type:"matrix",values:"0 0 0 0 0.235294 0 0 0 0 0.933333 0 0 0 0 0.682353 0 0 0 1 0"}),(0,s.jsx)("feBlend",{mode:"normal",in2:"BackgroundImageFix",result:"effect1_dropShadow_840_1930"}),(0,s.jsx)("feBlend",{mode:"normal",in:"SourceGraphic",in2:"effect1_dropShadow_840_1930",result:"shape"})]})})]})}),(0,s.jsx)("p",{className:"relative text-xs right-4",children:"sk_TEwCE9AY9BFTq1XJdIO"})]})]}),(0,s.jsxs)("div",{className:"inline-flex w-[205px] opacity-70 items-center ml-[150px] mt-[30px] h-[36px] text-white font-mono text-sm ratelimits-link ratelimits-key-gradient border-[.75px] border-[#ffffff]/20 rounded-xl [mask-image:linear-gradient(to_bottom_left,black_30%,transparent)]",children:[(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"84",height:"84",viewBox:"0 0 84 84",fill:"none",children:[(0,s.jsxs)("g",{filter:"url(#filter0_d_61_98)",children:[(0,s.jsx)("rect",{x:"30",y:"30",width:"24",height:"24",rx:"6",fill:"#6E56CF",shapeRendering:"crispEdges"}),(0,s.jsx)("rect",{x:"30",y:"30",width:"24",height:"24",rx:"6",fill:"black",fillOpacity:"0.15",shapeRendering:"crispEdges"}),(0,s.jsx)("rect",{x:"30.375",y:"30.375",width:"23.25",height:"23.25",rx:"5.625",stroke:"white",strokeWidth:"0.75",strokeOpacity:"0.1",shapeRendering:"crispEdges"}),(0,s.jsx)("path",{d:"M46.9 38H38.1C37.4925 38 37 38.4477 37 39V45C37 45.5523 37.4925 46 38.1 46H46.9C47.5075 46 48 45.5523 48 45V39C48 38.4477 47.5075 38 46.9 38Z",stroke:"white",strokeLinecap:"round",strokeLinejoin:"round"}),(0,s.jsx)("path",{d:"M48 40L43.0665 42.8519C42.8967 42.9487 42.7004 43 42.5 43C42.2996 43 42.1033 42.9487 41.9335 42.8519L37 40",stroke:"white",strokeLinecap:"round",strokeLinejoin:"round"})]}),(0,s.jsx)("defs",{children:(0,s.jsxs)("filter",{id:"filter0_d_61_98",x:"0",y:"0",width:"84",height:"84",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB",children:[(0,s.jsx)("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),(0,s.jsx)("feColorMatrix",{in:"SourceAlpha",type:"matrix",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),(0,s.jsx)("feOffset",{}),(0,s.jsx)("feGaussianBlur",{stdDeviation:"15"}),(0,s.jsx)("feComposite",{in2:"hardAlpha",operator:"out"}),(0,s.jsx)("feColorMatrix",{type:"matrix",values:"0 0 0 0 0.431373 0 0 0 0 0.337255 0 0 0 0 0.811765 0 0 0 1 0"}),(0,s.jsx)("feBlend",{mode:"normal",in2:"BackgroundImageFix",result:"effect1_dropShadow_61_98"}),(0,s.jsx)("feBlend",{mode:"normal",in:"SourceGraphic",in2:"effect1_dropShadow_61_98",result:"shape"})]})})]}),(0,s.jsx)("p",{className:"relative text-xs right-4",children:"<EMAIL>"})]})]})]})}function R(){return(0,s.jsxs)("div",{className:"flex flex-col text-white absolute left-[20px] sm:left-[40px] xl:left-[40px] bottom-[40px] max-w-[350px]",children:[(0,s.jsxs)("div",{className:"flex items-center w-full",children:[(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:(0,s.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M21 4H18V3H21.5H22V3.5V8.42105V20.5V21H21.5H18V20H21V16H18V15H21V12H18V11H21V8.42105V8H18V7H21V4ZM9.85355 5.14645L9.5 4.79289L9.14645 5.14645L5.64645 8.64645L5.29289 9L5.64645 9.35355L6.79289 10.5L2.14645 15.1464L2 15.2929V15.5V17.5V18H2.5H5.5H6V17.5V16H7.5H8V15.5V14.7071L9.5 13.2071L10.6464 14.3536L11 14.7071L11.3536 14.3536L14.8536 10.8536L15.2071 10.5L14.8536 10.1464L9.85355 5.14645ZM7.85355 10.1464L7.5 9.79289L6.70711 9L9.5 6.20711L13.7929 10.5L11 13.2929L10.2071 12.5L9.85355 12.1464L7.85355 10.1464ZM3 15.7071L7.5 11.2071L8.79289 12.5L7.14645 14.1464L7 14.2929V14.5V15H5.5H5V15.5V17H3V15.7071ZM9.14645 9.35355L10.6464 10.8536L11.3536 10.1464L9.85355 8.64645L9.14645 9.35355Z",fill:"white",fillOpacity:"0.4"})}),(0,s.jsx)("h3",{className:"ml-4 text-lg font-medium text-white",children:"Rate Limits"})]}),(0,s.jsx)("p",{className:"mt-4 leading-6 text-white/60",children:"Per IP, per user, per API key, or any identifier that matters to you. Enforced on the edge, as close to your users as possible, delivering fast and reliable rate limiting."})]})}function I({label:e,title:t,text:i,align:r="left",children:n,className:a}){return(0,s.jsxs)("div",{className:d("flex flex-col items-center",{"xl:items-start":"left"===r},a),children:[(0,s.jsx)("span",{className:d("font-mono text-sm md:text-md text-white/50 text-center",{"xl:text-left":"left"===r}),children:e}),(0,s.jsx)("h2",{className:d("text-[28px] sm:pb-3 sm:text-[52px] sm:leading-[64px] text-white text-pretty max-w-sm md:max-w-md lg:max-w-2xl xl:max-w-4xl via-30/%  pt-4 font-medium bg-gradient-to-br text-transparent bg-gradient-stop bg-clip-text from-white via-white to-white/30 text-center leading-none",{"xl:text-left":"left"===r}),children:t}),i&&(0,s.jsx)("p",{className:d("text-sm md:text-base text-white/70 leading-7 py-6 text-center max-w-sm md:max-w-md lg:max-w-xl xl:max-w-4xl text-balance",{"xl:text-left xl:max-w-xl":"left"===r}),children:i}),n]})}let F=({children:e,className:t})=>(0,s.jsx)("section",{className:d(t),children:e}),U=({className:e})=>(0,s.jsxs)("svg",{width:960,height:324,viewBox:"0 0 960 324",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:e,children:[(0,s.jsx)("mask",{id:"b",style:{maskType:"alpha"},maskUnits:"userSpaceOnUse",x:-56,y:0,width:1072,height:324,children:(0,s.jsx)("path",{transform:"translate(-56 .25)",fill:"url(#a)",d:"M0 0h1072v323H0z"})}),(0,s.jsxs)("g",{mask:"url(#b)",children:[(0,s.jsx)("path",{d:"M976 194.25H639.604c-3.914 0-5.87 0-7.712-.442a16 16 0 0 1-4.625-1.916c-1.614-.989-2.998-2.373-5.765-5.14L507.75 73",stroke:"#fff",strokeOpacity:.08,strokeWidth:.5}),(0,s.jsx)("rect",{x:732,y:149,width:21,height:24,rx:3,fill:"#fff",fillOpacity:.06}),(0,s.jsx)("rect",{x:735,y:163,width:15,height:6,rx:1.5,fill:"#fff",fillOpacity:.06}),(0,s.jsx)("path",{fill:"#fff",fillOpacity:.08,d:"M650 151h8v2h-8z"}),(0,s.jsx)("path",{fill:"#fff",fillOpacity:.04,d:"M650 151h8v1h-8z"}),(0,s.jsx)("path",{fill:"#fff",fillOpacity:.08,d:"M650 161h8v2h-8z"}),(0,s.jsx)("path",{fill:"#fff",fillOpacity:.04,d:"M650 161h8v1h-8z"}),(0,s.jsx)("path",{fill:"#fff",fillOpacity:.08,d:"M651 155h2v4h-2z"}),(0,s.jsx)("path",{fill:"#fff",fillOpacity:.04,d:"M651 155h2v1h-2z"}),(0,s.jsx)("path",{fill:"#fff",fillOpacity:.08,d:"M655 155h2v4h-2z"}),(0,s.jsx)("path",{fill:"#fff",fillOpacity:.04,d:"M655 155h2v1h-2z"}),(0,s.jsx)("path",{fill:"#fff",fillOpacity:.08,d:"M650 171h8v2h-8z"}),(0,s.jsx)("path",{fill:"#fff",fillOpacity:.04,d:"M650 171h8v1h-8z"}),(0,s.jsx)("path",{fill:"#fff",fillOpacity:.08,d:"M650 181h8v2h-8z"}),(0,s.jsx)("path",{fill:"#fff",fillOpacity:.04,d:"M650 181h8v1h-8z"}),(0,s.jsx)("path",{fill:"#fff",fillOpacity:.08,d:"M651 175h2v4h-2z"}),(0,s.jsx)("path",{fill:"#fff",fillOpacity:.04,d:"M651 175h2v1h-2z"}),(0,s.jsx)("path",{fill:"#fff",fillOpacity:.08,d:"M655 175h2v4h-2z"}),(0,s.jsx)("path",{fill:"#fff",fillOpacity:.04,d:"M655 175h2v1h-2z"}),(0,s.jsx)("path",{fill:"#fff",fillOpacity:.08,d:"M666 171h8v2h-8z"}),(0,s.jsx)("path",{fill:"#fff",fillOpacity:.04,d:"M666 171h8v1h-8z"}),(0,s.jsx)("path",{fill:"#fff",fillOpacity:.08,d:"M666 181h8v2h-8z"}),(0,s.jsx)("path",{fill:"#fff",fillOpacity:.04,d:"M666 181h8v1h-8z"}),(0,s.jsx)("path",{fill:"#fff",fillOpacity:.08,d:"M667 175h2v4h-2z"}),(0,s.jsx)("path",{fill:"#fff",fillOpacity:.04,d:"M667 175h2v1h-2z"}),(0,s.jsx)("path",{fill:"#fff",fillOpacity:.08,d:"M671 175h2v4h-2z"}),(0,s.jsx)("path",{fill:"#fff",fillOpacity:.04,d:"M671 175h2v1h-2z"}),(0,s.jsx)("path",{fill:"#fff",fillOpacity:.08,d:"M682 171h8v2h-8z"}),(0,s.jsx)("path",{fill:"#fff",fillOpacity:.04,d:"M682 171h8v1h-8z"}),(0,s.jsx)("path",{fill:"#fff",fillOpacity:.08,d:"M682 181h8v2h-8z"}),(0,s.jsx)("path",{fill:"#fff",fillOpacity:.04,d:"M682 181h8v1h-8z"}),(0,s.jsx)("path",{fill:"#fff",fillOpacity:.08,d:"M683 175h2v4h-2z"}),(0,s.jsx)("path",{fill:"#fff",fillOpacity:.04,d:"M683 175h2v1h-2z"}),(0,s.jsx)("path",{fill:"#fff",fillOpacity:.08,d:"M687 175h2v4h-2z"}),(0,s.jsx)("path",{fill:"#fff",fillOpacity:.04,d:"M687 175h2v1h-2z"}),(0,s.jsx)("path",{fill:"#fff",fillOpacity:.08,d:"M666 151h8v2h-8z"}),(0,s.jsx)("path",{fill:"#fff",fillOpacity:.04,d:"M666 151h8v1h-8z"}),(0,s.jsx)("path",{fill:"#fff",fillOpacity:.08,d:"M666 161h8v2h-8z"}),(0,s.jsx)("path",{fill:"#fff",fillOpacity:.04,d:"M666 161h8v1h-8z"}),(0,s.jsx)("path",{fill:"#fff",fillOpacity:.08,d:"M667 155h2v4h-2z"}),(0,s.jsx)("path",{fill:"#fff",fillOpacity:.04,d:"M667 155h2v1h-2z"}),(0,s.jsx)("path",{fill:"#fff",fillOpacity:.08,d:"M671 155h2v4h-2z"}),(0,s.jsx)("path",{fill:"#fff",fillOpacity:.04,d:"M671 155h2v1h-2z"}),(0,s.jsx)("path",{d:"M773 194.5V323",stroke:"#fff",strokeOpacity:.04,strokeWidth:2}),(0,s.jsx)("path",{d:"M822 .25H711.25V194m117 129.25h-117V194.5M-16 237.25h312.084c2.031 0 3.047 0 4.021-.195.863-.174 1.701-.46 2.489-.853.889-.442 1.692-1.065 3.297-2.31l29.405-22.811c1.818-1.411 2.727-2.116 3.382-3a8 8 0 0 0 1.275-2.603c.297-1.059.297-2.21.297-4.511V65",stroke:"#fff",strokeOpacity:.08,strokeWidth:.5}),(0,s.jsx)("path",{d:"M340 157.25H161.25V237",stroke:"#fff",strokeOpacity:.08,strokeWidth:.5}),(0,s.jsx)("path",{fill:"#fff",fillOpacity:.08,d:"M305 142h2v6h-2zm5 0h2v6h-2zm5 0h2v6h-2zm5 0h2v6h-2zm5 0h2v6h-2zm5 0h2v6h-2z"}),(0,s.jsx)("path",{d:"M161.25 7.5V157",stroke:"#fff",strokeOpacity:.08,strokeWidth:.5}),(0,s.jsx)("rect",{opacity:.02,x:167,y:163,width:75,height:68,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:171,y:189,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:171,y:182,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:171,y:174,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:171,y:174,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:171,y:167,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:171,y:196,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:171,y:203,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:171,y:210,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:171,y:217,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:171,y:224,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:179,y:189,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:179,y:182,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:179,y:174,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:179,y:174,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:179,y:167,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:179,y:196,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.1,x:179,y:203,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:179,y:210,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:179,y:217,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:179,y:224,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:187,y:189,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:187,y:182,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:187,y:174,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:187,y:174,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:187,y:167,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:187,y:196,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:187,y:203,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:187,y:210,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:187,y:217,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.15,x:187,y:224,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.1,x:195,y:189,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:195,y:182,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:195,y:174,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:195,y:174,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:195,y:167,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:195,y:196,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:195,y:203,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:195,y:210,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:195,y:217,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:195,y:224,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:203,y:189,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:203,y:182,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:203,y:174,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:203,y:174,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:203,y:167,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:203,y:196,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:203,y:203,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.15,x:203,y:210,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:203,y:217,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:203,y:224,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:211,y:189,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:211,y:182,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:211,y:174,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:211,y:174,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:211,y:167,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:211,y:196,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:211,y:203,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:211,y:210,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:211,y:217,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:211,y:224,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:219,y:189,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:219,y:182,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:219,y:174,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:219,y:174,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:219,y:167,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:219,y:196,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.15,x:219,y:203,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:219,y:210,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:219,y:217,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:219,y:224,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.1,x:227,y:189,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:227,y:182,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:227,y:174,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:227,y:174,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:227,y:167,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:227,y:196,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:227,y:203,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:227,y:210,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:227,y:217,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.1,x:227,y:224,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:235,y:189,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:235,y:182,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:235,y:174,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:235,y:174,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:235,y:167,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:235,y:196,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:235,y:203,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:235,y:210,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:235,y:217,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("rect",{opacity:.05,x:235,y:224,width:3,height:3,rx:.5,fill:"#fff"}),(0,s.jsx)("path",{fill:"#fff",fillOpacity:.08,d:"M91 215h2v8h-2zm4 0h2v8h-2zm-9 3h2v2h-2z"}),(0,s.jsx)("path",{fill:"#fff",fillOpacity:.04,d:"M86 218h2v1h-2zm5-3h2v1h-2zm4 0h2v1h-2z"}),(0,s.jsx)("path",{fill:"#fff",fillOpacity:.08,d:"M91 199h2v8h-2zm4 0h2v8h-2zm-9 3h2v2h-2z"}),(0,s.jsx)("path",{fill:"#fff",fillOpacity:.04,d:"M86 202h2v1h-2zm5-3h2v1h-2zm4 0h2v1h-2z"}),(0,s.jsx)("path",{fill:"#fff",fillOpacity:.08,d:"M91 183h2v8h-2zm4 0h2v8h-2zm-9 3h2v2h-2z"}),(0,s.jsx)("path",{fill:"#fff",fillOpacity:.04,d:"M86 186h2v1h-2zm5-3h2v1h-2zm4 0h2v1h-2z"}),(0,s.jsx)("path",{fill:"#fff",fillOpacity:.08,d:"M110 215h2v8h-2zm4 0h2v8h-2zm-9 3h2v2h-2z"}),(0,s.jsx)("path",{fill:"#fff",fillOpacity:.04,d:"M105 218h2v1h-2zm5-3h2v1h-2zm4 0h2v1h-2z"}),(0,s.jsx)("path",{d:"M270 7.25H142.67c-4.559 0-6.839 0-8.941.584a16 16 0 0 0-5.174 2.51c-1.759 1.29-3.17 3.08-5.992 6.662l-45.82 58.149c-2.037 2.584-3.055 3.876-3.779 5.31a16 16 0 0 0-1.394 4.024c-.32 1.575-.32 3.22-.32 6.51V237",stroke:"#fff",strokeOpacity:.08,strokeWidth:.5}),(0,s.jsx)("g",{clipPath:"url(#c)",stroke:"#fff",strokeOpacity:.1,strokeWidth:.3,children:(0,s.jsx)("path",{d:"m648 288 160-160M656 288l160-160M664 288l160-160M672 288l160-160M680 288l160-160M688 288l160-160M696 288l160-160M704 288l160-160M712 288l160-160M720 288l160-160M728 288l160-160M736 288l160-160M744 288l160-160M752 288l160-160"})}),(0,s.jsx)("path",{fill:"#fff",fillOpacity:.08,d:"M722 298h2v6h-2zm5 0h2v6h-2zm5 0h2v6h-2zm5 0h2v6h-2zm5 0h2v6h-2zm5 0h2v6h-2z"})]}),(0,s.jsxs)("defs",{children:[(0,s.jsxs)("radialGradient",{id:"a",cx:0,cy:0,r:1,gradientUnits:"userSpaceOnUse",gradientTransform:"matrix(0 161.25 -507 0 536 161.5)",children:[(0,s.jsx)("stop",{stopColor:"#fff"}),(0,s.jsx)("stop",{offset:.422,stopColor:"#fff"}),(0,s.jsx)("stop",{offset:.789,stopColor:"#fff"}),(0,s.jsx)("stop",{offset:1,stopColor:"#fff",stopOpacity:0})]}),(0,s.jsx)("clipPath",{id:"c",children:(0,s.jsx)("path",{fill:"#fff",d:"M722 205h40v77h-40z"})})]})]}),D=()=>(0,s.jsxs)("svg",{className:"absolute top-0 left-0 hidden pointer-events-none md:flex 2xl:hidden",width:"579",height:"511",viewBox:"0 0 579 511",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,s.jsx)("g",{style:{mixBlendMode:"lighten"},filter:"url(#filter0_f_574_3059)",children:(0,s.jsx)("ellipse",{cx:"-5.14484",cy:"-64.8282",rx:"32.7783",ry:"293.346",transform:"rotate(20.0538 -5.14484 -64.8282)",fill:"url(#paint0_linear_574_3059)",fillOpacity:"0.5"})}),(0,s.jsx)("g",{style:{mixBlendMode:"lighten"},filter:"url(#filter2_f_574_3059)",children:(0,s.jsx)("ellipse",{cx:"198.822",cy:"3.50066",rx:"22.3794",ry:"381.284",transform:"rotate(-10 198.822 3.50066)",fill:"url(#paint2_linear_574_3059)",fillOpacity:"0.5"})}),(0,s.jsx)("g",{style:{mixBlendMode:"lighten"},filter:"url(#filter3_f_574_3059)",children:(0,s.jsx)("ellipse",{cx:"163.986",cy:"-194.068",rx:"22.3794",ry:"180.667",transform:"rotate(-10 163.986 -194.068)",fill:"url(#paint3_linear_574_3059)",fillOpacity:"0.5"})}),(0,s.jsx)("g",{style:{mixBlendMode:"lighten"},filter:"url(#filter4_f_574_3059)",children:(0,s.jsx)("ellipse",{cx:"88.5057",cy:"41.4464",rx:"22.25",ry:"381.5",transform:"rotate(5 88.5057 41.4464)",fill:"url(#paint4_linear_574_3059)",fillOpacity:"0.5"})}),(0,s.jsx)("g",{style:{mixBlendMode:"lighten"},filter:"url(#filter5_f_574_3059)",children:(0,s.jsx)("ellipse",{cx:"107.823",cy:"-182.221",rx:"321.5",ry:"187.5",transform:"rotate(5 107.823 -182.221)",fill:"url(#paint5_linear_574_3059)",fillOpacity:"0.5"})}),(0,s.jsxs)("defs",{children:[(0,s.jsxs)("filter",{id:"filter0_f_574_3059",x:"-199.369",y:"-429.622",width:"388.449",height:"729.588",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB",children:[(0,s.jsx)("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),(0,s.jsx)("feBlend",{mode:"normal",in:"SourceGraphic",in2:"BackgroundImageFix",result:"shape"}),(0,s.jsx)("feGaussianBlur",{stdDeviation:"44.5",result:"effect1_foregroundBlur_574_3059"})]}),(0,s.jsxs)("filter",{id:"filter1_f_574_3059",x:"-23.6509",y:"-457.712",width:"251.489",height:"762.287",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB",children:[(0,s.jsx)("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),(0,s.jsx)("feBlend",{mode:"normal",in:"SourceGraphic",in2:"BackgroundImageFix",result:"shape"}),(0,s.jsx)("feGaussianBlur",{stdDeviation:"44.5",result:"effect1_foregroundBlur_574_3059"})]}),(0,s.jsxs)("filter",{id:"filter2_f_574_3059",x:"40.0224",y:"-461.011",width:"317.6",height:"929.023",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB",children:[(0,s.jsx)("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),(0,s.jsx)("feBlend",{mode:"normal",in:"SourceGraphic",in2:"BackgroundImageFix",result:"shape"}),(0,s.jsx)("feGaussianBlur",{stdDeviation:"44.5",result:"effect1_foregroundBlur_574_3059"})]}),(0,s.jsxs)("filter",{id:"filter3_f_574_3059",x:"36.6421",y:"-461.034",width:"254.687",height:"533.932",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB",children:[(0,s.jsx)("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),(0,s.jsx)("feBlend",{mode:"normal",in:"SourceGraphic",in2:"BackgroundImageFix",result:"shape"}),(0,s.jsx)("feGaussianBlur",{stdDeviation:"44.5",result:"effect1_foregroundBlur_574_3059"})]}),(0,s.jsxs)("filter",{id:"filter4_f_574_3059",x:"-40.4595",y:"-427.607",width:"257.93",height:"938.107",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB",children:[(0,s.jsx)("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),(0,s.jsx)("feBlend",{mode:"normal",in:"SourceGraphic",in2:"BackgroundImageFix",result:"shape"}),(0,s.jsx)("feGaussianBlur",{stdDeviation:"44.5",result:"effect1_foregroundBlur_574_3059"})]}),(0,s.jsxs)("filter",{id:"filter5_f_574_3059",x:"-362.878",y:"-521.123",width:"941.402",height:"677.805",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB",children:[(0,s.jsx)("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),(0,s.jsx)("feBlend",{mode:"normal",in:"SourceGraphic",in2:"BackgroundImageFix",result:"shape"}),(0,s.jsx)("feGaussianBlur",{stdDeviation:"75",result:"effect1_foregroundBlur_574_3059"})]}),(0,s.jsxs)("linearGradient",{id:"paint0_linear_574_3059",x1:"-5.14484",y1:"-358.174",x2:"-5.14484",y2:"228.517",gradientUnits:"userSpaceOnUse",children:[(0,s.jsx)("stop",{stopColor:"white"}),(0,s.jsx)("stop",{offset:"1",stopColor:"white",stopOpacity:"0"})]}),(0,s.jsxs)("linearGradient",{id:"paint1_linear_574_3059",x1:"102.094",y1:"-369.819",x2:"102.094",y2:"216.681",gradientUnits:"userSpaceOnUse",children:[(0,s.jsx)("stop",{stopColor:"white"}),(0,s.jsx)("stop",{offset:"1",stopColor:"white",stopOpacity:"0"})]}),(0,s.jsxs)("linearGradient",{id:"paint2_linear_574_3059",x1:"198.822",y1:"-377.783",x2:"198.822",y2:"384.784",gradientUnits:"userSpaceOnUse",children:[(0,s.jsx)("stop",{stopColor:"white"}),(0,s.jsx)("stop",{offset:"1",stopColor:"white",stopOpacity:"0"})]}),(0,s.jsxs)("linearGradient",{id:"paint3_linear_574_3059",x1:"163.986",y1:"-374.736",x2:"163.986",y2:"-13.4011",gradientUnits:"userSpaceOnUse",children:[(0,s.jsx)("stop",{stopColor:"white"}),(0,s.jsx)("stop",{offset:"1",stopColor:"white",stopOpacity:"0"})]}),(0,s.jsxs)("linearGradient",{id:"paint4_linear_574_3059",x1:"88.5057",y1:"-340.054",x2:"88.5057",y2:"422.946",gradientUnits:"userSpaceOnUse",children:[(0,s.jsx)("stop",{stopColor:"white"}),(0,s.jsx)("stop",{offset:"1",stopColor:"white",stopOpacity:"0"})]}),(0,s.jsxs)("linearGradient",{id:"paint5_linear_574_3059",x1:"107.823",y1:"-369.721",x2:"107.823",y2:"5.27896",gradientUnits:"userSpaceOnUse",children:[(0,s.jsx)("stop",{stopColor:"white"}),(0,s.jsx)("stop",{offset:"1",stopColor:"white",stopOpacity:"0"})]})]})]}),H=()=>(0,s.jsxs)("svg",{className:"absolute top-0 right-0 hidden pointer-events-none md:flex 2xl:hidden",width:"445",height:"699",viewBox:"0 0 445 699",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,s.jsx)("g",{style:{mixBlendMode:"lighten"},filter:"url(#filter0_f_574_3050)",children:(0,s.jsx)("ellipse",{cx:"336.291",cy:"119.962",rx:"32.7783",ry:"293.346",transform:"rotate(30.0538 336.291 119.962)",fill:"url(#paint0_linear_574_3050)",fillOpacity:"0.5"})}),(0,s.jsx)("g",{style:{mixBlendMode:"lighten"},filter:"url(#filter2_f_574_3050)",children:(0,s.jsx)("ellipse",{cx:"525.295",cy:"222.671",rx:"22.3794",ry:"381.284",fill:"url(#paint2_linear_574_3050)",fillOpacity:"0.5"})}),(0,s.jsx)("g",{style:{mixBlendMode:"lighten"},filter:"url(#filter3_f_574_3050)",children:(0,s.jsx)("ellipse",{cx:"525.295",cy:"22.0542",rx:"22.3794",ry:"180.667",fill:"url(#paint3_linear_574_3050)",fillOpacity:"0.5"})}),(0,s.jsx)("g",{style:{mixBlendMode:"lighten"},filter:"url(#filter4_f_574_3050)",children:(0,s.jsx)("ellipse",{cx:"410.065",cy:"240.884",rx:"22.25",ry:"381.5",transform:"rotate(15 410.065 240.884)",fill:"url(#paint4_linear_574_3050)",fillOpacity:"0.5"})}),(0,s.jsx)("g",{style:{mixBlendMode:"lighten"},filter:"url(#filter5_f_574_3050)",children:(0,s.jsx)("ellipse",{cx:"467.928",cy:"23.9689",rx:"321.5",ry:"187.5",transform:"rotate(15 467.928 23.9689)",fill:"url(#paint5_linear_574_3050)",fillOpacity:"0.5"})}),(0,s.jsx)("g",{style:{mixBlendMode:"lighten"},filter:"url(#filter6_f_574_3050)",children:(0,s.jsx)("ellipse",{cx:"491.74",cy:"-64.8963",rx:"160.5",ry:"95.5",transform:"rotate(15 491.74 -64.8963)",fill:"url(#paint6_linear_574_3050)",fillOpacity:"0.5"})}),(0,s.jsx)("g",{style:{mixBlendMode:"lighten"},filter:"url(#filter7_f_574_3050)",children:(0,s.jsx)("ellipse",{cx:"489.863",cy:"-57.8934",rx:"135",ry:"80.25",transform:"rotate(15 489.863 -57.8934)",fill:"url(#paint7_linear_574_3050)",fillOpacity:"0.5"})}),(0,s.jsxs)("defs",{children:[(0,s.jsxs)("filter",{id:"filter0_f_574_3050",x:"97.6377",y:"-223.485",width:"477.308",height:"686.892",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB",children:[(0,s.jsx)("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),(0,s.jsx)("feBlend",{mode:"normal",in:"SourceGraphic",in2:"BackgroundImageFix",result:"shape"}),(0,s.jsx)("feGaussianBlur",{stdDeviation:"44.5",result:"effect1_foregroundBlur_574_3050"})]}),(0,s.jsxs)("filter",{id:"filter1_f_574_3050",x:"274.818",y:"-245.321",width:"338.241",height:"744.685",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB",children:[(0,s.jsx)("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),(0,s.jsx)("feBlend",{mode:"normal",in:"SourceGraphic",in2:"BackgroundImageFix",result:"shape"}),(0,s.jsx)("feGaussianBlur",{stdDeviation:"44.5",result:"effect1_foregroundBlur_574_3050"})]}),(0,s.jsxs)("filter",{id:"filter2_f_574_3050",x:"413.916",y:"-247.613",width:"222.759",height:"940.567",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB",children:[(0,s.jsx)("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),(0,s.jsx)("feBlend",{mode:"normal",in:"SourceGraphic",in2:"BackgroundImageFix",result:"shape"}),(0,s.jsx)("feGaussianBlur",{stdDeviation:"44.5",result:"effect1_foregroundBlur_574_3050"})]}),(0,s.jsxs)("filter",{id:"filter3_f_574_3050",x:"413.916",y:"-247.613",width:"222.759",height:"539.335",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB",children:[(0,s.jsx)("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),(0,s.jsx)("feBlend",{mode:"normal",in:"SourceGraphic",in2:"BackgroundImageFix",result:"shape"}),(0,s.jsx)("feGaussianBlur",{stdDeviation:"44.5",result:"effect1_foregroundBlur_574_3050"})]}),(0,s.jsxs)("filter",{id:"filter4_f_574_3050",x:"219.992",y:"-216.663",width:"380.146",height:"915.093",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB",children:[(0,s.jsx)("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),(0,s.jsx)("feBlend",{mode:"normal",in:"SourceGraphic",in2:"BackgroundImageFix",result:"shape"}),(0,s.jsx)("feGaussianBlur",{stdDeviation:"44.5",result:"effect1_foregroundBlur_574_3050"})]}),(0,s.jsxs)("filter",{id:"filter5_f_574_3050",x:"3.56885",y:"-325.391",width:"928.719",height:"698.72",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB",children:[(0,s.jsx)("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),(0,s.jsx)("feBlend",{mode:"normal",in:"SourceGraphic",in2:"BackgroundImageFix",result:"shape"}),(0,s.jsx)("feGaussianBlur",{stdDeviation:"75",result:"effect1_foregroundBlur_574_3050"})]}),(0,s.jsxs)("filter",{id:"filter6_f_574_3050",x:"184.728",y:"-316.089",width:"614.024",height:"502.385",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB",children:[(0,s.jsx)("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),(0,s.jsx)("feBlend",{mode:"normal",in:"SourceGraphic",in2:"BackgroundImageFix",result:"shape"}),(0,s.jsx)("feGaussianBlur",{stdDeviation:"75",result:"effect1_foregroundBlur_574_3050"})]}),(0,s.jsxs)("filter",{id:"filter7_f_574_3050",x:"207.8",y:"-292.941",width:"564.126",height:"470.095",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB",children:[(0,s.jsx)("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),(0,s.jsx)("feBlend",{mode:"normal",in:"SourceGraphic",in2:"BackgroundImageFix",result:"shape"}),(0,s.jsx)("feGaussianBlur",{stdDeviation:"75",result:"effect1_foregroundBlur_574_3050"})]}),(0,s.jsxs)("linearGradient",{id:"paint0_linear_574_3050",x1:"336.291",y1:"-173.384",x2:"336.291",y2:"413.307",gradientUnits:"userSpaceOnUse",children:[(0,s.jsx)("stop",{stopColor:"white"}),(0,s.jsx)("stop",{offset:"1",stopColor:"white",stopOpacity:"0"})]}),(0,s.jsxs)("linearGradient",{id:"paint1_linear_574_3050",x1:"443.939",y1:"-166.229",x2:"443.939",y2:"420.271",gradientUnits:"userSpaceOnUse",children:[(0,s.jsx)("stop",{stopColor:"white"}),(0,s.jsx)("stop",{offset:"1",stopColor:"white",stopOpacity:"0"})]}),(0,s.jsxs)("linearGradient",{id:"paint2_linear_574_3050",x1:"525.295",y1:"-158.613",x2:"525.295",y2:"603.955",gradientUnits:"userSpaceOnUse",children:[(0,s.jsx)("stop",{stopColor:"white"}),(0,s.jsx)("stop",{offset:"1",stopColor:"white",stopOpacity:"0"})]}),(0,s.jsxs)("linearGradient",{id:"paint3_linear_574_3050",x1:"525.295",y1:"-158.613",x2:"525.295",y2:"202.721",gradientUnits:"userSpaceOnUse",children:[(0,s.jsx)("stop",{stopColor:"white"}),(0,s.jsx)("stop",{offset:"1",stopColor:"white",stopOpacity:"0"})]}),(0,s.jsxs)("linearGradient",{id:"paint4_linear_574_3050",x1:"410.065",y1:"-140.616",x2:"410.065",y2:"622.384",gradientUnits:"userSpaceOnUse",children:[(0,s.jsx)("stop",{stopColor:"white"}),(0,s.jsx)("stop",{offset:"1",stopColor:"white",stopOpacity:"0"})]}),(0,s.jsxs)("linearGradient",{id:"paint5_linear_574_3050",x1:"467.928",y1:"-163.531",x2:"467.928",y2:"211.469",gradientUnits:"userSpaceOnUse",children:[(0,s.jsx)("stop",{stopColor:"white"}),(0,s.jsx)("stop",{offset:"1",stopColor:"white",stopOpacity:"0"})]}),(0,s.jsxs)("linearGradient",{id:"paint6_linear_574_3050",x1:"491.74",y1:"-160.396",x2:"491.74",y2:"30.6037",gradientUnits:"userSpaceOnUse",children:[(0,s.jsx)("stop",{stopColor:"white"}),(0,s.jsx)("stop",{offset:"1",stopColor:"white",stopOpacity:"0"})]}),(0,s.jsxs)("linearGradient",{id:"paint7_linear_574_3050",x1:"489.863",y1:"-138.143",x2:"489.863",y2:"22.3566",gradientUnits:"userSpaceOnUse",children:[(0,s.jsx)("stop",{stopColor:"white"}),(0,s.jsx)("stop",{offset:"1",stopColor:"white",stopOpacity:"0"})]})]})]});function G({className:e}){return(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"472",height:"638",viewBox:"0 0 472 638",fill:"none",className:e,children:[(0,s.jsxs)("g",{opacity:"0.4",children:[(0,s.jsx)("g",{style:{mixBlendMode:"lighten"},filter:"url(#filter0_f_825_3716)",children:(0,s.jsx)("ellipse",{cx:"184.597",cy:"353.647",rx:"16.3892",ry:"146.673",transform:"rotate(15.0538 184.597 353.647)",fill:"url(#paint0_linear_825_3716)",fillOpacity:"0.5"})}),(0,s.jsx)("g",{style:{mixBlendMode:"color-dodge"},filter:"url(#filter1_f_825_3716)",children:(0,s.jsx)("ellipse",{cx:"237.5",cy:"343.125",rx:"13.25",ry:"146.625",fill:"url(#paint1_linear_825_3716)",fillOpacity:"0.5"})}),(0,s.jsx)("g",{style:{mixBlendMode:"lighten"},filter:"url(#filter2_f_825_3716)",children:(0,s.jsx)("ellipse",{cx:"289.17",cy:"378.792",rx:"11.1897",ry:"190.642",transform:"rotate(-15 289.17 378.792)",fill:"url(#paint2_linear_825_3716)",fillOpacity:"0.5"})}),(0,s.jsx)("g",{style:{mixBlendMode:"lighten"},filter:"url(#filter3_f_825_3716)",children:(0,s.jsx)("ellipse",{cx:"263.208",cy:"281.902",rx:"11.1897",ry:"90.3336",transform:"rotate(-15 263.208 281.902)",fill:"url(#paint3_linear_825_3716)",fillOpacity:"0.5"})}),(0,s.jsx)("g",{style:{mixBlendMode:"lighten"},filter:"url(#filter4_f_825_3716)",children:(0,s.jsx)("ellipse",{cx:"235.875",cy:"402.5",rx:"11.125",ry:"190.75",fill:"url(#paint4_linear_825_3716)",fillOpacity:"0.5"})}),(0,s.jsx)("g",{style:{mixBlendMode:"lighten"},filter:"url(#filter5_f_825_3716)",children:(0,s.jsx)("ellipse",{cx:"235.75",cy:"290.25",rx:"160.75",ry:"93.75",fill:"url(#paint5_linear_825_3716)",fillOpacity:"0.5"})}),(0,s.jsx)("g",{style:{mixBlendMode:"lighten"},filter:"url(#filter6_f_825_3716)",children:(0,s.jsx)("ellipse",{cx:"235.75",cy:"244.25",rx:"80.25",ry:"47.75",fill:"url(#paint6_linear_825_3716)",fillOpacity:"0.5"})}),(0,s.jsx)("g",{style:{mixBlendMode:"lighten"},filter:"url(#filter7_f_825_3716)",children:(0,s.jsx)("ellipse",{cx:"235.75",cy:"247.875",rx:"67.5",ry:"40.125",fill:"url(#paint7_linear_825_3716)",fillOpacity:"0.5"})})]}),(0,s.jsx)("mask",{id:"path-9-inside-1_825_3716",fill:"white",children:(0,s.jsx)("path",{d:"M204 161H212V593H204V161Z"})}),(0,s.jsx)("path",{d:"M211.5 161V593H212.5V161H211.5ZM204.5 593V161H203.5V593H204.5Z",fill:"url(#paint8_angular_825_3716)",fillOpacity:"0.5",mask:"url(#path-9-inside-1_825_3716)"}),(0,s.jsx)("mask",{id:"path-11-inside-2_825_3716",fill:"white",children:(0,s.jsx)("path",{d:"M180 51H188V483H180V51Z"})}),(0,s.jsx)("path",{d:"M187.5 51V483H188.5V51H187.5ZM180.5 483V51H179.5V483H180.5Z",fill:"url(#paint9_angular_825_3716)",fillOpacity:"0.2",mask:"url(#path-11-inside-2_825_3716)"}),(0,s.jsx)("mask",{id:"path-13-inside-3_825_3716",fill:"white",children:(0,s.jsx)("path",{d:"M228 101H236V533H228V101Z"})}),(0,s.jsx)("path",{d:"M235.5 101V533H236.5V101H235.5ZM228.5 533V101H227.5V533H228.5Z",fill:"url(#paint10_angular_825_3716)",fillOpacity:"0.3",mask:"url(#path-13-inside-3_825_3716)"}),(0,s.jsx)("mask",{id:"path-15-inside-4_825_3716",fill:"white",children:(0,s.jsx)("path",{d:"M252 191H260V623H252V191Z"})}),(0,s.jsx)("path",{d:"M259.5 191V623H260.5V191H259.5ZM252.5 623V191H251.5V623H252.5Z",fill:"url(#paint11_angular_825_3716)",fillOpacity:"0.8",mask:"url(#path-15-inside-4_825_3716)"}),(0,s.jsx)("mask",{id:"path-17-inside-5_825_3716",fill:"white",children:(0,s.jsx)("path",{d:"M276 1H284V433H276V1Z"})}),(0,s.jsx)("path",{d:"M283.5 1V433H284.5V1H283.5ZM276.5 433V1H275.5V433H276.5Z",fill:"url(#paint12_angular_825_3716)",fillOpacity:"0.1",mask:"url(#path-17-inside-5_825_3716)"}),(0,s.jsxs)("defs",{children:[(0,s.jsxs)("filter",{id:"filter0_f_825_3716",x:"98.835",y:"167.442",width:"171.524",height:"372.409",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB",children:[(0,s.jsx)("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),(0,s.jsx)("feBlend",{mode:"normal",in:"SourceGraphic",in2:"BackgroundImageFix",result:"shape"}),(0,s.jsx)("feGaussianBlur",{stdDeviation:"22.25",result:"effect1_foregroundBlur_825_3716"})]}),(0,s.jsxs)("filter",{id:"filter1_f_825_3716",x:"179.75",y:"152",width:"115.5",height:"382.25",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB",children:[(0,s.jsx)("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),(0,s.jsx)("feBlend",{mode:"normal",in:"SourceGraphic",in2:"BackgroundImageFix",result:"shape"}),(0,s.jsx)("feGaussianBlur",{stdDeviation:"22.25",result:"effect1_foregroundBlur_825_3716"})]}),(0,s.jsxs)("filter",{id:"filter2_f_825_3716",x:"194.147",y:"150.123",width:"190.045",height:"457.338",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB",children:[(0,s.jsx)("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),(0,s.jsx)("feBlend",{mode:"normal",in:"SourceGraphic",in2:"BackgroundImageFix",result:"shape"}),(0,s.jsx)("feGaussianBlur",{stdDeviation:"22.25",result:"effect1_foregroundBlur_825_3716"})]}),(0,s.jsxs)("filter",{id:"filter3_f_825_3716",x:"192.944",y:"150.097",width:"140.527",height:"263.609",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB",children:[(0,s.jsx)("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),(0,s.jsx)("feBlend",{mode:"normal",in:"SourceGraphic",in2:"BackgroundImageFix",result:"shape"}),(0,s.jsx)("feGaussianBlur",{stdDeviation:"22.25",result:"effect1_foregroundBlur_825_3716"})]}),(0,s.jsxs)("filter",{id:"filter4_f_825_3716",x:"180.25",y:"167.25",width:"111.25",height:"470.5",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB",children:[(0,s.jsx)("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),(0,s.jsx)("feBlend",{mode:"normal",in:"SourceGraphic",in2:"BackgroundImageFix",result:"shape"}),(0,s.jsx)("feGaussianBlur",{stdDeviation:"22.25",result:"effect1_foregroundBlur_825_3716"})]}),(0,s.jsxs)("filter",{id:"filter5_f_825_3716",x:"7.62939e-06",y:"121.5",width:"471.5",height:"337.5",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB",children:[(0,s.jsx)("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),(0,s.jsx)("feBlend",{mode:"normal",in:"SourceGraphic",in2:"BackgroundImageFix",result:"shape"}),(0,s.jsx)("feGaussianBlur",{stdDeviation:"37.5",result:"effect1_foregroundBlur_825_3716"})]}),(0,s.jsxs)("filter",{id:"filter6_f_825_3716",x:"80.5",y:"121.5",width:"310.5",height:"245.5",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB",children:[(0,s.jsx)("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),(0,s.jsx)("feBlend",{mode:"normal",in:"SourceGraphic",in2:"BackgroundImageFix",result:"shape"}),(0,s.jsx)("feGaussianBlur",{stdDeviation:"37.5",result:"effect1_foregroundBlur_825_3716"})]}),(0,s.jsxs)("filter",{id:"filter7_f_825_3716",x:"93.25",y:"132.75",width:"285",height:"230.25",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB",children:[(0,s.jsx)("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),(0,s.jsx)("feBlend",{mode:"normal",in:"SourceGraphic",in2:"BackgroundImageFix",result:"shape"}),(0,s.jsx)("feGaussianBlur",{stdDeviation:"37.5",result:"effect1_foregroundBlur_825_3716"})]}),(0,s.jsxs)("linearGradient",{id:"paint0_linear_825_3716",x1:"184.597",y1:"206.974",x2:"184.597",y2:"500.319",gradientUnits:"userSpaceOnUse",children:[(0,s.jsx)("stop",{stopColor:"white"}),(0,s.jsx)("stop",{offset:"1",stopColor:"white",stopOpacity:"0"})]}),(0,s.jsxs)("linearGradient",{id:"paint1_linear_825_3716",x1:"237.5",y1:"196.5",x2:"237.5",y2:"489.75",gradientUnits:"userSpaceOnUse",children:[(0,s.jsx)("stop",{stopColor:"white"}),(0,s.jsx)("stop",{offset:"1",stopColor:"white",stopOpacity:"0"})]}),(0,s.jsxs)("linearGradient",{id:"paint2_linear_825_3716",x1:"289.17",y1:"188.151",x2:"289.17",y2:"569.434",gradientUnits:"userSpaceOnUse",children:[(0,s.jsx)("stop",{stopColor:"white"}),(0,s.jsx)("stop",{offset:"1",stopColor:"white",stopOpacity:"0"})]}),(0,s.jsxs)("linearGradient",{id:"paint3_linear_825_3716",x1:"263.208",y1:"191.568",x2:"263.208",y2:"372.236",gradientUnits:"userSpaceOnUse",children:[(0,s.jsx)("stop",{stopColor:"white"}),(0,s.jsx)("stop",{offset:"1",stopColor:"white",stopOpacity:"0"})]}),(0,s.jsxs)("linearGradient",{id:"paint4_linear_825_3716",x1:"235.875",y1:"211.75",x2:"235.875",y2:"593.251",gradientUnits:"userSpaceOnUse",children:[(0,s.jsx)("stop",{stopColor:"white"}),(0,s.jsx)("stop",{offset:"1",stopColor:"white",stopOpacity:"0"})]}),(0,s.jsxs)("linearGradient",{id:"paint5_linear_825_3716",x1:"235.75",y1:"196.5",x2:"235.75",y2:"384.001",gradientUnits:"userSpaceOnUse",children:[(0,s.jsx)("stop",{stopColor:"white"}),(0,s.jsx)("stop",{offset:"1",stopColor:"white",stopOpacity:"0"})]}),(0,s.jsxs)("linearGradient",{id:"paint6_linear_825_3716",x1:"235.75",y1:"196.5",x2:"235.75",y2:"292",gradientUnits:"userSpaceOnUse",children:[(0,s.jsx)("stop",{stopColor:"white"}),(0,s.jsx)("stop",{offset:"1",stopColor:"white",stopOpacity:"0"})]}),(0,s.jsxs)("linearGradient",{id:"paint7_linear_825_3716",x1:"235.75",y1:"207.75",x2:"235.75",y2:"288",gradientUnits:"userSpaceOnUse",children:[(0,s.jsx)("stop",{stopColor:"white"}),(0,s.jsx)("stop",{offset:"1",stopColor:"white",stopOpacity:"0"})]}),(0,s.jsxs)("radialGradient",{id:"paint8_angular_825_3716",cx:"0",cy:"0",r:"1",gradientUnits:"userSpaceOnUse",gradientTransform:"translate(208 481) scale(32 185)",children:[(0,s.jsx)("stop",{stopColor:"white"}),(0,s.jsx)("stop",{offset:"0.0001",stopColor:"white",stopOpacity:"0"}),(0,s.jsx)("stop",{offset:"0.784842",stopColor:"white",stopOpacity:"0"})]}),(0,s.jsxs)("radialGradient",{id:"paint9_angular_825_3716",cx:"0",cy:"0",r:"1",gradientUnits:"userSpaceOnUse",gradientTransform:"translate(184 371) scale(32 185)",children:[(0,s.jsx)("stop",{stopColor:"white"}),(0,s.jsx)("stop",{offset:"0.0001",stopColor:"white",stopOpacity:"0"}),(0,s.jsx)("stop",{offset:"0.784842",stopColor:"white",stopOpacity:"0"})]}),(0,s.jsxs)("radialGradient",{id:"paint10_angular_825_3716",cx:"0",cy:"0",r:"1",gradientUnits:"userSpaceOnUse",gradientTransform:"translate(232 421) scale(32 185)",children:[(0,s.jsx)("stop",{stopColor:"white"}),(0,s.jsx)("stop",{offset:"0.0001",stopColor:"white",stopOpacity:"0"}),(0,s.jsx)("stop",{offset:"0.784842",stopColor:"white",stopOpacity:"0"})]}),(0,s.jsxs)("radialGradient",{id:"paint11_angular_825_3716",cx:"0",cy:"0",r:"1",gradientUnits:"userSpaceOnUse",gradientTransform:"translate(256 511) scale(32 185)",children:[(0,s.jsx)("stop",{stopColor:"white"}),(0,s.jsx)("stop",{offset:"0.0001",stopColor:"white",stopOpacity:"0"}),(0,s.jsx)("stop",{offset:"0.784842",stopColor:"white",stopOpacity:"0"})]}),(0,s.jsxs)("radialGradient",{id:"paint12_angular_825_3716",cx:"0",cy:"0",r:"1",gradientUnits:"userSpaceOnUse",gradientTransform:"translate(280 321) scale(32 185)",children:[(0,s.jsx)("stop",{stopColor:"white"}),(0,s.jsx)("stop",{offset:"0.0001",stopColor:"white",stopOpacity:"0"}),(0,s.jsx)("stop",{offset:"0.784842",stopColor:"white",stopOpacity:"0"})]})]})]})}var z=i(26993),q=i(24767);let K=(0,q.A)("log-in",[["path",{d:"m10 17 5-5-5-5",key:"1bsop3"}],["path",{d:"M15 12H3",key:"6jk70r"}],["path",{d:"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4",key:"u53s6r"}]]),W=(0,q.A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]);var Z=i(49499),$=i.n(Z),Y=i(76842);let X=[{name:"Fireworks",url:"/images/logo-cloud/fireworks-ai.svg",href:"https://fireworks.ai"},{name:"cal.com",url:"/images/logo-cloud/calcom.svg",href:"https://cal.com"},{name:"Mintlify",url:"/images/logo-cloud/mintlify.svg",href:"https://mintlify.com"}];function J(){return(0,s.jsxs)("div",{className:"hidden md:flex w-full flex-col items-center",children:[(0,s.jsx)("span",{className:d("font-mono text-sm md:text-md text-white/50 text-center opacity-0 animate-fade-in-up [animation-delay:1s]"),children:"Powering"}),(0,s.jsx)("div",{className:"flex w-full flex-col items-center justify-center px-4 md:px-8",children:(0,s.jsx)("div",{className:"mt-10 grid grid-cols-3 gap-x-6",children:X.map((e,t)=>(0,s.jsx)("div",{className:d("relative w-[229px] aspect-[229/36]","opacity-0 animate-fade-in-up [animation-delay:var(--animation-delay)]"),style:{"--animation-delay":`calc(1.1s + .12s * ${t})`},children:(0,s.jsx)($(),{href:e.href,target:"_blank",children:(0,s.jsx)(S(),{src:e.url,alt:e.name,fill:!0})})},String(t)))})})]})}let Q=()=>(0,s.jsxs)("div",{className:"md:hidden w-full flex flex-col items-center",children:[(0,s.jsx)("span",{className:d("font-mono text-sm md:text-md text-white/50 text-center"),children:"Powering"}),(0,s.jsx)("div",{className:"w-full px-4 md:px-8",children:(0,s.jsx)("div",{className:"group relative mt-6 flex gap-6 overflow-hidden p-2",style:{maskImage:"linear-gradient(to left, transparent 0%, black 20%, black 80%, transparent 95%)"},children:[0,1,2].map(e=>(0,s.jsx)("div",{className:"flex shrink-0 animate-logo-cloud flex-row justify-around gap-6",children:X.map((t,i)=>(0,s.jsx)("div",{className:"relative w-[229px] aspect-[229/36]",children:(0,s.jsx)($(),{href:t.href,target:"_blank",children:(0,s.jsx)(S(),{src:t.url,alt:t.name,fill:!0})})},`${e}-${t.name}`))},`logo-group-${e}`))})})]});var ee=i(75603),et=i(19257),ei=i(77422),es=i(56450);let er=async({params:e})=>{let{locale:t}=await e,i=await (0,ei.T)(t);return(0,es.w)(i.web.home.meta)},en=async({params:e})=>{let{locale:t}=await e;await (0,ei.T)(t);let i=await (0,et.showBetaFeature)();return(0,s.jsxs)(s.Fragment,{children:[i&&(0,s.jsx)("div",{className:"w-full bg-black py-2 text-center text-white",children:"Beta feature now available"}),(0,s.jsx)(H,{}),(0,s.jsx)(D,{}),(0,s.jsxs)("div",{className:"relative w-full pt-6 overflow-hidden",children:[(0,s.jsx)("div",{className:"container relative mx-auto",children:(0,s.jsx)(S(),{src:Y.default,alt:"Animated SVG showing computer circuits lighting up",className:"absolute inset-x-0 flex  xl:hidden -z-10 scale-[2]",priority:!0})}),(0,s.jsxs)("div",{className:"container relative flex flex-col mx-auto space-y-16 md:space-y-32",children:[(0,s.jsx)(F,{children:(0,s.jsx)(k.Hero,{})}),(0,s.jsxs)(F,{className:"mt-16 md:mt-32",children:[(0,s.jsx)(J,{}),(0,s.jsx)(Q,{})]}),(0,s.jsx)(F,{className:"mt-16 md:mt-18",children:(0,s.jsx)(ee.CodeExamples,{})}),(0,s.jsx)(F,{className:"mt-16 md:mt-18",children:(0,s.jsx)(N.OpenSource,{})}),(0,s.jsxs)(F,{className:"mt-16 md:mt-20",children:[(0,s.jsx)(I,{className:"mt-8 md:mt-16 lg:mt-32",title:"Everything you need for your API",text:"Build, monetize, analyze, and protect your APIs; our platform makes it easy, providing everything you need.",align:"center"}),(0,s.jsx)(r,{}),(0,s.jsxs)("div",{className:"mt-6 grid md:grid-cols-[1fr_1fr] lg:grid-cols-[1fr_2fr] gap-6 z-50",children:[(0,s.jsx)(P,{}),(0,s.jsx)(z.UsageBento,{})]})]}),(0,s.jsx)("div",{className:"relative w-full -z-10 ",children:(0,s.jsx)(G,{className:"absolute scale-[2] left-[-70px] sm:left-[70px] md:left-[150px] lg:left-[200px] xl:left-[420px] top-[-250px]"})}),(0,s.jsxs)(F,{className:"mt-16 md:mt-32",children:[(0,s.jsx)(I,{title:"Secure and scalable from day one",text:"Start secure. Our platform includes essential security features such as one-way hashed keys, audit logs, and rate limiting, enabling rapid API iteration and scaling.",align:"center",children:(0,s.jsxs)("div",{className:"flex mt-10 mb-10 space-x-6",children:[(0,s.jsx)($(),{href:"https://app.unkey.com",className:"group",children:(0,s.jsx)(c,{shiny:!0,IconLeft:K,label:"Get Started",className:"h-10"})}),(0,s.jsx)($(),{href:"/docs",children:(0,s.jsx)(u,{label:"Visit the Docs",IconRight:W})})]})}),(0,s.jsxs)("div",{className:"grid xl:grid-cols-[2fr_3fr] gap-6",children:[(0,s.jsx)(b,{}),(0,s.jsx)(a,{})]}),(0,s.jsxs)("div",{className:"relative grid md:grid-cols-[1fr_1fr] xl:grid-cols-[3fr_2fr] gap-6 z-50",children:[(0,s.jsx)("div",{"aria-hidden":!0,className:"hidden lg:block pointer-events-none absolute top-[calc(100%-51px)] right-[226px] lg:right-[500px] aspect-[1400/541] w-[1400px]",children:(0,s.jsx)(M,{src:"/images/landing/leveled-up-api-auth-chip-min.svg",alt:"Visual decoration auth chip",fill:!0})}),(0,s.jsx)(V,{}),(0,s.jsx)(T,{})]})]}),(0,s.jsxs)(F,{className:"mt-16 md:mt-32",children:[(0,s.jsx)("div",{className:"relative",children:(0,s.jsx)(I,{className:"mt-8 md:mt-16 lg:mt-32 xl:mt-48",title:"Leveled-up API development",text:"You gain enhanced security, low latency, and better control, enabling seamless API integration and unparalleled data protection.",children:(0,s.jsxs)("div",{className:"flex mt-10 mb-10 space-x-6",children:[(0,s.jsx)($(),{href:"https://app.unkey.com",className:"group",children:(0,s.jsx)(c,{shiny:!0,IconLeft:K,label:"Get Started",className:"h-10"})}),(0,s.jsx)($(),{href:"/docs",children:(0,s.jsx)(u,{label:"Visit the Docs",IconRight:W})})]})})}),(0,s.jsx)(w,{className:"relative z-50 mt-20"}),(0,s.jsx)("div",{className:"relative -z-10",children:(0,s.jsx)(U,{className:"absolute top-[50px] left-[400px]"})})]}),(0,s.jsx)(F,{className:"mt-16 md:mt-32",children:(0,s.jsx)(p.CTA,{})})]})]})]})}},31421:e=>{"use strict";e.exports=require("node:child_process")},33159:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"warnOnce",{enumerable:!0,get:function(){return i}});let i=e=>{}},33873:e=>{"use strict";e.exports=require("path")},34500:(e,t,i)=>{"use strict";i.d(t,{B:()=>s});let s="undefined"!=typeof window},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},37067:e=>{"use strict";e.exports=require("node:http")},38522:e=>{"use strict";e.exports=require("node:zlib")},38878:(e,t,i)=>{"use strict";i.d(t,{HashedKeys:()=>x});var s=i(99730),r=i(35371),n=i(70022),a=i(48190),o=i(26647),l=i(19161);let h=(0,l.A)("key",[["path",{d:"m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4",key:"g0fldk"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}]]),d=(0,l.A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]),c=(0,l.A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);var u=i(44669),p=i(60628),f=i(81700);let m=({keyData:e,className:t})=>{let[i,a]=(0,r.useState)(!1),[l,f]=(0,r.useState)(!1),m=async()=>{try{await navigator.clipboard.writeText(e.hash),f(!0),setTimeout(()=>f(!1),2e3)}catch(e){console.error("Failed to copy:",e)}},x=i?e.hash:e.prefix+"•".repeat(32)+e.hash.slice(-4);return(0,s.jsx)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},className:(0,o.cn)("flex items-center justify-between rounded-lg border border-white/10 bg-white/5 p-4 backdrop-blur-sm",t),children:(0,s.jsxs)("div",{className:"flex items-center space-x-3 flex-1 min-w-0",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)(h,{className:"h-5 w-5 text-blue-400"})}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-white truncate",children:e.name}),(0,s.jsx)("span",{className:(0,o.cn)("px-2 py-1 text-xs font-medium rounded-full border",(()=>{switch(e.status){case"active":return"text-green-400 bg-green-400/10 border-green-400/20";case"revoked":return"text-red-400 bg-red-400/10 border-red-400/20";case"expired":return"text-yellow-400 bg-yellow-400/10 border-yellow-400/20";default:return"text-gray-400 bg-gray-400/10 border-gray-400/20"}})()),children:e.status})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2 mt-1",children:[(0,s.jsx)("code",{className:"text-xs text-white/70 font-mono bg-black/20 px-2 py-1 rounded truncate max-w-xs",children:x}),(0,s.jsx)("button",{onClick:()=>a(!i),className:"text-white/60 hover:text-white transition-colors",title:i?"Hide key":"Show key",children:i?(0,s.jsx)(d,{className:"h-4 w-4"}):(0,s.jsx)(c,{className:"h-4 w-4"})}),(0,s.jsx)("button",{onClick:m,className:"text-white/60 hover:text-white transition-colors",title:"Copy key",children:l?(0,s.jsx)(u.A,{className:"h-4 w-4 text-green-400"}):(0,s.jsx)(p.A,{className:"h-4 w-4"})})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-4 mt-2 text-xs text-white/60",children:[(0,s.jsxs)("span",{children:["Created: ",e.created]}),(0,s.jsxs)("span",{children:["Last used: ",e.lastUsed]})]})]})]})})},x=({className:e,maxKeys:t=4})=>{let[i,n]=(0,r.useState)([]),l=[{id:"1",name:"Production API Key",prefix:"uk_",hash:"uk_1234567890abcdef1234567890abcdef12345678",created:"2 days ago",lastUsed:"5 min ago",status:"active"},{id:"2",name:"Development Key",prefix:"uk_",hash:"uk_abcdef1234567890abcdef1234567890abcdef12",created:"1 week ago",lastUsed:"2 hours ago",status:"active"},{id:"3",name:"Staging Environment",prefix:"uk_",hash:"uk_567890abcdef1234567890abcdef1234567890ab",created:"3 days ago",lastUsed:"1 day ago",status:"expired"},{id:"4",name:"Legacy Integration",prefix:"uk_",hash:"uk_cdef1234567890abcdef1234567890abcdef1234",created:"2 weeks ago",lastUsed:"1 week ago",status:"revoked"}];return(0,r.useEffect)(()=>{let e=setTimeout(()=>{n(l.slice(0,t))},500);return()=>clearTimeout(e)},[t]),(0,s.jsxs)("div",{className:(0,o.cn)("space-y-3",e),children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,s.jsx)(f.A,{className:"h-5 w-5 text-blue-400"}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Hashed API Keys"}),(0,s.jsx)("div",{className:"flex-1"}),(0,s.jsxs)("div",{className:"text-xs text-white/60",children:[i.length," keys"]})]}),(0,s.jsx)("div",{className:"space-y-2",children:(0,s.jsx)(a.N,{mode:"popLayout",children:i.map(e=>(0,s.jsx)(m,{keyData:e},e.id))})}),0===i.length&&(0,s.jsxs)("div",{className:"text-center py-8 text-white/60",children:[(0,s.jsx)(h,{className:"h-8 w-8 mx-auto mb-2 opacity-50"}),(0,s.jsx)("p",{className:"text-sm",children:"Loading API keys..."})]}),(0,s.jsx)("div",{className:"mt-4 p-3 rounded-lg bg-blue-400/10 border border-blue-400/20",children:(0,s.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,s.jsx)(f.A,{className:"h-4 w-4 text-blue-400 mt-0.5 flex-shrink-0"}),(0,s.jsxs)("div",{className:"text-xs text-blue-300",children:[(0,s.jsx)("p",{className:"font-medium mb-1",children:"One-way hashed keys"}),(0,s.jsx)("p",{className:"text-blue-300/80",children:"Keys are hashed using SHA-256 and cannot be reversed. Only the hash is stored in our database for maximum security."})]})]})})]})}},39281:(e,t,i)=>{"use strict";i.d(t,{CTA:()=>s});let s=(0,i(6340).registerClientReference)(function(){throw Error("Attempted to call CTA() from the server but CTA is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\web\\components\\cta.tsx","CTA")},39890:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>s});let s={src:"/_next/static/media/mainboard.9232efd0.svg",height:546,width:1512,blurWidth:0,blurHeight:0}},41155:(e,t,i)=>{"use strict";i.d(t,{OpenSource:()=>x});var s=i(99730),r=i(11252),n=i(70022);let a=(0,i(19161).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]]);var o=i(41265),l=i.n(o);let h={src:"/_next/static/media/unkey-github.8490178e.svg",height:24,width:24,blurWidth:0,blurHeight:0};var d=i(42849),c=i(48473);let u=e=>(0,s.jsx)(c.default,{...e,placeholder:"blur",blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8+e1bKQAJMQNc5W2CQwAAAABJRU5ErkJggg=="});i(35371);let p=({className:e})=>(0,s.jsxs)("svg",{className:e,viewBox:"0 0 200 100",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,s.jsxs)("defs",{children:[(0,s.jsxs)("linearGradient",{id:"ossGradient",x1:"0%",y1:"0%",x2:"100%",y2:"100%",children:[(0,s.jsx)("stop",{offset:"0%",stopColor:"#3b82f6",stopOpacity:"0.8"}),(0,s.jsx)("stop",{offset:"50%",stopColor:"#8b5cf6",stopOpacity:"0.6"}),(0,s.jsx)("stop",{offset:"100%",stopColor:"#06b6d4",stopOpacity:"0.4"})]}),(0,s.jsxs)("filter",{id:"glow",children:[(0,s.jsx)("feGaussianBlur",{stdDeviation:"3",result:"coloredBlur"}),(0,s.jsxs)("feMerge",{children:[(0,s.jsx)("feMergeNode",{in:"coloredBlur"}),(0,s.jsx)("feMergeNode",{in:"SourceGraphic"})]})]})]}),(0,s.jsx)("rect",{x:"10",y:"20",width:"180",height:"60",rx:"30",fill:"url(#ossGradient)",filter:"url(#glow)",opacity:"0.7"}),(0,s.jsx)("path",{d:"M30 50 L50 50 L50 30 L80 30 L80 50 L120 50 L120 70 L170 70",stroke:"currentColor",strokeWidth:"2",fill:"none",opacity:"0.6"}),(0,s.jsx)("circle",{cx:"30",cy:"50",r:"3",fill:"currentColor",opacity:"0.8"}),(0,s.jsx)("circle",{cx:"80",cy:"30",r:"3",fill:"currentColor",opacity:"0.8"}),(0,s.jsx)("circle",{cx:"120",cy:"50",r:"3",fill:"currentColor",opacity:"0.8"}),(0,s.jsx)("circle",{cx:"170",cy:"70",r:"3",fill:"currentColor",opacity:"0.8"}),(0,s.jsx)("rect",{x:"60",y:"45",width:"10",height:"10",rx:"2",fill:"currentColor",opacity:"0.5"}),(0,s.jsx)("rect",{x:"140",y:"35",width:"8",height:"8",rx:"1",fill:"currentColor",opacity:"0.5"})]});function f({className:e}){return(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"472",height:"638",viewBox:"0 0 472 638",fill:"none",className:e,children:[(0,s.jsxs)("g",{opacity:"0.4",children:[(0,s.jsx)("g",{style:{mixBlendMode:"lighten"},filter:"url(#filter0_f_825_3716)",children:(0,s.jsx)("ellipse",{cx:"184.597",cy:"353.647",rx:"16.3892",ry:"146.673",transform:"rotate(15.0538 184.597 353.647)",fill:"url(#paint0_linear_825_3716)",fillOpacity:"0.5"})}),(0,s.jsx)("g",{style:{mixBlendMode:"color-dodge"},filter:"url(#filter1_f_825_3716)",children:(0,s.jsx)("ellipse",{cx:"237.5",cy:"343.125",rx:"13.25",ry:"146.625",fill:"url(#paint1_linear_825_3716)",fillOpacity:"0.5"})}),(0,s.jsx)("g",{style:{mixBlendMode:"lighten"},filter:"url(#filter2_f_825_3716)",children:(0,s.jsx)("ellipse",{cx:"289.17",cy:"378.792",rx:"11.1897",ry:"190.642",transform:"rotate(-15 289.17 378.792)",fill:"url(#paint2_linear_825_3716)",fillOpacity:"0.5"})}),(0,s.jsx)("g",{style:{mixBlendMode:"lighten"},filter:"url(#filter3_f_825_3716)",children:(0,s.jsx)("ellipse",{cx:"263.208",cy:"281.902",rx:"11.1897",ry:"90.3336",transform:"rotate(-15 263.208 281.902)",fill:"url(#paint3_linear_825_3716)",fillOpacity:"0.5"})}),(0,s.jsx)("g",{style:{mixBlendMode:"lighten"},filter:"url(#filter4_f_825_3716)",children:(0,s.jsx)("ellipse",{cx:"235.875",cy:"402.5",rx:"11.125",ry:"190.75",fill:"url(#paint4_linear_825_3716)",fillOpacity:"0.5"})}),(0,s.jsx)("g",{style:{mixBlendMode:"lighten"},filter:"url(#filter5_f_825_3716)",children:(0,s.jsx)("ellipse",{cx:"235.75",cy:"290.25",rx:"160.75",ry:"93.75",fill:"url(#paint5_linear_825_3716)",fillOpacity:"0.5"})}),(0,s.jsx)("g",{style:{mixBlendMode:"lighten"},filter:"url(#filter6_f_825_3716)",children:(0,s.jsx)("ellipse",{cx:"235.75",cy:"244.25",rx:"80.25",ry:"47.75",fill:"url(#paint6_linear_825_3716)",fillOpacity:"0.5"})}),(0,s.jsx)("g",{style:{mixBlendMode:"lighten"},filter:"url(#filter7_f_825_3716)",children:(0,s.jsx)("ellipse",{cx:"235.75",cy:"247.875",rx:"67.5",ry:"40.125",fill:"url(#paint7_linear_825_3716)",fillOpacity:"0.5"})})]}),(0,s.jsx)("mask",{id:"path-9-inside-1_825_3716",fill:"white",children:(0,s.jsx)("path",{d:"M204 161H212V593H204V161Z"})}),(0,s.jsx)("path",{d:"M211.5 161V593H212.5V161H211.5ZM204.5 593V161H203.5V593H204.5Z",fill:"url(#paint8_angular_825_3716)",fillOpacity:"0.5",mask:"url(#path-9-inside-1_825_3716)"}),(0,s.jsx)("mask",{id:"path-11-inside-2_825_3716",fill:"white",children:(0,s.jsx)("path",{d:"M180 51H188V483H180V51Z"})}),(0,s.jsx)("path",{d:"M187.5 51V483H188.5V51H187.5ZM180.5 483V51H179.5V483H180.5Z",fill:"url(#paint9_angular_825_3716)",fillOpacity:"0.2",mask:"url(#path-11-inside-2_825_3716)"}),(0,s.jsx)("mask",{id:"path-13-inside-3_825_3716",fill:"white",children:(0,s.jsx)("path",{d:"M228 101H236V533H228V101Z"})}),(0,s.jsx)("path",{d:"M235.5 101V533H236.5V101H235.5ZM228.5 533V101H227.5V533H228.5Z",fill:"url(#paint10_angular_825_3716)",fillOpacity:"0.3",mask:"url(#path-13-inside-3_825_3716)"}),(0,s.jsx)("mask",{id:"path-15-inside-4_825_3716",fill:"white",children:(0,s.jsx)("path",{d:"M252 191H260V623H252V191Z"})}),(0,s.jsx)("path",{d:"M259.5 191V623H260.5V191H259.5ZM252.5 623V191H251.5V623H252.5Z",fill:"url(#paint11_angular_825_3716)",fillOpacity:"0.8",mask:"url(#path-15-inside-4_825_3716)"}),(0,s.jsx)("mask",{id:"path-17-inside-5_825_3716",fill:"white",children:(0,s.jsx)("path",{d:"M276 1H284V433H276V1Z"})}),(0,s.jsx)("path",{d:"M283.5 1V433H284.5V1H283.5ZM276.5 433V1H275.5V433H276.5Z",fill:"url(#paint12_angular_825_3716)",fillOpacity:"0.1",mask:"url(#path-17-inside-5_825_3716)"}),(0,s.jsxs)("defs",{children:[(0,s.jsxs)("filter",{id:"filter0_f_825_3716",x:"98.835",y:"167.442",width:"171.524",height:"372.409",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB",children:[(0,s.jsx)("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),(0,s.jsx)("feBlend",{mode:"normal",in:"SourceGraphic",in2:"BackgroundImageFix",result:"shape"}),(0,s.jsx)("feGaussianBlur",{stdDeviation:"22.25",result:"effect1_foregroundBlur_825_3716"})]}),(0,s.jsxs)("filter",{id:"filter1_f_825_3716",x:"179.75",y:"152",width:"115.5",height:"382.25",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB",children:[(0,s.jsx)("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),(0,s.jsx)("feBlend",{mode:"normal",in:"SourceGraphic",in2:"BackgroundImageFix",result:"shape"}),(0,s.jsx)("feGaussianBlur",{stdDeviation:"22.25",result:"effect1_foregroundBlur_825_3716"})]}),(0,s.jsxs)("filter",{id:"filter2_f_825_3716",x:"194.147",y:"150.123",width:"190.045",height:"457.338",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB",children:[(0,s.jsx)("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),(0,s.jsx)("feBlend",{mode:"normal",in:"SourceGraphic",in2:"BackgroundImageFix",result:"shape"}),(0,s.jsx)("feGaussianBlur",{stdDeviation:"22.25",result:"effect1_foregroundBlur_825_3716"})]}),(0,s.jsxs)("filter",{id:"filter3_f_825_3716",x:"192.944",y:"150.097",width:"140.527",height:"263.609",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB",children:[(0,s.jsx)("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),(0,s.jsx)("feBlend",{mode:"normal",in:"SourceGraphic",in2:"BackgroundImageFix",result:"shape"}),(0,s.jsx)("feGaussianBlur",{stdDeviation:"22.25",result:"effect1_foregroundBlur_825_3716"})]}),(0,s.jsxs)("filter",{id:"filter4_f_825_3716",x:"180.25",y:"167.25",width:"111.25",height:"470.5",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB",children:[(0,s.jsx)("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),(0,s.jsx)("feBlend",{mode:"normal",in:"SourceGraphic",in2:"BackgroundImageFix",result:"shape"}),(0,s.jsx)("feGaussianBlur",{stdDeviation:"22.25",result:"effect1_foregroundBlur_825_3716"})]}),(0,s.jsxs)("filter",{id:"filter5_f_825_3716",x:"7.62939e-06",y:"121.5",width:"471.5",height:"337.5",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB",children:[(0,s.jsx)("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),(0,s.jsx)("feBlend",{mode:"normal",in:"SourceGraphic",in2:"BackgroundImageFix",result:"shape"}),(0,s.jsx)("feGaussianBlur",{stdDeviation:"37.5",result:"effect1_foregroundBlur_825_3716"})]}),(0,s.jsxs)("filter",{id:"filter6_f_825_3716",x:"80.5",y:"121.5",width:"310.5",height:"245.5",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB",children:[(0,s.jsx)("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),(0,s.jsx)("feBlend",{mode:"normal",in:"SourceGraphic",in2:"BackgroundImageFix",result:"shape"}),(0,s.jsx)("feGaussianBlur",{stdDeviation:"37.5",result:"effect1_foregroundBlur_825_3716"})]}),(0,s.jsxs)("filter",{id:"filter7_f_825_3716",x:"93.25",y:"132.75",width:"285",height:"230.25",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB",children:[(0,s.jsx)("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),(0,s.jsx)("feBlend",{mode:"normal",in:"SourceGraphic",in2:"BackgroundImageFix",result:"shape"}),(0,s.jsx)("feGaussianBlur",{stdDeviation:"37.5",result:"effect1_foregroundBlur_825_3716"})]}),(0,s.jsxs)("linearGradient",{id:"paint0_linear_825_3716",x1:"184.597",y1:"206.974",x2:"184.597",y2:"500.319",gradientUnits:"userSpaceOnUse",children:[(0,s.jsx)("stop",{stopColor:"white"}),(0,s.jsx)("stop",{offset:"1",stopColor:"white",stopOpacity:"0"})]}),(0,s.jsxs)("linearGradient",{id:"paint1_linear_825_3716",x1:"237.5",y1:"196.5",x2:"237.5",y2:"489.75",gradientUnits:"userSpaceOnUse",children:[(0,s.jsx)("stop",{stopColor:"white"}),(0,s.jsx)("stop",{offset:"1",stopColor:"white",stopOpacity:"0"})]}),(0,s.jsxs)("linearGradient",{id:"paint2_linear_825_3716",x1:"289.17",y1:"188.151",x2:"289.17",y2:"569.434",gradientUnits:"userSpaceOnUse",children:[(0,s.jsx)("stop",{stopColor:"white"}),(0,s.jsx)("stop",{offset:"1",stopColor:"white",stopOpacity:"0"})]}),(0,s.jsxs)("linearGradient",{id:"paint3_linear_825_3716",x1:"263.208",y1:"191.568",x2:"263.208",y2:"372.236",gradientUnits:"userSpaceOnUse",children:[(0,s.jsx)("stop",{stopColor:"white"}),(0,s.jsx)("stop",{offset:"1",stopColor:"white",stopOpacity:"0"})]}),(0,s.jsxs)("linearGradient",{id:"paint4_linear_825_3716",x1:"235.875",y1:"211.75",x2:"235.875",y2:"593.251",gradientUnits:"userSpaceOnUse",children:[(0,s.jsx)("stop",{stopColor:"white"}),(0,s.jsx)("stop",{offset:"1",stopColor:"white",stopOpacity:"0"})]}),(0,s.jsxs)("linearGradient",{id:"paint5_linear_825_3716",x1:"235.75",y1:"196.5",x2:"235.75",y2:"384.001",gradientUnits:"userSpaceOnUse",children:[(0,s.jsx)("stop",{stopColor:"white"}),(0,s.jsx)("stop",{offset:"1",stopColor:"white",stopOpacity:"0"})]}),(0,s.jsxs)("linearGradient",{id:"paint6_linear_825_3716",x1:"235.75",y1:"196.5",x2:"235.75",y2:"292",gradientUnits:"userSpaceOnUse",children:[(0,s.jsx)("stop",{stopColor:"white"}),(0,s.jsx)("stop",{offset:"1",stopColor:"white",stopOpacity:"0"})]}),(0,s.jsxs)("linearGradient",{id:"paint7_linear_825_3716",x1:"235.75",y1:"207.75",x2:"235.75",y2:"288",gradientUnits:"userSpaceOnUse",children:[(0,s.jsx)("stop",{stopColor:"white"}),(0,s.jsx)("stop",{offset:"1",stopColor:"white",stopOpacity:"0"})]}),(0,s.jsxs)("radialGradient",{id:"paint8_angular_825_3716",cx:"0",cy:"0",r:"1",gradientUnits:"userSpaceOnUse",gradientTransform:"translate(208 481) scale(32 185)",children:[(0,s.jsx)("stop",{stopColor:"white"}),(0,s.jsx)("stop",{offset:"0.0001",stopColor:"white",stopOpacity:"0"}),(0,s.jsx)("stop",{offset:"0.784842",stopColor:"white",stopOpacity:"0"})]}),(0,s.jsxs)("radialGradient",{id:"paint9_angular_825_3716",cx:"0",cy:"0",r:"1",gradientUnits:"userSpaceOnUse",gradientTransform:"translate(184 371) scale(32 185)",children:[(0,s.jsx)("stop",{stopColor:"white"}),(0,s.jsx)("stop",{offset:"0.0001",stopColor:"white",stopOpacity:"0"}),(0,s.jsx)("stop",{offset:"0.784842",stopColor:"white",stopOpacity:"0"})]}),(0,s.jsxs)("radialGradient",{id:"paint10_angular_825_3716",cx:"0",cy:"0",r:"1",gradientUnits:"userSpaceOnUse",gradientTransform:"translate(232 421) scale(32 185)",children:[(0,s.jsx)("stop",{stopColor:"white"}),(0,s.jsx)("stop",{offset:"0.0001",stopColor:"white",stopOpacity:"0"}),(0,s.jsx)("stop",{offset:"0.784842",stopColor:"white",stopOpacity:"0"})]}),(0,s.jsxs)("radialGradient",{id:"paint11_angular_825_3716",cx:"0",cy:"0",r:"1",gradientUnits:"userSpaceOnUse",gradientTransform:"translate(256 511) scale(32 185)",children:[(0,s.jsx)("stop",{stopColor:"white"}),(0,s.jsx)("stop",{offset:"0.0001",stopColor:"white",stopOpacity:"0"}),(0,s.jsx)("stop",{offset:"0.784842",stopColor:"white",stopOpacity:"0"})]}),(0,s.jsxs)("radialGradient",{id:"paint12_angular_825_3716",cx:"0",cy:"0",r:"1",gradientUnits:"userSpaceOnUse",gradientTransform:"translate(280 321) scale(32 185)",children:[(0,s.jsx)("stop",{stopColor:"white"}),(0,s.jsx)("stop",{offset:"0.0001",stopColor:"white",stopOpacity:"0"}),(0,s.jsx)("stop",{offset:"0.784842",stopColor:"white",stopOpacity:"0"})]})]})]})}var m=i(56827);let x=()=>(0,s.jsxs)("div",{className:"pt-[00px] flex items-center flex-col md:flex-row relative",children:[(0,s.jsxs)("div",{className:"absolute top-[-320px] md:top-[-480px] xl:right-[120px] -z-[10]",children:[(0,s.jsx)(f,{className:"scale-[2]"}),(0,s.jsxs)("div",{className:"absolute right-[270px] top-[250px] -z-50",children:[(0,s.jsx)(m.Aj,{className:"ml-2 fade-in-0",delay:2,number:1}),(0,s.jsx)(m.Aj,{className:"ml-10 fade-in-40",number:1,delay:0}),(0,s.jsx)(m.Aj,{className:"ml-16 fade-in-100",delay:4,number:1})]})]}),(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center w-full xl:flex-row xl:justify-between",children:[(0,s.jsx)(n.P.div,{initial:{opacity:0},whileInView:{opacity:1},transition:{duration:1,ease:"easeInOut"},viewport:{once:!0,amount:.5},children:(0,s.jsx)(r._,{align:"left",title:"Open-source",text:"We believe in the power of open source. Read through our codebase, understand our development process, and help us build something amazing all on GitHub.",children:(0,s.jsx)("div",{className:"flex mt-10 space-x-6",children:(0,s.jsx)(l(),{href:"https://go.unkey.com/github",className:"group",children:(0,s.jsx)(d.j,{IconLeft:a,label:"Star us on GitHub",shiny:!0})})})})}),(0,s.jsx)("div",{className:"relative",children:(0,s.jsxs)(n.P.div,{initial:{opacity:0},whileInView:{opacity:1},viewport:{once:!0,amount:.5},transition:{duration:1,ease:"easeInOut"},children:[(0,s.jsx)(u,{alt:"Github logo",src:h,className:"mt-24"}),(0,s.jsx)("div",{className:"absolute -z-50 top-[150px] left-[-50px] lg:w-[1000px] lg:h-[400px] lg:top-[400px] lg:left-[150px]",children:(0,s.jsx)(p,{className:"flex"})})]})})]})]})},41692:e=>{"use strict";e.exports=require("node:tls")},42152:e=>{"use strict";e.exports=require("process")},42849:(e,t,i)=>{"use strict";i.d(t,{j:()=>n,t:()=>a});var s=i(99730),r=i(26647);let n=({className:e,IconLeft:t,label:i,IconRight:n,shiny:a=!1})=>(0,s.jsxs)("div",{className:"relative group/button",children:[(0,s.jsx)("div",{"aria-hidden":!0,className:"absolute -inset-0.5 bg-white rounded-lg blur-2xl group-hover/button:opacity-30 transition duration-300  opacity-0 "}),(0,s.jsxs)("div",{className:(0,r.cn)("relative flex items-center px-4 gap-2 text-sm font-semibold text-black group-hover:bg-white/90 duration-1000 rounded-lg h-10",{"bg-white":!a,"bg-gradient-to-r from-white/80 to-white":a},e),children:[t?(0,s.jsx)(t,{className:"w-4 h-4"}):null,i,n?(0,s.jsx)(n,{className:"w-4 h-4"}):null,a&&(0,s.jsx)("div",{"aria-hidden":!0,className:"pointer-events-none absolute inset-0 opacity-0 group-hover/button:[animation-delay:.2s] group-hover/button:animate-button-shine rounded-[inherit] bg-[length:200%_100%] bg-[linear-gradient(110deg,transparent,35%,rgba(255,255,255,.7),75%,transparent)]"})]})]}),a=({className:e,IconLeft:t,label:i,IconRight:n})=>(0,s.jsxs)("div",{className:(0,r.cn)("items-center gap-2 px-4 duration-500 text-white/70 hover:text-white h-10 flex",e),children:[t?(0,s.jsx)(t,{className:"w-4 h-4"}):null,i,n?(0,s.jsx)(n,{className:"w-4 h-4"}):null]})},44669:(e,t,i)=>{"use strict";i.d(t,{A:()=>s});let s=(0,i(19161).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},44708:e=>{"use strict";e.exports=require("node:https")},46069:(e,t,i)=>{"use strict";i.d(t,{M:()=>r});var s=i(35371);function r(e){let t=(0,s.useRef)(null);return null===t.current&&(t.current=e()),t.current}},48161:e=>{"use strict";e.exports=require("node:os")},48190:(e,t,i)=>{"use strict";i.d(t,{N:()=>y});var s=i(99730),r=i(35371),n=i(68797),a=i(46069),o=i(81300),l=i(62431),h=i(18836),d=i(15110);class c extends r.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=t.offsetParent,i=(0,h.s)(e)&&e.offsetWidth||0,s=this.props.sizeRef.current;s.height=t.offsetHeight||0,s.width=t.offsetWidth||0,s.top=t.offsetTop,s.left=t.offsetLeft,s.right=i-s.width-s.left}return null}componentDidUpdate(){}render(){return this.props.children}}function u({children:e,isPresent:t,anchorX:i}){let n=(0,r.useId)(),a=(0,r.useRef)(null),o=(0,r.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:l}=(0,r.useContext)(d.Q);return(0,r.useInsertionEffect)(()=>{let{width:e,height:s,top:r,left:h,right:d}=o.current;if(t||!a.current||!e||!s)return;let c="left"===i?`left: ${h}`:`right: ${d}`;a.current.dataset.motionPopId=n;let u=document.createElement("style");return l&&(u.nonce=l),document.head.appendChild(u),u.sheet&&u.sheet.insertRule(`
          [data-motion-pop-id="${n}"] {
            position: absolute !important;
            width: ${e}px !important;
            height: ${s}px !important;
            ${c}px !important;
            top: ${r}px !important;
          }
        `),()=>{document.head.contains(u)&&document.head.removeChild(u)}},[t]),(0,s.jsx)(c,{isPresent:t,childRef:a,sizeRef:o,children:r.cloneElement(e,{ref:a})})}let p=({children:e,initial:t,isPresent:i,onExitComplete:n,custom:o,presenceAffectsLayout:h,mode:d,anchorX:c})=>{let p=(0,a.M)(f),m=(0,r.useId)(),x=!0,g=(0,r.useMemo)(()=>(x=!1,{id:m,initial:t,isPresent:i,custom:o,onExitComplete:e=>{for(let t of(p.set(e,!0),p.values()))if(!t)return;n&&n()},register:e=>(p.set(e,!1),()=>p.delete(e))}),[i,p,n]);return h&&x&&(g={...g}),(0,r.useMemo)(()=>{p.forEach((e,t)=>p.set(t,!1))},[i]),r.useEffect(()=>{i||p.size||!n||n()},[i]),"popLayout"===d&&(e=(0,s.jsx)(u,{isPresent:i,anchorX:c,children:e})),(0,s.jsx)(l.t.Provider,{value:g,children:e})};function f(){return new Map}var m=i(27356);let x=e=>e.key||"";function g(e){let t=[];return r.Children.forEach(e,e=>{(0,r.isValidElement)(e)&&t.push(e)}),t}let y=({children:e,custom:t,initial:i=!0,onExitComplete:l,presenceAffectsLayout:h=!0,mode:d="sync",propagate:c=!1,anchorX:u="left"})=>{let[f,y]=(0,m.xQ)(c),v=(0,r.useMemo)(()=>g(e),[e]),w=c&&!f?[]:v.map(x),j=(0,r.useRef)(!0),b=(0,r.useRef)(v),_=(0,a.M)(()=>new Map),[k,C]=(0,r.useState)(v),[S,M]=(0,r.useState)(v);(0,o.E)(()=>{j.current=!1,b.current=v;for(let e=0;e<S.length;e++){let t=x(S[e]);w.includes(t)?_.delete(t):!0!==_.get(t)&&_.set(t,!1)}},[S,w.length,w.join("-")]);let O=[];if(v!==k){let e=[...v];for(let t=0;t<S.length;t++){let i=S[t],s=x(i);w.includes(s)||(e.splice(t,0,i),O.push(i))}return"wait"===d&&O.length&&(e=O),M(g(e)),C(v),null}let{forceRender:V}=(0,r.useContext)(n.L);return(0,s.jsx)(s.Fragment,{children:S.map(e=>{let r=x(e),n=(!c||!!f)&&(v===S||w.includes(r));return(0,s.jsx)(p,{isPresent:n,initial:(!j.current||!!i)&&void 0,custom:t,presenceAffectsLayout:h,mode:d,onExitComplete:n?void 0:()=>{if(!_.has(r))return;_.set(r,!0);let e=!0;_.forEach(t=>{t||(e=!1)}),e&&(V?.(),M(b.current),c&&y?.(),l&&l())},anchorX:u,children:e},r)})})}},50778:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>s});let s={src:"/_next/static/media/ip.5d4ed2b5.svg",height:840,width:840,blurWidth:0,blurHeight:0}},52912:(e,t,i)=>{"use strict";i.d(t,{Hero:()=>s});let s=(0,i(6340).registerClientReference)(function(){throw Error("Attempted to call Hero() from the server but Hero is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\web\\components\\hero\\hero.tsx","Hero")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},53361:(e,t,i)=>{"use strict";i.d(t,{UsageBento:()=>d});var s={};i.r(s);var r=i(99730),n=i(35371),a=i(48190),o=i(70022),l=i(26647);let h=({items:e,className:t,itemClassName:i,delay:s=.1,duration:h=.5})=>{let[d,c]=(0,n.useState)([]);return(0,n.useEffect)(()=>(e.forEach((e,t)=>{setTimeout(()=>{c(t=>[...t,e])},t*s*1e3)}),()=>c([])),[e,s]),(0,r.jsx)("div",{className:(0,l.cn)("space-y-2",t),children:(0,r.jsx)(a.N,{children:d.map((e,t)=>(0,r.jsx)(o.P.div,{initial:{opacity:0,y:20,scale:.95},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:-20,scale:.95},transition:{duration:h,delay:t*s,ease:"easeOut"},className:(0,l.cn)("w-full",i),children:e.content},e.id))})})};function d(){return(0,r.jsxs)("div",{className:"w-full overflow-hidden relative border-[.75px] h-[576px] rounded-[32px] usage-bento-bg-gradient border-[#ffffff]/10",children:[(0,r.jsx)(s.UsageSparkles,{className:"absolute top-0"}),(0,r.jsx)("div",{className:"relative ",children:(0,r.jsxs)(h,{className:"w-full",children:[(0,r.jsx)(x,{icon:(0,r.jsx)(c,{}),text:"Unkey created API key",latency:"3 s"}),(0,r.jsx)(x,{icon:(0,r.jsx)(p,{}),text:"User verified key and logged usage",latency:"1 s"}),(0,r.jsx)(x,{icon:(0,r.jsx)(u,{}),text:"Andreas enabled automatic billing",latency:"8 ms"}),(0,r.jsx)(x,{icon:(0,r.jsx)(f,{}),text:"Unkey sent invoice to customer",latency:"1 s"}),(0,r.jsx)(x,{icon:(0,r.jsx)(m,{}),text:"Andreas collected payments",latency:"2 s"})]})}),(0,r.jsx)(g,{})]})}let c=()=>(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",children:[(0,r.jsx)("path",{d:"M2.5 4.5H13.5M13.5 4.5L10.5 1.5M13.5 4.5L10.5 7.5",stroke:"white"}),(0,r.jsx)("path",{d:"M13.5 11.5H2.5M2.5 11.5L5.5 8.5M2.5 11.5L5.5 14.5",stroke:"white"})]}),u=()=>(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:[(0,r.jsx)("path",{d:"M5 8.5H9",stroke:"white",strokeLinejoin:"round"}),(0,r.jsx)("path",{d:"M5 15.5H13",stroke:"white",strokeLinejoin:"round"}),(0,r.jsx)("path",{d:"M12 8.5H19",stroke:"white",strokeLinejoin:"round"}),(0,r.jsx)("path",{d:"M16 15.5H19",stroke:"white",strokeLinejoin:"round"}),(0,r.jsx)("path",{d:"M10.5 10.5C11.6046 10.5 12.5 9.60457 12.5 8.5C12.5 7.39543 11.6046 6.5 10.5 6.5C9.39543 6.5 8.5 7.39543 8.5 8.5C8.5 9.60457 9.39543 10.5 10.5 10.5Z",stroke:"white",strokeLinejoin:"round"}),(0,r.jsx)("path",{d:"M14.5 17.5C15.6046 17.5 16.5 16.6046 16.5 15.5C16.5 14.3954 15.6046 13.5 14.5 13.5C13.3954 13.5 12.5 14.3954 12.5 15.5C12.5 16.6046 13.3954 17.5 14.5 17.5Z",stroke:"white",strokeLinejoin:"round"})]}),p=()=>(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:[(0,r.jsx)("path",{d:"M9 12L11.5 14L15.5 9.5",stroke:"white"}),(0,r.jsx)("path",{d:"M5.10608 7.24025L11.1227 4.66171C11.3716 4.55502 11.6397 4.5 11.9105 4.5H12.0895C12.3603 4.5 12.6284 4.55502 12.8773 4.66171L18.8939 7.24025C19.2616 7.39783 19.5 7.75937 19.5 8.1594V8.5L19.0821 11.6346C18.7148 14.3888 17.096 16.819 14.6958 18.2191L12.9319 19.2481C12.649 19.4131 12.3275 19.5 12 19.5C11.6725 19.5 11.351 19.4131 11.0681 19.2481L9.30415 18.2191C6.90403 16.819 5.28517 14.3888 4.91794 11.6346L4.5 8.5V8.1594C4.5 7.75937 4.7384 7.39783 5.10608 7.24025Z",stroke:"white"})]}),f=()=>(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:[(0,r.jsx)("path",{d:"M14.5 9.5H11.5M9.5 15.5H12.5M12.5 15.5H13C13.8284 15.5 14.5 14.8284 14.5 14V14C14.5 13.1716 13.8284 12.5 13 12.5H11C10.1716 12.5 9.5 11.8284 9.5 11V11C9.5 10.1716 10.1716 9.5 11 9.5H11.5M12.5 15.5V17M11.5 9.5V8",stroke:"white"}),(0,r.jsx)("path",{d:"M5.5 18V6C5.5 5.17157 6.17157 4.5 7 4.5H13.8787C14.2765 4.5 14.658 4.65803 14.9393 4.93934L18.0607 8.06066C18.342 8.34196 18.5 8.7235 18.5 9.12132V18C18.5 18.8284 17.8284 19.5 17 19.5H7C6.17157 19.5 5.5 18.8284 5.5 18Z",stroke:"white"})]}),m=()=>(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",children:[(0,r.jsx)("path",{d:"M0.5 12V4C0.5 3.17157 1.17157 2.5 2 2.5H14C14.8284 2.5 15.5 3.17157 15.5 4V12C15.5 12.8284 14.8284 13.5 14 13.5H2C1.17157 13.5 0.5 12.8284 0.5 12Z",stroke:"white"}),(0,r.jsx)("circle",{cx:"8",cy:"8",r:"2.25",fill:"white",fillOpacity:"0.99"}),(0,r.jsx)("path",{d:"M1 3H3.5C3.5 4.38071 2.38071 5.5 1 5.5V3Z",fill:"white"}),(0,r.jsx)("path",{d:"M1 10.5C2.38071 10.5 3.5 11.6193 3.5 13H1V10.5Z",fill:"white"}),(0,r.jsx)("path",{d:"M12.5 3H15V5.5C13.6193 5.5 12.5 4.38071 12.5 3Z",fill:"white"}),(0,r.jsx)("path",{d:"M12.5 13C12.5 11.6193 13.6193 10.5 15 10.5V13H12.5Z",fill:"white"})]});function x({className:e,icon:t,text:i,latency:s}){let[n,...a]=i.split(" ");return a=a.join(" "),(0,r.jsxs)("div",{className:`flex relative -top-7 left-14 md:left-0 rounded-xl border-[.75px] w-[440px] usage-item-gradient border-white/20 mt-4 md:ml-5 lg:ml-0 flex items-center py-[12px] px-[16px] ${e}`,children:[(0,r.jsx)("div",{className:"rounded-full bg-gray-500 flex items-center justify-center h-8 w-8 border-.75px border-white/20 bg-white/10",children:t}),(0,r.jsxs)("p",{className:"flex items-center ml-6 text-sm text-white",children:[n,(0,r.jsx)("span",{className:"ml-2 text-white/40",children:a}),(0,r.jsx)("svg",{className:"inline-flex ml-2",xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:(0,r.jsx)("path",{d:"M5 13L8 15.5L13.5 8.5M11.5 14L13.5 15.5L19.5 8.5",stroke:"#3CEEAE"})})]}),(0,r.jsxs)("div",{className:"flex items-center h-full ml-auto",children:[(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",children:[(0,r.jsx)("circle",{cx:"8",cy:"8",r:"5.5",stroke:"white",strokeOpacity:"0.25"}),(0,r.jsx)("path",{d:"M8.5 5V8L10.5 9.5",stroke:"white",strokeOpacity:"0.25"})]}),(0,r.jsx)("p",{className:"ml-2 text-sm text-white/20",children:s})]})]})}function g(){return(0,r.jsxs)("div",{className:"flex flex-col text-white absolute left-[20px] sm:left-[40px] xl:left-[40px] bottom-[40px] max-w-[3300px]",children:[(0,r.jsxs)("div",{className:"flex items-center w-full",children:[(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:(0,r.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M15.8536 1.85359L14.7047 3.00245C19.3045 3.11116 23 6.87404 23 11.5C23 16.1945 19.1944 20 14.5 20H14V19H14.5C18.6421 19 22 15.6422 22 11.5C22 7.42813 18.755 4.11412 14.71 4.00292L15.8536 5.14648L15.1464 5.85359L13.1464 3.85359L12.7929 3.50004L13.1464 3.14648L15.1464 1.14648L15.8536 1.85359ZM9.5 4.00004C5.35786 4.00004 2 7.3579 2 11.5C2 15.5719 5.24497 18.886 9.29001 18.9972L8.14645 17.8536L8.85355 17.1465L10.8536 19.1465L11.2071 19.5L10.8536 19.8536L8.85355 21.8536L8.14645 21.1465L9.29531 19.9976C4.69545 19.8889 1 16.126 1 11.5C1 6.80562 4.80558 3.00004 9.5 3.00004H10V4.00004H9.5ZM12 8.00004V7.00004H11V8.00004C9.89543 8.00004 9 8.89547 9 10C9 11.1046 9.89543 12 11 12H13C13.5523 12 14 12.4478 14 13C14 13.5523 13.5523 14 13 14H12.5H9.5V15H12V16H13V15C14.1046 15 15 14.1046 15 13C15 11.8955 14.1046 11 13 11H11C10.4477 11 10 10.5523 10 10C10 9.44775 10.4477 9.00004 11 9.00004H11.5H14.5V8.00004H12Z",fill:"white",fillOpacity:"0.4"})}),(0,r.jsx)("h3",{className:"relative z-50 ml-4 text-lg font-medium text-white bg-transparent",children:"Monetize your API"})]}),(0,r.jsx)("p",{className:"mt-4 text-white/60 leading-6 max-w-[350px]",children:"Unkey tracks all user actions in your API, making it straightforward to bill users based on their usage."})]})}},55511:e=>{"use strict";e.exports=require("crypto")},56450:(e,t,i)=>{"use strict";i.d(t,{w:()=>l});var s=i(81121),r=i.n(s);let n="next-forge",a={name:"Vercel",url:"https://vercel.com/"},o=process.env.VERCEL_PROJECT_PRODUCTION_URL,l=({title:e,description:t,image:i,...s})=>{let l=`${e} | ${n}`,h={title:l,description:t,applicationName:n,metadataBase:o?new URL(`https://${o}`):void 0,authors:[a],creator:a.name,formatDetection:{telephone:!1},appleWebApp:{capable:!0,statusBarStyle:"default",title:l},openGraph:{title:l,description:t,type:"website",siteName:n,locale:"en_US"},publisher:"Vercel",twitter:{card:"summary_large_image",creator:"@vercel"}},d=r()(h,s);return i&&d.openGraph&&(d.openGraph.images=[{url:i,width:1200,height:630,alt:e}]),d}},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},56827:(e,t,i)=>{"use strict";i.d(t,{Aj:()=>l});var s=i(99730),r=i(35371),n=i(70022),a=i(26647);let o=({className:e,delay:t=0,duration:i=2})=>(0,s.jsx)(n.P.div,{className:(0,a.cn)("absolute h-0.5 w-0.5 rounded-full bg-slate-500 shadow-[0_0_0_1px_#ffffff10]",e),initial:{opacity:0,x:-100,y:-100},animate:{opacity:[0,1,0],x:[0,300],y:[0,300]},transition:{duration:i,delay:t,repeat:1/0,repeatDelay:5*Math.random()+2,ease:"linear"},style:{background:"linear-gradient(90deg, #64748b, transparent)"}}),l=({number:e=20,className:t})=>{let[i,n]=(0,r.useState)([]);return(0,r.useEffect)(()=>{n(Array.from({length:e},(e,t)=>({id:t,delay:5*Math.random(),duration:2*Math.random()+1})))},[e]),(0,s.jsx)("div",{className:(0,a.cn)("absolute inset-0 overflow-hidden",t),children:i.map(e=>(0,s.jsx)(o,{delay:e.delay,duration:e.duration,className:"meteor-line"},e.id))})}},56829:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{VALID_LOADERS:function(){return i},imageConfigDefault:function(){return s}});let i=["default","imgix","cloudinary","akamai","custom"],s={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},58665:(e,t,i)=>{"use strict";i.d(t,{OpenSource:()=>s});let s=(0,i(6340).registerClientReference)(function(){throw Error("Attempted to call OpenSource() from the server but OpenSource is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\web\\components\\open-source.tsx","OpenSource")},60628:(e,t,i)=>{"use strict";i.d(t,{A:()=>s});let s=(0,i(19161).A)("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},62431:(e,t,i)=>{"use strict";i.d(t,{t:()=>s});let s=(0,i(35371).createContext)(null)},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64426:(e,t,i)=>{"use strict";i.r(t),i.d(t,{GlobalError:()=>a.a,__next_app__:()=>c,pages:()=>d,routeModule:()=>u,tree:()=>h});var s=i(57864),r=i(94327),n=i(73391),a=i.n(n),o=i(17984),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);i.d(t,l);let h={children:["",{children:["[locale]",{children:["(home)",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(i.bind(i,29317)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\web\\app\\[locale]\\(home)\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(i.bind(i,15190))).default(e)],apple:[async e=>(await Promise.resolve().then(i.bind(i,7820))).default(e)],openGraph:[async e=>(await Promise.resolve().then(i.bind(i,39440))).default(e)],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(i.bind(i,37919)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\web\\app\\[locale]\\layout.tsx"],"global-error":[()=>Promise.resolve().then(i.bind(i,84641)),"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\web\\app\\[locale]\\global-error.tsx"]}]},{"not-found":[()=>Promise.resolve().then(i.t.bind(i,47837,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(i.t.bind(i,47168,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(i.t.bind(i,52945,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\web\\app\\[locale]\\(home)\\page.tsx"],c={require:i,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/[locale]/(home)/page",pathname:"/[locale]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:h}})},68797:(e,t,i)=>{"use strict";i.d(t,{L:()=>s});let s=(0,i(35371).createContext)({})},70022:(e,t,i)=>{"use strict";let s;function r(e){return null!==e&&"object"==typeof e&&"function"==typeof e.start}function n(e){let t=[{},{}];return e?.values.forEach((e,i)=>{t[0][i]=e.get(),t[1][i]=e.getVelocity()}),t}function a(e,t,i,s){if("function"==typeof t){let[r,a]=n(s);t=t(void 0!==i?i:e.custom,r,a)}if("string"==typeof t&&(t=e.variants&&e.variants[t]),"function"==typeof t){let[r,a]=n(s);t=t(void 0!==i?i:e.custom,r,a)}return t}function o(e,t,i){let s=e.getProps();return a(s,t,void 0!==i?i:s.custom,e)}function l(e,t){return e?.[t]??e?.default??e}i.d(t,{P:()=>nM});let h=e=>e,d={},c=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],u={value:null,addProjectionMetrics:null};function p(e,t){let i=!1,s=!0,r={delta:0,timestamp:0,isProcessing:!1},n=()=>i=!0,a=c.reduce((e,i)=>(e[i]=function(e,t){let i=new Set,s=new Set,r=!1,n=!1,a=new WeakSet,o={delta:0,timestamp:0,isProcessing:!1},l=0;function h(t){a.has(t)&&(d.schedule(t),e()),l++,t(o)}let d={schedule:(e,t=!1,n=!1)=>{let o=n&&r?i:s;return t&&a.add(e),o.has(e)||o.add(e),e},cancel:e=>{s.delete(e),a.delete(e)},process:e=>{if(o=e,r){n=!0;return}r=!0,[i,s]=[s,i],i.forEach(h),t&&u.value&&u.value.frameloop[t].push(l),l=0,i.clear(),r=!1,n&&(n=!1,d.process(e))}};return d}(n,t?i:void 0),e),{}),{setup:o,read:l,resolveKeyframes:h,preUpdate:p,update:f,preRender:m,render:x,postRender:g}=a,y=()=>{let n=d.useManualTiming?r.timestamp:performance.now();i=!1,d.useManualTiming||(r.delta=s?1e3/60:Math.max(Math.min(n-r.timestamp,40),1)),r.timestamp=n,r.isProcessing=!0,o.process(r),l.process(r),h.process(r),p.process(r),f.process(r),m.process(r),x.process(r),g.process(r),r.isProcessing=!1,i&&t&&(s=!1,e(y))},v=()=>{i=!0,s=!0,r.isProcessing||e(y)};return{schedule:c.reduce((e,t)=>{let s=a[t];return e[t]=(e,t=!1,r=!1)=>(i||v(),s.schedule(e,t,r)),e},{}),cancel:e=>{for(let t=0;t<c.length;t++)a[c[t]].cancel(e)},state:r,steps:a}}let{schedule:f,cancel:m,state:x,steps:g}=p("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:h,!0),y=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],v=new Set(y),w=new Set(["width","height","top","left","right","bottom",...y]);function j(e,t){-1===e.indexOf(t)&&e.push(t)}function b(e,t){let i=e.indexOf(t);i>-1&&e.splice(i,1)}class _{constructor(){this.subscriptions=[]}add(e){return j(this.subscriptions,e),()=>b(this.subscriptions,e)}notify(e,t,i){let s=this.subscriptions.length;if(s)if(1===s)this.subscriptions[0](e,t,i);else for(let r=0;r<s;r++){let s=this.subscriptions[r];s&&s(e,t,i)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function k(){s=void 0}let C={now:()=>(void 0===s&&C.set(x.isProcessing||d.useManualTiming?x.timestamp:performance.now()),s),set:e=>{s=e,queueMicrotask(k)}},S=e=>!isNaN(parseFloat(e)),M={current:void 0};class O{constructor(e,t={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(e,t=!0)=>{let i=C.now();if(this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(e),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let e of this.dependents)e.dirty();t&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(e),this.owner=t.owner}setCurrent(e){this.current=e,this.updatedAt=C.now(),null===this.canTrackVelocity&&void 0!==e&&(this.canTrackVelocity=S(this.current))}setPrevFrameValue(e=this.current){this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt}onChange(e){return this.on("change",e)}on(e,t){this.events[e]||(this.events[e]=new _);let i=this.events[e].add(t);return"change"===e?()=>{i(),f.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let e in this.events)this.events[e].clear()}attach(e,t){this.passiveEffect=e,this.stopPassiveEffect=t}set(e,t=!0){t&&this.passiveEffect?this.passiveEffect(e,this.updateAndNotify):this.updateAndNotify(e,t)}setWithVelocity(e,t,i){this.set(t),this.prev=void 0,this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt-i}jump(e,t=!0){this.updateAndNotify(e),this.prev=e,this.prevUpdatedAt=this.prevFrameValue=void 0,t&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(e){this.dependents||(this.dependents=new Set),this.dependents.add(e)}removeDependent(e){this.dependents&&this.dependents.delete(e)}get(){return M.current&&M.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var e;let t=C.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;let i=Math.min(this.updatedAt-this.prevUpdatedAt,30);return e=parseFloat(this.current)-parseFloat(this.prevFrameValue),i?1e3/i*e:0}start(e){return this.stop(),new Promise(t=>{this.hasAnimated=!0,this.animation=e(t),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function V(e,t){return new O(e,t)}let A=e=>Array.isArray(e),B=e=>!!(e&&e.getVelocity);function P(e,t){let i=e.getValue("willChange");if(B(i)&&i.add)return i.add(t);if(!i&&d.WillChange){let i=new d.WillChange("auto");e.addValue("willChange",i),i.add(t)}}let L=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),N="data-"+L("framerAppearId"),T=(e,t)=>i=>t(e(i)),E=(...e)=>e.reduce(T),R=(e,t,i)=>i>t?t:i<e?e:i,I=e=>1e3*e,F=e=>e/1e3,U={layout:0,mainThread:0,waapi:0},D=()=>{},H=()=>{},G=e=>t=>"string"==typeof t&&t.startsWith(e),z=G("--"),q=G("var(--"),K=e=>!!q(e)&&W.test(e.split("/*")[0].trim()),W=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,Z={test:e=>"number"==typeof e,parse:parseFloat,transform:e=>e},$={...Z,transform:e=>R(0,1,e)},Y={...Z,default:1},X=e=>Math.round(1e5*e)/1e5,J=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,Q=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,ee=(e,t)=>i=>!!("string"==typeof i&&Q.test(i)&&i.startsWith(e)||t&&null!=i&&Object.prototype.hasOwnProperty.call(i,t)),et=(e,t,i)=>s=>{if("string"!=typeof s)return s;let[r,n,a,o]=s.match(J);return{[e]:parseFloat(r),[t]:parseFloat(n),[i]:parseFloat(a),alpha:void 0!==o?parseFloat(o):1}},ei=e=>R(0,255,e),es={...Z,transform:e=>Math.round(ei(e))},er={test:ee("rgb","red"),parse:et("red","green","blue"),transform:({red:e,green:t,blue:i,alpha:s=1})=>"rgba("+es.transform(e)+", "+es.transform(t)+", "+es.transform(i)+", "+X($.transform(s))+")"},en={test:ee("#"),parse:function(e){let t="",i="",s="",r="";return e.length>5?(t=e.substring(1,3),i=e.substring(3,5),s=e.substring(5,7),r=e.substring(7,9)):(t=e.substring(1,2),i=e.substring(2,3),s=e.substring(3,4),r=e.substring(4,5),t+=t,i+=i,s+=s,r+=r),{red:parseInt(t,16),green:parseInt(i,16),blue:parseInt(s,16),alpha:r?parseInt(r,16)/255:1}},transform:er.transform},ea=e=>({test:t=>"string"==typeof t&&t.endsWith(e)&&1===t.split(" ").length,parse:parseFloat,transform:t=>`${t}${e}`}),eo=ea("deg"),el=ea("%"),eh=ea("px"),ed=ea("vh"),ec=ea("vw"),eu={...el,parse:e=>el.parse(e)/100,transform:e=>el.transform(100*e)},ep={test:ee("hsl","hue"),parse:et("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:i,alpha:s=1})=>"hsla("+Math.round(e)+", "+el.transform(X(t))+", "+el.transform(X(i))+", "+X($.transform(s))+")"},ef={test:e=>er.test(e)||en.test(e)||ep.test(e),parse:e=>er.test(e)?er.parse(e):ep.test(e)?ep.parse(e):en.parse(e),transform:e=>"string"==typeof e?e:e.hasOwnProperty("red")?er.transform(e):ep.transform(e),getAnimatableNone:e=>{let t=ef.parse(e);return t.alpha=0,ef.transform(t)}},em=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,ex="number",eg="color",ey=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function ev(e){let t=e.toString(),i=[],s={color:[],number:[],var:[]},r=[],n=0,a=t.replace(ey,e=>(ef.test(e)?(s.color.push(n),r.push(eg),i.push(ef.parse(e))):e.startsWith("var(")?(s.var.push(n),r.push("var"),i.push(e)):(s.number.push(n),r.push(ex),i.push(parseFloat(e))),++n,"${}")).split("${}");return{values:i,split:a,indexes:s,types:r}}function ew(e){return ev(e).values}function ej(e){let{split:t,types:i}=ev(e),s=t.length;return e=>{let r="";for(let n=0;n<s;n++)if(r+=t[n],void 0!==e[n]){let t=i[n];t===ex?r+=X(e[n]):t===eg?r+=ef.transform(e[n]):r+=e[n]}return r}}let eb=e=>"number"==typeof e?0:ef.test(e)?ef.getAnimatableNone(e):e,e_={test:function(e){return isNaN(e)&&"string"==typeof e&&(e.match(J)?.length||0)+(e.match(em)?.length||0)>0},parse:ew,createTransformer:ej,getAnimatableNone:function(e){let t=ew(e);return ej(e)(t.map(eb))}};function ek(e,t,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?e+(t-e)*6*i:i<.5?t:i<2/3?e+(t-e)*(2/3-i)*6:e}function eC(e,t){return i=>i>0?t:e}let eS=(e,t,i)=>e+(t-e)*i,eM=(e,t,i)=>{let s=e*e,r=i*(t*t-s)+s;return r<0?0:Math.sqrt(r)},eO=[en,er,ep],eV=e=>eO.find(t=>t.test(e));function eA(e){let t=eV(e);if(D(!!t,`'${e}' is not an animatable color. Use the equivalent color code instead.`),!t)return!1;let i=t.parse(e);return t===ep&&(i=function({hue:e,saturation:t,lightness:i,alpha:s}){e/=360,i/=100;let r=0,n=0,a=0;if(t/=100){let s=i<.5?i*(1+t):i+t-i*t,o=2*i-s;r=ek(o,s,e+1/3),n=ek(o,s,e),a=ek(o,s,e-1/3)}else r=n=a=i;return{red:Math.round(255*r),green:Math.round(255*n),blue:Math.round(255*a),alpha:s}}(i)),i}let eB=(e,t)=>{let i=eA(e),s=eA(t);if(!i||!s)return eC(e,t);let r={...i};return e=>(r.red=eM(i.red,s.red,e),r.green=eM(i.green,s.green,e),r.blue=eM(i.blue,s.blue,e),r.alpha=eS(i.alpha,s.alpha,e),er.transform(r))},eP=new Set(["none","hidden"]);function eL(e,t){return i=>eS(e,t,i)}function eN(e){return"number"==typeof e?eL:"string"==typeof e?K(e)?eC:ef.test(e)?eB:eR:Array.isArray(e)?eT:"object"==typeof e?ef.test(e)?eB:eE:eC}function eT(e,t){let i=[...e],s=i.length,r=e.map((e,i)=>eN(e)(e,t[i]));return e=>{for(let t=0;t<s;t++)i[t]=r[t](e);return i}}function eE(e,t){let i={...e,...t},s={};for(let r in i)void 0!==e[r]&&void 0!==t[r]&&(s[r]=eN(e[r])(e[r],t[r]));return e=>{for(let t in s)i[t]=s[t](e);return i}}let eR=(e,t)=>{let i=e_.createTransformer(t),s=ev(e),r=ev(t);return s.indexes.var.length===r.indexes.var.length&&s.indexes.color.length===r.indexes.color.length&&s.indexes.number.length>=r.indexes.number.length?eP.has(e)&&!r.values.length||eP.has(t)&&!s.values.length?function(e,t){return eP.has(e)?i=>i<=0?e:t:i=>i>=1?t:e}(e,t):E(eT(function(e,t){let i=[],s={color:0,var:0,number:0};for(let r=0;r<t.values.length;r++){let n=t.types[r],a=e.indexes[n][s[n]],o=e.values[a]??0;i[r]=o,s[n]++}return i}(s,r),r.values),i):(D(!0,`Complex values '${e}' and '${t}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),eC(e,t))};function eI(e,t,i){return"number"==typeof e&&"number"==typeof t&&"number"==typeof i?eS(e,t,i):eN(e)(e,t)}let eF=e=>{let t=({timestamp:t})=>e(t);return{start:(e=!0)=>f.update(t,e),stop:()=>m(t),now:()=>x.isProcessing?x.timestamp:C.now()}},eU=(e,t,i=10)=>{let s="",r=Math.max(Math.round(t/i),2);for(let t=0;t<r;t++)s+=Math.round(1e4*e(t/(r-1)))/1e4+", ";return`linear(${s.substring(0,s.length-2)})`};function eD(e){let t=0,i=e.next(t);for(;!i.done&&t<2e4;)t+=50,i=e.next(t);return t>=2e4?1/0:t}function eH(e,t,i){var s,r;let n=Math.max(t-5,0);return s=i-e(n),(r=t-n)?1e3/r*s:0}let eG={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function ez(e,t){return e*Math.sqrt(1-t*t)}let eq=["duration","bounce"],eK=["stiffness","damping","mass"];function eW(e,t){return t.some(t=>void 0!==e[t])}function eZ(e=eG.visualDuration,t=eG.bounce){let i,s="object"!=typeof e?{visualDuration:e,keyframes:[0,1],bounce:t}:e,{restSpeed:r,restDelta:n}=s,a=s.keyframes[0],o=s.keyframes[s.keyframes.length-1],l={done:!1,value:a},{stiffness:h,damping:d,mass:c,duration:u,velocity:p,isResolvedFromDuration:f}=function(e){let t={velocity:eG.velocity,stiffness:eG.stiffness,damping:eG.damping,mass:eG.mass,isResolvedFromDuration:!1,...e};if(!eW(e,eK)&&eW(e,eq))if(e.visualDuration){let i=2*Math.PI/(1.2*e.visualDuration),s=i*i,r=2*R(.05,1,1-(e.bounce||0))*Math.sqrt(s);t={...t,mass:eG.mass,stiffness:s,damping:r}}else{let i=function({duration:e=eG.duration,bounce:t=eG.bounce,velocity:i=eG.velocity,mass:s=eG.mass}){let r,n;D(e<=I(eG.maxDuration),"Spring duration must be 10 seconds or less");let a=1-t;a=R(eG.minDamping,eG.maxDamping,a),e=R(eG.minDuration,eG.maxDuration,F(e)),a<1?(r=t=>{let s=t*a,r=s*e;return .001-(s-i)/ez(t,a)*Math.exp(-r)},n=t=>{let s=t*a*e,n=Math.pow(a,2)*Math.pow(t,2)*e,o=Math.exp(-s),l=ez(Math.pow(t,2),a);return(s*i+i-n)*o*(-r(t)+.001>0?-1:1)/l}):(r=t=>-.001+Math.exp(-t*e)*((t-i)*e+1),n=t=>e*e*(i-t)*Math.exp(-t*e));let o=function(e,t,i){let s=i;for(let i=1;i<12;i++)s-=e(s)/t(s);return s}(r,n,5/e);if(e=I(e),isNaN(o))return{stiffness:eG.stiffness,damping:eG.damping,duration:e};{let t=Math.pow(o,2)*s;return{stiffness:t,damping:2*a*Math.sqrt(s*t),duration:e}}}(e);(t={...t,...i,mass:eG.mass}).isResolvedFromDuration=!0}return t}({...s,velocity:-F(s.velocity||0)}),m=p||0,x=d/(2*Math.sqrt(h*c)),g=o-a,y=F(Math.sqrt(h/c)),v=5>Math.abs(g);if(r||(r=v?eG.restSpeed.granular:eG.restSpeed.default),n||(n=v?eG.restDelta.granular:eG.restDelta.default),x<1){let e=ez(y,x);i=t=>o-Math.exp(-x*y*t)*((m+x*y*g)/e*Math.sin(e*t)+g*Math.cos(e*t))}else if(1===x)i=e=>o-Math.exp(-y*e)*(g+(m+y*g)*e);else{let e=y*Math.sqrt(x*x-1);i=t=>{let i=Math.exp(-x*y*t),s=Math.min(e*t,300);return o-i*((m+x*y*g)*Math.sinh(s)+e*g*Math.cosh(s))/e}}let w={calculatedDuration:f&&u||null,next:e=>{let t=i(e);if(f)l.done=e>=u;else{let s=0===e?m:0;x<1&&(s=0===e?I(m):eH(i,e,t));let a=Math.abs(o-t)<=n;l.done=Math.abs(s)<=r&&a}return l.value=l.done?o:t,l},toString:()=>{let e=Math.min(eD(w),2e4),t=eU(t=>w.next(e*t).value,e,30);return e+"ms "+t},toTransition:()=>{}};return w}function e$({keyframes:e,velocity:t=0,power:i=.8,timeConstant:s=325,bounceDamping:r=10,bounceStiffness:n=500,modifyTarget:a,min:o,max:l,restDelta:h=.5,restSpeed:d}){let c,u,p=e[0],f={done:!1,value:p},m=e=>void 0!==o&&e<o||void 0!==l&&e>l,x=e=>void 0===o?l:void 0===l||Math.abs(o-e)<Math.abs(l-e)?o:l,g=i*t,y=p+g,v=void 0===a?y:a(y);v!==y&&(g=v-p);let w=e=>-g*Math.exp(-e/s),j=e=>v+w(e),b=e=>{let t=w(e),i=j(e);f.done=Math.abs(t)<=h,f.value=f.done?v:i},_=e=>{m(f.value)&&(c=e,u=eZ({keyframes:[f.value,x(f.value)],velocity:eH(j,e,f.value),damping:r,stiffness:n,restDelta:h,restSpeed:d}))};return _(0),{calculatedDuration:null,next:e=>{let t=!1;return(u||void 0!==c||(t=!0,b(e),_(e)),void 0!==c&&e>=c)?u.next(e-c):(t||b(e),f)}}}eZ.applyToOptions=e=>{let t=function(e,t=100,i){let s=i({...e,keyframes:[0,t]}),r=Math.min(eD(s),2e4);return{type:"keyframes",ease:e=>s.next(r*e).value/t,duration:F(r)}}(e,100,eZ);return e.ease=t.ease,e.duration=I(t.duration),e.type="keyframes",e};let eY=(e,t,i)=>(((1-3*i+3*t)*e+(3*i-6*t))*e+3*t)*e;function eX(e,t,i,s){if(e===t&&i===s)return h;let r=t=>(function(e,t,i,s,r){let n,a,o=0;do(n=eY(a=t+(i-t)/2,s,r)-e)>0?i=a:t=a;while(Math.abs(n)>1e-7&&++o<12);return a})(t,0,1,e,i);return e=>0===e||1===e?e:eY(r(e),t,s)}let eJ=eX(.42,0,1,1),eQ=eX(0,0,.58,1),e0=eX(.42,0,.58,1),e1=e=>Array.isArray(e)&&"number"!=typeof e[0],e2=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,e5=e=>t=>1-e(1-t),e3=eX(.33,1.53,.69,.99),e4=e5(e3),e6=e2(e4),e7=e=>(e*=2)<1?.5*e4(e):.5*(2-Math.pow(2,-10*(e-1))),e8=e=>1-Math.sin(Math.acos(e)),e9=e5(e8),te=e2(e8),tt=e=>Array.isArray(e)&&"number"==typeof e[0],ti={linear:h,easeIn:eJ,easeInOut:e0,easeOut:eQ,circIn:e8,circInOut:te,circOut:e9,backIn:e4,backInOut:e6,backOut:e3,anticipate:e7},ts=e=>"string"==typeof e,tr=e=>{if(tt(e)){H(4===e.length,"Cubic bezier arrays must contain four numerical values.");let[t,i,s,r]=e;return eX(t,i,s,r)}return ts(e)?(H(void 0!==ti[e],`Invalid easing type '${e}'`),ti[e]):e},tn=(e,t,i)=>{let s=t-e;return 0===s?1:(i-e)/s};function ta({duration:e=300,keyframes:t,times:i,ease:s="easeInOut"}){var r;let n=e1(s)?s.map(tr):tr(s),a={done:!1,value:t[0]},o=function(e,t,{clamp:i=!0,ease:s,mixer:r}={}){let n=e.length;if(H(n===t.length,"Both input and output ranges must be the same length"),1===n)return()=>t[0];if(2===n&&t[0]===t[1])return()=>t[1];let a=e[0]===e[1];e[0]>e[n-1]&&(e=[...e].reverse(),t=[...t].reverse());let o=function(e,t,i){let s=[],r=i||d.mix||eI,n=e.length-1;for(let i=0;i<n;i++){let n=r(e[i],e[i+1]);t&&(n=E(Array.isArray(t)?t[i]||h:t,n)),s.push(n)}return s}(t,s,r),l=o.length,c=i=>{if(a&&i<e[0])return t[0];let s=0;if(l>1)for(;s<e.length-2&&!(i<e[s+1]);s++);let r=tn(e[s],e[s+1],i);return o[s](r)};return i?t=>c(R(e[0],e[n-1],t)):c}((r=i&&i.length===t.length?i:function(e){let t=[0];return!function(e,t){let i=e[e.length-1];for(let s=1;s<=t;s++){let r=tn(0,t,s);e.push(eS(i,1,r))}}(t,e.length-1),t}(t),r.map(t=>t*e)),t,{ease:Array.isArray(n)?n:t.map(()=>n||e0).splice(0,t.length-1)});return{calculatedDuration:e,next:t=>(a.value=o(t),a.done=t>=e,a)}}let to=e=>null!==e;function tl(e,{repeat:t,repeatType:i="loop"},s,r=1){let n=e.filter(to),a=r<0||t&&"loop"!==i&&t%2==1?0:n.length-1;return a&&void 0!==s?s:n[a]}let th={decay:e$,inertia:e$,tween:ta,keyframes:ta,spring:eZ};function td(e){"string"==typeof e.type&&(e.type=th[e.type])}class tc{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(e=>{this.resolve=e})}notifyFinished(){this.resolve()}then(e,t){return this.finished.then(e,t)}}let tu=e=>e/100;class tp extends tc{constructor(e){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:e}=this.options;e&&e.updatedAt!==C.now()&&this.tick(C.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},U.mainThread++,this.options=e,this.initAnimation(),this.play(),!1===e.autoplay&&this.pause()}initAnimation(){let{options:e}=this;td(e);let{type:t=ta,repeat:i=0,repeatDelay:s=0,repeatType:r,velocity:n=0}=e,{keyframes:a}=e,o=t||ta;o!==ta&&"number"!=typeof a[0]&&(this.mixKeyframes=E(tu,eI(a[0],a[1])),a=[0,100]);let l=o({...e,keyframes:a});"mirror"===r&&(this.mirroredGenerator=o({...e,keyframes:[...a].reverse(),velocity:-n})),null===l.calculatedDuration&&(l.calculatedDuration=eD(l));let{calculatedDuration:h}=l;this.calculatedDuration=h,this.resolvedDuration=h+s,this.totalDuration=this.resolvedDuration*(i+1)-s,this.generator=l}updateTime(e){let t=Math.round(e-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=t}tick(e,t=!1){let{generator:i,totalDuration:s,mixKeyframes:r,mirroredGenerator:n,resolvedDuration:a,calculatedDuration:o}=this;if(null===this.startTime)return i.next(0);let{delay:l=0,keyframes:h,repeat:d,repeatType:c,repeatDelay:u,type:p,onUpdate:f,finalKeyframe:m}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,e):this.speed<0&&(this.startTime=Math.min(e-s/this.speed,this.startTime)),t?this.currentTime=e:this.updateTime(e);let x=this.currentTime-l*(this.playbackSpeed>=0?1:-1),g=this.playbackSpeed>=0?x<0:x>s;this.currentTime=Math.max(x,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=s);let y=this.currentTime,v=i;if(d){let e=Math.min(this.currentTime,s)/a,t=Math.floor(e),i=e%1;!i&&e>=1&&(i=1),1===i&&t--,(t=Math.min(t,d+1))%2&&("reverse"===c?(i=1-i,u&&(i-=u/a)):"mirror"===c&&(v=n)),y=R(0,1,i)*a}let w=g?{done:!1,value:h[0]}:v.next(y);r&&(w.value=r(w.value));let{done:j}=w;g||null===o||(j=this.playbackSpeed>=0?this.currentTime>=s:this.currentTime<=0);let b=null===this.holdTime&&("finished"===this.state||"running"===this.state&&j);return b&&p!==e$&&(w.value=tl(h,this.options,m,this.speed)),f&&f(w.value),b&&this.finish(),w}then(e,t){return this.finished.then(e,t)}get duration(){return F(this.calculatedDuration)}get time(){return F(this.currentTime)}set time(e){e=I(e),this.currentTime=e,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=e:this.driver&&(this.startTime=this.driver.now()-e/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(e){this.updateTime(C.now());let t=this.playbackSpeed!==e;this.playbackSpeed=e,t&&(this.time=F(this.currentTime))}play(){if(this.isStopped)return;let{driver:e=eF,startTime:t}=this.options;this.driver||(this.driver=e(e=>this.tick(e))),this.options.onPlay?.();let i=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=i):null!==this.holdTime?this.startTime=i-this.holdTime:this.startTime||(this.startTime=t??i),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(C.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,U.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(e){return this.startTime=0,this.tick(e,!0)}attachTimeline(e){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),e.observe(this)}}let tf=e=>180*e/Math.PI,tm=e=>tg(tf(Math.atan2(e[1],e[0]))),tx={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:e=>(Math.abs(e[0])+Math.abs(e[3]))/2,rotate:tm,rotateZ:tm,skewX:e=>tf(Math.atan(e[1])),skewY:e=>tf(Math.atan(e[2])),skew:e=>(Math.abs(e[1])+Math.abs(e[2]))/2},tg=e=>((e%=360)<0&&(e+=360),e),ty=e=>Math.sqrt(e[0]*e[0]+e[1]*e[1]),tv=e=>Math.sqrt(e[4]*e[4]+e[5]*e[5]),tw={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:ty,scaleY:tv,scale:e=>(ty(e)+tv(e))/2,rotateX:e=>tg(tf(Math.atan2(e[6],e[5]))),rotateY:e=>tg(tf(Math.atan2(-e[2],e[0]))),rotateZ:tm,rotate:tm,skewX:e=>tf(Math.atan(e[4])),skewY:e=>tf(Math.atan(e[1])),skew:e=>(Math.abs(e[1])+Math.abs(e[4]))/2};function tj(e){return+!!e.includes("scale")}function tb(e,t){let i,s;if(!e||"none"===e)return tj(t);let r=e.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(r)i=tw,s=r;else{let t=e.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=tx,s=t}if(!s)return tj(t);let n=i[t],a=s[1].split(",").map(tk);return"function"==typeof n?n(a):a[n]}let t_=(e,t)=>{let{transform:i="none"}=getComputedStyle(e);return tb(i,t)};function tk(e){return parseFloat(e.trim())}let tC=e=>e===Z||e===eh,tS=new Set(["x","y","z"]),tM=y.filter(e=>!tS.has(e)),tO={width:({x:e},{paddingLeft:t="0",paddingRight:i="0"})=>e.max-e.min-parseFloat(t)-parseFloat(i),height:({y:e},{paddingTop:t="0",paddingBottom:i="0"})=>e.max-e.min-parseFloat(t)-parseFloat(i),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:(e,{transform:t})=>tb(t,"x"),y:(e,{transform:t})=>tb(t,"y")};tO.translateX=tO.x,tO.translateY=tO.y;let tV=new Set,tA=!1,tB=!1,tP=!1;function tL(){if(tB){let e=Array.from(tV).filter(e=>e.needsMeasurement),t=new Set(e.map(e=>e.element)),i=new Map;t.forEach(e=>{let t=function(e){let t=[];return tM.forEach(i=>{let s=e.getValue(i);void 0!==s&&(t.push([i,s.get()]),s.set(+!!i.startsWith("scale")))}),t}(e);t.length&&(i.set(e,t),e.render())}),e.forEach(e=>e.measureInitialState()),t.forEach(e=>{e.render();let t=i.get(e);t&&t.forEach(([t,i])=>{e.getValue(t)?.set(i)})}),e.forEach(e=>e.measureEndState()),e.forEach(e=>{void 0!==e.suspendedScrollY&&window.scrollTo(0,e.suspendedScrollY)})}tB=!1,tA=!1,tV.forEach(e=>e.complete(tP)),tV.clear()}function tN(){tV.forEach(e=>{e.readKeyframes(),e.needsMeasurement&&(tB=!0)})}class tT{constructor(e,t,i,s,r,n=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...e],this.onComplete=t,this.name=i,this.motionValue=s,this.element=r,this.isAsync=n}scheduleResolve(){this.state="scheduled",this.isAsync?(tV.add(this),tA||(tA=!0,f.read(tN),f.resolveKeyframes(tL))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:e,name:t,element:i,motionValue:s}=this;if(null===e[0]){let r=s?.get(),n=e[e.length-1];if(void 0!==r)e[0]=r;else if(i&&t){let s=i.readValue(t,n);null!=s&&(e[0]=s)}void 0===e[0]&&(e[0]=n),s&&void 0===r&&s.set(e[0])}for(let t=1;t<e.length;t++)e[t]??(e[t]=e[t-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(e=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,e),tV.delete(this)}cancel(){"scheduled"===this.state&&(tV.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}let tE=e=>e.startsWith("--");function tR(e){let t;return()=>(void 0===t&&(t=e()),t)}let tI=tR(()=>void 0!==window.ScrollTimeline),tF={},tU=function(e,t){let i=tR(e);return()=>tF[t]??i()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(e){return!1}return!0},"linearEasing"),tD=([e,t,i,s])=>`cubic-bezier(${e}, ${t}, ${i}, ${s})`,tH={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:tD([0,.65,.55,1]),circOut:tD([.55,0,1,.45]),backIn:tD([.31,.01,.66,-.59]),backOut:tD([.33,1.53,.69,.99])};function tG(e){return"function"==typeof e&&"applyToOptions"in e}class tz extends tc{constructor(e){if(super(),this.finishedTime=null,this.isStopped=!1,!e)return;let{element:t,name:i,keyframes:s,pseudoElement:r,allowFlatten:n=!1,finalKeyframe:a,onComplete:o}=e;this.isPseudoElement=!!r,this.allowFlatten=n,this.options=e,H("string"!=typeof e.type,'animateMini doesn\'t support "type" as a string. Did you mean to import { spring } from "motion"?');let l=function({type:e,...t}){return tG(e)&&tU()?e.applyToOptions(t):(t.duration??(t.duration=300),t.ease??(t.ease="easeOut"),t)}(e);this.animation=function(e,t,i,{delay:s=0,duration:r=300,repeat:n=0,repeatType:a="loop",ease:o="easeOut",times:l}={},h){let d={[t]:i};l&&(d.offset=l);let c=function e(t,i){if(t)return"function"==typeof t?tU()?eU(t,i):"ease-out":tt(t)?tD(t):Array.isArray(t)?t.map(t=>e(t,i)||tH.easeOut):tH[t]}(o,r);Array.isArray(c)&&(d.easing=c),u.value&&U.waapi++;let p={delay:s,duration:r,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:n+1,direction:"reverse"===a?"alternate":"normal"};h&&(p.pseudoElement=h);let f=e.animate(d,p);return u.value&&f.finished.finally(()=>{U.waapi--}),f}(t,i,s,l,r),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!r){let e=tl(s,this.options,a,this.speed);this.updateMotionValue?this.updateMotionValue(e):function(e,t,i){tE(t)?e.style.setProperty(t,i):e.style[t]=i}(t,i,e),this.animation.cancel()}o?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(e){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:e}=this;"idle"!==e&&"finished"!==e&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){return F(Number(this.animation.effect?.getComputedTiming?.().duration||0))}get time(){return F(Number(this.animation.currentTime)||0)}set time(e){this.finishedTime=null,this.animation.currentTime=I(e)}get speed(){return this.animation.playbackRate}set speed(e){e<0&&(this.finishedTime=null),this.animation.playbackRate=e}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(e){this.animation.startTime=e}attachTimeline({timeline:e,observe:t}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,e&&tI())?(this.animation.timeline=e,h):t(this)}}let tq={anticipate:e7,backInOut:e6,circInOut:te};class tK extends tz{constructor(e){!function(e){"string"==typeof e.ease&&e.ease in tq&&(e.ease=tq[e.ease])}(e),td(e),super(e),e.startTime&&(this.startTime=e.startTime),this.options=e}updateMotionValue(e){let{motionValue:t,onUpdate:i,onComplete:s,element:r,...n}=this.options;if(!t)return;if(void 0!==e)return void t.set(e);let a=new tp({...n,autoplay:!1}),o=I(this.finishedTime??this.time);t.setWithVelocity(a.sample(o-10).value,a.sample(o).value,10),a.stop()}}let tW=(e,t)=>"zIndex"!==t&&!!("number"==typeof e||Array.isArray(e)||"string"==typeof e&&(e_.test(e)||"0"===e)&&!e.startsWith("url("));var tZ,t$,tY=i(18836);let tX=new Set(["opacity","clipPath","filter","transform"]),tJ=tR(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class tQ extends tc{constructor({autoplay:e=!0,delay:t=0,type:i="keyframes",repeat:s=0,repeatDelay:r=0,repeatType:n="loop",keyframes:a,name:o,motionValue:l,element:h,...d}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=C.now();let c={autoplay:e,delay:t,type:i,repeat:s,repeatDelay:r,repeatType:n,name:o,motionValue:l,element:h,...d},u=h?.KeyframeResolver||tT;this.keyframeResolver=new u(a,(e,t,i)=>this.onKeyframesResolved(e,t,c,!i),o,l,h),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(e,t,i,s){this.keyframeResolver=void 0;let{name:r,type:n,velocity:a,delay:o,isHandoff:l,onUpdate:c}=i;this.resolvedAt=C.now(),!function(e,t,i,s){let r=e[0];if(null===r)return!1;if("display"===t||"visibility"===t)return!0;let n=e[e.length-1],a=tW(r,t),o=tW(n,t);return D(a===o,`You are trying to animate ${t} from "${r}" to "${n}". ${r} is not an animatable value - to enable this animation set ${r} to a value animatable to ${n} via the \`style\` property.`),!!a&&!!o&&(function(e){let t=e[0];if(1===e.length)return!0;for(let i=0;i<e.length;i++)if(e[i]!==t)return!0}(e)||("spring"===i||tG(i))&&s)}(e,r,n,a)&&((d.instantAnimations||!o)&&c?.(tl(e,i,t)),e[0]=e[e.length-1],i.duration=0,i.repeat=0);let u={startTime:s?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:t,...i,keyframes:e},p=!l&&function(e){let{motionValue:t,name:i,repeatDelay:s,repeatType:r,damping:n,type:a}=e;if(!(0,tY.s)(t?.owner?.current))return!1;let{onUpdate:o,transformTemplate:l}=t.owner.getProps();return tJ()&&i&&tX.has(i)&&("transform"!==i||!l)&&!o&&!s&&"mirror"!==r&&0!==n&&"inertia"!==a}(u)?new tK({...u,element:u.motionValue.owner.current}):new tp(u);p.finished.then(()=>this.notifyFinished()).catch(h),this.pendingTimeline&&(this.stopTimeline=p.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=p}get finished(){return this._animation?this.animation.finished:this._finished}then(e,t){return this.finished.finally(e).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),tP=!0,tN(),tL(),tP=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(e){this.animation.time=e}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(e){this.animation.speed=e}get startTime(){return this.animation.startTime}attachTimeline(e){return this._animation?this.stopTimeline=this.animation.attachTimeline(e):this.pendingTimeline=e,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let t0=e=>null!==e,t1={type:"spring",stiffness:500,damping:25,restSpeed:10},t2=e=>({type:"spring",stiffness:550,damping:0===e?2*Math.sqrt(550):30,restSpeed:10}),t5={type:"keyframes",duration:.8},t3={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},t4=(e,{keyframes:t})=>t.length>2?t5:v.has(e)?e.startsWith("scale")?t2(t[1]):t1:t3,t6=(e,t,i,s={},r,n)=>a=>{let o=l(s,e)||{},h=o.delay||s.delay||0,{elapsed:c=0}=s;c-=I(h);let u={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:t.getVelocity(),...o,delay:-c,onUpdate:e=>{t.set(e),o.onUpdate&&o.onUpdate(e)},onComplete:()=>{a(),o.onComplete&&o.onComplete()},name:e,motionValue:t,element:n?void 0:r};!function({when:e,delay:t,delayChildren:i,staggerChildren:s,staggerDirection:r,repeat:n,repeatType:a,repeatDelay:o,from:l,elapsed:h,...d}){return!!Object.keys(d).length}(o)&&Object.assign(u,t4(e,u)),u.duration&&(u.duration=I(u.duration)),u.repeatDelay&&(u.repeatDelay=I(u.repeatDelay)),void 0!==u.from&&(u.keyframes[0]=u.from);let p=!1;if(!1!==u.type&&(0!==u.duration||u.repeatDelay)||(u.duration=0,0===u.delay&&(p=!0)),(d.instantAnimations||d.skipAnimations)&&(p=!0,u.duration=0,u.delay=0),u.allowFlatten=!o.type&&!o.ease,p&&!n&&void 0!==t.get()){let e=function(e,{repeat:t,repeatType:i="loop"},s){let r=e.filter(t0),n=t&&"loop"!==i&&t%2==1?0:r.length-1;return r[n]}(u.keyframes,o);if(void 0!==e)return void f.update(()=>{u.onUpdate(e),u.onComplete()})}return o.isSync?new tp(u):new tQ(u)};function t7(e,t,{delay:i=0,transitionOverride:s,type:r}={}){let{transition:n=e.getDefaultTransition(),transitionEnd:a,...h}=t;s&&(n=s);let d=[],c=r&&e.animationState&&e.animationState.getState()[r];for(let t in h){let s=e.getValue(t,e.latestValues[t]??null),r=h[t];if(void 0===r||c&&function({protectedKeys:e,needsAnimating:t},i){let s=e.hasOwnProperty(i)&&!0!==t[i];return t[i]=!1,s}(c,t))continue;let a={delay:i,...l(n||{},t)},o=s.get();if(void 0!==o&&!s.isAnimating&&!Array.isArray(r)&&r===o&&!a.velocity)continue;let u=!1;if(window.MotionHandoffAnimation){let i=e.props[N];if(i){let e=window.MotionHandoffAnimation(i,t,f);null!==e&&(a.startTime=e,u=!0)}}P(e,t),s.start(t6(t,s,r,e.shouldReduceMotion&&w.has(t)?{type:!1}:a,e,u));let p=s.animation;p&&d.push(p)}return a&&Promise.all(d).then(()=>{f.update(()=>{a&&function(e,t){let{transitionEnd:i={},transition:s={},...r}=o(e,t)||{};for(let t in r={...r,...i}){var n;let i=A(n=r[t])?n[n.length-1]||0:n;e.hasValue(t)?e.getValue(t).set(i):e.addValue(t,V(i))}}(e,a)})}),d}function t8(e,t,i={}){let s=o(e,t,"exit"===i.type?e.presenceContext?.custom:void 0),{transition:r=e.getDefaultTransition()||{}}=s||{};i.transitionOverride&&(r=i.transitionOverride);let n=s?()=>Promise.all(t7(e,s,i)):()=>Promise.resolve(),a=e.variantChildren&&e.variantChildren.size?(s=0)=>{let{delayChildren:n=0,staggerChildren:a,staggerDirection:o}=r;return function(e,t,i=0,s=0,r=1,n){let a=[],o=(e.variantChildren.size-1)*s,l=1===r?(e=0)=>e*s:(e=0)=>o-e*s;return Array.from(e.variantChildren).sort(t9).forEach((e,s)=>{e.notify("AnimationStart",t),a.push(t8(e,t,{...n,delay:i+l(s)}).then(()=>e.notify("AnimationComplete",t)))}),Promise.all(a)}(e,t,n+s,a,o,i)}:()=>Promise.resolve(),{when:l}=r;if(!l)return Promise.all([n(),a(i.delay)]);{let[e,t]="beforeChildren"===l?[n,a]:[a,n];return e().then(()=>t())}}function t9(e,t){return e.sortNodePosition(t)}function ie(e,t){if(!Array.isArray(t))return!1;let i=t.length;if(i!==e.length)return!1;for(let s=0;s<i;s++)if(t[s]!==e[s])return!1;return!0}function it(e){return"string"==typeof e||Array.isArray(e)}let ii=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],is=["initial",...ii],ir=is.length,ia=[...ii].reverse(),io=ii.length;function il(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function ih(){return{animate:il(!0),whileInView:il(),whileHover:il(),whileTap:il(),whileDrag:il(),whileFocus:il(),exit:il()}}class id{constructor(e){this.isMounted=!1,this.node=e}update(){}}class ic extends id{constructor(e){super(e),e.animationState||(e.animationState=function(e){let t=t=>Promise.all(t.map(({animation:t,options:i})=>(function(e,t,i={}){let s;if(e.notify("AnimationStart",t),Array.isArray(t))s=Promise.all(t.map(t=>t8(e,t,i)));else if("string"==typeof t)s=t8(e,t,i);else{let r="function"==typeof t?o(e,t,i.custom):t;s=Promise.all(t7(e,r,i))}return s.then(()=>{e.notify("AnimationComplete",t)})})(e,t,i))),i=ih(),s=!0,n=t=>(i,s)=>{let r=o(e,s,"exit"===t?e.presenceContext?.custom:void 0);if(r){let{transition:e,transitionEnd:t,...s}=r;i={...i,...s,...t}}return i};function a(a){let{props:l}=e,h=function e(t){if(!t)return;if(!t.isControllingVariants){let i=t.parent&&e(t.parent)||{};return void 0!==t.props.initial&&(i.initial=t.props.initial),i}let i={};for(let e=0;e<ir;e++){let s=is[e],r=t.props[s];(it(r)||!1===r)&&(i[s]=r)}return i}(e.parent)||{},d=[],c=new Set,u={},p=1/0;for(let t=0;t<io;t++){var f,m;let o=ia[t],x=i[o],g=void 0!==l[o]?l[o]:h[o],y=it(g),v=o===a?x.isActive:null;!1===v&&(p=t);let w=g===h[o]&&g!==l[o]&&y;if(w&&s&&e.manuallyAnimateOnMount&&(w=!1),x.protectedKeys={...u},!x.isActive&&null===v||!g&&!x.prevProp||r(g)||"boolean"==typeof g)continue;let j=(f=x.prevProp,"string"==typeof(m=g)?m!==f:!!Array.isArray(m)&&!ie(m,f)),b=j||o===a&&x.isActive&&!w&&y||t>p&&y,_=!1,k=Array.isArray(g)?g:[g],C=k.reduce(n(o),{});!1===v&&(C={});let{prevResolvedValues:S={}}=x,M={...S,...C},O=t=>{b=!0,c.has(t)&&(_=!0,c.delete(t)),x.needsAnimating[t]=!0;let i=e.getValue(t);i&&(i.liveStyle=!1)};for(let e in M){let t=C[e],i=S[e];if(u.hasOwnProperty(e))continue;let s=!1;(A(t)&&A(i)?ie(t,i):t===i)?void 0!==t&&c.has(e)?O(e):x.protectedKeys[e]=!0:null!=t?O(e):c.add(e)}x.prevProp=g,x.prevResolvedValues=C,x.isActive&&(u={...u,...C}),s&&e.blockInitialAnimation&&(b=!1);let V=!(w&&j)||_;b&&V&&d.push(...k.map(e=>({animation:e,options:{type:o}})))}if(c.size){let t={};if("boolean"!=typeof l.initial){let i=o(e,Array.isArray(l.initial)?l.initial[0]:l.initial);i&&i.transition&&(t.transition=i.transition)}c.forEach(i=>{let s=e.getBaseTarget(i),r=e.getValue(i);r&&(r.liveStyle=!0),t[i]=s??null}),d.push({animation:t})}let x=!!d.length;return s&&(!1===l.initial||l.initial===l.animate)&&!e.manuallyAnimateOnMount&&(x=!1),s=!1,x?t(d):Promise.resolve()}return{animateChanges:a,setActive:function(t,s){if(i[t].isActive===s)return Promise.resolve();e.variantChildren?.forEach(e=>e.animationState?.setActive(t,s)),i[t].isActive=s;let r=a(t);for(let e in i)i[e].protectedKeys={};return r},setAnimateFunction:function(i){t=i(e)},getState:()=>i,reset:()=>{i=ih(),s=!0}}}(e))}updateAnimationControlsSubscription(){let{animate:e}=this.node.getProps();r(e)&&(this.unmountControls=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:e}=this.node.getProps(),{animate:t}=this.node.prevProps||{};e!==t&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let iu=0;class ip extends id{constructor(){super(...arguments),this.id=iu++}update(){if(!this.node.presenceContext)return;let{isPresent:e,onExitComplete:t}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===i)return;let s=this.node.animationState.setActive("exit",!e);t&&!e&&s.then(()=>{t(this.id)})}mount(){let{register:e,onExitComplete:t}=this.node.presenceContext||{};t&&t(this.id),e&&(this.unmount=e(this.id))}unmount(){}}let im={x:!1,y:!1};function ix(e,t,i,s={passive:!0}){return e.addEventListener(t,i,s),()=>e.removeEventListener(t,i)}let ig=e=>"mouse"===e.pointerType?"number"!=typeof e.button||e.button<=0:!1!==e.isPrimary;function iy(e){return{point:{x:e.pageX,y:e.pageY}}}let iv=e=>t=>ig(t)&&e(t,iy(t));function iw(e,t,i,s){return ix(e,t,iv(i),s)}function ij({top:e,left:t,right:i,bottom:s}){return{x:{min:t,max:i},y:{min:e,max:s}}}function ib(e){return e.max-e.min}function i_(e,t,i,s=.5){e.origin=s,e.originPoint=eS(t.min,t.max,e.origin),e.scale=ib(i)/ib(t),e.translate=eS(i.min,i.max,e.origin)-e.originPoint,(e.scale>=.9999&&e.scale<=1.0001||isNaN(e.scale))&&(e.scale=1),(e.translate>=-.01&&e.translate<=.01||isNaN(e.translate))&&(e.translate=0)}function ik(e,t,i,s){i_(e.x,t.x,i.x,s?s.originX:void 0),i_(e.y,t.y,i.y,s?s.originY:void 0)}function iC(e,t,i){e.min=i.min+t.min,e.max=e.min+ib(t)}function iS(e,t,i){e.min=t.min-i.min,e.max=e.min+ib(t)}function iM(e,t,i){iS(e.x,t.x,i.x),iS(e.y,t.y,i.y)}let iO=()=>({translate:0,scale:1,origin:0,originPoint:0}),iV=()=>({x:iO(),y:iO()}),iA=()=>({min:0,max:0}),iB=()=>({x:iA(),y:iA()});function iP(e){return[e("x"),e("y")]}function iL(e){return void 0===e||1===e}function iN({scale:e,scaleX:t,scaleY:i}){return!iL(e)||!iL(t)||!iL(i)}function iT(e){return iN(e)||iE(e)||e.z||e.rotate||e.rotateX||e.rotateY||e.skewX||e.skewY}function iE(e){var t,i;return(t=e.x)&&"0%"!==t||(i=e.y)&&"0%"!==i}function iR(e,t,i,s,r){return void 0!==r&&(e=s+r*(e-s)),s+i*(e-s)+t}function iI(e,t=0,i=1,s,r){e.min=iR(e.min,t,i,s,r),e.max=iR(e.max,t,i,s,r)}function iF(e,{x:t,y:i}){iI(e.x,t.translate,t.scale,t.originPoint),iI(e.y,i.translate,i.scale,i.originPoint)}function iU(e,t){e.min=e.min+t,e.max=e.max+t}function iD(e,t,i,s,r=.5){let n=eS(e.min,e.max,r);iI(e,t,i,n,s)}function iH(e,t){iD(e.x,t.x,t.scaleX,t.scale,t.originX),iD(e.y,t.y,t.scaleY,t.scale,t.originY)}function iG(e,t){return ij(function(e,t){if(!t)return e;let i=t({x:e.left,y:e.top}),s=t({x:e.right,y:e.bottom});return{top:i.y,left:i.x,bottom:s.y,right:s.x}}(e.getBoundingClientRect(),t))}let iz=({current:e})=>e?e.ownerDocument.defaultView:null;function iq(e){return e&&"object"==typeof e&&Object.prototype.hasOwnProperty.call(e,"current")}let iK=(e,t)=>Math.abs(e-t);class iW{constructor(e,t,{transformPagePoint:i,contextWindow:s,dragSnapToOrigin:r=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let e=iY(this.lastMoveEventInfo,this.history),t=null!==this.startEvent,i=function(e,t){return Math.sqrt(iK(e.x,t.x)**2+iK(e.y,t.y)**2)}(e.offset,{x:0,y:0})>=3;if(!t&&!i)return;let{point:s}=e,{timestamp:r}=x;this.history.push({...s,timestamp:r});let{onStart:n,onMove:a}=this.handlers;t||(n&&n(this.lastMoveEvent,e),this.startEvent=this.lastMoveEvent),a&&a(this.lastMoveEvent,e)},this.handlePointerMove=(e,t)=>{this.lastMoveEvent=e,this.lastMoveEventInfo=iZ(t,this.transformPagePoint),f.update(this.updatePoint,!0)},this.handlePointerUp=(e,t)=>{this.end();let{onEnd:i,onSessionEnd:s,resumeAnimation:r}=this.handlers;if(this.dragSnapToOrigin&&r&&r(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let n=iY("pointercancel"===e.type?this.lastMoveEventInfo:iZ(t,this.transformPagePoint),this.history);this.startEvent&&i&&i(e,n),s&&s(e,n)},!ig(e))return;this.dragSnapToOrigin=r,this.handlers=t,this.transformPagePoint=i,this.contextWindow=s||window;let n=iZ(iy(e),this.transformPagePoint),{point:a}=n,{timestamp:o}=x;this.history=[{...a,timestamp:o}];let{onSessionStart:l}=t;l&&l(e,iY(n,this.history)),this.removeListeners=E(iw(this.contextWindow,"pointermove",this.handlePointerMove),iw(this.contextWindow,"pointerup",this.handlePointerUp),iw(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),m(this.updatePoint)}}function iZ(e,t){return t?{point:t(e.point)}:e}function i$(e,t){return{x:e.x-t.x,y:e.y-t.y}}function iY({point:e},t){return{point:e,delta:i$(e,iX(t)),offset:i$(e,t[0]),velocity:function(e,t){if(e.length<2)return{x:0,y:0};let i=e.length-1,s=null,r=iX(e);for(;i>=0&&(s=e[i],!(r.timestamp-s.timestamp>I(.1)));)i--;if(!s)return{x:0,y:0};let n=F(r.timestamp-s.timestamp);if(0===n)return{x:0,y:0};let a={x:(r.x-s.x)/n,y:(r.y-s.y)/n};return a.x===1/0&&(a.x=0),a.y===1/0&&(a.y=0),a}(t,.1)}}function iX(e){return e[e.length-1]}function iJ(e,t,i){return{min:void 0!==t?e.min+t:void 0,max:void 0!==i?e.max+i-(e.max-e.min):void 0}}function iQ(e,t){let i=t.min-e.min,s=t.max-e.max;return t.max-t.min<e.max-e.min&&([i,s]=[s,i]),{min:i,max:s}}function i0(e,t,i){return{min:i1(e,t),max:i1(e,i)}}function i1(e,t){return"number"==typeof e?e:e[t]||0}let i2=new WeakMap;class i5{constructor(e){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=iB(),this.visualElement=e}start(e,{snapToCursor:t=!1}={}){let{presenceContext:i}=this.visualElement;if(i&&!1===i.isPresent)return;let{dragSnapToOrigin:s}=this.getProps();this.panSession=new iW(e,{onSessionStart:e=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),t&&this.snapToCursor(iy(e).point)},onStart:(e,t)=>{let{drag:i,dragPropagation:s,onDragStart:r}=this.getProps();if(i&&!s&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(e){if("x"===e||"y"===e)if(im[e])return null;else return im[e]=!0,()=>{im[e]=!1};return im.x||im.y?null:(im.x=im.y=!0,()=>{im.x=im.y=!1})}(i),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),iP(e=>{let t=this.getAxisMotionValue(e).get()||0;if(el.test(t)){let{projection:i}=this.visualElement;if(i&&i.layout){let s=i.layout.layoutBox[e];s&&(t=ib(s)*(parseFloat(t)/100))}}this.originPoint[e]=t}),r&&f.postRender(()=>r(e,t)),P(this.visualElement,"transform");let{animationState:n}=this.visualElement;n&&n.setActive("whileDrag",!0)},onMove:(e,t)=>{let{dragPropagation:i,dragDirectionLock:s,onDirectionLock:r,onDrag:n}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:a}=t;if(s&&null===this.currentDirection){this.currentDirection=function(e,t=10){let i=null;return Math.abs(e.y)>t?i="y":Math.abs(e.x)>t&&(i="x"),i}(a),null!==this.currentDirection&&r&&r(this.currentDirection);return}this.updateAxis("x",t.point,a),this.updateAxis("y",t.point,a),this.visualElement.render(),n&&n(e,t)},onSessionEnd:(e,t)=>this.stop(e,t),resumeAnimation:()=>iP(e=>"paused"===this.getAnimationState(e)&&this.getAxisMotionValue(e).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:s,contextWindow:iz(this.visualElement)})}stop(e,t){let i=this.isDragging;if(this.cancel(),!i)return;let{velocity:s}=t;this.startAnimation(s);let{onDragEnd:r}=this.getProps();r&&f.postRender(()=>r(e,t))}cancel(){this.isDragging=!1;let{projection:e,animationState:t}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),t&&t.setActive("whileDrag",!1)}updateAxis(e,t,i){let{drag:s}=this.getProps();if(!i||!i3(e,s,this.currentDirection))return;let r=this.getAxisMotionValue(e),n=this.originPoint[e]+i[e];this.constraints&&this.constraints[e]&&(n=function(e,{min:t,max:i},s){return void 0!==t&&e<t?e=s?eS(t,e,s.min):Math.max(e,t):void 0!==i&&e>i&&(e=s?eS(i,e,s.max):Math.min(e,i)),e}(n,this.constraints[e],this.elastic[e])),r.set(n)}resolveConstraints(){let{dragConstraints:e,dragElastic:t}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,s=this.constraints;e&&iq(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):e&&i?this.constraints=function(e,{top:t,left:i,bottom:s,right:r}){return{x:iJ(e.x,i,r),y:iJ(e.y,t,s)}}(i.layoutBox,e):this.constraints=!1,this.elastic=function(e=.35){return!1===e?e=0:!0===e&&(e=.35),{x:i0(e,"left","right"),y:i0(e,"top","bottom")}}(t),s!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&iP(e=>{!1!==this.constraints&&this.getAxisMotionValue(e)&&(this.constraints[e]=function(e,t){let i={};return void 0!==t.min&&(i.min=t.min-e.min),void 0!==t.max&&(i.max=t.max-e.min),i}(i.layoutBox[e],this.constraints[e]))})}resolveRefConstraints(){var e;let{dragConstraints:t,onMeasureDragConstraints:i}=this.getProps();if(!t||!iq(t))return!1;let s=t.current;H(null!==s,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:r}=this.visualElement;if(!r||!r.layout)return!1;let n=function(e,t,i){let s=iG(e,i),{scroll:r}=t;return r&&(iU(s.x,r.offset.x),iU(s.y,r.offset.y)),s}(s,r.root,this.visualElement.getTransformPagePoint()),a=(e=r.layout.layoutBox,{x:iQ(e.x,n.x),y:iQ(e.y,n.y)});if(i){let e=i(function({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}(a));this.hasMutatedConstraints=!!e,e&&(a=ij(e))}return a}startAnimation(e){let{drag:t,dragMomentum:i,dragElastic:s,dragTransition:r,dragSnapToOrigin:n,onDragTransitionEnd:a}=this.getProps(),o=this.constraints||{};return Promise.all(iP(a=>{if(!i3(a,t,this.currentDirection))return;let l=o&&o[a]||{};n&&(l={min:0,max:0});let h={type:"inertia",velocity:i?e[a]:0,bounceStiffness:s?200:1e6,bounceDamping:s?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...r,...l};return this.startAxisValueAnimation(a,h)})).then(a)}startAxisValueAnimation(e,t){let i=this.getAxisMotionValue(e);return P(this.visualElement,e),i.start(t6(e,i,0,t,this.visualElement,!1))}stopAnimation(){iP(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){iP(e=>this.getAxisMotionValue(e).animation?.pause())}getAnimationState(e){return this.getAxisMotionValue(e).animation?.state}getAxisMotionValue(e){let t=`_drag${e.toUpperCase()}`,i=this.visualElement.getProps();return i[t]||this.visualElement.getValue(e,(i.initial?i.initial[e]:void 0)||0)}snapToCursor(e){iP(t=>{let{drag:i}=this.getProps();if(!i3(t,i,this.currentDirection))return;let{projection:s}=this.visualElement,r=this.getAxisMotionValue(t);if(s&&s.layout){let{min:i,max:n}=s.layout.layoutBox[t];r.set(e[t]-eS(i,n,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:e,dragConstraints:t}=this.getProps(),{projection:i}=this.visualElement;if(!iq(t)||!i||!this.constraints)return;this.stopAnimation();let s={x:0,y:0};iP(e=>{let t=this.getAxisMotionValue(e);if(t&&!1!==this.constraints){let i=t.get();s[e]=function(e,t){let i=.5,s=ib(e),r=ib(t);return r>s?i=tn(t.min,t.max-s,e.min):s>r&&(i=tn(e.min,e.max-r,t.min)),R(0,1,i)}({min:i,max:i},this.constraints[e])}});let{transformTemplate:r}=this.visualElement.getProps();this.visualElement.current.style.transform=r?r({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),iP(t=>{if(!i3(t,e,null))return;let i=this.getAxisMotionValue(t),{min:r,max:n}=this.constraints[t];i.set(eS(r,n,s[t]))})}addListeners(){if(!this.visualElement.current)return;i2.set(this.visualElement,this);let e=iw(this.visualElement.current,"pointerdown",e=>{let{drag:t,dragListener:i=!0}=this.getProps();t&&i&&this.start(e)}),t=()=>{let{dragConstraints:e}=this.getProps();iq(e)&&e.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,s=i.addEventListener("measure",t);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),f.read(t);let r=ix(window,"resize",()=>this.scalePositionWithinConstraints()),n=i.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t})=>{this.isDragging&&t&&(iP(t=>{let i=this.getAxisMotionValue(t);i&&(this.originPoint[t]+=e[t].translate,i.set(i.get()+e[t].translate))}),this.visualElement.render())});return()=>{r(),e(),s(),n&&n()}}getProps(){let e=this.visualElement.getProps(),{drag:t=!1,dragDirectionLock:i=!1,dragPropagation:s=!1,dragConstraints:r=!1,dragElastic:n=.35,dragMomentum:a=!0}=e;return{...e,drag:t,dragDirectionLock:i,dragPropagation:s,dragConstraints:r,dragElastic:n,dragMomentum:a}}}function i3(e,t,i){return(!0===t||t===e)&&(null===i||i===e)}class i4 extends id{constructor(e){super(e),this.removeGroupControls=h,this.removeListeners=h,this.controls=new i5(e)}mount(){let{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||h}unmount(){this.removeGroupControls(),this.removeListeners()}}let i6=e=>(t,i)=>{e&&f.postRender(()=>e(t,i))};class i7 extends id{constructor(){super(...arguments),this.removePointerDownListener=h}onPointerDown(e){this.session=new iW(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:iz(this.node)})}createPanHandlers(){let{onPanSessionStart:e,onPanStart:t,onPan:i,onPanEnd:s}=this.node.getProps();return{onSessionStart:i6(e),onStart:i6(t),onMove:i,onEnd:(e,t)=>{delete this.session,s&&f.postRender(()=>s(e,t))}}}mount(){this.removePointerDownListener=iw(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var i8=i(99730);let{schedule:i9}=p(queueMicrotask,!1);var se=i(35371),st=i(27356),si=i(68797);let ss=(0,se.createContext)({}),sr={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function sn(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}let sa={correct:(e,t)=>{if(!t.target)return e;if("string"==typeof e)if(!eh.test(e))return e;else e=parseFloat(e);let i=sn(e,t.target.x),s=sn(e,t.target.y);return`${i}% ${s}%`}},so={};class sl extends se.Component{componentDidMount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:i,layoutId:s}=this.props,{projection:r}=e;for(let e in sd)so[e]=sd[e],z(e)&&(so[e].isCSSVariable=!0);r&&(t.group&&t.group.add(r),i&&i.register&&s&&i.register(r),r.root.didUpdate(),r.addEventListener("animationComplete",()=>{this.safeToRemove()}),r.setOptions({...r.options,onExitComplete:()=>this.safeToRemove()})),sr.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){let{layoutDependency:t,visualElement:i,drag:s,isPresent:r}=this.props,{projection:n}=i;return n&&(n.isPresent=r,s||e.layoutDependency!==t||void 0===t||e.isPresent!==r?n.willUpdate():this.safeToRemove(),e.isPresent!==r&&(r?n.promote():n.relegate()||f.postRender(()=>{let e=n.getStack();e&&e.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),i9.postRender(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:i}=this.props,{projection:s}=e;s&&(s.scheduleCheckAfterUnmount(),t&&t.group&&t.group.remove(s),i&&i.deregister&&i.deregister(s))}safeToRemove(){let{safeToRemove:e}=this.props;e&&e()}render(){return null}}function sh(e){let[t,i]=(0,st.xQ)(),s=(0,se.useContext)(si.L);return(0,i8.jsx)(sl,{...e,layoutGroup:s,switchLayoutGroup:(0,se.useContext)(ss),isPresent:t,safeToRemove:i})}let sd={borderRadius:{...sa,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:sa,borderTopRightRadius:sa,borderBottomLeftRadius:sa,borderBottomRightRadius:sa,boxShadow:{correct:(e,{treeScale:t,projectionDelta:i})=>{let s=e_.parse(e);if(s.length>5)return e;let r=e_.createTransformer(e),n=+("number"!=typeof s[0]),a=i.x.scale*t.x,o=i.y.scale*t.y;s[0+n]/=a,s[1+n]/=o;let l=eS(a,o,.5);return"number"==typeof s[2+n]&&(s[2+n]/=l),"number"==typeof s[3+n]&&(s[3+n]/=l),r(s)}}};var sc=i(90043);function su(e){return(0,sc.G)(e)&&"ownerSVGElement"in e}let sp=(e,t)=>e.depth-t.depth;class sf{constructor(){this.children=[],this.isDirty=!1}add(e){j(this.children,e),this.isDirty=!0}remove(e){b(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(sp),this.isDirty=!1,this.children.forEach(e)}}function sm(e){return B(e)?e.get():e}let sx=["TopLeft","TopRight","BottomLeft","BottomRight"],sg=sx.length,sy=e=>"string"==typeof e?parseFloat(e):e,sv=e=>"number"==typeof e||eh.test(e);function sw(e,t){return void 0!==e[t]?e[t]:e.borderRadius}let sj=s_(0,.5,e9),sb=s_(.5,.95,h);function s_(e,t,i){return s=>s<e?0:s>t?1:i(tn(e,t,s))}function sk(e,t){e.min=t.min,e.max=t.max}function sC(e,t){sk(e.x,t.x),sk(e.y,t.y)}function sS(e,t){e.translate=t.translate,e.scale=t.scale,e.originPoint=t.originPoint,e.origin=t.origin}function sM(e,t,i,s,r){return e-=t,e=s+1/i*(e-s),void 0!==r&&(e=s+1/r*(e-s)),e}function sO(e,t,[i,s,r],n,a){!function(e,t=0,i=1,s=.5,r,n=e,a=e){if(el.test(t)&&(t=parseFloat(t),t=eS(a.min,a.max,t/100)-a.min),"number"!=typeof t)return;let o=eS(n.min,n.max,s);e===n&&(o-=t),e.min=sM(e.min,t,i,o,r),e.max=sM(e.max,t,i,o,r)}(e,t[i],t[s],t[r],t.scale,n,a)}let sV=["x","scaleX","originX"],sA=["y","scaleY","originY"];function sB(e,t,i,s){sO(e.x,t,sV,i?i.x:void 0,s?s.x:void 0),sO(e.y,t,sA,i?i.y:void 0,s?s.y:void 0)}function sP(e){return 0===e.translate&&1===e.scale}function sL(e){return sP(e.x)&&sP(e.y)}function sN(e,t){return e.min===t.min&&e.max===t.max}function sT(e,t){return Math.round(e.min)===Math.round(t.min)&&Math.round(e.max)===Math.round(t.max)}function sE(e,t){return sT(e.x,t.x)&&sT(e.y,t.y)}function sR(e){return ib(e.x)/ib(e.y)}function sI(e,t){return e.translate===t.translate&&e.scale===t.scale&&e.originPoint===t.originPoint}class sF{constructor(){this.members=[]}add(e){j(this.members,e),e.scheduleRender()}remove(e){if(b(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){let e=this.members[this.members.length-1];e&&this.promote(e)}}relegate(e){let t,i=this.members.findIndex(t=>e===t);if(0===i)return!1;for(let e=i;e>=0;e--){let i=this.members[e];if(!1!==i.isPresent){t=i;break}}return!!t&&(this.promote(t),!0)}promote(e,t){let i=this.lead;if(e!==i&&(this.prevLead=i,this.lead=e,e.show(),i)){i.instance&&i.scheduleRender(),e.scheduleRender(),e.resumeFrom=i,t&&(e.resumeFrom.preserveOpacity=!0),i.snapshot&&(e.snapshot=i.snapshot,e.snapshot.latestValues=i.animationValues||i.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);let{crossfade:s}=e.options;!1===s&&i.hide()}}exitAnimationComplete(){this.members.forEach(e=>{let{options:t,resumingFrom:i}=e;t.onExitComplete&&t.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let sU={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},sD=["","X","Y","Z"],sH={visibility:"hidden"},sG=0;function sz(e,t,i,s){let{latestValues:r}=t;r[e]&&(i[e]=r[e],t.setStaticValue(e,0),s&&(s[e]=0))}function sq({attachResizeListener:e,defaultParent:t,measureScroll:i,checkIsScrollRoot:s,resetTransform:r}){return class{constructor(e={},i=t?.()){this.id=sG++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,u.value&&(sU.nodes=sU.calculatedTargetDeltas=sU.calculatedProjections=0),this.nodes.forEach(sZ),this.nodes.forEach(s1),this.nodes.forEach(s2),this.nodes.forEach(s$),u.addProjectionMetrics&&u.addProjectionMetrics(sU)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=e,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let e=0;e<this.path.length;e++)this.path[e].shouldResetTransform=!0;this.root===this&&(this.nodes=new sf)}addEventListener(e,t){return this.eventHandlers.has(e)||this.eventHandlers.set(e,new _),this.eventHandlers.get(e).add(t)}notifyListeners(e,...t){let i=this.eventHandlers.get(e);i&&i.notify(...t)}hasListeners(e){return this.eventHandlers.has(e)}mount(t){if(this.instance)return;this.isSVG=su(t)&&!(su(t)&&"svg"===t.tagName),this.instance=t;let{layoutId:i,layout:s,visualElement:r}=this.options;if(r&&!r.current&&r.mount(t),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(s||i)&&(this.isLayoutDirty=!0),e){let i,s=()=>this.root.updateBlockedByResize=!1;e(t,()=>{this.root.updateBlockedByResize=!0,i&&i(),i=function(e,t){let i=C.now(),s=({timestamp:r})=>{let n=r-i;n>=250&&(m(s),e(n-t))};return f.setup(s,!0),()=>m(s)}(s,250),sr.hasAnimatedSinceResize&&(sr.hasAnimatedSinceResize=!1,this.nodes.forEach(s0))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&r&&(i||s)&&this.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t,hasRelativeLayoutChanged:i,layout:s})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let n=this.options.transition||r.getDefaultTransition()||s8,{onLayoutAnimationStart:a,onLayoutAnimationComplete:o}=r.getProps(),h=!this.targetLayout||!sE(this.targetLayout,s),d=!t&&i;if(this.options.layoutRoot||this.resumeFrom||d||t&&(h||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let t={...l(n,"layout"),onPlay:a,onComplete:o};(r.shouldReduceMotion||this.options.layoutRoot)&&(t.delay=0,t.type=!1),this.startAnimation(t),this.setAnimationOrigin(e,d)}else t||s0(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=s})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let e=this.getStack();e&&e.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),m(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(s5),this.animationId++)}getTransformTemplate(){let{visualElement:e}=this.options;return e&&e.getProps().transformTemplate}willUpdate(e=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function e(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;let{visualElement:i}=t.options;if(!i)return;let s=i.props[N];if(window.MotionHasOptimisedAnimation(s,"transform")){let{layout:e,layoutId:i}=t.options;window.MotionCancelOptimisedAnimation(s,"transform",f,!(e||i))}let{parent:r}=t;r&&!r.hasCheckedOptimisedAppear&&e(r)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let e=0;e<this.path.length;e++){let t=this.path[e];t.shouldResetTransform=!0,t.updateScroll("snapshot"),t.options.layoutRoot&&t.willUpdate(!1)}let{layoutId:t,layout:i}=this.options;if(void 0===t&&!i)return;let s=this.getTransformTemplate();this.prevTransformTemplateValue=s?s(this.latestValues,""):void 0,this.updateSnapshot(),e&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(sX);return}if(this.animationId<=this.animationCommitId)return void this.nodes.forEach(sJ);this.isUpdating||this.nodes.forEach(sJ),this.animationCommitId=this.animationId,this.isUpdating=!1,this.nodes.forEach(sQ),this.nodes.forEach(sK),this.nodes.forEach(sW),this.clearAllSnapshots();let e=C.now();x.delta=R(0,1e3/60,e-x.timestamp),x.timestamp=e,x.isProcessing=!0,g.update.process(x),g.preRender.process(x),g.render.process(x),x.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,i9.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(sY),this.sharedNodes.forEach(s3)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,f.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){f.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||ib(this.snapshot.measuredBox.x)||ib(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let e=0;e<this.path.length;e++)this.path[e].updateScroll();let e=this.layout;this.layout=this.measure(!1),this.layoutCorrected=iB(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:t}=this.options;t&&t.notify("LayoutMeasure",this.layout.layoutBox,e?e.layoutBox:void 0)}updateScroll(e="measure"){let t=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===e&&(t=!1),t&&this.instance){let t=s(this.instance);this.scroll={animationId:this.root.animationId,phase:e,isRoot:t,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:t}}}resetTransform(){if(!r)return;let e=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,t=this.projectionDelta&&!sL(this.projectionDelta),i=this.getTransformTemplate(),s=i?i(this.latestValues,""):void 0,n=s!==this.prevTransformTemplateValue;e&&this.instance&&(t||iT(this.latestValues)||n)&&(r(this.instance,s),this.shouldResetTransform=!1,this.scheduleRender())}measure(e=!0){var t;let i=this.measurePageBox(),s=this.removeElementScroll(i);return e&&(s=this.removeTransform(s)),rt((t=s).x),rt(t.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:s,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:e}=this.options;if(!e)return iB();let t=e.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(rs))){let{scroll:e}=this.root;e&&(iU(t.x,e.offset.x),iU(t.y,e.offset.y))}return t}removeElementScroll(e){let t=iB();if(sC(t,e),this.scroll?.wasRoot)return t;for(let i=0;i<this.path.length;i++){let s=this.path[i],{scroll:r,options:n}=s;s!==this.root&&r&&n.layoutScroll&&(r.wasRoot&&sC(t,e),iU(t.x,r.offset.x),iU(t.y,r.offset.y))}return t}applyTransform(e,t=!1){let i=iB();sC(i,e);for(let e=0;e<this.path.length;e++){let s=this.path[e];!t&&s.options.layoutScroll&&s.scroll&&s!==s.root&&iH(i,{x:-s.scroll.offset.x,y:-s.scroll.offset.y}),iT(s.latestValues)&&iH(i,s.latestValues)}return iT(this.latestValues)&&iH(i,this.latestValues),i}removeTransform(e){let t=iB();sC(t,e);for(let e=0;e<this.path.length;e++){let i=this.path[e];if(!i.instance||!iT(i.latestValues))continue;iN(i.latestValues)&&i.updateSnapshot();let s=iB();sC(s,i.measurePageBox()),sB(t,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,s)}return iT(this.latestValues)&&sB(t,this.latestValues),t}setTargetDelta(e){this.targetDelta=e,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(e){this.options={...this.options,...e,crossfade:void 0===e.crossfade||e.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==x.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(e=!1){let t=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=t.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=t.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=t.isSharedProjectionDirty);let i=!!this.resumingFrom||this!==t;if(!(e||i&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:s,layoutId:r}=this.options;if(this.layout&&(s||r)){if(this.resolvedRelativeTargetAt=x.timestamp,!this.targetDelta&&!this.relativeTarget){let e=this.getClosestProjectingParent();e&&e.layout&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=iB(),this.relativeTargetOrigin=iB(),iM(this.relativeTargetOrigin,this.layout.layoutBox,e.layout.layoutBox),sC(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=iB(),this.targetWithTransforms=iB()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var n,a,o;this.forceRelativeParentToResolveTarget(),n=this.target,a=this.relativeTarget,o=this.relativeParent.target,iC(n.x,a.x,o.x),iC(n.y,a.y,o.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):sC(this.target,this.layout.layoutBox),iF(this.target,this.targetDelta)):sC(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let e=this.getClosestProjectingParent();e&&!!e.resumingFrom==!!this.resumingFrom&&!e.options.layoutScroll&&e.target&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=iB(),this.relativeTargetOrigin=iB(),iM(this.relativeTargetOrigin,this.target,e.target),sC(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}u.value&&sU.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||iN(this.parent.latestValues)||iE(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let e=this.getLead(),t=!!this.resumingFrom||this!==e,i=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(i=!1),t&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===x.timestamp&&(i=!1),i)return;let{layout:s,layoutId:r}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(s||r))return;sC(this.layoutCorrected,this.layout.layoutBox);let n=this.treeScale.x,a=this.treeScale.y;!function(e,t,i,s=!1){let r,n,a=i.length;if(a){t.x=t.y=1;for(let o=0;o<a;o++){n=(r=i[o]).projectionDelta;let{visualElement:a}=r.options;(!a||!a.props.style||"contents"!==a.props.style.display)&&(s&&r.options.layoutScroll&&r.scroll&&r!==r.root&&iH(e,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),n&&(t.x*=n.x.scale,t.y*=n.y.scale,iF(e,n)),s&&iT(r.latestValues)&&iH(e,r.latestValues))}t.x<1.0000000000001&&t.x>.999999999999&&(t.x=1),t.y<1.0000000000001&&t.y>.999999999999&&(t.y=1)}}(this.layoutCorrected,this.treeScale,this.path,t),e.layout&&!e.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(e.target=e.layout.layoutBox,e.targetWithTransforms=iB());let{target:o}=e;if(!o){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(sS(this.prevProjectionDelta.x,this.projectionDelta.x),sS(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),ik(this.projectionDelta,this.layoutCorrected,o,this.latestValues),this.treeScale.x===n&&this.treeScale.y===a&&sI(this.projectionDelta.x,this.prevProjectionDelta.x)&&sI(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",o)),u.value&&sU.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(e=!0){if(this.options.visualElement?.scheduleRender(),e){let e=this.getStack();e&&e.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=iV(),this.projectionDelta=iV(),this.projectionDeltaWithTransform=iV()}setAnimationOrigin(e,t=!1){let i,s=this.snapshot,r=s?s.latestValues:{},n={...this.latestValues},a=iV();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!t;let o=iB(),l=(s?s.source:void 0)!==(this.layout?this.layout.source:void 0),h=this.getStack(),d=!h||h.members.length<=1,c=!!(l&&!d&&!0===this.options.crossfade&&!this.path.some(s7));this.animationProgress=0,this.mixTargetDelta=t=>{let s=t/1e3;if(s4(a.x,e.x,s),s4(a.y,e.y,s),this.setTargetDelta(a),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var h,u,p,f,m,x;iM(o,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,f=this.relativeTargetOrigin,m=o,x=s,s6(p.x,f.x,m.x,x),s6(p.y,f.y,m.y,x),i&&(h=this.relativeTarget,u=i,sN(h.x,u.x)&&sN(h.y,u.y))&&(this.isProjectionDirty=!1),i||(i=iB()),sC(i,this.relativeTarget)}l&&(this.animationValues=n,function(e,t,i,s,r,n){r?(e.opacity=eS(0,i.opacity??1,sj(s)),e.opacityExit=eS(t.opacity??1,0,sb(s))):n&&(e.opacity=eS(t.opacity??1,i.opacity??1,s));for(let r=0;r<sg;r++){let n=`border${sx[r]}Radius`,a=sw(t,n),o=sw(i,n);(void 0!==a||void 0!==o)&&(a||(a=0),o||(o=0),0===a||0===o||sv(a)===sv(o)?(e[n]=Math.max(eS(sy(a),sy(o),s),0),(el.test(o)||el.test(a))&&(e[n]+="%")):e[n]=o)}(t.rotate||i.rotate)&&(e.rotate=eS(t.rotate||0,i.rotate||0,s))}(n,r,this.latestValues,s,c,d)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=s},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(e){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(m(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=f.update(()=>{sr.hasAnimatedSinceResize=!0,U.layout++,this.motionValue||(this.motionValue=V(0)),this.currentAnimation=function(e,t,i){let s=B(e)?e:V(e);return s.start(t6("",s,t,i)),s.animation}(this.motionValue,[0,1e3],{...e,velocity:0,isSync:!0,onUpdate:t=>{this.mixTargetDelta(t),e.onUpdate&&e.onUpdate(t)},onStop:()=>{U.layout--},onComplete:()=>{U.layout--,e.onComplete&&e.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let e=this.getStack();e&&e.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let e=this.getLead(),{targetWithTransforms:t,target:i,layout:s,latestValues:r}=e;if(t&&i&&s){if(this!==e&&this.layout&&s&&ri(this.options.animationType,this.layout.layoutBox,s.layoutBox)){i=this.target||iB();let t=ib(this.layout.layoutBox.x);i.x.min=e.target.x.min,i.x.max=i.x.min+t;let s=ib(this.layout.layoutBox.y);i.y.min=e.target.y.min,i.y.max=i.y.min+s}sC(t,i),iH(t,r),ik(this.projectionDeltaWithTransform,this.layoutCorrected,t,r)}}registerSharedNode(e,t){this.sharedNodes.has(e)||this.sharedNodes.set(e,new sF),this.sharedNodes.get(e).add(t);let i=t.options.initialPromotionConfig;t.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(t):void 0})}isLead(){let e=this.getStack();return!e||e.lead===this}getLead(){let{layoutId:e}=this.options;return e&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:e}=this.options;return e?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:e}=this.options;if(e)return this.root.sharedNodes.get(e)}promote({needsReset:e,transition:t,preserveFollowOpacity:i}={}){let s=this.getStack();s&&s.promote(this,i),e&&(this.projectionDelta=void 0,this.needsReset=!0),t&&this.setOptions({transition:t})}relegate(){let e=this.getStack();return!!e&&e.relegate(this)}resetSkewAndRotation(){let{visualElement:e}=this.options;if(!e)return;let t=!1,{latestValues:i}=e;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(t=!0),!t)return;let s={};i.z&&sz("z",e,s,this.animationValues);for(let t=0;t<sD.length;t++)sz(`rotate${sD[t]}`,e,s,this.animationValues),sz(`skew${sD[t]}`,e,s,this.animationValues);for(let t in e.render(),s)e.setStaticValue(t,s[t]),this.animationValues&&(this.animationValues[t]=s[t]);e.scheduleRender()}getProjectionStyles(e){if(!this.instance||this.isSVG)return;if(!this.isVisible)return sH;let t={visibility:""},i=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,t.opacity="",t.pointerEvents=sm(e?.pointerEvents)||"",t.transform=i?i(this.latestValues,""):"none",t;let s=this.getLead();if(!this.projectionDelta||!this.layout||!s.target){let t={};return this.options.layoutId&&(t.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,t.pointerEvents=sm(e?.pointerEvents)||""),this.hasProjected&&!iT(this.latestValues)&&(t.transform=i?i({},""):"none",this.hasProjected=!1),t}let r=s.animationValues||s.latestValues;this.applyTransformsToTarget(),t.transform=function(e,t,i){let s="",r=e.x.translate/t.x,n=e.y.translate/t.y,a=i?.z||0;if((r||n||a)&&(s=`translate3d(${r}px, ${n}px, ${a}px) `),(1!==t.x||1!==t.y)&&(s+=`scale(${1/t.x}, ${1/t.y}) `),i){let{transformPerspective:e,rotate:t,rotateX:r,rotateY:n,skewX:a,skewY:o}=i;e&&(s=`perspective(${e}px) ${s}`),t&&(s+=`rotate(${t}deg) `),r&&(s+=`rotateX(${r}deg) `),n&&(s+=`rotateY(${n}deg) `),a&&(s+=`skewX(${a}deg) `),o&&(s+=`skewY(${o}deg) `)}let o=e.x.scale*t.x,l=e.y.scale*t.y;return(1!==o||1!==l)&&(s+=`scale(${o}, ${l})`),s||"none"}(this.projectionDeltaWithTransform,this.treeScale,r),i&&(t.transform=i(r,t.transform));let{x:n,y:a}=this.projectionDelta;for(let e in t.transformOrigin=`${100*n.origin}% ${100*a.origin}% 0`,s.animationValues?t.opacity=s===this?r.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:r.opacityExit:t.opacity=s===this?void 0!==r.opacity?r.opacity:"":void 0!==r.opacityExit?r.opacityExit:0,so){if(void 0===r[e])continue;let{correct:i,applyTo:n,isCSSVariable:a}=so[e],o="none"===t.transform?r[e]:i(r[e],s);if(n){let e=n.length;for(let i=0;i<e;i++)t[n[i]]=o}else a?this.options.visualElement.renderState.vars[e]=o:t[e]=o}return this.options.layoutId&&(t.pointerEvents=s===this?sm(e?.pointerEvents)||"":"none"),t}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(e=>e.currentAnimation?.stop()),this.root.nodes.forEach(sX),this.root.sharedNodes.clear()}}}function sK(e){e.updateLayout()}function sW(e){let t=e.resumeFrom?.snapshot||e.snapshot;if(e.isLead()&&e.layout&&t&&e.hasListeners("didUpdate")){let{layoutBox:i,measuredBox:s}=e.layout,{animationType:r}=e.options,n=t.source!==e.layout.source;"size"===r?iP(e=>{let s=n?t.measuredBox[e]:t.layoutBox[e],r=ib(s);s.min=i[e].min,s.max=s.min+r}):ri(r,t.layoutBox,i)&&iP(s=>{let r=n?t.measuredBox[s]:t.layoutBox[s],a=ib(i[s]);r.max=r.min+a,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[s].max=e.relativeTarget[s].min+a)});let a=iV();ik(a,i,t.layoutBox);let o=iV();n?ik(o,e.applyTransform(s,!0),t.measuredBox):ik(o,i,t.layoutBox);let l=!sL(a),h=!1;if(!e.resumeFrom){let s=e.getClosestProjectingParent();if(s&&!s.resumeFrom){let{snapshot:r,layout:n}=s;if(r&&n){let a=iB();iM(a,t.layoutBox,r.layoutBox);let o=iB();iM(o,i,n.layoutBox),sE(a,o)||(h=!0),s.options.layoutRoot&&(e.relativeTarget=o,e.relativeTargetOrigin=a,e.relativeParent=s)}}}e.notifyListeners("didUpdate",{layout:i,snapshot:t,delta:o,layoutDelta:a,hasLayoutChanged:l,hasRelativeLayoutChanged:h})}else if(e.isLead()){let{onExitComplete:t}=e.options;t&&t()}e.options.transition=void 0}function sZ(e){u.value&&sU.nodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function s$(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function sY(e){e.clearSnapshot()}function sX(e){e.clearMeasurements()}function sJ(e){e.isLayoutDirty=!1}function sQ(e){let{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function s0(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function s1(e){e.resolveTargetDelta()}function s2(e){e.calcProjection()}function s5(e){e.resetSkewAndRotation()}function s3(e){e.removeLeadSnapshot()}function s4(e,t,i){e.translate=eS(t.translate,0,i),e.scale=eS(t.scale,1,i),e.origin=t.origin,e.originPoint=t.originPoint}function s6(e,t,i,s){e.min=eS(t.min,i.min,s),e.max=eS(t.max,i.max,s)}function s7(e){return e.animationValues&&void 0!==e.animationValues.opacityExit}let s8={duration:.45,ease:[.4,0,.1,1]},s9=e=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e),re=s9("applewebkit/")&&!s9("chrome/")?Math.round:h;function rt(e){e.min=re(e.min),e.max=re(e.max)}function ri(e,t,i){return"position"===e||"preserve-aspect"===e&&!(.2>=Math.abs(sR(t)-sR(i)))}function rs(e){return e!==e.root&&e.scroll?.wasRoot}let rr=sq({attachResizeListener:(e,t)=>ix(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),rn={current:void 0},ra=sq({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!rn.current){let e=new rr({});e.mount(window),e.setOptions({layoutScroll:!0}),rn.current=e}return rn.current},resetTransform:(e,t)=>{e.style.transform=void 0!==t?t:"none"},checkIsScrollRoot:e=>"fixed"===window.getComputedStyle(e).position});function ro(e,t){let i=function(e,t,i){if(e instanceof EventTarget)return[e];if("string"==typeof e){let t=document,i=(void 0)??t.querySelectorAll(e);return i?Array.from(i):[]}return Array.from(e)}(e),s=new AbortController;return[i,{passive:!0,...t,signal:s.signal},()=>s.abort()]}function rl(e){return!("touch"===e.pointerType||im.x||im.y)}function rh(e,t,i){let{props:s}=e;e.animationState&&s.whileHover&&e.animationState.setActive("whileHover","Start"===i);let r=s["onHover"+i];r&&f.postRender(()=>r(t,iy(t)))}class rd extends id{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,i={}){let[s,r,n]=ro(e,i),a=e=>{if(!rl(e))return;let{target:i}=e,s=t(i,e);if("function"!=typeof s||!i)return;let n=e=>{rl(e)&&(s(e),i.removeEventListener("pointerleave",n))};i.addEventListener("pointerleave",n,r)};return s.forEach(e=>{e.addEventListener("pointerenter",a,r)}),n}(e,(e,t)=>(rh(this.node,t,"Start"),e=>rh(this.node,e,"End"))))}unmount(){}}class rc extends id{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch(t){e=!0}e&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=E(ix(this.node.current,"focus",()=>this.onFocus()),ix(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let ru=(e,t)=>!!t&&(e===t||ru(e,t.parentElement)),rp=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),rf=new WeakSet;function rm(e){return t=>{"Enter"===t.key&&e(t)}}function rx(e,t){e.dispatchEvent(new PointerEvent("pointer"+t,{isPrimary:!0,bubbles:!0}))}let rg=(e,t)=>{let i=e.currentTarget;if(!i)return;let s=rm(()=>{if(rf.has(i))return;rx(i,"down");let e=rm(()=>{rx(i,"up")});i.addEventListener("keyup",e,t),i.addEventListener("blur",()=>rx(i,"cancel"),t)});i.addEventListener("keydown",s,t),i.addEventListener("blur",()=>i.removeEventListener("keydown",s),t)};function ry(e){return ig(e)&&!(im.x||im.y)}function rv(e,t,i){let{props:s}=e;if(e.current instanceof HTMLButtonElement&&e.current.disabled)return;e.animationState&&s.whileTap&&e.animationState.setActive("whileTap","Start"===i);let r=s["onTap"+("End"===i?"":i)];r&&f.postRender(()=>r(t,iy(t)))}class rw extends id{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,i={}){let[s,r,n]=ro(e,i),a=e=>{let s=e.currentTarget;if(!ry(e))return;rf.add(s);let n=t(s,e),a=(e,t)=>{window.removeEventListener("pointerup",o),window.removeEventListener("pointercancel",l),rf.has(s)&&rf.delete(s),ry(e)&&"function"==typeof n&&n(e,{success:t})},o=e=>{a(e,s===window||s===document||i.useGlobalTarget||ru(s,e.target))},l=e=>{a(e,!1)};window.addEventListener("pointerup",o,r),window.addEventListener("pointercancel",l,r)};return s.forEach(e=>{((i.useGlobalTarget?window:e).addEventListener("pointerdown",a,r),(0,tY.s)(e))&&(e.addEventListener("focus",e=>rg(e,r)),rp.has(e.tagName)||-1!==e.tabIndex||e.hasAttribute("tabindex")||(e.tabIndex=0))}),n}(e,(e,t)=>(rv(this.node,t,"Start"),(e,{success:t})=>rv(this.node,e,t?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let rj=new WeakMap,rb=new WeakMap,r_=e=>{let t=rj.get(e.target);t&&t(e)},rk=e=>{e.forEach(r_)},rC={some:0,all:1};class rS extends id{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:e={}}=this.node.getProps(),{root:t,margin:i,amount:s="some",once:r}=e,n={root:t?t.current:void 0,rootMargin:i,threshold:"number"==typeof s?s:rC[s]};return function(e,t,i){let s=function({root:e,...t}){let i=e||document;rb.has(i)||rb.set(i,{});let s=rb.get(i),r=JSON.stringify(t);return s[r]||(s[r]=new IntersectionObserver(rk,{root:e,...t})),s[r]}(t);return rj.set(e,i),s.observe(e),()=>{rj.delete(e),s.unobserve(e)}}(this.node.current,n,e=>{let{isIntersecting:t}=e;if(this.isInView===t||(this.isInView=t,r&&!t&&this.hasEnteredView))return;t&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",t);let{onViewportEnter:i,onViewportLeave:s}=this.node.getProps(),n=t?i:s;n&&n(e)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:e,prevProps:t}=this.node;["amount","margin","root"].some(function({viewport:e={}},{viewport:t={}}={}){return i=>e[i]!==t[i]}(e,t))&&this.startObserver()}unmount(){}}let rM=(0,se.createContext)({strict:!1});var rO=i(15110);let rV=(0,se.createContext)({});function rA(e){return r(e.animate)||is.some(t=>it(e[t]))}function rB(e){return!!(rA(e)||e.variants)}function rP(e){return Array.isArray(e)?e.join(" "):e}var rL=i(34500);let rN={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},rT={};for(let e in rN)rT[e]={isEnabled:t=>rN[e].some(e=>!!t[e])};let rE=Symbol.for("motionComponentSymbol");var rR=i(62431),rI=i(81300);function rF(e,{layout:t,layoutId:i}){return v.has(e)||e.startsWith("origin")||(t||void 0!==i)&&(!!so[e]||"opacity"===e)}let rU=(e,t)=>t&&"number"==typeof e?t.transform(e):e,rD={...Z,transform:Math.round},rH={borderWidth:eh,borderTopWidth:eh,borderRightWidth:eh,borderBottomWidth:eh,borderLeftWidth:eh,borderRadius:eh,radius:eh,borderTopLeftRadius:eh,borderTopRightRadius:eh,borderBottomRightRadius:eh,borderBottomLeftRadius:eh,width:eh,maxWidth:eh,height:eh,maxHeight:eh,top:eh,right:eh,bottom:eh,left:eh,padding:eh,paddingTop:eh,paddingRight:eh,paddingBottom:eh,paddingLeft:eh,margin:eh,marginTop:eh,marginRight:eh,marginBottom:eh,marginLeft:eh,backgroundPositionX:eh,backgroundPositionY:eh,rotate:eo,rotateX:eo,rotateY:eo,rotateZ:eo,scale:Y,scaleX:Y,scaleY:Y,scaleZ:Y,skew:eo,skewX:eo,skewY:eo,distance:eh,translateX:eh,translateY:eh,translateZ:eh,x:eh,y:eh,z:eh,perspective:eh,transformPerspective:eh,opacity:$,originX:eu,originY:eu,originZ:eh,zIndex:rD,fillOpacity:$,strokeOpacity:$,numOctaves:rD},rG={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},rz=y.length;function rq(e,t,i){let{style:s,vars:r,transformOrigin:n}=e,a=!1,o=!1;for(let e in t){let i=t[e];if(v.has(e)){a=!0;continue}if(z(e)){r[e]=i;continue}{let t=rU(i,rH[e]);e.startsWith("origin")?(o=!0,n[e]=t):s[e]=t}}if(!t.transform&&(a||i?s.transform=function(e,t,i){let s="",r=!0;for(let n=0;n<rz;n++){let a=y[n],o=e[a];if(void 0===o)continue;let l=!0;if(!(l="number"==typeof o?o===+!!a.startsWith("scale"):0===parseFloat(o))||i){let e=rU(o,rH[a]);if(!l){r=!1;let t=rG[a]||a;s+=`${t}(${e}) `}i&&(t[a]=e)}}return s=s.trim(),i?s=i(t,r?"":s):r&&(s="none"),s}(t,e.transform,i):s.transform&&(s.transform="none")),o){let{originX:e="50%",originY:t="50%",originZ:i=0}=n;s.transformOrigin=`${e} ${t} ${i}`}}let rK=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function rW(e,t,i){for(let s in t)B(t[s])||rF(s,i)||(e[s]=t[s])}let rZ={offset:"stroke-dashoffset",array:"stroke-dasharray"},r$={offset:"strokeDashoffset",array:"strokeDasharray"};function rY(e,{attrX:t,attrY:i,attrScale:s,pathLength:r,pathSpacing:n=1,pathOffset:a=0,...o},l,h,d){if(rq(e,o,h),l){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};let{attrs:c,style:u}=e;c.transform&&(u.transform=c.transform,delete c.transform),(u.transform||c.transformOrigin)&&(u.transformOrigin=c.transformOrigin??"50% 50%",delete c.transformOrigin),u.transform&&(u.transformBox=d?.transformBox??"fill-box",delete c.transformBox),void 0!==t&&(c.x=t),void 0!==i&&(c.y=i),void 0!==s&&(c.scale=s),void 0!==r&&function(e,t,i=1,s=0,r=!0){e.pathLength=1;let n=r?rZ:r$;e[n.offset]=eh.transform(-s);let a=eh.transform(t),o=eh.transform(i);e[n.array]=`${a} ${o}`}(c,r,n,a,!1)}let rX=()=>({...rK(),attrs:{}}),rJ=e=>"string"==typeof e&&"svg"===e.toLowerCase(),rQ=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function r0(e){return e.startsWith("while")||e.startsWith("drag")&&"draggable"!==e||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||rQ.has(e)}let r1=e=>!r0(e);try{!function(e){"function"==typeof e&&(r1=t=>t.startsWith("on")?!r0(t):e(t))}(require("@emotion/is-prop-valid").default)}catch{}let r2=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function r5(e){if("string"!=typeof e||e.includes("-"));else if(r2.indexOf(e)>-1||/[A-Z]/u.test(e))return!0;return!1}var r3=i(46069);let r4=e=>(t,i)=>{let s=(0,se.useContext)(rV),n=(0,se.useContext)(rR.t),o=()=>(function({scrapeMotionValuesFromProps:e,createRenderState:t},i,s,n){return{latestValues:function(e,t,i,s){let n={},o=s(e,{});for(let e in o)n[e]=sm(o[e]);let{initial:l,animate:h}=e,d=rA(e),c=rB(e);t&&c&&!d&&!1!==e.inherit&&(void 0===l&&(l=t.initial),void 0===h&&(h=t.animate));let u=!!i&&!1===i.initial,p=(u=u||!1===l)?h:l;if(p&&"boolean"!=typeof p&&!r(p)){let t=Array.isArray(p)?p:[p];for(let i=0;i<t.length;i++){let s=a(e,t[i]);if(s){let{transitionEnd:e,transition:t,...i}=s;for(let e in i){let t=i[e];if(Array.isArray(t)){let e=u?t.length-1:0;t=t[e]}null!==t&&(n[e]=t)}for(let t in e)n[t]=e[t]}}}return n}(i,s,n,e),renderState:t()}})(e,t,s,n);return i?o():(0,r3.M)(o)};function r6(e,t,i){let{style:s}=e,r={};for(let n in s)(B(s[n])||t.style&&B(t.style[n])||rF(n,e)||i?.getValue(n)?.liveStyle!==void 0)&&(r[n]=s[n]);return r}let r7={useVisualState:r4({scrapeMotionValuesFromProps:r6,createRenderState:rK})};function r8(e,t,i){let s=r6(e,t,i);for(let i in e)(B(e[i])||B(t[i]))&&(s[-1!==y.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=e[i]);return s}let r9={useVisualState:r4({scrapeMotionValuesFromProps:r8,createRenderState:rX})},ne=e=>t=>t.test(e),nt=[Z,eh,el,eo,ec,ed,{test:e=>"auto"===e,parse:e=>e}],ni=e=>nt.find(ne(e)),ns=e=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(e),nr=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,nn=e=>/^0[^.\s]+$/u.test(e),na=new Set(["brightness","contrast","saturate","opacity"]);function no(e){let[t,i]=e.slice(0,-1).split("(");if("drop-shadow"===t)return e;let[s]=i.match(J)||[];if(!s)return e;let r=i.replace(s,""),n=+!!na.has(t);return s!==i&&(n*=100),t+"("+n+r+")"}let nl=/\b([a-z-]*)\(.*?\)/gu,nh={...e_,getAnimatableNone:e=>{let t=e.match(nl);return t?t.map(no).join(" "):e}},nd={...rH,color:ef,backgroundColor:ef,outlineColor:ef,fill:ef,stroke:ef,borderColor:ef,borderTopColor:ef,borderRightColor:ef,borderBottomColor:ef,borderLeftColor:ef,filter:nh,WebkitFilter:nh},nc=e=>nd[e];function nu(e,t){let i=nc(e);return i!==nh&&(i=e_),i.getAnimatableNone?i.getAnimatableNone(t):void 0}let np=new Set(["auto","none","0"]);class nf extends tT{constructor(e,t,i,s,r){super(e,t,i,s,r,!0)}readKeyframes(){let{unresolvedKeyframes:e,element:t,name:i}=this;if(!t||!t.current)return;super.readKeyframes();for(let i=0;i<e.length;i++){let s=e[i];if("string"==typeof s&&K(s=s.trim())){let r=function e(t,i,s=1){H(s<=4,`Max CSS variable fallback depth detected in property "${t}". This may indicate a circular fallback dependency.`);let[r,n]=function(e){let t=nr.exec(e);if(!t)return[,];let[,i,s,r]=t;return[`--${i??s}`,r]}(t);if(!r)return;let a=window.getComputedStyle(i).getPropertyValue(r);if(a){let e=a.trim();return ns(e)?parseFloat(e):e}return K(n)?e(n,i,s+1):n}(s,t.current);void 0!==r&&(e[i]=r),i===e.length-1&&(this.finalKeyframe=s)}}if(this.resolveNoneKeyframes(),!w.has(i)||2!==e.length)return;let[s,r]=e,n=ni(s),a=ni(r);if(n!==a)if(tC(n)&&tC(a))for(let t=0;t<e.length;t++){let i=e[t];"string"==typeof i&&(e[t]=parseFloat(i))}else tO[i]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:e,name:t}=this,i=[];for(let t=0;t<e.length;t++){var s;(null===e[t]||("number"==typeof(s=e[t])?0===s:null===s||"none"===s||"0"===s||nn(s)))&&i.push(t)}i.length&&function(e,t,i){let s,r=0;for(;r<e.length&&!s;){let t=e[r];"string"==typeof t&&!np.has(t)&&ev(t).values.length&&(s=e[r]),r++}if(s&&i)for(let r of t)e[r]=nu(i,s)}(e,i,t)}measureInitialState(){let{element:e,unresolvedKeyframes:t,name:i}=this;if(!e||!e.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=tO[i](e.measureViewportBox(),window.getComputedStyle(e.current)),t[0]=this.measuredOrigin;let s=t[t.length-1];void 0!==s&&e.getValue(i,s).jump(s,!1)}measureEndState(){let{element:e,name:t,unresolvedKeyframes:i}=this;if(!e||!e.current)return;let s=e.getValue(t);s&&s.jump(this.measuredOrigin,!1);let r=i.length-1,n=i[r];i[r]=tO[t](e.measureViewportBox(),window.getComputedStyle(e.current)),null!==n&&void 0===this.finalKeyframe&&(this.finalKeyframe=n),this.removedTransforms?.length&&this.removedTransforms.forEach(([t,i])=>{e.getValue(t).set(i)}),this.resolveNoneKeyframes()}}let nm=[...nt,ef,e_],nx=e=>nm.find(ne(e)),ng={current:null},ny={current:!1},nv=new WeakMap,nw=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class nj{scrapeMotionValuesFromProps(e,t,i){return{}}constructor({parent:e,props:t,presenceContext:i,reducedMotionConfig:s,blockInitialAnimation:r,visualState:n},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=tT,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let e=C.now();this.renderScheduledAt<e&&(this.renderScheduledAt=e,f.render(this.render,!1,!0))};let{latestValues:o,renderState:l}=n;this.latestValues=o,this.baseTarget={...o},this.initialValues=t.initial?{...o}:{},this.renderState=l,this.parent=e,this.props=t,this.presenceContext=i,this.depth=e?e.depth+1:0,this.reducedMotionConfig=s,this.options=a,this.blockInitialAnimation=!!r,this.isControllingVariants=rA(t),this.isVariantNode=rB(t),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);let{willChange:h,...d}=this.scrapeMotionValuesFromProps(t,{},this);for(let e in d){let t=d[e];void 0!==o[e]&&B(t)&&t.set(o[e],!1)}}mount(e){this.current=e,nv.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((e,t)=>this.bindToMotionValue(t,e)),ny.current||function(){if(ny.current=!0,rL.B)if(window.matchMedia){let e=window.matchMedia("(prefers-reduced-motion)"),t=()=>ng.current=e.matches;e.addListener(t),t()}else ng.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||ng.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let e in this.projection&&this.projection.unmount(),m(this.notifyUpdate),m(this.render),this.valueSubscriptions.forEach(e=>e()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[e].clear();for(let e in this.features){let t=this.features[e];t&&(t.unmount(),t.isMounted=!1)}this.current=null}bindToMotionValue(e,t){let i;this.valueSubscriptions.has(e)&&this.valueSubscriptions.get(e)();let s=v.has(e);s&&this.onBindTransform&&this.onBindTransform();let r=t.on("change",t=>{this.latestValues[e]=t,this.props.onUpdate&&f.preRender(this.notifyUpdate),s&&this.projection&&(this.projection.isTransformDirty=!0)}),n=t.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,e,t)),this.valueSubscriptions.set(e,()=>{r(),n(),i&&i(),t.owner&&t.stop()})}sortNodePosition(e){return this.current&&this.sortInstanceNodePosition&&this.type===e.type?this.sortInstanceNodePosition(this.current,e.current):0}updateFeatures(){let e="animation";for(e in rT){let t=rT[e];if(!t)continue;let{isEnabled:i,Feature:s}=t;if(!this.features[e]&&s&&i(this.props)&&(this.features[e]=new s(this)),this.features[e]){let t=this.features[e];t.isMounted?t.update():(t.mount(),t.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):iB()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,t){this.latestValues[e]=t}update(e,t){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=t;for(let t=0;t<nw.length;t++){let i=nw[t];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let s=e["on"+i];s&&(this.propEventSubscriptions[i]=this.on(i,s))}this.prevMotionValues=function(e,t,i){for(let s in t){let r=t[s],n=i[s];if(B(r))e.addValue(s,r);else if(B(n))e.addValue(s,V(r,{owner:e}));else if(n!==r)if(e.hasValue(s)){let t=e.getValue(s);!0===t.liveStyle?t.jump(r):t.hasAnimated||t.set(r)}else{let t=e.getStaticValue(s);e.addValue(s,V(void 0!==t?t:r,{owner:e}))}}for(let s in i)void 0===t[s]&&e.removeValue(s);return t}(this,this.scrapeMotionValuesFromProps(e,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(e){let t=this.getClosestVariantNode();if(t)return t.variantChildren&&t.variantChildren.add(e),()=>t.variantChildren.delete(e)}addValue(e,t){let i=this.values.get(e);t!==i&&(i&&this.removeValue(e),this.bindToMotionValue(e,t),this.values.set(e,t),this.latestValues[e]=t.get())}removeValue(e){this.values.delete(e);let t=this.valueSubscriptions.get(e);t&&(t(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,t){if(this.props.values&&this.props.values[e])return this.props.values[e];let i=this.values.get(e);return void 0===i&&void 0!==t&&(i=V(null===t?void 0:t,{owner:this}),this.addValue(e,i)),i}readValue(e,t){let i=void 0===this.latestValues[e]&&this.current?this.getBaseTargetFromProps(this.props,e)??this.readValueFromInstance(this.current,e,this.options):this.latestValues[e];return null!=i&&("string"==typeof i&&(ns(i)||nn(i))?i=parseFloat(i):!nx(i)&&e_.test(t)&&(i=nu(e,t)),this.setBaseTarget(e,B(i)?i.get():i)),B(i)?i.get():i}setBaseTarget(e,t){this.baseTarget[e]=t}getBaseTarget(e){let t,{initial:i}=this.props;if("string"==typeof i||"object"==typeof i){let s=a(this.props,i,this.presenceContext?.custom);s&&(t=s[e])}if(i&&void 0!==t)return t;let s=this.getBaseTargetFromProps(this.props,e);return void 0===s||B(s)?void 0!==this.initialValues[e]&&void 0===t?void 0:this.baseTarget[e]:s}on(e,t){return this.events[e]||(this.events[e]=new _),this.events[e].add(t)}notify(e,...t){this.events[e]&&this.events[e].notify(...t)}}class nb extends nj{constructor(){super(...arguments),this.KeyframeResolver=nf}sortInstanceNodePosition(e,t){return 2&e.compareDocumentPosition(t)?1:-1}getBaseTargetFromProps(e,t){return e.style?e.style[t]:void 0}removeValueFromRenderState(e,{vars:t,style:i}){delete t[e],delete i[e]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:e}=this.props;B(e)&&(this.childSubscription=e.on("change",e=>{this.current&&(this.current.textContent=`${e}`)}))}}function n_(e,{style:t,vars:i},s,r){for(let n in Object.assign(e.style,t,r&&r.getProjectionStyles(s)),i)e.style.setProperty(n,i[n])}class nk extends nb{constructor(){super(...arguments),this.type="html",this.renderInstance=n_}readValueFromInstance(e,t){if(v.has(t))return this.projection?.isProjecting?tj(t):t_(e,t);{let i=window.getComputedStyle(e),s=(z(t)?i.getPropertyValue(t):i[t])||0;return"string"==typeof s?s.trim():s}}measureInstanceViewportBox(e,{transformPagePoint:t}){return iG(e,t)}build(e,t,i){rq(e,t,i.transformTemplate)}scrapeMotionValuesFromProps(e,t,i){return r6(e,t,i)}}let nC=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class nS extends nb{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=iB}getBaseTargetFromProps(e,t){return e[t]}readValueFromInstance(e,t){if(v.has(t)){let e=nc(t);return e&&e.default||0}return t=nC.has(t)?t:L(t),e.getAttribute(t)}scrapeMotionValuesFromProps(e,t,i){return r8(e,t,i)}build(e,t,i){rY(e,t,this.isSVGTag,i.transformTemplate,i.style)}renderInstance(e,t,i,s){for(let i in n_(e,t,void 0,s),t.attrs)e.setAttribute(nC.has(i)?i:L(i),t.attrs[i])}mount(e){this.isSVGTag=rJ(e.tagName),super.mount(e)}}let nM=function(e){if("undefined"==typeof Proxy)return e;let t=new Map;return new Proxy((...t)=>e(...t),{get:(i,s)=>"create"===s?e:(t.has(s)||t.set(s,e(s)),t.get(s))})}((tZ={animation:{Feature:ic},exit:{Feature:ip},inView:{Feature:rS},tap:{Feature:rw},focus:{Feature:rc},hover:{Feature:rd},pan:{Feature:i7},drag:{Feature:i4,ProjectionNode:ra,MeasureLayout:sh},layout:{ProjectionNode:ra,MeasureLayout:sh}},t$=(e,t)=>r5(e)?new nS(t):new nk(t,{allowProjection:e!==se.Fragment}),function(e,{forwardMotionProps:t}={forwardMotionProps:!1}){return function({preloadedFeatures:e,createVisualElement:t,useRender:i,useVisualState:s,Component:r}){function n(e,n){var a,o,l;let h,d={...(0,se.useContext)(rO.Q),...e,layoutId:function({layoutId:e}){let t=(0,se.useContext)(si.L).id;return t&&void 0!==e?t+"-"+e:e}(e)},{isStatic:c}=d,u=function(e){let{initial:t,animate:i}=function(e,t){if(rA(e)){let{initial:t,animate:i}=e;return{initial:!1===t||it(t)?t:void 0,animate:it(i)?i:void 0}}return!1!==e.inherit?t:{}}(e,(0,se.useContext)(rV));return(0,se.useMemo)(()=>({initial:t,animate:i}),[rP(t),rP(i)])}(e),p=s(e,c);if(!c&&rL.B){o=0,l=0,(0,se.useContext)(rM).strict;let e=function(e){let{drag:t,layout:i}=rT;if(!t&&!i)return{};let s={...t,...i};return{MeasureLayout:t?.isEnabled(e)||i?.isEnabled(e)?s.MeasureLayout:void 0,ProjectionNode:s.ProjectionNode}}(d);h=e.MeasureLayout,u.visualElement=function(e,t,i,s,r){let{visualElement:n}=(0,se.useContext)(rV),a=(0,se.useContext)(rM),o=(0,se.useContext)(rR.t),l=(0,se.useContext)(rO.Q).reducedMotion,h=(0,se.useRef)(null);s=s||a.renderer,!h.current&&s&&(h.current=s(e,{visualState:t,parent:n,props:i,presenceContext:o,blockInitialAnimation:!!o&&!1===o.initial,reducedMotionConfig:l}));let d=h.current,c=(0,se.useContext)(ss);d&&!d.projection&&r&&("html"===d.type||"svg"===d.type)&&function(e,t,i,s){let{layoutId:r,layout:n,drag:a,dragConstraints:o,layoutScroll:l,layoutRoot:h,layoutCrossfade:d}=t;e.projection=new i(e.latestValues,t["data-framer-portal-id"]?void 0:function e(t){if(t)return!1!==t.options.allowProjection?t.projection:e(t.parent)}(e.parent)),e.projection.setOptions({layoutId:r,layout:n,alwaysMeasureLayout:!!a||o&&iq(o),visualElement:e,animationType:"string"==typeof n?n:"both",initialPromotionConfig:s,crossfade:d,layoutScroll:l,layoutRoot:h})}(h.current,i,r,c);let u=(0,se.useRef)(!1);(0,se.useInsertionEffect)(()=>{d&&u.current&&d.update(i,o)});let p=i[N],f=(0,se.useRef)(!!p&&!window.MotionHandoffIsComplete?.(p)&&window.MotionHasOptimisedAnimation?.(p));return(0,rI.E)(()=>{d&&(u.current=!0,window.MotionIsMounted=!0,d.updateFeatures(),i9.render(d.render),f.current&&d.animationState&&d.animationState.animateChanges())}),(0,se.useEffect)(()=>{d&&(!f.current&&d.animationState&&d.animationState.animateChanges(),f.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(p)}),f.current=!1))}),d}(r,p,d,t,e.ProjectionNode)}return(0,i8.jsxs)(rV.Provider,{value:u,children:[h&&u.visualElement?(0,i8.jsx)(h,{visualElement:u.visualElement,...d}):null,i(r,e,(a=u.visualElement,(0,se.useCallback)(e=>{e&&p.onMount&&p.onMount(e),a&&(e?a.mount(e):a.unmount()),n&&("function"==typeof n?n(e):iq(n)&&(n.current=e))},[a])),p,c,u.visualElement)]})}e&&function(e){for(let t in e)rT[t]={...rT[t],...e[t]}}(e),n.displayName=`motion.${"string"==typeof r?r:`create(${r.displayName??r.name??""})`}`;let a=(0,se.forwardRef)(n);return a[rE]=r,a}({...r5(e)?r9:r7,preloadedFeatures:tZ,useRender:function(e=!1){return(t,i,s,{latestValues:r},n)=>{let a=(r5(t)?function(e,t,i,s){let r=(0,se.useMemo)(()=>{let i=rX();return rY(i,t,rJ(s),e.transformTemplate,e.style),{...i.attrs,style:{...i.style}}},[t]);if(e.style){let t={};rW(t,e.style,e),r.style={...t,...r.style}}return r}:function(e,t){let i={},s=function(e,t){let i=e.style||{},s={};return rW(s,i,e),Object.assign(s,function({transformTemplate:e},t){return(0,se.useMemo)(()=>{let i=rK();return rq(i,t,e),Object.assign({},i.vars,i.style)},[t])}(e,t)),s}(e,t);return e.drag&&!1!==e.dragListener&&(i.draggable=!1,s.userSelect=s.WebkitUserSelect=s.WebkitTouchCallout="none",s.touchAction=!0===e.drag?"none":`pan-${"x"===e.drag?"y":"x"}`),void 0===e.tabIndex&&(e.onTap||e.onTapStart||e.whileTap)&&(i.tabIndex=0),i.style=s,i})(i,r,n,t),o=function(e,t,i){let s={};for(let r in e)("values"!==r||"object"!=typeof e.values)&&(r1(r)||!0===i&&r0(r)||!t&&!r0(r)||e.draggable&&r.startsWith("onDrag"))&&(s[r]=e[r]);return s}(i,"string"==typeof t,e),l=t!==se.Fragment?{...o,...a,ref:s}:{},{children:h}=i,d=(0,se.useMemo)(()=>B(h)?h.get():h,[h]);return(0,se.createElement)(t,{...l,children:d})}}(t),createVisualElement:t$,Component:e})}))},73024:e=>{"use strict";e.exports=require("node:fs")},73566:e=>{"use strict";e.exports=require("worker_threads")},74075:e=>{"use strict";e.exports=require("zlib")},74998:e=>{"use strict";e.exports=require("perf_hooks")},75603:(e,t,i)=>{"use strict";i.d(t,{CodeExamples:()=>s});let s=(0,i(6340).registerClientReference)(function(){throw Error("Attempted to call CodeExamples() from the server but CodeExamples is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubentweb\\cubent\\apps\\web\\app\\[locale]\\(home)\\code-examples.tsx","CodeExamples")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76760:e=>{"use strict";e.exports=require("node:path")},76842:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>s});let s={src:"/_next/static/media/mainboard.9232efd0.svg",height:546,width:1512,blurWidth:0,blurHeight:0}},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},80481:e=>{"use strict";e.exports=require("node:readline")},81300:(e,t,i)=>{"use strict";i.d(t,{E:()=>r});var s=i(35371);let r=i(34500).B?s.useLayoutEffect:s.useEffect},81700:(e,t,i)=>{"use strict";i.d(t,{A:()=>s});let s=(0,i(19161).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},82049:(e,t)=>{"use strict";function i(e){let{widthInt:t,heightInt:i,blurWidth:s,blurHeight:r,blurDataURL:n,objectFit:a}=e,o=s?40*s:t,l=r?40*r:i,h=o&&l?"viewBox='0 0 "+o+" "+l+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+h+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(h?"none":"contain"===a?"xMidYMid":"cover"===a?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+n+"'/%3E%3C/svg%3E"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImageBlurSvg",{enumerable:!0,get:function(){return i}})},83528:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{default:function(){return l},getImageProps:function(){return o}});let s=i(32446),r=i(26634),n=i(2150),a=s._(i(85692));function o(e){let{props:t}=(0,r.getImgProps)(e,{defaultLoader:a.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,i]of Object.entries(t))void 0===i&&delete t[e];return{props:t}}let l=n.Image},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},85692:(e,t)=>{"use strict";function i(e){var t;let{config:i,src:s,width:r,quality:n}=e,a=n||(null==(t=i.qualities)?void 0:t.reduce((e,t)=>Math.abs(t-75)<Math.abs(e-75)?t:e))||75;return i.path+"?url="+encodeURIComponent(s)+"&w="+r+"&q="+a+(s.startsWith("/_next/static/media/"),"")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return s}}),i.__next_img_default=!0;let s=i},86592:e=>{"use strict";e.exports=require("node:inspector")},89319:(e,t,i)=>{Promise.resolve().then(i.bind(i,75603)),Promise.resolve().then(i.bind(i,16156)),Promise.resolve().then(i.bind(i,39281)),Promise.resolve().then(i.bind(i,2391)),Promise.resolve().then(i.bind(i,52912)),Promise.resolve().then(i.bind(i,58665)),Promise.resolve().then(i.bind(i,26993)),Promise.resolve().then(i.bind(i,3922)),Promise.resolve().then(i.bind(i,76842)),Promise.resolve().then(i.bind(i,24761)),Promise.resolve().then(i.t.bind(i,49499,23)),Promise.resolve().then(i.t.bind(i,2150,23))},90043:(e,t,i)=>{"use strict";function s(e){return"object"==typeof e&&null!==e}i.d(t,{G:()=>s})},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var i=e=>t(t.s=e),s=t.X(0,[5319,3396,415,2644,8990,6239,1121,7170,9752,4788],()=>i(64426));module.exports=s})();