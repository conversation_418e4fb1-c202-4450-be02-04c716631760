"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[71],{9071:(t,e,n)=>{n.r(e),n.d(e,{default:()=>i});let i=(0,n(82920).P)({"../../node_modules/.pnpm/pusher-js@8.4.0/node_modules/pusher-js/dist/web/pusher.js"(t,e){var i,r;i=window,r=function(){var t=[function(t,e,n){var i,r=this&&this.__extends||(i=function(t,e){return(i=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}i(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)});Object.defineProperty(e,"__esModule",{value:!0});var s=function(){function t(t){void 0===t&&(t="="),this._paddingCharacter=t}return t.prototype.encodedLength=function(t){return this._paddingCharacter?(t+2)/3*4|0:(8*t+5)/6|0},t.prototype.encode=function(t){for(var e="",n=0;n<t.length-2;n+=3){var i=t[n]<<16|t[n+1]<<8|t[n+2];e+=this._encodeByte(i>>>18&63),e+=this._encodeByte(i>>>12&63),e+=this._encodeByte(i>>>6&63),e+=this._encodeByte(i>>>0&63)}var r=t.length-n;if(r>0){var i=t[n]<<16|(2===r?t[n+1]<<8:0);e+=this._encodeByte(i>>>18&63),e+=this._encodeByte(i>>>12&63),2===r?e+=this._encodeByte(i>>>6&63):e+=this._paddingCharacter||"",e+=this._paddingCharacter||""}return e},t.prototype.maxDecodedLength=function(t){return this._paddingCharacter?t/4*3|0:(6*t+7)/8|0},t.prototype.decodedLength=function(t){return this.maxDecodedLength(t.length-this._getPaddingLength(t))},t.prototype.decode=function(t){if(0===t.length)return new Uint8Array(0);for(var e=this._getPaddingLength(t),n=t.length-e,i=new Uint8Array(this.maxDecodedLength(n)),r=0,s=0,o=0,a=0,c=0,h=0,u=0;s<n-4;s+=4)a=this._decodeChar(t.charCodeAt(s+0)),c=this._decodeChar(t.charCodeAt(s+1)),h=this._decodeChar(t.charCodeAt(s+2)),u=this._decodeChar(t.charCodeAt(s+3)),i[r++]=a<<2|c>>>4,i[r++]=c<<4|h>>>2,i[r++]=h<<6|u,o|=256&a,o|=256&c,o|=256&h,o|=256&u;if(s<n-1&&(a=this._decodeChar(t.charCodeAt(s)),c=this._decodeChar(t.charCodeAt(s+1)),i[r++]=a<<2|c>>>4,o|=256&a,o|=256&c),s<n-2&&(h=this._decodeChar(t.charCodeAt(s+2)),i[r++]=c<<4|h>>>2,o|=256&h),s<n-3&&(u=this._decodeChar(t.charCodeAt(s+3)),i[r++]=h<<6|u,o|=256&u),0!==o)throw Error("Base64Coder: incorrect characters for decoding");return i},t.prototype._encodeByte=function(t){var e=t;return e+=65,e+=25-t>>>8&6,e+=51-t>>>8&-75,e+=61-t>>>8&-15,String.fromCharCode(e+=62-t>>>8&3)},t.prototype._decodeChar=function(t){var e;return 256+((42-t&t-44)>>>8&-256+t-43+62)+((46-t&t-48)>>>8&-256+t-47+63)+((47-t&t-58)>>>8&-256+t-48+52)+((64-t&t-91)>>>8&-256+t-65+0)+((96-t&t-123)>>>8&-256+t-97+26)},t.prototype._getPaddingLength=function(t){var e=0;if(this._paddingCharacter){for(var n=t.length-1;n>=0&&t[n]===this._paddingCharacter;n--)e++;if(t.length<4||e>2)throw Error("Base64Coder: incorrect padding")}return e},t}();e.Coder=s;var o=new s;e.encode=function(t){return o.encode(t)},e.decode=function(t){return o.decode(t)};var a=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return r(e,t),e.prototype._encodeByte=function(t){var e=t;return e+=65,e+=25-t>>>8&6,e+=51-t>>>8&-75,e+=61-t>>>8&-13,String.fromCharCode(e+=62-t>>>8&49)},e.prototype._decodeChar=function(t){var e;return 256+((44-t&t-46)>>>8&-256+t-45+62)+((94-t&t-96)>>>8&-256+t-95+63)+((47-t&t-58)>>>8&-256+t-48+52)+((64-t&t-91)>>>8&-256+t-65+0)+((96-t&t-123)>>>8&-256+t-97+26)},e}(s);e.URLSafeCoder=a;var c=new a;e.encodeURLSafe=function(t){return c.encode(t)},e.decodeURLSafe=function(t){return c.decode(t)},e.encodedLength=function(t){return o.encodedLength(t)},e.maxDecodedLength=function(t){return o.maxDecodedLength(t)},e.decodedLength=function(t){return o.decodedLength(t)}},function(t,e,n){Object.defineProperty(e,"__esModule",{value:!0});var i="utf8: invalid string",r="utf8: invalid source encoding";function s(t){for(var e=0,n=0;n<t.length;n++){var r=t.charCodeAt(n);if(r<128)e+=1;else if(r<2048)e+=2;else if(r<55296)e+=3;else if(r<=57343){if(n>=t.length-1)throw Error(i);n++,e+=4}else throw Error(i)}return e}e.encode=function(t){for(var e=new Uint8Array(s(t)),n=0,i=0;i<t.length;i++){var r=t.charCodeAt(i);r<128?e[n++]=r:(r<2048?e[n++]=192|r>>6:(r<55296?e[n++]=224|r>>12:(i++,r=((1023&r)<<10|1023&t.charCodeAt(i))+65536,e[n++]=240|r>>18,e[n++]=128|r>>12&63),e[n++]=128|r>>6&63),e[n++]=128|63&r)}return e},e.encodedLength=s,e.decode=function(t){for(var e=[],n=0;n<t.length;n++){var i=t[n];if(128&i){var s=void 0;if(i<224){if(n>=t.length)throw Error(r);var o=t[++n];if((192&o)!=128)throw Error(r);i=(31&i)<<6|63&o,s=128}else if(i<240){if(n>=t.length-1)throw Error(r);var o=t[++n],a=t[++n];if((192&o)!=128||(192&a)!=128)throw Error(r);i=(15&i)<<12|(63&o)<<6|63&a,s=2048}else if(i<248){if(n>=t.length-2)throw Error(r);var o=t[++n],a=t[++n],c=t[++n];if((192&o)!=128||(192&a)!=128||(192&c)!=128)throw Error(r);i=(15&i)<<18|(63&o)<<12|(63&a)<<6|63&c,s=65536}else throw Error(r);if(i<s||i>=55296&&i<=57343)throw Error(r);if(i>=65536){if(i>1114111)throw Error(r);i-=65536,e.push(String.fromCharCode(55296|i>>10)),i=56320|1023&i}}e.push(String.fromCharCode(i))}return e.join("")}},function(t,e,n){t.exports=n(3).default},function(t,e,n){n.r(e);class i{create(t){this.lastId++;var e=this.lastId,n=this.prefix+e,i=this.name+"["+e+"]",r=!1,s=function(){r||(t.apply(null,arguments),r=!0)};return this[e]=s,{number:e,id:n,name:i,callback:s}}remove(t){delete this[t.number]}constructor(t,e){this.lastId=0,this.prefix=t,this.name=e}}var r,s,o,a,c,h,u=new i("_pusher_script_","Pusher.ScriptReceivers"),l={VERSION:"8.4.0",PROTOCOL:7,wsPort:80,wssPort:443,wsPath:"",httpHost:"sockjs.pusher.com",httpPort:80,httpsPort:443,httpPath:"/pusher",stats_host:"stats.pusher.com",authEndpoint:"/pusher/auth",authTransport:"ajax",activityTimeout:12e4,pongTimeout:3e4,unavailableTimeout:1e4,userAuthentication:{endpoint:"/pusher/user-auth",transport:"ajax"},channelAuthorization:{endpoint:"/pusher/auth",transport:"ajax"},cdn_http:"http://js.pusher.com",cdn_https:"https://js.pusher.com",dependency_suffix:""};class d{load(t,e,n){var i=this;if(i.loading[t]&&i.loading[t].length>0)i.loading[t].push(n);else{i.loading[t]=[n];var r=t4.createScriptRequest(i.getPath(t,e)),s=i.receivers.create(function(e){if(i.receivers.remove(s),i.loading[t]){var n=i.loading[t];delete i.loading[t];for(var o=function(t){t||r.cleanup()},a=0;a<n.length;a++)n[a](e,o)}});r.send(s)}}getRoot(t){var e,n=t4.getDocument().location.protocol;return(t&&t.useTLS||"https:"===n?this.options.cdn_https:this.options.cdn_http).replace(/\/*$/,"")+"/"+this.options.version}getPath(t,e){return this.getRoot(e)+"/"+t+this.options.suffix+".js"}constructor(t){this.options=t,this.receivers=t.receivers||u,this.loading={}}}var p=new i("_pusher_dependencies","Pusher.DependenciesReceivers"),f=new d({cdn_http:l.cdn_http,cdn_https:l.cdn_https,version:l.VERSION,suffix:l.dependency_suffix,receivers:p});let g={baseUrl:"https://pusher.com",urls:{authenticationEndpoint:{path:"/docs/channels/server_api/authenticating_users"},authorizationEndpoint:{path:"/docs/channels/server_api/authorizing-users/"},javascriptQuickStart:{path:"/docs/javascript_quick_start"},triggeringClientEvents:{path:"/docs/client_api_guide/client_events#trigger-events"},encryptedChannelSupport:{fullUrl:"https://github.com/pusher/pusher-js/tree/cc491015371a4bde5743d1c87a0fbac0feb53195#encrypted-channel-support"}}};var b={buildLogSuffix:function(t){let e,n=g.urls[t];return n&&(n.fullUrl?e=n.fullUrl:n.path&&(e=g.baseUrl+n.path),e)?"".concat("See:"," ").concat(e):""}};(r=a||(a={})).UserAuthentication="user-authentication",r.ChannelAuthorization="channel-authorization";class v extends Error{constructor(t){super(t),Object.setPrototypeOf(this,new.target.prototype)}}class m extends Error{constructor(t){super(t),Object.setPrototypeOf(this,new.target.prototype)}}class y extends Error{constructor(t){super(t),Object.setPrototypeOf(this,new.target.prototype)}}class w extends Error{constructor(t){super(t),Object.setPrototypeOf(this,new.target.prototype)}}class S extends Error{constructor(t){super(t),Object.setPrototypeOf(this,new.target.prototype)}}class _ extends Error{constructor(t){super(t),Object.setPrototypeOf(this,new.target.prototype)}}class k extends Error{constructor(t){super(t),Object.setPrototypeOf(this,new.target.prototype)}}class C extends Error{constructor(t){super(t),Object.setPrototypeOf(this,new.target.prototype)}}class T extends Error{constructor(t,e){super(e),this.status=t,Object.setPrototypeOf(this,new.target.prototype)}}for(var P=function(t,e,n,i,r){let s=t4.createXHR();for(var o in s.open("POST",n.endpoint,!0),s.setRequestHeader("Content-Type","application/x-www-form-urlencoded"),n.headers)s.setRequestHeader(o,n.headers[o]);if(null!=n.headersProvider){let t=n.headersProvider();for(var o in t)s.setRequestHeader(o,t[o])}return s.onreadystatechange=function(){if(4===s.readyState)if(200===s.status){let t,e=!1;try{t=JSON.parse(s.responseText),e=!0}catch(t){r(new T(200,"JSON returned from ".concat(i.toString()," endpoint was invalid, yet status code was 200. Data was: ").concat(s.responseText)),null)}e&&r(null,t)}else{let t="";switch(i){case a.UserAuthentication:t=b.buildLogSuffix("authenticationEndpoint");break;case a.ChannelAuthorization:t="Clients must be authorized to join private or presence channels. ".concat(b.buildLogSuffix("authorizationEndpoint"))}r(new T(s.status,"Unable to retrieve auth string from ".concat(i.toString()," endpoint - received status: ").concat(s.status," from ").concat(n.endpoint,". ").concat(t)),null)}},s.send(e),s},E=String.fromCharCode,x="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",O={},L=0,A=x.length;L<A;L++)O[x.charAt(L)]=L;var R=function(t){var e=t.charCodeAt(0);return e<128?t:e<2048?E(192|e>>>6)+E(128|63&e):E(224|e>>>12&15)+E(128|e>>>6&63)+E(128|63&e)},I=function(t){var e=[0,2,1][t.length%3],n=t.charCodeAt(0)<<16|(t.length>1?t.charCodeAt(1):0)<<8|(t.length>2?t.charCodeAt(2):0);return[x.charAt(n>>>18),x.charAt(n>>>12&63),e>=2?"=":x.charAt(n>>>6&63),e>=1?"=":x.charAt(63&n)].join("")},D=window.btoa||function(t){return t.replace(/[\s\S]{1,3}/g,I)};class j{isRunning(){return null!==this.timer}ensureAborted(){this.timer&&(this.clear(this.timer),this.timer=null)}constructor(t,e,n,i){this.clear=e,this.timer=t(()=>{this.timer&&(this.timer=i(this.timer))},n)}}var N=j;function H(t){window.clearTimeout(t)}function U(t){window.clearInterval(t)}class M extends N{constructor(t,e){super(setTimeout,H,t,function(t){return e(),null})}}class z extends N{constructor(t,e){super(setInterval,U,t,function(t){return e(),t})}}var q={now:()=>Date.now?Date.now():new Date().valueOf(),defer:t=>new M(0,t),method(t){for(var e=arguments.length,n=Array(e>1?e-1:0),i=1;i<e;i++)n[i-1]=arguments[i];var r=Array.prototype.slice.call(arguments,1);return function(e){return e[t].apply(e,r.concat(arguments))}}};function B(t){for(var e=arguments.length,n=Array(e>1?e-1:0),i=1;i<e;i++)n[i-1]=arguments[i];for(var r=0;r<n.length;r++){var s=n[r];for(var o in s)s[o]&&s[o].constructor&&s[o].constructor===Object?t[o]=B(t[o]||{},s[o]):t[o]=s[o]}return t}function F(){for(var t=["Pusher"],e=0;e<arguments.length;e++)"string"==typeof arguments[e]?t.push(arguments[e]):t.push(K(arguments[e]));return t.join(" : ")}function X(t,e){var n=Array.prototype.indexOf;if(null===t)return -1;if(n&&t.indexOf===n)return t.indexOf(e);for(var i=0,r=t.length;i<r;i++)if(t[i]===e)return i;return -1}function J(t,e){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&e(t[n],n,t)}function W(t){var e=[];return J(t,function(t,n){e.push(n)}),e}function G(t,e,n){for(var i=0;i<t.length;i++)e.call(n||window,t[i],i,t)}function V(t,e){for(var n=[],i=0;i<t.length;i++)n.push(e(t[i],i,t,n));return n}function Y(t,e){e=e||function(t){return!!t};for(var n=[],i=0;i<t.length;i++)e(t[i],i,t,n)&&n.push(t[i]);return n}function $(t,e){var n={};return J(t,function(i,r){(e&&e(i,r,t,n)||i)&&(n[r]=i)}),n}function Q(t,e){for(var n=0;n<t.length;n++)if(e(t[n],n,t))return!0;return!1}function K(t){try{return JSON.stringify(t)}catch(i){var e,n;return JSON.stringify((e=[],n=[],function t(i,r){var s,o,a;switch(typeof i){case"object":if(!i)return null;for(s=0;s<e.length;s+=1)if(e[s]===i)return{$ref:n[s]};if(e.push(i),n.push(r),"[object Array]"===Object.prototype.toString.apply(i))for(s=0,a=[];s<i.length;s+=1)a[s]=t(i[s],r+"["+s+"]");else for(o in a={},i)Object.prototype.hasOwnProperty.call(i,o)&&(a[o]=t(i[o],r+"["+JSON.stringify(o)+"]"));return a;case"number":case"string":case"boolean":return i}}(t,"$")))}}class Z{debug(){for(var t=arguments.length,e=Array(t),n=0;n<t;n++)e[n]=arguments[n];this.log(this.globalLog,e)}warn(){for(var t=arguments.length,e=Array(t),n=0;n<t;n++)e[n]=arguments[n];this.log(this.globalLogWarn,e)}error(){for(var t=arguments.length,e=Array(t),n=0;n<t;n++)e[n]=arguments[n];this.log(this.globalLogError,e)}globalLogWarn(t){window.console&&window.console.warn?window.console.warn(t):this.globalLog(t)}globalLogError(t){window.console&&window.console.error?window.console.error(t):this.globalLogWarn(t)}log(t){for(var e=arguments.length,n=Array(e>1?e-1:0),i=1;i<e;i++)n[i-1]=arguments[i];var r=F.apply(this,arguments);ed.log?ed.log(r):ed.logToConsole&&t.bind(this)(r)}constructor(){this.globalLog=t=>{window.console&&window.console.log&&window.console.log(t)}}}var tt=new Z,te=function(t,e,n,i,r){(void 0!==n.headers||null!=n.headersProvider)&&tt.warn("To send headers with the ".concat(i.toString()," request, you must use AJAX, rather than JSONP."));var s=t.nextAuthCallbackID.toString();t.nextAuthCallbackID++;var o=t.getDocument(),a=o.createElement("script");t.auth_callbacks[s]=function(t){r(null,t)},a.src=n.endpoint+"?callback="+encodeURIComponent("Pusher.auth_callbacks['"+s+"']")+"&"+e;var c=o.getElementsByTagName("head")[0]||o.documentElement;c.insertBefore(a,c.firstChild)};class tn{send(t){var e=this,n="Error loading "+e.src;e.script=document.createElement("script"),e.script.id=t.id,e.script.src=e.src,e.script.type="text/javascript",e.script.charset="UTF-8",e.script.addEventListener?(e.script.onerror=function(){t.callback(n)},e.script.onload=function(){t.callback(null)}):e.script.onreadystatechange=function(){("loaded"===e.script.readyState||"complete"===e.script.readyState)&&t.callback(null)},void 0===e.script.async&&document.attachEvent&&/opera/i.test(navigator.userAgent)?(e.errorScript=document.createElement("script"),e.errorScript.id=t.id+"_error",e.errorScript.text=t.name+"('"+n+"');",e.script.async=e.errorScript.async=!1):e.script.async=!0;var i=document.getElementsByTagName("head")[0];i.insertBefore(e.script,i.firstChild),e.errorScript&&i.insertBefore(e.errorScript,e.script.nextSibling)}cleanup(){this.script&&(this.script.onload=this.script.onerror=null,this.script.onreadystatechange=null),this.script&&this.script.parentNode&&this.script.parentNode.removeChild(this.script),this.errorScript&&this.errorScript.parentNode&&this.errorScript.parentNode.removeChild(this.errorScript),this.script=null,this.errorScript=null}constructor(t){this.src=t}}class ti{send(t){if(!this.request){var e,n,i,r,s=V((e=$(this.data,function(t){return void 0!==t}),n=function(t){return"object"==typeof t&&(t=K(t)),encodeURIComponent(D(t.toString().replace(/[^\x00-\x7F]/g,R)))},i={},J(e,function(t,e){i[e]=n(t)}),r=[],J(i,function(t,e){r.push([e,t])}),r),q.method("join","=")).join("&"),o=this.url+"/"+t.number+"?"+s;this.request=t4.createScriptRequest(o),this.request.send(t)}}cleanup(){this.request&&this.request.cleanup()}constructor(t,e){this.url=t,this.data=e}}function tr(t,e,n){return t+(e.useTLS?"s":"")+"://"+(e.useTLS?e.hostTLS:e.hostNonTLS)+n}function ts(t,e){return"/app/"+t+("?protocol="+l.PROTOCOL+"&client=js&version="+l.VERSION)+(e?"&"+e:"")}class to{get(t){return this._callbacks["_"+t]}add(t,e,n){var i="_"+t;this._callbacks[i]=this._callbacks[i]||[],this._callbacks[i].push({fn:e,context:n})}remove(t,e,n){if(!t&&!e&&!n){this._callbacks={};return}var i=t?["_"+t]:W(this._callbacks);e||n?this.removeCallback(i,e,n):this.removeAllCallbacks(i)}removeCallback(t,e,n){G(t,function(t){this._callbacks[t]=Y(this._callbacks[t]||[],function(t){return e&&e!==t.fn||n&&n!==t.context}),0===this._callbacks[t].length&&delete this._callbacks[t]},this)}removeAllCallbacks(t){G(t,function(t){delete this._callbacks[t]},this)}constructor(){this._callbacks={}}}class ta{bind(t,e,n){return this.callbacks.add(t,e,n),this}bind_global(t){return this.global_callbacks.push(t),this}unbind(t,e,n){return this.callbacks.remove(t,e,n),this}unbind_global(t){return t?this.global_callbacks=Y(this.global_callbacks||[],e=>e!==t):this.global_callbacks=[],this}unbind_all(){return this.unbind(),this.unbind_global(),this}emit(t,e,n){for(var i=0;i<this.global_callbacks.length;i++)this.global_callbacks[i](t,e);var r=this.callbacks.get(t),s=[];if(n?s.push(e,n):e&&s.push(e),r&&r.length>0)for(var i=0;i<r.length;i++)r[i].fn.apply(r[i].context||window,s);else this.failThrough&&this.failThrough(t,e);return this}constructor(t){this.callbacks=new to,this.global_callbacks=[],this.failThrough=t}}class tc extends ta{handlesActivityChecks(){return!!this.hooks.handlesActivityChecks}supportsPing(){return!!this.hooks.supportsPing}connect(){if(this.socket||"initialized"!==this.state)return!1;var t=this.hooks.urls.getInitial(this.key,this.options);try{this.socket=this.hooks.getSocket(t,this.options)}catch(t){return q.defer(()=>{this.onError(t),this.changeState("closed")}),!1}return this.bindListeners(),tt.debug("Connecting",{transport:this.name,url:t}),this.changeState("connecting"),!0}close(){return!!this.socket&&(this.socket.close(),!0)}send(t){return"open"===this.state&&(q.defer(()=>{this.socket&&this.socket.send(t)}),!0)}ping(){"open"===this.state&&this.supportsPing()&&this.socket.ping()}onOpen(){this.hooks.beforeOpen&&this.hooks.beforeOpen(this.socket,this.hooks.urls.getPath(this.key,this.options)),this.changeState("open"),this.socket.onopen=void 0}onError(t){this.emit("error",{type:"WebSocketError",error:t}),this.timeline.error(this.buildTimelineMessage({error:t.toString()}))}onClose(t){t?this.changeState("closed",{code:t.code,reason:t.reason,wasClean:t.wasClean}):this.changeState("closed"),this.unbindListeners(),this.socket=void 0}onMessage(t){this.emit("message",t)}onActivity(){this.emit("activity")}bindListeners(){this.socket.onopen=()=>{this.onOpen()},this.socket.onerror=t=>{this.onError(t)},this.socket.onclose=t=>{this.onClose(t)},this.socket.onmessage=t=>{this.onMessage(t)},this.supportsPing()&&(this.socket.onactivity=()=>{this.onActivity()})}unbindListeners(){this.socket&&(this.socket.onopen=void 0,this.socket.onerror=void 0,this.socket.onclose=void 0,this.socket.onmessage=void 0,this.supportsPing()&&(this.socket.onactivity=void 0))}changeState(t,e){this.state=t,this.timeline.info(this.buildTimelineMessage({state:t,params:e})),this.emit(t,e)}buildTimelineMessage(t){return B({cid:this.id},t)}constructor(t,e,n,i,r){super(),this.initialize=t4.transportConnectionInitializer,this.hooks=t,this.name=e,this.priority=n,this.key=i,this.options=r,this.state="new",this.timeline=r.timeline,this.activityTimeout=r.activityTimeout,this.id=this.timeline.generateUniqueID()}}class th{isSupported(t){return this.hooks.isSupported(t)}createConnection(t,e,n,i){return new tc(this.hooks,t,e,n,i)}constructor(t){this.hooks=t}}var tu=new th({urls:{getInitial:function(t,e){var n=(e.httpPath||"")+ts(t,"flash=false");return tr("ws",e,n)}},handlesActivityChecks:!1,supportsPing:!1,isInitialized:function(){return!!t4.getWebSocketAPI()},isSupported:function(){return!!t4.getWebSocketAPI()},getSocket:function(t){return t4.createWebSocket(t)}}),tl={urls:{getInitial:function(t,e){var n=(e.httpPath||"/pusher")+ts(t);return tr("http",e,n)}},handlesActivityChecks:!1,supportsPing:!0,isInitialized:function(){return!0}},td=B({getSocket:function(t){return t4.HTTPFactory.createStreamingSocket(t)}},tl),tp=B({getSocket:function(t){return t4.HTTPFactory.createPollingSocket(t)}},tl),tf={isSupported:function(){return t4.isXHRSupported()}},tg={ws:tu,xhr_streaming:new th(B({},td,tf)),xhr_polling:new th(B({},tp,tf))},tb=new th({file:"sockjs",urls:{getInitial:function(t,e){return tr("http",e,e.httpPath||"/pusher")},getPath:function(t,e){return ts(t)}},handlesActivityChecks:!0,supportsPing:!1,isSupported:function(){return!0},isInitialized:function(){return void 0!==window.SockJS},getSocket:function(t,e){return new window.SockJS(t,null,{js_path:f.getPath("sockjs",{useTLS:e.useTLS}),ignore_null_origin:e.ignoreNullOrigin})},beforeOpen:function(t,e){t.send(JSON.stringify({path:e}))}}),tv={isSupported:function(t){return t4.isXDRSupported(t.useTLS)}},tm=new th(B({},td,tv)),ty=new th(B({},tp,tv));tg.xdr_streaming=tm,tg.xdr_polling=ty,tg.sockjs=tb;class tw extends ta{isOnline(){return void 0===window.navigator.onLine||window.navigator.onLine}constructor(){super();var t=this;void 0!==window.addEventListener&&(window.addEventListener("online",function(){t.emit("online")},!1),window.addEventListener("offline",function(){t.emit("offline")},!1))}}var tS=new tw;class t_{createConnection(t,e,n,i){i=B({},i,{activityTimeout:this.pingDelay});var r=this.transport.createConnection(t,e,n,i),s=null,o=function(){r.unbind("open",o),r.bind("closed",a),s=q.now()},a=t=>{if(r.unbind("closed",a),1002===t.code||1003===t.code)this.manager.reportDeath();else if(!t.wasClean&&s){var e=q.now()-s;e<2*this.maxPingDelay&&(this.manager.reportDeath(),this.pingDelay=Math.max(e/2,this.minPingDelay))}};return r.bind("open",o),r}isSupported(t){return this.manager.isAlive()&&this.transport.isSupported(t)}constructor(t,e,n){this.manager=t,this.transport=e,this.minPingDelay=n.minPingDelay,this.maxPingDelay=n.maxPingDelay,this.pingDelay=void 0}}let tk={decodeMessage:function(t){try{var e=JSON.parse(t.data),n=e.data;if("string"==typeof n)try{n=JSON.parse(e.data)}catch(t){}var i={event:e.event,channel:e.channel,data:n};return e.user_id&&(i.user_id=e.user_id),i}catch(e){throw{type:"MessageParseError",error:e,data:t.data}}},encodeMessage:function(t){return JSON.stringify(t)},processHandshake:function(t){var e=tk.decodeMessage(t);if("pusher:connection_established"===e.event){if(!e.data.activity_timeout)throw"No activity timeout specified in handshake";return{action:"connected",id:e.data.socket_id,activityTimeout:1e3*e.data.activity_timeout}}if("pusher:error"===e.event)return{action:this.getCloseAction(e.data),error:this.getCloseError(e.data)};throw"Invalid handshake"},getCloseAction:function(t){if(t.code<4e3)if(t.code>=1002&&t.code<=1004)return"backoff";else return null;if(4e3===t.code)return"tls_only";if(t.code<4100)return"refused";if(t.code<4200)return"backoff";if(t.code<4300)return"retry";else return"refused"},getCloseError:function(t){return 1e3!==t.code&&1001!==t.code?{type:"PusherError",data:{code:t.code,message:t.reason||t.message}}:null}};class tC extends ta{handlesActivityChecks(){return this.transport.handlesActivityChecks()}send(t){return this.transport.send(t)}send_event(t,e,n){var i={event:t,data:e};return n&&(i.channel=n),tt.debug("Event sent",i),this.send(tk.encodeMessage(i))}ping(){this.transport.supportsPing()?this.transport.ping():this.send_event("pusher:ping",{})}close(){this.transport.close()}bindListeners(){var t={message:t=>{var e;try{e=tk.decodeMessage(t)}catch(e){this.emit("error",{type:"MessageParseError",error:e,data:t.data})}if(void 0!==e){switch(tt.debug("Event recd",e),e.event){case"pusher:error":this.emit("error",{type:"PusherError",data:e.data});break;case"pusher:ping":this.emit("ping");break;case"pusher:pong":this.emit("pong")}this.emit("message",e)}},activity:()=>{this.emit("activity")},error:t=>{this.emit("error",t)},closed:t=>{e(),t&&t.code&&this.handleCloseEvent(t),this.transport=null,this.emit("closed")}},e=()=>{J(t,(t,e)=>{this.transport.unbind(e,t)})};J(t,(t,e)=>{this.transport.bind(e,t)})}handleCloseEvent(t){var e=tk.getCloseAction(t),n=tk.getCloseError(t);n&&this.emit("error",n),e&&this.emit(e,{action:e,error:n})}constructor(t,e){super(),this.id=t,this.transport=e,this.activityTimeout=e.activityTimeout,this.bindListeners()}}class tT{close(){this.unbindListeners(),this.transport.close()}bindListeners(){this.onMessage=t=>{var e;this.unbindListeners();try{e=tk.processHandshake(t)}catch(t){this.finish("error",{error:t}),this.transport.close();return}"connected"===e.action?this.finish("connected",{connection:new tC(e.id,this.transport),activityTimeout:e.activityTimeout}):(this.finish(e.action,{error:e.error}),this.transport.close())},this.onClosed=t=>{this.unbindListeners();var e=tk.getCloseAction(t)||"backoff",n=tk.getCloseError(t);this.finish(e,{error:n})},this.transport.bind("message",this.onMessage),this.transport.bind("closed",this.onClosed)}unbindListeners(){this.transport.unbind("message",this.onMessage),this.transport.unbind("closed",this.onClosed)}finish(t,e){this.callback(B({transport:this.transport,action:t},e))}constructor(t,e){this.transport=t,this.callback=e,this.bindListeners()}}class tP{send(t,e){this.timeline.isEmpty()||this.timeline.send(t4.TimelineTransport.getAgent(this,t),e)}constructor(t,e){this.timeline=t,this.options=e||{}}}class tE extends ta{authorize(t,e){return e(null,{auth:""})}trigger(t,e){if(0!==t.indexOf("client-"))throw new v("Event '"+t+"' does not start with 'client-'");if(!this.subscribed){var n=b.buildLogSuffix("triggeringClientEvents");tt.warn("Client event triggered before channel 'subscription_succeeded' event . ".concat(n))}return this.pusher.send_event(t,e,this.name)}disconnect(){this.subscribed=!1,this.subscriptionPending=!1}handleEvent(t){var e=t.event,n=t.data;"pusher_internal:subscription_succeeded"===e?this.handleSubscriptionSucceededEvent(t):"pusher_internal:subscription_count"===e?this.handleSubscriptionCountEvent(t):0!==e.indexOf("pusher_internal:")&&this.emit(e,n,{})}handleSubscriptionSucceededEvent(t){this.subscriptionPending=!1,this.subscribed=!0,this.subscriptionCancelled?this.pusher.unsubscribe(this.name):this.emit("pusher:subscription_succeeded",t.data)}handleSubscriptionCountEvent(t){t.data.subscription_count&&(this.subscriptionCount=t.data.subscription_count),this.emit("pusher:subscription_count",t.data)}subscribe(){this.subscribed||(this.subscriptionPending=!0,this.subscriptionCancelled=!1,this.authorize(this.pusher.connection.socket_id,(t,e)=>{t?(this.subscriptionPending=!1,tt.error(t.toString()),this.emit("pusher:subscription_error",Object.assign({},{type:"AuthError",error:t.message},t instanceof T?{status:t.status}:{}))):this.pusher.send_event("pusher:subscribe",{auth:e.auth,channel_data:e.channel_data,channel:this.name})}))}unsubscribe(){this.subscribed=!1,this.pusher.send_event("pusher:unsubscribe",{channel:this.name})}cancelSubscription(){this.subscriptionCancelled=!0}reinstateSubscription(){this.subscriptionCancelled=!1}constructor(t,e){super(function(e,n){tt.debug("No callbacks on "+t+" for "+e)}),this.name=t,this.pusher=e,this.subscribed=!1,this.subscriptionPending=!1,this.subscriptionCancelled=!1}}class tx extends tE{authorize(t,e){return this.pusher.config.channelAuthorizer({channelName:this.name,socketId:t},e)}}class tO{get(t){return Object.prototype.hasOwnProperty.call(this.members,t)?{id:t,info:this.members[t]}:null}each(t){J(this.members,(e,n)=>{t(this.get(n))})}setMyID(t){this.myID=t}onSubscription(t){this.members=t.presence.hash,this.count=t.presence.count,this.me=this.get(this.myID)}addMember(t){return null===this.get(t.user_id)&&this.count++,this.members[t.user_id]=t.user_info,this.get(t.user_id)}removeMember(t){var e=this.get(t.user_id);return e&&(delete this.members[t.user_id],this.count--),e}reset(){this.members={},this.count=0,this.myID=null,this.me=null}constructor(){this.reset()}}class tL extends tx{authorize(t,e){super.authorize(t,(t,n)=>{var i,r,s,o;return i=this,r=void 0,s=void 0,o=function*(){if(!t)if(null!=n.channel_data){var i=JSON.parse(n.channel_data);this.members.setMyID(i.user_id)}else if(yield this.pusher.user.signinDonePromise,null!=this.pusher.user.user_data)this.members.setMyID(this.pusher.user.user_data.id);else{let t=b.buildLogSuffix("authorizationEndpoint");tt.error("Invalid auth response for channel '".concat(this.name,"', expected 'channel_data' field. ").concat(t,", or the user should be signed in.")),e("Invalid auth response");return}e(t,n)},new(s||(s=Promise))(function(t,e){function n(t){try{c(o.next(t))}catch(t){e(t)}}function a(t){try{c(o.throw(t))}catch(t){e(t)}}function c(e){var i;e.done?t(e.value):((i=e.value)instanceof s?i:new s(function(t){t(i)})).then(n,a)}c((o=o.apply(i,r||[])).next())})})}handleEvent(t){var e=t.event;if(0===e.indexOf("pusher_internal:"))this.handleInternalEvent(t);else{var n=t.data,i={};t.user_id&&(i.user_id=t.user_id),this.emit(e,n,i)}}handleInternalEvent(t){var e=t.event,n=t.data;switch(e){case"pusher_internal:subscription_succeeded":this.handleSubscriptionSucceededEvent(t);break;case"pusher_internal:subscription_count":this.handleSubscriptionCountEvent(t);break;case"pusher_internal:member_added":var i=this.members.addMember(n);this.emit("pusher:member_added",i);break;case"pusher_internal:member_removed":var r=this.members.removeMember(n);r&&this.emit("pusher:member_removed",r)}}handleSubscriptionSucceededEvent(t){this.subscriptionPending=!1,this.subscribed=!0,this.subscriptionCancelled?this.pusher.unsubscribe(this.name):(this.members.onSubscription(t.data),this.emit("pusher:subscription_succeeded",this.members))}disconnect(){this.members.reset(),super.disconnect()}constructor(t,e){super(t,e),this.members=new tO}}var tA=n(1),tR=n(0);class tI extends tx{authorize(t,e){super.authorize(t,(t,n)=>{if(t)return void e(t,n);let i=n.shared_secret;if(!i)return void e(Error("No shared_secret key in auth payload for encrypted channel: ".concat(this.name)),null);this.key=Object(tR.decode)(i),delete n.shared_secret,e(null,n)})}trigger(t,e){throw new _("Client events are not currently supported for encrypted channels")}handleEvent(t){var e=t.event,n=t.data;if(0===e.indexOf("pusher_internal:")||0===e.indexOf("pusher:"))return void super.handleEvent(t);this.handleEncryptedEvent(e,n)}handleEncryptedEvent(t,e){if(!this.key)return void tt.debug("Received encrypted event before key has been retrieved from the authEndpoint");if(!e.ciphertext||!e.nonce)return void tt.error("Unexpected format for encrypted event, expected object with `ciphertext` and `nonce` fields, got: "+e);let n=Object(tR.decode)(e.ciphertext);if(n.length<this.nacl.secretbox.overheadLength)return void tt.error("Expected encrypted event ciphertext length to be ".concat(this.nacl.secretbox.overheadLength,", got: ").concat(n.length));let i=Object(tR.decode)(e.nonce);if(i.length<this.nacl.secretbox.nonceLength)return void tt.error("Expected encrypted event nonce length to be ".concat(this.nacl.secretbox.nonceLength,", got: ").concat(i.length));let r=this.nacl.secretbox.open(n,i,this.key);if(null===r){tt.debug("Failed to decrypt an event, probably because it was encrypted with a different key. Fetching a new key from the authEndpoint..."),this.authorize(this.pusher.connection.socket_id,(e,s)=>e?void tt.error("Failed to make a request to the authEndpoint: ".concat(s,". Unable to fetch new key, so dropping encrypted event")):null===(r=this.nacl.secretbox.open(n,i,this.key))?void tt.error("Failed to decrypt event with new key. Dropping encrypted event"):void this.emit(t,this.getDataToEmit(r)));return}this.emit(t,this.getDataToEmit(r))}getDataToEmit(t){let e=Object(tA.decode)(t);try{return JSON.parse(e)}catch(t){return e}}constructor(t,e,n){super(t,e),this.key=null,this.nacl=n}}class tD extends ta{connect(){if(!this.connection&&!this.runner){if(!this.strategy.isSupported())return void this.updateState("failed");this.updateState("connecting"),this.startConnecting(),this.setUnavailableTimer()}}send(t){return!!this.connection&&this.connection.send(t)}send_event(t,e,n){return!!this.connection&&this.connection.send_event(t,e,n)}disconnect(){this.disconnectInternally(),this.updateState("disconnected")}isUsingTLS(){return this.usingTLS}startConnecting(){var t=(e,n)=>{e?this.runner=this.strategy.connect(0,t):"error"===n.action?(this.emit("error",{type:"HandshakeError",error:n.error}),this.timeline.error({handshakeError:n.error})):(this.abortConnecting(),this.handshakeCallbacks[n.action](n))};this.runner=this.strategy.connect(0,t)}abortConnecting(){this.runner&&(this.runner.abort(),this.runner=null)}disconnectInternally(){this.abortConnecting(),this.clearRetryTimer(),this.clearUnavailableTimer(),this.connection&&this.abandonConnection().close()}updateStrategy(){this.strategy=this.options.getStrategy({key:this.key,timeline:this.timeline,useTLS:this.usingTLS})}retryIn(t){this.timeline.info({action:"retry",delay:t}),t>0&&this.emit("connecting_in",Math.round(t/1e3)),this.retryTimer=new M(t||0,()=>{this.disconnectInternally(),this.connect()})}clearRetryTimer(){this.retryTimer&&(this.retryTimer.ensureAborted(),this.retryTimer=null)}setUnavailableTimer(){this.unavailableTimer=new M(this.options.unavailableTimeout,()=>{this.updateState("unavailable")})}clearUnavailableTimer(){this.unavailableTimer&&this.unavailableTimer.ensureAborted()}sendActivityCheck(){this.stopActivityCheck(),this.connection.ping(),this.activityTimer=new M(this.options.pongTimeout,()=>{this.timeline.error({pong_timed_out:this.options.pongTimeout}),this.retryIn(0)})}resetActivityCheck(){this.stopActivityCheck(),this.connection&&!this.connection.handlesActivityChecks()&&(this.activityTimer=new M(this.activityTimeout,()=>{this.sendActivityCheck()}))}stopActivityCheck(){this.activityTimer&&this.activityTimer.ensureAborted()}buildConnectionCallbacks(t){return B({},t,{message:t=>{this.resetActivityCheck(),this.emit("message",t)},ping:()=>{this.send_event("pusher:pong",{})},activity:()=>{this.resetActivityCheck()},error:t=>{this.emit("error",t)},closed:()=>{this.abandonConnection(),this.shouldRetry()&&this.retryIn(1e3)}})}buildHandshakeCallbacks(t){return B({},t,{connected:t=>{this.activityTimeout=Math.min(this.options.activityTimeout,t.activityTimeout,t.connection.activityTimeout||1/0),this.clearUnavailableTimer(),this.setConnection(t.connection),this.socket_id=this.connection.id,this.updateState("connected",{socket_id:this.socket_id})}})}buildErrorCallbacks(){let t=t=>e=>{e.error&&this.emit("error",{type:"WebSocketError",error:e.error}),t(e)};return{tls_only:t(()=>{this.usingTLS=!0,this.updateStrategy(),this.retryIn(0)}),refused:t(()=>{this.disconnect()}),backoff:t(()=>{this.retryIn(1e3)}),retry:t(()=>{this.retryIn(0)})}}setConnection(t){for(var e in this.connection=t,this.connectionCallbacks)this.connection.bind(e,this.connectionCallbacks[e]);this.resetActivityCheck()}abandonConnection(){if(this.connection){for(var t in this.stopActivityCheck(),this.connectionCallbacks)this.connection.unbind(t,this.connectionCallbacks[t]);var e=this.connection;return this.connection=null,e}}updateState(t,e){var n=this.state;if(this.state=t,n!==t){var i=t;"connected"===i&&(i+=" with new socket ID "+e.socket_id),tt.debug("State changed",n+" -> "+i),this.timeline.info({state:t,params:e}),this.emit("state_change",{previous:n,current:t}),this.emit(t,e)}}shouldRetry(){return"connecting"===this.state||"connected"===this.state}constructor(t,e){super(),this.state="initialized",this.connection=null,this.key=t,this.options=e,this.timeline=this.options.timeline,this.usingTLS=this.options.useTLS,this.errorCallbacks=this.buildErrorCallbacks(),this.connectionCallbacks=this.buildConnectionCallbacks(this.errorCallbacks),this.handshakeCallbacks=this.buildHandshakeCallbacks(this.errorCallbacks);var n=t4.getNetwork();n.bind("online",()=>{this.timeline.info({netinfo:"online"}),("connecting"===this.state||"unavailable"===this.state)&&this.retryIn(0)}),n.bind("offline",()=>{this.timeline.info({netinfo:"offline"}),this.connection&&this.sendActivityCheck()}),this.updateStrategy()}}class tj{add(t,e){return this.channels[t]||(this.channels[t]=function(t,e){if(0===t.indexOf("private-encrypted-")){if(e.config.nacl)return tN.createEncryptedChannel(t,e,e.config.nacl);let n=b.buildLogSuffix("encryptedChannelSupport");throw new _("".concat("Tried to subscribe to a private-encrypted- channel but no nacl implementation available",". ").concat(n))}if(0===t.indexOf("private-"))return tN.createPrivateChannel(t,e);if(0===t.indexOf("presence-"))return tN.createPresenceChannel(t,e);if(0!==t.indexOf("#"))return tN.createChannel(t,e);throw new m('Cannot create a channel with name "'+t+'".')}(t,e)),this.channels[t]}all(){var t,e;return t=this.channels,e=[],J(t,function(t){e.push(t)}),e}find(t){return this.channels[t]}remove(t){var e=this.channels[t];return delete this.channels[t],e}disconnect(){J(this.channels,function(t){t.disconnect()})}constructor(){this.channels={}}}var tN={createChannels:()=>new tj,createConnectionManager:(t,e)=>new tD(t,e),createChannel:(t,e)=>new tE(t,e),createPrivateChannel:(t,e)=>new tx(t,e),createPresenceChannel:(t,e)=>new tL(t,e),createEncryptedChannel:(t,e,n)=>new tI(t,e,n),createTimelineSender:(t,e)=>new tP(t,e),createHandshake:(t,e)=>new tT(t,e),createAssistantToTheTransportManager:(t,e,n)=>new t_(t,e,n)};class tH{getAssistant(t){return tN.createAssistantToTheTransportManager(this,t,{minPingDelay:this.options.minPingDelay,maxPingDelay:this.options.maxPingDelay})}isAlive(){return this.livesLeft>0}reportDeath(){this.livesLeft-=1}constructor(t){this.options=t||{},this.livesLeft=this.options.lives||1/0}}class tU{isSupported(){return Q(this.strategies,q.method("isSupported"))}connect(t,e){var n=this.strategies,i=0,r=this.timeout,s=null,o=(a,c)=>{c?e(null,c):(i+=1,this.loop&&(i%=n.length),i<n.length?(r&&(r*=2,this.timeoutLimit&&(r=Math.min(r,this.timeoutLimit))),s=this.tryStrategy(n[i],t,{timeout:r,failFast:this.failFast},o)):e(!0))};return s=this.tryStrategy(n[i],t,{timeout:r,failFast:this.failFast},o),{abort:function(){s.abort()},forceMinPriority:function(e){t=e,s&&s.forceMinPriority(e)}}}tryStrategy(t,e,n,i){var r=null,s=null;return n.timeout>0&&(r=new M(n.timeout,function(){s.abort(),i(!0)})),s=t.connect(e,function(t,e){(!(t&&r&&r.isRunning())||n.failFast)&&(r&&r.ensureAborted(),i(t,e))}),{abort:function(){r&&r.ensureAborted(),s.abort()},forceMinPriority:function(t){s.forceMinPriority(t)}}}constructor(t,e){this.strategies=t,this.loop=!!e.loop,this.failFast=!!e.failFast,this.timeout=e.timeout,this.timeoutLimit=e.timeoutLimit}}class tM{isSupported(){return Q(this.strategies,q.method("isSupported"))}connect(t,e){var n,i,r,s;return n=this.strategies,i=t,r=function(t,n){return function(i,r){if(n[t].error=i,i){(function(t){for(var e,n=0;n<t.length;n++){if(e=t[n],!e.error)return!1}return!0})(n)&&e(!0);return}G(n,function(t){t.forceMinPriority(r.transport.priority)}),e(null,r)}},s=V(n,function(t,e,n,s){return t.connect(i,r(e,s))}),{abort:function(){G(s,tz)},forceMinPriority:function(t){G(s,function(e){e.forceMinPriority(t)})}}}constructor(t){this.strategies=t}}function tz(t){t.error||t.aborted||(t.abort(),t.aborted=!0)}class tq{isSupported(){return this.strategy.isSupported()}connect(t,e){var n=this.usingTLS,i=function(t){var e=t4.getLocalStorage();if(e)try{var n=e[tB(t)];if(n)return JSON.parse(n)}catch(e){tF(t)}return null}(n),r=i&&i.cacheSkipCount?i.cacheSkipCount:0,s=[this.strategy];if(i&&i.timestamp+this.ttl>=q.now()){var o=this.transports[i.transport];o&&(["ws","wss"].includes(i.transport)||r>3?(this.timeline.info({cached:!0,transport:i.transport,latency:i.latency}),s.push(new tU([o],{timeout:2*i.latency+1e3,failFast:!0}))):r++)}var a=q.now(),c=s.pop().connect(t,function i(o,h){o?(tF(n),s.length>0?(a=q.now(),c=s.pop().connect(t,i)):e(o)):(function(t,e,n,i){var r=t4.getLocalStorage();if(r)try{r[tB(t)]=K({timestamp:q.now(),transport:e,latency:n,cacheSkipCount:i})}catch(t){}}(n,h.transport.name,q.now()-a,r),e(null,h))});return{abort:function(){c.abort()},forceMinPriority:function(e){t=e,c&&c.forceMinPriority(e)}}}constructor(t,e,n){this.strategy=t,this.transports=e,this.ttl=n.ttl||18e5,this.usingTLS=n.useTLS,this.timeline=n.timeline}}function tB(t){return"pusherTransport"+(t?"TLS":"NonTLS")}function tF(t){var e=t4.getLocalStorage();if(e)try{delete e[tB(t)]}catch(t){}}class tX{isSupported(){return this.strategy.isSupported()}connect(t,e){var n,i=this.strategy,r=new M(this.options.delay,function(){n=i.connect(t,e)});return{abort:function(){r.ensureAborted(),n&&n.abort()},forceMinPriority:function(e){t=e,n&&n.forceMinPriority(e)}}}constructor(t,{delay:e}){this.strategy=t,this.options={delay:e}}}class tJ{isSupported(){return(this.test()?this.trueBranch:this.falseBranch).isSupported()}connect(t,e){return(this.test()?this.trueBranch:this.falseBranch).connect(t,e)}constructor(t,e,n){this.test=t,this.trueBranch=e,this.falseBranch=n}}class tW{isSupported(){return this.strategy.isSupported()}connect(t,e){var n=this.strategy.connect(t,function(t,i){i&&n.abort(),e(t,i)});return n}constructor(t){this.strategy=t}}function tG(t){return function(){return t.isSupported()}}var tV={getRequest:function(t){var e=new window.XDomainRequest;return e.ontimeout=function(){t.emit("error",new y),t.close()},e.onerror=function(e){t.emit("error",e),t.close()},e.onprogress=function(){e.responseText&&e.responseText.length>0&&t.onChunk(200,e.responseText)},e.onload=function(){e.responseText&&e.responseText.length>0&&t.onChunk(200,e.responseText),t.emit("finished",200),t.close()},e},abortRequest:function(t){t.ontimeout=t.onerror=t.onprogress=t.onload=null,t.abort()}};class tY extends ta{start(t){this.position=0,this.xhr=this.hooks.getRequest(this),this.unloader=()=>{this.close()},t4.addUnloadListener(this.unloader),this.xhr.open(this.method,this.url,!0),this.xhr.setRequestHeader&&this.xhr.setRequestHeader("Content-Type","application/json"),this.xhr.send(t)}close(){this.unloader&&(t4.removeUnloadListener(this.unloader),this.unloader=null),this.xhr&&(this.hooks.abortRequest(this.xhr),this.xhr=null)}onChunk(t,e){for(;;){var n=this.advanceBuffer(e);if(n)this.emit("chunk",{status:t,data:n});else break}this.isBufferTooLong(e)&&this.emit("buffer_too_long")}advanceBuffer(t){var e=t.slice(this.position),n=e.indexOf("\n");return -1!==n?(this.position+=n+1,e.slice(0,n)):null}isBufferTooLong(t){return this.position===t.length&&t.length>262144}constructor(t,e,n){super(),this.hooks=t,this.method=e,this.url=n}}(s=c||(c={}))[s.CONNECTING=0]="CONNECTING",s[s.OPEN=1]="OPEN",s[s.CLOSED=3]="CLOSED";var t$=c,tQ=1;class tK{send(t){return this.sendRaw(JSON.stringify([t]))}ping(){this.hooks.sendHeartbeat(this)}close(t,e){this.onClose(t,e,!0)}sendRaw(t){if(this.readyState!==t$.OPEN)return!1;try{var e,n;return t4.createSocketRequest("POST",tZ((e=this.location,n=this.session,e.base+"/"+n+"/xhr_send"))).start(t),!0}catch(t){return!1}}reconnect(){this.closeStream(),this.openStream()}onClose(t,e,n){this.closeStream(),this.readyState=t$.CLOSED,this.onclose&&this.onclose({code:t,reason:e,wasClean:n})}onChunk(t){if(200===t.status){var e;switch(this.readyState===t$.OPEN&&this.onActivity(),t.data.slice(0,1)){case"o":e=JSON.parse(t.data.slice(1)||"{}"),this.onOpen(e);break;case"a":e=JSON.parse(t.data.slice(1)||"[]");for(var n=0;n<e.length;n++)this.onEvent(e[n]);break;case"m":e=JSON.parse(t.data.slice(1)||"null"),this.onEvent(e);break;case"h":this.hooks.onHeartbeat(this);break;case"c":e=JSON.parse(t.data.slice(1)||"[]"),this.onClose(e[0],e[1],!0)}}}onOpen(t){var e,n,i;this.readyState===t$.CONNECTING?(t&&t.hostname&&(this.location.base=(e=this.location.base,n=t.hostname,(i=/(https?:\/\/)([^\/:]+)((\/|:)?.*)/.exec(e))[1]+n+i[3])),this.readyState=t$.OPEN,this.onopen&&this.onopen()):this.onClose(1006,"Server lost session",!0)}onEvent(t){this.readyState===t$.OPEN&&this.onmessage&&this.onmessage({data:t})}onActivity(){this.onactivity&&this.onactivity()}onError(t){this.onerror&&this.onerror(t)}openStream(){this.stream=t4.createSocketRequest("POST",tZ(this.hooks.getReceiveURL(this.location,this.session))),this.stream.bind("chunk",t=>{this.onChunk(t)}),this.stream.bind("finished",t=>{this.hooks.onFinished(this,t)}),this.stream.bind("buffer_too_long",()=>{this.reconnect()});try{this.stream.start()}catch(t){q.defer(()=>{this.onError(t),this.onClose(1006,"Could not start streaming",!1)})}}closeStream(){this.stream&&(this.stream.unbind_all(),this.stream.close(),this.stream=null)}constructor(t,e){var n,i;this.hooks=t,this.session=t0(1e3)+"/"+function(t){for(var e=[],n=0;n<8;n++)e.push(t0(32).toString(32));return e.join("")}(8),this.location=(n=e,{base:(i=/([^\?]*)\/*(\??.*)/.exec(n))[1],queryString:i[2]}),this.readyState=t$.CONNECTING,this.openStream()}}function tZ(t){var e=-1===t.indexOf("?")?"?":"&";return t+e+"t="+ +new Date+"&n="+tQ++}function t0(t){return t4.randomInt(t)}var t1={getReceiveURL:function(t,e){return t.base+"/"+e+"/xhr_streaming"+t.queryString},onHeartbeat:function(t){t.sendRaw("[]")},sendHeartbeat:function(t){t.sendRaw("[]")},onFinished:function(t,e){t.onClose(1006,"Connection interrupted ("+e+")",!1)}},t2={getReceiveURL:function(t,e){return t.base+"/"+e+"/xhr"+t.queryString},onHeartbeat:function(){},sendHeartbeat:function(t){t.sendRaw("[]")},onFinished:function(t,e){200===e?t.reconnect():t.onClose(1006,"Connection interrupted ("+e+")",!1)}},t6={getRequest:function(t){var e=new(t4.getXHRAPI());return e.onreadystatechange=e.onprogress=function(){switch(e.readyState){case 3:e.responseText&&e.responseText.length>0&&t.onChunk(e.status,e.responseText);break;case 4:e.responseText&&e.responseText.length>0&&t.onChunk(e.status,e.responseText),t.emit("finished",e.status),t.close()}},e},abortRequest:function(t){t.onreadystatechange=null,t.abort()}},t3={createStreamingSocket(t){return this.createSocket(t1,t)},createPollingSocket(t){return this.createSocket(t2,t)},createSocket:(t,e)=>new tK(t,e),createXHR(t,e){return this.createRequest(t6,t,e)},createRequest:(t,e,n)=>new tY(t,e,n)};t3.createXDR=function(t,e){return this.createRequest(tV,t,e)};var t4={nextAuthCallbackID:1,auth_callbacks:{},ScriptReceivers:u,DependenciesReceivers:p,getDefaultStrategy:function(t,e,n){var i,r={};function s(e,i,s,o,a){var c=n(t,e,i,s,o,a);return r[e]=c,c}var o=Object.assign({},e,{hostNonTLS:t.wsHost+":"+t.wsPort,hostTLS:t.wsHost+":"+t.wssPort,httpPath:t.wsPath}),a=Object.assign({},o,{useTLS:!0}),c=Object.assign({},e,{hostNonTLS:t.httpHost+":"+t.httpPort,hostTLS:t.httpHost+":"+t.httpsPort,httpPath:t.httpPath}),h={loop:!0,timeout:15e3,timeoutLimit:6e4},u=new tH({minPingDelay:1e4,maxPingDelay:t.activityTimeout}),l=new tH({lives:2,minPingDelay:1e4,maxPingDelay:t.activityTimeout}),d=s("ws","ws",3,o,u),p=s("wss","ws",3,a,u),f=s("sockjs","sockjs",1,c),g=s("xhr_streaming","xhr_streaming",1,c,l),b=s("xdr_streaming","xdr_streaming",1,c,l),v=s("xhr_polling","xhr_polling",1,c),m=s("xdr_polling","xdr_polling",1,c),y=new tU([d],h),w=new tU([p],h),S=new tU([f],h),_=new tU([new tJ(tG(g),g,b)],h),k=new tU([new tJ(tG(v),v,m)],h),C=new tU([new tJ(tG(_),new tM([_,new tX(k,{delay:4e3})]),k)],h),T=new tJ(tG(C),C,S);return i=new tM(e.useTLS?[y,new tX(T,{delay:2e3})]:[y,new tX(w,{delay:2e3}),new tX(T,{delay:5e3})]),new tq(new tW(new tJ(tG(d),i,T)),r,{ttl:18e5,timeline:e.timeline,useTLS:e.useTLS})},Transports:tg,transportConnectionInitializer:function(){var t=this;t.timeline.info(t.buildTimelineMessage({transport:t.name+(t.options.useTLS?"s":"")})),t.hooks.isInitialized()?t.changeState("initialized"):t.hooks.file?(t.changeState("initializing"),f.load(t.hooks.file,{useTLS:t.options.useTLS},function(e,n){t.hooks.isInitialized()?(t.changeState("initialized"),n(!0)):(e&&t.onError(e),t.onClose(),n(!1))})):t.onClose()},HTTPFactory:t3,TimelineTransport:{name:"jsonp",getAgent:function(t,e){return function(n,i){var r="http"+(e?"s":"")+"://"+(t.host||t.options.host)+t.options.path,s=t4.createJSONPRequest(r,n),o=t4.ScriptReceivers.create(function(e,n){u.remove(o),s.cleanup(),n&&n.host&&(t.host=n.host),i&&i(e,n)});s.send(o)}}},getXHRAPI:()=>window.XMLHttpRequest,getWebSocketAPI:()=>window.WebSocket||window.MozWebSocket,setup(t){window.Pusher=t;var e=()=>{this.onDocumentBody(t.ready)};window.JSON?e():f.load("json2",{},e)},getDocument:()=>document,getProtocol(){return this.getDocument().location.protocol},getAuthorizers:()=>({ajax:P,jsonp:te}),onDocumentBody(t){document.body?t():setTimeout(()=>{this.onDocumentBody(t)},0)},createJSONPRequest:(t,e)=>new ti(t,e),createScriptRequest:t=>new tn(t),getLocalStorage(){try{return window.localStorage}catch(t){return}},createXHR(){return this.getXHRAPI()?this.createXMLHttpRequest():this.createMicrosoftXHR()},createXMLHttpRequest(){return new(this.getXHRAPI())},createMicrosoftXHR:()=>new ActiveXObject("Microsoft.XMLHTTP"),getNetwork:()=>tS,createWebSocket(t){return new(this.getWebSocketAPI())(t)},createSocketRequest(t,e){if(this.isXHRSupported())return this.HTTPFactory.createXHR(t,e);if(this.isXDRSupported(0===e.indexOf("https:")))return this.HTTPFactory.createXDR(t,e);throw"Cross-origin HTTP requests are not supported"},isXHRSupported(){var t=this.getXHRAPI();return!!t&&void 0!==new t().withCredentials},isXDRSupported(t){var e=this.getProtocol();return!!window.XDomainRequest&&e===(t?"https:":"http:")},addUnloadListener(t){void 0!==window.addEventListener?window.addEventListener("unload",t,!1):void 0!==window.attachEvent&&window.attachEvent("onunload",t)},removeUnloadListener(t){void 0!==window.addEventListener?window.removeEventListener("unload",t,!1):void 0!==window.detachEvent&&window.detachEvent("onunload",t)},randomInt:t=>Math.floor((window.crypto||window.msCrypto).getRandomValues(new Uint32Array(1))[0]/0x100000000*t)};(o=h||(h={}))[o.ERROR=3]="ERROR",o[o.INFO=6]="INFO",o[o.DEBUG=7]="DEBUG";var t8=h;class t5{log(t,e){t<=this.options.level&&(this.events.push(B({},e,{timestamp:q.now()})),this.options.limit&&this.events.length>this.options.limit&&this.events.shift())}error(t){this.log(t8.ERROR,t)}info(t){this.log(t8.INFO,t)}debug(t){this.log(t8.DEBUG,t)}isEmpty(){return 0===this.events.length}send(t,e){var n=B({session:this.session,bundle:this.sent+1,key:this.key,lib:"js",version:this.options.version,cluster:this.options.cluster,features:this.options.features,timeline:this.events},this.options.params);return this.events=[],t(n,(t,n)=>{!t&&this.sent++,e&&e(t,n)}),!0}generateUniqueID(){return this.uniqueID++,this.uniqueID}constructor(t,e,n){this.key=t,this.session=e,this.events=[],this.options=n||{},this.sent=0,this.uniqueID=0}}class t9{isSupported(){return this.transport.isSupported({useTLS:this.options.useTLS})}connect(t,e){if(!this.isSupported())return t7(new C,e);if(this.priority<t)return t7(new w,e);var n=!1,i=this.transport.createConnection(this.name,this.priority,this.options.key,this.options),r=null,s=function(){i.unbind("initialized",s),i.connect()},o=function(){r=tN.createHandshake(i,function(t){n=!0,h(),e(null,t)})},a=function(t){h(),e(t)},c=function(){h(),e(new S(K(i)))},h=function(){i.unbind("initialized",s),i.unbind("open",o),i.unbind("error",a),i.unbind("closed",c)};return i.bind("initialized",s),i.bind("open",o),i.bind("error",a),i.bind("closed",c),i.initialize(),{abort:()=>{n||(h(),r?r.close():i.close())},forceMinPriority:t=>{!n&&this.priority<t&&(r?r.close():i.close())}}}constructor(t,e,n,i){this.name=t,this.priority=e,this.transport=n,this.options=i||{}}}function t7(t,e){return q.defer(function(){e(t)}),{abort:function(){},forceMinPriority:function(){}}}let{Transports:et}=t4;var ee=function(t,e,n,i,r,s){var o,a=et[n];if(!a)throw new k(n);return t.enabledTransports&&-1===X(t.enabledTransports,e)||t.disabledTransports&&-1!==X(t.disabledTransports,e)?o=en:(r=Object.assign({ignoreNullOrigin:t.ignoreNullOrigin},r),o=new t9(e,i,s?s.getAssistant(a):a,r)),o},en={isSupported:function(){return!1},connect:function(t,e){var n=q.defer(function(){e(new C)});return{abort:function(){n.ensureAborted()},forceMinPriority:function(){}}}};let ei=(t,e)=>{var n="socket_id="+encodeURIComponent(t.socketId);for(var i in e.params)n+="&"+encodeURIComponent(i)+"="+encodeURIComponent(e.params[i]);if(null!=e.paramsProvider){let t=e.paramsProvider();for(var i in t)n+="&"+encodeURIComponent(i)+"="+encodeURIComponent(t[i])}return n};var er=t=>{if(void 0===t4.getAuthorizers()[t.transport])throw"'".concat(t.transport,"' is not a recognized auth transport");return(e,n)=>{let i=ei(e,t);t4.getAuthorizers()[t.transport](t4,i,t,a.UserAuthentication,n)}};let es=(t,e)=>{var n="socket_id="+encodeURIComponent(t.socketId);for(var i in n+="&channel_name="+encodeURIComponent(t.channelName),e.params)n+="&"+encodeURIComponent(i)+"="+encodeURIComponent(e.params[i]);if(null!=e.paramsProvider){let t=e.paramsProvider();for(var i in t)n+="&"+encodeURIComponent(i)+"="+encodeURIComponent(t[i])}return n};var eo=t=>{if(void 0===t4.getAuthorizers()[t.transport])throw"'".concat(t.transport,"' is not a recognized auth transport");return(e,n)=>{let i=es(e,t);t4.getAuthorizers()[t.transport](t4,i,t,a.ChannelAuthorization,n)}};let ea=(t,e,n)=>{let i={authTransport:e.transport,authEndpoint:e.endpoint,auth:{params:e.params,headers:e.headers}};return(e,r)=>{n(t.channel(e.channelName),i).authorize(e.socketId,r)}};class ec extends ta{handleEvent(t){t.data.events.forEach(t=>{this.emit(t.name,t)})}bindWatchlistInternalEvent(){this.pusher.connection.bind("message",t=>{"pusher_internal:watchlist_events"===t.event&&this.handleEvent(t)})}constructor(t){super(function(t,e){tt.debug("No callbacks on watchlist events for ".concat(t))}),this.pusher=t,this.bindWatchlistInternalEvent()}}var eh=function(){let t,e;return{promise:new Promise((n,i)=>{t=n,e=i}),resolve:t,reject:e}};class eu extends ta{signin(){this.signin_requested||(this.signin_requested=!0,this._signin())}_signin(){this.signin_requested&&(this._newSigninPromiseIfNeeded(),"connected"===this.pusher.connection.state&&this.pusher.config.userAuthenticator({socketId:this.pusher.connection.socket_id},this._onAuthorize))}_onSigninSuccess(t){try{this.user_data=JSON.parse(t.user_data)}catch(e){tt.error("Failed parsing user data after signin: ".concat(t.user_data)),this._cleanup();return}if("string"!=typeof this.user_data.id||""===this.user_data.id){tt.error("user_data doesn't contain an id. user_data: ".concat(this.user_data)),this._cleanup();return}this._signinDoneResolve(),this._subscribeChannels()}_subscribeChannels(){var t;this.serverToUserChannel=new tE("#server-to-user-".concat(this.user_data.id),this.pusher),this.serverToUserChannel.bind_global((t,e)=>{0!==t.indexOf("pusher_internal:")&&0!==t.indexOf("pusher:")&&this.emit(t,e)}),(t=this.serverToUserChannel).subscriptionPending&&t.subscriptionCancelled?t.reinstateSubscription():t.subscriptionPending||"connected"!==this.pusher.connection.state||t.subscribe()}_cleanup(){this.user_data=null,this.serverToUserChannel&&(this.serverToUserChannel.unbind_all(),this.serverToUserChannel.disconnect(),this.serverToUserChannel=null),this.signin_requested&&this._signinDoneResolve()}_newSigninPromiseIfNeeded(){if(!this.signin_requested||this.signinDonePromise&&!this.signinDonePromise.done)return;let{promise:t,resolve:e,reject:n}=eh();t.done=!1;let i=()=>{t.done=!0};t.then(i).catch(i),this.signinDonePromise=t,this._signinDoneResolve=e}constructor(t){super(function(t,e){tt.debug("No callbacks on user for "+t)}),this.signin_requested=!1,this.user_data=null,this.serverToUserChannel=null,this.signinDonePromise=null,this._signinDoneResolve=null,this._onAuthorize=(t,e)=>{if(t){tt.warn("Error during signin: ".concat(t)),this._cleanup();return}this.pusher.send_event("pusher:signin",{auth:e.auth,user_data:e.user_data})},this.pusher=t,this.pusher.connection.bind("state_change",t=>{let{previous:e,current:n}=t;"connected"!==e&&"connected"===n&&this._signin(),"connected"===e&&"connected"!==n&&(this._cleanup(),this._newSigninPromiseIfNeeded())}),this.watchlist=new ec(t),this.pusher.connection.bind("message",t=>{"pusher:signin_success"===t.event&&this._onSigninSuccess(t.data),this.serverToUserChannel&&this.serverToUserChannel.name===t.channel&&this.serverToUserChannel.handleEvent(t)})}}class el{static ready(){el.isReady=!0;for(var t=0,e=el.instances.length;t<e;t++)el.instances[t].connect()}static getClientFeatures(){return W($({ws:t4.Transports.ws},function(t){return t.isSupported({})}))}channel(t){return this.channels.find(t)}allChannels(){return this.channels.all()}connect(){if(this.connection.connect(),this.timelineSender&&!this.timelineSenderTimer){var t=this.connection.isUsingTLS(),e=this.timelineSender;this.timelineSenderTimer=new z(6e4,function(){e.send(t)})}}disconnect(){this.connection.disconnect(),this.timelineSenderTimer&&(this.timelineSenderTimer.ensureAborted(),this.timelineSenderTimer=null)}bind(t,e,n){return this.global_emitter.bind(t,e,n),this}unbind(t,e,n){return this.global_emitter.unbind(t,e,n),this}bind_global(t){return this.global_emitter.bind_global(t),this}unbind_global(t){return this.global_emitter.unbind_global(t),this}unbind_all(t){return this.global_emitter.unbind_all(),this}subscribeAll(){var t;for(t in this.channels.channels)this.channels.channels.hasOwnProperty(t)&&this.subscribe(t)}subscribe(t){var e=this.channels.add(t,this);return e.subscriptionPending&&e.subscriptionCancelled?e.reinstateSubscription():e.subscriptionPending||"connected"!==this.connection.state||e.subscribe(),e}unsubscribe(t){var e=this.channels.find(t);e&&e.subscriptionPending?e.cancelSubscription():(e=this.channels.remove(t))&&e.subscribed&&e.unsubscribe()}send_event(t,e,n){return this.connection.send_event(t,e,n)}shouldUseTLS(){return this.config.useTLS}signin(){this.user.signin()}constructor(t,e){let n;var i,r,s,o,a=t;if(null==a)throw"You must pass your app key when you instantiate Pusher.";if(null==e)throw"You must pass an options object";if(null==e.cluster)throw"Options object must provide a cluster";"disableStats"in e&&tt.warn("The disableStats option is deprecated in favor of enableStats"),this.key=t,this.config=(n={activityTimeout:e.activityTimeout||l.activityTimeout,cluster:e.cluster,httpPath:e.httpPath||l.httpPath,httpPort:e.httpPort||l.httpPort,httpsPort:e.httpsPort||l.httpsPort,pongTimeout:e.pongTimeout||l.pongTimeout,statsHost:e.statsHost||l.stats_host,unavailableTimeout:e.unavailableTimeout||l.unavailableTimeout,wsPath:e.wsPath||l.wsPath,wsPort:e.wsPort||l.wsPort,wssPort:e.wssPort||l.wssPort,enableStats:"enableStats"in(i=e)?i.enableStats:"disableStats"in i&&!i.disableStats,httpHost:(r=e).httpHost?r.httpHost:r.cluster?"sockjs-".concat(r.cluster,".pusher.com"):l.httpHost,useTLS:function(t){if("https:"===t4.getProtocol());else if(!1===t.forceTLS)return!1;return!0}(e),wsHost:(s=e).wsHost?s.wsHost:(o=s.cluster,"ws-".concat(o,".pusher.com")),userAuthenticator:function(t){let e=Object.assign(Object.assign({},l.userAuthentication),t.userAuthentication);return"customHandler"in e&&null!=e.customHandler?e.customHandler:er(e)}(e),channelAuthorizer:function(t,e){let n,i=("channelAuthorization"in t?n=Object.assign(Object.assign({},l.channelAuthorization),t.channelAuthorization):(n={transport:t.authTransport||l.authTransport,endpoint:t.authEndpoint||l.authEndpoint},"auth"in t&&("params"in t.auth&&(n.params=t.auth.params),"headers"in t.auth&&(n.headers=t.auth.headers)),"authorizer"in t&&(n.customHandler=ea(e,n,t.authorizer))),n);return"customHandler"in i&&null!=i.customHandler?i.customHandler:eo(i)}(e,this)},"disabledTransports"in e&&(n.disabledTransports=e.disabledTransports),"enabledTransports"in e&&(n.enabledTransports=e.enabledTransports),"ignoreNullOrigin"in e&&(n.ignoreNullOrigin=e.ignoreNullOrigin),"timelineParams"in e&&(n.timelineParams=e.timelineParams),"nacl"in e&&(n.nacl=e.nacl),n),this.channels=tN.createChannels(),this.global_emitter=new ta,this.sessionID=t4.randomInt(1e9),this.timeline=new t5(this.key,this.sessionID,{cluster:this.config.cluster,features:el.getClientFeatures(),params:this.config.timelineParams||{},limit:50,level:t8.INFO,version:l.VERSION}),this.config.enableStats&&(this.timelineSender=tN.createTimelineSender(this.timeline,{host:this.config.statsHost,path:"/timeline/v2/"+t4.TimelineTransport.name})),this.connection=tN.createConnectionManager(this.key,{getStrategy:t=>t4.getDefaultStrategy(this.config,t,ee),timeline:this.timeline,activityTimeout:this.config.activityTimeout,pongTimeout:this.config.pongTimeout,unavailableTimeout:this.config.unavailableTimeout,useTLS:!!this.config.useTLS}),this.connection.bind("connected",()=>{this.subscribeAll(),this.timelineSender&&this.timelineSender.send(this.connection.isUsingTLS())}),this.connection.bind("message",t=>{var e=0===t.event.indexOf("pusher_internal:");if(t.channel){var n=this.channel(t.channel);n&&n.handleEvent(t)}e||this.global_emitter.emit(t.event,t.data)}),this.connection.bind("connecting",()=>{this.channels.disconnect()}),this.connection.bind("disconnected",()=>{this.channels.disconnect()}),this.connection.bind("error",t=>{tt.warn(t)}),el.instances.push(this),this.timeline.info({instances:el.instances.length}),this.user=new eu(this),el.isReady&&this.connect()}}el.instances=[],el.isReady=!1,el.logToConsole=!1,el.Runtime=t4,el.ScriptReceivers=t4.ScriptReceivers,el.DependenciesReceivers=t4.DependenciesReceivers,el.auth_callbacks=t4.auth_callbacks;var ed=e.default=el;t4.setup(el)}],e={};function n(i){if(e[i])return e[i].exports;var r=e[i]={i:i,l:!1,exports:{}};return t[i].call(r.exports,r,r.exports,n),r.l=!0,r.exports}return n.m=t,n.c=e,n.d=function(t,e,i){n.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:i})},n.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},n.t=function(t,e){if(1&e&&(t=n(t)),8&e||4&e&&"object"==typeof t&&t&&t.__esModule)return t;var i=Object.create(null);if(n.r(i),Object.defineProperty(i,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var r in t)n.d(i,r,(function(e){return t[e]}).bind(null,r));return i},n.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return n.d(e,"a",e),e},n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},n.p="",n(n.s=2)},"object"==typeof t&&"object"==typeof e?e.exports=r():"function"==typeof define&&n.amdO?define([],r):"object"==typeof t?t.Pusher=r():i.Pusher=r()}})()}}]);