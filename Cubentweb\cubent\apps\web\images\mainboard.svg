<svg xmlns="http://www.w3.org/2000/svg" width="1512" height="546" viewBox="0 0 1512 546" text-rendering="geometricPrecision" shape-rendering="geometricPrecision" style="white-space: pre; background: #000000;">
    <style>
@keyframes a0_t { 0% { transform: translate(1111px,323px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 76.1904% { transform: translate(1381px,323px); } 100% { transform: translate(1381px,323px); } }
@keyframes a1_t { 0% { transform: translate(1111px,314px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 76.1904% { transform: translate(1381px,314px); } 100% { transform: translate(1381px,314px); } }
@keyframes a2_t { 0% { transform: translate(1111px,306px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 76.1904% { transform: translate(1391px,306px); } 100% { transform: translate(1391px,306px); } }
@keyframes a3_t { 0% { transform: translate(1101px,296px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 76.1904% { transform: translate(1371px,296px); } 100% { transform: translate(1371px,296px); } }
@keyframes a4_t { 0% { transform: translate(1101px,287px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 76.1904% { transform: translate(1389px,287px); } 100% { transform: translate(1389px,287px); } }
@keyframes a5_t { 0% { transform: translate(1101px,278px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 76.1904% { transform: translate(1401px,278px); } 100% { transform: translate(1401px,278px); } }
@keyframes a6_t { 0% { transform: translate(1111px,269px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 76.1904% { transform: translate(1401px,269px); } 100% { transform: translate(1401px,269px); } }
@keyframes a7_t { 0% { transform: translate(1102.5px,319px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 66.6666% { transform: translate(1102.5px,549px); } 100% { transform: translate(1102.5px,549px); } }
@keyframes a8_t { 0% { transform: translate(1093px,319px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 66.6666% { transform: translate(1093px,549px); } 100% { transform: translate(1093px,549px); } }
@keyframes a9_t { 0% { transform: translate(1083px,325px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 60% { transform: translate(1083px,555px); } 100% { transform: translate(1083px,555px); } }
@keyframes a10_t { 0% { transform: translate(1073px,319px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 60% { transform: translate(1073px,559px); } 100% { transform: translate(1073px,559px); } }
@keyframes a11_t { 0% { transform: translate(1063px,319px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 60% { transform: translate(1063px,546px); } 100% { transform: translate(1063px,546px); } }
@keyframes a12_t { 0% { transform: translate(1053px,319px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 66.6666% { transform: translate(1053px,519px); } 100% { transform: translate(1053px,519px); } }
@keyframes a13_t { 0% { transform: translate(810px,286px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 66.6666% { transform: translate(1019.38px,286px); } 100% { transform: translate(1019.38px,286px); } }
@keyframes a14_t { 0% { transform: translate(811px,295px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 66.6666% { transform: translate(1026px,295px); } 100% { transform: translate(1026px,295px); } }
@keyframes a15_t { 0% { transform: translate(808px,304px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 66.6666% { transform: translate(1024px,304px); } 100% { transform: translate(1024px,304px); } }
@keyframes a16_t { 0% { transform: translate(829px,129px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 65.2173% { transform: translate(829px,279px); } 100% { transform: translate(829px,279px); } }
@keyframes a17_t { 0% { transform: translate(813.5px,127px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 65.2173% { transform: translate(813.5px,277px); } 100% { transform: translate(813.5px,277px); } }
@keyframes a18_t { 0% { transform: translate(829px,20px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 65.2173% { transform: translate(829px,150px); } 100% { transform: translate(829px,150px); } }
@keyframes a19_t { 0% { transform: translate(813.5px,20px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 65.2173% { transform: translate(813.5px,130px); } 100% { transform: translate(813.5px,130px); } }
@keyframes a20_o { 0% { opacity: 1; } 22.2222% { opacity: 0.5; } 33.3333% { opacity: 0.3; } 62.2222% { opacity: 1; } 100% { opacity: 1; } }
@keyframes a21_o { 0% { opacity: 1; } 25.641% { opacity: 0.5; } 38.4615% { opacity: 0.3; } 71.7949% { opacity: 1; } 100% { opacity: 1; } }
@keyframes a22_o { 0% { opacity: 1; } 25% { opacity: 0.5; } 37.5% { opacity: 0.3; } 70% { opacity: 1; } 100% { opacity: 1; } }
@keyframes a23_t { 0% { transform: translate(1111px,323px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 76.1904% { transform: translate(1381px,323px); } 100% { transform: translate(1381px,323px); } }
@keyframes a24_t { 0% { transform: translate(1111px,314px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 76.1904% { transform: translate(1381px,314px); } 100% { transform: translate(1381px,314px); } }
@keyframes a25_t { 0% { transform: translate(1111px,306px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 76.1904% { transform: translate(1391px,306px); } 100% { transform: translate(1391px,306px); } }
@keyframes a26_t { 0% { transform: translate(1101px,296px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 76.1904% { transform: translate(1371px,296px); } 100% { transform: translate(1371px,296px); } }
@keyframes a27_t { 0% { transform: translate(1101px,287px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 76.1904% { transform: translate(1389px,287px); } 100% { transform: translate(1389px,287px); } }
@keyframes a28_t { 0% { transform: translate(1101px,278px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 76.1904% { transform: translate(1401px,278px); } 100% { transform: translate(1401px,278px); } }
@keyframes a29_t { 0% { transform: translate(1111px,269px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 76.1904% { transform: translate(1401px,269px); } 100% { transform: translate(1401px,269px); } }
@keyframes a30_t { 0% { transform: translate(1102.5px,319px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 66.6666% { transform: translate(1102.5px,549px); } 100% { transform: translate(1102.5px,549px); } }
@keyframes a31_t { 0% { transform: translate(1093px,319px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 66.6666% { transform: translate(1093px,549px); } 100% { transform: translate(1093px,549px); } }
@keyframes a32_t { 0% { transform: translate(1083px,325px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 60% { transform: translate(1083px,555px); } 100% { transform: translate(1083px,555px); } }
@keyframes a33_t { 0% { transform: translate(1073px,319px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 60% { transform: translate(1073px,559px); } 100% { transform: translate(1073px,559px); } }
@keyframes a34_t { 0% { transform: translate(1063px,319px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 60% { transform: translate(1063px,546px); } 100% { transform: translate(1063px,546px); } }
@keyframes a35_t { 0% { transform: translate(1053px,319px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 66.6666% { transform: translate(1053px,519px); } 100% { transform: translate(1053px,519px); } }
@keyframes a36_t { 0% { transform: translate(810px,286px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 66.6666% { transform: translate(1019.38px,286px); } 100% { transform: translate(1019.38px,286px); } }
@keyframes a37_t { 0% { transform: translate(811px,295px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 66.6666% { transform: translate(1026px,295px); } 100% { transform: translate(1026px,295px); } }
@keyframes a38_t { 0% { transform: translate(808px,304px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 66.6666% { transform: translate(1024px,304px); } 100% { transform: translate(1024px,304px); } }
@keyframes a39_t { 0% { transform: translate(829px,129px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 65.2173% { transform: translate(829px,279px); } 100% { transform: translate(829px,279px); } }
@keyframes a40_t { 0% { transform: translate(813.5px,127px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 65.2173% { transform: translate(813.5px,277px); } 100% { transform: translate(813.5px,277px); } }
@keyframes a41_t { 0% { transform: translate(829px,20px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 65.2173% { transform: translate(829px,150px); } 100% { transform: translate(829px,150px); } }
@keyframes a42_t { 0% { transform: translate(813.5px,20px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 65.2173% { transform: translate(813.5px,130px); } 100% { transform: translate(813.5px,130px); } }
@keyframes a43_o { 0% { opacity: 1; } 22.2222% { opacity: 0.5; } 33.3333% { opacity: 0.3; } 62.2222% { opacity: 1; } 100% { opacity: 1; } }
@keyframes a44_o { 0% { opacity: 1; } 25.641% { opacity: 0.5; } 38.4615% { opacity: 0.3; } 71.7949% { opacity: 1; } 100% { opacity: 1; } }
@keyframes a45_o { 0% { opacity: 1; } 25% { opacity: 0.5; } 37.5% { opacity: 0.3; } 70% { opacity: 1; } 100% { opacity: 1; } }
    </style>
