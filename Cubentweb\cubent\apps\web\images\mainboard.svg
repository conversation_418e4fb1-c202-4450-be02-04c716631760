<svg xmlns="http://www.w3.org/2000/svg" width="1512" height="546" viewBox="0 0 1512 546" text-rendering="geometricPrecision" shape-rendering="geometricPrecision" style="white-space: pre; background: #000000;">
    <style>
@keyframes a0_t { 0% { transform: translate(1111px,323px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 76.1904% { transform: translate(1381px,323px); } 100% { transform: translate(1381px,323px); } }
@keyframes a1_t { 0% { transform: translate(1111px,314px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 76.1904% { transform: translate(1381px,314px); } 100% { transform: translate(1381px,314px); } }
@keyframes a2_t { 0% { transform: translate(1111px,306px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 76.1904% { transform: translate(1391px,306px); } 100% { transform: translate(1391px,306px); } }
@keyframes a3_t { 0% { transform: translate(1101px,296px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 76.1904% { transform: translate(1371px,296px); } 100% { transform: translate(1371px,296px); } }
@keyframes a4_t { 0% { transform: translate(1101px,287px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 76.1904% { transform: translate(1389px,287px); } 100% { transform: translate(1389px,287px); } }
@keyframes a5_t { 0% { transform: translate(1101px,278px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 76.1904% { transform: translate(1401px,278px); } 100% { transform: translate(1401px,278px); } }
@keyframes a6_t { 0% { transform: translate(1111px,269px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 76.1904% { transform: translate(1401px,269px); } 100% { transform: translate(1401px,269px); } }
@keyframes a7_t { 0% { transform: translate(1102.5px,319px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 66.6666% { transform: translate(1102.5px,549px); } 100% { transform: translate(1102.5px,549px); } }
@keyframes a8_t { 0% { transform: translate(1093px,319px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 66.6666% { transform: translate(1093px,549px); } 100% { transform: translate(1093px,549px); } }
@keyframes a9_t { 0% { transform: translate(1083px,325px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 60% { transform: translate(1083px,555px); } 100% { transform: translate(1083px,555px); } }
@keyframes a10_t { 0% { transform: translate(1073px,319px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 60% { transform: translate(1073px,559px); } 100% { transform: translate(1073px,559px); } }
@keyframes a11_t { 0% { transform: translate(1063px,319px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 60% { transform: translate(1063px,546px); } 100% { transform: translate(1063px,546px); } }
@keyframes a12_t { 0% { transform: translate(1053px,319px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 66.6666% { transform: translate(1053px,519px); } 100% { transform: translate(1053px,519px); } }
@keyframes a13_t { 0% { transform: translate(810px,286px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 66.6666% { transform: translate(1019.38px,286px); } 100% { transform: translate(1019.38px,286px); } }
@keyframes a14_t { 0% { transform: translate(811px,295px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 66.6666% { transform: translate(1026px,295px); } 100% { transform: translate(1026px,295px); } }
@keyframes a15_t { 0% { transform: translate(808px,304px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 66.6666% { transform: translate(1024px,304px); } 100% { transform: translate(1024px,304px); } }
@keyframes a16_t { 0% { transform: translate(829px,129px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 65.2173% { transform: translate(829px,279px); } 100% { transform: translate(829px,279px); } }
@keyframes a17_t { 0% { transform: translate(813.5px,127px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 65.2173% { transform: translate(813.5px,277px); } 100% { transform: translate(813.5px,277px); } }
@keyframes a18_t { 0% { transform: translate(829px,20px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 65.2173% { transform: translate(829px,150px); } 100% { transform: translate(829px,150px); } }
@keyframes a19_t { 0% { transform: translate(813.5px,20px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 65.2173% { transform: translate(813.5px,130px); } 100% { transform: translate(813.5px,130px); } }
@keyframes a20_o { 0% { opacity: 1; } 22.2222% { opacity: 0.5; } 33.3333% { opacity: 0.3; } 62.2222% { opacity: 1; } 100% { opacity: 1; } }
@keyframes a21_o { 0% { opacity: 1; } 25.641% { opacity: 0.5; } 38.4615% { opacity: 0.3; } 71.7949% { opacity: 1; } 100% { opacity: 1; } }
@keyframes a22_o { 0% { opacity: 1; } 25% { opacity: 0.5; } 37.5% { opacity: 0.3; } 70% { opacity: 1; } 100% { opacity: 1; } }
@keyframes a23_t { 0% { transform: translate(1111px,323px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 76.1904% { transform: translate(1381px,323px); } 100% { transform: translate(1381px,323px); } }
@keyframes a24_t { 0% { transform: translate(1111px,314px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 76.1904% { transform: translate(1381px,314px); } 100% { transform: translate(1381px,314px); } }
@keyframes a25_t { 0% { transform: translate(1111px,306px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 76.1904% { transform: translate(1391px,306px); } 100% { transform: translate(1391px,306px); } }
@keyframes a26_t { 0% { transform: translate(1101px,296px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 76.1904% { transform: translate(1371px,296px); } 100% { transform: translate(1371px,296px); } }
@keyframes a27_t { 0% { transform: translate(1101px,287px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 76.1904% { transform: translate(1389px,287px); } 100% { transform: translate(1389px,287px); } }
@keyframes a28_t { 0% { transform: translate(1101px,278px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 76.1904% { transform: translate(1401px,278px); } 100% { transform: translate(1401px,278px); } }
@keyframes a29_t { 0% { transform: translate(1111px,269px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 76.1904% { transform: translate(1401px,269px); } 100% { transform: translate(1401px,269px); } }
@keyframes a30_t { 0% { transform: translate(1102.5px,319px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 66.6666% { transform: translate(1102.5px,549px); } 100% { transform: translate(1102.5px,549px); } }
@keyframes a31_t { 0% { transform: translate(1093px,319px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 66.6666% { transform: translate(1093px,549px); } 100% { transform: translate(1093px,549px); } }
@keyframes a32_t { 0% { transform: translate(1083px,325px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 60% { transform: translate(1083px,555px); } 100% { transform: translate(1083px,555px); } }
@keyframes a33_t { 0% { transform: translate(1073px,319px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 60% { transform: translate(1073px,559px); } 100% { transform: translate(1073px,559px); } }
@keyframes a34_t { 0% { transform: translate(1063px,319px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 60% { transform: translate(1063px,546px); } 100% { transform: translate(1063px,546px); } }
@keyframes a35_t { 0% { transform: translate(1053px,319px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 66.6666% { transform: translate(1053px,519px); } 100% { transform: translate(1053px,519px); } }
@keyframes a36_t { 0% { transform: translate(810px,286px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 66.6666% { transform: translate(1019.38px,286px); } 100% { transform: translate(1019.38px,286px); } }
@keyframes a37_t { 0% { transform: translate(811px,295px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 66.6666% { transform: translate(1026px,295px); } 100% { transform: translate(1026px,295px); } }
@keyframes a38_t { 0% { transform: translate(808px,304px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 66.6666% { transform: translate(1024px,304px); } 100% { transform: translate(1024px,304px); } }
@keyframes a39_t { 0% { transform: translate(829px,129px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 65.2173% { transform: translate(829px,279px); } 100% { transform: translate(829px,279px); } }
@keyframes a40_t { 0% { transform: translate(813.5px,127px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 65.2173% { transform: translate(813.5px,277px); } 100% { transform: translate(813.5px,277px); } }
@keyframes a41_t { 0% { transform: translate(829px,20px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 65.2173% { transform: translate(829px,150px); } 100% { transform: translate(829px,150px); } }
@keyframes a42_t { 0% { transform: translate(813.5px,20px); animation-timing-function: cubic-bezier(0.55,0.085,0.68,0.53); } 65.2173% { transform: translate(813.5px,130px); } 100% { transform: translate(813.5px,130px); } }
@keyframes a43_o { 0% { opacity: 1; } 22.2222% { opacity: 0.5; } 33.3333% { opacity: 0.3; } 62.2222% { opacity: 1; } 100% { opacity: 1; } }
@keyframes a44_o { 0% { opacity: 1; } 25.641% { opacity: 0.5; } 38.4615% { opacity: 0.3; } 71.7949% { opacity: 1; } 100% { opacity: 1; } }
@keyframes a45_o { 0% { opacity: 1; } 25% { opacity: 0.5; } 37.5% { opacity: 0.3; } 70% { opacity: 1; } 100% { opacity: 1; } }
    </style>
    <defs>
        <symbol id="Symbol-1" preserveAspectRatio="none" width="1512.18" height="545.5" viewBox="0 0 1512.18 545.5" overflow="visible">
            <g fill="none" mask="url(#mask)" transform="translate(756.177,272.625) translate(-756,-272.625)">
                <g id="objects" transform="translate(756,272.625) translate(-756,-272.625)">
                    <g id="objects-2" transform="translate(756,272.625) translate(-756,-272.625)">
                        <rect width="21" height="24" rx="3" fill="white" fill-opacity="0.06" transform="translate(924.5,94) translate(-10.5,-12)"/>
                        <rect width="15" height="6" rx="1.5" fill="white" fill-opacity="0.06" transform="translate(924.5,99) translate(-7.5,-3)"/>
                        <rect width="2" height="8" fill="white" fill-opacity="0.08" transform="translate(551,280) translate(-1,-4)"/>
                        <rect width="2" height="8" fill="white" fill-opacity="0.08" transform="translate(555,280) translate(-1,-4)"/>
                        <rect width="2" height="2" fill="white" fill-opacity="0.08" transform="translate(546,280) translate(-1,-1)"/>
                        <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(546,279.5) translate(-1,-0.5)"/>
                        <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(551,276.5) translate(-1,-0.5)"/>
                        <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(555,276.5) translate(-1,-0.5)"/>
                        <rect width="2" height="8" fill="white" fill-opacity="0.08" transform="translate(570,280) translate(-1,-4)"/>
                        <rect width="2" height="8" fill="white" fill-opacity="0.08" transform="translate(574,280) translate(-1,-4)"/>
                        <rect width="2" height="2" fill="white" fill-opacity="0.08" transform="translate(565,280) translate(-1,-1)"/>
                        <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(565,279.5) translate(-1,-0.5)"/>
                        <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(570,276.5) translate(-1,-0.5)"/>
                        <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(574,276.5) translate(-1,-0.5)"/>
                        <rect width="2" height="8" fill="white" fill-opacity="0.08" transform="translate(589,280) translate(-1,-4)"/>
                        <rect width="2" height="8" fill="white" fill-opacity="0.08" transform="translate(593,280) translate(-1,-4)"/>
                        <rect width="2" height="2" fill="white" fill-opacity="0.08" transform="translate(584,280) translate(-1,-1)"/>
                        <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(584,279.5) translate(-1,-0.5)"/>
                        <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(589,276.5) translate(-1,-0.5)"/>
                        <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(593,276.5) translate(-1,-0.5)"/>
                        <rect width="2" height="8" fill="white" fill-opacity="0.08" transform="translate(589,264) translate(-1,-4)"/>
                        <rect width="2" height="8" fill="white" fill-opacity="0.08" transform="translate(593,264) translate(-1,-4)"/>
                        <rect width="2" height="2" fill="white" fill-opacity="0.08" transform="translate(584,264) translate(-1,-1)"/>
                        <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(584,263.5) translate(-1,-0.5)"/>
                        <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(589,260.5) translate(-1,-0.5)"/>
                        <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(593,260.5) translate(-1,-0.5)"/>
                        <rect width="2" height="8" fill="white" fill-opacity="0.08" transform="translate(616,80) translate(-1,-4)"/>
                        <rect width="2" height="8" fill="white" fill-opacity="0.08" transform="translate(620,80) translate(-1,-4)"/>
                        <rect width="2" height="2" fill="white" fill-opacity="0.08" transform="translate(611,80) translate(-1,-1)"/>
                        <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(611,79.5) translate(-1,-0.5)"/>
                        <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(616,76.5) translate(-1,-0.5)"/>
                        <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(620,76.5) translate(-1,-0.5)"/>
                        <rect width="2" height="8" fill="white" fill-opacity="0.08" transform="translate(1340,106) translate(-1,-4)"/>
                        <rect width="2" height="8" fill="white" fill-opacity="0.08" transform="translate(1344,106) translate(-1,-4)"/>
                        <rect width="2" height="2" fill="white" fill-opacity="0.08" transform="translate(1335,106) translate(-1,-1)"/>
                        <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(1335,105.5) translate(-1,-0.5)"/>
                        <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(1340,102.5) translate(-1,-0.5)"/>
                        <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(1344,102.5) translate(-1,-0.5)"/>
                        <rect width="2" height="8" fill="white" fill-opacity="0.08" transform="translate(635,80) translate(-1,-4)"/>
                        <rect width="2" height="8" fill="white" fill-opacity="0.08" transform="translate(639,80) translate(-1,-4)"/>
                        <rect width="2" height="2" fill="white" fill-opacity="0.08" transform="translate(630,80) translate(-1,-1)"/>
                        <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(630,79.5) translate(-1,-0.5)"/>
                        <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(635,76.5) translate(-1,-0.5)"/>
                        <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(639,76.5) translate(-1,-0.5)"/>
                        <rect width="2" height="8" fill="white" fill-opacity="0.08" transform="translate(1359,106) translate(-1,-4)"/>
                        <rect width="2" height="8" fill="white" fill-opacity="0.08" transform="translate(1363,106) translate(-1,-4)"/>
                        <rect width="2" height="2" fill="white" fill-opacity="0.08" transform="translate(1354,106) translate(-1,-1)"/>
                        <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(1354,105.5) translate(-1,-0.5)"/>
                        <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(1359,102.5) translate(-1,-0.5)"/>
                        <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(1363,102.5) translate(-1,-0.5)"/>
                        <rect width="2" height="8" fill="white" fill-opacity="0.08" transform="translate(654,80) translate(-1,-4)"/>
                        <rect width="2" height="8" fill="white" fill-opacity="0.08" transform="translate(658,80) translate(-1,-4)"/>
                        <rect width="2" height="2" fill="white" fill-opacity="0.08" transform="translate(649,80) translate(-1,-1)"/>
                        <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(649,79.5) translate(-1,-0.5)"/>
                        <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(654,76.5) translate(-1,-0.5)"/>
                        <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(658,76.5) translate(-1,-0.5)"/>
                        <rect width="2" height="8" fill="white" fill-opacity="0.08" transform="translate(1378,106) translate(-1,-4)"/>
                        <rect width="2" height="8" fill="white" fill-opacity="0.08" transform="translate(1382,106) translate(-1,-4)"/>
                        <rect width="2" height="2" fill="white" fill-opacity="0.08" transform="translate(1373,106) translate(-1,-1)"/>
                        <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(1373,105.5) translate(-1,-0.5)"/>
                        <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(1378,102.5) translate(-1,-0.5)"/>
                        <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(1382,102.5) translate(-1,-0.5)"/>
                        <rect width="8" height="2" fill="white" fill-opacity="0.08" transform="translate(814,361) translate(-4,-1)"/>
                        <rect width="8" height="1" fill="white" fill-opacity="0.04" transform="translate(814,360.5) translate(-4,-0.5)"/>
                        <rect width="8" height="2" fill="white" fill-opacity="0.08" transform="translate(814,371) translate(-4,-1)"/>
                        <rect width="8" height="1" fill="white" fill-opacity="0.04" transform="translate(814,370.5) translate(-4,-0.5)"/>
                        <rect width="2" height="4" fill="white" fill-opacity="0.08" transform="translate(812,366) translate(-1,-2)"/>
                        <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(812,364.5) translate(-1,-0.5)"/>
                        <rect width="2" height="4" fill="white" fill-opacity="0.08" transform="translate(816,366) translate(-1,-2)"/>
                        <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(816,364.5) translate(-1,-0.5)"/>
                        <rect width="8" height="2" fill="white" fill-opacity="0.08" transform="translate(331,276) translate(-4,-1)"/>
                        <rect width="8" height="1" fill="white" fill-opacity="0.04" transform="translate(331,275.5) translate(-4,-0.5)"/>
                        <rect width="8" height="2" fill="white" fill-opacity="0.08" transform="translate(331,286) translate(-4,-1)"/>
                        <rect width="8" height="1" fill="white" fill-opacity="0.04" transform="translate(331,285.5) translate(-4,-0.5)"/>
                        <rect width="2" height="4" fill="white" fill-opacity="0.08" transform="translate(329,281) translate(-1,-2)"/>
                        <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(329,279.5) translate(-1,-0.5)"/>
                        <rect width="2" height="4" fill="white" fill-opacity="0.08" transform="translate(333,281) translate(-1,-2)"/>
                        <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(333,279.5) translate(-1,-0.5)"/>
                        <rect width="8" height="2" fill="white" fill-opacity="0.08" transform="translate(1352,415) translate(-4,-1)"/>
                        <rect width="8" height="1" fill="white" fill-opacity="0.04" transform="translate(1352,414.5) translate(-4,-0.5)"/>
                        <rect width="8" height="2" fill="white" fill-opacity="0.08" transform="translate(1352,425) translate(-4,-1)"/>
                        <rect width="8" height="1" fill="white" fill-opacity="0.04" transform="translate(1352,424.5) translate(-4,-0.5)"/>
                        <rect width="2" height="4" fill="white" fill-opacity="0.08" transform="translate(1350,420) translate(-1,-2)"/>
                        <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(1350,418.5) translate(-1,-0.5)"/>
                        <rect width="2" height="4" fill="white" fill-opacity="0.08" transform="translate(1354,420) translate(-1,-2)"/>
                        <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(1354,418.5) translate(-1,-0.5)"/>
                        <rect width="8" height="2" fill="white" fill-opacity="0.08" transform="translate(798,361) translate(-4,-1)"/>
                        <rect width="8" height="1" fill="white" fill-opacity="0.04" transform="translate(798,360.5) translate(-4,-0.5)"/>
                        <rect width="8" height="2" fill="white" fill-opacity="0.08" transform="translate(798,371) translate(-4,-1)"/>
                        <rect width="8" height="1" fill="white" fill-opacity="0.04" transform="translate(798,370.5) translate(-4,-0.5)"/>
                        <rect width="2" height="4" fill="white" fill-opacity="0.08" transform="translate(796,366) translate(-1,-2)"/>
                        <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(796,364.5) translate(-1,-0.5)"/>
                        <rect width="2" height="4" fill="white" fill-opacity="0.08" transform="translate(800,366) translate(-1,-2)"/>
                        <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(800,364.5) translate(-1,-0.5)"/>
                        <rect width="8" height="2" fill="white" fill-opacity="0.08" transform="translate(315,276) translate(-4,-1)"/>
                        <rect width="8" height="1" fill="white" fill-opacity="0.04" transform="translate(315,275.5) translate(-4,-0.5)"/>
                        <rect width="8" height="2" fill="white" fill-opacity="0.08" transform="translate(315,286) translate(-4,-1)"/>
                        <rect width="8" height="1" fill="white" fill-opacity="0.04" transform="translate(315,285.5) translate(-4,-0.5)"/>
                        <rect width="2" height="4" fill="white" fill-opacity="0.08" transform="translate(313,281) translate(-1,-2)"/>
                        <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(313,279.5) translate(-1,-0.5)"/>
                        <rect width="2" height="4" fill="white" fill-opacity="0.08" transform="translate(317,281) translate(-1,-2)"/>
                        <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(317,279.5) translate(-1,-0.5)"/>
                        <rect width="8" height="2" fill="white" fill-opacity="0.08" transform="translate(1336,415) translate(-4,-1)"/>
                        <rect width="8" height="1" fill="white" fill-opacity="0.04" transform="translate(1336,414.5) translate(-4,-0.5)"/>
                        <rect width="8" height="2" fill="white" fill-opacity="0.08" transform="translate(1336,425) translate(-4,-1)"/>
                        <rect width="8" height="1" fill="white" fill-opacity="0.04" transform="translate(1336,424.5) translate(-4,-0.5)"/>
                        <rect width="2" height="4" fill="white" fill-opacity="0.08" transform="translate(1334,420) translate(-1,-2)"/>
                        <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(1334,418.5) translate(-1,-0.5)"/>
                        <rect width="2" height="4" fill="white" fill-opacity="0.08" transform="translate(1338,420) translate(-1,-2)"/>
                        <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(1338,418.5) translate(-1,-0.5)"/>
                        <rect width="8" height="2" fill="white" fill-opacity="0.08" transform="translate(782,361) translate(-4,-1)"/>
                        <rect width="8" height="1" fill="white" fill-opacity="0.04" transform="translate(782,360.5) translate(-4,-0.5)"/>
                        <rect width="8" height="2" fill="white" fill-opacity="0.08" transform="translate(782,371) translate(-4,-1)"/>
                        <rect width="8" height="1" fill="white" fill-opacity="0.04" transform="translate(782,370.5) translate(-4,-0.5)"/>
                        <rect width="2" height="4" fill="white" fill-opacity="0.08" transform="translate(780,366) translate(-1,-2)"/>
                        <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(780,364.5) translate(-1,-0.5)"/>
                        <rect width="2" height="4" fill="white" fill-opacity="0.08" transform="translate(784,366) translate(-1,-2)"/>
                        <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(784,364.5) translate(-1,-0.5)"/>
                        <rect width="8" height="2" fill="white" fill-opacity="0.08" transform="translate(299,276) translate(-4,-1)"/>
                        <rect width="8" height="1" fill="white" fill-opacity="0.04" transform="translate(299,275.5) translate(-4,-0.5)"/>
                        <rect width="8" height="2" fill="white" fill-opacity="0.08" transform="translate(299,286) translate(-4,-1)"/>
                        <rect width="8" height="1" fill="white" fill-opacity="0.04" transform="translate(299,285.5) translate(-4,-0.5)"/>
                        <rect width="2" height="4" fill="white" fill-opacity="0.08" transform="translate(297,281) translate(-1,-2)"/>
                        <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(297,279.5) translate(-1,-0.5)"/>
                        <rect width="2" height="4" fill="white" fill-opacity="0.08" transform="translate(301,281) translate(-1,-2)"/>
                        <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(301,279.5) translate(-1,-0.5)"/>
                        <rect width="8" height="2" fill="white" fill-opacity="0.08" transform="translate(1320,415) translate(-4,-1)"/>
                        <rect width="8" height="1" fill="white" fill-opacity="0.04" transform="translate(1320,414.5) translate(-4,-0.5)"/>
                        <rect width="8" height="2" fill="white" fill-opacity="0.08" transform="translate(1320,425) translate(-4,-1)"/>
                        <rect width="8" height="1" fill="white" fill-opacity="0.04" transform="translate(1320,424.5) translate(-4,-0.5)"/>
                        <rect width="2" height="4" fill="white" fill-opacity="0.08" transform="translate(1318,420) translate(-1,-2)"/>
                        <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(1318,418.5) translate(-1,-0.5)"/>
                        <rect width="2" height="4" fill="white" fill-opacity="0.08" transform="translate(1322,420) translate(-1,-2)"/>
                        <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(1322,418.5) translate(-1,-0.5)"/>
                        <rect width="8" height="2" fill="white" fill-opacity="0.08" transform="translate(720,112) translate(-4,-1)"/>
                        <rect width="8" height="1" fill="white" fill-opacity="0.04" transform="translate(720,111.5) translate(-4,-0.5)"/>
                        <rect width="8" height="2" fill="white" fill-opacity="0.08" transform="translate(720,122) translate(-4,-1)"/>
                        <rect width="8" height="1" fill="white" fill-opacity="0.04" transform="translate(720,121.5) translate(-4,-0.5)"/>
                        <rect width="2" height="4" fill="white" fill-opacity="0.08" transform="translate(718,117) translate(-1,-2)"/>
                        <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(718,115.5) translate(-1,-0.5)"/>
                        <rect width="2" height="4" fill="white" fill-opacity="0.08" transform="translate(722,117) translate(-1,-2)"/>
                        <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(722,115.5) translate(-1,-0.5)"/>
                        <rect width="8" height="2" fill="white" fill-opacity="0.08" transform="translate(500,322) translate(-4,-1)"/>
                        <rect width="8" height="1" fill="white" fill-opacity="0.04" transform="translate(500,321.5) translate(-4,-0.5)"/>
                        <rect width="8" height="2" fill="white" fill-opacity="0.08" transform="translate(500,332) translate(-4,-1)"/>
                        <rect width="8" height="1" fill="white" fill-opacity="0.04" transform="translate(500,331.5) translate(-4,-0.5)"/>
                        <rect width="2" height="4" fill="white" fill-opacity="0.08" transform="translate(498,327) translate(-1,-2)"/>
                        <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(498,325.5) translate(-1,-0.5)"/>
                        <rect width="2" height="4" fill="white" fill-opacity="0.08" transform="translate(502,327) translate(-1,-2)"/>
                        <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(502,325.5) translate(-1,-0.5)"/>
                        <rect width="8" height="2" fill="white" fill-opacity="0.08" transform="translate(1220,202) translate(-4,-1)"/>
                        <rect width="8" height="1" fill="white" fill-opacity="0.04" transform="translate(1220,201.5) translate(-4,-0.5)"/>
                        <rect width="8" height="2" fill="white" fill-opacity="0.08" transform="translate(1220,212) translate(-4,-1)"/>
                        <rect width="8" height="1" fill="white" fill-opacity="0.04" transform="translate(1220,211.5) translate(-4,-0.5)"/>
                        <rect width="2" height="4" fill="white" fill-opacity="0.08" transform="translate(1218,207) translate(-1,-2)"/>
                        <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(1218,205.5) translate(-1,-0.5)"/>
                        <rect width="2" height="4" fill="white" fill-opacity="0.08" transform="translate(1222,207) translate(-1,-2)"/>
                        <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(1222,205.5) translate(-1,-0.5)"/>
                        <rect width="8" height="2" fill="white" fill-opacity="0.08" transform="translate(1220,222) translate(-4,-1)"/>
                        <rect width="8" height="1" fill="white" fill-opacity="0.04" transform="translate(1220,221.5) translate(-4,-0.5)"/>
                        <rect width="8" height="2" fill="white" fill-opacity="0.08" transform="translate(1220,232) translate(-4,-1)"/>
                        <rect width="8" height="1" fill="white" fill-opacity="0.04" transform="translate(1220,231.5) translate(-4,-0.5)"/>
                        <rect width="2" height="4" fill="white" fill-opacity="0.08" transform="translate(1218,227) translate(-1,-2)"/>
                        <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(1218,225.5) translate(-1,-0.5)"/>
                        <rect width="2" height="4" fill="white" fill-opacity="0.08" transform="translate(1222,227) translate(-1,-2)"/>
                        <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(1222,225.5) translate(-1,-0.5)"/>
                        <rect width="8" height="2" fill="white" fill-opacity="0.08" transform="translate(1236,202) translate(-4,-1)"/>
                        <rect width="8" height="1" fill="white" fill-opacity="0.04" transform="translate(1236,201.5) translate(-4,-0.5)"/>
                        <rect width="8" height="2" fill="white" fill-opacity="0.08" transform="translate(1236,212) translate(-4,-1)"/>
                        <rect width="8" height="1" fill="white" fill-opacity="0.04" transform="translate(1236,211.5) translate(-4,-0.5)"/>
                        <rect width="2" height="4" fill="white" fill-opacity="0.08" transform="translate(1234,207) translate(-1,-2)"/>
                        <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(1234,205.5) translate(-1,-0.5)"/>
                        <rect width="2" height="4" fill="white" fill-opacity="0.08" transform="translate(1238,207) translate(-1,-2)"/>
                        <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(1238,205.5) translate(-1,-0.5)"/>
                        <rect width="8" height="2" fill="white" fill-opacity="0.08" transform="translate(516,322) translate(-4,-1)"/>
                        <rect width="8" height="1" fill="white" fill-opacity="0.04" transform="translate(516,321.5) translate(-4,-0.5)"/>
                        <rect width="8" height="2" fill="white" fill-opacity="0.08" transform="translate(516,332) translate(-4,-1)"/>
                        <rect width="8" height="1" fill="white" fill-opacity="0.04" transform="translate(516,331.5) translate(-4,-0.5)"/>
                        <rect width="2" height="4" fill="white" fill-opacity="0.08" transform="translate(514,327) translate(-1,-2)"/>
                        <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(514,325.5) translate(-1,-0.5)"/>
                        <rect width="2" height="4" fill="white" fill-opacity="0.08" transform="translate(518,327) translate(-1,-2)"/>
                        <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(518,325.5) translate(-1,-0.5)"/>
                        <rect width="8" height="2" fill="white" fill-opacity="0.08" transform="translate(500,342) translate(-4,-1)"/>
                        <rect width="8" height="1" fill="white" fill-opacity="0.04" transform="translate(500,341.5) translate(-4,-0.5)"/>
                        <rect width="8" height="2" fill="white" fill-opacity="0.08" transform="translate(500,352) translate(-4,-1)"/>
                        <rect width="8" height="1" fill="white" fill-opacity="0.04" transform="translate(500,351.5) translate(-4,-0.5)"/>
                        <rect width="2" height="4" fill="white" fill-opacity="0.08" transform="translate(498,347) translate(-1,-2)"/>
                        <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(498,345.5) translate(-1,-0.5)"/>
                        <rect width="2" height="4" fill="white" fill-opacity="0.08" transform="translate(502,347) translate(-1,-2)"/>
                        <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(502,345.5) translate(-1,-0.5)"/>
                        <rect width="8" height="8" fill="white" fill-opacity="0.04" transform="translate(668,363) translate(-4,-4)"/>
                        <rect width="7.5" height="7.5" stroke="white" stroke-opacity="0.08" stroke-width="0.5" transform="translate(668,363) translate(-3.75,-3.75)"/>
                        <rect width="2" height="2" fill="white" fill-opacity="0.08" transform="translate(669,364) translate(-1,-1)"/>
                        <rect width="15.5" height="15.5" stroke="white" stroke-opacity="0.08" stroke-width="0.5" transform="translate(668,363) translate(-7.75,-7.75)"/>
                        <rect width="2" height="2" fill="white" fill-opacity="0.08" transform="translate(752,127) translate(-1,-1)"/>
                        <rect width="2" height="2" fill="white" fill-opacity="0.08" transform="translate(758,127) translate(-1,-1)"/>
                        <rect width="2" height="2" fill="white" fill-opacity="0.08" transform="translate(752,133) translate(-1,-1)"/>
                        <rect width="2" height="2" fill="white" fill-opacity="0.08" transform="translate(758,133) translate(-1,-1)"/>
                        <rect width="2" height="2" fill="white" fill-opacity="0.08" transform="translate(752,139) translate(-1,-1)"/>
                        <rect width="2" height="2" fill="white" fill-opacity="0.08" transform="translate(758,139) translate(-1,-1)"/>
                        <rect width="2" height="2" fill="white" fill-opacity="0.08" transform="translate(752,145) translate(-1,-1)"/>
                        <rect width="2" height="2" fill="white" fill-opacity="0.08" transform="translate(758,145) translate(-1,-1)"/>
                        <rect width="2" height="2" fill="white" fill-opacity="0.08" transform="translate(752,151) translate(-1,-1)"/>
                        <rect width="2" height="2" fill="white" fill-opacity="0.08" transform="translate(758,151) translate(-1,-1)"/>
                        <rect width="2" height="2" fill="white" fill-opacity="0.08" transform="translate(669,322) translate(-1,-1)"/>
                        <rect width="15.5" height="15.5" stroke="white" stroke-opacity="0.08" stroke-width="0.5" transform="translate(668,321) translate(-7.75,-7.75)"/>
                        <rect width="8" height="8" fill="white" fill-opacity="0.04" transform="translate(668,321) translate(-4,-4)"/>
                        <rect width="7.5" height="7.5" stroke="white" stroke-opacity="0.08" stroke-width="0.5" transform="translate(668,321) translate(-3.75,-3.75)"/>
                        <rect width="2" height="2" fill="white" fill-opacity="0.08" transform="translate(1354,201) translate(-1,-1)"/>
                        <rect width="15.5" height="15.5" stroke="white" stroke-opacity="0.08" stroke-width="0.5" transform="translate(1353,200) translate(-7.75,-7.75)"/>
                        <rect width="8" height="8" fill="white" fill-opacity="0.04" transform="translate(1353,200) translate(-4,-4)"/>
                        <rect width="7.5" height="7.5" stroke="white" stroke-opacity="0.08" stroke-width="0.5" transform="translate(1353,200) translate(-3.75,-3.75)"/>
                        <rect width="2" height="6" fill="white" fill-opacity="0.08" transform="translate(672,216) translate(-1,-3)"/>
                        <rect width="2" height="6" fill="white" fill-opacity="0.08" transform="translate(677,216) translate(-1,-3)"/>
                        <rect width="2" height="6" fill="white" fill-opacity="0.08" transform="translate(682,216) translate(-1,-3)"/>
                        <rect width="2" height="6" fill="white" fill-opacity="0.08" transform="translate(687,216) translate(-1,-3)"/>
                        <rect width="2" height="6" fill="white" fill-opacity="0.08" transform="translate(692,216) translate(-1,-3)"/>
                        <rect width="2" height="6" fill="white" fill-opacity="0.08" transform="translate(697,216) translate(-1,-3)"/>
                        <rect width="2" height="6" fill="white" fill-opacity="0.08" transform="translate(915,151) translate(-1,-3)"/>
                        <rect width="2" height="6" fill="white" fill-opacity="0.08" transform="translate(920,151) translate(-1,-3)"/>
                        <rect width="2" height="6" fill="white" fill-opacity="0.08" transform="translate(925,151) translate(-1,-3)"/>
                        <rect width="2" height="6" fill="white" fill-opacity="0.08" transform="translate(930,151) translate(-1,-3)"/>
                        <rect width="2" height="6" fill="white" fill-opacity="0.08" transform="translate(935,151) translate(-1,-3)"/>
                        <rect width="2" height="6" fill="white" fill-opacity="0.08" transform="translate(940,151) translate(-1,-3)"/>
                        <rect width="2" height="6" fill="white" fill-opacity="0.08" transform="translate(911,430) translate(-1,-3)"/>
                        <rect width="2" height="6" fill="white" fill-opacity="0.08" transform="translate(916,430) translate(-1,-3)"/>
                        <rect width="2" height="6" fill="white" fill-opacity="0.08" transform="translate(921,430) translate(-1,-3)"/>
                        <rect width="2" height="6" fill="white" fill-opacity="0.08" transform="translate(926,430) translate(-1,-3)"/>
                        <rect width="2" height="6" fill="white" fill-opacity="0.08" transform="translate(931,430) translate(-1,-3)"/>
                        <rect width="2" height="6" fill="white" fill-opacity="0.08" transform="translate(936,430) translate(-1,-3)"/>
                        <rect width="2" height="6" fill="white" fill-opacity="0.08" transform="translate(1217,412) translate(-1,-3)"/>
                        <rect width="2" height="6" fill="white" fill-opacity="0.08" transform="translate(1222,412) translate(-1,-3)"/>
                        <rect width="2" height="6" fill="white" fill-opacity="0.08" transform="translate(1227,412) translate(-1,-3)"/>
                        <rect width="2" height="6" fill="white" fill-opacity="0.08" transform="translate(1232,412) translate(-1,-3)"/>
                        <rect width="2" height="6" fill="white" fill-opacity="0.08" transform="translate(1237,412) translate(-1,-3)"/>
                        <rect width="2" height="6" fill="white" fill-opacity="0.08" transform="translate(1242,412) translate(-1,-3)"/>
                        <rect width="2" height="6" fill="white" fill-opacity="0.08" transform="translate(1025,82) translate(-1,-3)"/>
                        <rect width="2" height="6" fill="white" fill-opacity="0.08" transform="translate(1030,82) translate(-1,-3)"/>
                        <rect width="2" height="6" fill="white" fill-opacity="0.08" transform="translate(1035,82) translate(-1,-3)"/>
                        <rect width="2" height="6" fill="white" fill-opacity="0.08" transform="translate(1040,82) translate(-1,-3)"/>
                        <rect width="2" height="6" fill="white" fill-opacity="0.08" transform="translate(1045,82) translate(-1,-3)"/>
                        <rect width="2" height="6" fill="white" fill-opacity="0.08" transform="translate(1050,82) translate(-1,-3)"/>
                        <path d="M736.25,98.25L591.25,98.25L591.25,20" stroke="white" stroke-opacity="0.06" stroke-width="0.5" transform="translate(663.75,59.125) translate(-663.75,-59.125)"/>
                        <g clip-path="url(#clip0_1398_56)" transform="translate(650,306) translate(-650,-306)">
                            <path d="M518,386C523.333,380.667,626.889,277.111,678,226" stroke="white" stroke-opacity="0.1" stroke-width="0.3" transform="translate(598,306) translate(-598,-306)"/>
                            <path d="M526,386C531.333,380.667,634.889,277.111,686,226" stroke="white" stroke-opacity="0.1" stroke-width="0.3" transform="translate(606,306) translate(-606,-306)"/>
                            <path d="M534,386C539.333,380.667,642.889,277.111,694,226" stroke="white" stroke-opacity="0.1" stroke-width="0.3" transform="translate(614,306) translate(-614,-306)"/>
                            <path d="M542,386C547.333,380.667,650.889,277.111,702,226" stroke="white" stroke-opacity="0.1" stroke-width="0.3" transform="translate(622,306) translate(-622,-306)"/>
                            <path d="M550,386C555.333,380.667,658.889,277.111,710,226" stroke="white" stroke-opacity="0.1" stroke-width="0.3" transform="translate(630,306) translate(-630,-306)"/>
                            <path d="M558,386C563.333,380.667,666.889,277.111,718,226" stroke="white" stroke-opacity="0.1" stroke-width="0.3" transform="translate(638,306) translate(-638,-306)"/>
                            <path d="M566,386C571.333,380.667,674.889,277.111,726,226" stroke="white" stroke-opacity="0.1" stroke-width="0.3" transform="translate(646,306) translate(-646,-306)"/>
                            <path d="M574,386C579.333,380.667,682.889,277.111,734,226" stroke="white" stroke-opacity="0.1" stroke-width="0.3" transform="translate(654,306) translate(-654,-306)"/>
                            <path d="M582,386C587.333,380.667,690.889,277.111,742,226" stroke="white" stroke-opacity="0.1" stroke-width="0.3" transform="translate(662,306) translate(-662,-306)"/>
                            <path d="M590,386C595.333,380.667,698.889,277.111,750,226" stroke="white" stroke-opacity="0.1" stroke-width="0.3" transform="translate(670,306) translate(-670,-306)"/>
                            <path d="M598,386C603.333,380.667,706.889,277.111,758,226" stroke="white" stroke-opacity="0.1" stroke-width="0.3" transform="translate(678,306) translate(-678,-306)"/>
                            <path d="M606,386C611.333,380.667,714.889,277.111,766,226" stroke="white" stroke-opacity="0.1" stroke-width="0.3" transform="translate(686,306) translate(-686,-306)"/>
                            <path d="M614,386C619.333,380.667,722.889,277.111,774,226" stroke="white" stroke-opacity="0.1" stroke-width="0.3" transform="translate(694,306) translate(-694,-306)"/>
                            <path d="M622,386C627.333,380.667,730.889,277.111,782,226" stroke="white" stroke-opacity="0.1" stroke-width="0.3" transform="translate(702,306) translate(-702,-306)"/>
                        </g>
                        <g clip-path="url(#clip1_1398_56)" transform="translate(901,366) translate(-901,-366)">
                            <path d="M769,446C774.333,440.667,877.889,337.111,929,286" stroke="white" stroke-opacity="0.1" stroke-width="0.3" transform="translate(849,366) translate(-849,-366)"/>
                            <path d="M777,446C782.333,440.667,885.889,337.111,937,286" stroke="white" stroke-opacity="0.1" stroke-width="0.3" transform="translate(857,366) translate(-857,-366)"/>
                            <path d="M785,446C790.333,440.667,893.889,337.111,945,286" stroke="white" stroke-opacity="0.1" stroke-width="0.3" transform="translate(865,366) translate(-865,-366)"/>
                            <path d="M793,446C798.333,440.667,901.889,337.111,953,286" stroke="white" stroke-opacity="0.1" stroke-width="0.3" transform="translate(873,366) translate(-873,-366)"/>
                            <path d="M801,446C806.333,440.667,909.889,337.111,961,286" stroke="white" stroke-opacity="0.1" stroke-width="0.3" transform="translate(881,366) translate(-881,-366)"/>
                            <path d="M809,446C814.333,440.667,917.889,337.111,969,286" stroke="white" stroke-opacity="0.1" stroke-width="0.3" transform="translate(889,366) translate(-889,-366)"/>
                            <path d="M817,446C822.333,440.667,925.889,337.111,977,286" stroke="white" stroke-opacity="0.1" stroke-width="0.3" transform="translate(897,366) translate(-897,-366)"/>
                            <path d="M825,446C830.333,440.667,933.889,337.111,985,286" stroke="white" stroke-opacity="0.1" stroke-width="0.3" transform="translate(905,366) translate(-905,-366)"/>
                            <path d="M833,446C838.333,440.667,941.889,337.111,993,286" stroke="white" stroke-opacity="0.1" stroke-width="0.3" transform="translate(913,366) translate(-913,-366)"/>
                            <path d="M841,446C846.333,440.667,949.889,337.111,1001,286" stroke="white" stroke-opacity="0.1" stroke-width="0.3" transform="translate(921,366) translate(-921,-366)"/>
                            <path d="M849,446C854.333,440.667,957.889,337.111,1009,286" stroke="white" stroke-opacity="0.1" stroke-width="0.3" transform="translate(929,366) translate(-929,-366)"/>
                            <path d="M857,446C862.333,440.667,965.889,337.111,1017,286" stroke="white" stroke-opacity="0.1" stroke-width="0.3" transform="translate(937,366) translate(-937,-366)"/>
                            <path d="M865,446C870.333,440.667,973.889,337.111,1025,286" stroke="white" stroke-opacity="0.1" stroke-width="0.3" transform="translate(945,366) translate(-945,-366)"/>
                            <path d="M873,446C878.333,440.667,981.889,337.111,1033,286" stroke="white" stroke-opacity="0.1" stroke-width="0.3" transform="translate(953,366) translate(-953,-366)"/>
                        </g>
                        <path d="M802,299.25L263.604,299.25C259.69,299.25,257.734,299.25,255.892,298.808C254.26,298.416,252.699,297.77,251.267,296.892C249.653,295.903,248.269,294.519,245.502,291.752L0,46.25" stroke="white" stroke-opacity="0.08" stroke-width="0.5" transform="translate(401,172.75) translate(-401,-172.75)"/>
                        <path d="M711,199.25L602.604,199.25C598.69,199.25,596.734,199.25,594.892,199.692C593.26,200.084,591.699,200.73,590.267,201.608C588.653,202.597,587.269,203.981,584.502,206.748L492,299.25" stroke="white" stroke-opacity="0.08" stroke-width="0.5" transform="translate(601.5,249.25) translate(-601.5,-249.25)"/>
                        <path d="M830,384.25L493.604,384.25C489.69,384.25,487.734,384.25,485.892,383.808C484.26,383.416,482.699,382.77,481.267,381.892C479.653,380.903,478.269,379.519,475.502,376.752L398,299.25" stroke="white" stroke-opacity="0.08" stroke-width="0.5" transform="translate(614,341.75) translate(-614,-341.75)"/>
                        <path d="M802,165.25L711.25,165.25L711.25,299" stroke="white" stroke-opacity="0.08" stroke-width="0.5" transform="translate(756.625,232.125) translate(-756.625,-232.125)"/>
                        <path d="M281.25,299L281.25,245.25L441.25,245.25L441.25,299" stroke="white" stroke-opacity="0.08" stroke-width="0.5" transform="translate(361.25,272.125) translate(-361.25,-272.125)"/>
                        <rect width="2" height="2" fill="white" fill-opacity="0.08" transform="translate(420,261) translate(-1,-1)"/>
                        <rect width="2" height="2" fill="white" fill-opacity="0.08" transform="translate(426,261) translate(-1,-1)"/>
                        <rect width="2" height="2" fill="white" fill-opacity="0.08" transform="translate(420,267) translate(-1,-1)"/>
                        <rect width="2" height="2" fill="white" fill-opacity="0.08" transform="translate(426,267) translate(-1,-1)"/>
                        <rect width="2" height="2" fill="white" fill-opacity="0.08" transform="translate(420,273) translate(-1,-1)"/>
                        <rect width="2" height="2" fill="white" fill-opacity="0.08" transform="translate(426,273) translate(-1,-1)"/>
                        <rect width="2" height="2" fill="white" fill-opacity="0.08" transform="translate(420,279) translate(-1,-1)"/>
                        <rect width="2" height="2" fill="white" fill-opacity="0.08" transform="translate(426,279) translate(-1,-1)"/>
                        <rect width="2" height="2" fill="white" fill-opacity="0.08" transform="translate(420,285) translate(-1,-1)"/>
                        <rect width="2" height="2" fill="white" fill-opacity="0.08" transform="translate(426,285) translate(-1,-1)"/>
                        <path d="M1380,395.25L1202.25,395.25L1202.25,529" stroke="white" stroke-opacity="0.08" stroke-width="0.5" transform="translate(1291.12,462.125) translate(-1291.12,-462.125)"/>
                        <path d="M1512,492.25L1334.25,492.25L1334.25,545" stroke="white" stroke-opacity="0.08" stroke-width="0.5" transform="translate(1423.12,518.625) translate(-1423.12,-518.625)"/>
                        <path d="M1262.25,545.25L1440,545.25" stroke="white" stroke-opacity="0.08" stroke-width="0.5" transform="translate(1351.12,545.25) translate(-1351.12,-545.25)"/>
                        <path d="M711.5,214.25L765,214.25" stroke="white" stroke-opacity="0.08" stroke-width="0.5" transform="translate(738.25,214.25) translate(-738.25,-214.25)"/>
                        <rect opacity="0.02" width="49" height="79" rx="0.5" fill="white" transform="translate(738.5,256.5) translate(-24.5,-39.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(717.5,220.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(717.5,226.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(717.5,232.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(717.5,238.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(717.5,244.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.1" width="3" height="3" rx="0.5" fill="white" transform="translate(717.5,250.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(717.5,256.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(717.5,262.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(717.5,268.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(717.5,274.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(717.5,280.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(717.5,286.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(717.5,292.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(724.5,220.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.15" width="3" height="3" rx="0.5" fill="white" transform="translate(724.5,226.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(724.5,232.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(724.5,238.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(724.5,244.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(724.5,250.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(724.5,256.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(724.5,262.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(724.5,268.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.15" width="3" height="3" rx="0.5" fill="white" transform="translate(724.5,274.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(724.5,280.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(724.5,286.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(724.5,292.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(731.5,220.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(731.5,226.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(731.5,232.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(731.5,238.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.1" width="3" height="3" rx="0.5" fill="white" transform="translate(731.5,244.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(731.5,250.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(731.5,256.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(731.5,262.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(731.5,268.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(731.5,274.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.1" width="3" height="3" rx="0.5" fill="white" transform="translate(731.5,280.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(731.5,286.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(731.5,292.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(738.5,220.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(738.5,226.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(738.5,232.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(738.5,238.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(738.5,244.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(738.5,250.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(738.5,256.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(738.5,262.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(738.5,268.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(738.5,274.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(738.5,280.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(738.5,286.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(738.5,292.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(745.5,220.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(745.5,226.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(745.5,232.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.1" width="3" height="3" rx="0.5" fill="white" transform="translate(745.5,238.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(745.5,244.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(745.5,250.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.1" width="3" height="3" rx="0.5" fill="white" transform="translate(745.5,256.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(745.5,262.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(745.5,268.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(745.5,274.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(745.5,280.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(745.5,286.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(745.5,292.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(752.5,220.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.1" width="3" height="3" rx="0.5" fill="white" transform="translate(752.5,226.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(752.5,232.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(752.5,238.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(752.5,244.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(752.5,250.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(752.5,256.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(752.5,262.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.1" width="3" height="3" rx="0.5" fill="white" transform="translate(752.5,268.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(752.5,274.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(752.5,280.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(752.5,286.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(752.5,292.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(759.5,220.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(759.5,226.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(759.5,232.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(759.5,238.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(759.5,244.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(759.5,250.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(759.5,256.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(759.5,262.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(759.5,268.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(759.5,274.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(759.5,280.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(759.5,286.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.15" width="3" height="3" rx="0.5" fill="white" transform="translate(759.5,292.5) translate(-1.5,-1.5)"/>
                        <path d="M765.25,165.5L765.25,299" stroke="white" stroke-opacity="0.08" stroke-width="0.5" transform="translate(765.25,232.25) translate(-765.25,-232.25)"/>
                        <path d="M1014.25,262.25L917.25,262.25L917.25,213.5" stroke="white" stroke-opacity="0.08" stroke-width="0.5" transform="translate(965.75,237.875) translate(-965.75,-237.875)"/>
                        <path d="M1374.25,219.25L1277.25,219.25L1277.25,90.5" stroke="white" stroke-opacity="0.08" stroke-width="0.5" transform="translate(1325.75,154.875) translate(-1325.75,-154.875)"/>
                        <path d="M830.25,338L830.25,416.25L947.25,416.25L947.25,494" stroke="white" stroke-opacity="0.08" stroke-width="0.5" transform="translate(888.75,416) translate(-888.75,-416)"/>
                        <path d="M901,70L901,165" stroke="white" stroke-opacity="0.04" stroke-width="2" transform="translate(901,117.5) translate(-901,-117.5)"/>
                        <path d="M737,70L737,165" stroke="white" stroke-opacity="0.04" stroke-width="2" transform="translate(737,117.5) translate(-737,-117.5)"/>
                        <path d="M647,299.5L647,384" stroke="white" stroke-opacity="0.04" stroke-width="2" transform="translate(647,341.75) translate(-647,-341.75)"/>
                        <path d="M689,299.5L689,384" stroke="white" stroke-opacity="0.04" stroke-width="2" transform="translate(689,341.75) translate(-689,-341.75)"/>
                        <path d="M1268,395.5L1268,480" stroke="white" stroke-opacity="0.04" stroke-width="2" transform="translate(1268,437.75) translate(-1268,-437.75)"/>
                        <path d="M759,384.5L759,498" stroke="white" stroke-opacity="0.04" stroke-width="2" transform="translate(759,441.25) translate(-759,-441.25)"/>
                        <path d="M858,165.25L982.378,165.25C984.348,165.25,985.333,165.25,986.28,165.066C987.119,164.902,987.935,164.632,988.706,164.261C989.575,163.842,990.364,163.253,991.944,162.075L1009.1,149.277C1010.99,147.869,1011.93,147.165,1012.61,146.272C1013.22,145.48,1013.67,144.584,1013.94,143.627C1014.25,142.548,1014.25,141.37,1014.25,139.016L1014.25,70" stroke="white" stroke-opacity="0.08" stroke-width="0.5" transform="translate(936.125,117.625) translate(-936.125,-117.625)"/>
                        <path d="M1014,95.25L1090.25,95.25L1090.25,0" stroke="white" stroke-opacity="0.08" stroke-width="0.5" transform="translate(1052.12,47.625) translate(-1052.12,-47.625)"/>
                        <path d="M963.25,165.25L963.25,213.25L886.25,213.25L886.25,165.25" stroke="white" stroke-opacity="0.08" stroke-width="0.5" transform="translate(924.75,189.25) translate(-924.75,-189.25)"/>
                        <rect opacity="0.02" width="70" height="42" rx="0.5" fill="white" transform="translate(925,189) translate(-35,-21)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(892.5,171.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(892.5,178.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(892.5,185.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(892.5,192.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(892.5,199.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(892.5,206.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(900.5,171.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(900.5,178.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.1" width="3" height="3" rx="0.5" fill="white" transform="translate(900.5,185.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(900.5,192.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(900.5,199.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(900.5,206.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(908.5,171.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(908.5,178.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(908.5,185.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(908.5,192.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(908.5,199.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.15" width="3" height="3" rx="0.5" fill="white" transform="translate(908.5,206.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.1" width="3" height="3" rx="0.5" fill="white" transform="translate(916.5,171.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(916.5,178.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(916.5,185.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(916.5,192.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(916.5,199.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(916.5,206.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(924.5,171.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(924.5,178.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(924.5,185.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.15" width="3" height="3" rx="0.5" fill="white" transform="translate(924.5,192.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(924.5,199.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(924.5,206.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(932.5,171.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(932.5,178.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(932.5,185.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(932.5,192.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(932.5,199.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(932.5,206.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(940.5,171.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(940.5,178.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.15" width="3" height="3" rx="0.5" fill="white" transform="translate(940.5,185.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(940.5,192.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(940.5,199.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(940.5,206.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.1" width="3" height="3" rx="0.5" fill="white" transform="translate(948.5,171.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(948.5,178.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(948.5,185.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(948.5,192.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(948.5,199.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.1" width="3" height="3" rx="0.5" fill="white" transform="translate(948.5,206.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(956.5,171.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(956.5,178.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(956.5,185.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(956.5,192.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(956.5,199.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(956.5,206.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.02" width="70" height="42" rx="0.5" fill="white" transform="translate(1375,519) translate(-35,-21)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1342.5,501.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1342.5,508.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1342.5,515.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1342.5,522.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1342.5,529.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1342.5,536.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1350.5,501.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1350.5,508.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.1" width="3" height="3" rx="0.5" fill="white" transform="translate(1350.5,515.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1350.5,522.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1350.5,529.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1350.5,536.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1358.5,501.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1358.5,508.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1358.5,515.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1358.5,522.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1358.5,529.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.15" width="3" height="3" rx="0.5" fill="white" transform="translate(1358.5,536.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.1" width="3" height="3" rx="0.5" fill="white" transform="translate(1366.5,501.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1366.5,508.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1366.5,515.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1366.5,522.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1366.5,529.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1366.5,536.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1374.5,501.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1374.5,508.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1374.5,515.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.15" width="3" height="3" rx="0.5" fill="white" transform="translate(1374.5,522.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1374.5,529.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1374.5,536.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1382.5,501.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1382.5,508.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1382.5,515.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1382.5,522.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1382.5,529.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1382.5,536.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1390.5,501.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1390.5,508.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.15" width="3" height="3" rx="0.5" fill="white" transform="translate(1390.5,515.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1390.5,522.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1390.5,529.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1390.5,536.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.1" width="3" height="3" rx="0.5" fill="white" transform="translate(1398.5,501.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1398.5,508.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1398.5,515.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1398.5,522.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1398.5,529.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.1" width="3" height="3" rx="0.5" fill="white" transform="translate(1398.5,536.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1406.5,501.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1406.5,508.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1406.5,515.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1406.5,522.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1406.5,529.5) translate(-1.5,-1.5)"/>
                        <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1406.5,536.5) translate(-1.5,-1.5)"/>
                        <path d="M1370,90L1196.59,90C1192.68,90,1190.73,90,1188.89,90.4407C1187.26,90.8314,1185.7,91.4758,1184.27,92.3505C1182.65,93.3369,1181.27,94.7164,1178.51,97.4755L1093.27,182.498C1090.5,185.267,1089.11,186.651,1088.12,188.267C1087.24,189.7,1086.59,191.263,1086.19,192.898C1085.75,194.742,1085.75,196.702,1085.75,200.623L1085.75,233.75" stroke="white" stroke-opacity="0.1" stroke-width="0.5" transform="translate(1227.88,161.875) translate(-1227.88,-161.875)"/>
                    </g>
                    <g id="dots" transform="translate(991.5,261.5) translate(-170.5,-120.5)">
                        <g id="dots-2" filter="url(#filter0_b_1398_56)" transform="translate(264.75,91) translate(-1085.75,-232)">
                            <path d="M1084.5,230.8C1084.5,230.52,1084.5,230.38,1084.55,230.273C1084.6,230.179,1084.68,230.102,1084.77,230.054C1084.88,230,1085.02,230,1085.3,230L1086.2,230C1086.48,230,1086.62,230,1086.73,230.054C1086.82,230.102,1086.9,230.179,1086.95,230.273C1087,230.38,1087,230.52,1087,230.8L1087,234L1084.5,234L1084.5,230.8Z" fill="black" transform="translate(1085.75,232) translate(-1085.75,-232)"/>
                            <path d="M1084.5,230.8C1084.5,230.52,1084.5,230.38,1084.55,230.273C1084.6,230.179,1084.68,230.102,1084.77,230.054C1084.88,230,1085.02,230,1085.3,230L1086.2,230C1086.48,230,1086.62,230,1086.73,230.054C1086.82,230.102,1086.9,230.179,1086.95,230.273C1087,230.38,1087,230.52,1087,230.8L1087,234L1084.5,234L1084.5,230.8Z" fill="url(#Gradient-0)" fill-opacity="0.6" transform="translate(1085.75,232) translate(-1085.75,-232)"/>
                        </g>
                        <g id="dots-3" transform="translate(9,131) translate(-9,-2)">
                            <g filter="url(#filter1_b_1398_56)" transform="translate(1.25,2) translate(-822.25,-272)">
                                <path d="M821,270.8C821,270.52,821,270.38,821.054,270.273C821.102,270.179,821.179,270.102,821.273,270.054C821.38,270,821.52,270,821.8,270L822.7,270C822.98,270,823.12,270,823.227,270.054C823.321,270.102,823.398,270.179,823.446,270.273C823.5,270.38,823.5,270.52,823.5,270.8L823.5,274L821,274L821,270.8Z" fill="black" transform="translate(822.25,272) translate(-822.25,-272)"/>
                                <path d="M821,270.8C821,270.52,821,270.38,821.054,270.273C821.102,270.179,821.179,270.102,821.273,270.054C821.38,270,821.52,270,821.8,270L822.7,270C822.98,270,823.12,270,823.227,270.054C823.321,270.102,823.398,270.179,823.446,270.273C823.5,270.38,823.5,270.52,823.5,270.8L823.5,274L821,274L821,270.8Z" fill="url(#Gradient-1)" fill-opacity="0.6" transform="translate(822.25,272) translate(-822.25,-272)"/>
                            </g>
                            <g filter="url(#filter2_b_1398_56)" transform="translate(16.75,2) translate(-837.75,-272)">
                                <path d="M836.5,270.8C836.5,270.52,836.5,270.38,836.554,270.273C836.602,270.179,836.679,270.102,836.773,270.054C836.88,270,837.02,270,837.3,270L838.2,270C838.48,270,838.62,270,838.727,270.054C838.821,270.102,838.898,270.179,838.946,270.273C839,270.38,839,270.52,839,270.8L839,274L836.5,274L836.5,270.8Z" fill="black" transform="translate(837.75,272) translate(-837.75,-272)"/>
                                <path d="M836.5,270.8C836.5,270.52,836.5,270.38,836.554,270.273C836.602,270.179,836.679,270.102,836.773,270.054C836.88,270,837.02,270,837.3,270L838.2,270C838.48,270,838.62,270,838.727,270.054C838.821,270.102,838.898,270.179,838.946,270.273C839,270.38,839,270.52,839,270.8L839,274L836.5,274L836.5,270.8Z" fill="url(#Gradient-2)" fill-opacity="0.6" transform="translate(837.75,272) translate(-837.75,-272)"/>
                            </g>
                        </g>
                        <g id="dots-4" transform="translate(9,46) translate(-9,-2)">
                            <g filter="url(#filter3_b_1398_56)" transform="translate(1.25,2) translate(-822.25,-187)">
                                <path d="M821,185L823.5,185L823.5,188.2C823.5,188.48,823.5,188.62,823.446,188.727C823.398,188.821,823.321,188.898,823.227,188.946C823.12,189,822.98,189,822.7,189L821.8,189C821.52,189,821.38,189,821.273,188.946C821.179,188.898,821.102,188.821,821.054,188.727C821,188.62,821,188.48,821,188.2L821,185Z" fill="black" transform="translate(822.25,187) translate(-822.25,-187)"/>
                                <path d="M821,185L823.5,185L823.5,188.2C823.5,188.48,823.5,188.62,823.446,188.727C823.398,188.821,823.321,188.898,823.227,188.946C823.12,189,822.98,189,822.7,189L821.8,189C821.52,189,821.38,189,821.273,188.946C821.179,188.898,821.102,188.821,821.054,188.727C821,188.62,821,188.48,821,188.2L821,185Z" fill="url(#Gradient-3)" fill-opacity="0.6" transform="translate(822.25,187) translate(-822.25,-187)"/>
                            </g>
                            <g filter="url(#filter4_b_1398_56)" transform="translate(16.75,2) translate(-837.75,-187)">
                                <path d="M836.5,185L839,185L839,188.2C839,188.48,839,188.62,838.946,188.727C838.898,188.821,838.821,188.898,838.727,188.946C838.62,189,838.48,189,838.2,189L837.3,189C837.02,189,836.88,189,836.773,188.946C836.679,188.898,836.602,188.821,836.554,188.727C836.5,188.62,836.5,188.48,836.5,188.2L836.5,185Z" fill="black" transform="translate(837.75,187) translate(-837.75,-187)"/>
                                <path d="M836.5,185L839,185L839,188.2C839,188.48,839,188.62,838.946,188.727C838.898,188.821,838.821,188.898,838.727,188.946C838.62,189,838.48,189,838.2,189L837.3,189C837.02,189,836.88,189,836.773,188.946C836.679,188.898,836.602,188.821,836.554,188.727C836.5,188.62,836.5,188.48,836.5,188.2L836.5,185Z" fill="url(#Gradient-4)" fill-opacity="0.6" transform="translate(837.75,187) translate(-837.75,-187)"/>
                            </g>
                        </g>
                        <g id="dots-5" transform="translate(265,239) translate(-26,-2)">
                            <g filter="url(#filter5_b_1398_56)" transform="translate(41.25,2) translate(-1101.25,-380)">
                                <path d="M1100,378L1102.5,378L1102.5,381.2C1102.5,381.48,1102.5,381.62,1102.45,381.727C1102.4,381.821,1102.32,381.898,1102.23,381.946C1102.12,382,1101.98,382,1101.7,382L1100.8,382C1100.52,382,1100.38,382,1100.27,381.946C1100.18,381.898,1100.1,381.821,1100.05,381.727C1100,381.62,1100,381.48,1100,381.2L1100,378Z" fill="black" transform="translate(1101.25,380) translate(-1101.25,-380)"/>
                                <path d="M1100,378L1102.5,378L1102.5,381.2C1102.5,381.48,1102.5,381.62,1102.45,381.727C1102.4,381.821,1102.32,381.898,1102.23,381.946C1102.12,382,1101.98,382,1101.7,382L1100.8,382C1100.52,382,1100.38,382,1100.27,381.946C1100.18,381.898,1100.1,381.821,1100.05,381.727C1100,381.62,1100,381.48,1100,381.2L1100,378Z" fill="url(#Gradient-5)" fill-opacity="0.6" transform="translate(1101.25,380) translate(-1101.25,-380)"/>
                            </g>
                            <g filter="url(#filter6_b_1398_56)" transform="translate(31.25,2) translate(-1091.25,-380)">
                                <path d="M1090,378L1092.5,378L1092.5,381.2C1092.5,381.48,1092.5,381.62,1092.45,381.727C1092.4,381.821,1092.32,381.898,1092.23,381.946C1092.12,382,1091.98,382,1091.7,382L1090.8,382C1090.52,382,1090.38,382,1090.27,381.946C1090.18,381.898,1090.1,381.821,1090.05,381.727C1090,381.62,1090,381.48,1090,381.2L1090,378Z" fill="black" transform="translate(1091.25,380) translate(-1091.25,-380)"/>
                                <path d="M1090,378L1092.5,378L1092.5,381.2C1092.5,381.48,1092.5,381.62,1092.45,381.727C1092.4,381.821,1092.32,381.898,1092.23,381.946C1092.12,382,1091.98,382,1091.7,382L1090.8,382C1090.52,382,1090.38,382,1090.27,381.946C1090.18,381.898,1090.1,381.821,1090.05,381.727C1090,381.62,1090,381.48,1090,381.2L1090,378Z" fill="url(#Gradient-6)" fill-opacity="0.6" transform="translate(1091.25,380) translate(-1091.25,-380)"/>
                            </g>
                            <g filter="url(#filter7_b_1398_56)" transform="translate(21.25,2) translate(-1081.25,-380)">
                                <path d="M1080,378L1082.5,378L1082.5,381.2C1082.5,381.48,1082.5,381.62,1082.45,381.727C1082.4,381.821,1082.32,381.898,1082.23,381.946C1082.12,382,1081.98,382,1081.7,382L1080.8,382C1080.52,382,1080.38,382,1080.27,381.946C1080.18,381.898,1080.1,381.821,1080.05,381.727C1080,381.62,1080,381.48,1080,381.2L1080,378Z" fill="black" transform="translate(1081.25,380) translate(-1081.25,-380)"/>
                                <path d="M1080,378L1082.5,378L1082.5,381.2C1082.5,381.48,1082.5,381.62,1082.45,381.727C1082.4,381.821,1082.32,381.898,1082.23,381.946C1082.12,382,1081.98,382,1081.7,382L1080.8,382C1080.52,382,1080.38,382,1080.27,381.946C1080.18,381.898,1080.1,381.821,1080.05,381.727C1080,381.62,1080,381.48,1080,381.2L1080,378Z" fill="url(#Gradient-7)" fill-opacity="0.6" transform="translate(1081.25,380) translate(-1081.25,-380)"/>
                            </g>
                            <g filter="url(#filter8_b_1398_56)" transform="translate(11.25,2) translate(-1071.25,-380)">
                                <path d="M1070,378L1072.5,378L1072.5,381.2C1072.5,381.48,1072.5,381.62,1072.45,381.727C1072.4,381.821,1072.32,381.898,1072.23,381.946C1072.12,382,1071.98,382,1071.7,382L1070.8,382C1070.52,382,1070.38,382,1070.27,381.946C1070.18,381.898,1070.1,381.821,1070.05,381.727C1070,381.62,1070,381.48,1070,381.2L1070,378Z" fill="black" transform="translate(1071.25,380) translate(-1071.25,-380)"/>
                                <path d="M1070,378L1072.5,378L1072.5,381.2C1072.5,381.48,1072.5,381.62,1072.45,381.727C1072.4,381.821,1072.32,381.898,1072.23,381.946C1072.12,382,1071.98,382,1071.7,382L1070.8,382C1070.52,382,1070.38,382,1070.27,381.946C1070.18,381.898,1070.1,381.821,1070.05,381.727C1070,381.62,1070,381.48,1070,381.2L1070,378Z" fill="url(#Gradient-8)" fill-opacity="0.6" transform="translate(1071.25,380) translate(-1071.25,-380)"/>
                            </g>
                            <g filter="url(#filter9_b_1398_56)" transform="translate(1.25,2) translate(-1061.25,-380)">
                                <path d="M1060,378L1062.5,378L1062.5,381.2C1062.5,381.48,1062.5,381.62,1062.45,381.727C1062.4,381.821,1062.32,381.898,1062.23,381.946C1062.12,382,1061.98,382,1061.7,382L1060.8,382C1060.52,382,1060.38,382,1060.27,381.946C1060.18,381.898,1060.1,381.821,1060.05,381.727C1060,381.62,1060,381.48,1060,381.2L1060,378Z" fill="black" transform="translate(1061.25,380) translate(-1061.25,-380)"/>
                                <path d="M1060,378L1062.5,378L1062.5,381.2C1062.5,381.48,1062.5,381.62,1062.45,381.727C1062.4,381.821,1062.32,381.898,1062.23,381.946C1062.12,382,1061.98,382,1061.7,382L1060.8,382C1060.52,382,1060.38,382,1060.27,381.946C1060.18,381.898,1060.1,381.821,1060.05,381.727C1060,381.62,1060,381.48,1060,381.2L1060,378Z" fill="url(#Gradient-9)" fill-opacity="0.6" transform="translate(1061.25,380) translate(-1061.25,-380)"/>
                            </g>
                            <g filter="url(#filter10_b_1398_56)" transform="translate(50.75,2) translate(-1110.75,-380)">
                                <path d="M1109.5,378L1112,378L1112,381.2C1112,381.48,1112,381.62,1111.95,381.727C1111.9,381.821,1111.82,381.898,1111.73,381.946C1111.62,382,1111.48,382,1111.2,382L1110.3,382C1110.02,382,1109.88,382,1109.77,381.946C1109.68,381.898,1109.6,381.821,1109.55,381.727C1109.5,381.62,1109.5,381.48,1109.5,381.2L1109.5,378Z" fill="black" transform="translate(1110.75,380) translate(-1110.75,-380)"/>
                                <path d="M1109.5,378L1112,378L1112,381.2C1112,381.48,1112,381.62,1111.95,381.727C1111.9,381.821,1111.82,381.898,1111.73,381.946C1111.62,382,1111.48,382,1111.2,382L1110.3,382C1110.02,382,1109.88,382,1109.77,381.946C1109.68,381.898,1109.6,381.821,1109.55,381.727C1109.5,381.62,1109.5,381.48,1109.5,381.2L1109.5,378Z" fill="url(#Gradient-10)" fill-opacity="0.6" transform="translate(1110.75,380) translate(-1110.75,-380)"/>
                            </g>
                        </g>
                        <g id="dots-6" transform="translate(191,164.75) translate(-2,-10.75)">
                            <g filter="url(#filter11_b_1398_56)" transform="translate(2,1.25) translate(-1012,-296.25)">
                                <path d="M1010,295.8C1010,295.52,1010,295.38,1010.05,295.273C1010.1,295.179,1010.18,295.102,1010.27,295.054C1010.38,295,1010.52,295,1010.8,295L1014,295L1014,297.5L1010.8,297.5C1010.52,297.5,1010.38,297.5,1010.27,297.446C1010.18,297.398,1010.1,297.321,1010.05,297.227C1010,297.12,1010,296.98,1010,296.7L1010,295.8Z" fill="black" transform="translate(1012,296.25) translate(-1012,-296.25)"/>
                                <path d="M1010,295.8C1010,295.52,1010,295.38,1010.05,295.273C1010.1,295.179,1010.18,295.102,1010.27,295.054C1010.38,295,1010.52,295,1010.8,295L1014,295L1014,297.5L1010.8,297.5C1010.52,297.5,1010.38,297.5,1010.27,297.446C1010.18,297.398,1010.1,297.321,1010.05,297.227C1010,297.12,1010,296.98,1010,296.7L1010,295.8Z" fill="url(#Gradient-11)" fill-opacity="0.6" transform="translate(1012,296.25) translate(-1012,-296.25)"/>
                            </g>
                            <g filter="url(#filter12_b_1398_56)" transform="translate(2,10.25) translate(-1012,-305.25)">
                                <path d="M1010,304.8C1010,304.52,1010,304.38,1010.05,304.273C1010.1,304.179,1010.18,304.102,1010.27,304.054C1010.38,304,1010.52,304,1010.8,304L1014,304L1014,306.5L1010.8,306.5C1010.52,306.5,1010.38,306.5,1010.27,306.446C1010.18,306.398,1010.1,306.321,1010.05,306.227C1010,306.12,1010,305.98,1010,305.7L1010,304.8Z" fill="black" transform="translate(1012,305.25) translate(-1012,-305.25)"/>
                                <path d="M1010,304.8C1010,304.52,1010,304.38,1010.05,304.273C1010.1,304.179,1010.18,304.102,1010.27,304.054C1010.38,304,1010.52,304,1010.8,304L1014,304L1014,306.5L1010.8,306.5C1010.52,306.5,1010.38,306.5,1010.27,306.446C1010.18,306.398,1010.1,306.321,1010.05,306.227C1010,306.12,1010,305.98,1010,305.7L1010,304.8Z" fill="url(#Gradient-12)" fill-opacity="0.6" transform="translate(1012,305.25) translate(-1012,-305.25)"/>
                            </g>
                            <g filter="url(#filter13_b_1398_56)" transform="translate(2,20.25) translate(-1012,-315.25)">
                                <path d="M1010,314.8C1010,314.52,1010,314.38,1010.05,314.273C1010.1,314.179,1010.18,314.102,1010.27,314.054C1010.38,314,1010.52,314,1010.8,314L1014,314L1014,316.5L1010.8,316.5C1010.52,316.5,1010.38,316.5,1010.27,316.446C1010.18,316.398,1010.1,316.321,1010.05,316.227C1010,316.12,1010,315.98,1010,315.7L1010,314.8Z" fill="black" transform="translate(1012,315.25) translate(-1012,-315.25)"/>
                                <path d="M1010,314.8C1010,314.52,1010,314.38,1010.05,314.273C1010.1,314.179,1010.18,314.102,1010.27,314.054C1010.38,314,1010.52,314,1010.8,314L1014,314L1014,316.5L1010.8,316.5C1010.52,316.5,1010.38,316.5,1010.27,316.446C1010.18,316.398,1010.1,316.321,1010.05,316.227C1010,316.12,1010,315.98,1010,315.7L1010,314.8Z" fill="url(#Gradient-13)" fill-opacity="0.6" transform="translate(1012,315.25) translate(-1012,-315.25)"/>
                            </g>
                        </g>
                        <g id="dots-7" transform="translate(39,164.25) translate(-2,-10.25)">
                            <g filter="url(#filter14_b_1398_56)" transform="translate(2,1.25) translate(-860,-296.25)">
                                <path d="M858,295L861.2,295C861.48,295,861.62,295,861.727,295.054C861.821,295.102,861.898,295.179,861.946,295.273C862,295.38,862,295.52,862,295.8L862,296.7C862,296.98,862,297.12,861.946,297.227C861.898,297.321,861.821,297.398,861.727,297.446C861.62,297.5,861.48,297.5,861.2,297.5L858,297.5L858,295Z" fill="black" transform="translate(860,296.25) translate(-860,-296.25)"/>
                                <path d="M858,295L861.2,295C861.48,295,861.62,295,861.727,295.054C861.821,295.102,861.898,295.179,861.946,295.273C862,295.38,862,295.52,862,295.8L862,296.7C862,296.98,862,297.12,861.946,297.227C861.898,297.321,861.821,297.398,861.727,297.446C861.62,297.5,861.48,297.5,861.2,297.5L858,297.5L858,295Z" fill="url(#Gradient-14)" fill-opacity="0.6" transform="translate(860,296.25) translate(-860,-296.25)"/>
                            </g>
                            <g filter="url(#filter15_b_1398_56)" transform="translate(2,10.25) translate(-860,-305.25)">
                                <path d="M858,304L861.2,304C861.48,304,861.62,304,861.727,304.054C861.821,304.102,861.898,304.179,861.946,304.273C862,304.38,862,304.52,862,304.8L862,305.7C862,305.98,862,306.12,861.946,306.227C861.898,306.321,861.821,306.398,861.727,306.446C861.62,306.5,861.48,306.5,861.2,306.5L858,306.5L858,304Z" fill="black" transform="translate(860,305.25) translate(-860,-305.25)"/>
                                <path d="M858,304L861.2,304C861.48,304,861.62,304,861.727,304.054C861.821,304.102,861.898,304.179,861.946,304.273C862,304.38,862,304.52,862,304.8L862,305.7C862,305.98,862,306.12,861.946,306.227C861.898,306.321,861.821,306.398,861.727,306.446C861.62,306.5,861.48,306.5,861.2,306.5L858,306.5L858,304Z" fill="url(#Gradient-15)" fill-opacity="0.6" transform="translate(860,305.25) translate(-860,-305.25)"/>
                            </g>
                            <g filter="url(#filter16_b_1398_56)" transform="translate(2,19.25) translate(-860,-314.25)">
                                <path d="M858,313L861.2,313C861.48,313,861.62,313,861.727,313.054C861.821,313.102,861.898,313.179,861.946,313.273C862,313.38,862,313.52,862,313.8L862,314.7C862,314.98,862,315.12,861.946,315.227C861.898,315.321,861.821,315.398,861.727,315.446C861.62,315.5,861.48,315.5,861.2,315.5L858,315.5L858,313Z" fill="black" transform="translate(860,314.25) translate(-860,-314.25)"/>
                                <path d="M858,313L861.2,313C861.48,313,861.62,313,861.727,313.054C861.821,313.102,861.898,313.179,861.946,313.273C862,313.38,862,313.52,862,313.8L862,314.7C862,314.98,862,315.12,861.946,315.227C861.898,315.321,861.821,315.398,861.727,315.446C861.62,315.5,861.48,315.5,861.2,315.5L858,315.5L858,313Z" fill="url(#Gradient-16)" fill-opacity="0.6" transform="translate(860,314.25) translate(-860,-314.25)"/>
                            </g>
                        </g>
                        <g id="dots-8" transform="translate(9,2) translate(-9,-2)">
                            <g filter="url(#filter19_b_1398_56)" transform="translate(1.25,2) translate(-822.25,-143)">
                                <path d="M821,141.8C821,141.52,821,141.38,821.054,141.273C821.102,141.179,821.179,141.102,821.273,141.054C821.38,141,821.52,141,821.8,141L822.7,141C822.98,141,823.12,141,823.227,141.054C823.321,141.102,823.398,141.179,823.446,141.273C823.5,141.38,823.5,141.52,823.5,141.8L823.5,145L821,145L821,141.8Z" fill="black" transform="translate(822.25,143) translate(-822.25,-143)"/>
                                <path d="M821,141.8C821,141.52,821,141.38,821.054,141.273C821.102,141.179,821.179,141.102,821.273,141.054C821.38,141,821.52,141,821.8,141L822.7,141C822.98,141,823.12,141,823.227,141.054C823.321,141.102,823.398,141.179,823.446,141.273C823.5,141.38,823.5,141.52,823.5,141.8L823.5,145L821,145L821,141.8Z" fill="url(#Gradient-17)" fill-opacity="0.6" transform="translate(822.25,143) translate(-822.25,-143)"/>
                            </g>
                            <g filter="url(#filter20_b_1398_56)" transform="translate(16.75,2) translate(-837.75,-143)">
                                <path d="M836.5,141.8C836.5,141.52,836.5,141.38,836.554,141.273C836.602,141.179,836.679,141.102,836.773,141.054C836.88,141,837.02,141,837.3,141L838.2,141C838.48,141,838.62,141,838.727,141.054C838.821,141.102,838.898,141.179,838.946,141.273C839,141.38,839,141.52,839,141.8L839,145L836.5,145L836.5,141.8Z" fill="black" transform="translate(837.75,143) translate(-837.75,-143)"/>
                                <path d="M836.5,141.8C836.5,141.52,836.5,141.38,836.554,141.273C836.602,141.179,836.679,141.102,836.773,141.054C836.88,141,837.02,141,837.3,141L838.2,141C838.48,141,838.62,141,838.727,141.054C838.821,141.102,838.898,141.179,838.946,141.273C839,141.38,839,141.52,839,141.8L839,145L836.5,145L836.5,141.8Z" fill="url(#Gradient-18)" fill-opacity="0.6" transform="translate(837.75,143) translate(-837.75,-143)"/>
                            </g>
                        </g>
                        <g id="dots-9" transform="translate(339,165.25) translate(-2,-28.25)">
                            <g filter="url(#filter21_b_1398_56)" transform="translate(2,38.25) translate(-1160,-316.25)">
                                <path d="M1158,315L1161.2,315C1161.48,315,1161.62,315,1161.73,315.054C1161.82,315.102,1161.9,315.179,1161.95,315.273C1162,315.38,1162,315.52,1162,315.8L1162,316.7C1162,316.98,1162,317.12,1161.95,317.227C1161.9,317.321,1161.82,317.398,1161.73,317.446C1161.62,317.5,1161.48,317.5,1161.2,317.5L1158,317.5L1158,315Z" fill="black" transform="translate(1160,316.25) translate(-1160,-316.25)"/>
                                <path d="M1158,315L1161.2,315C1161.48,315,1161.62,315,1161.73,315.054C1161.82,315.102,1161.9,315.179,1161.95,315.273C1162,315.38,1162,315.52,1162,315.8L1162,316.7C1162,316.98,1162,317.12,1161.95,317.227C1161.9,317.321,1161.82,317.398,1161.73,317.446C1161.62,317.5,1161.48,317.5,1161.2,317.5L1158,317.5L1158,315Z" fill="url(#Gradient-19)" fill-opacity="0.6" transform="translate(1160,316.25) translate(-1160,-316.25)"/>
                            </g>
                            <g filter="url(#filter22_b_1398_56)" transform="translate(2,28.25) translate(-1160,-306.25)">
                                <path d="M1158,305L1161.2,305C1161.48,305,1161.62,305,1161.73,305.054C1161.82,305.102,1161.9,305.179,1161.95,305.273C1162,305.38,1162,305.52,1162,305.8L1162,306.7C1162,306.98,1162,307.12,1161.95,307.227C1161.9,307.321,1161.82,307.398,1161.73,307.446C1161.62,307.5,1161.48,307.5,1161.2,307.5L1158,307.5L1158,305Z" fill="black" transform="translate(1160,306.25) translate(-1160,-306.25)"/>
                                <path d="M1158,305L1161.2,305C1161.48,305,1161.62,305,1161.73,305.054C1161.82,305.102,1161.9,305.179,1161.95,305.273C1162,305.38,1162,305.52,1162,305.8L1162,306.7C1162,306.98,1162,307.12,1161.95,307.227C1161.9,307.321,1161.82,307.398,1161.73,307.446C1161.62,307.5,1161.48,307.5,1161.2,307.5L1158,307.5L1158,305Z" fill="url(#Gradient-20)" fill-opacity="0.6" transform="translate(1160,306.25) translate(-1160,-306.25)"/>
                            </g>
                            <g filter="url(#filter23_b_1398_56)" transform="translate(2,19.25) translate(-1160,-297.25)">
                                <path d="M1158,296L1161.2,296C1161.48,296,1161.62,296,1161.73,296.054C1161.82,296.102,1161.9,296.179,1161.95,296.273C1162,296.38,1162,296.52,1162,296.8L1162,297.7C1162,297.98,1162,298.12,1161.95,298.227C1161.9,298.321,1161.82,298.398,1161.73,298.446C1161.62,298.5,1161.48,298.5,1161.2,298.5L1158,298.5L1158,296Z" fill="black" transform="translate(1160,297.25) translate(-1160,-297.25)"/>
                                <path d="M1158,296L1161.2,296C1161.48,296,1161.62,296,1161.73,296.054C1161.82,296.102,1161.9,296.179,1161.95,296.273C1162,296.38,1162,296.52,1162,296.8L1162,297.7C1162,297.98,1162,298.12,1161.95,298.227C1161.9,298.321,1161.82,298.398,1161.73,298.446C1161.62,298.5,1161.48,298.5,1161.2,298.5L1158,298.5L1158,296Z" fill="url(#Gradient-21)" fill-opacity="0.6" transform="translate(1160,297.25) translate(-1160,-297.25)"/>
                            </g>
                            <g filter="url(#filter24_b_1398_56)" transform="translate(2,10.25) translate(-1160,-288.25)">
                                <path d="M1158,287L1161.2,287C1161.48,287,1161.62,287,1161.73,287.054C1161.82,287.102,1161.9,287.179,1161.95,287.273C1162,287.38,1162,287.52,1162,287.8L1162,288.7C1162,288.98,1162,289.12,1161.95,289.227C1161.9,289.321,1161.82,289.398,1161.73,289.446C1161.62,289.5,1161.48,289.5,1161.2,289.5L1158,289.5L1158,287Z" fill="black" transform="translate(1160,288.25) translate(-1160,-288.25)"/>
                                <path d="M1158,287L1161.2,287C1161.48,287,1161.62,287,1161.73,287.054C1161.82,287.102,1161.9,287.179,1161.95,287.273C1162,287.38,1162,287.52,1162,287.8L1162,288.7C1162,288.98,1162,289.12,1161.95,289.227C1161.9,289.321,1161.82,289.398,1161.73,289.446C1161.62,289.5,1161.48,289.5,1161.2,289.5L1158,289.5L1158,287Z" fill="url(#Gradient-22)" fill-opacity="0.6" transform="translate(1160,288.25) translate(-1160,-288.25)"/>
                            </g>
                            <g filter="url(#filter25_b_1398_56)" transform="translate(2,1.25) translate(-1160,-279.25)">
                                <path d="M1158,278L1161.2,278C1161.48,278,1161.62,278,1161.73,278.054C1161.82,278.102,1161.9,278.179,1161.95,278.273C1162,278.38,1162,278.52,1162,278.8L1162,279.7C1162,279.98,1162,280.12,1161.95,280.227C1161.9,280.321,1161.82,280.398,1161.73,280.446C1161.62,280.5,1161.48,280.5,1161.2,280.5L1158,280.5L1158,278Z" fill="black" transform="translate(1160,279.25) translate(-1160,-279.25)"/>
                                <path d="M1158,278L1161.2,278C1161.48,278,1161.62,278,1161.73,278.054C1161.82,278.102,1161.9,278.179,1161.95,278.273C1162,278.38,1162,278.52,1162,278.8L1162,279.7C1162,279.98,1162,280.12,1161.95,280.227C1161.9,280.321,1161.82,280.398,1161.73,280.446C1161.62,280.5,1161.48,280.5,1161.2,280.5L1158,280.5L1158,278Z" fill="url(#Gradient-23)" fill-opacity="0.6" transform="translate(1160,279.25) translate(-1160,-279.25)"/>
                            </g>
                            <g filter="url(#filter26_b_1398_56)" transform="translate(2,46.25) translate(-1160,-324.25)">
                                <path d="M1158,323L1161.2,323C1161.48,323,1161.62,323,1161.73,323.054C1161.82,323.102,1161.9,323.179,1161.95,323.273C1162,323.38,1162,323.52,1162,323.8L1162,324.7C1162,324.98,1162,325.12,1161.95,325.227C1161.9,325.321,1161.82,325.398,1161.73,325.446C1161.62,325.5,1161.48,325.5,1161.2,325.5L1158,325.5L1158,323Z" fill="black" transform="translate(1160,324.25) translate(-1160,-324.25)"/>
                                <path d="M1158,323L1161.2,323C1161.48,323,1161.62,323,1161.73,323.054C1161.82,323.102,1161.9,323.179,1161.95,323.273C1162,323.38,1162,323.52,1162,323.8L1162,324.7C1162,324.98,1162,325.12,1161.95,325.227C1161.9,325.321,1161.82,325.398,1161.73,325.446C1161.62,325.5,1161.48,325.5,1161.2,325.5L1158,325.5L1158,323Z" fill="url(#Gradient-24)" fill-opacity="0.6" transform="translate(1160,324.25) translate(-1160,-324.25)"/>
                            </g>
                            <g filter="url(#filter27_b_1398_56)" transform="translate(2,55.25) translate(-1160,-333.25)">
                                <path d="M1158,332L1161.2,332C1161.48,332,1161.62,332,1161.73,332.054C1161.82,332.102,1161.9,332.179,1161.95,332.273C1162,332.38,1162,332.52,1162,332.8L1162,333.7C1162,333.98,1162,334.12,1161.95,334.227C1161.9,334.321,1161.82,334.398,1161.73,334.446C1161.62,334.5,1161.48,334.5,1161.2,334.5L1158,334.5L1158,332Z" fill="black" transform="translate(1160,333.25) translate(-1160,-333.25)"/>
                                <path d="M1158,332L1161.2,332C1161.48,332,1161.62,332,1161.73,332.054C1161.82,332.102,1161.9,332.179,1161.95,332.273C1162,332.38,1162,332.52,1162,332.8L1162,333.7C1162,333.98,1162,334.12,1161.95,334.227C1161.9,334.321,1161.82,334.398,1161.73,334.446C1161.62,334.5,1161.48,334.5,1161.2,334.5L1158,334.5L1158,332Z" fill="url(#Gradient-25)" fill-opacity="0.6" transform="translate(1160,333.25) translate(-1160,-333.25)"/>
                            </g>
                        </g>
                    </g>
                    <g id="chip" filter="url(#filter28_d_1398_56)" transform="translate(1086,306) translate(-1086,-306)">
                        <g clip-path="url(#clip3_1398_56)" transform="translate(1086,306) translate(-1086,-306)">
                            <rect width="144" height="144" rx="3" fill="white" fill-opacity="0.05" shape-rendering="crispEdges" transform="translate(1086,306) translate(-72,-72)"/>
                            <rect width="144" height="144" rx="3" fill="url(#Gradient-26)" fill-opacity="0.6" shape-rendering="crispEdges" transform="translate(1086,306) translate(-72,-72)"/>
                            <path d="M1048.84,308.28C1048.84,312.6,1051.5,315.24,1056.74,315.24C1061.99,315.24,1064.63,312.6,1064.63,308.28L1064.63,298.92L1061.03,298.92L1061.03,308.016C1061.03,310.752,1060.02,311.736,1056.74,311.736C1053.47,311.736,1052.44,310.752,1052.44,308.016L1052.44,298.92L1048.84,298.92L1048.84,308.28Z" fill="url(#Gradient-27)" transform="translate(1056.73,307.08) translate(-1056.73,-307.08)"/>
                            <path d="M1081.56,315L1085.16,315L1085.16,310.656L1087.92,310.656L1090.92,315L1095.17,315L1090.85,308.784L1095.24,302.976L1090.97,302.976L1087.9,307.44L1085.16,307.44L1085.16,298.92L1081.56,298.92L1081.56,315Z" fill="url(#Gradient-27)" transform="translate(1088.4,306.96) translate(-1088.4,-306.96)"/>
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M1102.5,315.24C1098.49,315.24,1095.76,313.272,1095.76,309C1095.76,305.16,1098.47,302.736,1102.43,302.736C1106.36,302.736,1108.98,304.8,1108.98,308.568C1108.98,308.932,1108.95,309.21,1108.91,309.533C1108.9,309.593,1108.89,309.656,1108.88,309.72L1099.09,309.72C1099.19,311.568,1100.05,312.36,1102.38,312.36C1104.52,312.36,1105.26,311.808,1105.26,310.776L1105.26,310.536L1108.86,310.536L1108.86,310.8C1108.86,313.416,1106.32,315.24,1102.5,315.24ZM1102.36,305.544C1100.22,305.544,1099.31,306.264,1099.14,307.824L1105.52,307.824C1105.43,306.24,1104.47,305.544,1102.36,305.544Z" fill="url(#Gradient-27)" transform="translate(1102.37,308.988) translate(-1102.37,-308.988)"/>
                            <path d="M1112.81,319.08L1110.84,319.08L1110.84,315.816L1113.79,315.816C1114.68,315.816,1115.07,315.576,1115.31,315.048L1109.43,302.976L1113.51,302.976L1116.05,308.472L1117.06,311.304L1117.3,311.304L1118.26,308.448L1120.54,302.976L1124.55,302.976L1118.59,315.864C1117.44,318.384,1115.83,319.08,1112.81,319.08Z" fill="url(#Gradient-27)" transform="translate(1116.99,311.028) translate(-1116.99,-311.028)"/>
                            <path d="M1070.04,315L1066.44,315L1066.44,302.976L1069.78,302.976L1069.78,306.672L1070,306.672C1070.31,304.656,1071.75,302.736,1074.89,302.736C1078.18,302.736,1079.74,304.848,1079.74,307.512L1079.74,315L1076.14,315L1076.14,308.712C1076.14,306.792,1075.35,305.976,1073.14,305.976C1070.86,305.976,1070.04,306.888,1070.04,308.928L1070.04,315Z" fill="url(#Gradient-27)" transform="translate(1073.09,308.868) translate(-1073.09,-308.868)"/>
                            <path d="M1048.84,308.28C1048.84,312.6,1051.5,315.24,1056.74,315.24C1059.41,315.24,1061.4,314.559,1062.72,313.323C1061.41,314.717,1059.33,315.49,1056.49,315.49C1051.25,315.49,1048.59,312.85,1048.59,308.53L1048.59,299.17L1048.84,299.17L1048.84,308.28Z" fill="white" fill-opacity="0.15" transform="translate(1055.65,307.33) translate(-1055.65,-307.33)"/>
                            <path d="M1061.03,299.17L1060.78,299.17L1060.78,308.266C1060.78,309.474,1060.59,310.341,1060.08,310.932C1060.77,310.345,1061.03,309.407,1061.03,308.016L1061.03,299.17Z" fill="white" fill-opacity="0.15" transform="translate(1060.55,305.051) translate(-1060.55,-305.051)"/>
                            <path d="M1084.91,315L1081.56,315L1081.56,299.17L1081.31,299.17L1081.31,315.25L1084.91,315.25L1084.91,315Z" fill="white" fill-opacity="0.15" transform="translate(1083.11,307.21) translate(-1083.11,-307.21)"/>
                            <path d="M1085.16,310.906L1087.67,310.906L1090.67,315.25L1094.92,315.25L1094.75,315L1090.92,315L1087.92,310.656L1085.16,310.656L1085.16,310.906Z" fill="white" fill-opacity="0.15" transform="translate(1090.04,312.953) translate(-1090.04,-312.953)"/>
                            <path d="M1090.8,303.226L1090.72,303.226L1087.82,307.44L1087.9,307.44L1090.8,303.226Z" fill="white" fill-opacity="0.15" transform="translate(1089.31,305.333) translate(-1089.31,-305.333)"/>
                            <path d="M1102.5,315.24C1098.49,315.24,1095.76,313.272,1095.76,309C1095.76,307.236,1096.33,305.771,1097.33,304.703C1096.17,305.788,1095.51,307.345,1095.51,309.25C1095.51,313.522,1098.24,315.49,1102.25,315.49C1104.64,315.49,1106.52,314.777,1107.6,313.604C1106.5,314.628,1104.71,315.24,1102.5,315.24Z" fill="white" fill-opacity="0.15" transform="translate(1101.55,310.096) translate(-1101.55,-310.096)"/>
                            <path d="M1105.26,310.786L1105.01,310.786L1105.01,311.026C1105.01,311.352,1104.94,311.63,1104.77,311.857C1105.11,311.597,1105.26,311.238,1105.26,310.786Z" fill="white" fill-opacity="0.15" transform="translate(1105.01,311.322) translate(-1105.01,-311.322)"/>
                            <path d="M1099.11,309.97L1108.63,309.97C1108.64,309.906,1108.65,309.844,1108.66,309.783C1108.66,309.762,1108.66,309.741,1108.66,309.72L1099.09,309.72C1099.1,309.806,1099.1,309.889,1099.11,309.97Z" fill="white" fill-opacity="0.15" transform="translate(1103.88,309.845) translate(-1103.88,-309.845)"/>
                            <path d="M1099.66,306.426C1100.18,305.999,1100.97,305.794,1102.11,305.794C1104.1,305.794,1105.07,306.416,1105.25,307.824L1105.52,307.824C1105.43,306.24,1104.47,305.544,1102.36,305.544C1101.01,305.544,1100.16,305.828,1099.66,306.426Z" fill="white" fill-opacity="0.15" transform="translate(1102.59,306.684) translate(-1102.59,-306.684)"/>
                            <path d="M1112.81,319.08L1110.84,319.08L1110.84,316.066L1110.59,316.066L1110.59,319.33L1112.56,319.33C1114.75,319.33,1116.2,318.965,1117.27,317.807C1116.22,318.768,1114.83,319.08,1112.81,319.08Z" fill="white" fill-opacity="0.15" transform="translate(1113.93,317.698) translate(-1113.93,-317.698)"/>
                            <path d="M1120.43,303.226L1120.29,303.226L1118.01,308.698L1117.13,311.304L1117.3,311.304L1118.26,308.448L1120.43,303.226Z" fill="white" fill-opacity="0.15" transform="translate(1118.78,307.265) translate(-1118.78,-307.265)"/>
                            <path d="M1115.31,315.048L1109.55,303.226L1109.18,303.226L1115.06,315.298C1115.01,315.395,1114.96,315.482,1114.91,315.56C1115.08,315.437,1115.21,315.267,1115.31,315.048Z" fill="white" fill-opacity="0.15" transform="translate(1112.24,309.393) translate(-1112.24,-309.393)"/>
                            <path d="M1069.79,315L1066.44,315L1066.44,303.226L1066.19,303.226L1066.19,315.25L1069.79,315.25L1069.79,315Z" fill="white" fill-opacity="0.15" transform="translate(1067.99,309.238) translate(-1067.99,-309.238)"/>
                            <path d="M1069.79,306.672L1070,306.672C1070.14,305.77,1070.5,304.888,1071.14,304.188C1070.41,304.86,1069.98,305.748,1069.79,306.672Z" fill="white" fill-opacity="0.15" transform="translate(1070.46,305.43) translate(-1070.46,-305.43)"/>
                            <path d="M1073.14,305.976C1071.9,305.976,1071.09,306.248,1070.61,306.826C1071.1,306.419,1071.84,306.226,1072.89,306.226C1075.1,306.226,1075.89,307.042,1075.89,308.962L1075.89,315.25L1079.49,315.25L1079.49,315L1076.14,315L1076.14,308.712C1076.14,306.792,1075.35,305.976,1073.14,305.976Z" fill="white" fill-opacity="0.15" transform="translate(1075.05,310.613) translate(-1075.05,-310.613)"/>
                            <path d="M1048.84,299.17L1048.84,298.92L1052.44,298.92L1052.44,308.016C1052.44,310.752,1053.47,311.736,1056.74,311.736C1058.35,311.736,1059.42,311.498,1060.08,310.932C1059.45,311.679,1058.32,311.986,1056.49,311.986C1053.22,311.986,1052.19,311.002,1052.19,308.266L1052.19,299.17L1048.84,299.17Z" fill="black" fill-opacity="0.64" transform="translate(1054.46,305.453) translate(-1054.46,-305.453)"/>
                            <path d="M1061.03,299.17L1061.03,298.92L1064.63,298.92L1064.63,308.28C1064.63,310.406,1063.99,312.125,1062.72,313.323C1063.83,312.141,1064.38,310.512,1064.38,308.53L1064.38,299.17L1061.03,299.17Z" fill="black" fill-opacity="0.64" transform="translate(1062.83,306.121) translate(-1062.83,-306.121)"/>
                            <path d="M1084.91,315L1085.16,315L1085.16,310.906L1084.91,310.906L1084.91,315Z" fill="black" fill-opacity="0.64" transform="translate(1085.03,312.953) translate(-1085.03,-312.953)"/>
                            <path d="M1081.56,299.17L1081.56,298.92L1085.16,298.92L1085.16,307.44L1087.82,307.44L1087.65,307.69L1084.91,307.69L1084.91,299.17L1081.56,299.17Z" fill="black" fill-opacity="0.64" transform="translate(1084.69,303.305) translate(-1084.69,-303.305)"/>
                            <path d="M1090.8,303.226L1094.99,303.226L1090.6,309.034L1094.75,315L1095.17,315L1090.85,308.784L1095.24,302.976L1090.97,302.976L1090.8,303.226Z" fill="black" fill-opacity="0.64" transform="translate(1092.92,308.988) translate(-1092.92,-308.988)"/>
                            <path d="M1097.33,304.703C1098.5,303.601,1100.18,302.986,1102.18,302.986C1106.11,302.986,1108.73,305.05,1108.73,308.818C1108.73,309.158,1108.7,309.423,1108.66,309.72L1108.88,309.72C1108.89,309.656,1108.9,309.594,1108.91,309.533C1108.95,309.211,1108.98,308.932,1108.98,308.568C1108.98,304.8,1106.36,302.736,1102.43,302.736C1100.29,302.736,1098.51,303.445,1097.33,304.703Z" fill="black" fill-opacity="0.64" transform="translate(1103.15,306.228) translate(-1103.15,-306.228)"/>
                            <path d="M1107.6,313.604C1108.41,312.861,1108.86,311.9,1108.86,310.8L1108.86,310.536L1105.26,310.536L1105.26,310.776C1105.26,310.779,1105.26,310.783,1105.26,310.786L1108.61,310.786L1108.61,311.05C1108.61,312.031,1108.25,312.9,1107.6,313.604Z" fill="black" fill-opacity="0.64" transform="translate(1107.06,312.07) translate(-1107.06,-312.07)"/>
                            <path d="M1104.77,311.857C1104.33,312.188,1103.57,312.36,1102.38,312.36C1100.16,312.36,1099.27,311.64,1099.11,309.97L1098.84,309.97C1098.94,311.818,1099.8,312.61,1102.13,312.61C1103.59,312.61,1104.4,312.351,1104.77,311.857Z" fill="black" fill-opacity="0.64" transform="translate(1101.8,311.29) translate(-1101.8,-311.29)"/>
                            <path d="M1099.66,306.426C1099.37,306.78,1099.2,307.244,1099.14,307.824L1105.25,307.824C1105.26,307.905,1105.27,307.988,1105.27,308.074L1098.89,308.074C1098.97,307.346,1099.21,306.8,1099.66,306.426Z" fill="black" fill-opacity="0.64" transform="translate(1102.08,307.25) translate(-1102.08,-307.25)"/>
                            <path d="M1110.84,316.066L1113.54,316.066C1114.27,316.066,1114.66,315.906,1114.91,315.56C1114.66,315.734,1114.31,315.816,1113.79,315.816L1110.84,315.816L1110.84,316.066Z" fill="black" fill-opacity="0.64" transform="translate(1112.88,315.813) translate(-1112.88,-315.813)"/>
                            <path d="M1117.27,317.807C1117.78,317.332,1118.21,316.698,1118.59,315.864L1124.55,302.976L1120.54,302.976L1120.43,303.226L1124.3,303.226L1118.34,316.114C1118.03,316.809,1117.67,317.366,1117.27,317.807Z" fill="black" fill-opacity="0.64" transform="translate(1120.91,310.391) translate(-1120.91,-310.391)"/>
                            <path d="M1117.13,311.304L1117.05,311.554L1116.81,311.554L1115.8,308.722L1113.26,303.226L1109.55,303.226L1109.43,302.976L1113.51,302.976L1116.05,308.472L1117.06,311.304L1117.13,311.304Z" fill="black" fill-opacity="0.64" transform="translate(1113.28,307.265) translate(-1113.28,-307.265)"/>
                            <path d="M1069.79,315L1070.04,315L1070.04,308.928C1070.04,308.001,1070.21,307.307,1070.61,306.826C1070.03,307.304,1069.79,308.076,1069.79,309.178L1069.79,315Z" fill="black" fill-opacity="0.64" transform="translate(1070.2,310.913) translate(-1070.2,-310.913)"/>
                            <path d="M1066.44,303.226L1069.53,303.226L1069.53,306.922L1069.75,306.922C1069.76,306.838,1069.77,306.755,1069.79,306.672L1069.78,306.672L1069.78,302.976L1066.44,302.976L1066.44,303.226Z" fill="black" fill-opacity="0.64" transform="translate(1068.12,304.949) translate(-1068.12,-304.949)"/>
                            <path d="M1071.14,304.188C1071.94,303.322,1073.15,302.736,1074.89,302.736C1078.18,302.736,1079.74,304.848,1079.74,307.512L1079.74,315L1079.49,315L1079.49,307.762C1079.49,305.098,1077.93,302.986,1074.64,302.986C1073.08,302.986,1071.94,303.461,1071.14,304.188Z" fill="black" fill-opacity="0.64" transform="translate(1075.44,308.868) translate(-1075.44,-308.868)"/>
                        </g>
                        <rect width="143.25" height="143.25" rx="2.625" stroke="url(#Gradient-28)" stroke-opacity="0.13" stroke-width="0.75" shape-rendering="crispEdges" transform="translate(1086.01,306) translate(-71.625,-71.625)"/>
                    </g>
                    <g id="chip-2" filter="url(#filter30_d_1398_56)" transform="translate(830,306) translate(-830,-306)">
                        <g clip-path="url(#clip4_1398_56)" transform="translate(830,306) translate(-830,-306)">
                            <rect width="56" height="64" rx="3" fill="url(#Gradient-29)" fill-opacity="0.6" shape-rendering="crispEdges" transform="translate(830,306) translate(-28,-32)"/>
                            <rect width="56" height="64" rx="3" fill="white" fill-opacity="0.05" shape-rendering="crispEdges" transform="translate(830,306) translate(-28,-32)"/>
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M830,297L829.965,297L829.965,297C829.534,297,829.279,297,829.059,297.022C826.922,297.232,825.232,298.922,825.022,301.059C825,301.279,825,301.534,825,301.965L825,302L825,305L824.1,305L824.081,305C823.817,305,823.59,305,823.402,305.015C823.205,305.031,823.008,305.067,822.819,305.163C822.537,305.307,822.307,305.537,822.163,305.819C822.067,306.008,822.031,306.205,822.015,306.402C822,306.59,822,306.817,822,307.081L822,307.081L822,307.1L822,313.9L822,313.919L822,313.919C822,314.183,822,314.41,822.015,314.598C822.031,314.795,822.067,314.992,822.163,315.181C822.307,315.463,822.537,315.693,822.819,315.837C823.008,315.933,823.205,315.969,823.402,315.985C823.59,316,823.817,316,824.081,316L824.1,316L835.9,316L835.919,316C836.183,316,836.41,316,836.598,315.985C836.795,315.969,836.992,315.933,837.181,315.837C837.463,315.693,837.693,315.463,837.837,315.181C837.933,314.992,837.969,314.795,837.985,314.598C838,314.41,838,314.183,838,313.919L838,313.9L838,307.1L838,307.081C838,306.817,838,306.59,837.985,306.402C837.969,306.205,837.933,306.008,837.837,305.819C837.693,305.537,837.463,305.307,837.181,305.163C836.992,305.067,836.795,305.031,836.598,305.015C836.41,305,836.183,305,835.919,305L835.9,305L835,305L835,302L835,301.965C835,301.534,835,301.279,834.978,301.059C834.768,298.922,833.078,297.232,830.941,297.022C830.721,297,830.466,297,830.035,297L830.035,297L830,297ZM829.157,298.017C829.322,298.001,829.524,298,830,298C830.476,298,830.678,298.001,830.843,298.017C832.505,298.181,833.819,299.495,833.983,301.157C833.999,301.322,834,301.524,834,302L834,305L826,305L826,302C826,301.524,826.001,301.322,826.017,301.157C826.181,299.495,827.495,298.181,829.157,298.017ZM823.273,306.055C823.298,306.042,823.348,306.023,823.484,306.012C823.626,306,823.812,306,824.1,306L835.9,306C836.188,306,836.374,306,836.516,306.012C836.652,306.023,836.702,306.042,836.727,306.055C836.821,306.102,836.898,306.179,836.946,306.273C836.958,306.298,836.977,306.348,836.988,306.484C837,306.626,837,306.812,837,307.1L837,313.9C837,314.188,837,314.374,836.988,314.516C836.977,314.652,836.958,314.702,836.946,314.727C836.898,314.821,836.821,314.898,836.727,314.946C836.702,314.958,836.652,314.977,836.516,314.988C836.374,315,836.188,315,835.9,315L824.1,315C823.812,315,823.626,315,823.484,314.988C823.348,314.977,823.298,314.958,823.273,314.946C823.179,314.898,823.102,314.821,823.054,314.727C823.042,314.702,823.023,314.652,823.012,314.516C823,314.374,823,314.188,823,313.9L823,307.1C823,306.812,823,306.626,823.012,306.484C823.023,306.348,823.042,306.298,823.054,306.273C823.102,306.179,823.179,306.102,823.273,306.055Z" fill="url(#Gradient-30)" fill-opacity="0.2" transform="translate(830,306.5) translate(-830,-306.5)"/>
                        </g>
                        <rect width="55.25" height="63.25" rx="2.625" stroke="url(#Gradient-31)" stroke-opacity="0.13" stroke-width="0.75" shape-rendering="crispEdges" transform="translate(830,306) translate(-27.625,-31.625)"/>
                    </g>
                    <g id="chip-3" transform="translate(830,165) translate(-28,-20)">
                        <g filter="url(#filter17_d_1398_56)" transform="translate(28,20) translate(-830,-165)">
                            <rect width="56" height="40" rx="3" fill="url(#Gradient-32)" fill-opacity="0.6" shape-rendering="crispEdges" transform="translate(830,165) translate(-28,-20)"/>
                        </g>
                        <g clip-path="url(#clip2_1398_56)" transform="translate(28,20) translate(-830,-165)">
                            <rect width="56" height="40" rx="3" fill="white" fill-opacity="0.05" transform="translate(830,165) translate(-28,-20)"/>
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M822.081,157L822.1,157L825,157L825,158L822.1,158C821.812,158,821.626,158,821.484,158.012C821.348,158.023,821.298,158.042,821.273,158.054C821.179,158.102,821.102,158.179,821.054,158.273C821.042,158.298,821.023,158.348,821.012,158.484C821,158.626,821,158.812,821,159.1L821,162L820,162L820,159.1L820,159.081L820,159.081C820,158.817,820,158.59,820.015,158.402C820.031,158.205,820.067,158.008,820.163,157.819C820.307,157.537,820.537,157.307,820.819,157.163C821.008,157.067,821.205,157.031,821.402,157.015C821.59,157,821.817,157,822.081,157L822.081,157ZM838.516,158.012C838.374,158,838.188,158,837.9,158L835,158L835,157L837.9,157L837.919,157L837.919,157C838.183,157,838.41,157,838.598,157.015C838.795,157.031,838.992,157.067,839.181,157.163C839.463,157.307,839.693,157.537,839.837,157.819C839.933,158.008,839.969,158.205,839.985,158.402C840,158.59,840,158.817,840,159.081L840,159.1L840,162L839,162L839,159.1C839,158.812,839,158.626,838.988,158.484C838.977,158.348,838.958,158.298,838.946,158.273C838.898,158.179,838.821,158.102,838.727,158.054C838.702,158.042,838.652,158.023,838.516,158.012ZM821,169L821,171.9C821,172.188,821,172.374,821.012,172.516C821.023,172.652,821.042,172.702,821.054,172.727C821.102,172.821,821.179,172.898,821.273,172.946C821.298,172.958,821.348,172.977,821.484,172.988C821.626,173,821.812,173,822.1,173L825,173L825,174L822.1,174L822.081,174C821.817,174,821.59,174,821.402,173.985C821.205,173.969,821.008,173.933,820.819,173.837C820.537,173.693,820.307,173.463,820.163,173.181C820.067,172.992,820.031,172.795,820.015,172.598C820,172.41,820,172.183,820,171.919L820,171.919L820,171.9L820,169L821,169ZM839,171.9L839,169L840,169L840,171.9L840,171.919C840,172.183,840,172.41,839.985,172.598C839.969,172.795,839.933,172.992,839.837,173.181C839.693,173.463,839.463,173.693,839.181,173.837C838.992,173.933,838.795,173.969,838.598,173.985C838.41,174,838.183,174,837.919,174L837.9,174L835,174L835,173L837.9,173C838.188,173,838.374,173,838.516,172.988C838.652,172.977,838.702,172.958,838.727,172.946C838.821,172.898,838.898,172.821,838.946,172.727C838.958,172.702,838.977,172.652,838.988,172.516C839,172.374,839,172.188,839,171.9ZM826.5,161C826.776,161,827,161.224,827,161.5L827,162.5C827,162.776,826.776,163,826.5,163C826.224,163,826,162.776,826,162.5L826,161.5C826,161.224,826.224,161,826.5,161ZM831,163.5C831,163.224,830.776,163,830.5,163C830.224,163,830,163.224,830,163.5L830,164.75C830,164.888,829.888,165,829.75,165L829.5,165C829.224,165,829,165.224,829,165.5C829,165.776,829.224,166,829.5,166L829.75,166C830.44,166,831,165.44,831,164.75L831,163.5ZM826.85,168.143C826.654,167.951,826.34,167.952,826.146,168.146C825.951,168.342,825.951,168.658,826.146,168.854L826.5,168.5C826.146,168.854,826.147,168.854,826.147,168.854L826.147,168.854L826.148,168.855L826.151,168.858L826.156,168.863L826.173,168.879C826.186,168.891,826.203,168.906,826.224,168.924C826.267,168.961,826.328,169.009,826.407,169.065C826.565,169.176,826.797,169.318,827.109,169.457C827.737,169.736,828.681,170,830,170C831.319,170,832.263,169.736,832.891,169.457C833.203,169.318,833.435,169.176,833.593,169.065C833.672,169.009,833.733,168.961,833.776,168.924C833.797,168.906,833.814,168.891,833.827,168.879L833.844,168.863L833.849,168.858L833.852,168.855L833.853,168.854L833.853,168.854C833.853,168.854,833.854,168.854,833.5,168.5L833.854,168.854C834.049,168.658,834.049,168.342,833.854,168.146C833.66,167.952,833.346,167.951,833.15,168.143L833.149,168.144C833.146,168.147,833.139,168.153,833.129,168.162C833.107,168.18,833.07,168.21,833.016,168.248C832.909,168.324,832.734,168.432,832.484,168.543C831.987,168.764,831.181,169,830,169C828.819,169,828.013,168.764,827.516,168.543C827.266,168.432,827.091,168.324,826.984,168.248C826.93,168.21,826.893,168.18,826.871,168.162C826.861,168.153,826.854,168.147,826.851,168.144L826.85,168.143L826.85,168.143ZM833.149,168.144L833.148,168.145L833.147,168.146L833.149,168.144ZM834,161.5C834,161.224,833.776,161,833.5,161C833.224,161,833,161.224,833,161.5L833,162.5C833,162.776,833.224,163,833.5,163C833.776,163,834,162.776,834,162.5L834,161.5Z" fill="url(#Gradient-33)" fill-opacity="0.2" transform="translate(830,165.5) translate(-830,-165.5)"/>
                        </g>
                        <rect width="55.25" height="39.25" rx="2.625" stroke="url(#Gradient-34)" stroke-opacity="0.13" stroke-width="0.75" transform="translate(28,20) translate(-27.625,-19.625)"/>
                    </g>
                </g>
                <g id="lines-5" transform="translate(1268.5,306) translate(-109.5,-37)">
                    <g mask="url(#mask14_1398_56)" transform="translate(109.5,64) translate(-1268.5,-333)">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M1378,333.5L1159,333.5L1159,333L1378,333L1378,333.5Z" fill="url(#Gradient-35)" fill-opacity="0.1" transform="translate(1268.5,333.25) translate(-1268.5,-333.25)"/>
                        <rect width="45" height="20" fill="url(#Gradient-36)" transform="translate(1133.5,333) translate(-22.5,-10)" style="animation: 2.1s linear 5.7s infinite both a0_t;"/>
                    </g>
                    <g mask="url(#mask15_1398_56)" transform="translate(109.5,55) translate(-1268.5,-324)">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M1378,324.5L1159,324.5L1159,324L1378,324L1378,324.5Z" fill="url(#Gradient-37)" fill-opacity="0.1" transform="translate(1268.5,324.25) translate(-1268.5,-324.25)"/>
                        <rect width="45" height="20" fill="url(#Gradient-38)" transform="translate(1133.5,324) translate(-22.5,-10)" style="animation: 2.1s linear 5.2s infinite both a1_t;"/>
                    </g>
                    <g mask="url(#mask16_1398_56)" transform="translate(109.5,47) translate(-1268.5,-316)">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M1378,316.5L1159,316.5L1159,316L1378,316L1378,316.5Z" fill="url(#Gradient-39)" fill-opacity="0.1" transform="translate(1268.5,316.25) translate(-1268.5,-316.25)"/>
                        <rect width="45" height="20" fill="url(#Gradient-40)" transform="translate(1133.5,316) translate(-22.5,-10)" style="animation: 2.1s linear 5.4s infinite both a2_t;"/>
                    </g>
                    <g mask="url(#mask17_1398_56)" transform="translate(109.5,37) translate(-1268.5,-306)">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M1378,306.5L1159,306.5L1159,306L1378,306L1378,306.5Z" fill="url(#Gradient-41)" fill-opacity="0.1" transform="translate(1268.5,306.25) translate(-1268.5,-306.25)"/>
                        <rect width="45" height="20" fill="url(#Gradient-42)" transform="translate(1123.5,306) translate(-22.5,-10)" style="animation: 2.1s linear 6.1s infinite both a3_t;"/>
                    </g>
                    <g mask="url(#mask18_1398_56)" transform="translate(109.5,28) translate(-1268.5,-297)">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M1378,297.5L1159,297.5L1159,297L1378,297L1378,297.5Z" fill="url(#Gradient-43)" fill-opacity="0.1" transform="translate(1268.5,297.25) translate(-1268.5,-297.25)"/>
                        <rect width="45" height="20" fill="url(#Gradient-44)" transform="translate(1123.5,297) translate(-22.5,-10)" style="animation: 2.1s linear 5s infinite both a4_t;"/>
                    </g>
                    <g mask="url(#mask19_1398_56)" transform="translate(109.5,19) translate(-1268.5,-288)">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M1378,288.5L1159,288.5L1159,288L1378,288L1378,288.5Z" fill="url(#Gradient-45)" fill-opacity="0.1" transform="translate(1268.5,288.25) translate(-1268.5,-288.25)"/>
                        <rect width="45" height="20" fill="url(#Gradient-46)" transform="translate(1123.5,288) translate(-22.5,-10)" style="animation: 2.1s linear 4.7s infinite both a5_t;"/>
                    </g>
                    <g mask="url(#mask20_1398_56)" transform="translate(109.5,10) translate(-1268.5,-279)">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M1378,279.5L1159,279.5L1159,279L1378,279L1378,279.5Z" fill="url(#Gradient-47)" fill-opacity="0.1" transform="translate(1268.5,279.25) translate(-1268.5,-279.25)"/>
                        <rect width="45" height="20" fill="url(#Gradient-48)" transform="translate(1133.5,279) translate(-22.5,-10)" style="animation: 2.1s linear 5.2s infinite both a6_t;"/>
                    </g>
                </g>
                <g id="lines-4" transform="translate(1086.25,440) translate(-33.25,-58)">
                    <g mask="url(#mask3_1398_56)" transform="translate(58,58) translate(-1111,-440)">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M1111,382L1111,498L1110.5,498L1110.5,382L1111,382Z" fill="white" fill-opacity="0.15" transform="translate(1110.75,440) translate(-1110.75,-440)"/>
                        <rect width="17" height="51" fill="url(#Gradient-49)" transform="translate(1111,344.5) translate(-8.5,-25.5)" style="animation: 1.8s linear 6s infinite both a7_t;"/>
                    </g>
                    <g mask="url(#mask4_1398_56)" transform="translate(48.5,58) translate(-1101.5,-440)">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M1101.5,382L1101.5,498L1101,498L1101,382L1101.5,382Z" fill="white" fill-opacity="0.15" transform="translate(1101.25,440) translate(-1101.25,-440)"/>
                        <rect width="17" height="51" fill="url(#Gradient-50)" transform="translate(1101.5,344.5) translate(-8.5,-25.5)" style="animation: 1.8s linear 6.6s infinite both a8_t;"/>
                    </g>
                    <g mask="url(#mask5_1398_56)" transform="translate(38.5,58) translate(-1091.5,-440)">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M1091.5,382L1091.5,498L1091,498L1091,382L1091.5,382Z" fill="white" fill-opacity="0.15" transform="translate(1091.25,440) translate(-1091.25,-440)"/>
                        <rect width="17" height="51" fill="url(#Gradient-51)" transform="translate(1091.5,350.5) translate(-8.5,-25.5)" style="animation: 2s linear 6.1s infinite both a9_t;"/>
                    </g>
                    <g mask="url(#mask6_1398_56)" transform="translate(28.5,58) translate(-1081.5,-440)">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M1081.5,382L1081.5,498L1081,498L1081,382L1081.5,382Z" fill="white" fill-opacity="0.15" transform="translate(1081.25,440) translate(-1081.25,-440)"/>
                        <rect width="17" height="51" fill="url(#Gradient-52)" transform="translate(1081.5,344.5) translate(-8.5,-25.5)" style="animation: 2s linear 5.3s infinite both a10_t;"/>
                    </g>
                    <g mask="url(#mask7_1398_56)" transform="translate(18.5,58) translate(-1071.5,-440)">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M1071.5,382L1071.5,498L1071,498L1071,382L1071.5,382Z" fill="white" fill-opacity="0.15" transform="translate(1071.25,440) translate(-1071.25,-440)"/>
                        <rect width="17" height="51" fill="url(#Gradient-53)" transform="translate(1071.5,344.5) translate(-8.5,-25.5)" style="animation: 2s linear 5.7s infinite both a11_t;"/>
                    </g>
                    <g mask="url(#mask8_1398_56)" transform="translate(8.5,58) translate(-1061.5,-440)">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M1061.5,382L1061.5,498L1061,498L1061,382L1061.5,382Z" fill="white" fill-opacity="0.15" transform="translate(1061.25,440) translate(-1061.25,-440)"/>
                        <rect width="17" height="51" fill="url(#Gradient-54)" transform="translate(1061.5,344.5) translate(-8.5,-25.5)" style="animation: 1.8s linear 5s infinite both a12_t;"/>
                    </g>
                </g>
                <g id="lines-3" transform="translate(936,305) translate(-78,-19)">
                    <g mask="url(#mask9_1398_56)" transform="translate(78,10) translate(-936,-296)">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M1014,296.5L858,296.5L858,296L1014,296L1014,296.5Z" fill="url(#Gradient-55)" fill-opacity="0.1" transform="translate(936,296.25) translate(-936,-296.25)"/>
                        <rect width="45" height="20" fill="url(#Gradient-56)" transform="translate(832.5,296) translate(-22.5,-10)" style="animation: 1.8s linear 4.1s infinite both a13_t;"/>
                    </g>
                    <g mask="url(#mask10_1398_56)" transform="translate(78,19) translate(-936,-305)">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M1014,305.5L858,305.5L858,305L1014,305L1014,305.5Z" fill="url(#Gradient-57)" fill-opacity="0.1" transform="translate(936,305.25) translate(-936,-305.25)"/>
                        <rect width="45" height="20" fill="url(#Gradient-58)" transform="translate(833.5,305) translate(-22.5,-10)" style="animation: 1.8s linear 4.2s infinite both a14_t;"/>
                    </g>
                    <g mask="url(#mask11_1398_56)" transform="translate(78,28) translate(-936,-314)">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M1014,314.5L858,314.5L858,314L1014,314L1014,314.5Z" fill="url(#Gradient-59)" fill-opacity="0.1" transform="translate(936,314.25) translate(-936,-314.25)"/>
                        <rect width="45" height="20" fill="url(#Gradient-60)" transform="translate(830.5,314) translate(-22.5,-10)" style="animation: 1.8s linear 3.7s infinite both a15_t;"/>
                    </g>
                </g>
                <g id="lines-2" transform="translate(829.75,229.5) translate(-16.25,-40.5)">
                    <g mask="url(#mask1_1398_56)" transform="translate(24,40.5) translate(-837.5,-229.5)">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M838,189L838,270L837.5,270L837.5,189L838,189Z" fill="white" fill-opacity="0.1" transform="translate(837.75,229.5) translate(-837.75,-229.5)"/>
                        <rect width="17" height="51" fill="url(#Gradient-61)" transform="translate(837.5,154.5) translate(-8.5,-25.5)" style="animation: 2.3s linear 3s infinite both a16_t;"/>
                    </g>
                    <g mask="url(#mask2_1398_56)" transform="translate(8.5,40.5) translate(-822,-229.5)">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M822.5,189L822.5,270L822,270L822,189L822.5,189Z" fill="white" fill-opacity="0.1" transform="translate(822.25,229.5) translate(-822.25,-229.5)"/>
                        <rect width="17" height="51" fill="url(#Gradient-62)" transform="translate(822,152.5) translate(-8.5,-25.5)" style="animation: 2.3s linear 2s infinite both a17_t;"/>
                    </g>
                </g>
                <g id="lines-1" transform="translate(829.75,105.5) translate(-16.25,-35.5)">
                    <g mask="url(#mask12_1398_56)" transform="translate(24,35.5) translate(-837.5,-105.5)">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M838,70L838,141L837.5,141L837.5,70L838,70Z" fill="white" fill-opacity="0.1" transform="translate(837.75,105.5) translate(-837.75,-105.5)"/>
                        <rect width="17" height="51" fill="url(#Gradient-63)" transform="translate(837.5,45.5) translate(-8.5,-25.5)" style="animation: 2.3s linear 1s infinite both a18_t;"/>
                    </g>
                    <g mask="url(#mask13_1398_56)" transform="translate(8.5,35.5) translate(-822,-105.5)">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M822.5,70L822.5,141L822,141L822,70L822.5,70Z" fill="white" fill-opacity="0.1" transform="translate(822.25,105.5) translate(-822.25,-105.5)"/>
                        <rect width="17" height="51" fill="url(#Gradient-64)" transform="translate(822,45.5) translate(-8.5,-25.5)" style="animation: 2.3s linear infinite both a19_t;"/>
                    </g>
                </g>
                <g id="light-3" filter="url(#filter29_dd_1398_56)" transform="translate(1144.5,247.5) translate(-1144.5,-247.5)">
                    <rect width="3" height="3" rx="1.5" fill="url(#Gradient-65)" opacity="1" transform="translate(1144.5,247.5) translate(-1.5,-1.5)" style="animation: 4.5s linear 1.2s infinite both a20_o;"/>
                    <rect width="2.5" height="2.5" rx="1.25" stroke="black" stroke-opacity="0.05" stroke-width="0.5" transform="translate(1144.5,247.5) translate(-1.25,-1.25)"/>
                </g>
                <g id="light-2" filter="url(#filter31_dd_1398_56)" transform="translate(850.5,281.5) translate(-850.5,-281.5)">
                    <rect width="3" height="3" rx="1.5" fill="url(#Gradient-66)" opacity="1" transform="translate(850.5,281.5) translate(-1.5,-1.5)" style="animation: 3.9s linear 0.5s infinite both a21_o;"/>
                    <rect width="2.5" height="2.5" rx="1.25" stroke="black" stroke-opacity="0.05" stroke-width="0.5" transform="translate(850.5,281.5) translate(-1.25,-1.25)"/>
                </g>
                <g id="light-1" filter="url(#filter18_dd_1398_56)" transform="translate(850.5,152.5) translate(-850.5,-152.5)">
                    <rect width="3" height="3" rx="1.5" fill="url(#Gradient-67)" shape-rendering="crispEdges" opacity="1" transform="translate(850.5,152.5) translate(-1.5,-1.5)" style="animation: 4s linear infinite both a22_o;"/>
                    <rect width="3.5" height="3.5" rx="1.75" stroke="black" stroke-opacity="0.05" stroke-width="0.5" shape-rendering="crispEdges" transform="translate(850.5,152.5) translate(-1.75,-1.75)"/>
                </g>
            </g>
        </symbol>
        <filter id="filter0_b_1398_56" x="1083.5" y="229" width="4.5" height="6" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB" fill="none">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feGaussianBlur in="BackgroundImageFix" stdDeviation="0.5"/>
            <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1398_56"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_1398_56" result="shape"/>
        </filter>
        <filter id="filter1_b_1398_56" x="820" y="269" width="4.5" height="6" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB" fill="none">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feGaussianBlur in="BackgroundImageFix" stdDeviation="0.5"/>
            <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1398_56"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_1398_56" result="shape"/>
        </filter>
        <filter id="filter2_b_1398_56" x="835.5" y="269" width="4.5" height="6" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB" fill="none">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feGaussianBlur in="BackgroundImageFix" stdDeviation="0.5"/>
            <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1398_56"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_1398_56" result="shape"/>
        </filter>
        <filter id="filter3_b_1398_56" x="820" y="184" width="4.5" height="6" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB" fill="none">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feGaussianBlur in="BackgroundImageFix" stdDeviation="0.5"/>
            <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1398_56"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_1398_56" result="shape"/>
        </filter>
        <filter id="filter4_b_1398_56" x="835.5" y="184" width="4.5" height="6" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB" fill="none">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feGaussianBlur in="BackgroundImageFix" stdDeviation="0.5"/>
            <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1398_56"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_1398_56" result="shape"/>
        </filter>
        <filter id="filter5_b_1398_56" x="1099" y="377" width="4.5" height="6" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB" fill="none">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feGaussianBlur in="BackgroundImageFix" stdDeviation="0.5"/>
            <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1398_56"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_1398_56" result="shape"/>
        </filter>
        <filter id="filter6_b_1398_56" x="1089" y="377" width="4.5" height="6" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB" fill="none">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feGaussianBlur in="BackgroundImageFix" stdDeviation="0.5"/>
            <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1398_56"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_1398_56" result="shape"/>
        </filter>
        <filter id="filter7_b_1398_56" x="1079" y="377" width="4.5" height="6" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB" fill="none">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feGaussianBlur in="BackgroundImageFix" stdDeviation="0.5"/>
            <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1398_56"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_1398_56" result="shape"/>
        </filter>
        <filter id="filter8_b_1398_56" x="1069" y="377" width="4.5" height="6" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB" fill="none">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feGaussianBlur in="BackgroundImageFix" stdDeviation="0.5"/>
            <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1398_56"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_1398_56" result="shape"/>
        </filter>
        <filter id="filter9_b_1398_56" x="1059" y="377" width="4.5" height="6" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB" fill="none">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feGaussianBlur in="BackgroundImageFix" stdDeviation="0.5"/>
            <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1398_56"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_1398_56" result="shape"/>
        </filter>
        <filter id="filter10_b_1398_56" x="1108.5" y="377" width="4.5" height="6" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB" fill="none">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feGaussianBlur in="BackgroundImageFix" stdDeviation="0.5"/>
            <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1398_56"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_1398_56" result="shape"/>
        </filter>
        <filter id="filter11_b_1398_56" x="1009" y="294" width="6" height="4.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB" fill="none">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feGaussianBlur in="BackgroundImageFix" stdDeviation="0.5"/>
            <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1398_56"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_1398_56" result="shape"/>
        </filter>
        <filter id="filter12_b_1398_56" x="1009" y="303" width="6" height="4.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB" fill="none">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feGaussianBlur in="BackgroundImageFix" stdDeviation="0.5"/>
            <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1398_56"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_1398_56" result="shape"/>
        </filter>
        <filter id="filter13_b_1398_56" x="1009" y="313" width="6" height="4.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB" fill="none">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feGaussianBlur in="BackgroundImageFix" stdDeviation="0.5"/>
            <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1398_56"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_1398_56" result="shape"/>
        </filter>
        <filter id="filter14_b_1398_56" x="857" y="294" width="6" height="4.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB" fill="none">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feGaussianBlur in="BackgroundImageFix" stdDeviation="0.5"/>
            <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1398_56"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_1398_56" result="shape"/>
        </filter>
        <filter id="filter15_b_1398_56" x="857" y="303" width="6" height="4.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB" fill="none">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feGaussianBlur in="BackgroundImageFix" stdDeviation="0.5"/>
            <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1398_56"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_1398_56" result="shape"/>
        </filter>
        <filter id="filter16_b_1398_56" x="857" y="312" width="6" height="4.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB" fill="none">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feGaussianBlur in="BackgroundImageFix" stdDeviation="0.5"/>
            <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1398_56"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_1398_56" result="shape"/>
        </filter>
        <filter id="filter17_d_1398_56" x="786" y="129" width="88" height="72" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB" fill="none">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feMorphology radius="4" operator="dilate" in="SourceAlpha" result="effect1_dropShadow_1398_56"/>
            <feOffset/>
            <feGaussianBlur stdDeviation="6"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.8 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1398_56"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1398_56" result="shape"/>
        </filter>
        <filter id="filter18_dd_1398_56" x="828.5" y="130.5" width="44" height="44" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB" fill="none">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset/>
            <feGaussianBlur stdDeviation="10"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.466667 0 0 0 0 0.278431 0 0 0 0 1 0 0 0 1 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1398_56"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset/>
            <feGaussianBlur stdDeviation="3"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.466667 0 0 0 0 0.278431 0 0 0 0 1 0 0 0 1 0"/>
            <feBlend mode="normal" in2="effect1_dropShadow_1398_56" result="effect2_dropShadow_1398_56"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_1398_56" result="shape"/>
        </filter>
        <filter id="filter19_b_1398_56" x="820" y="140" width="4.5" height="6" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB" fill="none">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feGaussianBlur in="BackgroundImageFix" stdDeviation="0.5"/>
            <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1398_56"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_1398_56" result="shape"/>
        </filter>
        <filter id="filter20_b_1398_56" x="835.5" y="140" width="4.5" height="6" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB" fill="none">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feGaussianBlur in="BackgroundImageFix" stdDeviation="0.5"/>
            <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1398_56"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_1398_56" result="shape"/>
        </filter>
        <filter id="filter21_b_1398_56" x="1157" y="314" width="6" height="4.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB" fill="none">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feGaussianBlur in="BackgroundImageFix" stdDeviation="0.5"/>
            <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1398_56"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_1398_56" result="shape"/>
        </filter>
        <filter id="filter22_b_1398_56" x="1157" y="304" width="6" height="4.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB" fill="none">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feGaussianBlur in="BackgroundImageFix" stdDeviation="0.5"/>
            <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1398_56"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_1398_56" result="shape"/>
        </filter>
        <filter id="filter23_b_1398_56" x="1157" y="295" width="6" height="4.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB" fill="none">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feGaussianBlur in="BackgroundImageFix" stdDeviation="0.5"/>
            <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1398_56"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_1398_56" result="shape"/>
        </filter>
        <filter id="filter24_b_1398_56" x="1157" y="286" width="6" height="4.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB" fill="none">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feGaussianBlur in="BackgroundImageFix" stdDeviation="0.5"/>
            <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1398_56"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_1398_56" result="shape"/>
        </filter>
        <filter id="filter25_b_1398_56" x="1157" y="277" width="6" height="4.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB" fill="none">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feGaussianBlur in="BackgroundImageFix" stdDeviation="0.5"/>
            <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1398_56"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_1398_56" result="shape"/>
        </filter>
        <filter id="filter26_b_1398_56" x="1157" y="322" width="6" height="4.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB" fill="none">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feGaussianBlur in="BackgroundImageFix" stdDeviation="0.5"/>
            <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1398_56"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_1398_56" result="shape"/>
        </filter>
        <filter id="filter27_b_1398_56" x="1157" y="331" width="6" height="4.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB" fill="none">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feGaussianBlur in="BackgroundImageFix" stdDeviation="0.5"/>
            <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1398_56"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_1398_56" result="shape"/>
        </filter>
        <filter id="filter28_d_1398_56" x="998" y="218" width="176" height="176" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB" fill="none">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feMorphology radius="4" operator="dilate" in="SourceAlpha" result="effect1_dropShadow_1398_56"/>
            <feOffset/>
            <feGaussianBlur stdDeviation="6"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.8 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1398_56"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1398_56" result="shape"/>
        </filter>
        <filter id="filter29_dd_1398_56" x="1123" y="226" width="43" height="43" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB" fill="none">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset/>
            <feGaussianBlur stdDeviation="10"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.00784314 0 0 0 0 0.988235 0 0 0 0 0.945098 0 0 0 1 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1398_56"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset/>
            <feGaussianBlur stdDeviation="3"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.00784314 0 0 0 0 0.988235 0 0 0 0 0.945098 0 0 0 0.75 0"/>
            <feBlend mode="normal" in2="effect1_dropShadow_1398_56" result="effect2_dropShadow_1398_56"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_1398_56" result="shape"/>
        </filter>
        <filter id="filter30_d_1398_56" x="786" y="258" width="88" height="96" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB" fill="none">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feMorphology radius="4" operator="dilate" in="SourceAlpha" result="effect1_dropShadow_1398_56"/>
            <feOffset/>
            <feGaussianBlur stdDeviation="6"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.8 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1398_56"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1398_56" result="shape"/>
        </filter>
        <filter id="filter31_dd_1398_56" x="829" y="260" width="43" height="43" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB" fill="none">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset/>
            <feGaussianBlur stdDeviation="10"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.00784314 0 0 0 0 0.988235 0 0 0 0 0.945098 0 0 0 1 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1398_56"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset/>
            <feGaussianBlur stdDeviation="3"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.00784314 0 0 0 0 0.988235 0 0 0 0 0.945098 0 0 0 0.75 0"/>
            <feBlend mode="normal" in2="effect1_dropShadow_1398_56" result="effect2_dropShadow_1398_56"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_1398_56" result="shape"/>
        </filter>
        <linearGradient id="Gradient-0" x1="1085.75" y1="230" x2="1085.75" y2="234" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#ffffff" stop-opacity="0.6"/>
            <stop offset="1" stop-color="#ffffff" stop-opacity="0.3"/>
        </linearGradient>
        <linearGradient id="Gradient-1" x1="822.25" y1="270" x2="822.25" y2="274" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#ffffff" stop-opacity="0.6"/>
            <stop offset="1" stop-color="#ffffff" stop-opacity="0.3"/>
        </linearGradient>
        <linearGradient id="Gradient-2" x1="837.75" y1="270" x2="837.75" y2="274" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#ffffff" stop-opacity="0.6"/>
            <stop offset="1" stop-color="#ffffff" stop-opacity="0.3"/>
        </linearGradient>
        <linearGradient id="Gradient-3" x1="822.25" y1="185" x2="822.25" y2="189" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#ffffff" stop-opacity="0.6"/>
            <stop offset="1" stop-color="#ffffff" stop-opacity="0.3"/>
        </linearGradient>
        <linearGradient id="Gradient-4" x1="837.75" y1="185" x2="837.75" y2="189" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#ffffff" stop-opacity="0.6"/>
            <stop offset="1" stop-color="#ffffff" stop-opacity="0.3"/>
        </linearGradient>
        <linearGradient id="Gradient-5" x1="1101.25" y1="378" x2="1101.25" y2="382" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#ffffff" stop-opacity="0.6"/>
            <stop offset="1" stop-color="#ffffff" stop-opacity="0.3"/>
        </linearGradient>
        <linearGradient id="Gradient-6" x1="1091.25" y1="378" x2="1091.25" y2="382" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#ffffff" stop-opacity="0.6"/>
            <stop offset="1" stop-color="#ffffff" stop-opacity="0.3"/>
        </linearGradient>
        <linearGradient id="Gradient-7" x1="1081.25" y1="378" x2="1081.25" y2="382" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#ffffff" stop-opacity="0.6"/>
            <stop offset="1" stop-color="#ffffff" stop-opacity="0.3"/>
        </linearGradient>
        <linearGradient id="Gradient-8" x1="1071.25" y1="378" x2="1071.25" y2="382" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#ffffff" stop-opacity="0.6"/>
            <stop offset="1" stop-color="#ffffff" stop-opacity="0.3"/>
        </linearGradient>
        <linearGradient id="Gradient-9" x1="1061.25" y1="378" x2="1061.25" y2="382" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#ffffff" stop-opacity="0.6"/>
            <stop offset="1" stop-color="#ffffff" stop-opacity="0.3"/>
        </linearGradient>
        <linearGradient id="Gradient-10" x1="1110.75" y1="378" x2="1110.75" y2="382" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#ffffff" stop-opacity="0.6"/>
            <stop offset="1" stop-color="#ffffff" stop-opacity="0.3"/>
        </linearGradient>
        <linearGradient id="Gradient-11" x1="1012" y1="295" x2="1012" y2="297.5" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#ffffff" stop-opacity="0.6"/>
            <stop offset="1" stop-color="#ffffff" stop-opacity="0.3"/>
        </linearGradient>
        <linearGradient id="Gradient-12" x1="1012" y1="304" x2="1012" y2="306.5" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#ffffff" stop-opacity="0.6"/>
            <stop offset="1" stop-color="#ffffff" stop-opacity="0.3"/>
        </linearGradient>
        <linearGradient id="Gradient-13" x1="1012" y1="314" x2="1012" y2="316.5" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#ffffff" stop-opacity="0.6"/>
            <stop offset="1" stop-color="#ffffff" stop-opacity="0.3"/>
        </linearGradient>
        <linearGradient id="Gradient-14" x1="860" y1="295" x2="860" y2="297.5" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#ffffff" stop-opacity="0.6"/>
            <stop offset="1" stop-color="#ffffff" stop-opacity="0.3"/>
        </linearGradient>
        <linearGradient id="Gradient-15" x1="860" y1="304" x2="860" y2="306.5" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#ffffff" stop-opacity="0.6"/>
            <stop offset="1" stop-color="#ffffff" stop-opacity="0.3"/>
        </linearGradient>
        <linearGradient id="Gradient-16" x1="860" y1="313" x2="860" y2="315.5" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#ffffff" stop-opacity="0.6"/>
            <stop offset="1" stop-color="#ffffff" stop-opacity="0.3"/>
        </linearGradient>
        <linearGradient id="Gradient-17" x1="822.25" y1="141" x2="822.25" y2="145" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#ffffff" stop-opacity="0.6"/>
            <stop offset="1" stop-color="#ffffff" stop-opacity="0.3"/>
        </linearGradient>
        <linearGradient id="Gradient-18" x1="837.75" y1="141" x2="837.75" y2="145" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#ffffff" stop-opacity="0.6"/>
            <stop offset="1" stop-color="#ffffff" stop-opacity="0.3"/>
        </linearGradient>
        <linearGradient id="Gradient-19" x1="1160" y1="315" x2="1160" y2="317.5" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#ffffff" stop-opacity="0.6"/>
            <stop offset="1" stop-color="#ffffff" stop-opacity="0.3"/>
        </linearGradient>
        <linearGradient id="Gradient-20" x1="1160" y1="305" x2="1160" y2="307.5" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#ffffff" stop-opacity="0.6"/>
            <stop offset="1" stop-color="#ffffff" stop-opacity="0.3"/>
        </linearGradient>
        <linearGradient id="Gradient-21" x1="1160" y1="296" x2="1160" y2="298.5" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#ffffff" stop-opacity="0.6"/>
            <stop offset="1" stop-color="#ffffff" stop-opacity="0.3"/>
        </linearGradient>
        <linearGradient id="Gradient-22" x1="1160" y1="287" x2="1160" y2="289.5" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#ffffff" stop-opacity="0.6"/>
            <stop offset="1" stop-color="#ffffff" stop-opacity="0.3"/>
        </linearGradient>
        <linearGradient id="Gradient-23" x1="1160" y1="278" x2="1160" y2="280.5" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#ffffff" stop-opacity="0.6"/>
            <stop offset="1" stop-color="#ffffff" stop-opacity="0.3"/>
        </linearGradient>
        <linearGradient id="Gradient-24" x1="1160" y1="323" x2="1160" y2="325.5" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#ffffff" stop-opacity="0.6"/>
            <stop offset="1" stop-color="#ffffff" stop-opacity="0.3"/>
        </linearGradient>
        <linearGradient id="Gradient-25" x1="1160" y1="332" x2="1160" y2="334.5" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#ffffff" stop-opacity="0.6"/>
            <stop offset="1" stop-color="#ffffff" stop-opacity="0.3"/>
        </linearGradient>
        <radialGradient id="Gradient-26" cx="-1.625" cy="7.7381" r="1" fx="-1.625" fy="7.7381" gradientUnits="userSpaceOnUse" gradientTransform="matrix(0 144 -131.04 0 1086 234)">
            <stop offset="0" stop-color="#ffffff" stop-opacity="0.2"/>
            <stop offset="1" stop-color="#ffffff" stop-opacity="0"/>
        </radialGradient>
        <linearGradient id="Gradient-27" x1="1086.5" y1="291" x2="1086.5" y2="323" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#000000"/>
            <stop offset="0.0001" stop-color="#000000" stop-opacity="0.41"/>
            <stop offset="1" stop-color="#000000" stop-opacity="0.1"/>
        </linearGradient>
        <radialGradient id="Gradient-28" cx="-1.6276" cy="4.1573" r="1" fx="-1.6276" fy="4.1573" gradientUnits="userSpaceOnUse" gradientTransform="matrix(0 144 -244 0 1158 234)">
            <stop offset="0" stop-color="#ffffff"/>
            <stop offset="1" stop-color="#ffffff" stop-opacity="0"/>
        </radialGradient>
        <radialGradient id="Gradient-29" cx="-4.28125" cy="15.7378" r="1" fx="-4.28125" fy="15.7378" gradientUnits="userSpaceOnUse" gradientTransform="matrix(0 64 -50.96 0 830 274)">
            <stop offset="0" stop-color="#ffffff" stop-opacity="0.2"/>
            <stop offset="1" stop-color="#ffffff" stop-opacity="0"/>
        </radialGradient>
        <linearGradient id="Gradient-30" x1="830" y1="299" x2="830" y2="314" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#ffffff"/>
            <stop offset="1" stop-color="#ffffff" stop-opacity="0.4"/>
        </linearGradient>
        <radialGradient id="Gradient-31" cx="-4.28711" cy="14.3281" r="1" fx="-4.28711" fy="14.3281" gradientUnits="userSpaceOnUse" gradientTransform="matrix(0 64 -56 0 858 274)">
            <stop offset="0" stop-color="#ffffff"/>
            <stop offset="1" stop-color="#ffffff" stop-opacity="0"/>
        </radialGradient>
        <radialGradient id="Gradient-32" cx="-3.625" cy="15.7378" r="1" fx="-3.625" fy="15.7378" gradientUnits="userSpaceOnUse" gradientTransform="matrix(0 40 -50.96 0 830 145)">
            <stop offset="0" stop-color="#ffffff" stop-opacity="0.2"/>
            <stop offset="1" stop-color="#ffffff" stop-opacity="0"/>
        </radialGradient>
        <linearGradient id="Gradient-33" x1="830" y1="164.438" x2="830" y2="174" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#ffffff"/>
            <stop offset="1" stop-color="#ffffff" stop-opacity="0.4"/>
        </linearGradient>
        <radialGradient id="Gradient-34" cx="-3.63437" cy="14.3281" r="1" fx="-3.63437" fy="14.3281" gradientUnits="userSpaceOnUse" gradientTransform="matrix(0 40 -56 0 858 145)">
            <stop offset="0" stop-color="#ffffff"/>
            <stop offset="1" stop-color="#ffffff" stop-opacity="0"/>
        </radialGradient>
        <linearGradient id="Gradient-35" x1="1211.4" y1="118" x2="1211.4" y2="333.25" gradientUnits="userSpaceOnUse">
            <stop offset="0.204413" stop-color="#ffffff" stop-opacity="0"/>
            <stop offset="1" stop-color="#ffffff"/>
        </linearGradient>
        <radialGradient id="Gradient-36" cx="-26.0222" cy="-0.103526" r="1" fx="-26.0222" fy="-0.103526" gradientUnits="userSpaceOnUse" gradientTransform="matrix(45 0 0 3120 1171 333)">
            <stop offset="0" stop-color="#3dc5fa" stop-opacity="0"/>
            <stop offset="1" stop-color="#3dc5fa"/>
        </radialGradient>
        <linearGradient id="Gradient-37" x1="1211.4" y1="109" x2="1211.4" y2="324.25" gradientUnits="userSpaceOnUse">
            <stop offset="0.204413" stop-color="#ffffff" stop-opacity="0"/>
            <stop offset="1" stop-color="#ffffff"/>
        </linearGradient>
        <radialGradient id="Gradient-38" cx="-28.4667" cy="-0.100641" r="1" fx="-28.4667" fy="-0.100641" gradientUnits="userSpaceOnUse" gradientTransform="matrix(45 0 0 3120 1281 324)">
            <stop offset="0" stop-color="#3dc5fa" stop-opacity="0"/>
            <stop offset="1" stop-color="#3dc5fa"/>
        </radialGradient>
        <linearGradient id="Gradient-39" x1="1211.4" y1="101" x2="1211.4" y2="316.25" gradientUnits="userSpaceOnUse">
            <stop offset="0.204413" stop-color="#ffffff" stop-opacity="0"/>
            <stop offset="1" stop-color="#ffffff"/>
        </linearGradient>
        <radialGradient id="Gradient-40" cx="-27.3556" cy="-0.0980769" r="1" fx="-27.3556" fy="-0.0980769" gradientUnits="userSpaceOnUse" gradientTransform="matrix(45 0 0 3120 1231 316)">
            <stop offset="0" stop-color="#3dc5fa" stop-opacity="0"/>
            <stop offset="1" stop-color="#3dc5fa"/>
        </radialGradient>
        <linearGradient id="Gradient-41" x1="1211.4" y1="91" x2="1211.4" y2="306.25" gradientUnits="userSpaceOnUse">
            <stop offset="0.204413" stop-color="#ffffff" stop-opacity="0"/>
            <stop offset="1" stop-color="#ffffff"/>
        </linearGradient>
        <radialGradient id="Gradient-42" cx="-29.1333" cy="-0.0948718" r="1" fx="-29.1333" fy="-0.0948718" gradientUnits="userSpaceOnUse" gradientTransform="matrix(45 0 0 3120 1311 306)">
            <stop offset="0" stop-color="#3dc5fa" stop-opacity="0"/>
            <stop offset="1" stop-color="#3dc5fa"/>
        </radialGradient>
        <linearGradient id="Gradient-43" x1="1211.4" y1="82" x2="1211.4" y2="297.25" gradientUnits="userSpaceOnUse">
            <stop offset="0.204413" stop-color="#ffffff" stop-opacity="0"/>
            <stop offset="1" stop-color="#ffffff"/>
        </linearGradient>
        <radialGradient id="Gradient-44" cx="-26.6889" cy="-0.0919872" r="1" fx="-26.6889" fy="-0.0919872" gradientUnits="userSpaceOnUse" gradientTransform="matrix(45 0 0 3120 1201 297)">
            <stop offset="0" stop-color="#3dc5fa" stop-opacity="0"/>
            <stop offset="1" stop-color="#3dc5fa"/>
        </radialGradient>
        <linearGradient id="Gradient-45" x1="1211.4" y1="73" x2="1211.4" y2="288.25" gradientUnits="userSpaceOnUse">
            <stop offset="0.204413" stop-color="#ffffff" stop-opacity="0"/>
            <stop offset="1" stop-color="#ffffff"/>
        </linearGradient>
        <radialGradient id="Gradient-46" cx="-27.5778" cy="-0.0891026" r="1" fx="-27.5778" fy="-0.0891026" gradientUnits="userSpaceOnUse" gradientTransform="matrix(45 0 0 3120 1241 288)">
            <stop offset="0" stop-color="#3dc5fa" stop-opacity="0"/>
            <stop offset="1" stop-color="#3dc5fa"/>
        </radialGradient>
        <linearGradient id="Gradient-47" x1="1211.4" y1="64" x2="1211.4" y2="279.25" gradientUnits="userSpaceOnUse">
            <stop offset="0.204413" stop-color="#ffffff" stop-opacity="0"/>
            <stop offset="1" stop-color="#ffffff"/>
        </linearGradient>
        <radialGradient id="Gradient-48" cx="-28.4667" cy="-0.0862179" r="1" fx="-28.4667" fy="-0.0862179" gradientUnits="userSpaceOnUse" gradientTransform="matrix(45 0 0 3120 1281 279)">
            <stop offset="0" stop-color="#3dc5fa" stop-opacity="0"/>
            <stop offset="1" stop-color="#3dc5fa"/>
        </radialGradient>
        <radialGradient id="Gradient-49" cx="7.62745" cy="-21.6176" r="1" fx="7.62745" fy="-21.6176" gradientUnits="userSpaceOnUse" gradientTransform="matrix(0 -51 51 0 1111.5 440)">
            <stop offset="0" stop-color="#3dc5fa"/>
            <stop offset="0.778822" stop-color="#3dc5fa" stop-opacity="0"/>
        </radialGradient>
        <radialGradient id="Gradient-50" cx="7.62745" cy="-21.4314" r="1" fx="7.62745" fy="-21.4314" gradientUnits="userSpaceOnUse" gradientTransform="matrix(0 -51 51 0 1102 440)">
            <stop offset="0" stop-color="#3dc5fa"/>
            <stop offset="0.778822" stop-color="#3dc5fa" stop-opacity="0"/>
        </radialGradient>
        <radialGradient id="Gradient-51" cx="7.62745" cy="-21.2353" r="1" fx="7.62745" fy="-21.2353" gradientUnits="userSpaceOnUse" gradientTransform="matrix(0 -51 51 0 1092 440)">
            <stop offset="0" stop-color="#3dc5fa"/>
            <stop offset="0.778822" stop-color="#3dc5fa" stop-opacity="0"/>
        </radialGradient>
        <radialGradient id="Gradient-52" cx="7.62745" cy="-21.0392" r="1" fx="7.62745" fy="-21.0392" gradientUnits="userSpaceOnUse" gradientTransform="matrix(0 -51 51 0 1082 440)">
            <stop offset="0" stop-color="#3dc5fa"/>
            <stop offset="0.778822" stop-color="#3dc5fa" stop-opacity="0"/>
        </radialGradient>
        <radialGradient id="Gradient-53" cx="7.62745" cy="-20.8431" r="1" fx="7.62745" fy="-20.8431" gradientUnits="userSpaceOnUse" gradientTransform="matrix(0 -51 51 0 1072 440)">
            <stop offset="0" stop-color="#3dc5fa"/>
            <stop offset="0.778822" stop-color="#3dc5fa" stop-opacity="0"/>
        </radialGradient>
        <radialGradient id="Gradient-54" cx="7.62745" cy="-20.6471" r="1" fx="7.62745" fy="-20.6471" gradientUnits="userSpaceOnUse" gradientTransform="matrix(0 -51 51 0 1062 440)">
            <stop offset="0" stop-color="#3dc5fa"/>
            <stop offset="0.778822" stop-color="#3dc5fa" stop-opacity="0"/>
        </radialGradient>
        <linearGradient id="Gradient-55" x1="895.328" y1="81" x2="895.328" y2="296.25" gradientUnits="userSpaceOnUse">
            <stop offset="0.204413" stop-color="#ffffff" stop-opacity="0"/>
            <stop offset="1" stop-color="#ffffff"/>
        </linearGradient>
        <radialGradient id="Gradient-56" cx="-19.8" cy="-0.0916667" r="1" fx="-19.8" fy="-0.0916667" gradientUnits="userSpaceOnUse" gradientTransform="matrix(45 0 0 3120 891 296)">
            <stop offset="0" stop-color="#3ceeae" stop-opacity="0"/>
            <stop offset="1" stop-color="#3ceeae"/>
        </radialGradient>
        <linearGradient id="Gradient-57" x1="895.328" y1="90" x2="895.328" y2="305.25" gradientUnits="userSpaceOnUse">
            <stop offset="0.204413" stop-color="#ffffff" stop-opacity="0"/>
            <stop offset="1" stop-color="#ffffff"/>
        </linearGradient>
        <radialGradient id="Gradient-58" cx="-21" cy="-0.0945513" r="1" fx="-21" fy="-0.0945513" gradientUnits="userSpaceOnUse" gradientTransform="matrix(45 0 0 3120 945 305)">
            <stop offset="0" stop-color="#3ceeae" stop-opacity="0"/>
            <stop offset="1" stop-color="#3ceeae"/>
        </radialGradient>
        <linearGradient id="Gradient-59" x1="895.328" y1="99" x2="895.328" y2="314.25" gradientUnits="userSpaceOnUse">
            <stop offset="0.204413" stop-color="#ffffff" stop-opacity="0"/>
            <stop offset="1" stop-color="#ffffff"/>
        </linearGradient>
        <radialGradient id="Gradient-60" cx="-20.4667" cy="-0.0974359" r="1" fx="-20.4667" fy="-0.0974359" gradientUnits="userSpaceOnUse" gradientTransform="matrix(45 0 0 3120 921 314)">
            <stop offset="0" stop-color="#3ceeae" stop-opacity="0"/>
            <stop offset="1" stop-color="#3ceeae"/>
        </radialGradient>
        <radialGradient id="Gradient-61" cx="3.70588" cy="-16.2549" r="1" fx="3.70588" fy="-16.2549" gradientUnits="userSpaceOnUse" gradientTransform="matrix(0 -51 51 0 838 240)">
            <stop offset="0" stop-color="#9d72ff"/>
            <stop offset="0.778822" stop-color="#9d72ff" stop-opacity="0"/>
        </radialGradient>
        <radialGradient id="Gradient-62" cx="3.70588" cy="-15.951" r="1" fx="3.70588" fy="-15.951" gradientUnits="userSpaceOnUse" gradientTransform="matrix(0 -51 51 0 822.5 240)">
            <stop offset="0" stop-color="#9d72ff"/>
            <stop offset="0.778822" stop-color="#9d72ff" stop-opacity="0"/>
        </radialGradient>
        <radialGradient id="Gradient-63" cx="1.37255" cy="-16.2549" r="1" fx="1.37255" fy="-16.2549" gradientUnits="userSpaceOnUse" gradientTransform="matrix(0 -51 51 0 838 121)">
            <stop offset="0" stop-color="#9d72ff"/>
            <stop offset="0.778822" stop-color="#9d72ff" stop-opacity="0"/>
        </radialGradient>
        <radialGradient id="Gradient-64" cx="1.37255" cy="-15.951" r="1" fx="1.37255" fy="-15.951" gradientUnits="userSpaceOnUse" gradientTransform="matrix(0 -51 51 0 822.5 121)">
            <stop offset="0" stop-color="#9d72ff"/>
            <stop offset="0.778822" stop-color="#9d72ff" stop-opacity="0"/>
        </radialGradient>
        <radialGradient id="Gradient-65" cx="-765.147" cy="-164" r="1" fx="-765.147" fy="-164" gradientUnits="userSpaceOnUse" gradientTransform="matrix(1.49383 0 0 1.5 1144.51 247.5)">
            <stop offset="0" stop-color="#72fff9"/>
            <stop offset="1" stop-color="#0284fc"/>
        </radialGradient>
        <radialGradient id="Gradient-66" cx="-568.338" cy="-186.667" r="1" fx="-568.338" fy="-186.667" gradientUnits="userSpaceOnUse" gradientTransform="matrix(1.49383 0 0 1.5 850.506 281.5)">
            <stop offset="0" stop-color="#72ffaa"/>
            <stop offset="1" stop-color="#00dab3"/>
        </radialGradient>
        <radialGradient id="Gradient-67" cx="-568.338" cy="-100.667" r="1" fx="-568.338" fy="-100.667" gradientUnits="userSpaceOnUse" gradientTransform="matrix(1.49383 0 0 1.5 850.506 152.5)">
            <stop offset="0.041667" stop-color="#d4c5ff"/>
            <stop offset="1" stop-color="#6015ff"/>
        </radialGradient>
        <radialGradient id="Gradient-68" cx="0" cy="0" r="1" fx="0" fy="0" gradientUnits="userSpaceOnUse" gradientTransform="matrix(-567 68.0021 -28.599 -238.458 871 290)">
            <stop offset="0" stop-color="#ffffff"/>
            <stop offset="0.669795" stop-color="#ffffff"/>
            <stop offset="0.950551" stop-color="#ffffff" stop-opacity="0"/>
        </radialGradient>
        <clipPath id="clip0_1398_56">
            <rect width="40" height="77" fill="white" transform="translate(612,341.5) translate(-20,-38.5)"/>
        </clipPath>
        <clipPath id="clip1_1398_56">
            <rect width="80" height="40" fill="white" transform="translate(883,383) translate(-40,-20)"/>
        </clipPath>
        <clipPath id="clip3_1398_56">
            <rect width="144" height="144" rx="3" fill="white" transform="translate(1086,306) translate(-72,-72)"/>
        </clipPath>
        <clipPath id="clip4_1398_56">
            <rect width="56" height="64" rx="3" fill="white" transform="translate(830,306) translate(-28,-32)"/>
        </clipPath>
        <clipPath id="clip2_1398_56">
            <rect width="56" height="40" rx="3" fill="white" transform="translate(830,165) translate(-28,-20)"/>
        </clipPath>
        <mask id="mask14_1398_56" style="mask-type: alpha;">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M1378,333.5L1159,333.5L1159,333L1378,333L1378,333.5Z" fill="white" transform="translate(1268.5,333.25) translate(-1268.5,-333.25)"/>
        </mask>
        <mask id="mask15_1398_56" style="mask-type: alpha;">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M1378,324.5L1159,324.5L1159,324L1378,324L1378,324.5Z" fill="white" transform="translate(1268.5,324.25) translate(-1268.5,-324.25)"/>
        </mask>
        <mask id="mask16_1398_56" style="mask-type: alpha;">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M1378,316.5L1159,316.5L1159,316L1378,316L1378,316.5Z" fill="white" transform="translate(1268.5,316.25) translate(-1268.5,-316.25)"/>
        </mask>
        <mask id="mask17_1398_56" style="mask-type: alpha;">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M1378,306.5L1159,306.5L1159,306L1378,306L1378,306.5Z" fill="white" transform="translate(1268.5,306.25) translate(-1268.5,-306.25)"/>
        </mask>
        <mask id="mask18_1398_56" style="mask-type: alpha;">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M1378,297.5L1159,297.5L1159,297L1378,297L1378,297.5Z" fill="white" transform="translate(1268.5,297.25) translate(-1268.5,-297.25)"/>
        </mask>
        <mask id="mask19_1398_56" style="mask-type: alpha;">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M1378,288.5L1159,288.5L1159,288L1378,288L1378,288.5Z" fill="white" transform="translate(1268.5,288.25) translate(-1268.5,-288.25)"/>
        </mask>
        <mask id="mask20_1398_56" style="mask-type: alpha;">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M1378,279.5L1159,279.5L1159,279L1378,279L1378,279.5Z" fill="white" transform="translate(1268.5,279.25) translate(-1268.5,-279.25)"/>
        </mask>
        <mask id="mask3_1398_56" style="mask-type: alpha;">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M1111,382L1111,498L1110.5,498L1110.5,382L1111,382Z" fill="white" transform="translate(1110.75,440) translate(-1110.75,-440)"/>
        </mask>
        <mask id="mask4_1398_56" style="mask-type: alpha;">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M1101.5,382L1101.5,498L1101,498L1101,382L1101.5,382Z" fill="white" transform="translate(1101.25,440) translate(-1101.25,-440)"/>
        </mask>
        <mask id="mask5_1398_56" style="mask-type: alpha;">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M1091.5,382L1091.5,498L1091,498L1091,382L1091.5,382Z" fill="white" transform="translate(1091.25,440) translate(-1091.25,-440)"/>
        </mask>
        <mask id="mask6_1398_56" style="mask-type: alpha;">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M1081.5,382L1081.5,498L1081,498L1081,382L1081.5,382Z" fill="white" transform="translate(1081.25,440) translate(-1081.25,-440)"/>
        </mask>
        <mask id="mask7_1398_56" style="mask-type: alpha;">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M1071.5,382L1071.5,498L1071,498L1071,382L1071.5,382Z" fill="white" transform="translate(1071.25,440) translate(-1071.25,-440)"/>
        </mask>
        <mask id="mask8_1398_56" style="mask-type: alpha;">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M1061.5,382L1061.5,498L1061,498L1061,382L1061.5,382Z" fill="white" transform="translate(1061.25,440) translate(-1061.25,-440)"/>
        </mask>
        <mask id="mask9_1398_56" style="mask-type: alpha;">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M1014,296.5L858,296.5L858,296L1014,296L1014,296.5Z" fill="white" transform="translate(936,296.25) translate(-936,-296.25)"/>
        </mask>
        <mask id="mask10_1398_56" style="mask-type: alpha;">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M1014,305.5L858,305.5L858,305L1014,305L1014,305.5Z" fill="white" transform="translate(936,305.25) translate(-936,-305.25)"/>
        </mask>
        <mask id="mask11_1398_56" style="mask-type: alpha;">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M1014,314.5L858,314.5L858,314L1014,314L1014,314.5Z" fill="white" transform="translate(936,314.25) translate(-936,-314.25)"/>
        </mask>
        <mask id="mask1_1398_56" style="mask-type: alpha;">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M838,189L838,270L837.5,270L837.5,189L838,189Z" fill="white" transform="translate(837.75,229.5) translate(-837.75,-229.5)"/>
        </mask>
        <mask id="mask2_1398_56" style="mask-type: alpha;">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M822.5,189L822.5,270L822,270L822,189L822.5,189Z" fill="white" transform="translate(822.25,229.5) translate(-822.25,-229.5)"/>
        </mask>
        <mask id="mask12_1398_56" style="mask-type: alpha;">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M838,70L838,141L837.5,141L837.5,70L838,70Z" fill="white" transform="translate(837.75,105.5) translate(-837.75,-105.5)"/>
        </mask>
        <mask id="mask13_1398_56" style="mask-type: alpha;">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M822.5,70L822.5,141L822,141L822,70L822.5,70Z" fill="white" transform="translate(822.25,105.5) translate(-822.25,-105.5)"/>
        </mask>
        <mask id="mask" style="mask-type: alpha;">
            <rect width="1512" height="545.25" fill="url(#Gradient-68)" transform="translate(756,272.625) translate(-756,-272.625)"/>
        </mask>
        <clipPath id="clip0_1398_56">
            <rect width="40" height="77" fill="white" transform="translate(612,341.5) translate(-20,-38.5)"/>
        </clipPath>
        <clipPath id="clip1_1398_56">
            <rect width="80" height="40" fill="white" transform="translate(883,383) translate(-40,-20)"/>
        </clipPath>
        <clipPath id="clip3_1398_56">
            <rect width="144" height="144" rx="3" fill="white" transform="translate(1086,306) translate(-72,-72)"/>
        </clipPath>
        <clipPath id="clip4_1398_56">
            <rect width="56" height="64" rx="3" fill="white" transform="translate(830,306) translate(-28,-32)"/>
        </clipPath>
        <clipPath id="clip2_1398_56">
            <rect width="56" height="40" rx="3" fill="white" transform="translate(830,165) translate(-28,-20)"/>
        </clipPath>
        <mask id="mask14_1398_56" style="mask-type: alpha;">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M1378,333.5L1159,333.5L1159,333L1378,333L1378,333.5Z" fill="white" transform="translate(1268.5,333.25) translate(-1268.5,-333.25)"/>
        </mask>
        <mask id="mask15_1398_56" style="mask-type: alpha;">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M1378,324.5L1159,324.5L1159,324L1378,324L1378,324.5Z" fill="white" transform="translate(1268.5,324.25) translate(-1268.5,-324.25)"/>
        </mask>
        <mask id="mask16_1398_56" style="mask-type: alpha;">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M1378,316.5L1159,316.5L1159,316L1378,316L1378,316.5Z" fill="white" transform="translate(1268.5,316.25) translate(-1268.5,-316.25)"/>
        </mask>
        <mask id="mask17_1398_56" style="mask-type: alpha;">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M1378,306.5L1159,306.5L1159,306L1378,306L1378,306.5Z" fill="white" transform="translate(1268.5,306.25) translate(-1268.5,-306.25)"/>
        </mask>
        <mask id="mask18_1398_56" style="mask-type: alpha;">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M1378,297.5L1159,297.5L1159,297L1378,297L1378,297.5Z" fill="white" transform="translate(1268.5,297.25) translate(-1268.5,-297.25)"/>
        </mask>
        <mask id="mask19_1398_56" style="mask-type: alpha;">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M1378,288.5L1159,288.5L1159,288L1378,288L1378,288.5Z" fill="white" transform="translate(1268.5,288.25) translate(-1268.5,-288.25)"/>
        </mask>
        <mask id="mask20_1398_56" style="mask-type: alpha;">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M1378,279.5L1159,279.5L1159,279L1378,279L1378,279.5Z" fill="white" transform="translate(1268.5,279.25) translate(-1268.5,-279.25)"/>
        </mask>
        <mask id="mask3_1398_56" style="mask-type: alpha;">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M1111,382L1111,498L1110.5,498L1110.5,382L1111,382Z" fill="white" transform="translate(1110.75,440) translate(-1110.75,-440)"/>
        </mask>
        <mask id="mask4_1398_56" style="mask-type: alpha;">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M1101.5,382L1101.5,498L1101,498L1101,382L1101.5,382Z" fill="white" transform="translate(1101.25,440) translate(-1101.25,-440)"/>
        </mask>
        <mask id="mask5_1398_56" style="mask-type: alpha;">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M1091.5,382L1091.5,498L1091,498L1091,382L1091.5,382Z" fill="white" transform="translate(1091.25,440) translate(-1091.25,-440)"/>
        </mask>
        <mask id="mask6_1398_56" style="mask-type: alpha;">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M1081.5,382L1081.5,498L1081,498L1081,382L1081.5,382Z" fill="white" transform="translate(1081.25,440) translate(-1081.25,-440)"/>
        </mask>
        <mask id="mask7_1398_56" style="mask-type: alpha;">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M1071.5,382L1071.5,498L1071,498L1071,382L1071.5,382Z" fill="white" transform="translate(1071.25,440) translate(-1071.25,-440)"/>
        </mask>
        <mask id="mask8_1398_56" style="mask-type: alpha;">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M1061.5,382L1061.5,498L1061,498L1061,382L1061.5,382Z" fill="white" transform="translate(1061.25,440) translate(-1061.25,-440)"/>
        </mask>
        <mask id="mask9_1398_56" style="mask-type: alpha;">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M1014,296.5L858,296.5L858,296L1014,296L1014,296.5Z" fill="white" transform="translate(936,296.25) translate(-936,-296.25)"/>
        </mask>
        <mask id="mask10_1398_56" style="mask-type: alpha;">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M1014,305.5L858,305.5L858,305L1014,305L1014,305.5Z" fill="white" transform="translate(936,305.25) translate(-936,-305.25)"/>
        </mask>
        <mask id="mask11_1398_56" style="mask-type: alpha;">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M1014,314.5L858,314.5L858,314L1014,314L1014,314.5Z" fill="white" transform="translate(936,314.25) translate(-936,-314.25)"/>
        </mask>
        <mask id="mask1_1398_56" style="mask-type: alpha;">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M838,189L838,270L837.5,270L837.5,189L838,189Z" fill="white" transform="translate(837.75,229.5) translate(-837.75,-229.5)"/>
        </mask>
        <mask id="mask2_1398_56" style="mask-type: alpha;">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M822.5,189L822.5,270L822,270L822,189L822.5,189Z" fill="white" transform="translate(822.25,229.5) translate(-822.25,-229.5)"/>
        </mask>
        <mask id="mask12_1398_56" style="mask-type: alpha;">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M838,70L838,141L837.5,141L837.5,70L838,70Z" fill="white" transform="translate(837.75,105.5) translate(-837.75,-105.5)"/>
        </mask>
        <mask id="mask13_1398_56" style="mask-type: alpha;">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M822.5,70L822.5,141L822,141L822,70L822.5,70Z" fill="white" transform="translate(822.25,105.5) translate(-822.25,-105.5)"/>
        </mask>
        <mask id="mask" style="mask-type: alpha;">
            <rect width="1512" height="545.25" fill="url(#Gradient-68)" transform="translate(756,272.625) translate(-756,-272.625)"/>
        </mask>
    </defs>
    <g transform="translate(755.912,272.75) scale(0.999998,1) translate(-756.09,-272.75)">
        <g fill="none" mask="url(#mask)" transform="translate(756.177,272.625) translate(-756,-272.625)">
            <g id="objects" transform="translate(756,272.625) translate(-756,-272.625)">
                <g id="objects-2" transform="translate(756,272.625) translate(-756,-272.625)">
                    <rect width="21" height="24" rx="3" fill="white" fill-opacity="0.06" transform="translate(924.5,94) translate(-10.5,-12)"/>
                    <rect width="15" height="6" rx="1.5" fill="white" fill-opacity="0.06" transform="translate(924.5,99) translate(-7.5,-3)"/>
                    <rect width="2" height="8" fill="white" fill-opacity="0.08" transform="translate(551,280) translate(-1,-4)"/>
                    <rect width="2" height="8" fill="white" fill-opacity="0.08" transform="translate(555,280) translate(-1,-4)"/>
                    <rect width="2" height="2" fill="white" fill-opacity="0.08" transform="translate(546,280) translate(-1,-1)"/>
                    <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(546,279.5) translate(-1,-0.5)"/>
                    <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(551,276.5) translate(-1,-0.5)"/>
                    <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(555,276.5) translate(-1,-0.5)"/>
                    <rect width="2" height="8" fill="white" fill-opacity="0.08" transform="translate(570,280) translate(-1,-4)"/>
                    <rect width="2" height="8" fill="white" fill-opacity="0.08" transform="translate(574,280) translate(-1,-4)"/>
                    <rect width="2" height="2" fill="white" fill-opacity="0.08" transform="translate(565,280) translate(-1,-1)"/>
                    <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(565,279.5) translate(-1,-0.5)"/>
                    <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(570,276.5) translate(-1,-0.5)"/>
                    <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(574,276.5) translate(-1,-0.5)"/>
                    <rect width="2" height="8" fill="white" fill-opacity="0.08" transform="translate(589,280) translate(-1,-4)"/>
                    <rect width="2" height="8" fill="white" fill-opacity="0.08" transform="translate(593,280) translate(-1,-4)"/>
                    <rect width="2" height="2" fill="white" fill-opacity="0.08" transform="translate(584,280) translate(-1,-1)"/>
                    <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(584,279.5) translate(-1,-0.5)"/>
                    <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(589,276.5) translate(-1,-0.5)"/>
                    <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(593,276.5) translate(-1,-0.5)"/>
                    <rect width="2" height="8" fill="white" fill-opacity="0.08" transform="translate(589,264) translate(-1,-4)"/>
                    <rect width="2" height="8" fill="white" fill-opacity="0.08" transform="translate(593,264) translate(-1,-4)"/>
                    <rect width="2" height="2" fill="white" fill-opacity="0.08" transform="translate(584,264) translate(-1,-1)"/>
                    <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(584,263.5) translate(-1,-0.5)"/>
                    <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(589,260.5) translate(-1,-0.5)"/>
                    <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(593,260.5) translate(-1,-0.5)"/>
                    <rect width="2" height="8" fill="white" fill-opacity="0.08" transform="translate(616,80) translate(-1,-4)"/>
                    <rect width="2" height="8" fill="white" fill-opacity="0.08" transform="translate(620,80) translate(-1,-4)"/>
                    <rect width="2" height="2" fill="white" fill-opacity="0.08" transform="translate(611,80) translate(-1,-1)"/>
                    <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(611,79.5) translate(-1,-0.5)"/>
                    <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(616,76.5) translate(-1,-0.5)"/>
                    <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(620,76.5) translate(-1,-0.5)"/>
                    <rect width="2" height="8" fill="white" fill-opacity="0.08" transform="translate(1340,106) translate(-1,-4)"/>
                    <rect width="2" height="8" fill="white" fill-opacity="0.08" transform="translate(1344,106) translate(-1,-4)"/>
                    <rect width="2" height="2" fill="white" fill-opacity="0.08" transform="translate(1335,106) translate(-1,-1)"/>
                    <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(1335,105.5) translate(-1,-0.5)"/>
                    <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(1340,102.5) translate(-1,-0.5)"/>
                    <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(1344,102.5) translate(-1,-0.5)"/>
                    <rect width="2" height="8" fill="white" fill-opacity="0.08" transform="translate(635,80) translate(-1,-4)"/>
                    <rect width="2" height="8" fill="white" fill-opacity="0.08" transform="translate(639,80) translate(-1,-4)"/>
                    <rect width="2" height="2" fill="white" fill-opacity="0.08" transform="translate(630,80) translate(-1,-1)"/>
                    <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(630,79.5) translate(-1,-0.5)"/>
                    <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(635,76.5) translate(-1,-0.5)"/>
                    <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(639,76.5) translate(-1,-0.5)"/>
                    <rect width="2" height="8" fill="white" fill-opacity="0.08" transform="translate(1359,106) translate(-1,-4)"/>
                    <rect width="2" height="8" fill="white" fill-opacity="0.08" transform="translate(1363,106) translate(-1,-4)"/>
                    <rect width="2" height="2" fill="white" fill-opacity="0.08" transform="translate(1354,106) translate(-1,-1)"/>
                    <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(1354,105.5) translate(-1,-0.5)"/>
                    <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(1359,102.5) translate(-1,-0.5)"/>
                    <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(1363,102.5) translate(-1,-0.5)"/>
                    <rect width="2" height="8" fill="white" fill-opacity="0.08" transform="translate(654,80) translate(-1,-4)"/>
                    <rect width="2" height="8" fill="white" fill-opacity="0.08" transform="translate(658,80) translate(-1,-4)"/>
                    <rect width="2" height="2" fill="white" fill-opacity="0.08" transform="translate(649,80) translate(-1,-1)"/>
                    <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(649,79.5) translate(-1,-0.5)"/>
                    <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(654,76.5) translate(-1,-0.5)"/>
                    <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(658,76.5) translate(-1,-0.5)"/>
                    <rect width="2" height="8" fill="white" fill-opacity="0.08" transform="translate(1378,106) translate(-1,-4)"/>
                    <rect width="2" height="8" fill="white" fill-opacity="0.08" transform="translate(1382,106) translate(-1,-4)"/>
                    <rect width="2" height="2" fill="white" fill-opacity="0.08" transform="translate(1373,106) translate(-1,-1)"/>
                    <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(1373,105.5) translate(-1,-0.5)"/>
                    <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(1378,102.5) translate(-1,-0.5)"/>
                    <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(1382,102.5) translate(-1,-0.5)"/>
                    <rect width="8" height="2" fill="white" fill-opacity="0.08" transform="translate(814,361) translate(-4,-1)"/>
                    <rect width="8" height="1" fill="white" fill-opacity="0.04" transform="translate(814,360.5) translate(-4,-0.5)"/>
                    <rect width="8" height="2" fill="white" fill-opacity="0.08" transform="translate(814,371) translate(-4,-1)"/>
                    <rect width="8" height="1" fill="white" fill-opacity="0.04" transform="translate(814,370.5) translate(-4,-0.5)"/>
                    <rect width="2" height="4" fill="white" fill-opacity="0.08" transform="translate(812,366) translate(-1,-2)"/>
                    <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(812,364.5) translate(-1,-0.5)"/>
                    <rect width="2" height="4" fill="white" fill-opacity="0.08" transform="translate(816,366) translate(-1,-2)"/>
                    <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(816,364.5) translate(-1,-0.5)"/>
                    <rect width="8" height="2" fill="white" fill-opacity="0.08" transform="translate(331,276) translate(-4,-1)"/>
                    <rect width="8" height="1" fill="white" fill-opacity="0.04" transform="translate(331,275.5) translate(-4,-0.5)"/>
                    <rect width="8" height="2" fill="white" fill-opacity="0.08" transform="translate(331,286) translate(-4,-1)"/>
                    <rect width="8" height="1" fill="white" fill-opacity="0.04" transform="translate(331,285.5) translate(-4,-0.5)"/>
                    <rect width="2" height="4" fill="white" fill-opacity="0.08" transform="translate(329,281) translate(-1,-2)"/>
                    <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(329,279.5) translate(-1,-0.5)"/>
                    <rect width="2" height="4" fill="white" fill-opacity="0.08" transform="translate(333,281) translate(-1,-2)"/>
                    <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(333,279.5) translate(-1,-0.5)"/>
                    <rect width="8" height="2" fill="white" fill-opacity="0.08" transform="translate(1352,415) translate(-4,-1)"/>
                    <rect width="8" height="1" fill="white" fill-opacity="0.04" transform="translate(1352,414.5) translate(-4,-0.5)"/>
                    <rect width="8" height="2" fill="white" fill-opacity="0.08" transform="translate(1352,425) translate(-4,-1)"/>
                    <rect width="8" height="1" fill="white" fill-opacity="0.04" transform="translate(1352,424.5) translate(-4,-0.5)"/>
                    <rect width="2" height="4" fill="white" fill-opacity="0.08" transform="translate(1350,420) translate(-1,-2)"/>
                    <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(1350,418.5) translate(-1,-0.5)"/>
                    <rect width="2" height="4" fill="white" fill-opacity="0.08" transform="translate(1354,420) translate(-1,-2)"/>
                    <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(1354,418.5) translate(-1,-0.5)"/>
                    <rect width="8" height="2" fill="white" fill-opacity="0.08" transform="translate(798,361) translate(-4,-1)"/>
                    <rect width="8" height="1" fill="white" fill-opacity="0.04" transform="translate(798,360.5) translate(-4,-0.5)"/>
                    <rect width="8" height="2" fill="white" fill-opacity="0.08" transform="translate(798,371) translate(-4,-1)"/>
                    <rect width="8" height="1" fill="white" fill-opacity="0.04" transform="translate(798,370.5) translate(-4,-0.5)"/>
                    <rect width="2" height="4" fill="white" fill-opacity="0.08" transform="translate(796,366) translate(-1,-2)"/>
                    <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(796,364.5) translate(-1,-0.5)"/>
                    <rect width="2" height="4" fill="white" fill-opacity="0.08" transform="translate(800,366) translate(-1,-2)"/>
                    <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(800,364.5) translate(-1,-0.5)"/>
                    <rect width="8" height="2" fill="white" fill-opacity="0.08" transform="translate(315,276) translate(-4,-1)"/>
                    <rect width="8" height="1" fill="white" fill-opacity="0.04" transform="translate(315,275.5) translate(-4,-0.5)"/>
                    <rect width="8" height="2" fill="white" fill-opacity="0.08" transform="translate(315,286) translate(-4,-1)"/>
                    <rect width="8" height="1" fill="white" fill-opacity="0.04" transform="translate(315,285.5) translate(-4,-0.5)"/>
                    <rect width="2" height="4" fill="white" fill-opacity="0.08" transform="translate(313,281) translate(-1,-2)"/>
                    <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(313,279.5) translate(-1,-0.5)"/>
                    <rect width="2" height="4" fill="white" fill-opacity="0.08" transform="translate(317,281) translate(-1,-2)"/>
                    <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(317,279.5) translate(-1,-0.5)"/>
                    <rect width="8" height="2" fill="white" fill-opacity="0.08" transform="translate(1336,415) translate(-4,-1)"/>
                    <rect width="8" height="1" fill="white" fill-opacity="0.04" transform="translate(1336,414.5) translate(-4,-0.5)"/>
                    <rect width="8" height="2" fill="white" fill-opacity="0.08" transform="translate(1336,425) translate(-4,-1)"/>
                    <rect width="8" height="1" fill="white" fill-opacity="0.04" transform="translate(1336,424.5) translate(-4,-0.5)"/>
                    <rect width="2" height="4" fill="white" fill-opacity="0.08" transform="translate(1334,420) translate(-1,-2)"/>
                    <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(1334,418.5) translate(-1,-0.5)"/>
                    <rect width="2" height="4" fill="white" fill-opacity="0.08" transform="translate(1338,420) translate(-1,-2)"/>
                    <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(1338,418.5) translate(-1,-0.5)"/>
                    <rect width="8" height="2" fill="white" fill-opacity="0.08" transform="translate(782,361) translate(-4,-1)"/>
                    <rect width="8" height="1" fill="white" fill-opacity="0.04" transform="translate(782,360.5) translate(-4,-0.5)"/>
                    <rect width="8" height="2" fill="white" fill-opacity="0.08" transform="translate(782,371) translate(-4,-1)"/>
                    <rect width="8" height="1" fill="white" fill-opacity="0.04" transform="translate(782,370.5) translate(-4,-0.5)"/>
                    <rect width="2" height="4" fill="white" fill-opacity="0.08" transform="translate(780,366) translate(-1,-2)"/>
                    <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(780,364.5) translate(-1,-0.5)"/>
                    <rect width="2" height="4" fill="white" fill-opacity="0.08" transform="translate(784,366) translate(-1,-2)"/>
                    <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(784,364.5) translate(-1,-0.5)"/>
                    <rect width="8" height="2" fill="white" fill-opacity="0.08" transform="translate(299,276) translate(-4,-1)"/>
                    <rect width="8" height="1" fill="white" fill-opacity="0.04" transform="translate(299,275.5) translate(-4,-0.5)"/>
                    <rect width="8" height="2" fill="white" fill-opacity="0.08" transform="translate(299,286) translate(-4,-1)"/>
                    <rect width="8" height="1" fill="white" fill-opacity="0.04" transform="translate(299,285.5) translate(-4,-0.5)"/>
                    <rect width="2" height="4" fill="white" fill-opacity="0.08" transform="translate(297,281) translate(-1,-2)"/>
                    <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(297,279.5) translate(-1,-0.5)"/>
                    <rect width="2" height="4" fill="white" fill-opacity="0.08" transform="translate(301,281) translate(-1,-2)"/>
                    <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(301,279.5) translate(-1,-0.5)"/>
                    <rect width="8" height="2" fill="white" fill-opacity="0.08" transform="translate(1320,415) translate(-4,-1)"/>
                    <rect width="8" height="1" fill="white" fill-opacity="0.04" transform="translate(1320,414.5) translate(-4,-0.5)"/>
                    <rect width="8" height="2" fill="white" fill-opacity="0.08" transform="translate(1320,425) translate(-4,-1)"/>
                    <rect width="8" height="1" fill="white" fill-opacity="0.04" transform="translate(1320,424.5) translate(-4,-0.5)"/>
                    <rect width="2" height="4" fill="white" fill-opacity="0.08" transform="translate(1318,420) translate(-1,-2)"/>
                    <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(1318,418.5) translate(-1,-0.5)"/>
                    <rect width="2" height="4" fill="white" fill-opacity="0.08" transform="translate(1322,420) translate(-1,-2)"/>
                    <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(1322,418.5) translate(-1,-0.5)"/>
                    <rect width="8" height="2" fill="white" fill-opacity="0.08" transform="translate(720,112) translate(-4,-1)"/>
                    <rect width="8" height="1" fill="white" fill-opacity="0.04" transform="translate(720,111.5) translate(-4,-0.5)"/>
                    <rect width="8" height="2" fill="white" fill-opacity="0.08" transform="translate(720,122) translate(-4,-1)"/>
                    <rect width="8" height="1" fill="white" fill-opacity="0.04" transform="translate(720,121.5) translate(-4,-0.5)"/>
                    <rect width="2" height="4" fill="white" fill-opacity="0.08" transform="translate(718,117) translate(-1,-2)"/>
                    <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(718,115.5) translate(-1,-0.5)"/>
                    <rect width="2" height="4" fill="white" fill-opacity="0.08" transform="translate(722,117) translate(-1,-2)"/>
                    <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(722,115.5) translate(-1,-0.5)"/>
                    <rect width="8" height="2" fill="white" fill-opacity="0.08" transform="translate(500,322) translate(-4,-1)"/>
                    <rect width="8" height="1" fill="white" fill-opacity="0.04" transform="translate(500,321.5) translate(-4,-0.5)"/>
                    <rect width="8" height="2" fill="white" fill-opacity="0.08" transform="translate(500,332) translate(-4,-1)"/>
                    <rect width="8" height="1" fill="white" fill-opacity="0.04" transform="translate(500,331.5) translate(-4,-0.5)"/>
                    <rect width="2" height="4" fill="white" fill-opacity="0.08" transform="translate(498,327) translate(-1,-2)"/>
                    <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(498,325.5) translate(-1,-0.5)"/>
                    <rect width="2" height="4" fill="white" fill-opacity="0.08" transform="translate(502,327) translate(-1,-2)"/>
                    <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(502,325.5) translate(-1,-0.5)"/>
                    <rect width="8" height="2" fill="white" fill-opacity="0.08" transform="translate(1220,202) translate(-4,-1)"/>
                    <rect width="8" height="1" fill="white" fill-opacity="0.04" transform="translate(1220,201.5) translate(-4,-0.5)"/>
                    <rect width="8" height="2" fill="white" fill-opacity="0.08" transform="translate(1220,212) translate(-4,-1)"/>
                    <rect width="8" height="1" fill="white" fill-opacity="0.04" transform="translate(1220,211.5) translate(-4,-0.5)"/>
                    <rect width="2" height="4" fill="white" fill-opacity="0.08" transform="translate(1218,207) translate(-1,-2)"/>
                    <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(1218,205.5) translate(-1,-0.5)"/>
                    <rect width="2" height="4" fill="white" fill-opacity="0.08" transform="translate(1222,207) translate(-1,-2)"/>
                    <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(1222,205.5) translate(-1,-0.5)"/>
                    <rect width="8" height="2" fill="white" fill-opacity="0.08" transform="translate(1220,222) translate(-4,-1)"/>
                    <rect width="8" height="1" fill="white" fill-opacity="0.04" transform="translate(1220,221.5) translate(-4,-0.5)"/>
                    <rect width="8" height="2" fill="white" fill-opacity="0.08" transform="translate(1220,232) translate(-4,-1)"/>
                    <rect width="8" height="1" fill="white" fill-opacity="0.04" transform="translate(1220,231.5) translate(-4,-0.5)"/>
                    <rect width="2" height="4" fill="white" fill-opacity="0.08" transform="translate(1218,227) translate(-1,-2)"/>
                    <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(1218,225.5) translate(-1,-0.5)"/>
                    <rect width="2" height="4" fill="white" fill-opacity="0.08" transform="translate(1222,227) translate(-1,-2)"/>
                    <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(1222,225.5) translate(-1,-0.5)"/>
                    <rect width="8" height="2" fill="white" fill-opacity="0.08" transform="translate(1236,202) translate(-4,-1)"/>
                    <rect width="8" height="1" fill="white" fill-opacity="0.04" transform="translate(1236,201.5) translate(-4,-0.5)"/>
                    <rect width="8" height="2" fill="white" fill-opacity="0.08" transform="translate(1236,212) translate(-4,-1)"/>
                    <rect width="8" height="1" fill="white" fill-opacity="0.04" transform="translate(1236,211.5) translate(-4,-0.5)"/>
                    <rect width="2" height="4" fill="white" fill-opacity="0.08" transform="translate(1234,207) translate(-1,-2)"/>
                    <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(1234,205.5) translate(-1,-0.5)"/>
                    <rect width="2" height="4" fill="white" fill-opacity="0.08" transform="translate(1238,207) translate(-1,-2)"/>
                    <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(1238,205.5) translate(-1,-0.5)"/>
                    <rect width="8" height="2" fill="white" fill-opacity="0.08" transform="translate(516,322) translate(-4,-1)"/>
                    <rect width="8" height="1" fill="white" fill-opacity="0.04" transform="translate(516,321.5) translate(-4,-0.5)"/>
                    <rect width="8" height="2" fill="white" fill-opacity="0.08" transform="translate(516,332) translate(-4,-1)"/>
                    <rect width="8" height="1" fill="white" fill-opacity="0.04" transform="translate(516,331.5) translate(-4,-0.5)"/>
                    <rect width="2" height="4" fill="white" fill-opacity="0.08" transform="translate(514,327) translate(-1,-2)"/>
                    <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(514,325.5) translate(-1,-0.5)"/>
                    <rect width="2" height="4" fill="white" fill-opacity="0.08" transform="translate(518,327) translate(-1,-2)"/>
                    <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(518,325.5) translate(-1,-0.5)"/>
                    <rect width="8" height="2" fill="white" fill-opacity="0.08" transform="translate(500,342) translate(-4,-1)"/>
                    <rect width="8" height="1" fill="white" fill-opacity="0.04" transform="translate(500,341.5) translate(-4,-0.5)"/>
                    <rect width="8" height="2" fill="white" fill-opacity="0.08" transform="translate(500,352) translate(-4,-1)"/>
                    <rect width="8" height="1" fill="white" fill-opacity="0.04" transform="translate(500,351.5) translate(-4,-0.5)"/>
                    <rect width="2" height="4" fill="white" fill-opacity="0.08" transform="translate(498,347) translate(-1,-2)"/>
                    <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(498,345.5) translate(-1,-0.5)"/>
                    <rect width="2" height="4" fill="white" fill-opacity="0.08" transform="translate(502,347) translate(-1,-2)"/>
                    <rect width="2" height="1" fill="white" fill-opacity="0.04" transform="translate(502,345.5) translate(-1,-0.5)"/>
                    <rect width="8" height="8" fill="white" fill-opacity="0.04" transform="translate(668,363) translate(-4,-4)"/>
                    <rect width="7.5" height="7.5" stroke="white" stroke-opacity="0.08" stroke-width="0.5" transform="translate(668,363) translate(-3.75,-3.75)"/>
                    <rect width="2" height="2" fill="white" fill-opacity="0.08" transform="translate(669,364) translate(-1,-1)"/>
                    <rect width="15.5" height="15.5" stroke="white" stroke-opacity="0.08" stroke-width="0.5" transform="translate(668,363) translate(-7.75,-7.75)"/>
                    <rect width="2" height="2" fill="white" fill-opacity="0.08" transform="translate(752,127) translate(-1,-1)"/>
                    <rect width="2" height="2" fill="white" fill-opacity="0.08" transform="translate(758,127) translate(-1,-1)"/>
                    <rect width="2" height="2" fill="white" fill-opacity="0.08" transform="translate(752,133) translate(-1,-1)"/>
                    <rect width="2" height="2" fill="white" fill-opacity="0.08" transform="translate(758,133) translate(-1,-1)"/>
                    <rect width="2" height="2" fill="white" fill-opacity="0.08" transform="translate(752,139) translate(-1,-1)"/>
                    <rect width="2" height="2" fill="white" fill-opacity="0.08" transform="translate(758,139) translate(-1,-1)"/>
                    <rect width="2" height="2" fill="white" fill-opacity="0.08" transform="translate(752,145) translate(-1,-1)"/>
                    <rect width="2" height="2" fill="white" fill-opacity="0.08" transform="translate(758,145) translate(-1,-1)"/>
                    <rect width="2" height="2" fill="white" fill-opacity="0.08" transform="translate(752,151) translate(-1,-1)"/>
                    <rect width="2" height="2" fill="white" fill-opacity="0.08" transform="translate(758,151) translate(-1,-1)"/>
                    <rect width="2" height="2" fill="white" fill-opacity="0.08" transform="translate(669,322) translate(-1,-1)"/>
                    <rect width="15.5" height="15.5" stroke="white" stroke-opacity="0.08" stroke-width="0.5" transform="translate(668,321) translate(-7.75,-7.75)"/>
                    <rect width="8" height="8" fill="white" fill-opacity="0.04" transform="translate(668,321) translate(-4,-4)"/>
                    <rect width="7.5" height="7.5" stroke="white" stroke-opacity="0.08" stroke-width="0.5" transform="translate(668,321) translate(-3.75,-3.75)"/>
                    <rect width="2" height="2" fill="white" fill-opacity="0.08" transform="translate(1354,201) translate(-1,-1)"/>
                    <rect width="15.5" height="15.5" stroke="white" stroke-opacity="0.08" stroke-width="0.5" transform="translate(1353,200) translate(-7.75,-7.75)"/>
                    <rect width="8" height="8" fill="white" fill-opacity="0.04" transform="translate(1353,200) translate(-4,-4)"/>
                    <rect width="7.5" height="7.5" stroke="white" stroke-opacity="0.08" stroke-width="0.5" transform="translate(1353,200) translate(-3.75,-3.75)"/>
                    <rect width="2" height="6" fill="white" fill-opacity="0.08" transform="translate(672,216) translate(-1,-3)"/>
                    <rect width="2" height="6" fill="white" fill-opacity="0.08" transform="translate(677,216) translate(-1,-3)"/>
                    <rect width="2" height="6" fill="white" fill-opacity="0.08" transform="translate(682,216) translate(-1,-3)"/>
                    <rect width="2" height="6" fill="white" fill-opacity="0.08" transform="translate(687,216) translate(-1,-3)"/>
                    <rect width="2" height="6" fill="white" fill-opacity="0.08" transform="translate(692,216) translate(-1,-3)"/>
                    <rect width="2" height="6" fill="white" fill-opacity="0.08" transform="translate(697,216) translate(-1,-3)"/>
                    <rect width="2" height="6" fill="white" fill-opacity="0.08" transform="translate(915,151) translate(-1,-3)"/>
                    <rect width="2" height="6" fill="white" fill-opacity="0.08" transform="translate(920,151) translate(-1,-3)"/>
                    <rect width="2" height="6" fill="white" fill-opacity="0.08" transform="translate(925,151) translate(-1,-3)"/>
                    <rect width="2" height="6" fill="white" fill-opacity="0.08" transform="translate(930,151) translate(-1,-3)"/>
                    <rect width="2" height="6" fill="white" fill-opacity="0.08" transform="translate(935,151) translate(-1,-3)"/>
                    <rect width="2" height="6" fill="white" fill-opacity="0.08" transform="translate(940,151) translate(-1,-3)"/>
                    <rect width="2" height="6" fill="white" fill-opacity="0.08" transform="translate(911,430) translate(-1,-3)"/>
                    <rect width="2" height="6" fill="white" fill-opacity="0.08" transform="translate(916,430) translate(-1,-3)"/>
                    <rect width="2" height="6" fill="white" fill-opacity="0.08" transform="translate(921,430) translate(-1,-3)"/>
                    <rect width="2" height="6" fill="white" fill-opacity="0.08" transform="translate(926,430) translate(-1,-3)"/>
                    <rect width="2" height="6" fill="white" fill-opacity="0.08" transform="translate(931,430) translate(-1,-3)"/>
                    <rect width="2" height="6" fill="white" fill-opacity="0.08" transform="translate(936,430) translate(-1,-3)"/>
                    <rect width="2" height="6" fill="white" fill-opacity="0.08" transform="translate(1217,412) translate(-1,-3)"/>
                    <rect width="2" height="6" fill="white" fill-opacity="0.08" transform="translate(1222,412) translate(-1,-3)"/>
                    <rect width="2" height="6" fill="white" fill-opacity="0.08" transform="translate(1227,412) translate(-1,-3)"/>
                    <rect width="2" height="6" fill="white" fill-opacity="0.08" transform="translate(1232,412) translate(-1,-3)"/>
                    <rect width="2" height="6" fill="white" fill-opacity="0.08" transform="translate(1237,412) translate(-1,-3)"/>
                    <rect width="2" height="6" fill="white" fill-opacity="0.08" transform="translate(1242,412) translate(-1,-3)"/>
                    <rect width="2" height="6" fill="white" fill-opacity="0.08" transform="translate(1025,82) translate(-1,-3)"/>
                    <rect width="2" height="6" fill="white" fill-opacity="0.08" transform="translate(1030,82) translate(-1,-3)"/>
                    <rect width="2" height="6" fill="white" fill-opacity="0.08" transform="translate(1035,82) translate(-1,-3)"/>
                    <rect width="2" height="6" fill="white" fill-opacity="0.08" transform="translate(1040,82) translate(-1,-3)"/>
                    <rect width="2" height="6" fill="white" fill-opacity="0.08" transform="translate(1045,82) translate(-1,-3)"/>
                    <rect width="2" height="6" fill="white" fill-opacity="0.08" transform="translate(1050,82) translate(-1,-3)"/>
                    <path d="M736.25,98.25L591.25,98.25L591.25,20" stroke="white" stroke-opacity="0.06" stroke-width="0.5" transform="translate(663.75,59.125) translate(-663.75,-59.125)"/>
                    <g clip-path="url(#clip0_1398_56)" transform="translate(650,306) translate(-650,-306)">
                        <path d="M518,386C523.333,380.667,626.889,277.111,678,226" stroke="white" stroke-opacity="0.1" stroke-width="0.3" transform="translate(598,306) translate(-598,-306)"/>
                        <path d="M526,386C531.333,380.667,634.889,277.111,686,226" stroke="white" stroke-opacity="0.1" stroke-width="0.3" transform="translate(606,306) translate(-606,-306)"/>
                        <path d="M534,386C539.333,380.667,642.889,277.111,694,226" stroke="white" stroke-opacity="0.1" stroke-width="0.3" transform="translate(614,306) translate(-614,-306)"/>
                        <path d="M542,386C547.333,380.667,650.889,277.111,702,226" stroke="white" stroke-opacity="0.1" stroke-width="0.3" transform="translate(622,306) translate(-622,-306)"/>
                        <path d="M550,386C555.333,380.667,658.889,277.111,710,226" stroke="white" stroke-opacity="0.1" stroke-width="0.3" transform="translate(630,306) translate(-630,-306)"/>
                        <path d="M558,386C563.333,380.667,666.889,277.111,718,226" stroke="white" stroke-opacity="0.1" stroke-width="0.3" transform="translate(638,306) translate(-638,-306)"/>
                        <path d="M566,386C571.333,380.667,674.889,277.111,726,226" stroke="white" stroke-opacity="0.1" stroke-width="0.3" transform="translate(646,306) translate(-646,-306)"/>
                        <path d="M574,386C579.333,380.667,682.889,277.111,734,226" stroke="white" stroke-opacity="0.1" stroke-width="0.3" transform="translate(654,306) translate(-654,-306)"/>
                        <path d="M582,386C587.333,380.667,690.889,277.111,742,226" stroke="white" stroke-opacity="0.1" stroke-width="0.3" transform="translate(662,306) translate(-662,-306)"/>
                        <path d="M590,386C595.333,380.667,698.889,277.111,750,226" stroke="white" stroke-opacity="0.1" stroke-width="0.3" transform="translate(670,306) translate(-670,-306)"/>
                        <path d="M598,386C603.333,380.667,706.889,277.111,758,226" stroke="white" stroke-opacity="0.1" stroke-width="0.3" transform="translate(678,306) translate(-678,-306)"/>
                        <path d="M606,386C611.333,380.667,714.889,277.111,766,226" stroke="white" stroke-opacity="0.1" stroke-width="0.3" transform="translate(686,306) translate(-686,-306)"/>
                        <path d="M614,386C619.333,380.667,722.889,277.111,774,226" stroke="white" stroke-opacity="0.1" stroke-width="0.3" transform="translate(694,306) translate(-694,-306)"/>
                        <path d="M622,386C627.333,380.667,730.889,277.111,782,226" stroke="white" stroke-opacity="0.1" stroke-width="0.3" transform="translate(702,306) translate(-702,-306)"/>
                    </g>
                    <g clip-path="url(#clip1_1398_56)" transform="translate(901,366) translate(-901,-366)">
                        <path d="M769,446C774.333,440.667,877.889,337.111,929,286" stroke="white" stroke-opacity="0.1" stroke-width="0.3" transform="translate(849,366) translate(-849,-366)"/>
                        <path d="M777,446C782.333,440.667,885.889,337.111,937,286" stroke="white" stroke-opacity="0.1" stroke-width="0.3" transform="translate(857,366) translate(-857,-366)"/>
                        <path d="M785,446C790.333,440.667,893.889,337.111,945,286" stroke="white" stroke-opacity="0.1" stroke-width="0.3" transform="translate(865,366) translate(-865,-366)"/>
                        <path d="M793,446C798.333,440.667,901.889,337.111,953,286" stroke="white" stroke-opacity="0.1" stroke-width="0.3" transform="translate(873,366) translate(-873,-366)"/>
                        <path d="M801,446C806.333,440.667,909.889,337.111,961,286" stroke="white" stroke-opacity="0.1" stroke-width="0.3" transform="translate(881,366) translate(-881,-366)"/>
                        <path d="M809,446C814.333,440.667,917.889,337.111,969,286" stroke="white" stroke-opacity="0.1" stroke-width="0.3" transform="translate(889,366) translate(-889,-366)"/>
                        <path d="M817,446C822.333,440.667,925.889,337.111,977,286" stroke="white" stroke-opacity="0.1" stroke-width="0.3" transform="translate(897,366) translate(-897,-366)"/>
                        <path d="M825,446C830.333,440.667,933.889,337.111,985,286" stroke="white" stroke-opacity="0.1" stroke-width="0.3" transform="translate(905,366) translate(-905,-366)"/>
                        <path d="M833,446C838.333,440.667,941.889,337.111,993,286" stroke="white" stroke-opacity="0.1" stroke-width="0.3" transform="translate(913,366) translate(-913,-366)"/>
                        <path d="M841,446C846.333,440.667,949.889,337.111,1001,286" stroke="white" stroke-opacity="0.1" stroke-width="0.3" transform="translate(921,366) translate(-921,-366)"/>
                        <path d="M849,446C854.333,440.667,957.889,337.111,1009,286" stroke="white" stroke-opacity="0.1" stroke-width="0.3" transform="translate(929,366) translate(-929,-366)"/>
                        <path d="M857,446C862.333,440.667,965.889,337.111,1017,286" stroke="white" stroke-opacity="0.1" stroke-width="0.3" transform="translate(937,366) translate(-937,-366)"/>
                        <path d="M865,446C870.333,440.667,973.889,337.111,1025,286" stroke="white" stroke-opacity="0.1" stroke-width="0.3" transform="translate(945,366) translate(-945,-366)"/>
                        <path d="M873,446C878.333,440.667,981.889,337.111,1033,286" stroke="white" stroke-opacity="0.1" stroke-width="0.3" transform="translate(953,366) translate(-953,-366)"/>
                    </g>
                    <path d="M802,299.25L263.604,299.25C259.69,299.25,257.734,299.25,255.892,298.808C254.26,298.416,252.699,297.77,251.267,296.892C249.653,295.903,248.269,294.519,245.502,291.752L0,46.25" stroke="white" stroke-opacity="0.08" stroke-width="0.5" transform="translate(401,172.75) translate(-401,-172.75)"/>
                    <path d="M711,199.25L602.604,199.25C598.69,199.25,596.734,199.25,594.892,199.692C593.26,200.084,591.699,200.73,590.267,201.608C588.653,202.597,587.269,203.981,584.502,206.748L492,299.25" stroke="white" stroke-opacity="0.08" stroke-width="0.5" transform="translate(601.5,249.25) translate(-601.5,-249.25)"/>
                    <path d="M830,384.25L493.604,384.25C489.69,384.25,487.734,384.25,485.892,383.808C484.26,383.416,482.699,382.77,481.267,381.892C479.653,380.903,478.269,379.519,475.502,376.752L398,299.25" stroke="white" stroke-opacity="0.08" stroke-width="0.5" transform="translate(614,341.75) translate(-614,-341.75)"/>
                    <path d="M802,165.25L711.25,165.25L711.25,299" stroke="white" stroke-opacity="0.08" stroke-width="0.5" transform="translate(756.625,232.125) translate(-756.625,-232.125)"/>
                    <path d="M281.25,299L281.25,245.25L441.25,245.25L441.25,299" stroke="white" stroke-opacity="0.08" stroke-width="0.5" transform="translate(361.25,272.125) translate(-361.25,-272.125)"/>
                    <rect width="2" height="2" fill="white" fill-opacity="0.08" transform="translate(420,261) translate(-1,-1)"/>
                    <rect width="2" height="2" fill="white" fill-opacity="0.08" transform="translate(426,261) translate(-1,-1)"/>
                    <rect width="2" height="2" fill="white" fill-opacity="0.08" transform="translate(420,267) translate(-1,-1)"/>
                    <rect width="2" height="2" fill="white" fill-opacity="0.08" transform="translate(426,267) translate(-1,-1)"/>
                    <rect width="2" height="2" fill="white" fill-opacity="0.08" transform="translate(420,273) translate(-1,-1)"/>
                    <rect width="2" height="2" fill="white" fill-opacity="0.08" transform="translate(426,273) translate(-1,-1)"/>
                    <rect width="2" height="2" fill="white" fill-opacity="0.08" transform="translate(420,279) translate(-1,-1)"/>
                    <rect width="2" height="2" fill="white" fill-opacity="0.08" transform="translate(426,279) translate(-1,-1)"/>
                    <rect width="2" height="2" fill="white" fill-opacity="0.08" transform="translate(420,285) translate(-1,-1)"/>
                    <rect width="2" height="2" fill="white" fill-opacity="0.08" transform="translate(426,285) translate(-1,-1)"/>
                    <path d="M1380,395.25L1202.25,395.25L1202.25,529" stroke="white" stroke-opacity="0.08" stroke-width="0.5" transform="translate(1291.12,462.125) translate(-1291.12,-462.125)"/>
                    <path d="M1512,492.25L1334.25,492.25L1334.25,545" stroke="white" stroke-opacity="0.08" stroke-width="0.5" transform="translate(1423.12,518.625) translate(-1423.12,-518.625)"/>
                    <path d="M1262.25,545.25L1440,545.25" stroke="white" stroke-opacity="0.08" stroke-width="0.5" transform="translate(1351.12,545.25) translate(-1351.12,-545.25)"/>
                    <path d="M711.5,214.25L765,214.25" stroke="white" stroke-opacity="0.08" stroke-width="0.5" transform="translate(738.25,214.25) translate(-738.25,-214.25)"/>
                    <rect opacity="0.02" width="49" height="79" rx="0.5" fill="white" transform="translate(738.5,256.5) translate(-24.5,-39.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(717.5,220.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(717.5,226.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(717.5,232.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(717.5,238.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(717.5,244.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.1" width="3" height="3" rx="0.5" fill="white" transform="translate(717.5,250.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(717.5,256.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(717.5,262.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(717.5,268.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(717.5,274.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(717.5,280.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(717.5,286.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(717.5,292.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(724.5,220.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.15" width="3" height="3" rx="0.5" fill="white" transform="translate(724.5,226.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(724.5,232.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(724.5,238.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(724.5,244.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(724.5,250.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(724.5,256.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(724.5,262.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(724.5,268.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.15" width="3" height="3" rx="0.5" fill="white" transform="translate(724.5,274.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(724.5,280.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(724.5,286.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(724.5,292.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(731.5,220.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(731.5,226.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(731.5,232.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(731.5,238.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.1" width="3" height="3" rx="0.5" fill="white" transform="translate(731.5,244.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(731.5,250.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(731.5,256.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(731.5,262.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(731.5,268.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(731.5,274.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.1" width="3" height="3" rx="0.5" fill="white" transform="translate(731.5,280.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(731.5,286.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(731.5,292.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(738.5,220.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(738.5,226.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(738.5,232.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(738.5,238.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(738.5,244.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(738.5,250.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(738.5,256.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(738.5,262.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(738.5,268.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(738.5,274.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(738.5,280.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(738.5,286.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(738.5,292.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(745.5,220.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(745.5,226.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(745.5,232.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.1" width="3" height="3" rx="0.5" fill="white" transform="translate(745.5,238.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(745.5,244.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(745.5,250.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.1" width="3" height="3" rx="0.5" fill="white" transform="translate(745.5,256.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(745.5,262.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(745.5,268.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(745.5,274.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(745.5,280.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(745.5,286.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(745.5,292.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(752.5,220.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.1" width="3" height="3" rx="0.5" fill="white" transform="translate(752.5,226.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(752.5,232.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(752.5,238.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(752.5,244.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(752.5,250.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(752.5,256.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(752.5,262.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.1" width="3" height="3" rx="0.5" fill="white" transform="translate(752.5,268.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(752.5,274.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(752.5,280.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(752.5,286.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(752.5,292.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(759.5,220.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(759.5,226.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(759.5,232.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(759.5,238.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(759.5,244.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(759.5,250.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(759.5,256.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(759.5,262.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(759.5,268.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(759.5,274.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(759.5,280.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(759.5,286.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.15" width="3" height="3" rx="0.5" fill="white" transform="translate(759.5,292.5) translate(-1.5,-1.5)"/>
                    <path d="M765.25,165.5L765.25,299" stroke="white" stroke-opacity="0.08" stroke-width="0.5" transform="translate(765.25,232.25) translate(-765.25,-232.25)"/>
                    <path d="M1014.25,262.25L917.25,262.25L917.25,213.5" stroke="white" stroke-opacity="0.08" stroke-width="0.5" transform="translate(965.75,237.875) translate(-965.75,-237.875)"/>
                    <path d="M1374.25,219.25L1277.25,219.25L1277.25,90.5" stroke="white" stroke-opacity="0.08" stroke-width="0.5" transform="translate(1325.75,154.875) translate(-1325.75,-154.875)"/>
                    <path d="M830.25,338L830.25,416.25L947.25,416.25L947.25,494" stroke="white" stroke-opacity="0.08" stroke-width="0.5" transform="translate(888.75,416) translate(-888.75,-416)"/>
                    <path d="M901,70L901,165" stroke="white" stroke-opacity="0.04" stroke-width="2" transform="translate(901,117.5) translate(-901,-117.5)"/>
                    <path d="M737,70L737,165" stroke="white" stroke-opacity="0.04" stroke-width="2" transform="translate(737,117.5) translate(-737,-117.5)"/>
                    <path d="M647,299.5L647,384" stroke="white" stroke-opacity="0.04" stroke-width="2" transform="translate(647,341.75) translate(-647,-341.75)"/>
                    <path d="M689,299.5L689,384" stroke="white" stroke-opacity="0.04" stroke-width="2" transform="translate(689,341.75) translate(-689,-341.75)"/>
                    <path d="M1268,395.5L1268,480" stroke="white" stroke-opacity="0.04" stroke-width="2" transform="translate(1268,437.75) translate(-1268,-437.75)"/>
                    <path d="M759,384.5L759,498" stroke="white" stroke-opacity="0.04" stroke-width="2" transform="translate(759,441.25) translate(-759,-441.25)"/>
                    <path d="M858,165.25L982.378,165.25C984.348,165.25,985.333,165.25,986.28,165.066C987.119,164.902,987.935,164.632,988.706,164.261C989.575,163.842,990.364,163.253,991.944,162.075L1009.1,149.277C1010.99,147.869,1011.93,147.165,1012.61,146.272C1013.22,145.48,1013.67,144.584,1013.94,143.627C1014.25,142.548,1014.25,141.37,1014.25,139.016L1014.25,70" stroke="white" stroke-opacity="0.08" stroke-width="0.5" transform="translate(936.125,117.625) translate(-936.125,-117.625)"/>
                    <path d="M1014,95.25L1090.25,95.25L1090.25,0" stroke="white" stroke-opacity="0.08" stroke-width="0.5" transform="translate(1052.12,47.625) translate(-1052.12,-47.625)"/>
                    <path d="M963.25,165.25L963.25,213.25L886.25,213.25L886.25,165.25" stroke="white" stroke-opacity="0.08" stroke-width="0.5" transform="translate(924.75,189.25) translate(-924.75,-189.25)"/>
                    <rect opacity="0.02" width="70" height="42" rx="0.5" fill="white" transform="translate(925,189) translate(-35,-21)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(892.5,171.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(892.5,178.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(892.5,185.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(892.5,192.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(892.5,199.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(892.5,206.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(900.5,171.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(900.5,178.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.1" width="3" height="3" rx="0.5" fill="white" transform="translate(900.5,185.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(900.5,192.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(900.5,199.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(900.5,206.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(908.5,171.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(908.5,178.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(908.5,185.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(908.5,192.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(908.5,199.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.15" width="3" height="3" rx="0.5" fill="white" transform="translate(908.5,206.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.1" width="3" height="3" rx="0.5" fill="white" transform="translate(916.5,171.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(916.5,178.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(916.5,185.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(916.5,192.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(916.5,199.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(916.5,206.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(924.5,171.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(924.5,178.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(924.5,185.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.15" width="3" height="3" rx="0.5" fill="white" transform="translate(924.5,192.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(924.5,199.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(924.5,206.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(932.5,171.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(932.5,178.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(932.5,185.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(932.5,192.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(932.5,199.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(932.5,206.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(940.5,171.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(940.5,178.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.15" width="3" height="3" rx="0.5" fill="white" transform="translate(940.5,185.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(940.5,192.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(940.5,199.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(940.5,206.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.1" width="3" height="3" rx="0.5" fill="white" transform="translate(948.5,171.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(948.5,178.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(948.5,185.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(948.5,192.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(948.5,199.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.1" width="3" height="3" rx="0.5" fill="white" transform="translate(948.5,206.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(956.5,171.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(956.5,178.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(956.5,185.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(956.5,192.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(956.5,199.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(956.5,206.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.02" width="70" height="42" rx="0.5" fill="white" transform="translate(1375,519) translate(-35,-21)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1342.5,501.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1342.5,508.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1342.5,515.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1342.5,522.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1342.5,529.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1342.5,536.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1350.5,501.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1350.5,508.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.1" width="3" height="3" rx="0.5" fill="white" transform="translate(1350.5,515.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1350.5,522.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1350.5,529.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1350.5,536.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1358.5,501.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1358.5,508.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1358.5,515.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1358.5,522.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1358.5,529.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.15" width="3" height="3" rx="0.5" fill="white" transform="translate(1358.5,536.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.1" width="3" height="3" rx="0.5" fill="white" transform="translate(1366.5,501.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1366.5,508.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1366.5,515.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1366.5,522.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1366.5,529.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1366.5,536.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1374.5,501.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1374.5,508.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1374.5,515.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.15" width="3" height="3" rx="0.5" fill="white" transform="translate(1374.5,522.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1374.5,529.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1374.5,536.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1382.5,501.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1382.5,508.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1382.5,515.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1382.5,522.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1382.5,529.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1382.5,536.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1390.5,501.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1390.5,508.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.15" width="3" height="3" rx="0.5" fill="white" transform="translate(1390.5,515.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1390.5,522.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1390.5,529.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1390.5,536.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.1" width="3" height="3" rx="0.5" fill="white" transform="translate(1398.5,501.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1398.5,508.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1398.5,515.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1398.5,522.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1398.5,529.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.1" width="3" height="3" rx="0.5" fill="white" transform="translate(1398.5,536.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1406.5,501.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1406.5,508.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1406.5,515.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1406.5,522.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1406.5,529.5) translate(-1.5,-1.5)"/>
                    <rect opacity="0.05" width="3" height="3" rx="0.5" fill="white" transform="translate(1406.5,536.5) translate(-1.5,-1.5)"/>
                    <path d="M1370,90L1196.59,90C1192.68,90,1190.73,90,1188.89,90.4407C1187.26,90.8314,1185.7,91.4758,1184.27,92.3505C1182.65,93.3369,1181.27,94.7164,1178.51,97.4755L1093.27,182.498C1090.5,185.267,1089.11,186.651,1088.12,188.267C1087.24,189.7,1086.59,191.263,1086.19,192.898C1085.75,194.742,1085.75,196.702,1085.75,200.623L1085.75,233.75" stroke="white" stroke-opacity="0.1" stroke-width="0.5" transform="translate(1227.88,161.875) translate(-1227.88,-161.875)"/>
                </g>
                <g id="dots" transform="translate(991.5,261.5) translate(-170.5,-120.5)">
                    <g id="dots-2" filter="url(#filter0_b_1398_56)" transform="translate(264.75,91) translate(-1085.75,-232)">
                        <path d="M1084.5,230.8C1084.5,230.52,1084.5,230.38,1084.55,230.273C1084.6,230.179,1084.68,230.102,1084.77,230.054C1084.88,230,1085.02,230,1085.3,230L1086.2,230C1086.48,230,1086.62,230,1086.73,230.054C1086.82,230.102,1086.9,230.179,1086.95,230.273C1087,230.38,1087,230.52,1087,230.8L1087,234L1084.5,234L1084.5,230.8Z" fill="black" transform="translate(1085.75,232) translate(-1085.75,-232)"/>
                        <path d="M1084.5,230.8C1084.5,230.52,1084.5,230.38,1084.55,230.273C1084.6,230.179,1084.68,230.102,1084.77,230.054C1084.88,230,1085.02,230,1085.3,230L1086.2,230C1086.48,230,1086.62,230,1086.73,230.054C1086.82,230.102,1086.9,230.179,1086.95,230.273C1087,230.38,1087,230.52,1087,230.8L1087,234L1084.5,234L1084.5,230.8Z" fill="url(#Gradient-0)" fill-opacity="0.6" transform="translate(1085.75,232) translate(-1085.75,-232)"/>
                    </g>
                    <g id="dots-3" transform="translate(9,131) translate(-9,-2)">
                        <g filter="url(#filter1_b_1398_56)" transform="translate(1.25,2) translate(-822.25,-272)">
                            <path d="M821,270.8C821,270.52,821,270.38,821.054,270.273C821.102,270.179,821.179,270.102,821.273,270.054C821.38,270,821.52,270,821.8,270L822.7,270C822.98,270,823.12,270,823.227,270.054C823.321,270.102,823.398,270.179,823.446,270.273C823.5,270.38,823.5,270.52,823.5,270.8L823.5,274L821,274L821,270.8Z" fill="black" transform="translate(822.25,272) translate(-822.25,-272)"/>
                            <path d="M821,270.8C821,270.52,821,270.38,821.054,270.273C821.102,270.179,821.179,270.102,821.273,270.054C821.38,270,821.52,270,821.8,270L822.7,270C822.98,270,823.12,270,823.227,270.054C823.321,270.102,823.398,270.179,823.446,270.273C823.5,270.38,823.5,270.52,823.5,270.8L823.5,274L821,274L821,270.8Z" fill="url(#Gradient-1)" fill-opacity="0.6" transform="translate(822.25,272) translate(-822.25,-272)"/>
                        </g>
                        <g filter="url(#filter2_b_1398_56)" transform="translate(16.75,2) translate(-837.75,-272)">
                            <path d="M836.5,270.8C836.5,270.52,836.5,270.38,836.554,270.273C836.602,270.179,836.679,270.102,836.773,270.054C836.88,270,837.02,270,837.3,270L838.2,270C838.48,270,838.62,270,838.727,270.054C838.821,270.102,838.898,270.179,838.946,270.273C839,270.38,839,270.52,839,270.8L839,274L836.5,274L836.5,270.8Z" fill="black" transform="translate(837.75,272) translate(-837.75,-272)"/>
                            <path d="M836.5,270.8C836.5,270.52,836.5,270.38,836.554,270.273C836.602,270.179,836.679,270.102,836.773,270.054C836.88,270,837.02,270,837.3,270L838.2,270C838.48,270,838.62,270,838.727,270.054C838.821,270.102,838.898,270.179,838.946,270.273C839,270.38,839,270.52,839,270.8L839,274L836.5,274L836.5,270.8Z" fill="url(#Gradient-2)" fill-opacity="0.6" transform="translate(837.75,272) translate(-837.75,-272)"/>
                        </g>
                    </g>
                    <g id="dots-4" transform="translate(9,46) translate(-9,-2)">
                        <g filter="url(#filter3_b_1398_56)" transform="translate(1.25,2) translate(-822.25,-187)">
                            <path d="M821,185L823.5,185L823.5,188.2C823.5,188.48,823.5,188.62,823.446,188.727C823.398,188.821,823.321,188.898,823.227,188.946C823.12,189,822.98,189,822.7,189L821.8,189C821.52,189,821.38,189,821.273,188.946C821.179,188.898,821.102,188.821,821.054,188.727C821,188.62,821,188.48,821,188.2L821,185Z" fill="black" transform="translate(822.25,187) translate(-822.25,-187)"/>
                            <path d="M821,185L823.5,185L823.5,188.2C823.5,188.48,823.5,188.62,823.446,188.727C823.398,188.821,823.321,188.898,823.227,188.946C823.12,189,822.98,189,822.7,189L821.8,189C821.52,189,821.38,189,821.273,188.946C821.179,188.898,821.102,188.821,821.054,188.727C821,188.62,821,188.48,821,188.2L821,185Z" fill="url(#Gradient-3)" fill-opacity="0.6" transform="translate(822.25,187) translate(-822.25,-187)"/>
                        </g>
                        <g filter="url(#filter4_b_1398_56)" transform="translate(16.75,2) translate(-837.75,-187)">
                            <path d="M836.5,185L839,185L839,188.2C839,188.48,839,188.62,838.946,188.727C838.898,188.821,838.821,188.898,838.727,188.946C838.62,189,838.48,189,838.2,189L837.3,189C837.02,189,836.88,189,836.773,188.946C836.679,188.898,836.602,188.821,836.554,188.727C836.5,188.62,836.5,188.48,836.5,188.2L836.5,185Z" fill="black" transform="translate(837.75,187) translate(-837.75,-187)"/>
                            <path d="M836.5,185L839,185L839,188.2C839,188.48,839,188.62,838.946,188.727C838.898,188.821,838.821,188.898,838.727,188.946C838.62,189,838.48,189,838.2,189L837.3,189C837.02,189,836.88,189,836.773,188.946C836.679,188.898,836.602,188.821,836.554,188.727C836.5,188.62,836.5,188.48,836.5,188.2L836.5,185Z" fill="url(#Gradient-4)" fill-opacity="0.6" transform="translate(837.75,187) translate(-837.75,-187)"/>
                        </g>
                    </g>
                    <g id="dots-5" transform="translate(265,239) translate(-26,-2)">
                        <g filter="url(#filter5_b_1398_56)" transform="translate(41.25,2) translate(-1101.25,-380)">
                            <path d="M1100,378L1102.5,378L1102.5,381.2C1102.5,381.48,1102.5,381.62,1102.45,381.727C1102.4,381.821,1102.32,381.898,1102.23,381.946C1102.12,382,1101.98,382,1101.7,382L1100.8,382C1100.52,382,1100.38,382,1100.27,381.946C1100.18,381.898,1100.1,381.821,1100.05,381.727C1100,381.62,1100,381.48,1100,381.2L1100,378Z" fill="black" transform="translate(1101.25,380) translate(-1101.25,-380)"/>
                            <path d="M1100,378L1102.5,378L1102.5,381.2C1102.5,381.48,1102.5,381.62,1102.45,381.727C1102.4,381.821,1102.32,381.898,1102.23,381.946C1102.12,382,1101.98,382,1101.7,382L1100.8,382C1100.52,382,1100.38,382,1100.27,381.946C1100.18,381.898,1100.1,381.821,1100.05,381.727C1100,381.62,1100,381.48,1100,381.2L1100,378Z" fill="url(#Gradient-5)" fill-opacity="0.6" transform="translate(1101.25,380) translate(-1101.25,-380)"/>
                        </g>
                        <g filter="url(#filter6_b_1398_56)" transform="translate(31.25,2) translate(-1091.25,-380)">
                            <path d="M1090,378L1092.5,378L1092.5,381.2C1092.5,381.48,1092.5,381.62,1092.45,381.727C1092.4,381.821,1092.32,381.898,1092.23,381.946C1092.12,382,1091.98,382,1091.7,382L1090.8,382C1090.52,382,1090.38,382,1090.27,381.946C1090.18,381.898,1090.1,381.821,1090.05,381.727C1090,381.62,1090,381.48,1090,381.2L1090,378Z" fill="black" transform="translate(1091.25,380) translate(-1091.25,-380)"/>
                            <path d="M1090,378L1092.5,378L1092.5,381.2C1092.5,381.48,1092.5,381.62,1092.45,381.727C1092.4,381.821,1092.32,381.898,1092.23,381.946C1092.12,382,1091.98,382,1091.7,382L1090.8,382C1090.52,382,1090.38,382,1090.27,381.946C1090.18,381.898,1090.1,381.821,1090.05,381.727C1090,381.62,1090,381.48,1090,381.2L1090,378Z" fill="url(#Gradient-6)" fill-opacity="0.6" transform="translate(1091.25,380) translate(-1091.25,-380)"/>
                        </g>
                        <g filter="url(#filter7_b_1398_56)" transform="translate(21.25,2) translate(-1081.25,-380)">
                            <path d="M1080,378L1082.5,378L1082.5,381.2C1082.5,381.48,1082.5,381.62,1082.45,381.727C1082.4,381.821,1082.32,381.898,1082.23,381.946C1082.12,382,1081.98,382,1081.7,382L1080.8,382C1080.52,382,1080.38,382,1080.27,381.946C1080.18,381.898,1080.1,381.821,1080.05,381.727C1080,381.62,1080,381.48,1080,381.2L1080,378Z" fill="black" transform="translate(1081.25,380) translate(-1081.25,-380)"/>
                            <path d="M1080,378L1082.5,378L1082.5,381.2C1082.5,381.48,1082.5,381.62,1082.45,381.727C1082.4,381.821,1082.32,381.898,1082.23,381.946C1082.12,382,1081.98,382,1081.7,382L1080.8,382C1080.52,382,1080.38,382,1080.27,381.946C1080.18,381.898,1080.1,381.821,1080.05,381.727C1080,381.62,1080,381.48,1080,381.2L1080,378Z" fill="url(#Gradient-7)" fill-opacity="0.6" transform="translate(1081.25,380) translate(-1081.25,-380)"/>
                        </g>
                        <g filter="url(#filter8_b_1398_56)" transform="translate(11.25,2) translate(-1071.25,-380)">
                            <path d="M1070,378L1072.5,378L1072.5,381.2C1072.5,381.48,1072.5,381.62,1072.45,381.727C1072.4,381.821,1072.32,381.898,1072.23,381.946C1072.12,382,1071.98,382,1071.7,382L1070.8,382C1070.52,382,1070.38,382,1070.27,381.946C1070.18,381.898,1070.1,381.821,1070.05,381.727C1070,381.62,1070,381.48,1070,381.2L1070,378Z" fill="black" transform="translate(1071.25,380) translate(-1071.25,-380)"/>
                            <path d="M1070,378L1072.5,378L1072.5,381.2C1072.5,381.48,1072.5,381.62,1072.45,381.727C1072.4,381.821,1072.32,381.898,1072.23,381.946C1072.12,382,1071.98,382,1071.7,382L1070.8,382C1070.52,382,1070.38,382,1070.27,381.946C1070.18,381.898,1070.1,381.821,1070.05,381.727C1070,381.62,1070,381.48,1070,381.2L1070,378Z" fill="url(#Gradient-8)" fill-opacity="0.6" transform="translate(1071.25,380) translate(-1071.25,-380)"/>
                        </g>
                        <g filter="url(#filter9_b_1398_56)" transform="translate(1.25,2) translate(-1061.25,-380)">
                            <path d="M1060,378L1062.5,378L1062.5,381.2C1062.5,381.48,1062.5,381.62,1062.45,381.727C1062.4,381.821,1062.32,381.898,1062.23,381.946C1062.12,382,1061.98,382,1061.7,382L1060.8,382C1060.52,382,1060.38,382,1060.27,381.946C1060.18,381.898,1060.1,381.821,1060.05,381.727C1060,381.62,1060,381.48,1060,381.2L1060,378Z" fill="black" transform="translate(1061.25,380) translate(-1061.25,-380)"/>
                            <path d="M1060,378L1062.5,378L1062.5,381.2C1062.5,381.48,1062.5,381.62,1062.45,381.727C1062.4,381.821,1062.32,381.898,1062.23,381.946C1062.12,382,1061.98,382,1061.7,382L1060.8,382C1060.52,382,1060.38,382,1060.27,381.946C1060.18,381.898,1060.1,381.821,1060.05,381.727C1060,381.62,1060,381.48,1060,381.2L1060,378Z" fill="url(#Gradient-9)" fill-opacity="0.6" transform="translate(1061.25,380) translate(-1061.25,-380)"/>
                        </g>
                        <g filter="url(#filter10_b_1398_56)" transform="translate(50.75,2) translate(-1110.75,-380)">
                            <path d="M1109.5,378L1112,378L1112,381.2C1112,381.48,1112,381.62,1111.95,381.727C1111.9,381.821,1111.82,381.898,1111.73,381.946C1111.62,382,1111.48,382,1111.2,382L1110.3,382C1110.02,382,1109.88,382,1109.77,381.946C1109.68,381.898,1109.6,381.821,1109.55,381.727C1109.5,381.62,1109.5,381.48,1109.5,381.2L1109.5,378Z" fill="black" transform="translate(1110.75,380) translate(-1110.75,-380)"/>
                            <path d="M1109.5,378L1112,378L1112,381.2C1112,381.48,1112,381.62,1111.95,381.727C1111.9,381.821,1111.82,381.898,1111.73,381.946C1111.62,382,1111.48,382,1111.2,382L1110.3,382C1110.02,382,1109.88,382,1109.77,381.946C1109.68,381.898,1109.6,381.821,1109.55,381.727C1109.5,381.62,1109.5,381.48,1109.5,381.2L1109.5,378Z" fill="url(#Gradient-10)" fill-opacity="0.6" transform="translate(1110.75,380) translate(-1110.75,-380)"/>
                        </g>
                    </g>
                    <g id="dots-6" transform="translate(191,164.75) translate(-2,-10.75)">
                        <g filter="url(#filter11_b_1398_56)" transform="translate(2,1.25) translate(-1012,-296.25)">
                            <path d="M1010,295.8C1010,295.52,1010,295.38,1010.05,295.273C1010.1,295.179,1010.18,295.102,1010.27,295.054C1010.38,295,1010.52,295,1010.8,295L1014,295L1014,297.5L1010.8,297.5C1010.52,297.5,1010.38,297.5,1010.27,297.446C1010.18,297.398,1010.1,297.321,1010.05,297.227C1010,297.12,1010,296.98,1010,296.7L1010,295.8Z" fill="black" transform="translate(1012,296.25) translate(-1012,-296.25)"/>
                            <path d="M1010,295.8C1010,295.52,1010,295.38,1010.05,295.273C1010.1,295.179,1010.18,295.102,1010.27,295.054C1010.38,295,1010.52,295,1010.8,295L1014,295L1014,297.5L1010.8,297.5C1010.52,297.5,1010.38,297.5,1010.27,297.446C1010.18,297.398,1010.1,297.321,1010.05,297.227C1010,297.12,1010,296.98,1010,296.7L1010,295.8Z" fill="url(#Gradient-11)" fill-opacity="0.6" transform="translate(1012,296.25) translate(-1012,-296.25)"/>
                        </g>
                        <g filter="url(#filter12_b_1398_56)" transform="translate(2,10.25) translate(-1012,-305.25)">
                            <path d="M1010,304.8C1010,304.52,1010,304.38,1010.05,304.273C1010.1,304.179,1010.18,304.102,1010.27,304.054C1010.38,304,1010.52,304,1010.8,304L1014,304L1014,306.5L1010.8,306.5C1010.52,306.5,1010.38,306.5,1010.27,306.446C1010.18,306.398,1010.1,306.321,1010.05,306.227C1010,306.12,1010,305.98,1010,305.7L1010,304.8Z" fill="black" transform="translate(1012,305.25) translate(-1012,-305.25)"/>
                            <path d="M1010,304.8C1010,304.52,1010,304.38,1010.05,304.273C1010.1,304.179,1010.18,304.102,1010.27,304.054C1010.38,304,1010.52,304,1010.8,304L1014,304L1014,306.5L1010.8,306.5C1010.52,306.5,1010.38,306.5,1010.27,306.446C1010.18,306.398,1010.1,306.321,1010.05,306.227C1010,306.12,1010,305.98,1010,305.7L1010,304.8Z" fill="url(#Gradient-12)" fill-opacity="0.6" transform="translate(1012,305.25) translate(-1012,-305.25)"/>
                        </g>
                        <g filter="url(#filter13_b_1398_56)" transform="translate(2,20.25) translate(-1012,-315.25)">
                            <path d="M1010,314.8C1010,314.52,1010,314.38,1010.05,314.273C1010.1,314.179,1010.18,314.102,1010.27,314.054C1010.38,314,1010.52,314,1010.8,314L1014,314L1014,316.5L1010.8,316.5C1010.52,316.5,1010.38,316.5,1010.27,316.446C1010.18,316.398,1010.1,316.321,1010.05,316.227C1010,316.12,1010,315.98,1010,315.7L1010,314.8Z" fill="black" transform="translate(1012,315.25) translate(-1012,-315.25)"/>
                            <path d="M1010,314.8C1010,314.52,1010,314.38,1010.05,314.273C1010.1,314.179,1010.18,314.102,1010.27,314.054C1010.38,314,1010.52,314,1010.8,314L1014,314L1014,316.5L1010.8,316.5C1010.52,316.5,1010.38,316.5,1010.27,316.446C1010.18,316.398,1010.1,316.321,1010.05,316.227C1010,316.12,1010,315.98,1010,315.7L1010,314.8Z" fill="url(#Gradient-13)" fill-opacity="0.6" transform="translate(1012,315.25) translate(-1012,-315.25)"/>
                        </g>
                    </g>
                    <g id="dots-7" transform="translate(39,164.25) translate(-2,-10.25)">
                        <g filter="url(#filter14_b_1398_56)" transform="translate(2,1.25) translate(-860,-296.25)">
                            <path d="M858,295L861.2,295C861.48,295,861.62,295,861.727,295.054C861.821,295.102,861.898,295.179,861.946,295.273C862,295.38,862,295.52,862,295.8L862,296.7C862,296.98,862,297.12,861.946,297.227C861.898,297.321,861.821,297.398,861.727,297.446C861.62,297.5,861.48,297.5,861.2,297.5L858,297.5L858,295Z" fill="black" transform="translate(860,296.25) translate(-860,-296.25)"/>
                            <path d="M858,295L861.2,295C861.48,295,861.62,295,861.727,295.054C861.821,295.102,861.898,295.179,861.946,295.273C862,295.38,862,295.52,862,295.8L862,296.7C862,296.98,862,297.12,861.946,297.227C861.898,297.321,861.821,297.398,861.727,297.446C861.62,297.5,861.48,297.5,861.2,297.5L858,297.5L858,295Z" fill="url(#Gradient-14)" fill-opacity="0.6" transform="translate(860,296.25) translate(-860,-296.25)"/>
                        </g>
                        <g filter="url(#filter15_b_1398_56)" transform="translate(2,10.25) translate(-860,-305.25)">
                            <path d="M858,304L861.2,304C861.48,304,861.62,304,861.727,304.054C861.821,304.102,861.898,304.179,861.946,304.273C862,304.38,862,304.52,862,304.8L862,305.7C862,305.98,862,306.12,861.946,306.227C861.898,306.321,861.821,306.398,861.727,306.446C861.62,306.5,861.48,306.5,861.2,306.5L858,306.5L858,304Z" fill="black" transform="translate(860,305.25) translate(-860,-305.25)"/>
                            <path d="M858,304L861.2,304C861.48,304,861.62,304,861.727,304.054C861.821,304.102,861.898,304.179,861.946,304.273C862,304.38,862,304.52,862,304.8L862,305.7C862,305.98,862,306.12,861.946,306.227C861.898,306.321,861.821,306.398,861.727,306.446C861.62,306.5,861.48,306.5,861.2,306.5L858,306.5L858,304Z" fill="url(#Gradient-15)" fill-opacity="0.6" transform="translate(860,305.25) translate(-860,-305.25)"/>
                        </g>
                        <g filter="url(#filter16_b_1398_56)" transform="translate(2,19.25) translate(-860,-314.25)">
                            <path d="M858,313L861.2,313C861.48,313,861.62,313,861.727,313.054C861.821,313.102,861.898,313.179,861.946,313.273C862,313.38,862,313.52,862,313.8L862,314.7C862,314.98,862,315.12,861.946,315.227C861.898,315.321,861.821,315.398,861.727,315.446C861.62,315.5,861.48,315.5,861.2,315.5L858,315.5L858,313Z" fill="black" transform="translate(860,314.25) translate(-860,-314.25)"/>
                            <path d="M858,313L861.2,313C861.48,313,861.62,313,861.727,313.054C861.821,313.102,861.898,313.179,861.946,313.273C862,313.38,862,313.52,862,313.8L862,314.7C862,314.98,862,315.12,861.946,315.227C861.898,315.321,861.821,315.398,861.727,315.446C861.62,315.5,861.48,315.5,861.2,315.5L858,315.5L858,313Z" fill="url(#Gradient-16)" fill-opacity="0.6" transform="translate(860,314.25) translate(-860,-314.25)"/>
                        </g>
                    </g>
                    <g id="dots-8" transform="translate(9,2) translate(-9,-2)">
                        <g filter="url(#filter19_b_1398_56)" transform="translate(1.25,2) translate(-822.25,-143)">
                            <path d="M821,141.8C821,141.52,821,141.38,821.054,141.273C821.102,141.179,821.179,141.102,821.273,141.054C821.38,141,821.52,141,821.8,141L822.7,141C822.98,141,823.12,141,823.227,141.054C823.321,141.102,823.398,141.179,823.446,141.273C823.5,141.38,823.5,141.52,823.5,141.8L823.5,145L821,145L821,141.8Z" fill="black" transform="translate(822.25,143) translate(-822.25,-143)"/>
                            <path d="M821,141.8C821,141.52,821,141.38,821.054,141.273C821.102,141.179,821.179,141.102,821.273,141.054C821.38,141,821.52,141,821.8,141L822.7,141C822.98,141,823.12,141,823.227,141.054C823.321,141.102,823.398,141.179,823.446,141.273C823.5,141.38,823.5,141.52,823.5,141.8L823.5,145L821,145L821,141.8Z" fill="url(#Gradient-17)" fill-opacity="0.6" transform="translate(822.25,143) translate(-822.25,-143)"/>
                        </g>
                        <g filter="url(#filter20_b_1398_56)" transform="translate(16.75,2) translate(-837.75,-143)">
                            <path d="M836.5,141.8C836.5,141.52,836.5,141.38,836.554,141.273C836.602,141.179,836.679,141.102,836.773,141.054C836.88,141,837.02,141,837.3,141L838.2,141C838.48,141,838.62,141,838.727,141.054C838.821,141.102,838.898,141.179,838.946,141.273C839,141.38,839,141.52,839,141.8L839,145L836.5,145L836.5,141.8Z" fill="black" transform="translate(837.75,143) translate(-837.75,-143)"/>
                            <path d="M836.5,141.8C836.5,141.52,836.5,141.38,836.554,141.273C836.602,141.179,836.679,141.102,836.773,141.054C836.88,141,837.02,141,837.3,141L838.2,141C838.48,141,838.62,141,838.727,141.054C838.821,141.102,838.898,141.179,838.946,141.273C839,141.38,839,141.52,839,141.8L839,145L836.5,145L836.5,141.8Z" fill="url(#Gradient-18)" fill-opacity="0.6" transform="translate(837.75,143) translate(-837.75,-143)"/>
                        </g>
                    </g>
                    <g id="dots-9" transform="translate(339,165.25) translate(-2,-28.25)">
                        <g filter="url(#filter21_b_1398_56)" transform="translate(2,38.25) translate(-1160,-316.25)">
                            <path d="M1158,315L1161.2,315C1161.48,315,1161.62,315,1161.73,315.054C1161.82,315.102,1161.9,315.179,1161.95,315.273C1162,315.38,1162,315.52,1162,315.8L1162,316.7C1162,316.98,1162,317.12,1161.95,317.227C1161.9,317.321,1161.82,317.398,1161.73,317.446C1161.62,317.5,1161.48,317.5,1161.2,317.5L1158,317.5L1158,315Z" fill="black" transform="translate(1160,316.25) translate(-1160,-316.25)"/>
                            <path d="M1158,315L1161.2,315C1161.48,315,1161.62,315,1161.73,315.054C1161.82,315.102,1161.9,315.179,1161.95,315.273C1162,315.38,1162,315.52,1162,315.8L1162,316.7C1162,316.98,1162,317.12,1161.95,317.227C1161.9,317.321,1161.82,317.398,1161.73,317.446C1161.62,317.5,1161.48,317.5,1161.2,317.5L1158,317.5L1158,315Z" fill="url(#Gradient-19)" fill-opacity="0.6" transform="translate(1160,316.25) translate(-1160,-316.25)"/>
                        </g>
                        <g filter="url(#filter22_b_1398_56)" transform="translate(2,28.25) translate(-1160,-306.25)">
                            <path d="M1158,305L1161.2,305C1161.48,305,1161.62,305,1161.73,305.054C1161.82,305.102,1161.9,305.179,1161.95,305.273C1162,305.38,1162,305.52,1162,305.8L1162,306.7C1162,306.98,1162,307.12,1161.95,307.227C1161.9,307.321,1161.82,307.398,1161.73,307.446C1161.62,307.5,1161.48,307.5,1161.2,307.5L1158,307.5L1158,305Z" fill="black" transform="translate(1160,306.25) translate(-1160,-306.25)"/>
                            <path d="M1158,305L1161.2,305C1161.48,305,1161.62,305,1161.73,305.054C1161.82,305.102,1161.9,305.179,1161.95,305.273C1162,305.38,1162,305.52,1162,305.8L1162,306.7C1162,306.98,1162,307.12,1161.95,307.227C1161.9,307.321,1161.82,307.398,1161.73,307.446C1161.62,307.5,1161.48,307.5,1161.2,307.5L1158,307.5L1158,305Z" fill="url(#Gradient-20)" fill-opacity="0.6" transform="translate(1160,306.25) translate(-1160,-306.25)"/>
                        </g>
                        <g filter="url(#filter23_b_1398_56)" transform="translate(2,19.25) translate(-1160,-297.25)">
                            <path d="M1158,296L1161.2,296C1161.48,296,1161.62,296,1161.73,296.054C1161.82,296.102,1161.9,296.179,1161.95,296.273C1162,296.38,1162,296.52,1162,296.8L1162,297.7C1162,297.98,1162,298.12,1161.95,298.227C1161.9,298.321,1161.82,298.398,1161.73,298.446C1161.62,298.5,1161.48,298.5,1161.2,298.5L1158,298.5L1158,296Z" fill="black" transform="translate(1160,297.25) translate(-1160,-297.25)"/>
                            <path d="M1158,296L1161.2,296C1161.48,296,1161.62,296,1161.73,296.054C1161.82,296.102,1161.9,296.179,1161.95,296.273C1162,296.38,1162,296.52,1162,296.8L1162,297.7C1162,297.98,1162,298.12,1161.95,298.227C1161.9,298.321,1161.82,298.398,1161.73,298.446C1161.62,298.5,1161.48,298.5,1161.2,298.5L1158,298.5L1158,296Z" fill="url(#Gradient-21)" fill-opacity="0.6" transform="translate(1160,297.25) translate(-1160,-297.25)"/>
                        </g>
                        <g filter="url(#filter24_b_1398_56)" transform="translate(2,10.25) translate(-1160,-288.25)">
                            <path d="M1158,287L1161.2,287C1161.48,287,1161.62,287,1161.73,287.054C1161.82,287.102,1161.9,287.179,1161.95,287.273C1162,287.38,1162,287.52,1162,287.8L1162,288.7C1162,288.98,1162,289.12,1161.95,289.227C1161.9,289.321,1161.82,289.398,1161.73,289.446C1161.62,289.5,1161.48,289.5,1161.2,289.5L1158,289.5L1158,287Z" fill="black" transform="translate(1160,288.25) translate(-1160,-288.25)"/>
                            <path d="M1158,287L1161.2,287C1161.48,287,1161.62,287,1161.73,287.054C1161.82,287.102,1161.9,287.179,1161.95,287.273C1162,287.38,1162,287.52,1162,287.8L1162,288.7C1162,288.98,1162,289.12,1161.95,289.227C1161.9,289.321,1161.82,289.398,1161.73,289.446C1161.62,289.5,1161.48,289.5,1161.2,289.5L1158,289.5L1158,287Z" fill="url(#Gradient-22)" fill-opacity="0.6" transform="translate(1160,288.25) translate(-1160,-288.25)"/>
                        </g>
                        <g filter="url(#filter25_b_1398_56)" transform="translate(2,1.25) translate(-1160,-279.25)">
                            <path d="M1158,278L1161.2,278C1161.48,278,1161.62,278,1161.73,278.054C1161.82,278.102,1161.9,278.179,1161.95,278.273C1162,278.38,1162,278.52,1162,278.8L1162,279.7C1162,279.98,1162,280.12,1161.95,280.227C1161.9,280.321,1161.82,280.398,1161.73,280.446C1161.62,280.5,1161.48,280.5,1161.2,280.5L1158,280.5L1158,278Z" fill="black" transform="translate(1160,279.25) translate(-1160,-279.25)"/>
                            <path d="M1158,278L1161.2,278C1161.48,278,1161.62,278,1161.73,278.054C1161.82,278.102,1161.9,278.179,1161.95,278.273C1162,278.38,1162,278.52,1162,278.8L1162,279.7C1162,279.98,1162,280.12,1161.95,280.227C1161.9,280.321,1161.82,280.398,1161.73,280.446C1161.62,280.5,1161.48,280.5,1161.2,280.5L1158,280.5L1158,278Z" fill="url(#Gradient-23)" fill-opacity="0.6" transform="translate(1160,279.25) translate(-1160,-279.25)"/>
                        </g>
                        <g filter="url(#filter26_b_1398_56)" transform="translate(2,46.25) translate(-1160,-324.25)">
                            <path d="M1158,323L1161.2,323C1161.48,323,1161.62,323,1161.73,323.054C1161.82,323.102,1161.9,323.179,1161.95,323.273C1162,323.38,1162,323.52,1162,323.8L1162,324.7C1162,324.98,1162,325.12,1161.95,325.227C1161.9,325.321,1161.82,325.398,1161.73,325.446C1161.62,325.5,1161.48,325.5,1161.2,325.5L1158,325.5L1158,323Z" fill="black" transform="translate(1160,324.25) translate(-1160,-324.25)"/>
                            <path d="M1158,323L1161.2,323C1161.48,323,1161.62,323,1161.73,323.054C1161.82,323.102,1161.9,323.179,1161.95,323.273C1162,323.38,1162,323.52,1162,323.8L1162,324.7C1162,324.98,1162,325.12,1161.95,325.227C1161.9,325.321,1161.82,325.398,1161.73,325.446C1161.62,325.5,1161.48,325.5,1161.2,325.5L1158,325.5L1158,323Z" fill="url(#Gradient-24)" fill-opacity="0.6" transform="translate(1160,324.25) translate(-1160,-324.25)"/>
                        </g>
                        <g filter="url(#filter27_b_1398_56)" transform="translate(2,55.25) translate(-1160,-333.25)">
                            <path d="M1158,332L1161.2,332C1161.48,332,1161.62,332,1161.73,332.054C1161.82,332.102,1161.9,332.179,1161.95,332.273C1162,332.38,1162,332.52,1162,332.8L1162,333.7C1162,333.98,1162,334.12,1161.95,334.227C1161.9,334.321,1161.82,334.398,1161.73,334.446C1161.62,334.5,1161.48,334.5,1161.2,334.5L1158,334.5L1158,332Z" fill="black" transform="translate(1160,333.25) translate(-1160,-333.25)"/>
                            <path d="M1158,332L1161.2,332C1161.48,332,1161.62,332,1161.73,332.054C1161.82,332.102,1161.9,332.179,1161.95,332.273C1162,332.38,1162,332.52,1162,332.8L1162,333.7C1162,333.98,1162,334.12,1161.95,334.227C1161.9,334.321,1161.82,334.398,1161.73,334.446C1161.62,334.5,1161.48,334.5,1161.2,334.5L1158,334.5L1158,332Z" fill="url(#Gradient-25)" fill-opacity="0.6" transform="translate(1160,333.25) translate(-1160,-333.25)"/>
                        </g>
                    </g>
                </g>
                <g id="chip" filter="url(#filter28_d_1398_56)" transform="translate(1086,306) translate(-1086,-306)">
                    <g clip-path="url(#clip3_1398_56)" transform="translate(1086,306) translate(-1086,-306)">
                        <rect width="144" height="144" rx="3" fill="white" fill-opacity="0.05" shape-rendering="crispEdges" transform="translate(1086,306) translate(-72,-72)"/>
                        <rect width="144" height="144" rx="3" fill="url(#Gradient-26)" fill-opacity="0.6" shape-rendering="crispEdges" transform="translate(1086,306) translate(-72,-72)"/>
                        <path d="M1048.84,308.28C1048.84,312.6,1051.5,315.24,1056.74,315.24C1061.99,315.24,1064.63,312.6,1064.63,308.28L1064.63,298.92L1061.03,298.92L1061.03,308.016C1061.03,310.752,1060.02,311.736,1056.74,311.736C1053.47,311.736,1052.44,310.752,1052.44,308.016L1052.44,298.92L1048.84,298.92L1048.84,308.28Z" fill="url(#Gradient-27)" transform="translate(1056.73,307.08) translate(-1056.73,-307.08)"/>
                        <path d="M1081.56,315L1085.16,315L1085.16,310.656L1087.92,310.656L1090.92,315L1095.17,315L1090.85,308.784L1095.24,302.976L1090.97,302.976L1087.9,307.44L1085.16,307.44L1085.16,298.92L1081.56,298.92L1081.56,315Z" fill="url(#Gradient-27)" transform="translate(1088.4,306.96) translate(-1088.4,-306.96)"/>
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M1102.5,315.24C1098.49,315.24,1095.76,313.272,1095.76,309C1095.76,305.16,1098.47,302.736,1102.43,302.736C1106.36,302.736,1108.98,304.8,1108.98,308.568C1108.98,308.932,1108.95,309.21,1108.91,309.533C1108.9,309.593,1108.89,309.656,1108.88,309.72L1099.09,309.72C1099.19,311.568,1100.05,312.36,1102.38,312.36C1104.52,312.36,1105.26,311.808,1105.26,310.776L1105.26,310.536L1108.86,310.536L1108.86,310.8C1108.86,313.416,1106.32,315.24,1102.5,315.24ZM1102.36,305.544C1100.22,305.544,1099.31,306.264,1099.14,307.824L1105.52,307.824C1105.43,306.24,1104.47,305.544,1102.36,305.544Z" fill="url(#Gradient-27)" transform="translate(1102.37,308.988) translate(-1102.37,-308.988)"/>
                        <path d="M1112.81,319.08L1110.84,319.08L1110.84,315.816L1113.79,315.816C1114.68,315.816,1115.07,315.576,1115.31,315.048L1109.43,302.976L1113.51,302.976L1116.05,308.472L1117.06,311.304L1117.3,311.304L1118.26,308.448L1120.54,302.976L1124.55,302.976L1118.59,315.864C1117.44,318.384,1115.83,319.08,1112.81,319.08Z" fill="url(#Gradient-27)" transform="translate(1116.99,311.028) translate(-1116.99,-311.028)"/>
                        <path d="M1070.04,315L1066.44,315L1066.44,302.976L1069.78,302.976L1069.78,306.672L1070,306.672C1070.31,304.656,1071.75,302.736,1074.89,302.736C1078.18,302.736,1079.74,304.848,1079.74,307.512L1079.74,315L1076.14,315L1076.14,308.712C1076.14,306.792,1075.35,305.976,1073.14,305.976C1070.86,305.976,1070.04,306.888,1070.04,308.928L1070.04,315Z" fill="url(#Gradient-27)" transform="translate(1073.09,308.868) translate(-1073.09,-308.868)"/>
                        <path d="M1048.84,308.28C1048.84,312.6,1051.5,315.24,1056.74,315.24C1059.41,315.24,1061.4,314.559,1062.72,313.323C1061.41,314.717,1059.33,315.49,1056.49,315.49C1051.25,315.49,1048.59,312.85,1048.59,308.53L1048.59,299.17L1048.84,299.17L1048.84,308.28Z" fill="white" fill-opacity="0.15" transform="translate(1055.65,307.33) translate(-1055.65,-307.33)"/>
                        <path d="M1061.03,299.17L1060.78,299.17L1060.78,308.266C1060.78,309.474,1060.59,310.341,1060.08,310.932C1060.77,310.345,1061.03,309.407,1061.03,308.016L1061.03,299.17Z" fill="white" fill-opacity="0.15" transform="translate(1060.55,305.051) translate(-1060.55,-305.051)"/>
                        <path d="M1084.91,315L1081.56,315L1081.56,299.17L1081.31,299.17L1081.31,315.25L1084.91,315.25L1084.91,315Z" fill="white" fill-opacity="0.15" transform="translate(1083.11,307.21) translate(-1083.11,-307.21)"/>
                        <path d="M1085.16,310.906L1087.67,310.906L1090.67,315.25L1094.92,315.25L1094.75,315L1090.92,315L1087.92,310.656L1085.16,310.656L1085.16,310.906Z" fill="white" fill-opacity="0.15" transform="translate(1090.04,312.953) translate(-1090.04,-312.953)"/>
                        <path d="M1090.8,303.226L1090.72,303.226L1087.82,307.44L1087.9,307.44L1090.8,303.226Z" fill="white" fill-opacity="0.15" transform="translate(1089.31,305.333) translate(-1089.31,-305.333)"/>
                        <path d="M1102.5,315.24C1098.49,315.24,1095.76,313.272,1095.76,309C1095.76,307.236,1096.33,305.771,1097.33,304.703C1096.17,305.788,1095.51,307.345,1095.51,309.25C1095.51,313.522,1098.24,315.49,1102.25,315.49C1104.64,315.49,1106.52,314.777,1107.6,313.604C1106.5,314.628,1104.71,315.24,1102.5,315.24Z" fill="white" fill-opacity="0.15" transform="translate(1101.55,310.096) translate(-1101.55,-310.096)"/>
                        <path d="M1105.26,310.786L1105.01,310.786L1105.01,311.026C1105.01,311.352,1104.94,311.63,1104.77,311.857C1105.11,311.597,1105.26,311.238,1105.26,310.786Z" fill="white" fill-opacity="0.15" transform="translate(1105.01,311.322) translate(-1105.01,-311.322)"/>
                        <path d="M1099.11,309.97L1108.63,309.97C1108.64,309.906,1108.65,309.844,1108.66,309.783C1108.66,309.762,1108.66,309.741,1108.66,309.72L1099.09,309.72C1099.1,309.806,1099.1,309.889,1099.11,309.97Z" fill="white" fill-opacity="0.15" transform="translate(1103.88,309.845) translate(-1103.88,-309.845)"/>
                        <path d="M1099.66,306.426C1100.18,305.999,1100.97,305.794,1102.11,305.794C1104.1,305.794,1105.07,306.416,1105.25,307.824L1105.52,307.824C1105.43,306.24,1104.47,305.544,1102.36,305.544C1101.01,305.544,1100.16,305.828,1099.66,306.426Z" fill="white" fill-opacity="0.15" transform="translate(1102.59,306.684) translate(-1102.59,-306.684)"/>
                        <path d="M1112.81,319.08L1110.84,319.08L1110.84,316.066L1110.59,316.066L1110.59,319.33L1112.56,319.33C1114.75,319.33,1116.2,318.965,1117.27,317.807C1116.22,318.768,1114.83,319.08,1112.81,319.08Z" fill="white" fill-opacity="0.15" transform="translate(1113.93,317.698) translate(-1113.93,-317.698)"/>
                        <path d="M1120.43,303.226L1120.29,303.226L1118.01,308.698L1117.13,311.304L1117.3,311.304L1118.26,308.448L1120.43,303.226Z" fill="white" fill-opacity="0.15" transform="translate(1118.78,307.265) translate(-1118.78,-307.265)"/>
                        <path d="M1115.31,315.048L1109.55,303.226L1109.18,303.226L1115.06,315.298C1115.01,315.395,1114.96,315.482,1114.91,315.56C1115.08,315.437,1115.21,315.267,1115.31,315.048Z" fill="white" fill-opacity="0.15" transform="translate(1112.24,309.393) translate(-1112.24,-309.393)"/>
                        <path d="M1069.79,315L1066.44,315L1066.44,303.226L1066.19,303.226L1066.19,315.25L1069.79,315.25L1069.79,315Z" fill="white" fill-opacity="0.15" transform="translate(1067.99,309.238) translate(-1067.99,-309.238)"/>
                        <path d="M1069.79,306.672L1070,306.672C1070.14,305.77,1070.5,304.888,1071.14,304.188C1070.41,304.86,1069.98,305.748,1069.79,306.672Z" fill="white" fill-opacity="0.15" transform="translate(1070.46,305.43) translate(-1070.46,-305.43)"/>
                        <path d="M1073.14,305.976C1071.9,305.976,1071.09,306.248,1070.61,306.826C1071.1,306.419,1071.84,306.226,1072.89,306.226C1075.1,306.226,1075.89,307.042,1075.89,308.962L1075.89,315.25L1079.49,315.25L1079.49,315L1076.14,315L1076.14,308.712C1076.14,306.792,1075.35,305.976,1073.14,305.976Z" fill="white" fill-opacity="0.15" transform="translate(1075.05,310.613) translate(-1075.05,-310.613)"/>
                        <path d="M1048.84,299.17L1048.84,298.92L1052.44,298.92L1052.44,308.016C1052.44,310.752,1053.47,311.736,1056.74,311.736C1058.35,311.736,1059.42,311.498,1060.08,310.932C1059.45,311.679,1058.32,311.986,1056.49,311.986C1053.22,311.986,1052.19,311.002,1052.19,308.266L1052.19,299.17L1048.84,299.17Z" fill="black" fill-opacity="0.64" transform="translate(1054.46,305.453) translate(-1054.46,-305.453)"/>
                        <path d="M1061.03,299.17L1061.03,298.92L1064.63,298.92L1064.63,308.28C1064.63,310.406,1063.99,312.125,1062.72,313.323C1063.83,312.141,1064.38,310.512,1064.38,308.53L1064.38,299.17L1061.03,299.17Z" fill="black" fill-opacity="0.64" transform="translate(1062.83,306.121) translate(-1062.83,-306.121)"/>
                        <path d="M1084.91,315L1085.16,315L1085.16,310.906L1084.91,310.906L1084.91,315Z" fill="black" fill-opacity="0.64" transform="translate(1085.03,312.953) translate(-1085.03,-312.953)"/>
                        <path d="M1081.56,299.17L1081.56,298.92L1085.16,298.92L1085.16,307.44L1087.82,307.44L1087.65,307.69L1084.91,307.69L1084.91,299.17L1081.56,299.17Z" fill="black" fill-opacity="0.64" transform="translate(1084.69,303.305) translate(-1084.69,-303.305)"/>
                        <path d="M1090.8,303.226L1094.99,303.226L1090.6,309.034L1094.75,315L1095.17,315L1090.85,308.784L1095.24,302.976L1090.97,302.976L1090.8,303.226Z" fill="black" fill-opacity="0.64" transform="translate(1092.92,308.988) translate(-1092.92,-308.988)"/>
                        <path d="M1097.33,304.703C1098.5,303.601,1100.18,302.986,1102.18,302.986C1106.11,302.986,1108.73,305.05,1108.73,308.818C1108.73,309.158,1108.7,309.423,1108.66,309.72L1108.88,309.72C1108.89,309.656,1108.9,309.594,1108.91,309.533C1108.95,309.211,1108.98,308.932,1108.98,308.568C1108.98,304.8,1106.36,302.736,1102.43,302.736C1100.29,302.736,1098.51,303.445,1097.33,304.703Z" fill="black" fill-opacity="0.64" transform="translate(1103.15,306.228) translate(-1103.15,-306.228)"/>
                        <path d="M1107.6,313.604C1108.41,312.861,1108.86,311.9,1108.86,310.8L1108.86,310.536L1105.26,310.536L1105.26,310.776C1105.26,310.779,1105.26,310.783,1105.26,310.786L1108.61,310.786L1108.61,311.05C1108.61,312.031,1108.25,312.9,1107.6,313.604Z" fill="black" fill-opacity="0.64" transform="translate(1107.06,312.07) translate(-1107.06,-312.07)"/>
                        <path d="M1104.77,311.857C1104.33,312.188,1103.57,312.36,1102.38,312.36C1100.16,312.36,1099.27,311.64,1099.11,309.97L1098.84,309.97C1098.94,311.818,1099.8,312.61,1102.13,312.61C1103.59,312.61,1104.4,312.351,1104.77,311.857Z" fill="black" fill-opacity="0.64" transform="translate(1101.8,311.29) translate(-1101.8,-311.29)"/>
                        <path d="M1099.66,306.426C1099.37,306.78,1099.2,307.244,1099.14,307.824L1105.25,307.824C1105.26,307.905,1105.27,307.988,1105.27,308.074L1098.89,308.074C1098.97,307.346,1099.21,306.8,1099.66,306.426Z" fill="black" fill-opacity="0.64" transform="translate(1102.08,307.25) translate(-1102.08,-307.25)"/>
                        <path d="M1110.84,316.066L1113.54,316.066C1114.27,316.066,1114.66,315.906,1114.91,315.56C1114.66,315.734,1114.31,315.816,1113.79,315.816L1110.84,315.816L1110.84,316.066Z" fill="black" fill-opacity="0.64" transform="translate(1112.88,315.813) translate(-1112.88,-315.813)"/>
                        <path d="M1117.27,317.807C1117.78,317.332,1118.21,316.698,1118.59,315.864L1124.55,302.976L1120.54,302.976L1120.43,303.226L1124.3,303.226L1118.34,316.114C1118.03,316.809,1117.67,317.366,1117.27,317.807Z" fill="black" fill-opacity="0.64" transform="translate(1120.91,310.391) translate(-1120.91,-310.391)"/>
                        <path d="M1117.13,311.304L1117.05,311.554L1116.81,311.554L1115.8,308.722L1113.26,303.226L1109.55,303.226L1109.43,302.976L1113.51,302.976L1116.05,308.472L1117.06,311.304L1117.13,311.304Z" fill="black" fill-opacity="0.64" transform="translate(1113.28,307.265) translate(-1113.28,-307.265)"/>
                        <path d="M1069.79,315L1070.04,315L1070.04,308.928C1070.04,308.001,1070.21,307.307,1070.61,306.826C1070.03,307.304,1069.79,308.076,1069.79,309.178L1069.79,315Z" fill="black" fill-opacity="0.64" transform="translate(1070.2,310.913) translate(-1070.2,-310.913)"/>
                        <path d="M1066.44,303.226L1069.53,303.226L1069.53,306.922L1069.75,306.922C1069.76,306.838,1069.77,306.755,1069.79,306.672L1069.78,306.672L1069.78,302.976L1066.44,302.976L1066.44,303.226Z" fill="black" fill-opacity="0.64" transform="translate(1068.12,304.949) translate(-1068.12,-304.949)"/>
                        <path d="M1071.14,304.188C1071.94,303.322,1073.15,302.736,1074.89,302.736C1078.18,302.736,1079.74,304.848,1079.74,307.512L1079.74,315L1079.49,315L1079.49,307.762C1079.49,305.098,1077.93,302.986,1074.64,302.986C1073.08,302.986,1071.94,303.461,1071.14,304.188Z" fill="black" fill-opacity="0.64" transform="translate(1075.44,308.868) translate(-1075.44,-308.868)"/>
                    </g>
                    <rect width="143.25" height="143.25" rx="2.625" stroke="url(#Gradient-28)" stroke-opacity="0.13" stroke-width="0.75" shape-rendering="crispEdges" transform="translate(1086.01,306) translate(-71.625,-71.625)"/>
                </g>
                <g id="chip-2" filter="url(#filter30_d_1398_56)" transform="translate(830,306) translate(-830,-306)">
                    <g clip-path="url(#clip4_1398_56)" transform="translate(830,306) translate(-830,-306)">
                        <rect width="56" height="64" rx="3" fill="url(#Gradient-29)" fill-opacity="0.6" shape-rendering="crispEdges" transform="translate(830,306) translate(-28,-32)"/>
                        <rect width="56" height="64" rx="3" fill="white" fill-opacity="0.05" shape-rendering="crispEdges" transform="translate(830,306) translate(-28,-32)"/>
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M830,297L829.965,297L829.965,297C829.534,297,829.279,297,829.059,297.022C826.922,297.232,825.232,298.922,825.022,301.059C825,301.279,825,301.534,825,301.965L825,302L825,305L824.1,305L824.081,305C823.817,305,823.59,305,823.402,305.015C823.205,305.031,823.008,305.067,822.819,305.163C822.537,305.307,822.307,305.537,822.163,305.819C822.067,306.008,822.031,306.205,822.015,306.402C822,306.59,822,306.817,822,307.081L822,307.081L822,307.1L822,313.9L822,313.919L822,313.919C822,314.183,822,314.41,822.015,314.598C822.031,314.795,822.067,314.992,822.163,315.181C822.307,315.463,822.537,315.693,822.819,315.837C823.008,315.933,823.205,315.969,823.402,315.985C823.59,316,823.817,316,824.081,316L824.1,316L835.9,316L835.919,316C836.183,316,836.41,316,836.598,315.985C836.795,315.969,836.992,315.933,837.181,315.837C837.463,315.693,837.693,315.463,837.837,315.181C837.933,314.992,837.969,314.795,837.985,314.598C838,314.41,838,314.183,838,313.919L838,313.9L838,307.1L838,307.081C838,306.817,838,306.59,837.985,306.402C837.969,306.205,837.933,306.008,837.837,305.819C837.693,305.537,837.463,305.307,837.181,305.163C836.992,305.067,836.795,305.031,836.598,305.015C836.41,305,836.183,305,835.919,305L835.9,305L835,305L835,302L835,301.965C835,301.534,835,301.279,834.978,301.059C834.768,298.922,833.078,297.232,830.941,297.022C830.721,297,830.466,297,830.035,297L830.035,297L830,297ZM829.157,298.017C829.322,298.001,829.524,298,830,298C830.476,298,830.678,298.001,830.843,298.017C832.505,298.181,833.819,299.495,833.983,301.157C833.999,301.322,834,301.524,834,302L834,305L826,305L826,302C826,301.524,826.001,301.322,826.017,301.157C826.181,299.495,827.495,298.181,829.157,298.017ZM823.273,306.055C823.298,306.042,823.348,306.023,823.484,306.012C823.626,306,823.812,306,824.1,306L835.9,306C836.188,306,836.374,306,836.516,306.012C836.652,306.023,836.702,306.042,836.727,306.055C836.821,306.102,836.898,306.179,836.946,306.273C836.958,306.298,836.977,306.348,836.988,306.484C837,306.626,837,306.812,837,307.1L837,313.9C837,314.188,837,314.374,836.988,314.516C836.977,314.652,836.958,314.702,836.946,314.727C836.898,314.821,836.821,314.898,836.727,314.946C836.702,314.958,836.652,314.977,836.516,314.988C836.374,315,836.188,315,835.9,315L824.1,315C823.812,315,823.626,315,823.484,314.988C823.348,314.977,823.298,314.958,823.273,314.946C823.179,314.898,823.102,314.821,823.054,314.727C823.042,314.702,823.023,314.652,823.012,314.516C823,314.374,823,314.188,823,313.9L823,307.1C823,306.812,823,306.626,823.012,306.484C823.023,306.348,823.042,306.298,823.054,306.273C823.102,306.179,823.179,306.102,823.273,306.055Z" fill="url(#Gradient-30)" fill-opacity="0.2" transform="translate(830,306.5) translate(-830,-306.5)"/>
                    </g>
                    <rect width="55.25" height="63.25" rx="2.625" stroke="url(#Gradient-31)" stroke-opacity="0.13" stroke-width="0.75" shape-rendering="crispEdges" transform="translate(830,306) translate(-27.625,-31.625)"/>
                </g>
                <g id="chip-3" transform="translate(830,165) translate(-28,-20)">
                    <g filter="url(#filter17_d_1398_56)" transform="translate(28,20) translate(-830,-165)">
                        <rect width="56" height="40" rx="3" fill="url(#Gradient-32)" fill-opacity="0.6" shape-rendering="crispEdges" transform="translate(830,165) translate(-28,-20)"/>
                    </g>
                    <g clip-path="url(#clip2_1398_56)" transform="translate(28,20) translate(-830,-165)">
                        <rect width="56" height="40" rx="3" fill="white" fill-opacity="0.05" transform="translate(830,165) translate(-28,-20)"/>
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M822.081,157L822.1,157L825,157L825,158L822.1,158C821.812,158,821.626,158,821.484,158.012C821.348,158.023,821.298,158.042,821.273,158.054C821.179,158.102,821.102,158.179,821.054,158.273C821.042,158.298,821.023,158.348,821.012,158.484C821,158.626,821,158.812,821,159.1L821,162L820,162L820,159.1L820,159.081L820,159.081C820,158.817,820,158.59,820.015,158.402C820.031,158.205,820.067,158.008,820.163,157.819C820.307,157.537,820.537,157.307,820.819,157.163C821.008,157.067,821.205,157.031,821.402,157.015C821.59,157,821.817,157,822.081,157L822.081,157ZM838.516,158.012C838.374,158,838.188,158,837.9,158L835,158L835,157L837.9,157L837.919,157L837.919,157C838.183,157,838.41,157,838.598,157.015C838.795,157.031,838.992,157.067,839.181,157.163C839.463,157.307,839.693,157.537,839.837,157.819C839.933,158.008,839.969,158.205,839.985,158.402C840,158.59,840,158.817,840,159.081L840,159.1L840,162L839,162L839,159.1C839,158.812,839,158.626,838.988,158.484C838.977,158.348,838.958,158.298,838.946,158.273C838.898,158.179,838.821,158.102,838.727,158.054C838.702,158.042,838.652,158.023,838.516,158.012ZM821,169L821,171.9C821,172.188,821,172.374,821.012,172.516C821.023,172.652,821.042,172.702,821.054,172.727C821.102,172.821,821.179,172.898,821.273,172.946C821.298,172.958,821.348,172.977,821.484,172.988C821.626,173,821.812,173,822.1,173L825,173L825,174L822.1,174L822.081,174C821.817,174,821.59,174,821.402,173.985C821.205,173.969,821.008,173.933,820.819,173.837C820.537,173.693,820.307,173.463,820.163,173.181C820.067,172.992,820.031,172.795,820.015,172.598C820,172.41,820,172.183,820,171.919L820,171.919L820,171.9L820,169L821,169ZM839,171.9L839,169L840,169L840,171.9L840,171.919C840,172.183,840,172.41,839.985,172.598C839.969,172.795,839.933,172.992,839.837,173.181C839.693,173.463,839.463,173.693,839.181,173.837C838.992,173.933,838.795,173.969,838.598,173.985C838.41,174,838.183,174,837.919,174L837.9,174L835,174L835,173L837.9,173C838.188,173,838.374,173,838.516,172.988C838.652,172.977,838.702,172.958,838.727,172.946C838.821,172.898,838.898,172.821,838.946,172.727C838.958,172.702,838.977,172.652,838.988,172.516C839,172.374,839,172.188,839,171.9ZM826.5,161C826.776,161,827,161.224,827,161.5L827,162.5C827,162.776,826.776,163,826.5,163C826.224,163,826,162.776,826,162.5L826,161.5C826,161.224,826.224,161,826.5,161ZM831,163.5C831,163.224,830.776,163,830.5,163C830.224,163,830,163.224,830,163.5L830,164.75C830,164.888,829.888,165,829.75,165L829.5,165C829.224,165,829,165.224,829,165.5C829,165.776,829.224,166,829.5,166L829.75,166C830.44,166,831,165.44,831,164.75L831,163.5ZM826.85,168.143C826.654,167.951,826.34,167.952,826.146,168.146C825.951,168.342,825.951,168.658,826.146,168.854L826.5,168.5C826.146,168.854,826.147,168.854,826.147,168.854L826.147,168.854L826.148,168.855L826.151,168.858L826.156,168.863L826.173,168.879C826.186,168.891,826.203,168.906,826.224,168.924C826.267,168.961,826.328,169.009,826.407,169.065C826.565,169.176,826.797,169.318,827.109,169.457C827.737,169.736,828.681,170,830,170C831.319,170,832.263,169.736,832.891,169.457C833.203,169.318,833.435,169.176,833.593,169.065C833.672,169.009,833.733,168.961,833.776,168.924C833.797,168.906,833.814,168.891,833.827,168.879L833.844,168.863L833.849,168.858L833.852,168.855L833.853,168.854L833.853,168.854C833.853,168.854,833.854,168.854,833.5,168.5L833.854,168.854C834.049,168.658,834.049,168.342,833.854,168.146C833.66,167.952,833.346,167.951,833.15,168.143L833.149,168.144C833.146,168.147,833.139,168.153,833.129,168.162C833.107,168.18,833.07,168.21,833.016,168.248C832.909,168.324,832.734,168.432,832.484,168.543C831.987,168.764,831.181,169,830,169C828.819,169,828.013,168.764,827.516,168.543C827.266,168.432,827.091,168.324,826.984,168.248C826.93,168.21,826.893,168.18,826.871,168.162C826.861,168.153,826.854,168.147,826.851,168.144L826.85,168.143L826.85,168.143ZM833.149,168.144L833.148,168.145L833.147,168.146L833.149,168.144ZM834,161.5C834,161.224,833.776,161,833.5,161C833.224,161,833,161.224,833,161.5L833,162.5C833,162.776,833.224,163,833.5,163C833.776,163,834,162.776,834,162.5L834,161.5Z" fill="url(#Gradient-33)" fill-opacity="0.2" transform="translate(830,165.5) translate(-830,-165.5)"/>
                    </g>
                    <rect width="55.25" height="39.25" rx="2.625" stroke="url(#Gradient-34)" stroke-opacity="0.13" stroke-width="0.75" transform="translate(28,20) translate(-27.625,-19.625)"/>
                </g>
            </g>
            <g id="lines-5" transform="translate(1268.5,306) translate(-109.5,-37)">
                <g mask="url(#mask14_1398_56)" transform="translate(109.5,64) translate(-1268.5,-333)">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M1378,333.5L1159,333.5L1159,333L1378,333L1378,333.5Z" fill="url(#Gradient-35)" fill-opacity="0.1" transform="translate(1268.5,333.25) translate(-1268.5,-333.25)"/>
                    <rect width="45" height="20" fill="url(#Gradient-36)" transform="translate(1133.5,333) translate(-22.5,-10)" style="animation: 4.2s linear 11.4s infinite both a23_t;"/>
                </g>
                <g mask="url(#mask15_1398_56)" transform="translate(109.5,55) translate(-1268.5,-324)">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M1378,324.5L1159,324.5L1159,324L1378,324L1378,324.5Z" fill="url(#Gradient-37)" fill-opacity="0.1" transform="translate(1268.5,324.25) translate(-1268.5,-324.25)"/>
                    <rect width="45" height="20" fill="url(#Gradient-38)" transform="translate(1133.5,324) translate(-22.5,-10)" style="animation: 4.2s linear 10.4s infinite both a24_t;"/>
                </g>
                <g mask="url(#mask16_1398_56)" transform="translate(109.5,47) translate(-1268.5,-316)">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M1378,316.5L1159,316.5L1159,316L1378,316L1378,316.5Z" fill="url(#Gradient-39)" fill-opacity="0.1" transform="translate(1268.5,316.25) translate(-1268.5,-316.25)"/>
                    <rect width="45" height="20" fill="url(#Gradient-40)" transform="translate(1133.5,316) translate(-22.5,-10)" style="animation: 4.2s linear 10.8s infinite both a25_t;"/>
                </g>
                <g mask="url(#mask17_1398_56)" transform="translate(109.5,37) translate(-1268.5,-306)">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M1378,306.5L1159,306.5L1159,306L1378,306L1378,306.5Z" fill="url(#Gradient-41)" fill-opacity="0.1" transform="translate(1268.5,306.25) translate(-1268.5,-306.25)"/>
                    <rect width="45" height="20" fill="url(#Gradient-42)" transform="translate(1123.5,306) translate(-22.5,-10)" style="animation: 4.2s linear 12.2s infinite both a26_t;"/>
                </g>
                <g mask="url(#mask18_1398_56)" transform="translate(109.5,28) translate(-1268.5,-297)">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M1378,297.5L1159,297.5L1159,297L1378,297L1378,297.5Z" fill="url(#Gradient-43)" fill-opacity="0.1" transform="translate(1268.5,297.25) translate(-1268.5,-297.25)"/>
                    <rect width="45" height="20" fill="url(#Gradient-44)" transform="translate(1123.5,297) translate(-22.5,-10)" style="animation: 4.2s linear 10s infinite both a27_t;"/>
                </g>
                <g mask="url(#mask19_1398_56)" transform="translate(109.5,19) translate(-1268.5,-288)">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M1378,288.5L1159,288.5L1159,288L1378,288L1378,288.5Z" fill="url(#Gradient-45)" fill-opacity="0.1" transform="translate(1268.5,288.25) translate(-1268.5,-288.25)"/>
                    <rect width="45" height="20" fill="url(#Gradient-46)" transform="translate(1123.5,288) translate(-22.5,-10)" style="animation: 4.2s linear 9.4s infinite both a28_t;"/>
                </g>
                <g mask="url(#mask20_1398_56)" transform="translate(109.5,10) translate(-1268.5,-279)">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M1378,279.5L1159,279.5L1159,279L1378,279L1378,279.5Z" fill="url(#Gradient-47)" fill-opacity="0.1" transform="translate(1268.5,279.25) translate(-1268.5,-279.25)"/>
                    <rect width="45" height="20" fill="url(#Gradient-48)" transform="translate(1133.5,279) translate(-22.5,-10)" style="animation: 4.2s linear 10.4s infinite both a29_t;"/>
                </g>
            </g>
            <g id="lines-4" transform="translate(1086.25,440) translate(-33.25,-58)">
                <g mask="url(#mask3_1398_56)" transform="translate(58,58) translate(-1111,-440)">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M1111,382L1111,498L1110.5,498L1110.5,382L1111,382Z" fill="white" fill-opacity="0.15" transform="translate(1110.75,440) translate(-1110.75,-440)"/>
                    <rect width="17" height="51" fill="url(#Gradient-49)" transform="translate(1111,344.5) translate(-8.5,-25.5)" style="animation: 3.6s linear 12s infinite both a30_t;"/>
                </g>
                <g mask="url(#mask4_1398_56)" transform="translate(48.5,58) translate(-1101.5,-440)">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M1101.5,382L1101.5,498L1101,498L1101,382L1101.5,382Z" fill="white" fill-opacity="0.15" transform="translate(1101.25,440) translate(-1101.25,-440)"/>
                    <rect width="17" height="51" fill="url(#Gradient-50)" transform="translate(1101.5,344.5) translate(-8.5,-25.5)" style="animation: 3.6s linear 13.2s infinite both a31_t;"/>
                </g>
                <g mask="url(#mask5_1398_56)" transform="translate(38.5,58) translate(-1091.5,-440)">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M1091.5,382L1091.5,498L1091,498L1091,382L1091.5,382Z" fill="white" fill-opacity="0.15" transform="translate(1091.25,440) translate(-1091.25,-440)"/>
                    <rect width="17" height="51" fill="url(#Gradient-51)" transform="translate(1091.5,350.5) translate(-8.5,-25.5)" style="animation: 4s linear 12.2s infinite both a32_t;"/>
                </g>
                <g mask="url(#mask6_1398_56)" transform="translate(28.5,58) translate(-1081.5,-440)">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M1081.5,382L1081.5,498L1081,498L1081,382L1081.5,382Z" fill="white" fill-opacity="0.15" transform="translate(1081.25,440) translate(-1081.25,-440)"/>
                    <rect width="17" height="51" fill="url(#Gradient-52)" transform="translate(1081.5,344.5) translate(-8.5,-25.5)" style="animation: 4s linear 10.6s infinite both a33_t;"/>
                </g>
                <g mask="url(#mask7_1398_56)" transform="translate(18.5,58) translate(-1071.5,-440)">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M1071.5,382L1071.5,498L1071,498L1071,382L1071.5,382Z" fill="white" fill-opacity="0.15" transform="translate(1071.25,440) translate(-1071.25,-440)"/>
                    <rect width="17" height="51" fill="url(#Gradient-53)" transform="translate(1071.5,344.5) translate(-8.5,-25.5)" style="animation: 4s linear 11.4s infinite both a34_t;"/>
                </g>
                <g mask="url(#mask8_1398_56)" transform="translate(8.5,58) translate(-1061.5,-440)">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M1061.5,382L1061.5,498L1061,498L1061,382L1061.5,382Z" fill="white" fill-opacity="0.15" transform="translate(1061.25,440) translate(-1061.25,-440)"/>
                    <rect width="17" height="51" fill="url(#Gradient-54)" transform="translate(1061.5,344.5) translate(-8.5,-25.5)" style="animation: 3.6s linear 10s infinite both a35_t;"/>
                </g>
            </g>
            <g id="lines-3" transform="translate(936,305) translate(-78,-19)">
                <g mask="url(#mask9_1398_56)" transform="translate(78,10) translate(-936,-296)">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M1014,296.5L858,296.5L858,296L1014,296L1014,296.5Z" fill="url(#Gradient-55)" fill-opacity="0.1" transform="translate(936,296.25) translate(-936,-296.25)"/>
                    <rect width="45" height="20" fill="url(#Gradient-56)" transform="translate(832.5,296) translate(-22.5,-10)" style="animation: 3.6s linear 8.2s infinite both a36_t;"/>
                </g>
                <g mask="url(#mask10_1398_56)" transform="translate(78,19) translate(-936,-305)">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M1014,305.5L858,305.5L858,305L1014,305L1014,305.5Z" fill="url(#Gradient-57)" fill-opacity="0.1" transform="translate(936,305.25) translate(-936,-305.25)"/>
                    <rect width="45" height="20" fill="url(#Gradient-58)" transform="translate(833.5,305) translate(-22.5,-10)" style="animation: 3.6s linear 8.4s infinite both a37_t;"/>
                </g>
                <g mask="url(#mask11_1398_56)" transform="translate(78,28) translate(-936,-314)">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M1014,314.5L858,314.5L858,314L1014,314L1014,314.5Z" fill="url(#Gradient-59)" fill-opacity="0.1" transform="translate(936,314.25) translate(-936,-314.25)"/>
                    <rect width="45" height="20" fill="url(#Gradient-60)" transform="translate(830.5,314) translate(-22.5,-10)" style="animation: 3.6s linear 7.4s infinite both a38_t;"/>
                </g>
            </g>
            <g id="lines-2" transform="translate(829.75,229.5) translate(-16.25,-40.5)">
                <g mask="url(#mask1_1398_56)" transform="translate(24,40.5) translate(-837.5,-229.5)">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M838,189L838,270L837.5,270L837.5,189L838,189Z" fill="white" fill-opacity="0.1" transform="translate(837.75,229.5) translate(-837.75,-229.5)"/>
                    <rect width="17" height="51" fill="url(#Gradient-61)" transform="translate(837.5,154.5) translate(-8.5,-25.5)" style="animation: 4.6s linear 6s infinite both a39_t;"/>
                </g>
                <g mask="url(#mask2_1398_56)" transform="translate(8.5,40.5) translate(-822,-229.5)">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M822.5,189L822.5,270L822,270L822,189L822.5,189Z" fill="white" fill-opacity="0.1" transform="translate(822.25,229.5) translate(-822.25,-229.5)"/>
                    <rect width="17" height="51" fill="url(#Gradient-62)" transform="translate(822,152.5) translate(-8.5,-25.5)" style="animation: 4.6s linear 4s infinite both a40_t;"/>
                </g>
            </g>
            <g id="lines-1" transform="translate(829.75,105.5) translate(-16.25,-35.5)">
                <g mask="url(#mask12_1398_56)" transform="translate(24,35.5) translate(-837.5,-105.5)">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M838,70L838,141L837.5,141L837.5,70L838,70Z" fill="white" fill-opacity="0.1" transform="translate(837.75,105.5) translate(-837.75,-105.5)"/>
                    <rect width="17" height="51" fill="url(#Gradient-63)" transform="translate(837.5,45.5) translate(-8.5,-25.5)" style="animation: 4.6s linear 2s infinite both a41_t;"/>
                </g>
                <g mask="url(#mask13_1398_56)" transform="translate(8.5,35.5) translate(-822,-105.5)">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M822.5,70L822.5,141L822,141L822,70L822.5,70Z" fill="white" fill-opacity="0.1" transform="translate(822.25,105.5) translate(-822.25,-105.5)"/>
                    <rect width="17" height="51" fill="url(#Gradient-64)" transform="translate(822,45.5) translate(-8.5,-25.5)" style="animation: 4.6s linear infinite both a42_t;"/>
                </g>
            </g>
            <g id="light-3" filter="url(#filter29_dd_1398_56)" transform="translate(1144.5,247.5) translate(-1144.5,-247.5)">
                <rect width="3" height="3" rx="1.5" fill="url(#Gradient-65)" opacity="1" transform="translate(1144.5,247.5) translate(-1.5,-1.5)" style="animation: 9s linear 2.4s infinite both a43_o;"/>
                <rect width="2.5" height="2.5" rx="1.25" stroke="black" stroke-opacity="0.05" stroke-width="0.5" transform="translate(1144.5,247.5) translate(-1.25,-1.25)"/>
            </g>
            <g id="light-2" filter="url(#filter31_dd_1398_56)" transform="translate(850.5,281.5) translate(-850.5,-281.5)">
                <rect width="3" height="3" rx="1.5" fill="url(#Gradient-66)" opacity="1" transform="translate(850.5,281.5) translate(-1.5,-1.5)" style="animation: 7.8s linear 1s infinite both a44_o;"/>
                <rect width="2.5" height="2.5" rx="1.25" stroke="black" stroke-opacity="0.05" stroke-width="0.5" transform="translate(850.5,281.5) translate(-1.25,-1.25)"/>
            </g>
            <g id="light-1" filter="url(#filter18_dd_1398_56)" transform="translate(850.5,152.5) translate(-850.5,-152.5)">
                <rect width="3" height="3" rx="1.5" fill="url(#Gradient-67)" shape-rendering="crispEdges" opacity="1" transform="translate(850.5,152.5) translate(-1.5,-1.5)" style="animation: 8s linear infinite both a45_o;"/>
                <rect width="3.5" height="3.5" rx="1.75" stroke="black" stroke-opacity="0.05" stroke-width="0.5" shape-rendering="crispEdges" transform="translate(850.5,152.5) translate(-1.75,-1.75)"/>
            </g>
        </g>
    </g>
</svg>