"use strict";exports.id=7453,exports.ids=[7453],exports.modules={87453:(e,t,r)=>{r.r(t),r.d(t,{ClientToolbar:()=>m});var a=r(34526),o=r(35371),n=r(53172),i=(0,a.P)({"../../node_modules/.pnpm/lodash.debounce@4.0.8/node_modules/lodash.debounce/index.js"(e,t){var r=0/0,a=/^\s+|\s+$/g,o=/^[-+]0x[0-9a-f]+$/i,n=/^0b[01]+$/i,i=/^0o[0-7]+$/i,l=parseInt,s="object"==typeof global&&global&&global.Object===Object&&global,p="object"==typeof self&&self&&self.Object===Object&&self,b=s||p||Function("return this")(),c=Object.prototype.toString,d=Math.max,f=Math.min,u=function(){return b.Date.now()};function _(e){var t=typeof e;return!!e&&("object"==t||"function"==t)}function m(e){if("number"==typeof e)return e;if("symbol"==typeof(t=e)||t&&"object"==typeof t&&"[object Symbol]"==c.call(t))return r;if(_(e)){var t,s="function"==typeof e.valueOf?e.valueOf():e;e=_(s)?s+"":s}if("string"!=typeof e)return 0===e?e:+e;e=e.replace(a,"");var p=n.test(e);return p||i.test(e)?l(e.slice(2),p?2:8):o.test(e)?r:+e}t.exports=function(e,t,r){var a,o,n,i,l,s,p=0,b=!1,c=!1,y=!0;if("function"!=typeof e)throw TypeError("Expected a function");function h(t){var r=a,n=o;return a=o=void 0,p=t,i=e.apply(n,r)}function g(e){var r=e-s,a=e-p;return void 0===s||r>=t||r<0||c&&a>=n}function w(){var e,r,a,o=u();if(g(o))return v(o);l=setTimeout(w,(e=o-s,r=o-p,a=t-e,c?f(a,n-r):a))}function v(e){return(l=void 0,y&&a)?h(e):(a=o=void 0,i)}function x(){var e,r=u(),n=g(r);if(a=arguments,o=this,s=r,n){if(void 0===l)return p=e=s,l=setTimeout(w,t),b?h(e):i;if(c)return l=setTimeout(w,t),h(s)}return void 0===l&&(l=setTimeout(w,t)),i}return t=m(t)||0,_(r)&&(b=!!r.leading,n=(c="maxWait"in r)?d(m(r.maxWait)||0,t):n,y="trailing"in r?!!r.trailing:y),x.cancel=function(){void 0!==l&&clearTimeout(l),p=0,a=s=o=l=void 0},x.flush=function(){return void 0===l?i:v(u())},x}}}),l="dffb3111f2dbe90df2c9f44aa745e1eaea5704ee2372695a11b6c736b47349b7",s=`._wrapper_ypbb5_1 {
  box-sizing: border-box;
  font-size: 16px;
}
._wrapper_ypbb5_1 *,
._wrapper_ypbb5_1 *:before,
._wrapper_ypbb5_1 *:after {
  box-sizing: inherit;
}
._wrapper_ypbb5_1 h1,
._wrapper_ypbb5_1 h2,
._wrapper_ypbb5_1 h3,
._wrapper_ypbb5_1 h4,
._wrapper_ypbb5_1 h5,
._wrapper_ypbb5_1 h6,
._wrapper_ypbb5_1 p,
._wrapper_ypbb5_1 ol,
._wrapper_ypbb5_1 ul {
  margin: 0;
  padding: 0;
  font-weight: normal;
}
._wrapper_ypbb5_1 ol,
._wrapper_ypbb5_1 ul {
  list-style: none;
}
._wrapper_ypbb5_1 img {
  max-width: 100%;
  height: auto;
}

._branch_ypbb5_32 {
  padding-left: 9px;
  padding-right: 12px;
  height: 100%;
  display: flex;
  align-items: center;
  font-weight: 500;
  user-select: none;
}

._wrapper_ypbb5_1 {
  position: fixed;
  bottom: 32px;
  right: 32px;
  background: #0c0c0c;
  z-index: 1000;
  border-radius: 7px;
  animation: _in_ypbb5_1 0.3s ease-out;
  display: flex;
}

._root_ypbb5_53 {
  --font-family: Inter, Segoe UI, Roboto, sans-serif, Apple Color Emoji,
    Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji, sans-serif;
  border-radius: 6px;
  height: 36px;
  color: white;
  display: flex;
  border: 1px solid #303030;
  font-family: var(--font-family);
}
._root_ypbb5_53[data-draft-active=true] {
  border-color: #ff6c02;
  background-color: rgba(255, 108, 2, 0.15);
}
._root_ypbb5_53[data-draft-active=true]:has(button._draft_ypbb5_67:enabled:hover) {
  border-color: #ff8b35;
}

._draft_ypbb5_67 {
  all: unset;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 10px;
  cursor: pointer;
  color: #646464;
  border-left: 1px solid #303030;
  border-radius: 0 5px 5px 0;
  margin: -1px;
}
._draft_ypbb5_67:disabled:hover {
  cursor: not-allowed;
}
._draft_ypbb5_67[data-active=true] {
  border-color: #ff6c02;
}
._draft_ypbb5_67[data-active=true]:enabled:hover {
  border-color: #ff8b35;
  background-color: #ff8b35;
}
._draft_ypbb5_67[data-active=false] {
  border: 1px solid #303030;
}
._draft_ypbb5_67[data-active=false]:enabled:hover {
  background-color: #0c0c0c;
}
._draft_ypbb5_67:focus-visible {
  outline: 1px solid;
  outline-offset: -1px;
  outline-color: #303030;
  border-radius: 0 6px 6px 0;
}
._draft_ypbb5_67[data-active=true] {
  color: #f3f3f3;
  background-color: #ff6c02;
}
._draft_ypbb5_67[data-loading=false] ._draft_ypbb5_67[data-active=true] {
  transition: color 0.2s, background-color 0.2s;
}
._draft_ypbb5_67[data-loading=false] ._draft_ypbb5_67[data-active=true]:enabled:hover {
  color: #fff;
}
._draft_ypbb5_67[data-loading=true] {
  cursor: wait !important;
}
._draft_ypbb5_67[data-loading=true] svg {
  animation: _breathe_ypbb5_1 1s infinite;
}

._tooltipWrapper_ypbb5_122 {
  position: relative;
  display: flex;
  height: 100%;
}
._tooltipWrapper_ypbb5_122:hover ._tooltip_ypbb5_122 {
  visibility: visible;
}

._dragHandle_ypbb5_131 {
  all: unset;
  cursor: grab;
}
._dragHandle_ypbb5_131._dragging_ypbb5_135 {
  cursor: grabbing;
}
._dragHandle_ypbb5_131:active {
  cursor: grabbing;
}

._tooltip_ypbb5_122 {
  position: absolute;
  bottom: 40px;
  left: 50%;
  transform: translateX(-50%) translateY(0);
  background-color: #0c0c0c;
  border: 1px solid #303030;
  color: white;
  border-radius: 4px;
  max-width: 250px;
  width: max-content;
  font-size: 14px;
  z-index: 1000;
  visibility: hidden;
  --translate-x: -50%;
}
._tooltip_ypbb5_122._forceVisible_ypbb5_158 {
  visibility: visible;
}
._tooltip_ypbb5_122._top_ypbb5_161 {
  top: 40px;
  bottom: unset;
  transform: translateY(0) translateX(var(--translate-x));
}
._tooltip_ypbb5_122._top_ypbb5_161:before {
  mask-image: linear-gradient(135deg, rgb(0, 0, 0) 31%, rgba(0, 0, 0, 0) 31%, rgba(0, 0, 0, 0) 100%);
  top: -4.5px;
  bottom: unset;
  transform: translateX(var(--translate-x)) rotate(45deg);
}
._tooltip_ypbb5_122._bottom_ypbb5_172 {
  bottom: unset;
  top: -40px;
  transform: translateY(0) translateX(var(--translate-x));
}
._tooltip_ypbb5_122._bottom_ypbb5_172:before {
  bottom: -4.5px;
  top: unset;
  transform: translateX(0) rotate(45deg);
}
._tooltip_ypbb5_122._right_ypbb5_182 {
  right: 0;
  left: unset;
  transform: translateX(0);
  --translate-x: 0;
}
._tooltip_ypbb5_122._right_ypbb5_182:before {
  right: 8px;
  left: unset;
  transform: translateX(--translate-x) rotate(45deg);
}
._tooltip_ypbb5_122._left_ypbb5_193 {
  left: 50%;
  right: unset;
  transform: translateX(-50%);
  --translate-x: -50%;
}
._tooltip_ypbb5_122._left_ypbb5_193:before {
  left: 50%;
  right: unset;
  transform: translateX(-50%) rotate(45deg);
}
._tooltip_ypbb5_122:before {
  z-index: -1;
  mask-image: linear-gradient(-45deg, rgb(0, 0, 0) 31%, rgba(0, 0, 0, 0) 31%, rgba(0, 0, 0, 0) 100%);
  content: "";
  position: absolute;
  bottom: -4.5px;
  left: 50%;
  width: 20px;
  height: 20px;
  background-color: #0c0c0c;
  transform: rotate(45deg) translateX(-50%);
  border-radius: 2px;
  border: 1px solid #303030;
}

._branchSelect_ypbb5_219 {
  height: 100%;
  background: none;
  border: none;
  font-weight: 500;
  font-size: 16px;
  padding-right: 8px;
  padding-bottom: 0px;
  padding-top: 0px;
  margin-bottom: 2px;
  min-width: 80px;
  max-width: 250px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: normal;
  outline: none;
  color: inherit;
  text-overflow: ellipsis;
  white-space: nowrap;
  opacity: 1;
  font-family: var(--font-family);
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
}

._branchSelectIcon_ypbb5_245 {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  right: 0;
  pointer-events: none;
}

@keyframes _in_ypbb5_1 {
  0% {
    opacity: 0;
    transform: translateY(4px) scale(0.98);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}
@keyframes _breathe_ypbb5_1 {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.45;
  }
  100% {
    opacity: 1;
  }
}`;!function(){if("undefined"!=typeof document&&!document.getElementById(l)){var e=document.createElement("style");e.id=l,e.textContent=s,document.head.appendChild(e)}}();var p={wrapper:"_wrapper_ypbb5_1",branch:"_branch_ypbb5_32",root:"_root_ypbb5_53",draft:"_draft_ypbb5_67",tooltipWrapper:"_tooltipWrapper_ypbb5_122",tooltip:"_tooltip_ypbb5_122",dragHandle:"_dragHandle_ypbb5_131",dragging:"_dragging_ypbb5_135",forceVisible:"_forceVisible_ypbb5_158",top:"_top_ypbb5_161",bottom:"_bottom_ypbb5_172",right:"_right_ypbb5_182",left:"_left_ypbb5_193",branchSelect:"_branchSelect_ypbb5_219",branchSelectIcon:"_branchSelectIcon_ypbb5_245"},b=(0,a.f)(i(),1),c=o.forwardRef(({children:e,content:t,forceVisible:r},a)=>{let n=o.useRef(null),i=o.useCallback((0,b.default)(()=>{if(n.current){let e=n.current.getBoundingClientRect(),t=n.current.classList.contains(p.left)?0:e.width/2,r=e.height;(n.current.classList.contains(p.bottom)?e.top:e.top-80-r)<=0?(n.current.classList.remove(p.bottom),n.current.classList.add(p.top)):(n.current.classList.remove(p.top),n.current.classList.add(p.bottom)),e.right+t>window.innerWidth?(n.current.classList.remove(p.left),n.current.classList.add(p.right)):(n.current.classList.remove(p.right),n.current.classList.add(p.left))}},100),[]);return o.useEffect(()=>(i(),window.addEventListener("resize",i),()=>{window.removeEventListener("resize",i)}),[i]),o.useImperativeHandle(a,()=>({checkOverflow:i}),[i]),o.createElement("div",{className:p.tooltipWrapper},o.createElement("p",{ref:n,style:{padding:"3px 8px"},className:r?`${p.tooltip} ${p.bottom} ${p.left} ${p.forceVisible}`:`${p.tooltip} ${p.bottom} ${p.left}`},t),e)}),d=o.forwardRef(({onDrag:e,children:t},r)=>{let[a,n]=o.useState(!1),i=o.useRef({x:0,y:0}),l=o.useRef({x:0,y:0}),s=o.useRef(!1);o.useImperativeHandle(r,()=>({hasDragged:s.current}));let b=o.useCallback(t=>{if(!a)return;let r=t.clientX-i.current.x,o=t.clientY-i.current.y,n=l.current.x+r,p=l.current.y+o;(Math.abs(r)>2||Math.abs(o)>2)&&(s.current=!0),e({x:n,y:p})},[a,e]);return o.useLayoutEffect(()=>{if(a)return window.addEventListener("pointermove",b),()=>{window.removeEventListener("pointermove",b)}},[a,e,b]),o.useLayoutEffect(()=>{if(!a){s.current=!1;return}let e=()=>{n(!1)};return window.addEventListener("pointerup",e),()=>{window.removeEventListener("pointerup",e)}},[a]),o.createElement("span",{draggable:!0,className:`${p.dragHandle} ${a?p.dragging:""}`,onPointerDown:e=>{if(e.target instanceof HTMLElement&&("select"===e.target.nodeName.toLowerCase()||e.target.closest("select")))return;let t=e.currentTarget;if(!t)return;e.stopPropagation(),e.preventDefault(),i.current={x:e.clientX,y:e.clientY};let r=t.getBoundingClientRect();l.current.x=r.left,l.current.y=r.top,n(!0)},onPointerUp:()=>{n(!1)}},t)}),f=({isForcedDraft:e,draft:t,apiRref:r,latestBranches:a,onRefChange:n,getAndSetLatestBranches:i})=>{let l=o.useRef(null),s=o.useRef(null),b=o.useMemo(()=>[...a].sort((e,t)=>e.isDefault?-1:t.isDefault?1:e.name.localeCompare(t.name)),[a]),d=o.useMemo(()=>{let e=new Set(b.map(e=>e.name));return e.add(r),Array.from(e)},[b,r]),[f,_]=o.useState(!1);o.useEffect(()=>{f&&i().then(()=>{_(!1)})},[f,i]),o.useEffect(()=>{let e=l.current,t=s.current;if(!e||!t)return;let r=()=>{let r=e.offsetWidth;t.style.width=`${r+20}px`};return r(),window.addEventListener("resize",r),()=>{window.removeEventListener("resize",r),t&&t.style.removeProperty("width")}},[r]);let m=e||t;return o.createElement("div",{className:p.branch,"data-draft-active":m,onMouseEnter:()=>{_(!0)}},o.createElement(u,null),"\xa0",o.createElement(c,{content:m?"Switch branch":"Switch branch and enter draft mode"},o.createElement("select",{ref:s,value:r,onChange:e=>n(e.target.value,{enableDraftMode:!m}),className:p.branchSelect,onMouseDown:e=>{e.stopPropagation()},onClick:e=>{e.stopPropagation(),_(!0)}},d.map(e=>o.createElement("option",{key:e,value:e},e))),o.createElement("svg",{width:"15",height:"15",viewBox:"0 0 15 15",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:p.branchSelectIcon},o.createElement("path",{d:"M4.93179 5.43179C4.75605 5.60753 4.75605 5.89245 4.93179 6.06819C5.10753 6.24392 5.39245 6.24392 5.56819 6.06819L7.49999 4.13638L9.43179 6.06819C9.60753 6.24392 9.89245 6.24392 10.0682 6.06819C10.2439 5.89245 10.2439 5.60753 10.0682 5.43179L7.81819 3.18179C7.73379 3.0974 7.61933 3.04999 7.49999 3.04999C7.38064 3.04999 7.26618 3.0974 7.18179 3.18179L4.93179 5.43179ZM10.0682 9.56819C10.2439 9.39245 10.2439 9.10753 10.0682 8.93179C9.89245 8.75606 9.60753 8.75606 9.43179 8.93179L7.49999 10.8636L5.56819 8.93179C5.39245 8.75606 5.10753 8.75606 4.93179 8.93179C4.75605 9.10753 4.75605 9.39245 4.93179 9.56819L7.18179 11.8182C7.35753 11.9939 7.64245 11.9939 7.81819 11.8182L10.0682 9.56819Z",fill:"currentColor",fillRule:"evenodd",clipRule:"evenodd"}))),o.createElement("span",{className:p.branchSelect,style:{visibility:"hidden",opacity:0,pointerEvents:"none",position:"absolute",top:0,left:0},"aria-hidden":"true",ref:l},r))},u=()=>o.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"18",height:"18",fill:"none"},o.createElement("path",{fill:"#F3F3F3",fillRule:"evenodd",d:"M12.765 5.365a1.25 1.25 0 1 0 .002-2.502 1.25 1.25 0 0 0-.002 2.502Zm0 1.063a2.315 2.315 0 1 0-2.315-2.313 2.315 2.315 0 0 0 2.316 2.313ZM5.234 15.137a1.25 1.25 0 1 0 .001-2.501 1.25 1.25 0 0 0 0 2.501Zm0 1.064a2.315 2.315 0 1 0-2.316-2.314 2.315 2.315 0 0 0 2.316 2.314Z",clipRule:"evenodd"}),o.createElement("path",{fill:"#F3F3F3",fillRule:"evenodd",d:"M5.767 8.98v3.648H4.702V8.98h1.065ZM13.298 5.798v2.694h-1.065V5.798h1.065Z",clipRule:"evenodd"}),o.createElement("path",{fill:"#F3F3F3",fillRule:"evenodd",d:"M13.298 8.448a.532.532 0 0 1-.533.532H5.29a.532.532 0 1 1 0-1.064h7.476c.294 0 .533.238.533.532ZM5.234 2.864a1.25 1.25 0 1 1 .001 2.502 1.25 1.25 0 0 1 0-2.502Zm0-1.063a2.315 2.315 0 1 1-2.316 2.314A2.315 2.315 0 0 1 5.234 1.8Z",clipRule:"evenodd"}),o.createElement("path",{fill:"#F3F3F3",fillRule:"evenodd",d:"M5.767 9.022V5.374H4.702v3.648h1.065Z",clipRule:"evenodd"})),_=(0,a.f)(i(),1),m=({draft:e,isForcedDraft:t,enableDraftMode:r,disableDraftMode:a,bshbPreviewToken:n,shouldAutoEnableDraft:i,seekAndStoreBshbPreviewToken:l,resolvedRef:s,getLatestBranches:b})=>{let[u,m]=o.useState(null),w=o.useRef(null),v=o.useRef(null),[x,L]=o.useState(""),[C,E]=o.useState(!1),[M,k]=o.useState(s.ref),[R,S]=o.useState(!0),[Z,$]=o.useState(!0),[P,j]=o.useState([]),D=o.useRef(0),H=o.useCallback(e=>{window.clearTimeout(D.current),L(e),D.current=window.setTimeout(()=>L(""),5e3)},[L]),T=o.useCallback(e=>{E(!0),r({bshbPreviewToken:e}).then(({status:e,response:t})=>{200===e?(j(e=>t.latestBranches??e),window.location.reload()):"error"in t?H(`Draft mode activation error: ${t.error}`):H("Draft mode activation error")}).finally(()=>E(!1))},[r,H]),z=`bshb-preview-ref-${s.repoHash}`,F=o.useMemo(()=>({set:e=>{document.cookie=`${z}=${e}; path=/; Max-Age=946080000`},clear:()=>{document.cookie=`${z}=; path=/; Max-Age=-1`},get:()=>document.cookie.split("; ").find(e=>e.startsWith(z))?.split("=")[1]??null}),[z]),[I,N]=o.useState(!1);o.useLayoutEffect(()=>{e||I||!i||t||!n||(T(n),N(!0))},[t,r,l,n,H,T,e,i,I]);let V=o.useCallback(async()=>{let e=[],t=await b({bshbPreviewToken:n});t&&(Array.isArray(t.response)?e=t.response:"error"in t.response&&console.error(`BaseHub Toolbar Error: ${t.response.error}`),j(e))},[n,b]);o.useEffect(()=>{!async function(){for(;;)try{V(),await new Promise(e=>setTimeout(e,3e4))}catch(e){console.error(`BaseHub Toolbar Error: ${e}`);break}}()},[V]);let X=o.useCallback(e=>{k(e),window.__bshb_ref=e,window.dispatchEvent(new CustomEvent("__bshb_ref_changed")),F.set(e),S(e===s.ref)},[F,s.ref]);o.useEffect(()=>{let e=new URL(window.location.href).searchParams.get("bshb-preview-ref");e||(e=F.get()),$(!1),e&&X(e)},[F,X,s.repoHash]),o.useEffect(()=>{Z||S(M===s.ref)},[M,s.ref,Z]),o.useEffect(()=>{if(!Z&&R){X(s.ref),F.clear();let e=new URL(window.location.href);e.searchParams.delete("bshb-preview-ref"),window.history.replaceState(null,"",e.toString())}},[R,Z,F,s.ref,X]),o.useLayoutEffect(()=>{v.current?.checkOverflow()},[x]);let W=o.useCallback(()=>{},[u]),B=o.useCallback((0,_.default)(e=>{},250),[]),O=o.useCallback(e=>{if(!u)return;let t=u.getBoundingClientRect(),r={};e.x-32<0?(u.style.left="32px",u.style.right="unset",r.x=32):e.x+t.width+32>window.innerWidth?(u.style.right="32px",u.style.left="unset",r.x=32):(u.style.right="unset",u.style.left=`${e.x}px`,r.x=e.x),e.y-32<0?(u.style.bottom="unset",u.style.top="32px",r.y=32):e.y+t.height+32>window.innerHeight?(u.style.top="unset",u.style.bottom="32px",r.y=32):(u.style.bottom="unset",u.style.top=`${e.y}px`,r.x=e.y),B({x:e.x,y:e.y})},[u,B]);o.useEffect(()=>{},[W,O]),o.useEffect(()=>{if(!P)return;let e=F.get();e&&(P.find(t=>t.name===e)||F.clear())},[P,F]);let U=t?"Draft enforced by dev env":`${e?"Disable":"Enable"} draft mode`;return o.createElement("div",{className:p.wrapper,ref:m},o.createElement(d,{ref:w,onDrag:e=>{O(e),v.current?.checkOverflow()}},o.createElement("div",{className:p.root,"data-draft-active":t||e},o.createElement(f,{isForcedDraft:t,draft:e,apiRref:M,latestBranches:P,onRefChange:(e,t)=>{let r=new URL(window.location.href);if(r.searchParams.set("bshb-preview-ref",e),window.history.replaceState(null,"",r.toString()),X(e),t.enableDraftMode){let e=n??l();if(!e)return H("Preview token not found");T(e)}},getAndSetLatestBranches:V}),o.createElement(y,{previewRef:M,resolvedRef:s,isDraftModeEnabled:t||e}),o.createElement(c,{content:x||U,ref:v,forceVisible:!!x},o.createElement("button",{className:p.draft,"data-active":t||e,"aria-label":`${e?"Disable":"Enable"} draft mode`,"data-loading":C,disabled:t||C,onClick:()=>{if(!(C||w.current?.hasDragged))if(e)E(!0),a().then(()=>{let e=new URL(window.location.href);e.searchParams.delete("bshb-preview"),e.searchParams.delete("__vercel_draft"),window.location.href=e.toString()}).finally(()=>E(!1));else{let e=n??l();if(!e)return H("Preview token not found");T(e)}}},e||t?o.createElement(g,null):o.createElement(h,null))))))},y=({previewRef:e,resolvedRef:t,isDraftModeEnabled:r})=>{let a=(0,n.usePathname)(),[i,l]=o.useState(a);return o.useEffect(()=>{i||l(a)},[a,i]),o.useEffect(()=>{if(!r&&i!==a&&e!==t.ref){let t=new URL(window.location.href);t.searchParams.set("bshb-preview-ref",e),window.history.replaceState(null,"",t.toString())}},[r,e,t.ref,a,i]),null},h=()=>o.createElement("svg",{"data-testid":"geist-icon",height:"16",strokeLinejoin:"round",viewBox:"0 0 16 16",width:"16",style:{color:"currentcolor"}},o.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M6.51404 3.15793C7.48217 2.87411 8.51776 2.87411 9.48589 3.15793L9.90787 1.71851C8.66422 1.35392 7.33571 1.35392 6.09206 1.71851L6.51404 3.15793ZM10.848 3.78166C11.2578 4.04682 11.6393 4.37568 11.9783 4.76932L13.046 6.00934L14.1827 5.03056L13.1149 3.79054C12.6818 3.28761 12.1918 2.86449 11.6628 2.52224L10.848 3.78166ZM4.02168 4.76932C4.36065 4.37568 4.74209 4.04682 5.15195 3.78166L4.33717 2.52225C3.80815 2.86449 3.3181 3.28761 2.88503 3.79054L1.81723 5.03056L2.95389 6.00934L4.02168 4.76932ZM14.1138 7.24936L14.7602 7.99999L14.1138 8.75062L15.2505 9.72941L16.3183 8.48938V7.5106L15.2505 6.27058L14.1138 7.24936ZM1.88609 7.24936L1.23971 7.99999L1.88609 8.75062L0.749437 9.72941L-0.318359 8.48938V7.5106L0.749436 6.27058L1.88609 7.24936ZM13.0461 9.99064L11.9783 11.2307C11.6393 11.6243 11.2578 11.9532 10.848 12.2183L11.6628 13.4777C12.1918 13.1355 12.6818 12.7124 13.1149 12.2094L14.1827 10.9694L13.0461 9.99064ZM4.02168 11.2307L2.95389 9.99064L1.81723 10.9694L2.88503 12.2094C3.3181 12.7124 3.80815 13.1355 4.33717 13.4777L5.15195 12.2183C4.7421 11.9532 4.36065 11.6243 4.02168 11.2307ZM9.90787 14.2815L9.48589 12.8421C8.51776 13.1259 7.48217 13.1259 6.51405 12.8421L6.09206 14.2815C7.33572 14.6461 8.66422 14.6461 9.90787 14.2815ZM6.49997 7.99999C6.49997 7.17157 7.17154 6.49999 7.99997 6.49999C8.82839 6.49999 9.49997 7.17157 9.49997 7.99999C9.49997 8.82842 8.82839 9.49999 7.99997 9.49999C7.17154 9.49999 6.49997 8.82842 6.49997 7.99999ZM7.99997 4.99999C6.34311 4.99999 4.99997 6.34314 4.99997 7.99999C4.99997 9.65685 6.34311 11 7.99997 11C9.65682 11 11 9.65685 11 7.99999C11 6.34314 9.65682 4.99999 7.99997 4.99999Z",fill:"currentColor"})),g=()=>o.createElement("svg",{"data-testid":"geist-icon",height:"16",strokeLinejoin:"round",viewBox:"0 0 16 16",width:"16",style:{color:"currentcolor"}},o.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M4.02168 4.76932C6.11619 2.33698 9.88374 2.33698 11.9783 4.76932L14.7602 7.99999L11.9783 11.2307C9.88374 13.663 6.1162 13.663 4.02168 11.2307L1.23971 7.99999L4.02168 4.76932ZM13.1149 3.79054C10.422 0.663244 5.57797 0.663247 2.88503 3.79054L-0.318359 7.5106V8.48938L2.88503 12.2094C5.57797 15.3367 10.422 15.3367 13.1149 12.2094L16.3183 8.48938V7.5106L13.1149 3.79054ZM6.49997 7.99999C6.49997 7.17157 7.17154 6.49999 7.99997 6.49999C8.82839 6.49999 9.49997 7.17157 9.49997 7.99999C9.49997 8.82842 8.82839 9.49999 7.99997 9.49999C7.17154 9.49999 6.49997 8.82842 6.49997 7.99999ZM7.99997 4.99999C6.34311 4.99999 4.99997 6.34314 4.99997 7.99999C4.99997 9.65685 6.34311 11 7.99997 11C9.65682 11 11 9.65685 11 7.99999C11 6.34314 9.65682 4.99999 7.99997 4.99999Z",fill:"currentColor"}))}};