"use strict";exports.id=5954,exports.ids=[5954],exports.modules={14251:(u,D,F)=>{F.d(D,{sD:()=>A});var e=F(66577);let t=/[\0-\x1F!-,\.\/:-@\[-\^`\{-\xA9\xAB-\xB4\xB6-\xB9\xBB-\xBF\xD7\xF7\u02C2-\u02C5\u02D2-\u02DF\u02E5-\u02EB\u02ED\u02EF-\u02FF\u0375\u0378\u0379\u037E\u0380-\u0385\u0387\u038B\u038D\u03A2\u03F6\u0482\u0530\u0557\u0558\u055A-\u055F\u0589-\u0590\u05BE\u05C0\u05C3\u05C6\u05C8-\u05CF\u05EB-\u05EE\u05F3-\u060F\u061B-\u061F\u066A-\u066D\u06D4\u06DD\u06DE\u06E9\u06FD\u06FE\u0700-\u070F\u074B\u074C\u07B2-\u07BF\u07F6-\u07F9\u07FB\u07FC\u07FE\u07FF\u082E-\u083F\u085C-\u085F\u086B-\u089F\u08B5\u08C8-\u08D2\u08E2\u0964\u0965\u0970\u0984\u098D\u098E\u0991\u0992\u09A9\u09B1\u09B3-\u09B5\u09BA\u09BB\u09C5\u09C6\u09C9\u09CA\u09CF-\u09D6\u09D8-\u09DB\u09DE\u09E4\u09E5\u09F2-\u09FB\u09FD\u09FF\u0A00\u0A04\u0A0B-\u0A0E\u0A11\u0A12\u0A29\u0A31\u0A34\u0A37\u0A3A\u0A3B\u0A3D\u0A43-\u0A46\u0A49\u0A4A\u0A4E-\u0A50\u0A52-\u0A58\u0A5D\u0A5F-\u0A65\u0A76-\u0A80\u0A84\u0A8E\u0A92\u0AA9\u0AB1\u0AB4\u0ABA\u0ABB\u0AC6\u0ACA\u0ACE\u0ACF\u0AD1-\u0ADF\u0AE4\u0AE5\u0AF0-\u0AF8\u0B00\u0B04\u0B0D\u0B0E\u0B11\u0B12\u0B29\u0B31\u0B34\u0B3A\u0B3B\u0B45\u0B46\u0B49\u0B4A\u0B4E-\u0B54\u0B58-\u0B5B\u0B5E\u0B64\u0B65\u0B70\u0B72-\u0B81\u0B84\u0B8B-\u0B8D\u0B91\u0B96-\u0B98\u0B9B\u0B9D\u0BA0-\u0BA2\u0BA5-\u0BA7\u0BAB-\u0BAD\u0BBA-\u0BBD\u0BC3-\u0BC5\u0BC9\u0BCE\u0BCF\u0BD1-\u0BD6\u0BD8-\u0BE5\u0BF0-\u0BFF\u0C0D\u0C11\u0C29\u0C3A-\u0C3C\u0C45\u0C49\u0C4E-\u0C54\u0C57\u0C5B-\u0C5F\u0C64\u0C65\u0C70-\u0C7F\u0C84\u0C8D\u0C91\u0CA9\u0CB4\u0CBA\u0CBB\u0CC5\u0CC9\u0CCE-\u0CD4\u0CD7-\u0CDD\u0CDF\u0CE4\u0CE5\u0CF0\u0CF3-\u0CFF\u0D0D\u0D11\u0D45\u0D49\u0D4F-\u0D53\u0D58-\u0D5E\u0D64\u0D65\u0D70-\u0D79\u0D80\u0D84\u0D97-\u0D99\u0DB2\u0DBC\u0DBE\u0DBF\u0DC7-\u0DC9\u0DCB-\u0DCE\u0DD5\u0DD7\u0DE0-\u0DE5\u0DF0\u0DF1\u0DF4-\u0E00\u0E3B-\u0E3F\u0E4F\u0E5A-\u0E80\u0E83\u0E85\u0E8B\u0EA4\u0EA6\u0EBE\u0EBF\u0EC5\u0EC7\u0ECE\u0ECF\u0EDA\u0EDB\u0EE0-\u0EFF\u0F01-\u0F17\u0F1A-\u0F1F\u0F2A-\u0F34\u0F36\u0F38\u0F3A-\u0F3D\u0F48\u0F6D-\u0F70\u0F85\u0F98\u0FBD-\u0FC5\u0FC7-\u0FFF\u104A-\u104F\u109E\u109F\u10C6\u10C8-\u10CC\u10CE\u10CF\u10FB\u1249\u124E\u124F\u1257\u1259\u125E\u125F\u1289\u128E\u128F\u12B1\u12B6\u12B7\u12BF\u12C1\u12C6\u12C7\u12D7\u1311\u1316\u1317\u135B\u135C\u1360-\u137F\u1390-\u139F\u13F6\u13F7\u13FE-\u1400\u166D\u166E\u1680\u169B-\u169F\u16EB-\u16ED\u16F9-\u16FF\u170D\u1715-\u171F\u1735-\u173F\u1754-\u175F\u176D\u1771\u1774-\u177F\u17D4-\u17D6\u17D8-\u17DB\u17DE\u17DF\u17EA-\u180A\u180E\u180F\u181A-\u181F\u1879-\u187F\u18AB-\u18AF\u18F6-\u18FF\u191F\u192C-\u192F\u193C-\u1945\u196E\u196F\u1975-\u197F\u19AC-\u19AF\u19CA-\u19CF\u19DA-\u19FF\u1A1C-\u1A1F\u1A5F\u1A7D\u1A7E\u1A8A-\u1A8F\u1A9A-\u1AA6\u1AA8-\u1AAF\u1AC1-\u1AFF\u1B4C-\u1B4F\u1B5A-\u1B6A\u1B74-\u1B7F\u1BF4-\u1BFF\u1C38-\u1C3F\u1C4A-\u1C4C\u1C7E\u1C7F\u1C89-\u1C8F\u1CBB\u1CBC\u1CC0-\u1CCF\u1CD3\u1CFB-\u1CFF\u1DFA\u1F16\u1F17\u1F1E\u1F1F\u1F46\u1F47\u1F4E\u1F4F\u1F58\u1F5A\u1F5C\u1F5E\u1F7E\u1F7F\u1FB5\u1FBD\u1FBF-\u1FC1\u1FC5\u1FCD-\u1FCF\u1FD4\u1FD5\u1FDC-\u1FDF\u1FED-\u1FF1\u1FF5\u1FFD-\u203E\u2041-\u2053\u2055-\u2070\u2072-\u207E\u2080-\u208F\u209D-\u20CF\u20F1-\u2101\u2103-\u2106\u2108\u2109\u2114\u2116-\u2118\u211E-\u2123\u2125\u2127\u2129\u212E\u213A\u213B\u2140-\u2144\u214A-\u214D\u214F-\u215F\u2189-\u24B5\u24EA-\u2BFF\u2C2F\u2C5F\u2CE5-\u2CEA\u2CF4-\u2CFF\u2D26\u2D28-\u2D2C\u2D2E\u2D2F\u2D68-\u2D6E\u2D70-\u2D7E\u2D97-\u2D9F\u2DA7\u2DAF\u2DB7\u2DBF\u2DC7\u2DCF\u2DD7\u2DDF\u2E00-\u2E2E\u2E30-\u3004\u3008-\u3020\u3030\u3036\u3037\u303D-\u3040\u3097\u3098\u309B\u309C\u30A0\u30FB\u3100-\u3104\u3130\u318F-\u319F\u31C0-\u31EF\u3200-\u33FF\u4DC0-\u4DFF\u9FFD-\u9FFF\uA48D-\uA4CF\uA4FE\uA4FF\uA60D-\uA60F\uA62C-\uA63F\uA673\uA67E\uA6F2-\uA716\uA720\uA721\uA789\uA78A\uA7C0\uA7C1\uA7CB-\uA7F4\uA828-\uA82B\uA82D-\uA83F\uA874-\uA87F\uA8C6-\uA8CF\uA8DA-\uA8DF\uA8F8-\uA8FA\uA8FC\uA92E\uA92F\uA954-\uA95F\uA97D-\uA97F\uA9C1-\uA9CE\uA9DA-\uA9DF\uA9FF\uAA37-\uAA3F\uAA4E\uAA4F\uAA5A-\uAA5F\uAA77-\uAA79\uAAC3-\uAADA\uAADE\uAADF\uAAF0\uAAF1\uAAF7-\uAB00\uAB07\uAB08\uAB0F\uAB10\uAB17-\uAB1F\uAB27\uAB2F\uAB5B\uAB6A-\uAB6F\uABEB\uABEE\uABEF\uABFA-\uABFF\uD7A4-\uD7AF\uD7C7-\uD7CA\uD7FC-\uD7FF\uE000-\uF8FF\uFA6E\uFA6F\uFADA-\uFAFF\uFB07-\uFB12\uFB18-\uFB1C\uFB29\uFB37\uFB3D\uFB3F\uFB42\uFB45\uFBB2-\uFBD2\uFD3E-\uFD4F\uFD90\uFD91\uFDC8-\uFDEF\uFDFC-\uFDFF\uFE10-\uFE1F\uFE30-\uFE32\uFE35-\uFE4C\uFE50-\uFE6F\uFE75\uFEFD-\uFF0F\uFF1A-\uFF20\uFF3B-\uFF3E\uFF40\uFF5B-\uFF65\uFFBF-\uFFC1\uFFC8\uFFC9\uFFD0\uFFD1\uFFD8\uFFD9\uFFDD-\uFFFF]|\uD800[\uDC0C\uDC27\uDC3B\uDC3E\uDC4E\uDC4F\uDC5E-\uDC7F\uDCFB-\uDD3F\uDD75-\uDDFC\uDDFE-\uDE7F\uDE9D-\uDE9F\uDED1-\uDEDF\uDEE1-\uDEFF\uDF20-\uDF2C\uDF4B-\uDF4F\uDF7B-\uDF7F\uDF9E\uDF9F\uDFC4-\uDFC7\uDFD0\uDFD6-\uDFFF]|\uD801[\uDC9E\uDC9F\uDCAA-\uDCAF\uDCD4-\uDCD7\uDCFC-\uDCFF\uDD28-\uDD2F\uDD64-\uDDFF\uDF37-\uDF3F\uDF56-\uDF5F\uDF68-\uDFFF]|\uD802[\uDC06\uDC07\uDC09\uDC36\uDC39-\uDC3B\uDC3D\uDC3E\uDC56-\uDC5F\uDC77-\uDC7F\uDC9F-\uDCDF\uDCF3\uDCF6-\uDCFF\uDD16-\uDD1F\uDD3A-\uDD7F\uDDB8-\uDDBD\uDDC0-\uDDFF\uDE04\uDE07-\uDE0B\uDE14\uDE18\uDE36\uDE37\uDE3B-\uDE3E\uDE40-\uDE5F\uDE7D-\uDE7F\uDE9D-\uDEBF\uDEC8\uDEE7-\uDEFF\uDF36-\uDF3F\uDF56-\uDF5F\uDF73-\uDF7F\uDF92-\uDFFF]|\uD803[\uDC49-\uDC7F\uDCB3-\uDCBF\uDCF3-\uDCFF\uDD28-\uDD2F\uDD3A-\uDE7F\uDEAA\uDEAD-\uDEAF\uDEB2-\uDEFF\uDF1D-\uDF26\uDF28-\uDF2F\uDF51-\uDFAF\uDFC5-\uDFDF\uDFF7-\uDFFF]|\uD804[\uDC47-\uDC65\uDC70-\uDC7E\uDCBB-\uDCCF\uDCE9-\uDCEF\uDCFA-\uDCFF\uDD35\uDD40-\uDD43\uDD48-\uDD4F\uDD74\uDD75\uDD77-\uDD7F\uDDC5-\uDDC8\uDDCD\uDDDB\uDDDD-\uDDFF\uDE12\uDE38-\uDE3D\uDE3F-\uDE7F\uDE87\uDE89\uDE8E\uDE9E\uDEA9-\uDEAF\uDEEB-\uDEEF\uDEFA-\uDEFF\uDF04\uDF0D\uDF0E\uDF11\uDF12\uDF29\uDF31\uDF34\uDF3A\uDF45\uDF46\uDF49\uDF4A\uDF4E\uDF4F\uDF51-\uDF56\uDF58-\uDF5C\uDF64\uDF65\uDF6D-\uDF6F\uDF75-\uDFFF]|\uD805[\uDC4B-\uDC4F\uDC5A-\uDC5D\uDC62-\uDC7F\uDCC6\uDCC8-\uDCCF\uDCDA-\uDD7F\uDDB6\uDDB7\uDDC1-\uDDD7\uDDDE-\uDDFF\uDE41-\uDE43\uDE45-\uDE4F\uDE5A-\uDE7F\uDEB9-\uDEBF\uDECA-\uDEFF\uDF1B\uDF1C\uDF2C-\uDF2F\uDF3A-\uDFFF]|\uD806[\uDC3B-\uDC9F\uDCEA-\uDCFE\uDD07\uDD08\uDD0A\uDD0B\uDD14\uDD17\uDD36\uDD39\uDD3A\uDD44-\uDD4F\uDD5A-\uDD9F\uDDA8\uDDA9\uDDD8\uDDD9\uDDE2\uDDE5-\uDDFF\uDE3F-\uDE46\uDE48-\uDE4F\uDE9A-\uDE9C\uDE9E-\uDEBF\uDEF9-\uDFFF]|\uD807[\uDC09\uDC37\uDC41-\uDC4F\uDC5A-\uDC71\uDC90\uDC91\uDCA8\uDCB7-\uDCFF\uDD07\uDD0A\uDD37-\uDD39\uDD3B\uDD3E\uDD48-\uDD4F\uDD5A-\uDD5F\uDD66\uDD69\uDD8F\uDD92\uDD99-\uDD9F\uDDAA-\uDEDF\uDEF7-\uDFAF\uDFB1-\uDFFF]|\uD808[\uDF9A-\uDFFF]|\uD809[\uDC6F-\uDC7F\uDD44-\uDFFF]|[\uD80A\uD80B\uD80E-\uD810\uD812-\uD819\uD824-\uD82B\uD82D\uD82E\uD830-\uD833\uD837\uD839\uD83D\uD83F\uD87B-\uD87D\uD87F\uD885-\uDB3F\uDB41-\uDBFF][\uDC00-\uDFFF]|\uD80D[\uDC2F-\uDFFF]|\uD811[\uDE47-\uDFFF]|\uD81A[\uDE39-\uDE3F\uDE5F\uDE6A-\uDECF\uDEEE\uDEEF\uDEF5-\uDEFF\uDF37-\uDF3F\uDF44-\uDF4F\uDF5A-\uDF62\uDF78-\uDF7C\uDF90-\uDFFF]|\uD81B[\uDC00-\uDE3F\uDE80-\uDEFF\uDF4B-\uDF4E\uDF88-\uDF8E\uDFA0-\uDFDF\uDFE2\uDFE5-\uDFEF\uDFF2-\uDFFF]|\uD821[\uDFF8-\uDFFF]|\uD823[\uDCD6-\uDCFF\uDD09-\uDFFF]|\uD82C[\uDD1F-\uDD4F\uDD53-\uDD63\uDD68-\uDD6F\uDEFC-\uDFFF]|\uD82F[\uDC6B-\uDC6F\uDC7D-\uDC7F\uDC89-\uDC8F\uDC9A-\uDC9C\uDC9F-\uDFFF]|\uD834[\uDC00-\uDD64\uDD6A-\uDD6C\uDD73-\uDD7A\uDD83\uDD84\uDD8C-\uDDA9\uDDAE-\uDE41\uDE45-\uDFFF]|\uD835[\uDC55\uDC9D\uDCA0\uDCA1\uDCA3\uDCA4\uDCA7\uDCA8\uDCAD\uDCBA\uDCBC\uDCC4\uDD06\uDD0B\uDD0C\uDD15\uDD1D\uDD3A\uDD3F\uDD45\uDD47-\uDD49\uDD51\uDEA6\uDEA7\uDEC1\uDEDB\uDEFB\uDF15\uDF35\uDF4F\uDF6F\uDF89\uDFA9\uDFC3\uDFCC\uDFCD]|\uD836[\uDC00-\uDDFF\uDE37-\uDE3A\uDE6D-\uDE74\uDE76-\uDE83\uDE85-\uDE9A\uDEA0\uDEB0-\uDFFF]|\uD838[\uDC07\uDC19\uDC1A\uDC22\uDC25\uDC2B-\uDCFF\uDD2D-\uDD2F\uDD3E\uDD3F\uDD4A-\uDD4D\uDD4F-\uDEBF\uDEFA-\uDFFF]|\uD83A[\uDCC5-\uDCCF\uDCD7-\uDCFF\uDD4C-\uDD4F\uDD5A-\uDFFF]|\uD83B[\uDC00-\uDDFF\uDE04\uDE20\uDE23\uDE25\uDE26\uDE28\uDE33\uDE38\uDE3A\uDE3C-\uDE41\uDE43-\uDE46\uDE48\uDE4A\uDE4C\uDE50\uDE53\uDE55\uDE56\uDE58\uDE5A\uDE5C\uDE5E\uDE60\uDE63\uDE65\uDE66\uDE6B\uDE73\uDE78\uDE7D\uDE7F\uDE8A\uDE9C-\uDEA0\uDEA4\uDEAA\uDEBC-\uDFFF]|\uD83C[\uDC00-\uDD2F\uDD4A-\uDD4F\uDD6A-\uDD6F\uDD8A-\uDFFF]|\uD83E[\uDC00-\uDFEF\uDFFA-\uDFFF]|\uD869[\uDEDE-\uDEFF]|\uD86D[\uDF35-\uDF3F]|\uD86E[\uDC1E\uDC1F]|\uD873[\uDEA2-\uDEAF]|\uD87A[\uDFE1-\uDFFF]|\uD87E[\uDE1E-\uDFFF]|\uD884[\uDF4B-\uDFFF]|\uDB40[\uDC00-\uDCFF\uDDF0-\uDFFF]/g,l=Object.hasOwnProperty;class r{constructor(){this.occurrences,this.reset()}slug(u,D){var F,e;let r=(F=u,e=!0===D,"string"!=typeof F?"":(e||(F=F.toLowerCase()),F.replace(t,"").replace(/ /g,"-"))),C=r;for(;l.call(this.occurrences,r);)this.occurrences[C]++,r=C+"-"+this.occurrences[C];return this.occurrences[r]=0,r}reset(){this.occurrences=Object.create(null)}}var C=F(94752),A=u=>{var D;let F,e=null!=(D=u.content)?D:u.children;e&&(Array.isArray(e)?F=e:"object"==typeof e&&e&&"content"in e&&Array.isArray(e.content)&&(F=e.content));let t=new r;return(0,C.jsx)(C.Fragment,{children:null==F?void 0:F.map((D,F)=>(0,C.jsx)(E,{node:D,components:u.components,blocks:u.blocks,slugger:t,disableDefaultComponents:u.disableDefaultComponents},F))})},n={a:u=>(0,C.jsx)("a",(0,e.IA)({},u)),p:({children:u})=>(0,C.jsx)("p",{children:u}),b:({children:u})=>(0,C.jsx)("b",{children:u}),highlight:({children:u})=>(0,C.jsx)("span",{children:u}),em:({children:u})=>(0,C.jsx)("em",{children:u}),s:({children:u})=>(0,C.jsx)("s",{children:u}),kbd:({children:u})=>(0,C.jsx)("kbd",{children:u}),code:({children:u})=>(0,C.jsx)("code",{children:u}),ol:u=>{var{children:D}=u,F=(0,e.YG)(u,["children"]);return(0,C.jsx)("ol",(0,e.ko)((0,e.IA)({},F),{children:D}))},ul:u=>{var{children:D}=u,F=(0,e.YG)(u,["children"]);return(0,C.jsx)("ul",(0,e.ko)((0,e.IA)({},F),{children:D}))},li:u=>{var{children:D}=u,F=(0,e.YG)(u,["children"]);return(0,C.jsxs)("li",(0,e.ko)((0,e.IA)({},F["data-is-task-list"]?{style:{display:"flex",alignItems:"baseline"}}:void 0),{children:[F["data-is-task-list"]?(0,C.jsx)("input",{type:"checkbox",defaultChecked:F["data-checked"]}):null,D]}))},h1:u=>{var{children:D}=u,F=(0,e.YG)(u,["children"]);return(0,C.jsx)("h1",(0,e.ko)((0,e.IA)({},F),{children:D}))},h2:u=>{var{children:D}=u,F=(0,e.YG)(u,["children"]);return(0,C.jsx)("h2",(0,e.ko)((0,e.IA)({},F),{children:D}))},h3:u=>{var{children:D}=u,F=(0,e.YG)(u,["children"]);return(0,C.jsx)("h3",(0,e.ko)((0,e.IA)({},F),{children:D}))},h4:u=>{var{children:D}=u,F=(0,e.YG)(u,["children"]);return(0,C.jsx)("h4",(0,e.ko)((0,e.IA)({},F),{children:D}))},h5:u=>{var{children:D}=u,F=(0,e.YG)(u,["children"]);return(0,C.jsx)("h5",(0,e.ko)((0,e.IA)({},F),{children:D}))},h6:u=>{var{children:D}=u,F=(0,e.YG)(u,["children"]);return(0,C.jsx)("h6",(0,e.ko)((0,e.IA)({},F),{children:D}))},hr:()=>(0,C.jsx)("hr",{}),img:u=>{var{caption:D,alt:F}=u,t=(0,e.YG)(u,["caption","alt"]);return(0,C.jsx)("img",(0,e.IA)((0,e.ko)((0,e.IA)({},t),{alt:null!=F?F:D}),D?{"data-caption":D}:{}))},video:u=>{var{caption:D}=u,F=(0,e.YG)(u,["caption"]);return(0,C.jsx)("video",(0,e.IA)((0,e.IA)({controls:!0},F),D?{"data-caption":D}:{}))},blockquote:({children:u})=>(0,C.jsx)("blockquote",{children:u}),pre:({children:u,language:D})=>(0,C.jsx)("pre",{"data-language":D,className:`language-${D}`,children:u}),table:({children:u})=>(0,C.jsx)("table",{children:u}),tr:({children:u})=>(0,C.jsx)("tr",{children:u}),td:({children:u,colspan:D,rowspan:F})=>(0,C.jsx)("td",{colSpan:D,rowSpan:F,children:u}),th:({children:u,colspan:D,rowspan:F})=>(0,C.jsx)("th",{colSpan:D,rowSpan:F,children:u}),thead:({children:u})=>(0,C.jsx)("thead",{children:u}),tbody:({children:u})=>(0,C.jsx)("tbody",{children:u}),tfoot:({children:u})=>(0,C.jsx)("tfoot",{children:u}),br:()=>(0,C.jsx)("br",{}),u:({children:u})=>(0,C.jsx)("u",{children:u})},E=({node:u,components:D,blocks:F,slugger:t,disableDefaultComponents:l})=>{var r,A,s,o,B,i,d,c,v,h,x,b,g,j,p,k,m,f,y,w,I,O,_,G,Y,L,P,q,R,S,H,$;let z,M,N=null==(r=u.content)?void 0:r.map((e,r)=>(0,C.jsx)(E,{node:e,parent:u,components:D,blocks:F,slugger:t,disableDefaultComponents:l},r));switch(u.type){case"paragraph":z=null!=(A=null==D?void 0:D.p)?A:l?()=>(0,C.jsx)(C.Fragment,{}):n.p,M={children:N};break;case"text":let U=[...null!=(s=u.marks)?s:[]];z=({children:u})=>(0,C.jsx)(a,{marks:U,components:D,blocks:F,disableDefaultComponents:l,children:u}),M={children:u.text};break;case"bulletList":case"taskList":z=null!=(o=null==D?void 0:D.ul)?o:l?()=>(0,C.jsx)(C.Fragment,{}):n.ul,M=(0,e.IA)({children:N},"taskList"===u.type?{"data-is-task-list":!0}:{});break;case"orderedList":z=null!=(B=null==D?void 0:D.ol)?B:l?()=>(0,C.jsx)(C.Fragment,{}):n.ol,M={children:N,start:null!=(d=null==(i=u.attrs)?void 0:i.start)?d:1};break;case"listItem":z=null!=(c=null==D?void 0:D.li)?c:l?()=>(0,C.jsx)(C.Fragment,{}):n.li,M={children:N};break;case"taskItem":z=null!=(v=null==D?void 0:D.li)?v:l?()=>(0,C.jsx)(C.Fragment,{}):n.li,M={children:N,"data-is-task-list":!0,"data-checked":null!=(x=null==(h=u.attrs)?void 0:h.checked)&&x};break;case"heading":let Z=`h${u.attrs.level}`;z=null!=(b=null==D?void 0:D[Z])?b:l?()=>(0,C.jsx)(C.Fragment,{}):n[Z],M={children:N,id:t.slug(function u(D){var F;let e="";return null==(F=null==D?void 0:D.content)||F.forEach(D=>{"text"===D.type&&(e+=D.text),"content"in D&&D.content&&(e+=u(D))}),e}(u))};break;case"horizontalRule":z=null!=(g=null==D?void 0:D.hr)?g:l?()=>(0,C.jsx)(C.Fragment,{}):n.hr;break;case"hardBreak":z=null!=(j=null==D?void 0:D.br)?j:l?()=>(0,C.jsx)(C.Fragment,{}):n.br;break;case"blockquote":z=null!=(p=null==D?void 0:D.blockquote)?p:l?()=>(0,C.jsx)(C.Fragment,{}):n.blockquote,M={children:N};break;case"codeBlock":z=null!=(k=null==D?void 0:D.pre)?k:l?()=>(0,C.jsx)(C.Fragment,{}):n.pre;let J=null!=(f=null==(m=u.content)?void 0:m[0].text)?f:"";M={children:N,language:null!=(w=null==(y=u.attrs)?void 0:y.language)?w:"text",code:J};break;case"table":z=null!=(I=null==D?void 0:D.table)?I:l?()=>(0,C.jsx)(C.Fragment,{}):n.table,M={children:(0,C.jsx)(E,{node:{type:"tableBody",content:u.content},parent:u,components:D,blocks:F,slugger:t,disableDefaultComponents:l})};break;case"tableRow":z=null!=(O=null==D?void 0:D.tr)?O:l?()=>(0,C.jsx)(C.Fragment,{}):n.tr,M={children:N};break;case"tableCell":z=null!=(_=null==D?void 0:D.td)?_:l?()=>(0,C.jsx)(C.Fragment,{}):n.td,M={children:N,colspan:u.attrs.colspan,rowspan:u.attrs.rowspan,colwidth:null!=(G=u.attrs.colwidth)?G:void 0};break;case"tableHeader":z=null!=(Y=null==D?void 0:D.th)?Y:l?()=>(0,C.jsx)(C.Fragment,{}):n.th,M={children:N,colspan:u.attrs.colspan,rowspan:u.attrs.rowspan,colwidth:null!=(L=u.attrs.colwidth)?L:void 0};break;case"tableFooter":z=null!=(P=null==D?void 0:D.tfoot)?P:l?()=>(0,C.jsx)(C.Fragment,{}):n.tfoot,M={children:N};break;case"tableBody":z=null!=(q=null==D?void 0:D.tbody)?q:l?()=>(0,C.jsx)(C.Fragment,{}):n.tbody,M={children:N};break;case"image":z=null!=(R=null==D?void 0:D.img)?R:l?()=>(0,C.jsx)(C.Fragment,{}):n.img,M={src:u.attrs.src,width:u.attrs.width,height:u.attrs.height,alt:u.attrs.alt,caption:u.attrs.caption};break;case"video":z=null!=(S=null==D?void 0:D.video)?S:l?()=>(0,C.jsx)(C.Fragment,{}):n.video,M={children:N,src:u.attrs.src,width:u.attrs.width,height:u.attrs.height,caption:u.attrs.caption};break;case"basehub-block":{let e=null==F?void 0:F.find(D=>{var F,e;let t=null==D?void 0:D.__typename,l=Object.keys(D).length,r=null!=(e=null==D?void 0:D._id)?e:null==(F=null==D?void 0:D._sys)?void 0:F.id;return("string"==typeof r||!!t&&!(l>1))&&r===u.attrs.id});if(!e)break;z=null!=(H=null==D?void 0:D[null==e?void 0:e.__typename])?H:()=>(0,C.jsx)(C.Fragment,{}),M=e;break}default:z=null!=($=null==D?void 0:D[u.type])?$:()=>(0,C.jsx)(C.Fragment,{}),M=(0,e.ko)((0,e.IA)({},u.attrs),{children:N})}return z?(0,C.jsx)(z,(0,e.IA)({},M)):(0,C.jsx)(C.Fragment,{})},a=({marks:u,children:D,components:F,blocks:t,disableDefaultComponents:l})=>{var r,A,E,s,o,B,i,d,c,v,h,x,b,g;let j,p;if(!u)return(0,C.jsx)(C.Fragment,{children:D});let k=[...u],m=k.pop();if(!m)return(0,C.jsx)(C.Fragment,{children:D});switch(m.type){case"bold":j=null!=(r=null==F?void 0:F.b)?r:l?()=>(0,C.jsx)(C.Fragment,{}):n.b,p={children:D};break;case"highlight":j=null!=(A=null==F?void 0:F.highlight)?A:l?()=>(0,C.jsx)(C.Fragment,{}):n.highlight,p={children:D};break;case"italic":j=null!=(E=null==F?void 0:F.em)?E:l?()=>(0,C.jsx)(C.Fragment,{}):n.em,p={children:D};break;case"strike":j=null!=(s=null==F?void 0:F.s)?s:l?()=>(0,C.jsx)(C.Fragment,{}):n.s,p={children:D};break;case"kbd":j=null!=(o=null==F?void 0:F.kbd)?o:l?()=>(0,C.jsx)(C.Fragment,{}):n.kbd,p={children:D};break;case"code":j=null!=(B=null==F?void 0:F.code)?B:l?()=>(0,C.jsx)(C.Fragment,{}):n.code,p={children:D};break;case"underline":j=null!=(i=null==F?void 0:F.u)?i:l?()=>(0,C.jsx)(C.Fragment,{}):n.u,p={children:D};break;case"link":if("internal"===m.attrs.type){let u=null==t?void 0:t.find(u=>{var D,F;let e=null==u?void 0:u.__typename,t=Object.keys(u).length,l=null!=(F=null==u?void 0:u._id)?F:null==(D=null==u?void 0:u._sys)?void 0:D.id;return("string"==typeof l||!!e&&!(t>1))&&l===m.attrs.targetId});if(!u){p={children:D,href:"#",target:null!=(d=m.attrs.target)?d:void 0,internal:void 0};break}p={children:D,href:"#",target:null!=(c=m.attrs.target)?c:void 0,internal:u}}else p={children:D,href:m.attrs.href,target:null!=(v=m.attrs.target)?v:void 0,internal:void 0,rel:(null==(h=m.attrs.target)?void 0:h.toLowerCase())==="_blank"?"noopener noreferrer":void 0};j=null!=(x=null==F?void 0:F.a)?x:l?()=>(0,C.jsx)(C.Fragment,{}):n.a;break;case"basehub-inline-block":{let u=null==t?void 0:t.find(u=>{var D,F;let e=null==u?void 0:u.__typename,t=Object.keys(u).length,l=null!=(F=null==u?void 0:u._id)?F:null==(D=null==u?void 0:u._sys)?void 0:D.id;return("string"==typeof l||!!e&&!(t>1))&&l===m.attrs.id});if(!u)break;j=null!=(b=null==F?void 0:F[(null==u?void 0:u.__typename)+"_mark"])?b:()=>(0,C.jsx)(C.Fragment,{children:D}),p=(0,e.ko)((0,e.IA)({},u),{children:D});break}default:j=null!=(g=null==F?void 0:F[m.type])?g:()=>(0,C.jsx)(C.Fragment,{}),p=(0,e.ko)((0,e.IA)({},m.attrs),{children:D})}return j?(0,C.jsx)(a,{marks:k,components:F,blocks:t,disableDefaultComponents:l,children:(0,C.jsx)(j,(0,e.IA)({},p))}):(0,C.jsx)(C.Fragment,{})}},66577:(u,D,F)=>{F.d(D,{BU:()=>o,IA:()=>E,YG:()=>s,ko:()=>a});var e=Object.defineProperty,t=Object.defineProperties,l=Object.getOwnPropertyDescriptors,r=Object.getOwnPropertySymbols,C=Object.prototype.hasOwnProperty,A=Object.prototype.propertyIsEnumerable,n=(u,D,F)=>D in u?e(u,D,{enumerable:!0,configurable:!0,writable:!0,value:F}):u[D]=F,E=(u,D)=>{for(var F in D||(D={}))C.call(D,F)&&n(u,F,D[F]);if(r)for(var F of r(D))A.call(D,F)&&n(u,F,D[F]);return u},a=(u,D)=>t(u,l(D)),s=(u,D)=>{var F={};for(var e in u)C.call(u,e)&&0>D.indexOf(e)&&(F[e]=u[e]);if(null!=u&&r)for(var e of r(u))0>D.indexOf(e)&&A.call(u,e)&&(F[e]=u[e]);return F},o=(u,D,F)=>new Promise((e,t)=>{var l=u=>{try{C(F.next(u))}catch(u){t(u)}},r=u=>{try{C(F.throw(u))}catch(u){t(u)}},C=u=>u.done?e(u.value):Promise.resolve(u.value).then(l,r);C((F=F.apply(u,D)).next())})},71844:(u,D,F)=>{F.d(D,{A60:()=>l});var e=F(23233),t=["color"],l=(0,e.forwardRef)(function(u,D){var F=u.color,l=function(u,D){if(null==u)return{};var F,e,t={},l=Object.keys(u);for(e=0;e<l.length;e++)F=l[e],D.indexOf(F)>=0||(t[F]=u[F]);return t}(u,t);return(0,e.createElement)("svg",Object.assign({width:"15",height:"15",viewBox:"0 0 15 15",fill:"none",xmlns:"http://www.w3.org/2000/svg"},l,{ref:D}),(0,e.createElement)("path",{d:"M6.85355 3.14645C7.04882 3.34171 7.04882 3.65829 6.85355 3.85355L3.70711 7H12.5C12.7761 7 13 7.22386 13 7.5C13 7.77614 12.7761 8 12.5 8H3.70711L6.85355 11.1464C7.04882 11.3417 7.04882 11.6583 6.85355 11.8536C6.65829 12.0488 6.34171 12.0488 6.14645 11.8536L2.14645 7.85355C1.95118 7.65829 1.95118 7.34171 2.14645 7.14645L6.14645 3.14645C6.34171 2.95118 6.65829 2.95118 6.85355 3.14645Z",fill:void 0===F?"currentColor":F,fillRule:"evenodd",clipRule:"evenodd"}))})}};