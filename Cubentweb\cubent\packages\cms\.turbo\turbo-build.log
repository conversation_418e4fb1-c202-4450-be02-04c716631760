[?25l[2J[m[2;1H> @repo/cms@0.0.0 build C:\Users\<USER>\Documents\2 FOLDERS FOR CUBENT\Cubentweb\cubent\packages\cms
> basehub build[5;1H]0;C:\Windows\system32\cmd.exe[?25h🪄 Generating...
┌─────────────────────────────────────────────────────────────────────────────────────────────────┐
│ 🎫 SDK Version: 8.2.7 (build id: bshb_sdk_e7721e50122f7)                                        │
│ 🔗 Endpoint: https://api.basehub.com/graphql                                                    │
│ 🔵 Draft: disabled                                                                              │
│ 📦 Output: C:\Users\<USER>\Documents\2 FOLDERS FOR CUBENT\Cubentweb\cubent\packages\cms\.basehub │
│ 🔀 Ref: main (basehub branch)                                                                   │
└─────────────────────────────────────────────────────────────────────────────────────────────────┘
[2m◼[22m generating the client in `C:\Users\<USER>\Documents\2 FOLDERS FOR CUBENT\Cubentweb\cubent\packages\cms\.basehub`[?25l
[33m[10;1H⠋ [mgenerating the client in `C:\Users\<USER>\Documents\2 FOLDERS FOR CUBENT\Cubentweb\cubent\packages\cms\.basehub`                    
[133C[33m[10;1H❯ [mgenerating the client in `C:\Users\<USER>\Documents\2 FOLDERS FOR CUBENT\Cubentweb\cubent\packages\cms\.basehub`[K
  [33m⠙ [mfetching schema from BaseHub's API[K[95C
  [2m◼[22m preparing client directory[K[103C
  [2m◼[22m writing files[K[116C
[133C[33m[7;1H❯ [mgenerating the client in `C:\Users\<USER>\Documents\2 FOLDERS FOR CUBENT\Cubentweb\cubent\packages\cms\.basehub`[K
  [33m⠹ [mfetching schema from BaseHub's API[K
  [2m◼[22m preparing client directory[K
  [2m◼[22m writing files[K
[K[133C[33m[7;1H❯ [mgenerating the client in `C:\Users\<USER>\Documents\2 FOLDERS FOR CUBENT\Cubentweb\cubent\packages\cms\.basehub`[K
  [33m⠸ [mfetching schema from BaseHub's API[K
  [2m◼[22m preparing client directory[K
  [2m◼[22m writing files[K
[K[133C[33m[7;1H❯ [mgenerating the client in `C:\Users\<USER>\Documents\2 FOLDERS FOR CUBENT\Cubentweb\cubent\packages\cms\.basehub`[K
  [32m✔ [mfetching schema from BaseHub's API[K
  [32m✔ [mpreparing client directory[K
  [33m❯ [mwriting files[K
    [33m❯ [mwriting schema.graphql[K[105C
      [33m⠼ [mcopy runtime files[K[107C
      [33m⠼ [mwriting schema.ts[K[108C
      [33m⠼ [mwriting types map[K[108C
      [33m⠼ [mwriting index.ts[K[109C
[133C[32m[2;1H✔ [mgenerating the client in `C:\Users\<USER>\Documents\2 FOLDERS FOR CUBENT\Cubentweb\cubent\packages\cms\.basehub`[K
📦 Compiling to JavaScript...[K
[K
[K
[K
[K
[K
[K
[K
[K[4;1H[?25h🪄 Generated `basehub` client in 3493ms
