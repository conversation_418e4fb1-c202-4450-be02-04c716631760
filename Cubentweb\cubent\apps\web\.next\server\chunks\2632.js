"use strict";exports.id=2632,exports.ids=[1726,2632],exports.modules={2632:(e,n,t)=>{t.r(n),t.d(n,{default:()=>d});var a=t(63069),s=t(98013),i=t(91726),c=t(6028),l=t(47350),r=t(42204),u=t(14055);let o=Object.freeze({displayName:"Pug",name:"pug",patterns:[{comment:"Doctype declaration.",match:"^(!!!|doctype)(\\s*[a-zA-Z0-9-_]+)?",name:"meta.tag.sgml.doctype.html"},{begin:"^(\\s*)//-",comment:"Unbuffered (pug-only) comments.",end:"^(?!(\\1\\s)|\\s*$)",name:"comment.unbuffered.block.pug"},{begin:"^(\\s*)//",comment:"Buffered (html) comments.",end:"^(?!(\\1\\s)|\\s*$)",name:"string.comment.buffered.block.pug",patterns:[{captures:{1:{name:"invalid.illegal.comment.comment.block.pug"}},comment:"Buffered comments inside buffered comments will generate invalid html.",match:"^\\s*(//)(?!-)",name:"string.comment.buffered.block.pug"}]},{begin:"\x3c!--",end:"--\\s*>",name:"comment.unbuffered.block.pug",patterns:[{match:"--",name:"invalid.illegal.comment.comment.block.pug"}]},{begin:"^(\\s*)-$",comment:"Unbuffered code block.",end:"^(?!(\\1\\s)|\\s*$)",name:"source.js",patterns:[{include:"source.js"}]},{begin:"^(\\s*)(script)((\\.$)|(?=[^\\n]*((text|application)/javascript|module).*\\.$))",beginCaptures:{2:{name:"entity.name.tag.pug"}},comment:"Script tag with JavaScript code.",end:"^(?!(\\1\\s)|\\s*$)",name:"meta.tag.other",patterns:[{begin:"\\G(?=\\()",end:"$",patterns:[{include:"#tag_attributes"}]},{begin:"\\G(?=[.#])",end:"$",patterns:[{include:"#complete_tag"}]},{include:"source.js"}]},{begin:"^(\\s*)(style)((\\.$)|(?=[.#(].*\\.$))",beginCaptures:{2:{name:"entity.name.tag.pug"}},comment:"Style tag with CSS code.",end:"^(?!(\\1\\s)|\\s*$)",name:"meta.tag.other",patterns:[{begin:"\\G(?=\\()",end:"$",patterns:[{include:"#tag_attributes"}]},{begin:"\\G(?=[.#])",end:"$",patterns:[{include:"#complete_tag"}]},{include:"source.css"}]},{begin:"^(\\s*):(sass)(?=\\(|$)",beginCaptures:{2:{name:"constant.language.name.sass.filter.pug"}},end:"^(?!(\\1\\s)|\\s*$)",name:"source.sass.filter.pug",patterns:[{include:"#tag_attributes"},{include:"source.sass"}]},{begin:"^(\\s*):(scss)(?=\\(|$)",beginCaptures:{2:{name:"constant.language.name.scss.filter.pug"}},end:"^(?!(\\1\\s)|\\s*$)",name:"source.css.scss.filter.pug",patterns:[{include:"#tag_attributes"},{include:"source.css.scss"}]},{begin:"^(\\s*):(less)(?=\\(|$)",beginCaptures:{2:{name:"constant.language.name.less.filter.pug"}},end:"^(?!(\\1\\s)|\\s*$)",name:"source.less.filter.pug",patterns:[{include:"#tag_attributes"},{include:"source.less"}]},{begin:"^(\\s*):(stylus)(?=\\(|$)",beginCaptures:{2:{name:"constant.language.name.stylus.filter.pug"}},end:"^(?!(\\1\\s)|\\s*$)",patterns:[{include:"#tag_attributes"},{include:"source.stylus"}]},{begin:"^(\\s*):(coffee(-?script)?)(?=\\(|$)",beginCaptures:{2:{name:"constant.language.name.coffeescript.filter.pug"}},end:"^(?!(\\1\\s)|\\s*$)",name:"source.coffeescript.filter.pug",patterns:[{include:"#tag_attributes"},{include:"source.coffee"}]},{begin:"^(\\s*):(uglify-js)(?=\\(|$)",beginCaptures:{2:{name:"constant.language.name.js.filter.pug"}},end:"^(?!(\\1\\s)|\\s*$)",name:"source.js.filter.pug",patterns:[{include:"#tag_attributes"},{include:"source.js"}]},{begin:"^(\\s*)((:(?=.))|(:$))",beginCaptures:{4:{name:"invalid.illegal.empty.generic.filter.pug"}},comment:"Generic Pug filter.",end:"^(?!(\\1\\s)|\\s*$)",patterns:[{begin:"\\G(?<=:)(?=.)",end:"$",name:"name.generic.filter.pug",patterns:[{match:"\\G\\(",name:"invalid.illegal.name.generic.filter.pug"},{match:"[\\w-]",name:"constant.language.name.generic.filter.pug"},{include:"#tag_attributes"},{match:"\\W",name:"invalid.illegal.name.generic.filter.pug"}]}]},{begin:"^(\\s*)(?:(?=\\.$)|(?:(?=[\\w.#].*?\\.$)(?=(?:(?:(?:(?:(?:#[\\w-]+)|(?:\\.[\\w-]+))|(?:(?:[#!]\\{[^}]*\\})|(?:\\w(?:(?:[\\w:-]+[\\w-])|(?:[\\w-]*)))))(?:(?:#[\\w-]+)|(?:\\.[\\w-]+)|(?:\\((?:[^()\\'\\\"]*(?:(?:\\'(?:[^\\']|(?:(?<!\\\\)\\\\\\'))*\\')|(?:\\\"(?:[^\\\"]|(?:(?<!\\\\)\\\\\\\"))*\\\")))*[^()]*\\))*)*)(?:(?:(?::\\s+)|(?<=\\)))(?:(?:(?:(?:#[\\w-]+)|(?:\\.[\\w-]+))|(?:(?:[#!]\\{[^}]*\\})|(?:\\w(?:(?:[\\w:-]+[\\w-])|(?:[\\w-]*)))))(?:(?:#[\\w-]+)|(?:\\.[\\w-]+)|(?:\\((?:[^()\\'\\\"]*(?:(?:\\'(?:[^\\']|(?:(?<!\\\\)\\\\\\'))*\\')|(?:\\\"(?:[^\\\"]|(?:(?<!\\\\)\\\\\\\"))*\\\")))*[^()]*\\))*)*))*)\\.$)(?:(?:(#[\\w-]+)|(\\.[\\w-]+))|((?:[#!]\\{[^}]*\\})|(?:\\w(?:(?:[\\w:-]+[\\w-])|(?:[\\w-]*)))))))",beginCaptures:{2:{name:"meta.selector.css entity.other.attribute-name.id.css.pug"},3:{name:"meta.selector.css entity.other.attribute-name.class.css.pug"},4:{name:"meta.tag.other entity.name.tag.pug"}},comment:"Generated from dot_block_tag.py",end:"^(?!(\\1\\s)|\\s*$)",patterns:[{match:"\\.$",name:"storage.type.function.pug.dot-block-dot"},{include:"#tag_attributes"},{include:"#complete_tag"},{begin:"^(?=.)",end:"$",name:"text.block.pug",patterns:[{include:"#inline_pug"},{include:"#embedded_html"},{include:"#html_entity"},{include:"#interpolated_value"},{include:"#interpolated_error"}]}]},{begin:"^\\s*",comment:"All constructs that generally span a single line starting with any number of white-spaces.",end:"$",patterns:[{include:"#inline_pug"},{include:"#blocks_and_includes"},{include:"#unbuffered_code"},{include:"#mixin_definition"},{include:"#mixin_call"},{include:"#flow_control"},{include:"#flow_control_each"},{include:"#case_conds"},{begin:"\\|",comment:"Tag pipe text line.",end:"$",name:"text.block.pipe.pug",patterns:[{include:"#inline_pug"},{include:"#embedded_html"},{include:"#html_entity"},{include:"#interpolated_value"},{include:"#interpolated_error"}]},{include:"#printed_expression"},{begin:"\\G(?=(#[^{\\w-])|[^\\w.#])",comment:"Line starting with characters incompatible with tag name/id/class is standalone text.",end:"$",patterns:[{begin:"</?(?=[!#])",end:">|$",patterns:[{include:"#inline_pug"},{include:"#interpolated_value"},{include:"#interpolated_error"}]},{include:"#inline_pug"},{include:"#embedded_html"},{include:"#html_entity"},{include:"#interpolated_value"},{include:"#interpolated_error"}]},{include:"#complete_tag"}]}],repository:{babel_parens:{begin:"\\(",end:"\\)|(({\\s*)?$)",patterns:[{include:"#babel_parens"},{include:"source.js"}]},blocks_and_includes:{captures:{1:{name:"storage.type.import.include.pug"},4:{name:"variable.control.import.include.pug"}},comment:"Template blocks and includes.",match:"(extends|include|yield|append|prepend|block( (append|prepend))?)\\s+(.*)$",name:"meta.first-class.pug"},case_conds:{begin:"(default|when)((\\s+|(?=:))|$)",captures:{1:{name:"storage.type.function.pug"}},comment:"Pug case conditionals.",end:"$",name:"meta.control.flow.pug",patterns:[{begin:"\\G(?!:)",end:"(?=:\\s+)|$",name:"js.embedded.control.flow.pug",patterns:[{include:"#case_when_paren"},{include:"source.js"}]},{begin:":\\s+",end:"$",name:"tag.case.control.flow.pug",patterns:[{include:"#complete_tag"}]}]},case_when_paren:{begin:"\\(",end:"\\)",name:"js.when.control.flow.pug",patterns:[{include:"#case_when_paren"},{match:":",name:"invalid.illegal.name.tag.pug"},{include:"source.js"}]},complete_tag:{begin:"(?=[\\w.#])|(:\\s*)",end:"(\\.?$)|(?=:.)",endCaptures:{1:{name:"storage.type.function.pug.dot-block-dot"}},patterns:[{include:"#blocks_and_includes"},{include:"#unbuffered_code"},{include:"#mixin_call"},{include:"#flow_control"},{include:"#flow_control_each"},{match:"(?<=:)\\w.*$",name:"invalid.illegal.name.tag.pug"},{include:"#tag_name"},{include:"#tag_id"},{include:"#tag_classes"},{include:"#tag_attributes"},{include:"#tag_mixin_attributes"},{captures:{2:{name:"invalid.illegal.end.tag.pug"},4:{name:"invalid.illegal.end.tag.pug"}},match:"((\\.)\\s+$)|((:)\\s*$)"},{include:"#printed_expression"},{include:"#tag_text"}]},embedded_html:{begin:"(?=<[^>]*>)",end:"$|(?=>)",name:"html",patterns:[{include:"text.html.basic"},{include:"#interpolated_value"},{include:"#interpolated_error"}]},flow_control:{begin:"(for|if|else if|else|until|while|unless|case)(\\s+|$)",captures:{1:{name:"storage.type.function.pug"}},comment:"Pug control flow.",end:"$",name:"meta.control.flow.pug",patterns:[{begin:"",end:"$",name:"js.embedded.control.flow.pug",patterns:[{include:"source.js"}]}]},flow_control_each:{begin:"(each)(\\s+|$)",captures:{1:{name:"storage.type.function.pug"}},end:"$",name:"meta.control.flow.pug.each",patterns:[{match:"([\\w$_]+)(?:\\s*,\\s*([\\w$_]+))?",name:"variable.other.pug.each-var"},{begin:"",end:"$",name:"js.embedded.control.flow.pug",patterns:[{include:"source.js"}]}]},html_entity:{patterns:[{match:"(&)([a-zA-Z0-9]+|#\\d+|#x[0-9a-fA-F]+)(;)",name:"constant.character.entity.html.text.pug"},{match:"[<>&]",name:"invalid.illegal.html_entity.text.pug"}]},inline_pug:{begin:"(?<!\\\\)(#\\[)",captures:{1:{name:"entity.name.function.pug"},2:{name:"entity.name.function.pug"}},end:"(\\])",name:"inline.pug",patterns:[{include:"#inline_pug"},{include:"#mixin_call"},{begin:"(?<!\\])(?=[\\w.#])|(:\\s*)",end:"(?=\\]|(:.)|=|\\s)",name:"tag.inline.pug",patterns:[{include:"#tag_name"},{include:"#tag_id"},{include:"#tag_classes"},{include:"#tag_attributes"},{include:"#tag_mixin_attributes"},{include:"#inline_pug"},{match:"\\[",name:"invalid.illegal.tag.pug"}]},{include:"#unbuffered_code"},{include:"#printed_expression"},{match:"\\[",name:"invalid.illegal.tag.pug"},{include:"#inline_pug_text"}]},inline_pug_text:{begin:"",end:"(?=\\])",patterns:[{begin:"\\[",end:"\\]",patterns:[{include:"#inline_pug_text"}]},{include:"#inline_pug"},{include:"#embedded_html"},{include:"#html_entity"},{include:"#interpolated_value"},{include:"#interpolated_error"}]},interpolated_error:{match:"(?<!\\\\)[#!]\\{(?=[^}]*$)",name:"invalid.illegal.tag.pug"},interpolated_value:{begin:"(?<!\\\\)[#!]\\{(?=.*?\\})",end:"\\}",name:"string.interpolated.pug",patterns:[{match:"{",name:"invalid.illegal.tag.pug"},{include:"source.js"}]},js_braces:{begin:"\\{",end:"\\}",patterns:[{include:"#js_braces"},{include:"source.js"}]},js_brackets:{begin:"\\[",end:"\\]",patterns:[{include:"#js_brackets"},{include:"source.js"}]},js_parens:{begin:"\\(",end:"\\)",patterns:[{include:"#js_parens"},{include:"source.js"}]},mixin_call:{begin:"((?:mixin\\s+)|\\+)([\\w-]+)",beginCaptures:{1:{name:"storage.type.function.pug"},2:{name:"meta.tag.other entity.name.function.pug"}},end:"(?!\\()|$",patterns:[{begin:"(?<!\\))\\(",end:"\\)",name:"args.mixin.pug",patterns:[{include:"#js_parens"},{captures:{1:{name:"meta.tag.other entity.other.attribute-name.tag.pug"}},match:"([^\\s(),=/]+)\\s*=\\s*"},{include:"source.js"}]},{include:"#tag_attributes"}]},mixin_definition:{captures:{1:{name:"storage.type.function.pug"},2:{name:"meta.tag.other entity.name.function.pug"},3:{name:"punctuation.definition.parameters.begin.js"},4:{name:"variable.parameter.function.js"},5:{name:"punctuation.definition.parameters.begin.js"}},match:"(mixin\\s+)([\\w-]+)(?:(\\()\\s*((?:[a-zA-Z_]\\w*\\s*)(?:,\\s*[a-zA-Z_]\\w*\\s*)*)(\\)))?$"},printed_expression:{begin:"(!?=)\\s*",captures:{1:{name:"constant"}},end:"(?=\\])|$",name:"source.js",patterns:[{include:"#js_brackets"},{include:"source.js"}]},tag_attribute_name:{captures:{1:{name:"entity.other.attribute-name.tag.pug"}},match:"([^\\s(),=/!]+)\\s*"},tag_attribute_name_paren:{begin:"\\(\\s*",end:"\\)",name:"entity.other.attribute-name.tag.pug",patterns:[{include:"#tag_attribute_name_paren"},{include:"#tag_attribute_name"}]},tag_attributes:{begin:"(\\(\\s*)",captures:{1:{name:"constant.name.attribute.tag.pug"}},end:"(\\))",name:"meta.tag.other",patterns:[{include:"#tag_attribute_name_paren"},{include:"#tag_attribute_name"},{match:"!(?!=)",name:"invalid.illegal.tag.pug"},{begin:"=\\s*",end:"$|(?=,|(?:\\s+[^!%&*\\-+~|<>?/])|\\))",name:"attribute_value",patterns:[{include:"#js_parens"},{include:"#js_brackets"},{include:"#js_braces"},{include:"source.js"}]},{begin:"(?<=[%&*\\-+~|<>:?/])\\s+",end:"$|(?=,|(?:\\s+[^!%&*\\-+~|<>?/])|\\))",name:"attribute_value2",patterns:[{include:"#js_parens"},{include:"#js_brackets"},{include:"#js_braces"},{include:"source.js"}]}]},tag_classes:{captures:{1:{name:"invalid.illegal.tag.pug"}},match:"\\.([^\\w-])?[\\w-]*",name:"meta.selector.css entity.other.attribute-name.class.css.pug"},tag_id:{match:"#[\\w-]+",name:"meta.selector.css entity.other.attribute-name.id.css.pug"},tag_mixin_attributes:{begin:"(&attributes\\()",captures:{1:{name:"entity.name.function.pug"}},end:"(\\))",name:"meta.tag.other",patterns:[{match:"attributes(?=\\))",name:"storage.type.keyword.pug"},{include:"source.js"}]},tag_name:{begin:"([#!]\\{(?=.*?\\}))|(\\w(([\\w:-]+[\\w-])|([\\w-]*)))",end:"(\\G(?<!\\5[^\\w-]))|\\}|$",name:"meta.tag.other entity.name.tag.pug",patterns:[{begin:"\\G(?<=\\{)",end:"(?=\\})",name:"meta.tag.other entity.name.tag.pug",patterns:[{match:"{",name:"invalid.illegal.tag.pug"},{include:"source.js"}]}]},tag_text:{begin:"(?=.)",end:"$",patterns:[{include:"#inline_pug"},{include:"#embedded_html"},{include:"#html_entity"},{include:"#interpolated_value"},{include:"#interpolated_error"}]},unbuffered_code:{begin:"(-|((\\w+)\\s+=))",beginCaptures:{3:{name:"variable.parameter.javascript.embedded.pug"}},comment:"name = function() {}",end:"(?=\\])|(({\\s*)?$)",name:"source.js",patterns:[{include:"#js_brackets"},{include:"#babel_parens"},{include:"source.js"}]}},scopeName:"text.pug",embeddedLangs:["javascript","css","sass","scss","stylus","coffee","html"],aliases:["jade"]});var d=[...a.default,...s.default,...i.default,...c.default,...l.default,...r.default,...u.default,o]},91726:(e,n,t)=>{t.r(n),t.d(n,{default:()=>a});var a=[Object.freeze({displayName:"Sass",fileTypes:["sass"],foldingStartMarker:"/\\*|^#|^\\*|^\\b|*#?region|^\\.",foldingStopMarker:"\\*/|*#?endregion|^\\s*$",name:"sass",patterns:[{begin:"^(\\s*)(/\\*)",end:"(\\*/)|^(?!\\s\\1)",name:"comment.block.sass",patterns:[{include:"#comment-tag"},{include:"#comment-param"}]},{match:"^[\\t ]*/?//[\\t ]*[SRI][\\t ]*$",name:"keyword.other.sass.formatter.action"},{begin:"^[\\t ]*//[\\t ]*(import)[\\t ]*(css-variables)[\\t ]*(from)",captures:{1:{name:"keyword.control"},2:{name:"variable"},3:{name:"keyword.control"}},end:"$\\n?",name:"comment.import.css.variables",patterns:[{include:"#import-quotes"}]},{include:"#double-slash"},{include:"#double-quoted"},{include:"#single-quoted"},{include:"#interpolation"},{include:"#curly-brackets"},{include:"#placeholder-selector"},{begin:"\\$[a-zA-Z0-9_-]+(?=:)",captures:{0:{name:"variable.other.name"}},end:"$\\n?|(?=\\)\\s\\)|\\)\\n)",name:"sass.script.maps",patterns:[{include:"#double-slash"},{include:"#double-quoted"},{include:"#single-quoted"},{include:"#interpolation"},{include:"#variable"},{include:"#rgb-value"},{include:"#numeric"},{include:"#unit"},{include:"#flag"},{include:"#comma"},{include:"#function"},{include:"#function-content"},{include:"#operator"},{include:"#reserved-words"},{include:"#parent-selector"},{include:"#property-value"},{include:"#semicolon"},{include:"#dotdotdot"}]},{include:"#variable-root"},{include:"#numeric"},{include:"#unit"},{include:"#flag"},{include:"#comma"},{include:"#semicolon"},{include:"#dotdotdot"},{begin:"@include|\\+(?!\\W|\\d)",captures:{0:{name:"keyword.control.at-rule.css.sass"}},end:"(?=\\n|\\()",name:"support.function.name.sass.library"},{begin:"^(@use)",captures:{0:{name:"keyword.control.at-rule.css.sass.use"}},end:"(?=\\n)",name:"sass.use",patterns:[{match:"as|with",name:"support.type.css.sass"},{include:"#numeric"},{include:"#unit"},{include:"#variable-root"},{include:"#rgb-value"},{include:"#comma"},{include:"#parenthesis-open"},{include:"#parenthesis-close"},{include:"#colon"},{include:"#import-quotes"}]},{begin:"^@import(.*?)( as.*)?$",captures:{1:{name:"constant.character.css.sass"},2:{name:"invalid"}},end:"(?=\\n)",name:"keyword.control.at-rule.use"},{begin:"@mixin|^[\\t ]*=|@function",captures:{0:{name:"keyword.control.at-rule.css.sass"}},end:"$\\n?|(?=\\()",name:"support.function.name.sass",patterns:[{match:"[\\w-]+",name:"entity.name.function"}]},{begin:"@",end:"$\\n?|\\s(?!(all|braille|embossed|handheld|print|projection|screen|speech|tty|tv|if|only|not)(\\s|,))",name:"keyword.control.at-rule.css.sass"},{begin:"(?<!-|\\()\\b(a|abbr|acronym|address|applet|area|article|aside|audio|b|base|big|blockquote|body|br|button|canvas|caption|cite|code|col|colgroup|datalist|dd|del|details|dfn|dialog|div|dl|dt|em|embed|eventsource|fieldset|figure|figcaption|footer|form|frame|frameset|(h[1-6])|head|header|hgroup|hr|html|i|iframe|img|input|ins|kbd|label|legend|li|link|map|mark|menu|meta|meter|nav|noframes|noscript|object|ol|optgroup|option|output|p|param|picture|pre|progress|q|samp|script|section|select|small|source|span|strike|strong|style|sub|summary|sup|table|tbody|td|textarea|tfoot|th|thead|time|title|tr|tt|ul|var|video|main|svg|rect|ruby|center|circle|ellipse|line|polyline|polygon|path|text|u|slot)\\b(?!-|\\)|:\\s)|&",end:"$\\n?|(?=\\s|,|\\(|\\)|\\.|\\#|\\[|>|-|_)",name:"entity.name.tag.css.sass.symbol",patterns:[{include:"#interpolation"},{include:"#pseudo-class"}]},{begin:"#",end:"$\\n?|(?=\\s|,|\\(|\\)|\\.|\\[|>)",name:"entity.other.attribute-name.id.css.sass",patterns:[{include:"#interpolation"},{include:"#pseudo-class"}]},{begin:"\\.|(?<=&)(-|_)",end:"$\\n?|(?=\\s|,|\\(|\\)|\\[|>)",name:"entity.other.attribute-name.class.css.sass",patterns:[{include:"#interpolation"},{include:"#pseudo-class"}]},{begin:"\\[",end:"\\]",name:"entity.other.attribute-selector.sass",patterns:[{include:"#double-quoted"},{include:"#single-quoted"},{match:"\\^|\\$|\\*|~",name:"keyword.other.regex.sass"}]},{match:"^((?<=\\]|\\)|not\\(|\\*|>|>\\s)|\n*):[a-z:-]+|(::|:-)[a-z:-]+",name:"entity.other.attribute-name.pseudo-class.css.sass"},{include:"#module"},{match:"[\\w-]*\\(",name:"entity.name.function"},{match:"\\)",name:"entity.name.function.close"},{begin:":",end:"$\\n?|(?=\\s\\(|and\\(|\\),)",name:"meta.property-list.css.sass.prop",patterns:[{match:"(?<=:)[a-z-]+\\s",name:"support.type.property-name.css.sass.prop.name"},{include:"#double-slash"},{include:"#double-quoted"},{include:"#single-quoted"},{include:"#interpolation"},{include:"#curly-brackets"},{include:"#variable"},{include:"#rgb-value"},{include:"#numeric"},{include:"#unit"},{include:"#module"},{match:"--.+?(?=\\))",name:"variable.css"},{match:"[\\w-]*\\(",name:"entity.name.function"},{match:"\\)",name:"entity.name.function.close"},{include:"#flag"},{include:"#comma"},{include:"#semicolon"},{include:"#function"},{include:"#function-content"},{include:"#operator"},{include:"#parent-selector"},{include:"#property-value"}]},{include:"#rgb-value"},{include:"#function"},{include:"#function-content"},{begin:"(?<=})(?!\\n|\\(|\\)|[a-zA-Z0-9_-]+:)",end:"\\s|(?=,|\\.|\\[|\\)|\\n)",name:"entity.name.tag.css.sass",patterns:[{include:"#interpolation"},{include:"#pseudo-class"}]},{include:"#operator"},{match:"[a-z-]+((?=:|#{))",name:"support.type.property-name.css.sass.prop.name"},{include:"#reserved-words"},{include:"#property-value"}],repository:{colon:{match:":",name:"meta.property-list.css.sass.colon"},comma:{match:"\\band\\b|\\bor\\b|,",name:"comment.punctuation.comma.sass"},"comment-param":{match:"\\@(\\w+)",name:"storage.type.class.jsdoc"},"comment-tag":{begin:"(?<={{)",end:"(?=}})",name:"comment.tag.sass"},"curly-brackets":{match:"{|}",name:"invalid"},dotdotdot:{match:"\\.\\.\\.",name:"variable.other"},"double-quoted":{begin:'"',end:'"',name:"string.quoted.double.css.sass",patterns:[{include:"#quoted-interpolation"}]},"double-slash":{begin:"//",end:"$\\n?",name:"comment.line.sass",patterns:[{include:"#comment-tag"}]},flag:{match:"!(important|default|optional|global)",name:"keyword.other.important.css.sass"},function:{match:"(?<=[\\s|(|,|:])(?!url|format|attr)[a-zA-Z0-9_-][\\w-]*(?=\\()",name:"support.function.name.sass"},"function-content":{begin:"(?<=url\\(|format\\(|attr\\()",end:".(?=\\))",name:"string.quoted.double.css.sass"},"import-quotes":{match:"[\"']?\\.{0,2}[\\w/]+[\"']?",name:"constant.character.css.sass"},interpolation:{begin:"#{",end:"}",name:"support.function.interpolation.sass",patterns:[{include:"#variable"},{include:"#numeric"},{include:"#operator"},{include:"#unit"},{include:"#comma"},{include:"#double-quoted"},{include:"#single-quoted"}]},module:{captures:{1:{name:"constant.character.module.name"},2:{name:"constant.numeric.module.dot"}},match:"([\\w-]+?)(\\.)",name:"constant.character.module"},numeric:{match:"(-|\\.)?\\d+(\\.\\d+)?",name:"constant.numeric.css.sass"},operator:{match:"\\+|\\s-\\s|\\s-(?=\\$)|(?<=\\()-(?=\\$)|\\s-(?=\\()|\\*|/|%|=|!|<|>|~",name:"keyword.operator.sass"},"parent-selector":{match:"&",name:"entity.name.tag.css.sass"},"parenthesis-close":{match:"\\)",name:"entity.name.function.parenthesis.close"},"parenthesis-open":{match:"\\(",name:"entity.name.function.parenthesis.open"},"placeholder-selector":{begin:"(?<!\\d)%(?!\\d)",end:"$\\n?|\\s",name:"entity.other.inherited-class.placeholder-selector.css.sass"},"property-value":{match:"[a-zA-Z0-9_-]+",name:"meta.property-value.css.sass support.constant.property-value.css.sass"},"pseudo-class":{match:":[a-z:-]+",name:"entity.other.attribute-name.pseudo-class.css.sass"},"quoted-interpolation":{begin:"#{",end:"}",name:"support.function.interpolation.sass",patterns:[{include:"#variable"},{include:"#numeric"},{include:"#operator"},{include:"#unit"},{include:"#comma"}]},"reserved-words":{match:"\\b(false|from|in|not|null|through|to|true)\\b",name:"support.type.property-name.css.sass"},"rgb-value":{match:"(#)([0-9a-fA-F]{3,4}|[0-9a-fA-F]{6}|[0-9a-fA-F]{8})\\b",name:"constant.language.color.rgb-value.css.sass"},semicolon:{match:";",name:"invalid"},"single-quoted":{begin:"'",end:"'",name:"string.quoted.single.css.sass",patterns:[{include:"#quoted-interpolation"}]},unit:{match:"(?<=[\\d]|})(ch|cm|deg|dpcm|dpi|dppx|em|ex|grad|Hz|in|kHz|mm|ms|pc|pt|px|rad|rem|s|turn|vh|vmax|vmin|vw|fr|%)",name:"keyword.control.unit.css.sass"},variable:{match:"\\$[a-zA-Z0-9_-]+",name:"variable.other.value"},"variable-root":{match:"\\$[a-zA-Z0-9_-]+",name:"variable.other.root"}},scopeName:"source.sass"})]}};