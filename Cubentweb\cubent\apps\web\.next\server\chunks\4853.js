"use strict";exports.id=4853,exports.ids=[349,1726,3034,3481,3588,3760,4853,5003,5253,5312,8050,9093,9684],exports.modules={3588:(e,n,t)=>{t.r(n),t.d(n,{default:()=>a});var a=[Object.freeze({displayName:"Dockerfile",name:"docker",patterns:[{captures:{1:{name:"keyword.other.special-method.dockerfile"},2:{name:"keyword.other.special-method.dockerfile"}},match:"^\\s*\\b(?i:(FROM))\\b.*?\\b(?i:(AS))\\b"},{captures:{1:{name:"keyword.control.dockerfile"},2:{name:"keyword.other.special-method.dockerfile"}},match:"^\\s*(?i:(ONBUILD)\\s+)?(?i:(ADD|ARG|CMD|COPY|ENTRYPOINT|ENV|EXPOSE|FROM|HEALTHCHECK|LABEL|MAINTAINER|RUN|SHELL|STOPSIGNAL|USER|VOLUME|WORKDIR))\\s"},{captures:{1:{name:"keyword.operator.dockerfile"},2:{name:"keyword.other.special-method.dockerfile"}},match:"^\\s*(?i:(ONBUILD)\\s+)?(?i:(CMD|ENTRYPOINT))\\s"},{begin:'"',beginCaptures:{1:{name:"punctuation.definition.string.begin.dockerfile"}},end:'"',endCaptures:{1:{name:"punctuation.definition.string.end.dockerfile"}},name:"string.quoted.double.dockerfile",patterns:[{match:"\\\\.",name:"constant.character.escaped.dockerfile"}]},{begin:"'",beginCaptures:{1:{name:"punctuation.definition.string.begin.dockerfile"}},end:"'",endCaptures:{1:{name:"punctuation.definition.string.end.dockerfile"}},name:"string.quoted.single.dockerfile",patterns:[{match:"\\\\.",name:"constant.character.escaped.dockerfile"}]},{captures:{1:{name:"punctuation.whitespace.comment.leading.dockerfile"},2:{name:"comment.line.number-sign.dockerfile"},3:{name:"punctuation.definition.comment.dockerfile"}},comment:"comment.line",match:"^(\\s*)((#).*$\\n?)"}],scopeName:"source.dockerfile",aliases:["dockerfile"]})]},9093:(e,n,t)=>{t.r(n),t.d(n,{default:()=>c});var a=t(14055);let i=Object.freeze({displayName:"Elixir",fileTypes:["ex","exs"],firstLineMatch:"^#!/.*\\belixir",foldingStartMarker:"(after|else|catch|rescue|->|\\{|\\[|do)\\s*$",foldingStopMarker:"^\\s*((\\}|\\]|after|else|catch|rescue)\\s*$|end\\b)",name:"elixir",patterns:[{begin:"\\b(fn)\\b(?!.*->)",beginCaptures:{1:{name:"keyword.control.elixir"}},end:"$",patterns:[{include:"#core_syntax"}]},{captures:{1:{name:"entity.name.type.class.elixir"},2:{name:"punctuation.separator.method.elixir"},3:{name:"entity.name.function.elixir"}},match:"([A-Z]\\w+)\\s*(\\.)\\s*([a-z_]\\w*[!?]?)"},{captures:{1:{name:"constant.other.symbol.elixir"},2:{name:"punctuation.separator.method.elixir"},3:{name:"entity.name.function.elixir"}},match:"(:\\w+)\\s*(\\.)\\s*([_]?\\w*[!?]?)"},{captures:{1:{name:"keyword.operator.other.elixir"},2:{name:"entity.name.function.elixir"}},match:"(\\|>)\\s*([a-z_]\\w*[!?]?)"},{match:"\\b[a-z_]\\w*[!?]?(?=\\s*\\.?\\s*\\()",name:"entity.name.function.elixir"},{begin:"\\b(fn)\\b(?=.*->)",beginCaptures:{1:{name:"keyword.control.elixir"}},end:"(?>(->)|(when)|(\\)))",endCaptures:{1:{name:"keyword.operator.other.elixir"},2:{name:"keyword.control.elixir"},3:{name:"punctuation.section.function.elixir"}},patterns:[{include:"#core_syntax"}]},{include:"#core_syntax"},{begin:"^(?=.*->)((?![^\"']*(\"|')[^\"']*->)|(?=.*->[^\"']*(\"|')[^\"']*->))((?!.*\\([^)]*->)|(?=[^()]*->)|(?=\\s*\\(.*\\).*->))((?!.*\\b(fn)\\b)|(?=.*->.*\\bfn\\b))",beginCaptures:{1:{name:"keyword.control.elixir"}},end:"(?>(->)|(when)|(\\)))",endCaptures:{1:{name:"keyword.operator.other.elixir"},2:{name:"keyword.control.elixir"},3:{name:"punctuation.section.function.elixir"}},patterns:[{include:"#core_syntax"}]}],repository:{core_syntax:{patterns:[{begin:"^\\s*(defmodule)\\b",beginCaptures:{1:{name:"keyword.control.module.elixir"}},end:"\\b(do)\\b",endCaptures:{1:{name:"keyword.control.module.elixir"}},name:"meta.module.elixir",patterns:[{match:"\\b[A-Z]\\w*(?=\\.)",name:"entity.other.inherited-class.elixir"},{match:"\\b[A-Z]\\w*\\b",name:"entity.name.type.class.elixir"}]},{begin:"^\\s*(defprotocol)\\b",beginCaptures:{1:{name:"keyword.control.protocol.elixir"}},end:"\\b(do)\\b",endCaptures:{1:{name:"keyword.control.protocol.elixir"}},name:"meta.protocol_declaration.elixir",patterns:[{match:"\\b[A-Z]\\w*\\b",name:"entity.name.type.protocol.elixir"}]},{begin:"^\\s*(defimpl)\\b",beginCaptures:{1:{name:"keyword.control.protocol.elixir"}},end:"\\b(do)\\b",endCaptures:{1:{name:"keyword.control.protocol.elixir"}},name:"meta.protocol_implementation.elixir",patterns:[{match:"\\b[A-Z]\\w*\\b",name:"entity.name.type.protocol.elixir"}]},{begin:"^\\s*(def|defmacro|defdelegate|defguard)\\s+((?>[a-zA-Z_]\\w*(?>\\.|::))?(?>[a-zA-Z_]\\w*(?>[?!]|=(?!>))?|===?|>[>=]?|<=>|<[<=]?|[%&`/\\|]|\\*\\*?|=?~|[-+]@?|\\[\\]=?))((\\()|\\s*)",beginCaptures:{1:{name:"keyword.control.module.elixir"},2:{name:"entity.name.function.public.elixir"},4:{name:"punctuation.section.function.elixir"}},end:"(\\bdo:)|(\\bdo\\b)|(?=\\s+(def|defn|defmacro|defdelegate|defguard)\\b)",endCaptures:{1:{name:"constant.other.keywords.elixir"},2:{name:"keyword.control.module.elixir"}},name:"meta.function.public.elixir",patterns:[{include:"$self"},{begin:"\\s(\\\\\\\\)",beginCaptures:{1:{name:"keyword.operator.other.elixir"}},end:",|\\)|$",patterns:[{include:"$self"}]},{match:"\\b(is_atom|is_binary|is_bitstring|is_boolean|is_float|is_function|is_integer|is_list|is_map|is_nil|is_number|is_pid|is_port|is_record|is_reference|is_tuple|is_exception|abs|bit_size|byte_size|div|elem|hd|length|map_size|node|rem|round|tl|trunc|tuple_size)\\b",name:"keyword.control.elixir"}]},{begin:"^\\s*(defp|defnp|defmacrop|defguardp)\\s+((?>[a-zA-Z_]\\w*(?>\\.|::))?(?>[a-zA-Z_]\\w*(?>[?!]|=(?!>))?|===?|>[>=]?|<=>|<[<=]?|[%&`/\\|]|\\*\\*?|=?~|[-+]@?|\\[\\]=?))((\\()|\\s*)",beginCaptures:{1:{name:"keyword.control.module.elixir"},2:{name:"entity.name.function.private.elixir"},4:{name:"punctuation.section.function.elixir"}},end:"(\\bdo:)|(\\bdo\\b)|(?=\\s+(defp|defmacrop|defguardp)\\b)",endCaptures:{1:{name:"constant.other.keywords.elixir"},2:{name:"keyword.control.module.elixir"}},name:"meta.function.private.elixir",patterns:[{include:"$self"},{begin:"\\s(\\\\\\\\)",beginCaptures:{1:{name:"keyword.operator.other.elixir"}},end:",|\\)|$",patterns:[{include:"$self"}]},{match:"\\b(is_atom|is_binary|is_bitstring|is_boolean|is_float|is_function|is_integer|is_list|is_map|is_nil|is_number|is_pid|is_port|is_record|is_reference|is_tuple|is_exception|abs|bit_size|byte_size|div|elem|hd|length|map_size|node|rem|round|tl|trunc|tuple_size)\\b",name:"keyword.control.elixir"}]},{begin:'\\s*~L"""',comment:"Leex Sigil",end:'\\s*"""',name:"sigil.leex",patterns:[{include:"text.elixir"},{include:"text.html.basic"}]},{begin:'\\s*~H"""',comment:"HEEx Sigil",end:'\\s*"""',name:"sigil.heex",patterns:[{include:"text.elixir"},{include:"text.html.basic"}]},{begin:'@(module|type)?doc (~[a-z])?"""',comment:"@doc with heredocs is treated as documentation",end:'\\s*"""',name:"comment.block.documentation.heredoc",patterns:[{include:"#interpolated_elixir"},{include:"#escaped_char"}]},{begin:'@(module|type)?doc ~[A-Z]"""',comment:"@doc with heredocs is treated as documentation",end:'\\s*"""',name:"comment.block.documentation.heredoc"},{begin:"@(module|type)?doc (~[a-z])?'''",comment:"@doc with heredocs is treated as documentation",end:"\\s*'''",name:"comment.block.documentation.heredoc",patterns:[{include:"#interpolated_elixir"},{include:"#escaped_char"}]},{begin:"@(module|type)?doc ~[A-Z]'''",comment:"@doc with heredocs is treated as documentation",end:"\\s*'''",name:"comment.block.documentation.heredoc"},{comment:"@doc false is treated as documentation",match:"@(module|type)?doc false",name:"comment.block.documentation.false"},{begin:'@(module|type)?doc "',comment:"@doc with string is treated as documentation",end:'"',name:"comment.block.documentation.string",patterns:[{include:"#interpolated_elixir"},{include:"#escaped_char"}]},{match:"(?<!\\.)\\b(do|end|case|bc|lc|for|if|cond|unless|try|receive|fn|defmodule|defp?|defprotocol|defimpl|defrecord|defstruct|defnp?|defmacrop?|defguardp?|defdelegate|defexception|defoverridable|exit|after|rescue|catch|else|raise|reraise|throw|import|require|alias|use|quote|unquote|super|with)\\b(?![?!:])",name:"keyword.control.elixir"},{comment:" as above, just doesn't need a 'end' and does a logic operation",match:"(?<!\\.)\\b(and|not|or|when|xor|in)\\b",name:"keyword.operator.elixir"},{match:"\\b[A-Z]\\w*\\b",name:"entity.name.type.class.elixir"},{match:"\\b(nil|true|false)\\b(?![?!])",name:"constant.language.elixir"},{match:"\\b(__(CALLER|ENV|MODULE|DIR|STACKTRACE)__)\\b(?![?!])",name:"variable.language.elixir"},{captures:{1:{name:"punctuation.definition.variable.elixir"}},match:"(@)[a-zA-Z_]\\w*",name:"variable.other.readwrite.module.elixir"},{captures:{1:{name:"punctuation.definition.variable.elixir"}},match:"(&)\\d+",name:"variable.other.anonymous.elixir"},{match:"&(?![&])",name:"variable.other.anonymous.elixir"},{captures:{1:{name:"punctuation.definition.variable.elixir"}},match:"\\^[a-z_]\\w*",name:"variable.other.capture.elixir"},{match:"\\b0x[0-9A-Fa-f](?>_?[0-9A-Fa-f])*\\b",name:"constant.numeric.hex.elixir"},{match:"\\b\\d(?>_?\\d)*(\\.(?![^\\s\\d])(?>_?\\d)+)([eE][-+]?\\d(?>_?\\d)*)?\\b",name:"constant.numeric.float.elixir"},{match:"\\b\\d(?>_?\\d)*\\b",name:"constant.numeric.integer.elixir"},{match:"\\b0b[01](?>_?[01])*\\b",name:"constant.numeric.binary.elixir"},{match:"\\b0o[0-7](?>_?[0-7])*\\b",name:"constant.numeric.octal.elixir"},{begin:":'",captures:{0:{name:"punctuation.definition.constant.elixir"}},end:"'",name:"constant.other.symbol.single-quoted.elixir",patterns:[{include:"#interpolated_elixir"},{include:"#escaped_char"}]},{begin:':"',captures:{0:{name:"punctuation.definition.constant.elixir"}},end:'"',name:"constant.other.symbol.double-quoted.elixir",patterns:[{include:"#interpolated_elixir"},{include:"#escaped_char"}]},{begin:"(?>''')",beginCaptures:{0:{name:"punctuation.definition.string.begin.elixir"}},comment:"Single-quoted heredocs",end:"^\\s*'''",endCaptures:{0:{name:"punctuation.definition.string.end.elixir"}},name:"string.quoted.single.heredoc.elixir",patterns:[{include:"#interpolated_elixir"},{include:"#escaped_char"}]},{begin:"'",beginCaptures:{0:{name:"punctuation.definition.string.begin.elixir"}},comment:"single quoted string (allows for interpolation)",end:"'",endCaptures:{0:{name:"punctuation.definition.string.end.elixir"}},name:"string.quoted.single.elixir",patterns:[{include:"#interpolated_elixir"},{include:"#escaped_char"}]},{begin:'(?>""")',beginCaptures:{0:{name:"punctuation.definition.string.begin.elixir"}},comment:"Double-quoted heredocs",end:'^\\s*"""',endCaptures:{0:{name:"punctuation.definition.string.end.elixir"}},name:"string.quoted.double.heredoc.elixir",patterns:[{include:"#interpolated_elixir"},{include:"#escaped_char"}]},{begin:'"',beginCaptures:{0:{name:"punctuation.definition.string.begin.elixir"}},comment:"double quoted string (allows for interpolation)",end:'"',endCaptures:{0:{name:"punctuation.definition.string.end.elixir"}},name:"string.quoted.double.elixir",patterns:[{include:"#interpolated_elixir"},{include:"#escaped_char"}]},{begin:'~[a-z](?>""")',beginCaptures:{0:{name:"punctuation.definition.string.begin.elixir"}},comment:"Double-quoted heredocs sigils",end:'^\\s*"""',endCaptures:{0:{name:"punctuation.definition.string.end.elixir"}},name:"string.quoted.other.sigil.heredoc.elixir",patterns:[{include:"#interpolated_elixir"},{include:"#escaped_char"}]},{begin:"~[a-z]\\{",beginCaptures:{0:{name:"punctuation.definition.string.begin.elixir"}},comment:"sigil (allow for interpolation)",end:"\\}[a-z]*",endCaptures:{0:{name:"punctuation.definition.string.end.elixir"}},name:"string.quoted.other.sigil.elixir",patterns:[{include:"#interpolated_elixir"},{include:"#escaped_char"}]},{begin:"~[a-z]\\[",beginCaptures:{0:{name:"punctuation.definition.string.begin.elixir"}},comment:"sigil (allow for interpolation)",end:"\\][a-z]*",endCaptures:{0:{name:"punctuation.definition.string.end.elixir"}},name:"string.quoted.other.sigil.elixir",patterns:[{include:"#interpolated_elixir"},{include:"#escaped_char"}]},{begin:"~[a-z]<",beginCaptures:{0:{name:"punctuation.definition.string.begin.elixir"}},comment:"sigil (allow for interpolation)",end:">[a-z]*",endCaptures:{0:{name:"punctuation.definition.string.end.elixir"}},name:"string.quoted.other.sigil.elixir",patterns:[{include:"#interpolated_elixir"},{include:"#escaped_char"}]},{begin:"~[a-z]\\(",beginCaptures:{0:{name:"punctuation.definition.string.begin.elixir"}},comment:"sigil (allow for interpolation)",end:"\\)[a-z]*",endCaptures:{0:{name:"punctuation.definition.string.end.elixir"}},name:"string.quoted.other.sigil.elixir",patterns:[{include:"#interpolated_elixir"},{include:"#escaped_char"}]},{begin:"~[a-z]([^\\w])",beginCaptures:{0:{name:"punctuation.definition.string.begin.elixir"}},comment:"sigil (allow for interpolation)",end:"\\1[a-z]*",endCaptures:{0:{name:"punctuation.definition.string.end.elixir"}},name:"string.quoted.other.sigil.elixir",patterns:[{include:"#interpolated_elixir"},{include:"#escaped_char"}]},{begin:'~[A-Z](?>""")',beginCaptures:{0:{name:"punctuation.definition.string.begin.elixir"}},comment:"Double-quoted heredocs sigils",end:'^\\s*"""',endCaptures:{0:{name:"punctuation.definition.string.end.elixir"}},name:"string.quoted.other.sigil.heredoc.literal.elixir"},{begin:"~[A-Z]\\{",beginCaptures:{0:{name:"punctuation.definition.string.begin.elixir"}},comment:"sigil (without interpolation)",end:"\\}[a-z]*",endCaptures:{0:{name:"punctuation.definition.string.end.elixir"}},name:"string.quoted.other.sigil.literal.elixir"},{begin:"~[A-Z]\\[",beginCaptures:{0:{name:"punctuation.definition.string.begin.elixir"}},comment:"sigil (without interpolation)",end:"\\][a-z]*",endCaptures:{0:{name:"punctuation.definition.string.end.elixir"}},name:"string.quoted.other.sigil.literal.elixir"},{begin:"~[A-Z]<",beginCaptures:{0:{name:"punctuation.definition.string.begin.elixir"}},comment:"sigil (without interpolation)",end:">[a-z]*",endCaptures:{0:{name:"punctuation.definition.string.end.elixir"}},name:"string.quoted.other.sigil.literal.elixir"},{begin:"~[A-Z]\\(",beginCaptures:{0:{name:"punctuation.definition.string.begin.elixir"}},comment:"sigil (without interpolation)",end:"\\)[a-z]*",endCaptures:{0:{name:"punctuation.definition.string.end.elixir"}},name:"string.quoted.other.sigil.literal.elixir"},{begin:"~[A-Z]([^\\w])",beginCaptures:{0:{name:"punctuation.definition.string.begin.elixir"}},comment:"sigil (without interpolation)",end:"\\1[a-z]*",endCaptures:{0:{name:"punctuation.definition.string.end.elixir"}},name:"string.quoted.other.sigil.literal.elixir"},{captures:{1:{name:"punctuation.definition.constant.elixir"}},comment:"symbols",match:"(?<!:)(:)(?>[a-zA-Z_][\\w@]*(?>[?!]|=(?![>=]))?|<>|===?|!==?|<<>>|<<<|>>>|~~~|::|<-|\\|>|=>|=~|=|/|\\\\\\\\|\\*\\*?|\\.\\.?\\.?|\\.\\.//|>=?|<=?|&&?&?|\\+\\+?|--?|\\|\\|?\\|?|!|@|\\%?\\{\\}|%|\\[\\]|\\^(\\^\\^)?)",name:"constant.other.symbol.elixir"},{captures:{1:{name:"punctuation.definition.constant.elixir"}},comment:"symbols",match:"(?>[a-zA-Z_][\\w@]*(?>[?!])?)(:)(?!:)",name:"constant.other.keywords.elixir"},{begin:"(^[ \\t]+)?(?=##)",beginCaptures:{1:{name:"punctuation.whitespace.comment.leading.elixir"}},end:"(?!#)",patterns:[{begin:"##",beginCaptures:{0:{name:"punctuation.definition.comment.elixir"}},end:"\\n",name:"comment.line.section.elixir"}]},{begin:"(^[ \\t]+)?(?=#)",beginCaptures:{1:{name:"punctuation.whitespace.comment.leading.elixir"}},end:"(?!#)",patterns:[{begin:"#",beginCaptures:{0:{name:"punctuation.definition.comment.elixir"}},end:"\\n",name:"comment.line.number-sign.elixir"}]},{match:"\\b_([^_][\\w]+[?!]?)",name:"comment.unused.elixir"},{match:"\\b_\\b",name:"comment.wildcard.elixir"},{comment:'\n			matches questionmark-letters.\n\n			examples (1st alternation = hex):\n			?\\x1     ?\\x61\n\n			examples (2rd alternation = escaped):\n			?\\n      ?\\b\n\n			examples (3rd alternation = normal):\n			?a       ?A       ?0\n			?*       ?"       ?(\n			?.       ?#\n\n			the negative lookbehind prevents against matching\n			p(42.tainted?)\n			',match:"(?<!\\w)\\?(\\\\(x[0-9A-Fa-f]{1,2}(?![0-9A-Fa-f])\\b|[^xMC])|[^\\s\\\\])",name:"constant.numeric.elixir"},{match:"\\+\\+|--|<\\|>",name:"keyword.operator.concatenation.elixir"},{match:"\\|>|<~>|<>|<<<|>>>|~>>|<<~|~>|<~|<\\|>",name:"keyword.operator.sigils_1.elixir"},{match:"&&&|&&",name:"keyword.operator.sigils_2.elixir"},{match:"<-|\\\\\\\\",name:"keyword.operator.sigils_3.elixir"},{match:"===?|!==?|<=?|>=?",name:"keyword.operator.comparison.elixir"},{match:"(\\|\\|\\||&&&|\\^\\^\\^|<<<|>>>|~~~)",name:"keyword.operator.bitwise.elixir"},{match:"(?<=[ \\t])!+|\\bnot\\b|&&|\\band\\b|\\|\\||\\bor\\b|\\bxor\\b",name:"keyword.operator.logical.elixir"},{match:"(\\*|\\+|-|/)",name:"keyword.operator.arithmetic.elixir"},{match:"\\||\\+\\+|--|\\*\\*|\\\\\\\\|<-|<>|<<|>>|::|\\.\\.|//|\\|>|~|=>|&",name:"keyword.operator.other.elixir"},{match:"=",name:"keyword.operator.assignment.elixir"},{match:":",name:"punctuation.separator.other.elixir"},{match:"\\;",name:"punctuation.separator.statement.elixir"},{match:",",name:"punctuation.separator.object.elixir"},{match:"\\.",name:"punctuation.separator.method.elixir"},{match:"\\{|\\}",name:"punctuation.section.scope.elixir"},{match:"\\[|\\]",name:"punctuation.section.array.elixir"},{match:"\\(|\\)",name:"punctuation.section.function.elixir"}]},escaped_char:{match:"\\\\(x[\\da-fA-F]{1,2}|.)",name:"constant.character.escaped.elixir"},interpolated_elixir:{begin:"#\\{",beginCaptures:{0:{name:"punctuation.section.embedded.begin.elixir"}},contentName:"source.elixir",end:"\\}",endCaptures:{0:{name:"punctuation.section.embedded.end.elixir"}},name:"meta.embedded.line.elixir",patterns:[{include:"#nest_curly_and_self"},{include:"$self"}]},nest_curly_and_self:{patterns:[{begin:"\\{",captures:{0:{name:"punctuation.section.scope.elixir"}},end:"\\}",patterns:[{include:"#nest_curly_and_self"}]},{include:"$self"}]}},scopeName:"source.elixir",embeddedLangs:["html"]});var c=[...a.default,i]},14853:(e,n,t)=>{t.r(n),t.d(n,{default:()=>P});var a=t(14055),i=t(25253),c=t(65312),o=t(14187),s=t(63760),r=t(42204),l=t(73279),d=t(98013),u=t(37861),m=t(90349),p=t(3588),b=t(9093),k=t(99056),g=t(55334),h=t(74058),f=t(63914),y=t(65152),$=t(96736),v=t(63069),w=t(85862),x=t(27155),C=t(44131),N=t(55003),_=t(7395),j=t(58050),A=t(56437),B=t(93399),q=t(43055),S=t(33374),z=t(20106),E=t(92200),Z=t(33034),O=t(91726),L=t(97310),T=t(6028),F=t(31767),R=t(21516),I=t(12734),G=t(69684),U=t(10861),D=t(31127);let M=Object.freeze({displayName:"AsciiDoc",fileTypes:["ad","asc","adoc","asciidoc","adoc.txt"],name:"asciidoc",patterns:[{include:"#comment"},{include:"#callout-list-item"},{include:"#titles"},{include:"#attribute-entry"},{include:"#blocks"},{include:"#block-title"},{include:"#tables"},{include:"#horizontal-rule"},{include:"#list"},{include:"#inlines"},{include:"#block-attribute"},{include:"#line-break"}],repository:{"admonition-paragraph":{patterns:[{begin:"(?=(?>(?:^\\[(NOTE|TIP|IMPORTANT|WARNING|CAUTION)((?:,|#|\\.|%)[^\\]]+)*\\]$)))",end:"((?<=--|====)$|^\\p{Blank}*$)",name:"markup.admonition.asciidoc",patterns:[{captures:{0:{patterns:[{include:"#block-attribute-inner"}]}},match:"^\\[(NOTE|TIP|IMPORTANT|WARNING|CAUTION)((?:,|#|\\.|%)([^,\\]]+))*\\]$"},{include:"#inlines"},{include:"#block-title"},{begin:"^(={4,})\\s*$",comment:"example block",end:"(?<=\\1)",patterns:[{include:"#inlines"},{include:"#list"}]},{begin:"^(-{2})\\s*$",comment:"open block",end:"(?<=\\1)",patterns:[{include:"#inlines"},{include:"#list"}]}]},{begin:"^(NOTE|TIP|IMPORTANT|WARNING|CAUTION):\\p{Blank}+",captures:{1:{name:"entity.name.function.asciidoc"}},end:"^\\p{Blank}*$",name:"markup.admonition.asciidoc",patterns:[{include:"#inlines"}]}]},"anchor-macro":{patterns:[{captures:{1:{name:"support.constant.asciidoc"},2:{name:"markup.blockid.asciidoc"},3:{name:"string.unquoted.asciidoc"},4:{name:"support.constant.asciidoc"}},match:"(?<!\\\\)(?:(\\[{2})([\\p{Alpha}:_][\\p{Word}:.-]*)(?:,\\p{Blank}*(\\S.*?))?(\\]{2}))",name:"markup.other.anchor.asciidoc"},{captures:{1:{name:"entity.name.function.asciidoc"},2:{name:"markup.blockid.asciidoc"},3:{name:"string.unquoted.asciidoc"}},match:"(?<!\\\\)(anchor):(\\S+)\\[(.*?[^\\\\])?\\]",name:"markup.other.anchor.asciidoc"}]},"attribute-entry":{patterns:[{begin:"^(:)(!?\\w.*?)(:)(\\p{Blank}+.+\\p{Blank}(?:\\+|\\\\))$",beginCaptures:{1:{name:"punctuation.separator.attribute-entry.asciidoc"},2:{name:"support.constant.attribute-name.asciidoc"},3:{name:"punctuation.separator.attribute-entry.asciidoc"},4:{name:"string.unquoted.attribute-value.asciidoc",patterns:[{include:"#inlines"},{include:"#hard-break-backslash"},{include:"#line-break"},{include:"#line-break-backslash"}]}},contentName:"string.unquoted.attribute-value.asciidoc",end:"^\\p{Blank}+.+$(?<!\\+|\\\\)|^\\p{Blank}*$",endCaptures:{0:{name:"string.unquoted.attribute-value.asciidoc"}},name:"meta.definition.attribute-entry.asciidoc",patterns:[{include:"#inlines"},{include:"#hard-break-backslash"},{include:"#line-break"},{include:"#line-break-backslash"}]},{captures:{1:{name:"punctuation.separator.asciidoc"},2:{name:"support.constant.attribute-name.asciidoc"},3:{name:"punctuation.separator.asciidoc"},4:{name:"string.unquoted.attribute-value.asciidoc",patterns:[{include:"#inlines"},{include:"#line-break"}]}},match:"^(:)(!?\\w.*?)(:)(\\p{Blank}+(.*))?$",name:"meta.definition.attribute-entry.asciidoc"}]},"attribute-reference":{patterns:[{captures:{2:{name:"entity.name.function.asciidoc"},3:{name:"punctuation.separator.asciidoc"},4:{name:"support.constant.attribute-name.asciidoc"},6:{name:"punctuation.separator.asciidoc"},7:{name:"string.unquoted.attribute-value.asciidoc"}},match:"(?<!\\\\)(\\{)(set|counter2?)(:)([\\p{Alnum}\\-_!]+)((:)(.*?))?(?<!\\\\)(\\})",name:"markup.substitution.attribute-reference.asciidoc"},{match:"(?<!\\\\)(\\{)(\\w+(?:[\\-]\\w+)*)(?<!\\\\)(\\})",name:"markup.substitution.attribute-reference.asciidoc"}]},"bibliography-anchor":{patterns:[{captures:{1:{name:"support.constant.asciidoc"},2:{name:"markup.biblioref.asciidoc"},3:{name:"support.constant.asciidoc"}},match:"(?<!\\\\)(\\[{3})([\\p{Word}:][\\p{Word}:.-]*?)(\\]{3})",name:"bibliography-anchor.asciidoc"}]},"bibtex-macro":{patterns:[{begin:"(?<!\\\\)(citenp:)([a-z,]*)(\\[)",beginCaptures:{1:{name:"entity.name.function.asciidoc"},2:{name:"markup.meta.attribute-list.asciidoc"}},contentName:"string.unquoted.asciidoc",end:"\\]|^$",name:"markup.macro.inline.bibtex.asciidoc"}]},"block-attribute":{patterns:[{captures:{0:{patterns:[{include:"#block-attribute-inner"}]}},match:"^\\[(|\\p{Blank}*[\\p{Word}{,.#\"'%].*)\\]$",name:"markup.heading.block-attribute.asciidoc"}]},"block-attribute-inner":{patterns:[{comment:"separators",match:"([,.#%])",name:"punctuation.separator.asciidoc"},{captures:{0:{name:"markup.meta.attribute-list.asciidoc",patterns:[{include:"#keywords"}]}},comment:"blockname",match:"(?<=\\[)([^\\[\\],.#%=]+)"},{captures:{0:{patterns:[{include:"#attribute-reference"}]}},comment:"attributes",match:"(?<=\\{|,|.|#|\"|'|%)([^\\],.#%]+)",name:"markup.meta.attribute-list.asciidoc"}]},"block-callout":{patterns:[{captures:{2:{name:"constant.other.symbol.asciidoc"},4:{name:"constant.numeric.asciidoc"},5:{name:"constant.other.symbol.asciidoc"}},match:"(?:(?:\\/\\/|#|--|;;) ?)?( )?(?<!\\\\)(<)!?(--|)(\\d+)\\3(>)(?=(?: ?<!?\\3\\d+\\3>)*$)",name:"callout.source.code.asciidoc"}]},"block-title":{patterns:[{begin:"^\\.([^\\p{Blank}.].*)",captures:{1:{name:"markup.heading.blocktitle.asciidoc"}},end:"$"}]},blocks:{patterns:[{include:"#front-matter-block"},{include:"#comment-paragraph"},{include:"#admonition-paragraph"},{include:"#quote-paragraph"},{include:"#listing-paragraph"},{include:"#source-paragraphs"},{include:"#passthrough-paragraph"},{include:"#example-paragraph"},{include:"#sidebar-paragraph"},{include:"#literal-paragraph"},{include:"#open-block"}]},"callout-list-item":{patterns:[{captures:{1:{name:"constant.other.symbol.asciidoc"},2:{name:"constant.numeric.asciidoc"},3:{name:"constant.other.symbol.asciidoc"},4:{patterns:[{include:"#inlines"}]}},match:"^(<)(\\d+)(>)\\p{Blank}+(.*)$",name:"callout.asciidoc"}]},characters:{patterns:[{captures:{1:{name:"constant.character.asciidoc"},3:{name:"constant.character.asciidoc"}},match:"(?<!\\\\)(&)(\\S+?)(;)",name:"markup.character-reference.asciidoc"}]},comment:{patterns:[{begin:"^(/{4,})$",end:"^\\1$",name:"comment.block.asciidoc",patterns:[{include:"#inlines"}]},{match:"^/{2}([^/].*)?$",name:"comment.inline.asciidoc"}]},"comment-paragraph":{patterns:[{begin:"(?=(?>(?:^\\[(comment)((?:,|#|\\.|%)[^\\]]+)*\\]$)))",end:"((?<=--)$|^\\p{Blank}*$)",name:"comment.block.asciidoc",patterns:[{captures:{0:{patterns:[{include:"#block-attribute-inner"}]}},match:"^\\[(comment)((?:,|#|\\.|%)([^,\\]]+))*\\]$"},{include:"#block-title"},{begin:"^(-{2})\\s*$",comment:"open block",end:"^(\\1)$",patterns:[{include:"#inlines"},{include:"#list"}]},{include:"#inlines"}]}]},emphasis:{patterns:[{captures:{1:{name:"markup.meta.attribute-list.asciidoc"},2:{name:"markup.italic.asciidoc"},3:{name:"punctuation.definition.asciidoc"},5:{name:"punctuation.definition.asciidoc"}},match:"(?<!\\\\\\\\)(\\[(?:[^\\]]+?)\\])?((__)((?!_).+?)(__))",name:"markup.emphasis.unconstrained.asciidoc"},{captures:{1:{name:"markup.meta.attribute-list.asciidoc"},2:{name:"markup.italic.asciidoc"},3:{name:"punctuation.definition.asciidoc"},5:{name:"punctuation.definition.asciidoc"}},match:"(?!_{4,}\\s*$)(?<=^|[^\\p{Word};:])(\\[(?:[^\\]]+?)\\])?((_)(\\S|\\S.*?\\S)(_))(?!\\p{Word})",name:"markup.emphasis.constrained.asciidoc"}]},"example-paragraph":{patterns:[{begin:"(?=(?>(?:^\\[(example)((?:,|#|\\.|%)[^\\]]+)*\\]$)))",end:"((?<=--|====)$|^\\p{Blank}*$)",name:"markup.block.example.asciidoc",patterns:[{captures:{0:{patterns:[{include:"#block-attribute-inner"}]}},match:"^\\[(example)((?:,|#|\\.|%)([^,\\]]+))*\\]$"},{include:"#block-title"},{begin:"^(={4,})$",comment:"example block",end:"^(\\1)$",patterns:[{include:"$self"}]},{begin:"^(-{2})$",comment:"open block",end:"^(\\1)$",patterns:[{include:"$self"}]},{include:"#inlines"}]},{begin:"^(={4,})$",end:"^(\\1)$",name:"markup.block.example.asciidoc",patterns:[{include:"$self"}]}]},"footnote-macro":{patterns:[{begin:"(?<!\\\\)footnote(?:(ref):|:([\\w-]+)?)\\[(?:|(.*?[^\\\\]))\\]",beginCaptures:{1:{name:"entity.name.function.asciidoc"},2:{name:"support.constant.attribute-name.asciidoc"}},contentName:"string.unquoted.asciidoc",end:"\\]|^$",name:"markup.other.footnote.asciidoc",patterns:[{include:"#inlines"}]}]},"front-matter-block":{patterns:[{begin:"\\A(-{3}$)",end:"^(\\1)$",name:"markup.block.front-matter.asciidoc",patterns:[{include:"source.yaml"}]}]},"general-block-macro":{patterns:[{captures:{1:{name:"entity.name.function.asciidoc"},2:{name:"punctuation.separator.asciidoc"},3:{name:"markup.link.asciidoc",patterns:[{include:"#attribute-reference"}]},4:{name:"punctuation.separator.asciidoc"},5:{name:"string.unquoted.asciidoc",patterns:[{include:"#attribute-reference"}]},6:{name:"punctuation.separator.asciidoc"}},match:"^(\\p{Word}+)(::)(\\S*?)(\\[)((?:\\\\\\]|[^\\]])*?)(\\])$",name:"markup.macro.block.general.asciidoc"}]},"hard-break-backslash":{patterns:[{captures:{1:{name:"constant.other.symbol.hard-break.asciidoc"}},match:"(?<=\\S)\\p{Blank}+(\\+ \\\\)$"}]},"horizontal-rule":{patterns:[{match:"^(?:'|<){3,}$|^ {0,3}([-\\*'])( *)\\1\\2\\1$",name:"constant.other.symbol.horizontal-rule.asciidoc"}]},"image-macro":{patterns:[{captures:{1:{name:"entity.name.function.asciidoc"},2:{name:"markup.link.asciidoc"},3:{name:"string.unquoted.asciidoc"}},match:"(?<!\\\\)(image|icon):([^:\\[][^\\[]*)\\[((?:\\\\\\]|[^\\]])*?)\\]",name:"markup.macro.image.asciidoc"}]},"include-directive":{patterns:[{captures:{1:{name:"entity.name.function.asciidoc"},2:{name:"punctuation.separator.asciidoc"},3:{name:"markup.link.asciidoc",patterns:[{include:"#attribute-reference"}]},4:{name:"punctuation.separator.asciidoc"},5:{name:"string.unquoted.asciidoc",patterns:[{include:"#attribute-reference"}]},6:{name:"punctuation.separator.asciidoc"}},match:"^(include)(::)([^\\[]+)(\\[)(.*?)(\\])$"}]},inlines:{patterns:[{include:"#typographic-quotes"},{include:"#strong"},{include:"#monospace"},{include:"#emphasis"},{include:"#superscript"},{include:"#subscript"},{include:"#mark"},{include:"#general-block-macro"},{include:"#anchor-macro"},{include:"#footnote-macro"},{include:"#image-macro"},{include:"#kbd-macro"},{include:"#link-macro"},{include:"#stem-macro"},{include:"#menu-macro"},{include:"#passthrough-macro"},{include:"#xref-macro"},{include:"#attribute-reference"},{include:"#characters"},{include:"#bibtex-macro"},{include:"#bibliography-anchor"}]},"kbd-macro":{patterns:[{captures:{1:{name:"entity.name.function.asciidoc"},3:{name:"string.unquoted.asciidoc"}},match:"(?<!\\\\)(kbd|btn):(\\[)((?:\\\\\\]|[^\\]])+?)(\\])",name:"markup.macro.kbd.asciidoc"}]},keywords:{patterns:[{comment:"Admonition",match:"(NOTE|TIP|IMPORTANT|WARNING|CAUTION)",name:"entity.name.function.asciidoc"},{comment:"Paragraph or verbatim",match:"(comment|example|literal|listing|normal|pass|quote|sidebar|source|verse|abstract|partintro)",name:"entity.name.function.asciidoc"},{comment:"Diagram",match:"(actdiag|blockdiag|ditaa|graphviz|meme|mermaid|nwdiag|packetdiag|pikchr|plantuml|rackdiag|seqdiag|shaape|wavedrom)",name:"entity.name.function.asciidoc"},{comment:"Others",match:"(sect[1-4]|preface|colophon|dedication|glossary|bibliography|synopsis|appendix|index|normal|partintro|music|latex|stem)",name:"entity.name.function.asciidoc"}]},"line-break":{patterns:[{captures:{1:{name:"variable.line-break.asciidoc"}},match:"(?<=\\S)\\p{Blank}+(\\+)$"}]},"line-break-backslash":{patterns:[{captures:{1:{name:"variable.line-break.asciidoc"}},match:"(?<=\\S)\\p{Blank}+(\\\\)$"}]},"link-macro":{patterns:[{captures:{1:{name:"markup.link.asciidoc",patterns:[{include:"#attribute-reference"}]},2:{name:"string.unquoted.asciidoc"}},match:"(?:^|<|[\\s>()\\[\\];])((?<!\\\\)(?:https?|file|ftp|irc)://[^\\s\\[\\]<]*[^\\s.,\\[\\]<)])(?:\\[((?:\\\\\\]|[^\\]])*?)\\])?",name:"markup.other.url.asciidoc"},{captures:{1:{name:"markup.substitution.attribute-reference.asciidoc"},2:{name:"string.unquoted.asciidoc"}},match:"(?:^|<|[\\p{Blank}>()\\[\\];])((?<!\\\\)\\{uri-\\w+(?:[\\-]\\w+)*(?<!\\\\)\\})(?:\\[((?:\\\\\\]|[^\\]])*?)\\])",name:"markup.other.url.asciidoc"},{captures:{1:{name:"entity.name.function.asciidoc"},2:{name:"markup.link.asciidoc",patterns:[{include:"#attribute-reference"}]},3:{name:"string.unquoted.asciidoc"}},match:"(?<!\\\\)(link|mailto):([^\\s\\[]+)(?:\\[((?:\\\\\\]|[^\\]])*?)\\])",name:"markup.other.url.asciidoc"},{match:"\\p{Word}[\\p{Word}.%+-]*(@)\\p{Alnum}[\\p{Alnum}.-]*(\\.)\\p{Alpha}{2,4}\\b",name:"markup.link.email.asciidoc"}]},list:{patterns:[{captures:{1:{name:"markup.list.bullet.asciidoc"},2:{name:"markup.todo.box.asciidoc"}},match:"^\\s*(-)\\p{Blank}(\\[[\\p{Blank}\\*x]\\])(?=\\p{Blank})",name:"markup.todo.asciidoc"},{captures:{1:{name:"markup.list.bullet.asciidoc"}},match:"^\\p{Blank}*(-|\\*{1,5}|\\u2022{1,5})(?=\\p{Blank})",name:"markup.list.asciidoc"},{captures:{1:{name:"markup.list.bullet.asciidoc"}},match:"^\\p{Blank}*(\\.{1,5}|\\d+\\.|[a-zA-Z]\\.|[IVXivx]+\\))(?=\\p{Blank})",name:"markup.list.asciidoc"},{captures:{1:{patterns:[{include:"#link-macro"},{include:"#attribute-reference"}]},2:{name:"markup.list.bullet.asciidoc"}},match:"^\\p{Blank}*(.*?\\S)(:{2,4}|;;)($|\\p{Blank}+)",name:"markup.heading.list.asciidoc"}]},"listing-paragraph":{patterns:[{begin:"(?=(?>(?:^\\[(listing)((?:,|#|\\.|%)[^\\]]+)*\\]$)))",end:"((?<=--)$|^\\p{Blank}*$)",name:"markup.block.listing.asciidoc",patterns:[{captures:{0:{patterns:[{include:"#block-attribute-inner"}]}},match:"^\\[(listing)((?:,|#|\\.|%)([^,\\]]+))*\\]$"},{include:"#block-title"},{begin:"^(-{4,})\\s*$",comment:"listing block",end:"^(\\1)$"},{begin:"^(-{2})\\s*$",comment:"open block",end:"^(\\1)$"},{include:"#inlines"}]}]},"literal-paragraph":{patterns:[{begin:"(?=(?>(?:^\\[(literal)((?:,|#|\\.|%)[^\\]]+)*\\]$)))",end:"((?<=--|\\.\\.)$|^\\p{Blank}*$)",name:"markup.block.literal.asciidoc",patterns:[{captures:{0:{patterns:[{include:"#block-attribute-inner"}]}},match:"^\\[(literal)((?:,|#|\\.|%)([^,\\]]+))*\\]$"},{include:"#block-title"},{begin:"^(\\.{4,})$",comment:"literal block",end:"^(\\1)$"},{begin:"^(-{2})\\s*$",comment:"open block",end:"^(\\1)$"},{include:"#inlines"}]},{begin:"^(\\.{4,})$",end:"^(\\1)$",name:"markup.block.literal.asciidoc"}]},mark:{patterns:[{captures:{1:{name:"markup.meta.attribute-list.asciidoc"},2:{name:"markup.mark.asciidoc"},3:{name:"punctuation.definition.asciidoc"},5:{name:"punctuation.definition.asciidoc"}},match:"(?<!\\\\\\\\)(\\[[^\\]]+?\\])((##)(.+?)(##))",name:"markup.mark.unconstrained.asciidoc"},{captures:{1:{name:"markup.highlight.asciidoc"},2:{name:"punctuation.definition.asciidoc"},4:{name:"punctuation.definition.asciidoc"}},match:"(?<!\\\\\\\\)((##)(.+?)(##))",name:"markup.mark.unconstrained.asciidoc"},{captures:{1:{name:"markup.meta.attribute-list.asciidoc"},2:{name:"markup.mark.asciidoc"},3:{name:"punctuation.definition.asciidoc"},5:{name:"punctuation.definition.asciidoc"}},match:"(?<![\\\\;:\\p{Word}#])(\\[[^\\]]+?\\])((#)(\\S|\\S.*?\\S)(#)(?!\\p{Word}))",name:"markup.mark.constrained.asciidoc"},{captures:{1:{name:"markup.meta.attribute-list.asciidoc"},2:{name:"markup.highlight.asciidoc"},3:{name:"punctuation.definition.asciidoc"},5:{name:"punctuation.definition.asciidoc"}},match:"(?<![\\\\;:\\p{Word}#])(\\[[^\\]]+?\\])?((#)(\\S|\\S.*?\\S)(#)(?!\\p{Word}))",name:"markup.mark.constrained.asciidoc"}]},"menu-macro":{patterns:[{captures:{1:{name:"entity.name.function.asciidoc"},2:{name:"markup.link.asciidoc"},3:{name:"string.unquoted.asciidoc"}},match:"(?<!\\\\)(menu):(\\p{Word}|\\p{Word}.*?\\S)\\[\\p{Blank}*(.+?)?\\]",name:"markup.other.menu.asciidoc"}]},monospace:{patterns:[{captures:{1:{name:"markup.meta.attribute-list.asciidoc"},2:{name:"markup.raw.monospace.asciidoc"},3:{name:"punctuation.definition.asciidoc"},5:{name:"punctuation.definition.asciidoc"}},match:"(?<!\\\\)(\\[.+?\\])?((``)(.+?)(``))",name:"markup.monospace.unconstrained.asciidoc"},{captures:{1:{name:"markup.meta.attribute-list.asciidoc"},2:{name:"markup.raw.monospace.asciidoc"},3:{name:"punctuation.definition.asciidoc"},5:{name:"punctuation.definition.asciidoc"}},match:"(?<![\\\\;:\\p{Word}\"'`])(\\[.+?\\])?((`)(\\S|\\S.*?\\S)(`))(?![\\p{Word}\"'`])",name:"markup.monospace.constrained.asciidoc"}]},"open-block":{patterns:[{begin:"^(-{2})$",beginCaptures:{1:{name:"constant.other.symbol.asciidoc"}},end:"^(\\1)$",endCaptures:{1:{name:"constant.other.symbol.asciidoc"}},name:"markup.block.open.asciidoc",patterns:[{include:"$self"}]}]},"passthrough-macro":{patterns:[{captures:{1:{name:"markup.meta.attribute-list.asciidoc"},3:{name:"support.constant.asciidoc"},4:{name:"string.unquoted.asciidoc",patterns:[{include:"text.html.basic"}]},5:{name:"support.constant.asciidoc"}},match:"(?:(?<!\\\\)(\\[([^\\]]+?)\\]))?(?:\\\\{0,2})(?<delim>\\+{2,3}|\\${2})(.*?)(\\k<delim>)",name:"markup.macro.inline.passthrough.asciidoc"},{begin:"(?<!\\\\)(pass:)([a-z,]*)(\\[)",beginCaptures:{1:{name:"entity.name.function.asciidoc"},2:{name:"markup.meta.attribute-list.asciidoc"}},contentName:"string.unquoted.asciidoc",end:"\\]|^$",name:"markup.macro.inline.passthrough.asciidoc",patterns:[{include:"text.html.basic"}]}]},"passthrough-paragraph":{patterns:[{begin:"(?=(?>(?:^\\[(pass)((?:,|#|\\.|%)[^\\]]+)*\\]$)))",end:"((?<=--|\\+\\+)$|^\\p{Blank}*$)",name:"markup.block.passthrough.asciidoc",patterns:[{captures:{0:{name:"markup.heading.asciidoc",patterns:[{include:"#block-attribute-inner"}]}},match:"^\\[(pass)((?:,|#|\\.|%)([^,\\]]+))*\\]$"},{include:"#block-title"},{begin:"^(\\+{4,})\\s*$",comment:"passthrough block",end:"(?<=\\1)",patterns:[{include:"text.html.basic"}]},{begin:"^(-{2})\\s*$",comment:"open block",end:"(?<=\\1)",patterns:[{include:"text.html.basic"}]}]},{begin:"(^\\+{4,}$)",end:"\\1",name:"markup.block.passthrough.asciidoc",patterns:[{include:"text.html.basic"}]}]},"quote-paragraph":{patterns:[{begin:"(?=(?>(?:^\\[(quote|verse)((?:,|#|\\.|%)([^,\\]]+))*\\]$)))",end:'((?<=____|""|--)$|^\\p{Blank}*$)',name:"markup.italic.quotes.asciidoc",patterns:[{captures:{0:{patterns:[{include:"#block-attribute-inner"}]}},match:"^\\[(quote|verse)((?:,|#|\\.|%)([^,\\]]+))*\\]$"},{include:"#block-title"},{include:"#inlines"},{begin:"^([_]{4,})\\s*$",comment:"quotes block",end:"(?<=\\1)",patterns:[{include:"#inlines"},{include:"#list"}]},{begin:'^("{2})\\s*$',comment:"air quotes",end:"(?<=\\1)",patterns:[{include:"#inlines"},{include:"#list"}]},{begin:"^(-{2})\\s*$",comment:"open block",end:"(?<=\\1)$",patterns:[{include:"#inlines"},{include:"#list"}]}]},{begin:'^("")$',end:"^\\1$",name:"markup.italic.quotes.asciidoc",patterns:[{include:"#inlines"},{include:"#list"}]},{begin:"^\\p{Blank}*(>) ",end:"^\\p{Blank}*?$",name:"markup.italic.quotes.asciidoc",patterns:[{include:"#inlines"},{include:"#list"}]}]},"sidebar-paragraph":{patterns:[{begin:"(?=(?>(?:^\\[(sidebar)((?:,|#|\\.|%)[^\\]]+)*\\]$)))",end:"((?<=--|\\*\\*\\*\\*)$|^\\p{Blank}*$)",name:"markup.block.sidebar.asciidoc",patterns:[{captures:{0:{patterns:[{include:"#block-attribute-inner"}]}},match:"^\\[(sidebar)((?:,|#|\\.|%)([^,\\]]+))*\\]$"},{include:"#block-title"},{begin:"^(\\*{4,})$",comment:"sidebar block",end:"^(\\1)$",patterns:[{include:"$self"}]},{begin:"^(-{2})$",comment:"open block",end:"^(\\1)$",patterns:[{include:"$self"}]},{include:"#inlines"}]},{begin:"^(\\*{4,})$",end:"^(\\1)$",name:"markup.block.sidebar.asciidoc",patterns:[{include:"$self"}]}]},"source-asciidoctor":{patterns:[{begin:"(?=(?>(?:^\\[(source)(?:,|#)\\p{Blank}*(?i:(c))((?:,|#)[^\\]]+)*\\]$)))",end:"((?<=--|\\.\\.\\.\\.)$|^\\p{Blank}*$)",name:"markup.code.c.asciidoc",patterns:[{captures:{0:{name:"markup.heading.asciidoc",patterns:[{include:"#block-attribute-inner"}]}},match:"^\\[(source)(?:,|#)\\p{Blank}*(?i:(c))((?:,|#)([^,\\]]+))*\\]$"},{include:"#inlines"},{include:"#block-title"},{begin:"^(-{4,})\\s*$",comment:"listing block",contentName:"source.embedded.c",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.c"}]},{begin:"^(-{2})\\s*$",comment:"open block",contentName:"source.embedded.c",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.c"}]},{begin:"^(\\.{4})\\s*$",comment:"literal block",contentName:"source.embedded.c",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.c"}]}]},{begin:"(?=(?>(?:^\\[(source)(?:,|#)\\p{Blank}*(?i:(clojure))((?:,|#)[^\\]]+)*\\]$)))",end:"((?<=--|\\.\\.\\.\\.)$|^\\p{Blank}*$)",name:"markup.code.clojure.asciidoc",patterns:[{captures:{0:{name:"markup.heading.asciidoc",patterns:[{include:"#block-attribute-inner"}]}},match:"^\\[(source)(?:,|#)\\p{Blank}*(?i:(clojure))((?:,|#)([^,\\]]+))*\\]$"},{include:"#inlines"},{include:"#block-title"},{begin:"^(-{4,})\\s*$",comment:"listing block",contentName:"source.embedded.clojure",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.clojure"}]},{begin:"^(-{2})\\s*$",comment:"open block",contentName:"source.embedded.clojure",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.clojure"}]},{begin:"^(\\.{4})\\s*$",comment:"literal block",contentName:"source.embedded.clojure",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.clojure"}]}]},{begin:"(?=(?>(?:^\\[(source)(?:,|#)\\p{Blank}*(?i:(coffee-?(script)?))((?:,|#)[^\\]]+)*\\]$)))",end:"((?<=--|\\.\\.\\.\\.)$|^\\p{Blank}*$)",name:"markup.code.coffee.asciidoc",patterns:[{captures:{0:{name:"markup.heading.asciidoc",patterns:[{include:"#block-attribute-inner"}]}},match:"^\\[(source)(?:,|#)\\p{Blank}*(?i:(coffee-?(script)?))((?:,|#)([^,\\]]+))*\\]$"},{include:"#inlines"},{include:"#block-title"},{begin:"^(-{4,})\\s*$",comment:"listing block",contentName:"source.embedded.coffee",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.coffee"}]},{begin:"^(-{2})\\s*$",comment:"open block",contentName:"source.embedded.coffee",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.coffee"}]},{begin:"^(\\.{4})\\s*$",comment:"literal block",contentName:"source.embedded.coffee",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.coffee"}]}]},{begin:"(?=(?>(?:^\\[(source)(?:,|#)\\p{Blank}*(?i:(c(pp|\\+\\+)))((?:,|#)[^\\]]+)*\\]$)))",end:"((?<=--|\\.\\.\\.\\.)$|^\\p{Blank}*$)",name:"markup.code.cpp.asciidoc",patterns:[{captures:{0:{name:"markup.heading.asciidoc",patterns:[{include:"#block-attribute-inner"}]}},match:"^\\[(source)(?:,|#)\\p{Blank}*(?i:(c(pp|\\+\\+)))((?:,|#)([^,\\]]+))*\\]$"},{include:"#inlines"},{include:"#block-title"},{begin:"^(-{4,})\\s*$",comment:"listing block",contentName:"source.embedded.cpp",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.cpp"}]},{begin:"^(-{2})\\s*$",comment:"open block",contentName:"source.embedded.cpp",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.cpp"}]},{begin:"^(\\.{4})\\s*$",comment:"literal block",contentName:"source.embedded.cpp",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.cpp"}]}]},{begin:"(?=(?>(?:^\\[(source)(?:,|#)\\p{Blank}*(?i:(css))((?:,|#)[^\\]]+)*\\]$)))",end:"((?<=--|\\.\\.\\.\\.)$|^\\p{Blank}*$)",name:"markup.code.css.asciidoc",patterns:[{captures:{0:{name:"markup.heading.asciidoc",patterns:[{include:"#block-attribute-inner"}]}},match:"^\\[(source)(?:,|#)\\p{Blank}*(?i:(css))((?:,|#)([^,\\]]+))*\\]$"},{include:"#inlines"},{include:"#block-title"},{begin:"^(-{4,})\\s*$",comment:"listing block",contentName:"source.embedded.css",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.css"}]},{begin:"^(-{2})\\s*$",comment:"open block",contentName:"source.embedded.css",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.css"}]},{begin:"^(\\.{4})\\s*$",comment:"literal block",contentName:"source.embedded.css",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.css"}]}]},{begin:"(?=(?>(?:^\\[(source)(?:,|#)\\p{Blank}*(?i:(cs(harp)?))((?:,|#)[^\\]]+)*\\]$)))",end:"((?<=--|\\.\\.\\.\\.)$|^\\p{Blank}*$)",name:"markup.code.cs.asciidoc",patterns:[{captures:{0:{name:"markup.heading.asciidoc",patterns:[{include:"#block-attribute-inner"}]}},match:"^\\[(source)(?:,|#)\\p{Blank}*(?i:(cs(harp)?))((?:,|#)([^,\\]]+))*\\]$"},{include:"#inlines"},{include:"#block-title"},{begin:"^(-{4,})\\s*$",comment:"listing block",contentName:"source.embedded.cs",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.cs"}]},{begin:"^(-{2})\\s*$",comment:"open block",contentName:"source.embedded.cs",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.cs"}]},{begin:"^(\\.{4})\\s*$",comment:"literal block",contentName:"source.embedded.cs",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.cs"}]}]},{begin:"(?=(?>(?:^\\[(source)(?:,|#)\\p{Blank}*(?i:(diff|patch|rej))((?:,|#)[^\\]]+)*\\]$)))",end:"((?<=--|\\.\\.\\.\\.)$|^\\p{Blank}*$)",name:"markup.code.diff.asciidoc",patterns:[{captures:{0:{name:"markup.heading.asciidoc",patterns:[{include:"#block-attribute-inner"}]}},match:"^\\[(source)(?:,|#)\\p{Blank}*(?i:(diff|patch|rej))((?:,|#)([^,\\]]+))*\\]$"},{include:"#inlines"},{include:"#block-title"},{begin:"^(-{4,})\\s*$",comment:"listing block",contentName:"source.embedded.diff",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.diff"}]},{begin:"^(-{2})\\s*$",comment:"open block",contentName:"source.embedded.diff",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.diff"}]},{begin:"^(\\.{4})\\s*$",comment:"literal block",contentName:"source.embedded.diff",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.diff"}]}]},{begin:"(?=(?>(?:^\\[(source)(?:,|#)\\p{Blank}*(?i:(docker(file)?))((?:,|#)[^\\]]+)*\\]$)))",end:"((?<=--|\\.\\.\\.\\.)$|^\\p{Blank}*$)",name:"markup.code.dockerfile.asciidoc",patterns:[{captures:{0:{name:"markup.heading.asciidoc",patterns:[{include:"#block-attribute-inner"}]}},match:"^\\[(source)(?:,|#)\\p{Blank}*(?i:(docker(file)?))((?:,|#)([^,\\]]+))*\\]$"},{include:"#inlines"},{include:"#block-title"},{begin:"^(-{4,})\\s*$",comment:"listing block",contentName:"source.embedded.dockerfile",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.dockerfile"}]},{begin:"^(-{2})\\s*$",comment:"open block",contentName:"source.embedded.dockerfile",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.dockerfile"}]},{begin:"^(\\.{4})\\s*$",comment:"literal block",contentName:"source.embedded.dockerfile",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.dockerfile"}]}]},{begin:"(?=(?>(?:^\\[(source)(?:,|#)\\p{Blank}*(?i:(elixir))((?:,|#)[^\\]]+)*\\]$)))",end:"((?<=--|\\.\\.\\.\\.)$|^\\p{Blank}*$)",name:"markup.code.elixir.asciidoc",patterns:[{captures:{0:{name:"markup.heading.asciidoc",patterns:[{include:"#block-attribute-inner"}]}},match:"^\\[(source)(?:,|#)\\p{Blank}*(?i:(elixir))((?:,|#)([^,\\]]+))*\\]$"},{include:"#inlines"},{include:"#block-title"},{begin:"^(-{4,})\\s*$",comment:"listing block",contentName:"source.embedded.elixir",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.elixir"}]},{begin:"^(-{2})\\s*$",comment:"open block",contentName:"source.embedded.elixir",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.elixir"}]},{begin:"^(\\.{4})\\s*$",comment:"literal block",contentName:"source.embedded.elixir",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.elixir"}]}]},{begin:"(?=(?>(?:^\\[(source)(?:,|#)\\p{Blank}*(?i:(elm))((?:,|#)[^\\]]+)*\\]$)))",end:"((?<=--|\\.\\.\\.\\.)$|^\\p{Blank}*$)",name:"markup.code.elm.asciidoc",patterns:[{captures:{0:{name:"markup.heading.asciidoc",patterns:[{include:"#block-attribute-inner"}]}},match:"^\\[(source)(?:,|#)\\p{Blank}*(?i:(elm))((?:,|#)([^,\\]]+))*\\]$"},{include:"#inlines"},{include:"#block-title"},{begin:"^(-{4,})\\s*$",comment:"listing block",contentName:"source.embedded.elm",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.elm"}]},{begin:"^(-{2})\\s*$",comment:"open block",contentName:"source.embedded.elm",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.elm"}]},{begin:"^(\\.{4})\\s*$",comment:"literal block",contentName:"source.embedded.elm",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.elm"}]}]},{begin:"(?=(?>(?:^\\[(source)(?:,|#)\\p{Blank}*(?i:(erlang))((?:,|#)[^\\]]+)*\\]$)))",end:"((?<=--|\\.\\.\\.\\.)$|^\\p{Blank}*$)",name:"markup.code.erlang.asciidoc",patterns:[{captures:{0:{name:"markup.heading.asciidoc",patterns:[{include:"#block-attribute-inner"}]}},match:"^\\[(source)(?:,|#)\\p{Blank}*(?i:(erlang))((?:,|#)([^,\\]]+))*\\]$"},{include:"#inlines"},{include:"#block-title"},{begin:"^(-{4,})\\s*$",comment:"listing block",contentName:"source.embedded.erlang",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.erlang"}]},{begin:"^(-{2})\\s*$",comment:"open block",contentName:"source.embedded.erlang",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.erlang"}]},{begin:"^(\\.{4})\\s*$",comment:"literal block",contentName:"source.embedded.erlang",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.erlang"}]}]},{begin:"(?=(?>(?:^\\[(source)(?:,|#)\\p{Blank}*(?i:(go(lang)?))((?:,|#)[^\\]]+)*\\]$)))",end:"((?<=--|\\.\\.\\.\\.)$|^\\p{Blank}*$)",name:"markup.code.go.asciidoc",patterns:[{captures:{0:{name:"markup.heading.asciidoc",patterns:[{include:"#block-attribute-inner"}]}},match:"^\\[(source)(?:,|#)\\p{Blank}*(?i:(go(lang)?))((?:,|#)([^,\\]]+))*\\]$"},{include:"#inlines"},{include:"#block-title"},{begin:"^(-{4,})\\s*$",comment:"listing block",contentName:"source.embedded.go",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.go"}]},{begin:"^(-{2})\\s*$",comment:"open block",contentName:"source.embedded.go",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.go"}]},{begin:"^(\\.{4})\\s*$",comment:"literal block",contentName:"source.embedded.go",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.go"}]}]},{begin:"(?=(?>(?:^\\[(source)(?:,|#)\\p{Blank}*(?i:(groovy))((?:,|#)[^\\]]+)*\\]$)))",end:"((?<=--|\\.\\.\\.\\.)$|^\\p{Blank}*$)",name:"markup.code.groovy.asciidoc",patterns:[{captures:{0:{name:"markup.heading.asciidoc",patterns:[{include:"#block-attribute-inner"}]}},match:"^\\[(source)(?:,|#)\\p{Blank}*(?i:(groovy))((?:,|#)([^,\\]]+))*\\]$"},{include:"#inlines"},{include:"#block-title"},{begin:"^(-{4,})\\s*$",comment:"listing block",contentName:"source.embedded.groovy",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.groovy"}]},{begin:"^(-{2})\\s*$",comment:"open block",contentName:"source.embedded.groovy",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.groovy"}]},{begin:"^(\\.{4})\\s*$",comment:"literal block",contentName:"source.embedded.groovy",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.groovy"}]}]},{begin:"(?=(?>(?:^\\[(source)(?:,|#)\\p{Blank}*(?i:(haskell))((?:,|#)[^\\]]+)*\\]$)))",end:"((?<=--|\\.\\.\\.\\.)$|^\\p{Blank}*$)",name:"markup.code.haskell.asciidoc",patterns:[{captures:{0:{name:"markup.heading.asciidoc",patterns:[{include:"#block-attribute-inner"}]}},match:"^\\[(source)(?:,|#)\\p{Blank}*(?i:(haskell))((?:,|#)([^,\\]]+))*\\]$"},{include:"#inlines"},{include:"#block-title"},{begin:"^(-{4,})\\s*$",comment:"listing block",contentName:"source.embedded.haskell",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.haskell"}]},{begin:"^(-{2})\\s*$",comment:"open block",contentName:"source.embedded.haskell",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.haskell"}]},{begin:"^(\\.{4})\\s*$",comment:"literal block",contentName:"source.embedded.haskell",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.haskell"}]}]},{begin:"(?=(?>(?:^\\[(source)(?:,|#)\\p{Blank}*(?i:(html))((?:,|#)[^\\]]+)*\\]$)))",end:"((?<=--|\\.\\.\\.\\.)$|^\\p{Blank}*$)",name:"markup.code.html.basic.asciidoc",patterns:[{captures:{0:{name:"markup.heading.asciidoc",patterns:[{include:"#block-attribute-inner"}]}},match:"^\\[(source)(?:,|#)\\p{Blank}*(?i:(html))((?:,|#)([^,\\]]+))*\\]$"},{include:"#inlines"},{include:"#block-title"},{begin:"^(-{4,})\\s*$",comment:"listing block",contentName:"text.embedded.html.basic",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"text.html.basic"}]},{begin:"^(-{2})\\s*$",comment:"open block",contentName:"text.embedded.html.basic",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"text.html.basic"}]},{begin:"^(\\.{4})\\s*$",comment:"literal block",contentName:"text.embedded.html.basic",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"text.html.basic"}]}]},{begin:"(?=(?>(?:^\\[(source)(?:,|#)\\p{Blank}*(?i:(java))((?:,|#)[^\\]]+)*\\]$)))",end:"((?<=--|\\.\\.\\.\\.)$|^\\p{Blank}*$)",name:"markup.code.java.asciidoc",patterns:[{captures:{0:{name:"markup.heading.asciidoc",patterns:[{include:"#block-attribute-inner"}]}},match:"^\\[(source)(?:,|#)\\p{Blank}*(?i:(java))((?:,|#)([^,\\]]+))*\\]$"},{include:"#inlines"},{include:"#block-title"},{begin:"^(-{4,})\\s*$",comment:"listing block",contentName:"source.embedded.java",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.java"}]},{begin:"^(-{2})\\s*$",comment:"open block",contentName:"source.embedded.java",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.java"}]},{begin:"^(\\.{4})\\s*$",comment:"literal block",contentName:"source.embedded.java",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.java"}]}]},{begin:"(?=(?>(?:^\\[(source)(?:,|#)\\p{Blank}*(?i:(javascript|js))((?:,|#)[^\\]]+)*\\]$)))",end:"((?<=--|\\.\\.\\.\\.)$|^\\p{Blank}*$)",name:"markup.code.js.asciidoc",patterns:[{captures:{0:{name:"markup.heading.asciidoc",patterns:[{include:"#block-attribute-inner"}]}},match:"^\\[(source)(?:,|#)\\p{Blank}*(?i:(javascript|js))((?:,|#)([^,\\]]+))*\\]$"},{include:"#inlines"},{include:"#block-title"},{begin:"^(-{4,})\\s*$",comment:"listing block",contentName:"source.embedded.js",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.js"}]},{begin:"^(-{2})\\s*$",comment:"open block",contentName:"source.embedded.js",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.js"}]},{begin:"^(\\.{4})\\s*$",comment:"literal block",contentName:"source.embedded.js",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.js"}]}]},{begin:"(?=(?>(?:^\\[(source)(?:,|#)\\p{Blank}*(?i:(json))((?:,|#)[^\\]]+)*\\]$)))",end:"((?<=--|\\.\\.\\.\\.)$|^\\p{Blank}*$)",name:"markup.code.json.asciidoc",patterns:[{captures:{0:{name:"markup.heading.asciidoc",patterns:[{include:"#block-attribute-inner"}]}},match:"^\\[(source)(?:,|#)\\p{Blank}*(?i:(json))((?:,|#)([^,\\]]+))*\\]$"},{include:"#inlines"},{include:"#block-title"},{begin:"^(-{4,})\\s*$",comment:"listing block",contentName:"source.embedded.json",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.json"}]},{begin:"^(-{2})\\s*$",comment:"open block",contentName:"source.embedded.json",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.json"}]},{begin:"^(\\.{4})\\s*$",comment:"literal block",contentName:"source.embedded.json",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.json"}]}]},{begin:"(?=(?>(?:^\\[(source)(?:,|#)\\p{Blank}*(?i:(jsx))((?:,|#)[^\\]]+)*\\]$)))",end:"((?<=--|\\.\\.\\.\\.)$|^\\p{Blank}*$)",name:"markup.code.js.jsx.asciidoc",patterns:[{captures:{0:{name:"markup.heading.asciidoc",patterns:[{include:"#block-attribute-inner"}]}},match:"^\\[(source)(?:,|#)\\p{Blank}*(?i:(jsx))((?:,|#)([^,\\]]+))*\\]$"},{include:"#inlines"},{include:"#block-title"},{begin:"^(-{4,})\\s*$",comment:"listing block",contentName:"source.embedded.js.jsx",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.js.jsx"}]},{begin:"^(-{2})\\s*$",comment:"open block",contentName:"source.embedded.js.jsx",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.js.jsx"}]},{begin:"^(\\.{4})\\s*$",comment:"literal block",contentName:"source.embedded.js.jsx",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.js.jsx"}]}]},{begin:"(?=(?>(?:^\\[(source)(?:,|#)\\p{Blank}*(?i:(julia))((?:,|#)[^\\]]+)*\\]$)))",end:"((?<=--|\\.\\.\\.\\.)$|^\\p{Blank}*$)",name:"markup.code.julia.asciidoc",patterns:[{captures:{0:{name:"markup.heading.asciidoc",patterns:[{include:"#block-attribute-inner"}]}},match:"^\\[(source)(?:,|#)\\p{Blank}*(?i:(julia))((?:,|#)([^,\\]]+))*\\]$"},{include:"#inlines"},{include:"#block-title"},{begin:"^(-{4,})\\s*$",comment:"listing block",contentName:"source.embedded.julia",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.julia"}]},{begin:"^(-{2})\\s*$",comment:"open block",contentName:"source.embedded.julia",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.julia"}]},{begin:"^(\\.{4})\\s*$",comment:"literal block",contentName:"source.embedded.julia",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.julia"}]}]},{begin:"(?=(?>(?:^\\[(source)(?:,|#)\\p{Blank}*(?i:(kotlin|kts?))((?:,|#)[^\\]]+)*\\]$)))",end:"((?<=--|\\.\\.\\.\\.)$|^\\p{Blank}*$)",name:"markup.code.kotlin.asciidoc",patterns:[{captures:{0:{name:"markup.heading.asciidoc",patterns:[{include:"#block-attribute-inner"}]}},match:"^\\[(source)(?:,|#)\\p{Blank}*(?i:(kotlin|kts?))((?:,|#)([^,\\]]+))*\\]$"},{include:"#inlines"},{include:"#block-title"},{begin:"^(-{4,})\\s*$",comment:"listing block",contentName:"source.embedded.kotlin",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.kotlin"}]},{begin:"^(-{2})\\s*$",comment:"open block",contentName:"source.embedded.kotlin",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.kotlin"}]},{begin:"^(\\.{4})\\s*$",comment:"literal block",contentName:"source.embedded.kotlin",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.kotlin"}]}]},{begin:"(?=(?>(?:^\\[(source)(?:,|#)\\p{Blank}*(?i:(less))((?:,|#)[^\\]]+)*\\]$)))",end:"((?<=--|\\.\\.\\.\\.)$|^\\p{Blank}*$)",name:"markup.code.css.less.asciidoc",patterns:[{captures:{0:{name:"markup.heading.asciidoc",patterns:[{include:"#block-attribute-inner"}]}},match:"^\\[(source)(?:,|#)\\p{Blank}*(?i:(less))((?:,|#)([^,\\]]+))*\\]$"},{include:"#inlines"},{include:"#block-title"},{begin:"^(-{4,})\\s*$",comment:"listing block",contentName:"source.embedded.css.less",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.css.less"}]},{begin:"^(-{2})\\s*$",comment:"open block",contentName:"source.embedded.css.less",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.css.less"}]},{begin:"^(\\.{4})\\s*$",comment:"literal block",contentName:"source.embedded.css.less",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.css.less"}]}]},{begin:"(?=(?>(?:^\\[(source)(?:,|#)\\p{Blank}*(?i:(make(file)?))((?:,|#)[^\\]]+)*\\]$)))",end:"((?<=--|\\.\\.\\.\\.)$|^\\p{Blank}*$)",name:"markup.code.makefile.asciidoc",patterns:[{captures:{0:{name:"markup.heading.asciidoc",patterns:[{include:"#block-attribute-inner"}]}},match:"^\\[(source)(?:,|#)\\p{Blank}*(?i:(make(file)?))((?:,|#)([^,\\]]+))*\\]$"},{include:"#inlines"},{include:"#block-title"},{begin:"^(-{4,})\\s*$",comment:"listing block",contentName:"source.embedded.makefile",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.makefile"}]},{begin:"^(-{2})\\s*$",comment:"open block",contentName:"source.embedded.makefile",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.makefile"}]},{begin:"^(\\.{4})\\s*$",comment:"literal block",contentName:"source.embedded.makefile",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.makefile"}]}]},{begin:"(?=(?>(?:^\\[(source)(?:,|#)\\p{Blank}*(?i:(markdown|mdown|md))((?:,|#)[^\\]]+)*\\]$)))",end:"((?<=--|\\.\\.\\.\\.)$|^\\p{Blank}*$)",name:"markup.code.gfm.asciidoc",patterns:[{captures:{0:{name:"markup.heading.asciidoc",patterns:[{include:"#block-attribute-inner"}]}},match:"^\\[(source)(?:,|#)\\p{Blank}*(?i:(markdown|mdown|md))((?:,|#)([^,\\]]+))*\\]$"},{include:"#inlines"},{include:"#block-title"},{begin:"^(-{4,})\\s*$",comment:"listing block",contentName:"source.embedded.gfm",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.gfm"}]},{begin:"^(-{2})\\s*$",comment:"open block",contentName:"source.embedded.gfm",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.gfm"}]},{begin:"^(\\.{4})\\s*$",comment:"literal block",contentName:"source.embedded.gfm",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.gfm"}]}]},{begin:"(?=(?>(?:^\\[(source)(?:,|#)\\p{Blank}*(?i:(mustache))((?:,|#)[^\\]]+)*\\]$)))",end:"((?<=--|\\.\\.\\.\\.)$|^\\p{Blank}*$)",name:"markup.code.html.mustache.asciidoc",patterns:[{captures:{0:{name:"markup.heading.asciidoc",patterns:[{include:"#block-attribute-inner"}]}},match:"^\\[(source)(?:,|#)\\p{Blank}*(?i:(mustache))((?:,|#)([^,\\]]+))*\\]$"},{include:"#inlines"},{include:"#block-title"},{begin:"^(-{4,})\\s*$",comment:"listing block",contentName:"text.embedded.html.mustache",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"text.html.mustache"}]},{begin:"^(-{2})\\s*$",comment:"open block",contentName:"text.embedded.html.mustache",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"text.html.mustache"}]},{begin:"^(\\.{4})\\s*$",comment:"literal block",contentName:"text.embedded.html.mustache",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"text.html.mustache"}]}]},{begin:"(?=(?>(?:^\\[(source)(?:,|#)\\p{Blank}*(?i:(objc|objective-c))((?:,|#)[^\\]]+)*\\]$)))",end:"((?<=--|\\.\\.\\.\\.)$|^\\p{Blank}*$)",name:"markup.code.objc.asciidoc",patterns:[{captures:{0:{name:"markup.heading.asciidoc",patterns:[{include:"#block-attribute-inner"}]}},match:"^\\[(source)(?:,|#)\\p{Blank}*(?i:(objc|objective-c))((?:,|#)([^,\\]]+))*\\]$"},{include:"#inlines"},{include:"#block-title"},{begin:"^(-{4,})\\s*$",comment:"listing block",contentName:"source.embedded.objc",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.objc"}]},{begin:"^(-{2})\\s*$",comment:"open block",contentName:"source.embedded.objc",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.objc"}]},{begin:"^(\\.{4})\\s*$",comment:"literal block",contentName:"source.embedded.objc",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.objc"}]}]},{begin:"(?=(?>(?:^\\[(source)(?:,|#)\\p{Blank}*(?i:(ocaml))((?:,|#)[^\\]]+)*\\]$)))",end:"((?<=--|\\.\\.\\.\\.)$|^\\p{Blank}*$)",name:"markup.code.ocaml.asciidoc",patterns:[{captures:{0:{name:"markup.heading.asciidoc",patterns:[{include:"#block-attribute-inner"}]}},match:"^\\[(source)(?:,|#)\\p{Blank}*(?i:(ocaml))((?:,|#)([^,\\]]+))*\\]$"},{include:"#inlines"},{include:"#block-title"},{begin:"^(-{4,})\\s*$",comment:"listing block",contentName:"source.embedded.ocaml",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.ocaml"}]},{begin:"^(-{2})\\s*$",comment:"open block",contentName:"source.embedded.ocaml",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.ocaml"}]},{begin:"^(\\.{4})\\s*$",comment:"literal block",contentName:"source.embedded.ocaml",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.ocaml"}]}]},{begin:"(?=(?>(?:^\\[(source)(?:,|#)\\p{Blank}*(?i:(perl))((?:,|#)[^\\]]+)*\\]$)))",end:"((?<=--|\\.\\.\\.\\.)$|^\\p{Blank}*$)",name:"markup.code.perl.asciidoc",patterns:[{captures:{0:{name:"markup.heading.asciidoc",patterns:[{include:"#block-attribute-inner"}]}},match:"^\\[(source)(?:,|#)\\p{Blank}*(?i:(perl))((?:,|#)([^,\\]]+))*\\]$"},{include:"#inlines"},{include:"#block-title"},{begin:"^(-{4,})\\s*$",comment:"listing block",contentName:"source.embedded.perl",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.perl"}]},{begin:"^(-{2})\\s*$",comment:"open block",contentName:"source.embedded.perl",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.perl"}]},{begin:"^(\\.{4})\\s*$",comment:"literal block",contentName:"source.embedded.perl",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.perl"}]}]},{begin:"(?=(?>(?:^\\[(source)(?:,|#)\\p{Blank}*(?i:(perl6))((?:,|#)[^\\]]+)*\\]$)))",end:"((?<=--|\\.\\.\\.\\.)$|^\\p{Blank}*$)",name:"markup.code.perl6.asciidoc",patterns:[{captures:{0:{name:"markup.heading.asciidoc",patterns:[{include:"#block-attribute-inner"}]}},match:"^\\[(source)(?:,|#)\\p{Blank}*(?i:(perl6))((?:,|#)([^,\\]]+))*\\]$"},{include:"#inlines"},{include:"#block-title"},{begin:"^(-{4,})\\s*$",comment:"listing block",contentName:"source.embedded.perl6",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.perl6"}]},{begin:"^(-{2})\\s*$",comment:"open block",contentName:"source.embedded.perl6",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.perl6"}]},{begin:"^(\\.{4})\\s*$",comment:"literal block",contentName:"source.embedded.perl6",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.perl6"}]}]},{begin:"(?=(?>(?:^\\[(source)(?:,|#)\\p{Blank}*(?i:(php))((?:,|#)[^\\]]+)*\\]$)))",end:"((?<=--|\\.\\.\\.\\.)$|^\\p{Blank}*$)",name:"markup.code.html.php.asciidoc",patterns:[{captures:{0:{name:"markup.heading.asciidoc",patterns:[{include:"#block-attribute-inner"}]}},match:"^\\[(source)(?:,|#)\\p{Blank}*(?i:(php))((?:,|#)([^,\\]]+))*\\]$"},{include:"#inlines"},{include:"#block-title"},{begin:"^(-{4,})\\s*$",comment:"listing block",contentName:"text.embedded.html.php",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"text.html.php"}]},{begin:"^(-{2})\\s*$",comment:"open block",contentName:"text.embedded.html.php",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"text.html.php"}]},{begin:"^(\\.{4})\\s*$",comment:"literal block",contentName:"text.embedded.html.php",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"text.html.php"}]}]},{begin:"(?=(?>(?:^\\[(source)(?:,|#)\\p{Blank}*(?i:(properties))((?:,|#)[^\\]]+)*\\]$)))",end:"((?<=--|\\.\\.\\.\\.)$|^\\p{Blank}*$)",name:"markup.code.asciidoc.properties.asciidoc",patterns:[{captures:{0:{name:"markup.heading.asciidoc",patterns:[{include:"#block-attribute-inner"}]}},match:"^\\[(source)(?:,|#)\\p{Blank}*(?i:(properties))((?:,|#)([^,\\]]+))*\\]$"},{include:"#inlines"},{include:"#block-title"},{begin:"^(-{4,})\\s*$",comment:"listing block",contentName:"source.embedded.asciidoc.properties",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.asciidoc.properties"}]},{begin:"^(-{2})\\s*$",comment:"open block",contentName:"source.embedded.asciidoc.properties",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.asciidoc.properties"}]},{begin:"^(\\.{4})\\s*$",comment:"literal block",contentName:"source.embedded.asciidoc.properties",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.asciidoc.properties"}]}]},{begin:"(?=(?>(?:^\\[(source)(?:,|#)\\p{Blank}*(?i:(py(thon)?))((?:,|#)[^\\]]+)*\\]$)))",end:"((?<=--|\\.\\.\\.\\.)$|^\\p{Blank}*$)",name:"markup.code.python.asciidoc",patterns:[{captures:{0:{name:"markup.heading.asciidoc",patterns:[{include:"#block-attribute-inner"}]}},match:"^\\[(source)(?:,|#)\\p{Blank}*(?i:(py(thon)?))((?:,|#)([^,\\]]+))*\\]$"},{include:"#inlines"},{include:"#block-title"},{begin:"^(-{4,})\\s*$",comment:"listing block",contentName:"source.embedded.python",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.python"}]},{begin:"^(-{2})\\s*$",comment:"open block",contentName:"source.embedded.python",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.python"}]},{begin:"^(\\.{4})\\s*$",comment:"literal block",contentName:"source.embedded.python",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.python"}]}]},{begin:"(?=(?>(?:^\\[(source)(?:,|#)\\p{Blank}*(?i:(r))((?:,|#)[^\\]]+)*\\]$)))",end:"((?<=--|\\.\\.\\.\\.)$|^\\p{Blank}*$)",name:"markup.code.r.asciidoc",patterns:[{captures:{0:{name:"markup.heading.asciidoc",patterns:[{include:"#block-attribute-inner"}]}},match:"^\\[(source)(?:,|#)\\p{Blank}*(?i:(r))((?:,|#)([^,\\]]+))*\\]$"},{include:"#inlines"},{include:"#block-title"},{begin:"^(-{4,})\\s*$",comment:"listing block",contentName:"source.embedded.r",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.r"}]},{begin:"^(-{2})\\s*$",comment:"open block",contentName:"source.embedded.r",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.r"}]},{begin:"^(\\.{4})\\s*$",comment:"literal block",contentName:"source.embedded.r",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.r"}]}]},{begin:"(?=(?>(?:^\\[(source)(?:,|#)\\p{Blank}*(?i:(ruby|rb))((?:,|#)[^\\]]+)*\\]$)))",end:"((?<=--|\\.\\.\\.\\.)$|^\\p{Blank}*$)",name:"markup.code.ruby.asciidoc",patterns:[{captures:{0:{name:"markup.heading.asciidoc",patterns:[{include:"#block-attribute-inner"}]}},match:"^\\[(source)(?:,|#)\\p{Blank}*(?i:(ruby|rb))((?:,|#)([^,\\]]+))*\\]$"},{include:"#inlines"},{include:"#block-title"},{begin:"^(-{4,})\\s*$",comment:"listing block",contentName:"source.embedded.ruby",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.ruby"}]},{begin:"^(-{2})\\s*$",comment:"open block",contentName:"source.embedded.ruby",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.ruby"}]},{begin:"^(\\.{4})\\s*$",comment:"literal block",contentName:"source.embedded.ruby",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.ruby"}]}]},{begin:"(?=(?>(?:^\\[(source)(?:,|#)\\p{Blank}*(?i:(rust|rs))((?:,|#)[^\\]]+)*\\]$)))",end:"((?<=--|\\.\\.\\.\\.)$|^\\p{Blank}*$)",name:"markup.code.rust.asciidoc",patterns:[{captures:{0:{name:"markup.heading.asciidoc",patterns:[{include:"#block-attribute-inner"}]}},match:"^\\[(source)(?:,|#)\\p{Blank}*(?i:(rust|rs))((?:,|#)([^,\\]]+))*\\]$"},{include:"#inlines"},{include:"#block-title"},{begin:"^(-{4,})\\s*$",comment:"listing block",contentName:"source.embedded.rust",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.rust"}]},{begin:"^(-{2})\\s*$",comment:"open block",contentName:"source.embedded.rust",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.rust"}]},{begin:"^(\\.{4})\\s*$",comment:"literal block",contentName:"source.embedded.rust",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.rust"}]}]},{begin:"(?=(?>(?:^\\[(source)(?:,|#)\\p{Blank}*(?i:(sass))((?:,|#)[^\\]]+)*\\]$)))",end:"((?<=--|\\.\\.\\.\\.)$|^\\p{Blank}*$)",name:"markup.code.sass.asciidoc",patterns:[{captures:{0:{name:"markup.heading.asciidoc",patterns:[{include:"#block-attribute-inner"}]}},match:"^\\[(source)(?:,|#)\\p{Blank}*(?i:(sass))((?:,|#)([^,\\]]+))*\\]$"},{include:"#inlines"},{include:"#block-title"},{begin:"^(-{4,})\\s*$",comment:"listing block",contentName:"source.embedded.sass",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.sass"}]},{begin:"^(-{2})\\s*$",comment:"open block",contentName:"source.embedded.sass",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.sass"}]},{begin:"^(\\.{4})\\s*$",comment:"literal block",contentName:"source.embedded.sass",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.sass"}]}]},{begin:"(?=(?>(?:^\\[(source)(?:,|#)\\p{Blank}*(?i:(scala))((?:,|#)[^\\]]+)*\\]$)))",end:"((?<=--|\\.\\.\\.\\.)$|^\\p{Blank}*$)",name:"markup.code.scala.asciidoc",patterns:[{captures:{0:{name:"markup.heading.asciidoc",patterns:[{include:"#block-attribute-inner"}]}},match:"^\\[(source)(?:,|#)\\p{Blank}*(?i:(scala))((?:,|#)([^,\\]]+))*\\]$"},{include:"#inlines"},{include:"#block-title"},{begin:"^(-{4,})\\s*$",comment:"listing block",contentName:"source.embedded.scala",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.scala"}]},{begin:"^(-{2})\\s*$",comment:"open block",contentName:"source.embedded.scala",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.scala"}]},{begin:"^(\\.{4})\\s*$",comment:"literal block",contentName:"source.embedded.scala",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.scala"}]}]},{begin:"(?=(?>(?:^\\[(source)(?:,|#)\\p{Blank}*(?i:(scss))((?:,|#)[^\\]]+)*\\]$)))",end:"((?<=--|\\.\\.\\.\\.)$|^\\p{Blank}*$)",name:"markup.code.css.scss.asciidoc",patterns:[{captures:{0:{name:"markup.heading.asciidoc",patterns:[{include:"#block-attribute-inner"}]}},match:"^\\[(source)(?:,|#)\\p{Blank}*(?i:(scss))((?:,|#)([^,\\]]+))*\\]$"},{include:"#inlines"},{include:"#block-title"},{begin:"^(-{4,})\\s*$",comment:"listing block",contentName:"source.embedded.css.scss",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.css.scss"}]},{begin:"^(-{2})\\s*$",comment:"open block",contentName:"source.embedded.css.scss",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.css.scss"}]},{begin:"^(\\.{4})\\s*$",comment:"literal block",contentName:"source.embedded.css.scss",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.css.scss"}]}]},{begin:"(?=(?>(?:^\\[(source)(?:,|#)\\p{Blank}*(?i:(sh|bash|shell))((?:,|#)[^\\]]+)*\\]$)))",end:"((?<=--|\\.\\.\\.\\.)$|^\\p{Blank}*$)",name:"markup.code.shell.asciidoc",patterns:[{captures:{0:{name:"markup.heading.asciidoc",patterns:[{include:"#block-attribute-inner"}]}},match:"^\\[(source)(?:,|#)\\p{Blank}*(?i:(sh|bash|shell))((?:,|#)([^,\\]]+))*\\]$"},{include:"#inlines"},{include:"#block-title"},{begin:"^(-{4,})\\s*$",comment:"listing block",contentName:"source.embedded.shell",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.shell"}]},{begin:"^(-{2})\\s*$",comment:"open block",contentName:"source.embedded.shell",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.shell"}]},{begin:"^(\\.{4})\\s*$",comment:"literal block",contentName:"source.embedded.shell",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.shell"}]}]},{begin:"(?=(?>(?:^\\[(source)(?:,|#)\\p{Blank}*(?i:(sql))((?:,|#)[^\\]]+)*\\]$)))",end:"((?<=--|\\.\\.\\.\\.)$|^\\p{Blank}*$)",name:"markup.code.sql.asciidoc",patterns:[{captures:{0:{name:"markup.heading.asciidoc",patterns:[{include:"#block-attribute-inner"}]}},match:"^\\[(source)(?:,|#)\\p{Blank}*(?i:(sql))((?:,|#)([^,\\]]+))*\\]$"},{include:"#inlines"},{include:"#block-title"},{begin:"^(-{4,})\\s*$",comment:"listing block",contentName:"source.embedded.sql",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.sql"}]},{begin:"^(-{2})\\s*$",comment:"open block",contentName:"source.embedded.sql",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.sql"}]},{begin:"^(\\.{4})\\s*$",comment:"literal block",contentName:"source.embedded.sql",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.sql"}]}]},{begin:"(?=(?>(?:^\\[(source)(?:,|#)\\p{Blank}*(?i:(swift))((?:,|#)[^\\]]+)*\\]$)))",end:"((?<=--|\\.\\.\\.\\.)$|^\\p{Blank}*$)",name:"markup.code.swift.asciidoc",patterns:[{captures:{0:{name:"markup.heading.asciidoc",patterns:[{include:"#block-attribute-inner"}]}},match:"^\\[(source)(?:,|#)\\p{Blank}*(?i:(swift))((?:,|#)([^,\\]]+))*\\]$"},{include:"#inlines"},{include:"#block-title"},{begin:"^(-{4,})\\s*$",comment:"listing block",contentName:"source.embedded.swift",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.swift"}]},{begin:"^(-{2})\\s*$",comment:"open block",contentName:"source.embedded.swift",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.swift"}]},{begin:"^(\\.{4})\\s*$",comment:"literal block",contentName:"source.embedded.swift",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.swift"}]}]},{begin:"(?=(?>(?:^\\[(source)(?:,|#)\\p{Blank}*(?i:(toml))((?:,|#)[^\\]]+)*\\]$)))",end:"((?<=--|\\.\\.\\.\\.)$|^\\p{Blank}*$)",name:"markup.code.toml.asciidoc",patterns:[{captures:{0:{name:"markup.heading.asciidoc",patterns:[{include:"#block-attribute-inner"}]}},match:"^\\[(source)(?:,|#)\\p{Blank}*(?i:(toml))((?:,|#)([^,\\]]+))*\\]$"},{include:"#inlines"},{include:"#block-title"},{begin:"^(-{4,})\\s*$",comment:"listing block",contentName:"source.embedded.toml",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.toml"}]},{begin:"^(-{2})\\s*$",comment:"open block",contentName:"source.embedded.toml",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.toml"}]},{begin:"^(\\.{4})\\s*$",comment:"literal block",contentName:"source.embedded.toml",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.toml"}]}]},{begin:"(?=(?>(?:^\\[(source)(?:,|#)\\p{Blank}*(?i:(typescript|ts))((?:,|#)[^\\]]+)*\\]$)))",end:"((?<=--|\\.\\.\\.\\.)$|^\\p{Blank}*$)",name:"markup.code.ts.asciidoc",patterns:[{captures:{0:{name:"markup.heading.asciidoc",patterns:[{include:"#block-attribute-inner"}]}},match:"^\\[(source)(?:,|#)\\p{Blank}*(?i:(typescript|ts))((?:,|#)([^,\\]]+))*\\]$"},{include:"#inlines"},{include:"#block-title"},{begin:"^(-{4,})\\s*$",comment:"listing block",contentName:"source.embedded.ts",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.ts"}]},{begin:"^(-{2})\\s*$",comment:"open block",contentName:"source.embedded.ts",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.ts"}]},{begin:"^(\\.{4})\\s*$",comment:"literal block",contentName:"source.embedded.ts",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.ts"}]}]},{begin:"(?=(?>(?:^\\[(source)(?:,|#)\\p{Blank}*(?i:(xml))((?:,|#)[^\\]]+)*\\]$)))",end:"((?<=--|\\.\\.\\.\\.)$|^\\p{Blank}*$)",name:"markup.code.xml.asciidoc",patterns:[{captures:{0:{name:"markup.heading.asciidoc",patterns:[{include:"#block-attribute-inner"}]}},match:"^\\[(source)(?:,|#)\\p{Blank}*(?i:(xml))((?:,|#)([^,\\]]+))*\\]$"},{include:"#inlines"},{include:"#block-title"},{begin:"^(-{4,})\\s*$",comment:"listing block",contentName:"text.embedded.xml",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"text.xml"}]},{begin:"^(-{2})\\s*$",comment:"open block",contentName:"text.embedded.xml",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"text.xml"}]},{begin:"^(\\.{4})\\s*$",comment:"literal block",contentName:"text.embedded.xml",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"text.xml"}]}]},{begin:"(?=(?>(?:^\\[(source)(?:,|#)\\p{Blank}*(?i:(ya?ml))((?:,|#)[^\\]]+)*\\]$)))",end:"((?<=--|\\.\\.\\.\\.)$|^\\p{Blank}*$)",name:"markup.code.yaml.asciidoc",patterns:[{captures:{0:{name:"markup.heading.asciidoc",patterns:[{include:"#block-attribute-inner"}]}},match:"^\\[(source)(?:,|#)\\p{Blank}*(?i:(ya?ml))((?:,|#)([^,\\]]+))*\\]$"},{include:"#inlines"},{include:"#block-title"},{begin:"^(-{4,})\\s*$",comment:"listing block",contentName:"source.embedded.yaml",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.yaml"}]},{begin:"^(-{2})\\s*$",comment:"open block",contentName:"source.embedded.yaml",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.yaml"}]},{begin:"^(\\.{4})\\s*$",comment:"literal block",contentName:"source.embedded.yaml",end:"^(\\1)$",patterns:[{include:"#block-callout"},{include:"#include-directive"},{include:"source.yaml"}]}]},{begin:"(?=(?>(?:^\\[(source)((?:,|#)[^\\]]+)*\\]$)))",end:"((?<=--|\\.\\.\\.\\.)$|^\\p{Blank}*$)",patterns:[{captures:{0:{name:"markup.heading.asciidoc",patterns:[{include:"#block-attribute-inner"}]}},match:"^\\[(source)((?:,|#)([^,\\]]+))*\\]$"},{include:"#inlines"},{include:"#block-title"},{begin:"^(-{4,})\\s*$",comment:"listing block",end:"^(\\1)$",name:"markup.raw.asciidoc",patterns:[{include:"#block-callout"},{include:"#include-directive"}]},{begin:"^(-{2})\\s*$",comment:"open block",end:"^(\\1)$",name:"markup.raw.asciidoc",patterns:[{include:"#block-callout"},{include:"#include-directive"}]},{begin:"^(\\.{4})\\s*$",comment:"literal block",end:"^(\\1)$",name:"markup.raw.asciidoc",patterns:[{include:"#block-callout"},{include:"#include-directive"}]}]},{begin:"^(-{4,})\\s*$",beginCaptures:{0:{name:"support.asciidoc"}},end:"^(\\1)$",endCaptures:{0:{name:"support.asciidoc"}},name:"markup.raw.asciidoc",patterns:[{include:"#block-callout"},{include:"#include-directive"}]}]},"source-markdown":{patterns:[{begin:"^\\s*(`{3,})\\s*(?i:(c))\\s*$",beginCaptures:{0:{name:"support.asciidoc"}},contentName:"source.embedded.c",end:"^\\s*\\1\\s*$",endCaptures:{0:{name:"support.asciidoc"}},name:"markup.code.c.asciidoc",patterns:[{include:"#block-callout"},{include:"source.c"}]},{begin:"^\\s*(`{3,})\\s*(?i:(clojure))\\s*$",beginCaptures:{0:{name:"support.asciidoc"}},contentName:"source.embedded.clojure",end:"^\\s*\\1\\s*$",endCaptures:{0:{name:"support.asciidoc"}},name:"markup.code.clojure.asciidoc",patterns:[{include:"#block-callout"},{include:"source.clojure"}]},{begin:"^\\s*(`{3,})\\s*(?i:(coffee-?(script)?))\\s*$",beginCaptures:{0:{name:"support.asciidoc"}},contentName:"source.embedded.coffee",end:"^\\s*\\1\\s*$",endCaptures:{0:{name:"support.asciidoc"}},name:"markup.code.coffee.asciidoc",patterns:[{include:"#block-callout"},{include:"source.coffee"}]},{begin:"^\\s*(`{3,})\\s*(?i:(c(pp|\\+\\+)))\\s*$",beginCaptures:{0:{name:"support.asciidoc"}},contentName:"source.embedded.cpp",end:"^\\s*\\1\\s*$",endCaptures:{0:{name:"support.asciidoc"}},name:"markup.code.cpp.asciidoc",patterns:[{include:"#block-callout"},{include:"source.cpp"}]},{begin:"^\\s*(`{3,})\\s*(?i:(css))\\s*$",beginCaptures:{0:{name:"support.asciidoc"}},contentName:"source.embedded.css",end:"^\\s*\\1\\s*$",endCaptures:{0:{name:"support.asciidoc"}},name:"markup.code.css.asciidoc",patterns:[{include:"#block-callout"},{include:"source.css"}]},{begin:"^\\s*(`{3,})\\s*(?i:(cs(harp)?))\\s*$",beginCaptures:{0:{name:"support.asciidoc"}},contentName:"source.embedded.cs",end:"^\\s*\\1\\s*$",endCaptures:{0:{name:"support.asciidoc"}},name:"markup.code.cs.asciidoc",patterns:[{include:"#block-callout"},{include:"source.cs"}]},{begin:"^\\s*(`{3,})\\s*(?i:(diff|patch|rej))\\s*$",beginCaptures:{0:{name:"support.asciidoc"}},contentName:"source.embedded.diff",end:"^\\s*\\1\\s*$",endCaptures:{0:{name:"support.asciidoc"}},name:"markup.code.diff.asciidoc",patterns:[{include:"#block-callout"},{include:"source.diff"}]},{begin:"^\\s*(`{3,})\\s*(?i:(docker(file)?))\\s*$",beginCaptures:{0:{name:"support.asciidoc"}},contentName:"source.embedded.dockerfile",end:"^\\s*\\1\\s*$",endCaptures:{0:{name:"support.asciidoc"}},name:"markup.code.dockerfile.asciidoc",patterns:[{include:"#block-callout"},{include:"source.dockerfile"}]},{begin:"^\\s*(`{3,})\\s*(?i:(elixir))\\s*$",beginCaptures:{0:{name:"support.asciidoc"}},contentName:"source.embedded.elixir",end:"^\\s*\\1\\s*$",endCaptures:{0:{name:"support.asciidoc"}},name:"markup.code.elixir.asciidoc",patterns:[{include:"#block-callout"},{include:"source.elixir"}]},{begin:"^\\s*(`{3,})\\s*(?i:(elm))\\s*$",beginCaptures:{0:{name:"support.asciidoc"}},contentName:"source.embedded.elm",end:"^\\s*\\1\\s*$",endCaptures:{0:{name:"support.asciidoc"}},name:"markup.code.elm.asciidoc",patterns:[{include:"#block-callout"},{include:"source.elm"}]},{begin:"^\\s*(`{3,})\\s*(?i:(erlang))\\s*$",beginCaptures:{0:{name:"support.asciidoc"}},contentName:"source.embedded.erlang",end:"^\\s*\\1\\s*$",endCaptures:{0:{name:"support.asciidoc"}},name:"markup.code.erlang.asciidoc",patterns:[{include:"#block-callout"},{include:"source.erlang"}]},{begin:"^\\s*(`{3,})\\s*(?i:(go(lang)?))\\s*$",beginCaptures:{0:{name:"support.asciidoc"}},contentName:"source.embedded.go",end:"^\\s*\\1\\s*$",endCaptures:{0:{name:"support.asciidoc"}},name:"markup.code.go.asciidoc",patterns:[{include:"#block-callout"},{include:"source.go"}]},{begin:"^\\s*(`{3,})\\s*(?i:(groovy))\\s*$",beginCaptures:{0:{name:"support.asciidoc"}},contentName:"source.embedded.groovy",end:"^\\s*\\1\\s*$",endCaptures:{0:{name:"support.asciidoc"}},name:"markup.code.groovy.asciidoc",patterns:[{include:"#block-callout"},{include:"source.groovy"}]},{begin:"^\\s*(`{3,})\\s*(?i:(haskell))\\s*$",beginCaptures:{0:{name:"support.asciidoc"}},contentName:"source.embedded.haskell",end:"^\\s*\\1\\s*$",endCaptures:{0:{name:"support.asciidoc"}},name:"markup.code.haskell.asciidoc",patterns:[{include:"#block-callout"},{include:"source.haskell"}]},{begin:"^\\s*(`{3,})\\s*(?i:(html))\\s*$",beginCaptures:{0:{name:"support.asciidoc"}},contentName:"text.embedded.html.basic",end:"^\\s*\\1\\s*$",endCaptures:{0:{name:"support.asciidoc"}},name:"markup.code.html.basic.asciidoc",patterns:[{include:"#block-callout"},{include:"text.html.basic"}]},{begin:"^\\s*(`{3,})\\s*(?i:(java))\\s*$",beginCaptures:{0:{name:"support.asciidoc"}},contentName:"source.embedded.java",end:"^\\s*\\1\\s*$",endCaptures:{0:{name:"support.asciidoc"}},name:"markup.code.java.asciidoc",patterns:[{include:"#block-callout"},{include:"source.java"}]},{begin:"^\\s*(`{3,})\\s*(?i:(javascript|js))\\s*$",beginCaptures:{0:{name:"support.asciidoc"}},contentName:"source.embedded.js",end:"^\\s*\\1\\s*$",endCaptures:{0:{name:"support.asciidoc"}},name:"markup.code.js.asciidoc",patterns:[{include:"#block-callout"},{include:"source.js"}]},{begin:"^\\s*(`{3,})\\s*(?i:(json))\\s*$",beginCaptures:{0:{name:"support.asciidoc"}},contentName:"source.embedded.json",end:"^\\s*\\1\\s*$",endCaptures:{0:{name:"support.asciidoc"}},name:"markup.code.json.asciidoc",patterns:[{include:"#block-callout"},{include:"source.json"}]},{begin:"^\\s*(`{3,})\\s*(?i:(jsx))\\s*$",beginCaptures:{0:{name:"support.asciidoc"}},contentName:"source.embedded.js.jsx",end:"^\\s*\\1\\s*$",endCaptures:{0:{name:"support.asciidoc"}},name:"markup.code.js.jsx.asciidoc",patterns:[{include:"#block-callout"},{include:"source.js.jsx"}]},{begin:"^\\s*(`{3,})\\s*(?i:(julia))\\s*$",beginCaptures:{0:{name:"support.asciidoc"}},contentName:"source.embedded.julia",end:"^\\s*\\1\\s*$",endCaptures:{0:{name:"support.asciidoc"}},name:"markup.code.julia.asciidoc",patterns:[{include:"#block-callout"},{include:"source.julia"}]},{begin:"^\\s*(`{3,})\\s*(?i:(kotlin|kts?))\\s*$",beginCaptures:{0:{name:"support.asciidoc"}},contentName:"source.embedded.kotlin",end:"^\\s*\\1\\s*$",endCaptures:{0:{name:"support.asciidoc"}},name:"markup.code.kotlin.asciidoc",patterns:[{include:"#block-callout"},{include:"source.kotlin"}]},{begin:"^\\s*(`{3,})\\s*(?i:(less))\\s*$",beginCaptures:{0:{name:"support.asciidoc"}},contentName:"source.embedded.css.less",end:"^\\s*\\1\\s*$",endCaptures:{0:{name:"support.asciidoc"}},name:"markup.code.css.less.asciidoc",patterns:[{include:"#block-callout"},{include:"source.css.less"}]},{begin:"^\\s*(`{3,})\\s*(?i:(make(file)?))\\s*$",beginCaptures:{0:{name:"support.asciidoc"}},contentName:"source.embedded.makefile",end:"^\\s*\\1\\s*$",endCaptures:{0:{name:"support.asciidoc"}},name:"markup.code.makefile.asciidoc",patterns:[{include:"#block-callout"},{include:"source.makefile"}]},{begin:"^\\s*(`{3,})\\s*(?i:(markdown|mdown|md))\\s*$",beginCaptures:{0:{name:"support.asciidoc"}},contentName:"source.embedded.gfm",end:"^\\s*\\1\\s*$",endCaptures:{0:{name:"support.asciidoc"}},name:"markup.code.gfm.asciidoc",patterns:[{include:"#block-callout"},{include:"source.gfm"}]},{begin:"^\\s*(`{3,})\\s*(?i:(mustache))\\s*$",beginCaptures:{0:{name:"support.asciidoc"}},contentName:"text.embedded.html.mustache",end:"^\\s*\\1\\s*$",endCaptures:{0:{name:"support.asciidoc"}},name:"markup.code.html.mustache.asciidoc",patterns:[{include:"#block-callout"},{include:"text.html.mustache"}]},{begin:"^\\s*(`{3,})\\s*(?i:(objc|objective-c))\\s*$",beginCaptures:{0:{name:"support.asciidoc"}},contentName:"source.embedded.objc",end:"^\\s*\\1\\s*$",endCaptures:{0:{name:"support.asciidoc"}},name:"markup.code.objc.asciidoc",patterns:[{include:"#block-callout"},{include:"source.objc"}]},{begin:"^\\s*(`{3,})\\s*(?i:(ocaml))\\s*$",beginCaptures:{0:{name:"support.asciidoc"}},contentName:"source.embedded.ocaml",end:"^\\s*\\1\\s*$",endCaptures:{0:{name:"support.asciidoc"}},name:"markup.code.ocaml.asciidoc",patterns:[{include:"#block-callout"},{include:"source.ocaml"}]},{begin:"^\\s*(`{3,})\\s*(?i:(perl))\\s*$",beginCaptures:{0:{name:"support.asciidoc"}},contentName:"source.embedded.perl",end:"^\\s*\\1\\s*$",endCaptures:{0:{name:"support.asciidoc"}},name:"markup.code.perl.asciidoc",patterns:[{include:"#block-callout"},{include:"source.perl"}]},{begin:"^\\s*(`{3,})\\s*(?i:(perl6))\\s*$",beginCaptures:{0:{name:"support.asciidoc"}},contentName:"source.embedded.perl6",end:"^\\s*\\1\\s*$",endCaptures:{0:{name:"support.asciidoc"}},name:"markup.code.perl6.asciidoc",patterns:[{include:"#block-callout"},{include:"source.perl6"}]},{begin:"^\\s*(`{3,})\\s*(?i:(php))\\s*$",beginCaptures:{0:{name:"support.asciidoc"}},contentName:"text.embedded.html.php",end:"^\\s*\\1\\s*$",endCaptures:{0:{name:"support.asciidoc"}},name:"markup.code.html.php.asciidoc",patterns:[{include:"#block-callout"},{include:"text.html.php"}]},{begin:"^\\s*(`{3,})\\s*(?i:(properties))\\s*$",beginCaptures:{0:{name:"support.asciidoc"}},contentName:"source.embedded.asciidoc.properties",end:"^\\s*\\1\\s*$",endCaptures:{0:{name:"support.asciidoc"}},name:"markup.code.asciidoc.properties.asciidoc",patterns:[{include:"#block-callout"},{include:"source.asciidoc.properties"}]},{begin:"^\\s*(`{3,})\\s*(?i:(py(thon)?))\\s*$",beginCaptures:{0:{name:"support.asciidoc"}},contentName:"source.embedded.python",end:"^\\s*\\1\\s*$",endCaptures:{0:{name:"support.asciidoc"}},name:"markup.code.python.asciidoc",patterns:[{include:"#block-callout"},{include:"source.python"}]},{begin:"^\\s*(`{3,})\\s*(?i:(r))\\s*$",beginCaptures:{0:{name:"support.asciidoc"}},contentName:"source.embedded.r",end:"^\\s*\\1\\s*$",endCaptures:{0:{name:"support.asciidoc"}},name:"markup.code.r.asciidoc",patterns:[{include:"#block-callout"},{include:"source.r"}]},{begin:"^\\s*(`{3,})\\s*(?i:(ruby|rb))\\s*$",beginCaptures:{0:{name:"support.asciidoc"}},contentName:"source.embedded.ruby",end:"^\\s*\\1\\s*$",endCaptures:{0:{name:"support.asciidoc"}},name:"markup.code.ruby.asciidoc",patterns:[{include:"#block-callout"},{include:"source.ruby"}]},{begin:"^\\s*(`{3,})\\s*(?i:(rust|rs))\\s*$",beginCaptures:{0:{name:"support.asciidoc"}},contentName:"source.embedded.rust",end:"^\\s*\\1\\s*$",endCaptures:{0:{name:"support.asciidoc"}},name:"markup.code.rust.asciidoc",patterns:[{include:"#block-callout"},{include:"source.rust"}]},{begin:"^\\s*(`{3,})\\s*(?i:(sass))\\s*$",beginCaptures:{0:{name:"support.asciidoc"}},contentName:"source.embedded.sass",end:"^\\s*\\1\\s*$",endCaptures:{0:{name:"support.asciidoc"}},name:"markup.code.sass.asciidoc",patterns:[{include:"#block-callout"},{include:"source.sass"}]},{begin:"^\\s*(`{3,})\\s*(?i:(scala))\\s*$",beginCaptures:{0:{name:"support.asciidoc"}},contentName:"source.embedded.scala",end:"^\\s*\\1\\s*$",endCaptures:{0:{name:"support.asciidoc"}},name:"markup.code.scala.asciidoc",patterns:[{include:"#block-callout"},{include:"source.scala"}]},{begin:"^\\s*(`{3,})\\s*(?i:(scss))\\s*$",beginCaptures:{0:{name:"support.asciidoc"}},contentName:"source.embedded.css.scss",end:"^\\s*\\1\\s*$",endCaptures:{0:{name:"support.asciidoc"}},name:"markup.code.css.scss.asciidoc",patterns:[{include:"#block-callout"},{include:"source.css.scss"}]},{begin:"^\\s*(`{3,})\\s*(?i:(sh|bash|shell))\\s*$",beginCaptures:{0:{name:"support.asciidoc"}},contentName:"source.embedded.shell",end:"^\\s*\\1\\s*$",endCaptures:{0:{name:"support.asciidoc"}},name:"markup.code.shell.asciidoc",patterns:[{include:"#block-callout"},{include:"source.shell"}]},{begin:"^\\s*(`{3,})\\s*(?i:(sql))\\s*$",beginCaptures:{0:{name:"support.asciidoc"}},contentName:"source.embedded.sql",end:"^\\s*\\1\\s*$",endCaptures:{0:{name:"support.asciidoc"}},name:"markup.code.sql.asciidoc",patterns:[{include:"#block-callout"},{include:"source.sql"}]},{begin:"^\\s*(`{3,})\\s*(?i:(swift))\\s*$",beginCaptures:{0:{name:"support.asciidoc"}},contentName:"source.embedded.swift",end:"^\\s*\\1\\s*$",endCaptures:{0:{name:"support.asciidoc"}},name:"markup.code.swift.asciidoc",patterns:[{include:"#block-callout"},{include:"source.swift"}]},{begin:"^\\s*(`{3,})\\s*(?i:(toml))\\s*$",beginCaptures:{0:{name:"support.asciidoc"}},contentName:"source.embedded.toml",end:"^\\s*\\1\\s*$",endCaptures:{0:{name:"support.asciidoc"}},name:"markup.code.toml.asciidoc",patterns:[{include:"#block-callout"},{include:"source.toml"}]},{begin:"^\\s*(`{3,})\\s*(?i:(typescript|ts))\\s*$",beginCaptures:{0:{name:"support.asciidoc"}},contentName:"source.embedded.ts",end:"^\\s*\\1\\s*$",endCaptures:{0:{name:"support.asciidoc"}},name:"markup.code.ts.asciidoc",patterns:[{include:"#block-callout"},{include:"source.ts"}]},{begin:"^\\s*(`{3,})\\s*(?i:(xml))\\s*$",beginCaptures:{0:{name:"support.asciidoc"}},contentName:"text.embedded.xml",end:"^\\s*\\1\\s*$",endCaptures:{0:{name:"support.asciidoc"}},name:"markup.code.xml.asciidoc",patterns:[{include:"#block-callout"},{include:"text.xml"}]},{begin:"^\\s*(`{3,})\\s*(?i:(ya?ml))\\s*$",beginCaptures:{0:{name:"support.asciidoc"}},contentName:"source.embedded.yaml",end:"^\\s*\\1\\s*$",endCaptures:{0:{name:"support.asciidoc"}},name:"markup.code.yaml.asciidoc",patterns:[{include:"#block-callout"},{include:"source.yaml"}]},{begin:"^\\s*(`{3,}).*$",beginCaptures:{0:{name:"support.asciidoc"}},end:"^\\s*\\1\\s*$",endCaptures:{0:{name:"support.asciidoc"}},name:"markup.raw.asciidoc",patterns:[{include:"#block-callout"}]}]},"source-paragraphs":{patterns:[{include:"#source-asciidoctor"},{include:"#source-markdown"}]},"stem-macro":{patterns:[{begin:"(?<!\\\\)(stem|(?:latex|ascii)math):([a-z,]*)(\\[)",beginCaptures:{1:{name:"entity.name.function.asciidoc"},2:{name:"markup.meta.attribute-list.asciidoc"}},contentName:"string.unquoted.asciidoc",end:"\\]|^$",name:"markup.macro.inline.stem.asciidoc"}]},strong:{patterns:[{captures:{1:{name:"markup.meta.attribute-list.asciidoc"},2:{name:"markup.bold.asciidoc"},3:{name:"punctuation.definition.asciidoc"},5:{name:"punctuation.definition.asciidoc"}},match:"(?<!\\\\\\\\)(\\[.+?\\])?((\\*\\*)(.+?)(\\*\\*))",name:"markup.strong.unconstrained.asciidoc"},{captures:{1:{name:"markup.meta.attribute-list.asciidoc"},2:{name:"markup.bold.asciidoc"},3:{name:"punctuation.definition.asciidoc"},5:{name:"punctuation.definition.asciidoc"}},match:"(?<![\\\\;:\\p{Word}\\*])(\\[.+?\\])?((\\*)(\\S|\\S.*?\\S)(\\*)(?!\\p{Word}))",name:"markup.strong.constrained.asciidoc"}]},subscript:{patterns:[{captures:{1:{name:"markup.meta.sub.attribute-list.asciidoc"},2:{name:"markup.sub.subscript.asciidoc"},3:{name:"punctuation.definition.asciidoc"},5:{name:"punctuation.definition.asciidoc"}},match:"(?<!\\\\)(\\[.+?\\])?((~)(\\S+?)(~))",name:"markup.subscript.asciidoc"}]},superscript:{patterns:[{captures:{1:{name:"markup.meta.super.attribute-list.asciidoc"},2:{name:"markup.super.superscript.asciidoc"},3:{name:"punctuation.definition.asciidoc"},5:{name:"punctuation.definition.asciidoc"}},match:"(?<!\\\\)(\\[.+?\\])?((\\^)(\\S+?)(\\^))",name:"markup.superscript.asciidoc"}]},"table-csv":{patterns:[{begin:"^(,===)$",beginCaptures:{0:{name:"markup.table.delimiter.asciidoc"}},contentName:"string.unquoted.asciidoc",end:"^(\\1)$",endCaptures:{0:{name:"markup.table.delimiter.asciidoc"}},name:"markup.table.csv.asciidoc",patterns:[{include:"text.csv"},{captures:{0:{name:"markup.table.cell.delimiter.asciidoc"}},comment:"cell separator",match:","},{include:"#general-block-macro"}]}]},"table-dsv":{patterns:[{begin:"^(:===)$",beginCaptures:{0:{name:"markup.table.delimiter.asciidoc"}},contentName:"string.unquoted.asciidoc",end:"^(\\1)$",endCaptures:{0:{name:"markup.table.delimiter.asciidoc"}},name:"markup.table.dsv.asciidoc",patterns:[{captures:{0:{name:"markup.table.cell.delimiter.asciidoc"}},comment:"cell separator",match:":"},{include:"#general-block-macro"}]}]},"table-nested":{patterns:[{begin:"^(!===)$",beginCaptures:{0:{name:"markup.table.delimiter.asciidoc"}},contentName:"markup.table.content.asciidoc",end:"^(\\1)$",endCaptures:{0:{name:"markup.table.delimiter.asciidoc"}},name:"markup.table.nested.asciidoc",patterns:[{captures:{1:{name:"markup.meta.attribute-list.asciidoc"},2:{name:"markup.table.cell.delimiter.asciidoc"}},comment:"cell separator and attributes",match:"(^|[^\\p{Blank}\\\\]*)(?<!\\\\)(!)"},{include:"#tables-includes"}]}]},"table-psv":{patterns:[{begin:"^(\\|===)\\s*$",beginCaptures:{0:{name:"markup.table.delimiter.asciidoc"}},contentName:"markup.table.content.asciidoc",end:"^(\\1)\\s*$",endCaptures:{0:{name:"markup.table.delimiter.asciidoc"}},name:"markup.table.asciidoc",patterns:[{captures:{1:{name:"markup.meta.attribute-list.asciidoc"},2:{name:"markup.table.cell.delimiter.asciidoc"}},comment:"cell separator and attributes",match:"(^|[^\\p{Blank}\\\\]*)(?<!\\\\)(\\|)"},{include:"#tables-includes"}]}]},tables:{patterns:[{include:"#table-psv"},{include:"#table-nested"},{include:"#table-csv"},{include:"#table-dsv"}]},"tables-includes":{patterns:[{include:"#comment"},{include:"#callout-list-item"},{include:"#attribute-entry"},{include:"#block-title"},{include:"#explicit-paragraph"},{include:"#section"},{include:"#blocks"},{include:"#list"},{include:"#inlines"},{include:"#line-break"}]},titles:{patterns:[{begin:"^((?:=|#){6})([\\p{Blank}]+)(?=\\S+)",beginCaptures:{1:{name:"markup.heading.marker.asciidoc"},2:{name:"markup.heading.space.asciidoc"}},end:"$",name:"markup.heading.heading-5.asciidoc",patterns:[{include:"$self"}]},{begin:"^((?:=|#){5})([\\p{Blank}]+)(?=\\S+)",beginCaptures:{1:{name:"markup.heading.marker.asciidoc"},2:{name:"markup.heading.space.asciidoc"}},end:"$",name:"markup.heading.heading-4.asciidoc",patterns:[{include:"$self"}]},{begin:"^((?:=|#){4})([\\p{Blank}]+)(?=\\S+)",beginCaptures:{1:{name:"markup.heading.marker.asciidoc"},2:{name:"markup.heading.space.asciidoc"}},end:"$",name:"markup.heading.heading-3.asciidoc",patterns:[{include:"$self"}]},{begin:"^((?:=|#){3})([\\p{Blank}]+)(?=\\S+)",beginCaptures:{1:{name:"markup.heading.marker.asciidoc"},2:{name:"markup.heading.space.asciidoc"}},end:"$",name:"markup.heading.heading-2.asciidoc",patterns:[{include:"$self"}]},{begin:"^((?:=|#){2})([\\p{Blank}]+)(?=\\S+)",beginCaptures:{1:{name:"markup.heading.marker.asciidoc"},2:{name:"markup.heading.space.asciidoc"}},end:"$",name:"markup.heading.heading-1.asciidoc",patterns:[{include:"$self"}]},{begin:"^((?:=|#){1})([\\p{Blank}]+)(?=\\S+)",beginCaptures:{1:{name:"markup.heading.marker.asciidoc"},2:{name:"markup.heading.space.asciidoc"}},end:"$",name:"markup.heading.heading-0.asciidoc",patterns:[{include:"$self"}]}]},"typographic-quotes":{patterns:[{captures:{1:{name:"markup.meta.attribute-list.asciidoc"},3:{name:"punctuation.definition.asciidoc"},5:{name:"punctuation.definition.asciidoc"}},comment:"double-quoted",match:'(?:^|(?<!\\p{Word}|;|:))(\\[([^\\]]+?)\\])?("`)(\\S|\\S.*?\\S)(`")(?!\\p{Word})',name:"markup.italic.quote.typographic-quotes.asciidoc"},{captures:{1:{name:"markup.meta.attribute-list.asciidoc"},3:{name:"punctuation.definition.asciidoc"},5:{name:"punctuation.definition.asciidoc"}},comment:"single-quoted",match:"(?:^|(?<!\\p{Word}|;|:))(\\[([^\\]]+?)\\])?('`)(\\S|\\S.*?\\S)(`')(?!\\p{Word})",name:"markup.italic.quote.typographic-quotes.asciidoc"}]},"xref-macro":{patterns:[{captures:{1:{name:"constant.asciidoc"},2:{name:"markup.meta.attribute-list.asciidoc"},3:{name:"string.unquoted.asciidoc"},4:{name:"constant.asciidoc"}},match:'(?<!\\\\)(?:(<<)([\\p{Word}":./]+,)?(.*?)(>>))',name:"markup.reference.xref.asciidoc"},{begin:'(?<!\\\\)(xref:)([\\p{Word}":.\\/].*?)(\\[)',beginCaptures:{1:{name:"entity.name.function.asciidoc"},2:{name:"markup.meta.attribute-list.asciidoc"}},contentName:"string.unquoted.asciidoc",end:"\\]|^$",name:"markup.reference.xref.asciidoc"}]}},scopeName:"text.asciidoc",embeddedLangs:["html","yaml","csv","c","clojure","coffee","cpp","css","csharp","diff","docker","elixir","elm","erlang","go","groovy","haskell","java","javascript","json","jsx","julia","kotlin","less","make","objective-c","ocaml","perl","python","r","ruby","rust","sass","scala","scss","shellscript","sql","swift","toml","typescript","xml"],aliases:["adoc"]});var P=[...a.default,...i.default,...c.default,...o.default,...s.default,...r.default,...l.default,...d.default,...u.default,...m.default,...p.default,...b.default,...k.default,...g.default,...h.default,...f.default,...y.default,...$.default,...v.default,...w.default,...x.default,...C.default,...N.default,..._.default,...j.default,...A.default,...B.default,...q.default,...S.default,...z.default,...E.default,...Z.default,...O.default,...L.default,...T.default,...F.default,...R.default,...I.default,...G.default,...U.default,...D.default,M]},25253:(e,n,t)=>{t.r(n),t.d(n,{default:()=>a});var a=[Object.freeze({displayName:"YAML",fileTypes:["yaml","yml","rviz","reek","clang-format","yaml-tmlanguage","syntax","sublime-syntax"],firstLineMatch:"^%YAML( ?1.\\d+)?",name:"yaml",patterns:[{include:"#comment"},{include:"#property"},{include:"#directive"},{match:"^---",name:"entity.other.document.begin.yaml"},{match:"^\\.{3}",name:"entity.other.document.end.yaml"},{include:"#node"}],repository:{"block-collection":{patterns:[{include:"#block-sequence"},{include:"#block-mapping"}]},"block-mapping":{patterns:[{include:"#block-pair"}]},"block-node":{patterns:[{include:"#prototype"},{include:"#block-scalar"},{include:"#block-collection"},{include:"#flow-scalar-plain-out"},{include:"#flow-node"}]},"block-pair":{patterns:[{begin:"\\?",beginCaptures:{1:{name:"punctuation.definition.key-value.begin.yaml"}},end:"(?=\\?)|^ *(:)|(:)",endCaptures:{1:{name:"punctuation.separator.key-value.mapping.yaml"},2:{name:"invalid.illegal.expected-newline.yaml"}},name:"meta.block-mapping.yaml",patterns:[{include:"#block-node"}]},{begin:"(?=(?:[^\\s[-?:,\\[\\]{}#&*!|>'\"%@`]]|[?:-]\\S)([^\\s:]|:\\S|\\s+(?![#\\s]))*\\s*:(\\s|$))",end:"(?=\\s*$|\\s+\\#|\\s*:(\\s|$))",patterns:[{include:"#flow-scalar-plain-out-implicit-type"},{begin:"[^\\s[-?:,\\[\\]{}#&*!|>'\"%@`]]|[?:-]\\S",beginCaptures:{0:{name:"entity.name.tag.yaml"}},contentName:"entity.name.tag.yaml",end:"(?=\\s*$|\\s+\\#|\\s*:(\\s|$))",name:"string.unquoted.plain.out.yaml"}]},{match:":(?=\\s|$)",name:"punctuation.separator.key-value.mapping.yaml"}]},"block-scalar":{begin:"(?:(\\|)|(>))([1-9])?([-+])?(.*\\n?)",beginCaptures:{1:{name:"keyword.control.flow.block-scalar.literal.yaml"},2:{name:"keyword.control.flow.block-scalar.folded.yaml"},3:{name:"constant.numeric.indentation-indicator.yaml"},4:{name:"storage.modifier.chomping-indicator.yaml"},5:{patterns:[{include:"#comment"},{match:".+",name:"invalid.illegal.expected-comment-or-newline.yaml"}]}},end:"^(?=\\S)|(?!\\G)",patterns:[{begin:"^([ ]+)(?! )",end:"^(?!\\1|\\s*$)",name:"string.unquoted.block.yaml"}]},"block-sequence":{match:"(-)(?!\\S)",name:"punctuation.definition.block.sequence.item.yaml"},comment:{begin:"(?:(^[ \\t]*)|[ \\t]+)(?=#\\p{Print}*$)",beginCaptures:{1:{name:"punctuation.whitespace.comment.leading.yaml"}},end:"(?!\\G)",patterns:[{begin:"#",beginCaptures:{0:{name:"punctuation.definition.comment.yaml"}},end:"\\n",name:"comment.line.number-sign.yaml"}]},directive:{begin:"^%",beginCaptures:{0:{name:"punctuation.definition.directive.begin.yaml"}},end:"(?=$|[ \\t]+($|#))",name:"meta.directive.yaml",patterns:[{captures:{1:{name:"keyword.other.directive.yaml.yaml"},2:{name:"constant.numeric.yaml-version.yaml"}},match:"\\G(YAML)[ \\t]+(\\d+\\.\\d+)"},{captures:{1:{name:"keyword.other.directive.tag.yaml"},2:{name:"storage.type.tag-handle.yaml"},3:{name:"support.type.tag-prefix.yaml"}},match:"\\G(TAG)(?:[ \\t]+((?:!(?:[0-9A-Za-z\\-]*!)?))(?:[ \\t]+(!(?:%[0-9A-Fa-f]{2}|[0-9A-Za-z\\-#;/?:@&=+$,_.!~*'()\\[\\]])*|(?![,!\\[\\]{}])(?:%[0-9A-Fa-f]{2}|[0-9A-Za-z\\-#;/?:@&=+$,_.!~*'()\\[\\]])+))?)?"},{captures:{1:{name:"support.other.directive.reserved.yaml"},2:{name:"string.unquoted.directive-name.yaml"},3:{name:"string.unquoted.directive-parameter.yaml"}},match:"\\G(\\w+)(?:[ \\t]+(\\w+)(?:[ \\t]+(\\w+))?)?"},{match:"\\S+",name:"invalid.illegal.unrecognized.yaml"}]},"flow-alias":{captures:{1:{name:"keyword.control.flow.alias.yaml"},2:{name:"punctuation.definition.alias.yaml"},3:{name:"variable.other.alias.yaml"},4:{name:"invalid.illegal.character.anchor.yaml"}},match:"((\\*))([^\\s\\[\\]/{/},]+)([^\\s\\]},]\\S*)?"},"flow-collection":{patterns:[{include:"#flow-sequence"},{include:"#flow-mapping"}]},"flow-mapping":{begin:"\\{",beginCaptures:{0:{name:"punctuation.definition.mapping.begin.yaml"}},end:"\\}",endCaptures:{0:{name:"punctuation.definition.mapping.end.yaml"}},name:"meta.flow-mapping.yaml",patterns:[{include:"#prototype"},{match:",",name:"punctuation.separator.mapping.yaml"},{include:"#flow-pair"}]},"flow-node":{patterns:[{include:"#prototype"},{include:"#flow-alias"},{include:"#flow-collection"},{include:"#flow-scalar"}]},"flow-pair":{patterns:[{begin:"\\?",beginCaptures:{0:{name:"punctuation.definition.key-value.begin.yaml"}},end:"(?=[},\\]])",name:"meta.flow-pair.explicit.yaml",patterns:[{include:"#prototype"},{include:"#flow-pair"},{include:"#flow-node"},{begin:":(?=\\s|$|[\\[\\]{},])",beginCaptures:{0:{name:"punctuation.separator.key-value.mapping.yaml"}},end:"(?=[},\\]])",patterns:[{include:"#flow-value"}]}]},{begin:"(?=(?:[^\\s[-?:,\\[\\]{}#&*!|>'\"%@`]]|[?:-][^\\s[\\[\\]{},]])([^\\s:[\\[\\]{},]]|:[^\\s[\\[\\]{},]]|\\s+(?![#\\s]))*\\s*:(\\s|$))",end:"(?=\\s*$|\\s+\\#|\\s*:(\\s|$)|\\s*:[\\[\\]{},]|\\s*[\\[\\]{},])",name:"meta.flow-pair.key.yaml",patterns:[{include:"#flow-scalar-plain-in-implicit-type"},{begin:"[^\\s[-?:,\\[\\]{}#&*!|>'\"%@`]]|[?:-][^\\s[\\[\\]{},]]",beginCaptures:{0:{name:"entity.name.tag.yaml"}},contentName:"entity.name.tag.yaml",end:"(?=\\s*$|\\s+\\#|\\s*:(\\s|$)|\\s*:[\\[\\]{},]|\\s*[\\[\\]{},])",name:"string.unquoted.plain.in.yaml"}]},{include:"#flow-node"},{begin:":(?=\\s|$|[\\[\\]{},])",captures:{0:{name:"punctuation.separator.key-value.mapping.yaml"}},end:"(?=[},\\]])",name:"meta.flow-pair.yaml",patterns:[{include:"#flow-value"}]}]},"flow-scalar":{patterns:[{include:"#flow-scalar-double-quoted"},{include:"#flow-scalar-single-quoted"},{include:"#flow-scalar-plain-in"}]},"flow-scalar-double-quoted":{begin:'"',beginCaptures:{0:{name:"punctuation.definition.string.begin.yaml"}},end:'"',endCaptures:{0:{name:"punctuation.definition.string.end.yaml"}},name:"string.quoted.double.yaml",patterns:[{match:'\\\\([0abtnvfre "/\\\\N_Lp]|x\\d\\d|u\\d{4}|U\\d{8})',name:"constant.character.escape.yaml"},{match:"\\\\\\n",name:"constant.character.escape.double-quoted.newline.yaml"}]},"flow-scalar-plain-in":{patterns:[{include:"#flow-scalar-plain-in-implicit-type"},{begin:"[^\\s[-?:,\\[\\]{}#&*!|>'\"%@`]]|[?:-][^\\s[\\[\\]{},]]",end:"(?=\\s*$|\\s+\\#|\\s*:(\\s|$)|\\s*:[\\[\\]{},]|\\s*[\\[\\]{},])",name:"string.unquoted.plain.in.yaml"}]},"flow-scalar-plain-in-implicit-type":{patterns:[{captures:{1:{name:"constant.language.null.yaml"},2:{name:"constant.language.boolean.yaml"},3:{name:"constant.numeric.integer.yaml"},4:{name:"constant.numeric.float.yaml"},5:{name:"constant.other.timestamp.yaml"},6:{name:"constant.language.value.yaml"},7:{name:"constant.language.merge.yaml"}},match:"(?:(null|Null|NULL|~)|(y|Y|yes|Yes|YES|n|N|no|No|NO|true|True|TRUE|false|False|FALSE|on|On|ON|off|Off|OFF)|((?:[-+]?0b[0-1_]+|[-+]?0[0-7_]+|[-+]?(?:0|[1-9][0-9_]*)|[-+]?0x[0-9a-fA-F_]+|[-+]?[1-9][0-9_]*(?::[0-5]?\\d)+))|((?:[-+]?(?:\\d[0-9_]*)?\\.[0-9.]*(?:[eE][-+]\\d+)?|[-+]?\\d[0-9_]*(?::[0-5]?\\d)+\\.[0-9_]*|[-+]?\\.(?:inf|Inf|INF)|\\.(?:nan|NaN|NAN)))|((?:\\d{4}-\\d{2}-\\d{2}|\\d{4}-\\d{1,2}-\\d{1,2}(?:[Tt]|[ \\t]+)\\d{1,2}:\\d{2}:\\d{2}(?:\\.\\d*)?(?:(?:[ \\t]*)Z|[-+]\\d{1,2}(?::\\d{1,2})?)?))|(=)|(<<))(?:(?=\\s*$|\\s+\\#|\\s*:(\\s|$)|\\s*:[\\[\\]{},]|\\s*[\\[\\]{},]))"}]},"flow-scalar-plain-out":{patterns:[{include:"#flow-scalar-plain-out-implicit-type"},{begin:"[^\\s[-?:,\\[\\]{}#&*!|>'\"%@`]]|[?:-]\\S",end:"(?=\\s*$|\\s+\\#|\\s*:(\\s|$))",name:"string.unquoted.plain.out.yaml"}]},"flow-scalar-plain-out-implicit-type":{patterns:[{captures:{1:{name:"constant.language.null.yaml"},2:{name:"constant.language.boolean.yaml"},3:{name:"constant.numeric.integer.yaml"},4:{name:"constant.numeric.float.yaml"},5:{name:"constant.other.timestamp.yaml"},6:{name:"constant.language.value.yaml"},7:{name:"constant.language.merge.yaml"}},match:"(?:(null|Null|NULL|~)|(y|Y|yes|Yes|YES|n|N|no|No|NO|true|True|TRUE|false|False|FALSE|on|On|ON|off|Off|OFF)|((?:[-+]?0b[0-1_]+|[-+]?0[0-7_]+|[-+]?(?:0|[1-9][0-9_]*)|[-+]?0x[0-9a-fA-F_]+|[-+]?[1-9][0-9_]*(?::[0-5]?\\d)+))|((?:[-+]?(?:\\d[0-9_]*)?\\.[0-9.]*(?:[eE][-+]\\d+)?|[-+]?\\d[0-9_]*(?::[0-5]?\\d)+\\.[0-9_]*|[-+]?\\.(?:inf|Inf|INF)|\\.(?:nan|NaN|NAN)))|((?:\\d{4}-\\d{2}-\\d{2}|\\d{4}-\\d{1,2}-\\d{1,2}(?:[Tt]|[ \\t]+)\\d{1,2}:\\d{2}:\\d{2}(?:\\.\\d*)?(?:(?:[ \\t]*)Z|[-+]\\d{1,2}(?::\\d{1,2})?)?))|(=)|(<<))(?:(?=\\s*$|\\s+\\#|\\s*:(\\s|$)))"}]},"flow-scalar-single-quoted":{begin:"'",beginCaptures:{0:{name:"punctuation.definition.string.begin.yaml"}},end:"'(?!')",endCaptures:{0:{name:"punctuation.definition.string.end.yaml"}},name:"string.quoted.single.yaml",patterns:[{match:"''",name:"constant.character.escape.single-quoted.yaml"}]},"flow-sequence":{begin:"\\[",beginCaptures:{0:{name:"punctuation.definition.sequence.begin.yaml"}},end:"\\]",endCaptures:{0:{name:"punctuation.definition.sequence.end.yaml"}},name:"meta.flow-sequence.yaml",patterns:[{include:"#prototype"},{match:",",name:"punctuation.separator.sequence.yaml"},{include:"#flow-pair"},{include:"#flow-node"}]},"flow-value":{patterns:[{begin:"\\G(?![},\\]])",end:"(?=[},\\]])",name:"meta.flow-pair.value.yaml",patterns:[{include:"#flow-node"}]}]},node:{patterns:[{include:"#block-node"}]},property:{begin:"(?=!|&)",end:"(?!\\G)",name:"meta.property.yaml",patterns:[{captures:{1:{name:"keyword.control.property.anchor.yaml"},2:{name:"punctuation.definition.anchor.yaml"},3:{name:"entity.name.type.anchor.yaml"},4:{name:"invalid.illegal.character.anchor.yaml"}},match:"\\G((&))([^\\s\\[\\]/{/},]+)(\\S+)?"},{match:"\\G(?:!<(?:%[0-9A-Fa-f]{2}|[0-9A-Za-z\\-#;/?:@&=+$,_.!~*'()\\[\\]])+>|(?:!(?:[0-9A-Za-z\\-]*!)?)(?:%[0-9A-Fa-f]{2}|[0-9A-Za-z\\-#;/?:@&=+$_.~*'()])+|!)(?= |\\t|$)",name:"storage.type.tag-handle.yaml"},{match:"\\S+",name:"invalid.illegal.tag-handle.yaml"}]},prototype:{patterns:[{include:"#comment"},{include:"#property"}]}},scopeName:"source.yaml",aliases:["yml"]})]},33034:(e,n,t)=>{t.r(n),t.d(n,{default:()=>a});var a=[Object.freeze({displayName:"Rust",name:"rust",patterns:[{begin:"(<)(\\[)",beginCaptures:{1:{name:"punctuation.brackets.angle.rust"},2:{name:"punctuation.brackets.square.rust"}},comment:"boxed slice literal",end:">",endCaptures:{0:{name:"punctuation.brackets.angle.rust"}},patterns:[{include:"#block-comments"},{include:"#comments"},{include:"#gtypes"},{include:"#lvariables"},{include:"#lifetimes"},{include:"#punctuation"},{include:"#types"}]},{captures:{1:{name:"keyword.operator.macro.dollar.rust"},3:{name:"keyword.other.crate.rust"},4:{name:"entity.name.type.metavariable.rust"},6:{name:"keyword.operator.key-value.rust"},7:{name:"variable.other.metavariable.specifier.rust"}},comment:"macro type metavariables",match:"(\\$)((crate)|([A-Z][A-Za-z0-9_]*))((:)(block|expr|ident|item|lifetime|literal|meta|path?|stmt|tt|ty|vis))?",name:"meta.macro.metavariable.type.rust",patterns:[{include:"#keywords"}]},{captures:{1:{name:"keyword.operator.macro.dollar.rust"},2:{name:"variable.other.metavariable.name.rust"},4:{name:"keyword.operator.key-value.rust"},5:{name:"variable.other.metavariable.specifier.rust"}},comment:"macro metavariables",match:"(\\$)([a-z][A-Za-z0-9_]*)((:)(block|expr|ident|item|lifetime|literal|meta|path?|stmt|tt|ty|vis))?",name:"meta.macro.metavariable.rust",patterns:[{include:"#keywords"}]},{captures:{1:{name:"entity.name.function.macro.rules.rust"},3:{name:"entity.name.function.macro.rust"},4:{name:"entity.name.type.macro.rust"},5:{name:"punctuation.brackets.curly.rust"}},comment:"macro rules",match:"\\b(macro_rules!)\\s+(([a-z0-9_]+)|([A-Z][a-z0-9_]*))\\s+(\\{)",name:"meta.macro.rules.rust"},{captures:{1:{name:"storage.type.rust"},2:{name:"entity.name.module.rust"}},comment:"modules",match:"(mod)\\s+((?:r#(?!crate|[Ss]elf|super))?[a-z][A-Za-z0-9_]*)"},{begin:"\\b(extern)\\s+(crate)",beginCaptures:{1:{name:"storage.type.rust"},2:{name:"keyword.other.crate.rust"}},comment:"external crate imports",end:";",endCaptures:{0:{name:"punctuation.semi.rust"}},name:"meta.import.rust",patterns:[{include:"#block-comments"},{include:"#comments"},{include:"#keywords"},{include:"#punctuation"}]},{begin:"\\b(use)\\s",beginCaptures:{1:{name:"keyword.other.rust"}},comment:"use statements",end:";",endCaptures:{0:{name:"punctuation.semi.rust"}},name:"meta.use.rust",patterns:[{include:"#block-comments"},{include:"#comments"},{include:"#keywords"},{include:"#namespaces"},{include:"#punctuation"},{include:"#types"},{include:"#lvariables"}]},{include:"#block-comments"},{include:"#comments"},{include:"#attributes"},{include:"#lvariables"},{include:"#constants"},{include:"#gtypes"},{include:"#functions"},{include:"#types"},{include:"#keywords"},{include:"#lifetimes"},{include:"#macros"},{include:"#namespaces"},{include:"#punctuation"},{include:"#strings"},{include:"#variables"}],repository:{attributes:{begin:"(#)(!?)(\\[)",beginCaptures:{1:{name:"punctuation.definition.attribute.rust"},3:{name:"punctuation.brackets.attribute.rust"}},comment:"attributes",end:"\\]",endCaptures:{0:{name:"punctuation.brackets.attribute.rust"}},name:"meta.attribute.rust",patterns:[{include:"#block-comments"},{include:"#comments"},{include:"#keywords"},{include:"#lifetimes"},{include:"#punctuation"},{include:"#strings"},{include:"#gtypes"},{include:"#types"}]},"block-comments":{patterns:[{comment:"empty block comments",match:"/\\*\\*/",name:"comment.block.rust"},{begin:"/\\*\\*",comment:"block documentation comments",end:"\\*/",name:"comment.block.documentation.rust",patterns:[{include:"#block-comments"}]},{begin:"/\\*(?!\\*)",comment:"block comments",end:"\\*/",name:"comment.block.rust",patterns:[{include:"#block-comments"}]}]},comments:{patterns:[{captures:{1:{name:"punctuation.definition.comment.rust"}},comment:"documentation comments",match:"(///).*$",name:"comment.line.documentation.rust"},{captures:{1:{name:"punctuation.definition.comment.rust"}},comment:"line comments",match:"(//).*$",name:"comment.line.double-slash.rust"}]},constants:{patterns:[{comment:"ALL CAPS constants",match:"\\b[A-Z]{2}[A-Z0-9_]*\\b",name:"constant.other.caps.rust"},{captures:{1:{name:"storage.type.rust"},2:{name:"constant.other.caps.rust"}},comment:"constant declarations",match:"\\b(const)\\s+([A-Z][A-Za-z0-9_]*)\\b"},{captures:{1:{name:"punctuation.separator.dot.decimal.rust"},2:{name:"keyword.operator.exponent.rust"},3:{name:"keyword.operator.exponent.sign.rust"},4:{name:"constant.numeric.decimal.exponent.mantissa.rust"},5:{name:"entity.name.type.numeric.rust"}},comment:"decimal integers and floats",match:"\\b\\d[\\d_]*(\\.?)[\\d_]*(?:(E|e)([+-]?)([\\d_]+))?(f32|f64|i128|i16|i32|i64|i8|isize|u128|u16|u32|u64|u8|usize)?\\b",name:"constant.numeric.decimal.rust"},{captures:{1:{name:"entity.name.type.numeric.rust"}},comment:"hexadecimal integers",match:"\\b0x[\\da-fA-F_]+(i128|i16|i32|i64|i8|isize|u128|u16|u32|u64|u8|usize)?\\b",name:"constant.numeric.hex.rust"},{captures:{1:{name:"entity.name.type.numeric.rust"}},comment:"octal integers",match:"\\b0o[0-7_]+(i128|i16|i32|i64|i8|isize|u128|u16|u32|u64|u8|usize)?\\b",name:"constant.numeric.oct.rust"},{captures:{1:{name:"entity.name.type.numeric.rust"}},comment:"binary integers",match:"\\b0b[01_]+(i128|i16|i32|i64|i8|isize|u128|u16|u32|u64|u8|usize)?\\b",name:"constant.numeric.bin.rust"},{comment:"booleans",match:"\\b(true|false)\\b",name:"constant.language.bool.rust"}]},escapes:{captures:{1:{name:"constant.character.escape.backslash.rust"},2:{name:"constant.character.escape.bit.rust"},3:{name:"constant.character.escape.unicode.rust"},4:{name:"constant.character.escape.unicode.punctuation.rust"},5:{name:"constant.character.escape.unicode.punctuation.rust"}},comment:"escapes: ASCII, byte, Unicode, quote, regex",match:"(\\\\)(?:(?:(x[0-7][\\da-fA-F])|(u(\\{)[\\da-fA-F]{4,6}(\\}))|.))",name:"constant.character.escape.rust"},functions:{patterns:[{captures:{1:{name:"keyword.other.rust"},2:{name:"punctuation.brackets.round.rust"}},comment:"pub as a function",match:"\\b(pub)(\\()"},{begin:"\\b(fn)\\s+((?:r#(?!crate|[Ss]elf|super))?[A-Za-z0-9_]+)((\\()|(<))",beginCaptures:{1:{name:"keyword.other.fn.rust"},2:{name:"entity.name.function.rust"},4:{name:"punctuation.brackets.round.rust"},5:{name:"punctuation.brackets.angle.rust"}},comment:"function definition",end:"(\\{)|(;)",endCaptures:{1:{name:"punctuation.brackets.curly.rust"},2:{name:"punctuation.semi.rust"}},name:"meta.function.definition.rust",patterns:[{include:"#block-comments"},{include:"#comments"},{include:"#keywords"},{include:"#lvariables"},{include:"#constants"},{include:"#gtypes"},{include:"#functions"},{include:"#lifetimes"},{include:"#macros"},{include:"#namespaces"},{include:"#punctuation"},{include:"#strings"},{include:"#types"},{include:"#variables"}]},{begin:"((?:r#(?!crate|[Ss]elf|super))?[A-Za-z0-9_]+)(\\()",beginCaptures:{1:{name:"entity.name.function.rust"},2:{name:"punctuation.brackets.round.rust"}},comment:"function/method calls, chaining",end:"\\)",endCaptures:{0:{name:"punctuation.brackets.round.rust"}},name:"meta.function.call.rust",patterns:[{include:"#block-comments"},{include:"#comments"},{include:"#attributes"},{include:"#keywords"},{include:"#lvariables"},{include:"#constants"},{include:"#gtypes"},{include:"#functions"},{include:"#lifetimes"},{include:"#macros"},{include:"#namespaces"},{include:"#punctuation"},{include:"#strings"},{include:"#types"},{include:"#variables"}]},{begin:"((?:r#(?!crate|[Ss]elf|super))?[A-Za-z0-9_]+)(?=::<.*>\\()",beginCaptures:{1:{name:"entity.name.function.rust"}},comment:"function/method calls with turbofish",end:"\\)",endCaptures:{0:{name:"punctuation.brackets.round.rust"}},name:"meta.function.call.rust",patterns:[{include:"#block-comments"},{include:"#comments"},{include:"#attributes"},{include:"#keywords"},{include:"#lvariables"},{include:"#constants"},{include:"#gtypes"},{include:"#functions"},{include:"#lifetimes"},{include:"#macros"},{include:"#namespaces"},{include:"#punctuation"},{include:"#strings"},{include:"#types"},{include:"#variables"}]}]},gtypes:{patterns:[{comment:"option types",match:"\\b(Some|None)\\b",name:"entity.name.type.option.rust"},{comment:"result types",match:"\\b(Ok|Err)\\b",name:"entity.name.type.result.rust"}]},interpolations:{captures:{1:{name:"punctuation.definition.interpolation.rust"},2:{name:"punctuation.definition.interpolation.rust"}},comment:"curly brace interpolations",match:'({)[^"{}]*(})',name:"meta.interpolation.rust"},keywords:{patterns:[{comment:"control flow keywords",match:"\\b(await|break|continue|do|else|for|if|loop|match|return|try|while|yield)\\b",name:"keyword.control.rust"},{comment:"storage keywords",match:"\\b(extern|let|macro|mod)\\b",name:"keyword.other.rust storage.type.rust"},{comment:"const keyword",match:"\\b(const)\\b",name:"storage.modifier.rust"},{comment:"type keyword",match:"\\b(type)\\b",name:"keyword.declaration.type.rust storage.type.rust"},{comment:"enum keyword",match:"\\b(enum)\\b",name:"keyword.declaration.enum.rust storage.type.rust"},{comment:"trait keyword",match:"\\b(trait)\\b",name:"keyword.declaration.trait.rust storage.type.rust"},{comment:"struct keyword",match:"\\b(struct)\\b",name:"keyword.declaration.struct.rust storage.type.rust"},{comment:"storage modifiers",match:"\\b(abstract|static)\\b",name:"storage.modifier.rust"},{comment:"other keywords",match:"\\b(as|async|become|box|dyn|move|final|gen|impl|in|override|priv|pub|ref|typeof|union|unsafe|unsized|use|virtual|where)\\b",name:"keyword.other.rust"},{comment:"fn",match:"\\bfn\\b",name:"keyword.other.fn.rust"},{comment:"crate",match:"\\bcrate\\b",name:"keyword.other.crate.rust"},{comment:"mut",match:"\\bmut\\b",name:"storage.modifier.mut.rust"},{comment:"logical operators",match:"(\\^|\\||\\|\\||&&|<<|>>|!)(?!=)",name:"keyword.operator.logical.rust"},{comment:"logical AND, borrow references",match:"&(?![&=])",name:"keyword.operator.borrow.and.rust"},{comment:"assignment operators",match:"(\\+=|-=|\\*=|/=|%=|\\^=|&=|\\|=|<<=|>>=)",name:"keyword.operator.assignment.rust"},{comment:"single equal",match:"(?<![<>])=(?!=|>)",name:"keyword.operator.assignment.equal.rust"},{comment:"comparison operators",match:"(=(=)?(?!>)|!=|<=|(?<!=)>=)",name:"keyword.operator.comparison.rust"},{comment:"math operators",match:"(([+%]|(\\*(?!\\w)))(?!=))|(-(?!>))|(/(?!/))",name:"keyword.operator.math.rust"},{captures:{1:{name:"punctuation.brackets.round.rust"},2:{name:"punctuation.brackets.square.rust"},3:{name:"punctuation.brackets.curly.rust"},4:{name:"keyword.operator.comparison.rust"},5:{name:"punctuation.brackets.round.rust"},6:{name:"punctuation.brackets.square.rust"},7:{name:"punctuation.brackets.curly.rust"}},comment:"less than, greater than (special case)",match:"(?:\\b|(?:(\\))|(\\])|(\\})))[ \\t]+([<>])[ \\t]+(?:\\b|(?:(\\()|(\\[)|(\\{)))"},{comment:"namespace operator",match:"::",name:"keyword.operator.namespace.rust"},{captures:{1:{name:"keyword.operator.dereference.rust"}},comment:"dereference asterisk",match:"(\\*)(?=\\w+)"},{comment:"subpattern binding",match:"@",name:"keyword.operator.subpattern.rust"},{comment:"dot access",match:"\\.(?!\\.)",name:"keyword.operator.access.dot.rust"},{comment:"ranges, range patterns",match:"\\.{2}(=|\\.)?",name:"keyword.operator.range.rust"},{comment:"colon",match:":(?!:)",name:"keyword.operator.key-value.rust"},{comment:"dashrocket, skinny arrow",match:"->|<-",name:"keyword.operator.arrow.skinny.rust"},{comment:"hashrocket, fat arrow",match:"=>",name:"keyword.operator.arrow.fat.rust"},{comment:"dollar macros",match:"\\$",name:"keyword.operator.macro.dollar.rust"},{comment:"question mark operator, questionably sized, macro kleene matcher",match:"\\?",name:"keyword.operator.question.rust"}]},lifetimes:{patterns:[{captures:{1:{name:"punctuation.definition.lifetime.rust"},2:{name:"entity.name.type.lifetime.rust"}},comment:"named lifetime parameters",match:"(['])([a-zA-Z_][0-9a-zA-Z_]*)(?!['])\\b"},{captures:{1:{name:"keyword.operator.borrow.rust"},2:{name:"punctuation.definition.lifetime.rust"},3:{name:"entity.name.type.lifetime.rust"}},comment:"borrowing references to named lifetimes",match:"(\\&)(['])([a-zA-Z_][0-9a-zA-Z_]*)(?!['])\\b"}]},lvariables:{patterns:[{comment:"self",match:"\\b[Ss]elf\\b",name:"variable.language.self.rust"},{comment:"super",match:"\\bsuper\\b",name:"variable.language.super.rust"}]},macros:{patterns:[{captures:{2:{name:"entity.name.function.macro.rust"},3:{name:"entity.name.type.macro.rust"}},comment:"macros",match:"(([a-z_][A-Za-z0-9_]*!)|([A-Z_][A-Za-z0-9_]*!))",name:"meta.macro.rust"}]},namespaces:{patterns:[{captures:{1:{name:"entity.name.namespace.rust"},2:{name:"keyword.operator.namespace.rust"}},comment:"namespace (non-type, non-function path segment)",match:"(?<![A-Za-z0-9_])([A-Za-z0-9_]+)((?<!super|self)::)"}]},punctuation:{patterns:[{comment:"comma",match:",",name:"punctuation.comma.rust"},{comment:"curly braces",match:"[{}]",name:"punctuation.brackets.curly.rust"},{comment:"parentheses, round brackets",match:"[()]",name:"punctuation.brackets.round.rust"},{comment:"semicolon",match:";",name:"punctuation.semi.rust"},{comment:"square brackets",match:"[\\[\\]]",name:"punctuation.brackets.square.rust"},{comment:"angle brackets",match:"(?<!=)[<>]",name:"punctuation.brackets.angle.rust"}]},strings:{patterns:[{begin:'(b?)(")',beginCaptures:{1:{name:"string.quoted.byte.raw.rust"},2:{name:"punctuation.definition.string.rust"}},comment:"double-quoted strings and byte strings",end:'"',endCaptures:{0:{name:"punctuation.definition.string.rust"}},name:"string.quoted.double.rust",patterns:[{include:"#escapes"},{include:"#interpolations"}]},{begin:'(b?r)(#*)(")',beginCaptures:{1:{name:"string.quoted.byte.raw.rust"},2:{name:"punctuation.definition.string.raw.rust"},3:{name:"punctuation.definition.string.rust"}},comment:"double-quoted raw strings and raw byte strings",end:'(")(\\2)',endCaptures:{1:{name:"punctuation.definition.string.rust"},2:{name:"punctuation.definition.string.raw.rust"}},name:"string.quoted.double.rust"},{begin:"(b)?(')",beginCaptures:{1:{name:"string.quoted.byte.raw.rust"},2:{name:"punctuation.definition.char.rust"}},comment:"characters and bytes",end:"'",endCaptures:{0:{name:"punctuation.definition.char.rust"}},name:"string.quoted.single.char.rust",patterns:[{include:"#escapes"}]}]},types:{patterns:[{captures:{1:{name:"entity.name.type.numeric.rust"}},comment:"numeric types",match:"(?<![A-Za-z])(f32|f64|i128|i16|i32|i64|i8|isize|u128|u16|u32|u64|u8|usize)\\b"},{begin:"\\b(_?[A-Z][A-Za-z0-9_]*)(<)",beginCaptures:{1:{name:"entity.name.type.rust"},2:{name:"punctuation.brackets.angle.rust"}},comment:"parameterized types",end:">",endCaptures:{0:{name:"punctuation.brackets.angle.rust"}},patterns:[{include:"#block-comments"},{include:"#comments"},{include:"#keywords"},{include:"#lvariables"},{include:"#lifetimes"},{include:"#punctuation"},{include:"#types"},{include:"#variables"}]},{comment:"primitive types",match:"\\b(bool|char|str)\\b",name:"entity.name.type.primitive.rust"},{captures:{1:{name:"keyword.declaration.trait.rust storage.type.rust"},2:{name:"entity.name.type.trait.rust"}},comment:"trait declarations",match:"\\b(trait)\\s+(_?[A-Z][A-Za-z0-9_]*)\\b"},{captures:{1:{name:"keyword.declaration.struct.rust storage.type.rust"},2:{name:"entity.name.type.struct.rust"}},comment:"struct declarations",match:"\\b(struct)\\s+(_?[A-Z][A-Za-z0-9_]*)\\b"},{captures:{1:{name:"keyword.declaration.enum.rust storage.type.rust"},2:{name:"entity.name.type.enum.rust"}},comment:"enum declarations",match:"\\b(enum)\\s+(_?[A-Z][A-Za-z0-9_]*)\\b"},{captures:{1:{name:"keyword.declaration.type.rust storage.type.rust"},2:{name:"entity.name.type.declaration.rust"}},comment:"type declarations",match:"\\b(type)\\s+(_?[A-Z][A-Za-z0-9_]*)\\b"},{comment:"types",match:"\\b_?[A-Z][A-Za-z0-9_]*\\b(?!!)",name:"entity.name.type.rust"}]},variables:{patterns:[{comment:"variables",match:"\\b(?<!(?<!\\.)\\.)(?:r#(?!(crate|[Ss]elf|super)))?[a-z0-9_]+\\b",name:"variable.other.rust"}]}},scopeName:"source.rust",aliases:["rs"]})]},55003:(e,n,t)=>{t.r(n),t.d(n,{default:()=>a});var a=[Object.freeze({displayName:"Kotlin",fileTypes:["kt","kts"],name:"kotlin",patterns:[{include:"#import"},{include:"#package"},{include:"#code"}],repository:{"annotation-simple":{match:"(?<!\\w)@[\\w\\.]+\\b(?!:)",name:"entity.name.type.annotation.kotlin"},"annotation-site":{begin:"(?<!\\w)(@\\w+):\\s*(?!\\[)",beginCaptures:{1:{name:"entity.name.type.annotation-site.kotlin"}},end:"$",patterns:[{include:"#unescaped-annotation"}]},"annotation-site-list":{begin:"(?<!\\w)(@\\w+):\\s*\\[",beginCaptures:{1:{name:"entity.name.type.annotation-site.kotlin"}},end:"\\]",patterns:[{include:"#unescaped-annotation"}]},"binary-literal":{match:"0(b|B)[01][01_]*",name:"constant.numeric.binary.kotlin"},"boolean-literal":{match:"\\b(true|false)\\b",name:"constant.language.boolean.kotlin"},character:{begin:"'",end:"'",name:"string.quoted.single.kotlin",patterns:[{match:"\\\\.",name:"constant.character.escape.kotlin"}]},"class-declaration":{captures:{1:{name:"keyword.hard.class.kotlin"},2:{name:"entity.name.type.class.kotlin"},3:{patterns:[{include:"#type-parameter"}]}},match:"\\b(class|(?:fun\\s+)?interface)\\s+(\\b\\w+\\b|`[^`]+`)\\s*(?<GROUP><([^<>]|\\g<GROUP>)+>)?"},code:{patterns:[{include:"#comments"},{include:"#keywords"},{include:"#annotation-simple"},{include:"#annotation-site-list"},{include:"#annotation-site"},{include:"#class-declaration"},{include:"#object"},{include:"#type-alias"},{include:"#function"},{include:"#variable-declaration"},{include:"#type-constraint"},{include:"#type-annotation"},{include:"#function-call"},{include:"#method-reference"},{include:"#key"},{include:"#string"},{include:"#string-empty"},{include:"#string-multiline"},{include:"#character"},{include:"#lambda-arrow"},{include:"#operators"},{include:"#self-reference"},{include:"#decimal-literal"},{include:"#hex-literal"},{include:"#binary-literal"},{include:"#boolean-literal"},{include:"#null-literal"}]},"comment-block":{begin:"/\\*(?!\\*)",end:"\\*/",name:"comment.block.kotlin"},"comment-javadoc":{patterns:[{begin:"/\\*\\*",end:"\\*/",name:"comment.block.javadoc.kotlin",patterns:[{match:"@(return|constructor|receiver|sample|see|author|since|suppress)\\b",name:"keyword.other.documentation.javadoc.kotlin"},{captures:{1:{name:"keyword.other.documentation.javadoc.kotlin"},2:{name:"variable.parameter.kotlin"}},match:"(@param|@property)\\s+(\\S+)"},{captures:{1:{name:"keyword.other.documentation.javadoc.kotlin"},2:{name:"variable.parameter.kotlin"}},match:"(@param)\\[(\\S+)\\]"},{captures:{1:{name:"keyword.other.documentation.javadoc.kotlin"},2:{name:"entity.name.type.class.kotlin"}},match:"(@(?:exception|throws))\\s+(\\S+)"},{captures:{1:{name:"keyword.other.documentation.javadoc.kotlin"},2:{name:"entity.name.type.class.kotlin"},3:{name:"variable.parameter.kotlin"}},match:"{(@link)\\s+(\\S+)?#([\\w$]+\\s*\\([^()]*\\)).*}"}]}]},"comment-line":{begin:"//",end:"$",name:"comment.line.double-slash.kotlin"},comments:{patterns:[{include:"#comment-line"},{include:"#comment-block"},{include:"#comment-javadoc"}]},"control-keywords":{match:"\\b(if|else|while|do|when|try|throw|break|continue|return|for)\\b",name:"keyword.control.kotlin"},"decimal-literal":{match:"\\b\\d[\\d_]*(\\.[\\d_]+)?((e|E)\\d+)?(u|U)?(L|F|f)?\\b",name:"constant.numeric.decimal.kotlin"},function:{captures:{1:{name:"keyword.hard.fun.kotlin"},2:{patterns:[{include:"#type-parameter"}]},4:{name:"entity.name.type.class.extension.kotlin"},5:{name:"entity.name.function.declaration.kotlin"}},match:"\\b(fun)\\b\\s*(?<GROUP><([^<>]|\\g<GROUP>)+>)?\\s*(?:(?:(\\w+)\\.)?(\\b\\w+\\b|`[^`]+`))?"},"function-call":{captures:{1:{name:"entity.name.function.call.kotlin"},2:{patterns:[{include:"#type-parameter"}]}},match:"\\??\\.?(\\b\\w+\\b|`[^`]+`)\\s*(?<GROUP><([^<>]|\\g<GROUP>)+>)?\\s*(?=[({])"},"hard-keywords":{match:"\\b(as|typeof|is|in)\\b",name:"keyword.hard.kotlin"},"hex-literal":{match:"0(x|X)[A-Fa-f0-9][A-Fa-f0-9_]*(u|U)?",name:"constant.numeric.hex.kotlin"},import:{begin:"\\b(import)\\b\\s*",beginCaptures:{1:{name:"keyword.soft.kotlin"}},contentName:"entity.name.package.kotlin",end:";|$",name:"meta.import.kotlin",patterns:[{include:"#comments"},{include:"#hard-keywords"},{match:"\\*",name:"variable.language.wildcard.kotlin"}]},key:{captures:{1:{name:"variable.parameter.kotlin"},2:{name:"keyword.operator.assignment.kotlin"}},match:"\\b(\\w=)\\s*(=)"},keywords:{patterns:[{include:"#prefix-modifiers"},{include:"#postfix-modifiers"},{include:"#soft-keywords"},{include:"#hard-keywords"},{include:"#control-keywords"}]},"lambda-arrow":{match:"->",name:"storage.type.function.arrow.kotlin"},"method-reference":{captures:{1:{name:"entity.name.function.reference.kotlin"}},match:"\\??::(\\b\\w+\\b|`[^`]+`)"},"null-literal":{match:"\\bnull\\b",name:"constant.language.null.kotlin"},object:{captures:{1:{name:"keyword.hard.object.kotlin"},2:{name:"entity.name.type.object.kotlin"}},match:"\\b(object)(?:\\s+(\\b\\w+\\b|`[^`]+`))?"},operators:{patterns:[{match:"(===?|!==?|<=|>=|<|>)",name:"keyword.operator.comparison.kotlin"},{match:"([+*/%-]=)",name:"keyword.operator.assignment.arithmetic.kotlin"},{match:"(=)",name:"keyword.operator.assignment.kotlin"},{match:"([+*/%-])",name:"keyword.operator.arithmetic.kotlin"},{match:"(!|&&|\\|\\|)",name:"keyword.operator.logical.kotlin"},{match:"(--|\\+\\+)",name:"keyword.operator.increment-decrement.kotlin"},{match:"(\\.\\.)",name:"keyword.operator.range.kotlin"}]},package:{begin:"\\b(package)\\b\\s*",beginCaptures:{1:{name:"keyword.hard.package.kotlin"}},contentName:"entity.name.package.kotlin",end:";|$",name:"meta.package.kotlin",patterns:[{include:"#comments"}]},"postfix-modifiers":{match:"\\b(where|by|get|set)\\b",name:"storage.modifier.other.kotlin"},"prefix-modifiers":{match:"\\b(abstract|final|enum|open|annotation|sealed|data|override|final|lateinit|private|protected|public|internal|inner|companion|noinline|crossinline|vararg|reified|tailrec|operator|infix|inline|external|const|suspend|value)\\b",name:"storage.modifier.other.kotlin"},"self-reference":{match:"\\b(this|super)(@\\w+)?\\b",name:"variable.language.this.kotlin"},"soft-keywords":{match:"\\b(init|catch|finally|field)\\b",name:"keyword.soft.kotlin"},string:{begin:'(?<!")"(?!")',end:'"',name:"string.quoted.double.kotlin",patterns:[{match:"\\\\.",name:"constant.character.escape.kotlin"},{include:"#string-escape-simple"},{include:"#string-escape-bracketed"}]},"string-empty":{match:'(?<!")""(?!")',name:"string.quoted.double.kotlin"},"string-escape-bracketed":{begin:"(?<!\\\\)(\\$\\{)",beginCaptures:{1:{name:"punctuation.definition.template-expression.begin"}},end:"(\\})",endCaptures:{1:{name:"punctuation.definition.template-expression.end"}},name:"meta.template.expression.kotlin",patterns:[{include:"#code"}]},"string-escape-simple":{match:"(?<!\\\\)\\$\\w+\\b",name:"variable.string-escape.kotlin"},"string-multiline":{begin:'"""',end:'"""',name:"string.quoted.double.kotlin",patterns:[{match:"\\\\.",name:"constant.character.escape.kotlin"},{include:"#string-escape-simple"},{include:"#string-escape-bracketed"}]},"type-alias":{captures:{1:{name:"keyword.hard.typealias.kotlin"},2:{name:"entity.name.type.kotlin"},3:{patterns:[{include:"#type-parameter"}]}},match:"\\b(typealias)\\s+(\\b\\w+\\b|`[^`]+`)\\s*(?<GROUP><([^<>]|\\g<GROUP>)+>)?"},"type-annotation":{captures:{0:{patterns:[{include:"#type-parameter"}]}},match:"(?<![:?]):\\s*(\\w|\\?|\\s|->|(?<GROUP>[<(]([^<>()\"']|\\g<GROUP>)+[)>]))+"},"type-parameter":{patterns:[{match:"\\b\\w+\\b",name:"entity.name.type.kotlin"},{match:"\\b(in|out)\\b",name:"storage.modifier.kotlin"}]},"unescaped-annotation":{match:"\\b[\\w\\.]+\\b",name:"entity.name.type.annotation.kotlin"},"variable-declaration":{captures:{1:{name:"keyword.hard.kotlin"},2:{patterns:[{include:"#type-parameter"}]}},match:"\\b(val|var)\\b\\s*(?<GROUP><([^<>]|\\g<GROUP>)+>)?"}},scopeName:"source.kotlin",aliases:["kt","kts"]})]},58050:(e,n,t)=>{t.r(n),t.d(n,{default:()=>a});var a=[Object.freeze({displayName:"Makefile",name:"make",patterns:[{include:"#comment"},{include:"#variables"},{include:"#variable-assignment"},{include:"#directives"},{include:"#recipe"},{include:"#target"}],repository:{"another-variable-braces":{patterns:[{begin:"(?<={)(?!})",end:"(?=}|((?<!\\\\)\\n))",name:"variable.other.makefile",patterns:[{include:"#variables"},{match:"\\\\\\n",name:"constant.character.escape.continuation.makefile"}]}]},"another-variable-parentheses":{patterns:[{begin:"(?<=\\()(?!\\))",end:"(?=\\)|((?<!\\\\)\\n))",name:"variable.other.makefile",patterns:[{include:"#variables"},{match:"\\\\\\n",name:"constant.character.escape.continuation.makefile"}]}]},"braces-interpolation":{begin:"{",end:"}",patterns:[{include:"#variables"},{include:"#interpolation"}]},"builtin-variable-braces":{patterns:[{match:"(?<={)(MAKEFILES|VPATH|SHELL|MAKESHELL|MAKE|MAKELEVEL|MAKEFLAGS|MAKECMDGOALS|CURDIR|SUFFIXES|\\.LIBPATTERNS)(?=\\s*})",name:"variable.language.makefile"}]},"builtin-variable-parentheses":{patterns:[{match:"(?<=\\()(MAKEFILES|VPATH|SHELL|MAKESHELL|MAKE|MAKELEVEL|MAKEFLAGS|MAKECMDGOALS|CURDIR|SUFFIXES|\\.LIBPATTERNS)(?=\\s*\\))",name:"variable.language.makefile"}]},comma:{match:",",name:"punctuation.separator.delimeter.comma.makefile"},comment:{begin:"(^[ ]+)?((?<!\\\\)(\\\\\\\\)*)(?=#)",beginCaptures:{1:{name:"punctuation.whitespace.comment.leading.makefile"}},end:"(?!\\G)",patterns:[{begin:"#",beginCaptures:{0:{name:"punctuation.definition.comment.makefile"}},end:"(?=[^\\\\])$",name:"comment.line.number-sign.makefile",patterns:[{match:"\\\\\\n",name:"constant.character.escape.continuation.makefile"}]}]},directives:{patterns:[{begin:"^[ ]*([s\\-]?include)\\b",beginCaptures:{1:{name:"keyword.control.include.makefile"}},end:"^",patterns:[{include:"#comment"},{include:"#variables"},{match:"%",name:"constant.other.placeholder.makefile"}]},{begin:"^[ ]*(vpath)\\b",beginCaptures:{1:{name:"keyword.control.vpath.makefile"}},end:"^",patterns:[{include:"#comment"},{include:"#variables"},{match:"%",name:"constant.other.placeholder.makefile"}]},{begin:"^\\s*(?:(override)\\s*)?(define)\\s*([^\\s]+)\\s*(=|\\?=|:=|\\+=)?(?=\\s)",captures:{1:{name:"keyword.control.override.makefile"},2:{name:"keyword.control.define.makefile"},3:{name:"variable.other.makefile"},4:{name:"punctuation.separator.key-value.makefile"}},end:"^\\s*(endef)\\b",name:"meta.scope.conditional.makefile",patterns:[{begin:"\\G(?!\\n)",end:"^",patterns:[{include:"#comment"}]},{include:"#variables"},{include:"#directives"}]},{begin:"^[ ]*(export)\\b",beginCaptures:{1:{name:"keyword.control.$1.makefile"}},end:"^",patterns:[{include:"#comment"},{include:"#variable-assignment"},{match:"[^\\s]+",name:"variable.other.makefile"}]},{begin:"^[ ]*(override|private)\\b",beginCaptures:{1:{name:"keyword.control.$1.makefile"}},end:"^",patterns:[{include:"#comment"},{include:"#variable-assignment"}]},{begin:"^[ ]*(unexport|undefine)\\b",beginCaptures:{1:{name:"keyword.control.$1.makefile"}},end:"^",patterns:[{include:"#comment"},{match:"[^\\s]+",name:"variable.other.makefile"}]},{begin:"^\\s*(ifeq|ifneq|ifdef|ifndef)(?=\\s)",captures:{1:{name:"keyword.control.$1.makefile"}},end:"^\\s*(endif)\\b",name:"meta.scope.conditional.makefile",patterns:[{begin:"\\G",end:"^",name:"meta.scope.condition.makefile",patterns:[{include:"#comma"},{include:"#variables"},{include:"#comment"}]},{begin:"^\\s*else(?=\\s)\\s*(ifeq|ifneq|ifdef|ifndef)*(?=\\s)",beginCaptures:{0:{name:"keyword.control.else.makefile"}},end:"^",patterns:[{include:"#comma"},{include:"#variables"},{include:"#comment"}]},{include:"$self"}]}]},"flavor-variable-braces":{patterns:[{begin:"(?<={)(origin|flavor)\\s(?=[^\\s}]+\\s*})",beginCaptures:{1:{name:"support.function.$1.makefile"}},contentName:"variable.other.makefile",end:"(?=})",name:"meta.scope.function-call.makefile",patterns:[{include:"#variables"}]}]},"flavor-variable-parentheses":{patterns:[{begin:"(?<=\\()(origin|flavor)\\s(?=[^\\s)]+\\s*\\))",beginCaptures:{1:{name:"support.function.$1.makefile"}},contentName:"variable.other.makefile",end:"(?=\\))",name:"meta.scope.function-call.makefile",patterns:[{include:"#variables"}]}]},"function-variable-braces":{patterns:[{begin:"(?<={)(subst|patsubst|strip|findstring|filter(-out)?|sort|word(list)?|firstword|lastword|dir|notdir|suffix|basename|addsuffix|addprefix|join|wildcard|realpath|abspath|info|error|warning|shell|foreach|if|or|and|call|eval|value|file|guile)\\s",beginCaptures:{1:{name:"support.function.$1.makefile"}},end:"(?=}|((?<!\\\\)\\n))",name:"meta.scope.function-call.makefile",patterns:[{include:"#comma"},{include:"#variables"},{include:"#interpolation"},{match:"%|\\*",name:"constant.other.placeholder.makefile"},{match:"\\\\\\n",name:"constant.character.escape.continuation.makefile"}]}]},"function-variable-parentheses":{patterns:[{begin:"(?<=\\()(subst|patsubst|strip|findstring|filter(-out)?|sort|word(list)?|firstword|lastword|dir|notdir|suffix|basename|addsuffix|addprefix|join|wildcard|realpath|abspath|info|error|warning|shell|foreach|if|or|and|call|eval|value|file|guile)\\s",beginCaptures:{1:{name:"support.function.$1.makefile"}},end:"(?=\\)|((?<!\\\\)\\n))",name:"meta.scope.function-call.makefile",patterns:[{include:"#comma"},{include:"#variables"},{include:"#interpolation"},{match:"%|\\*",name:"constant.other.placeholder.makefile"},{match:"\\\\\\n",name:"constant.character.escape.continuation.makefile"}]}]},interpolation:{patterns:[{include:"#parentheses-interpolation"},{include:"#braces-interpolation"}]},"parentheses-interpolation":{begin:"\\(",end:"\\)",patterns:[{include:"#variables"},{include:"#interpolation"}]},recipe:{begin:"^\\t([+\\-@]*)",beginCaptures:{1:{name:"keyword.control.$1.makefile"}},end:"[^\\\\]$",name:"meta.scope.recipe.makefile",patterns:[{match:"\\\\\\n",name:"constant.character.escape.continuation.makefile"},{include:"#variables"}]},"simple-variable":{patterns:[{match:"\\$[^(){}]",name:"variable.language.makefile"}]},target:{begin:"^(?!\\t)([^:]*)(:)(?!=)",beginCaptures:{1:{patterns:[{captures:{1:{name:"support.function.target.$1.makefile"}},match:"^\\s*(\\.(PHONY|SUFFIXES|DEFAULT|PRECIOUS|INTERMEDIATE|SECONDARY|SECONDEXPANSION|DELETE_ON_ERROR|IGNORE|LOW_RESOLUTION_TIME|SILENT|EXPORT_ALL_VARIABLES|NOTPARALLEL|ONESHELL|POSIX))\\s*$"},{begin:"(?=\\S)",end:"(?=\\s|$)",name:"entity.name.function.target.makefile",patterns:[{include:"#variables"},{match:"%",name:"constant.other.placeholder.makefile"}]}]},2:{name:"punctuation.separator.key-value.makefile"}},end:"[^\\\\]$",name:"meta.scope.target.makefile",patterns:[{begin:"\\G",end:"(?=[^\\\\])$",name:"meta.scope.prerequisites.makefile",patterns:[{match:"\\\\\\n",name:"constant.character.escape.continuation.makefile"},{match:"%|\\*",name:"constant.other.placeholder.makefile"},{include:"#comment"},{include:"#variables"}]}]},"variable-assignment":{begin:"(^[ ]*|\\G\\s*)([^\\s:#=]+)\\s*((?<![?:+!])=|\\?=|:=|\\+=|!=)",beginCaptures:{2:{name:"variable.other.makefile",patterns:[{include:"#variables"}]},3:{name:"punctuation.separator.key-value.makefile"}},end:"\\n",patterns:[{match:"\\\\\\n",name:"constant.character.escape.continuation.makefile"},{include:"#comment"},{include:"#variables"}]},"variable-braces":{patterns:[{begin:"\\${",captures:{0:{name:"punctuation.definition.variable.makefile"}},end:"}|((?<!\\\\)\\n)",name:"string.interpolated.makefile",patterns:[{include:"#variables"},{include:"#builtin-variable-braces"},{include:"#function-variable-braces"},{include:"#flavor-variable-braces"},{include:"#another-variable-braces"}]}]},"variable-parentheses":{patterns:[{begin:"\\$\\(",captures:{0:{name:"punctuation.definition.variable.makefile"}},end:"\\)|((?<!\\\\)\\n)",name:"string.interpolated.makefile",patterns:[{include:"#variables"},{include:"#builtin-variable-parentheses"},{include:"#function-variable-parentheses"},{include:"#flavor-variable-parentheses"},{include:"#another-variable-parentheses"}]}]},variables:{patterns:[{include:"#simple-variable"},{include:"#variable-parentheses"},{include:"#variable-braces"}]}},scopeName:"source.makefile",aliases:["makefile"]})]},63760:(e,n,t)=>{t.r(n),t.d(n,{default:()=>a});var a=[Object.freeze({displayName:"Clojure",name:"clojure",patterns:[{include:"#comment"},{include:"#shebang-comment"},{include:"#quoted-sexp"},{include:"#sexp"},{include:"#keyfn"},{include:"#string"},{include:"#vector"},{include:"#set"},{include:"#map"},{include:"#regexp"},{include:"#var"},{include:"#constants"},{include:"#dynamic-variables"},{include:"#metadata"},{include:"#namespace-symbol"},{include:"#symbol"}],repository:{comment:{begin:"(?<!\\\\);",beginCaptures:{0:{name:"punctuation.definition.comment.clojure"}},end:"$",name:"comment.line.semicolon.clojure"},constants:{patterns:[{match:"(nil)(?=(\\s|\\)|\\]|\\}))",name:"constant.language.nil.clojure"},{match:"(true|false)",name:"constant.language.boolean.clojure"},{match:"(##(?:Inf|-Inf|NaN))",name:"constant.numeric.symbol.clojure"},{match:"([-+]?\\d+/\\d+)",name:"constant.numeric.ratio.clojure"},{match:"([-+]?(?:(?:3[0-6])|(?:[12]\\d)|[2-9])[rR][0-9A-Za-z]+N?)",name:"constant.numeric.arbitrary-radix.clojure"},{match:"([-+]?0[xX][0-9a-fA-F]+N?)",name:"constant.numeric.hexadecimal.clojure"},{match:"([-+]?0[0-7]+N?)",name:"constant.numeric.octal.clojure"},{match:"([-+]?\\d+(?:(\\.|(?=[eEM]))\\d*([eE][-+]?\\d+)?)M?)",name:"constant.numeric.double.clojure"},{match:"([-+]?\\d+N?)",name:"constant.numeric.long.clojure"},{include:"#keyword"}]},"dynamic-variables":{match:"\\*[\\w\\.\\-_:+=><!?\\d]+\\*",name:"meta.symbol.dynamic.clojure"},keyfn:{patterns:[{match:"(?<=(\\s|\\(|\\[|\\{))(if(-[-\\p{Ll}?]*)?|when(-[-\\p{Ll}]*)?|for(-[-\\p{Ll}]*)?|cond|do|let(-[-\\p{Ll}?]*)?|binding|loop|recur|fn|throw[\\p{Ll}\\-]*|try|catch|finally|([\\p{Ll}]*case))(?=(\\s|\\)|\\]|\\}))",name:"storage.control.clojure"},{match:"(?<=(\\s|\\(|\\[|\\{))(declare-?|(in-)?ns|import|use|require|load|compile|(def[\\p{Ll}\\-]*))(?=(\\s|\\)|\\]|\\}))",name:"keyword.control.clojure"}]},keyword:{match:"(?<=(\\s|\\(|\\[|\\{)):[\\w#\\.\\-_:+=><\\/!?\\*]+(?=(\\s|\\)|\\]|\\}|\\,))",name:"constant.keyword.clojure"},map:{begin:"(\\{)",beginCaptures:{1:{name:"punctuation.section.map.begin.clojure"}},end:"(\\}(?=[}\\])\\s]*(?:;|$)))|(\\})",endCaptures:{1:{name:"punctuation.section.map.end.trailing.clojure"},2:{name:"punctuation.section.map.end.clojure"}},name:"meta.map.clojure",patterns:[{include:"$self"}]},metadata:{patterns:[{begin:"(\\^\\{)",beginCaptures:{1:{name:"punctuation.section.metadata.map.begin.clojure"}},end:"(\\}(?=[}\\])\\s]*(?:;|$)))|(\\})",endCaptures:{1:{name:"punctuation.section.metadata.map.end.trailing.clojure"},2:{name:"punctuation.section.metadata.map.end.clojure"}},name:"meta.metadata.map.clojure",patterns:[{include:"$self"}]},{begin:"(\\^)",end:"(\\s)",name:"meta.metadata.simple.clojure",patterns:[{include:"#keyword"},{include:"$self"}]}]},"namespace-symbol":{patterns:[{captures:{1:{name:"meta.symbol.namespace.clojure"}},match:"([\\p{L}\\.\\-_+=><!?\\*][\\w\\.\\-_:+=><!?\\*\\d]*)/"}]},"quoted-sexp":{begin:"(['``]\\()",beginCaptures:{1:{name:"punctuation.section.expression.begin.clojure"}},end:"(\\))$|(\\)(?=[}\\])\\s]*(?:;|$)))|(\\))",endCaptures:{1:{name:"punctuation.section.expression.end.trailing.clojure"},2:{name:"punctuation.section.expression.end.trailing.clojure"},3:{name:"punctuation.section.expression.end.clojure"}},name:"meta.quoted-expression.clojure",patterns:[{include:"$self"}]},regexp:{begin:'#"',beginCaptures:{0:{name:"punctuation.definition.regexp.begin.clojure"}},end:'"',endCaptures:{0:{name:"punctuation.definition.regexp.end.clojure"}},name:"string.regexp.clojure",patterns:[{include:"#regexp_escaped_char"}]},regexp_escaped_char:{match:"\\\\.",name:"constant.character.escape.clojure"},set:{begin:"(\\#\\{)",beginCaptures:{1:{name:"punctuation.section.set.begin.clojure"}},end:"(\\}(?=[}\\])\\s]*(?:;|$)))|(\\})",endCaptures:{1:{name:"punctuation.section.set.end.trailing.clojure"},2:{name:"punctuation.section.set.end.clojure"}},name:"meta.set.clojure",patterns:[{include:"$self"}]},sexp:{begin:"(\\()",beginCaptures:{1:{name:"punctuation.section.expression.begin.clojure"}},end:"(\\))$|(\\)(?=[}\\])\\s]*(?:;|$)))|(\\))",endCaptures:{1:{name:"punctuation.section.expression.end.trailing.clojure"},2:{name:"punctuation.section.expression.end.trailing.clojure"},3:{name:"punctuation.section.expression.end.clojure"}},name:"meta.expression.clojure",patterns:[{begin:"(?<=\\()(ns|declare|def[\\w\\d._:+=><!?*-]*|[\\w._:+=><!?*-][\\w\\d._:+=><!?*-]*/def[\\w\\d._:+=><!?*-]*)\\s+",beginCaptures:{1:{name:"keyword.control.clojure"}},end:"(?=\\))",name:"meta.definition.global.clojure",patterns:[{include:"#metadata"},{include:"#dynamic-variables"},{match:"([\\p{L}\\.\\-_+=><!?\\*][\\w\\.\\-_:+=><!?\\*\\d]*)",name:"entity.global.clojure"},{include:"$self"}]},{include:"#keyfn"},{include:"#constants"},{include:"#vector"},{include:"#map"},{include:"#set"},{include:"#sexp"},{captures:{1:{name:"entity.name.function.clojure"}},match:"(?<=\\()(.+?)(?=\\s|\\))",patterns:[{include:"$self"}]},{include:"$self"}]},"shebang-comment":{begin:"^(#!)",beginCaptures:{1:{name:"punctuation.definition.comment.shebang.clojure"}},end:"$",name:"comment.line.shebang.clojure"},string:{begin:'(?<!\\\\)(")',beginCaptures:{1:{name:"punctuation.definition.string.begin.clojure"}},end:'(")',endCaptures:{1:{name:"punctuation.definition.string.end.clojure"}},name:"string.quoted.double.clojure",patterns:[{match:"\\\\.",name:"constant.character.escape.clojure"}]},symbol:{patterns:[{match:"([\\p{L}\\.\\-_+=><!?\\*][\\w\\.\\-_:+=><!?\\*\\d]*)",name:"meta.symbol.clojure"}]},var:{match:"(?<=(\\s|\\(|\\[|\\{)\\#)'[\\w\\.\\-_:+=><\\/!?\\*]+(?=(\\s|\\)|\\]|\\}))",name:"meta.var.clojure"},vector:{begin:"(\\[)",beginCaptures:{1:{name:"punctuation.section.vector.begin.clojure"}},end:"(\\](?=[}\\])\\s]*(?:;|$)))|(\\])",endCaptures:{1:{name:"punctuation.section.vector.end.trailing.clojure"},2:{name:"punctuation.section.vector.end.clojure"}},name:"meta.vector.clojure",patterns:[{include:"$self"}]}},scopeName:"source.clojure",aliases:["clj"]})]},65312:(e,n,t)=>{t.r(n),t.d(n,{default:()=>a});var a=[Object.freeze({displayName:"CSV",fileTypes:["csv"],name:"csv",patterns:[{captures:{1:{name:"rainbow1"},2:{name:"keyword.rainbow2"},3:{name:"entity.name.function.rainbow3"},4:{name:"comment.rainbow4"},5:{name:"string.rainbow5"},6:{name:"variable.parameter.rainbow6"},7:{name:"constant.numeric.rainbow7"},8:{name:"entity.name.type.rainbow8"},9:{name:"markup.bold.rainbow9"},10:{name:"invalid.rainbow10"}},match:'((?: *"(?:[^"]*"")*[^"]*" *(?:,|$))|(?:[^,]*(?:,|$)))?((?: *"(?:[^"]*"")*[^"]*" *(?:,|$))|(?:[^,]*(?:,|$)))?((?: *"(?:[^"]*"")*[^"]*" *(?:,|$))|(?:[^,]*(?:,|$)))?((?: *"(?:[^"]*"")*[^"]*" *(?:,|$))|(?:[^,]*(?:,|$)))?((?: *"(?:[^"]*"")*[^"]*" *(?:,|$))|(?:[^,]*(?:,|$)))?((?: *"(?:[^"]*"")*[^"]*" *(?:,|$))|(?:[^,]*(?:,|$)))?((?: *"(?:[^"]*"")*[^"]*" *(?:,|$))|(?:[^,]*(?:,|$)))?((?: *"(?:[^"]*"")*[^"]*" *(?:,|$))|(?:[^,]*(?:,|$)))?((?: *"(?:[^"]*"")*[^"]*" *(?:,|$))|(?:[^,]*(?:,|$)))?((?: *"(?:[^"]*"")*[^"]*" *(?:,|$))|(?:[^,]*(?:,|$)))?',name:"rainbowgroup"}],scopeName:"text.csv"})]},69684:(e,n,t)=>{t.r(n),t.d(n,{default:()=>a});var a=[Object.freeze({displayName:"TOML",fileTypes:["toml"],name:"toml",patterns:[{include:"#comments"},{include:"#groups"},{include:"#key_pair"},{include:"#invalid"}],repository:{comments:{begin:"(^[ \\t]+)?(?=#)",beginCaptures:{1:{name:"punctuation.whitespace.comment.leading.toml"}},end:"(?!\\G)",patterns:[{begin:"#",beginCaptures:{0:{name:"punctuation.definition.comment.toml"}},end:"\\n",name:"comment.line.number-sign.toml"}]},groups:{patterns:[{captures:{1:{name:"punctuation.definition.section.begin.toml"},2:{patterns:[{match:"[^\\s.]+",name:"entity.name.section.toml"}]},3:{name:"punctuation.definition.section.begin.toml"}},match:"^\\s*(\\[)([^\\[\\]]*)(\\])",name:"meta.group.toml"},{captures:{1:{name:"punctuation.definition.section.begin.toml"},2:{patterns:[{match:"[^\\s.]+",name:"entity.name.section.toml"}]},3:{name:"punctuation.definition.section.begin.toml"}},match:"^\\s*(\\[\\[)([^\\[\\]]*)(\\]\\])",name:"meta.group.double.toml"}]},invalid:{match:"\\S+(\\s*(?=\\S))?",name:"invalid.illegal.not-allowed-here.toml"},key_pair:{patterns:[{begin:"([A-Za-z0-9_-]+)\\s*(=)\\s*",captures:{1:{name:"variable.other.key.toml"},2:{name:"punctuation.separator.key-value.toml"}},end:"(?<=\\S)(?<!=)|$",patterns:[{include:"#primatives"}]},{begin:'((")(.*?)("))\\s*(=)\\s*',captures:{1:{name:"variable.other.key.toml"},2:{name:"punctuation.definition.variable.begin.toml"},3:{patterns:[{match:'\\\\([btnfr"\\\\]|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})',name:"constant.character.escape.toml"},{match:'\\\\[^btnfr"\\\\]',name:"invalid.illegal.escape.toml"},{match:'"',name:"invalid.illegal.not-allowed-here.toml"}]},4:{name:"punctuation.definition.variable.end.toml"},5:{name:"punctuation.separator.key-value.toml"}},end:"(?<=\\S)(?<!=)|$",patterns:[{include:"#primatives"}]},{begin:"((')([^']*)('))\\s*(=)\\s*",captures:{1:{name:"variable.other.key.toml"},2:{name:"punctuation.definition.variable.begin.toml"},4:{name:"punctuation.definition.variable.end.toml"},5:{name:"punctuation.separator.key-value.toml"}},end:"(?<=\\S)(?<!=)|$",patterns:[{include:"#primatives"}]},{begin:"(((?:[A-Za-z0-9_-]+|\"(?:[^\"\\\\]|\\\\.)*\"|'[^']*')(?:\\s*\\.\\s*|(?=\\s*=))){2,})\\s*(=)\\s*",captures:{1:{name:"variable.other.key.toml",patterns:[{match:"\\.",name:"punctuation.separator.variable.toml"},{captures:{1:{name:"punctuation.definition.variable.begin.toml"},2:{patterns:[{match:'\\\\([btnfr"\\\\]|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})',name:"constant.character.escape.toml"},{match:'\\\\[^btnfr"\\\\]',name:"invalid.illegal.escape.toml"}]},3:{name:"punctuation.definition.variable.end.toml"}},match:'(")((?:[^"\\\\]|\\\\.)*)(")'},{captures:{1:{name:"punctuation.definition.variable.begin.toml"},2:{name:"punctuation.definition.variable.end.toml"}},match:"(')[^']*(')"}]},3:{name:"punctuation.separator.key-value.toml"}},comment:"Dotted key",end:"(?<=\\S)(?<!=)|$",patterns:[{include:"#primatives"}]}]},primatives:{patterns:[{begin:'\\G"""',beginCaptures:{0:{name:"punctuation.definition.string.begin.toml"}},end:'"{3,5}',endCaptures:{0:{name:"punctuation.definition.string.end.toml"}},name:"string.quoted.triple.double.toml",patterns:[{match:'\\\\([btnfr"\\\\]|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})',name:"constant.character.escape.toml"},{match:'\\\\[^btnfr"\\\\\\n]',name:"invalid.illegal.escape.toml"}]},{begin:'\\G"',beginCaptures:{0:{name:"punctuation.definition.string.begin.toml"}},end:'"',endCaptures:{0:{name:"punctuation.definition.string.end.toml"}},name:"string.quoted.double.toml",patterns:[{match:'\\\\([btnfr"\\\\]|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})',name:"constant.character.escape.toml"},{match:'\\\\[^btnfr"\\\\]',name:"invalid.illegal.escape.toml"}]},{begin:"\\G'''",beginCaptures:{0:{name:"punctuation.definition.string.begin.toml"}},end:"'{3,5}",endCaptures:{0:{name:"punctuation.definition.string.end.toml"}},name:"string.quoted.triple.single.toml"},{begin:"\\G'",beginCaptures:{0:{name:"punctuation.definition.string.begin.toml"}},end:"'",endCaptures:{0:{name:"punctuation.definition.string.end.toml"}},name:"string.quoted.single.toml"},{match:"\\G\\d{4}-(0[1-9]|1[012])-(?!00|3[2-9])[0-3]\\d([Tt ](?!2[5-9])[0-2]\\d:[0-5]\\d:(?!6[1-9])[0-6]\\d(\\.\\d+)?(Z|[+-](?!2[5-9])[0-2]\\d:[0-5]\\d)?)?",name:"constant.other.date.toml"},{match:"\\G(?!2[5-9])[0-2]\\d:[0-5]\\d:(?!6[1-9])[0-6]\\d(\\.\\d+)?",name:"constant.other.time.toml"},{match:"\\G(true|false)",name:"constant.language.boolean.toml"},{match:"\\G0x\\h(\\h|_\\h)*",name:"constant.numeric.hex.toml"},{match:"\\G0o[0-7]([0-7]|_[0-7])*",name:"constant.numeric.octal.toml"},{match:"\\G0b[01]([01]|_[01])*",name:"constant.numeric.binary.toml"},{match:"\\G[+-]?(inf|nan)",name:"constant.numeric.toml"},{match:"\\G([+-]?(0|([1-9]((\\d|_\\d)+)?)))(?=[.eE])(\\.(\\d((\\d|_\\d)+)?))?([eE]([+-]?\\d((\\d|_\\d)+)?))?",name:"constant.numeric.float.toml"},{match:"\\G([+-]?(0|([1-9]((\\d|_\\d)+)?)))",name:"constant.numeric.integer.toml"},{begin:"\\G\\[",beginCaptures:{0:{name:"punctuation.definition.array.begin.toml"}},end:"\\]",endCaptures:{0:{name:"punctuation.definition.array.end.toml"}},name:"meta.array.toml",patterns:[{begin:"(?=[\"'']|[+-]?\\d|[+-]?(inf|nan)|true|false|\\[|\\{)",end:",|(?=])",endCaptures:{0:{name:"punctuation.separator.array.toml"}},patterns:[{include:"#primatives"},{include:"#comments"},{include:"#invalid"}]},{include:"#comments"},{include:"#invalid"}]},{begin:"\\G\\{",beginCaptures:{0:{name:"punctuation.definition.inline-table.begin.toml"}},end:"\\}",endCaptures:{0:{name:"punctuation.definition.inline-table.end.toml"}},name:"meta.inline-table.toml",patterns:[{begin:"(?=\\S)",end:",|(?=})",endCaptures:{0:{name:"punctuation.separator.inline-table.toml"}},patterns:[{include:"#key_pair"}]},{include:"#comments"}]}]}},scopeName:"source.toml"})]},85862:(e,n,t)=>{t.r(n),t.d(n,{default:()=>a});var a=[Object.freeze({displayName:"JSON",name:"json",patterns:[{include:"#value"}],repository:{array:{begin:"\\[",beginCaptures:{0:{name:"punctuation.definition.array.begin.json"}},end:"\\]",endCaptures:{0:{name:"punctuation.definition.array.end.json"}},name:"meta.structure.array.json",patterns:[{include:"#value"},{match:",",name:"punctuation.separator.array.json"},{match:"[^\\s\\]]",name:"invalid.illegal.expected-array-separator.json"}]},comments:{patterns:[{begin:"/\\*\\*(?!/)",captures:{0:{name:"punctuation.definition.comment.json"}},end:"\\*/",name:"comment.block.documentation.json"},{begin:"/\\*",captures:{0:{name:"punctuation.definition.comment.json"}},end:"\\*/",name:"comment.block.json"},{captures:{1:{name:"punctuation.definition.comment.json"}},match:"(//).*$\\n?",name:"comment.line.double-slash.js"}]},constant:{match:"\\b(?:true|false|null)\\b",name:"constant.language.json"},number:{match:"-?(?:0|[1-9]\\d*)(?:(?:\\.\\d+)?(?:[eE][+-]?\\d+)?)?",name:"constant.numeric.json"},object:{begin:"\\{",beginCaptures:{0:{name:"punctuation.definition.dictionary.begin.json"}},end:"\\}",endCaptures:{0:{name:"punctuation.definition.dictionary.end.json"}},name:"meta.structure.dictionary.json",patterns:[{comment:"the JSON object key",include:"#objectkey"},{include:"#comments"},{begin:":",beginCaptures:{0:{name:"punctuation.separator.dictionary.key-value.json"}},end:"(,)|(?=\\})",endCaptures:{1:{name:"punctuation.separator.dictionary.pair.json"}},name:"meta.structure.dictionary.value.json",patterns:[{comment:"the JSON object value",include:"#value"},{match:"[^\\s,]",name:"invalid.illegal.expected-dictionary-separator.json"}]},{match:"[^\\s}]",name:"invalid.illegal.expected-dictionary-separator.json"}]},objectkey:{begin:'"',beginCaptures:{0:{name:"punctuation.support.type.property-name.begin.json"}},end:'"',endCaptures:{0:{name:"punctuation.support.type.property-name.end.json"}},name:"string.json support.type.property-name.json",patterns:[{include:"#stringcontent"}]},string:{begin:'"',beginCaptures:{0:{name:"punctuation.definition.string.begin.json"}},end:'"',endCaptures:{0:{name:"punctuation.definition.string.end.json"}},name:"string.quoted.double.json",patterns:[{include:"#stringcontent"}]},stringcontent:{patterns:[{match:'\\\\(?:["\\\\/bfnrt]|u[0-9a-fA-F]{4})',name:"constant.character.escape.json"},{match:"\\\\.",name:"invalid.illegal.unrecognized-string-escape.json"}]},value:{patterns:[{include:"#constant"},{include:"#number"},{include:"#string"},{include:"#array"},{include:"#object"},{include:"#comments"}]}},scopeName:"source.json"})]},90349:(e,n,t)=>{t.r(n),t.d(n,{default:()=>a});var a=[Object.freeze({displayName:"Diff",name:"diff",patterns:[{captures:{1:{name:"punctuation.definition.separator.diff"}},match:"^((\\*{15})|(={67})|(-{3}))$\\n?",name:"meta.separator.diff"},{match:"^\\d+(,\\d+)*(a|d|c)\\d+(,\\d+)*$\\n?",name:"meta.diff.range.normal"},{captures:{1:{name:"punctuation.definition.range.diff"},2:{name:"meta.toc-list.line-number.diff"},3:{name:"punctuation.definition.range.diff"}},match:"^(@@)\\s*(.+?)\\s*(@@)($\\n?)?",name:"meta.diff.range.unified"},{captures:{3:{name:"punctuation.definition.range.diff"},4:{name:"punctuation.definition.range.diff"},6:{name:"punctuation.definition.range.diff"},7:{name:"punctuation.definition.range.diff"}},match:"^(((-{3}) .+ (-{4}))|((\\*{3}) .+ (\\*{4})))$\\n?",name:"meta.diff.range.context"},{match:"^diff --git a/.*$\\n?",name:"meta.diff.header.git"},{match:"^diff (-|\\S+\\s+\\S+).*$\\n?",name:"meta.diff.header.command"},{captures:{4:{name:"punctuation.definition.from-file.diff"},6:{name:"punctuation.definition.from-file.diff"},7:{name:"punctuation.definition.from-file.diff"}},match:"(^(((-{3}) .+)|((\\*{3}) .+))$\\n?|^(={4}) .+(?= - ))",name:"meta.diff.header.from-file"},{captures:{2:{name:"punctuation.definition.to-file.diff"},3:{name:"punctuation.definition.to-file.diff"},4:{name:"punctuation.definition.to-file.diff"}},match:"(^(\\+{3}) .+$\\n?| (-) .* (={4})$\\n?)",name:"meta.diff.header.to-file"},{captures:{3:{name:"punctuation.definition.inserted.diff"},6:{name:"punctuation.definition.inserted.diff"}},match:"^(((>)( .*)?)|((\\+).*))$\\n?",name:"markup.inserted.diff"},{captures:{1:{name:"punctuation.definition.changed.diff"}},match:"^(!).*$\\n?",name:"markup.changed.diff"},{captures:{3:{name:"punctuation.definition.deleted.diff"},6:{name:"punctuation.definition.deleted.diff"}},match:"^(((<)( .*)?)|((-).*))$\\n?",name:"markup.deleted.diff"},{begin:"^(#)",captures:{1:{name:"punctuation.definition.comment.diff"}},comment:'Git produces unified diffs with embedded comments"',end:"\\n",name:"comment.line.number-sign.diff"},{match:"^index [0-9a-f]{7,40}\\.\\.[0-9a-f]{7,40}.*$\\n?",name:"meta.diff.index.git"},{captures:{1:{name:"punctuation.separator.key-value.diff"},2:{name:"meta.toc-list.file-name.diff"}},match:"^Index(:) (.+)$\\n?",name:"meta.diff.index"},{match:"^Only in .*: .*$\\n?",name:"meta.diff.only-in"}],scopeName:"source.diff"})]},91726:(e,n,t)=>{t.r(n),t.d(n,{default:()=>a});var a=[Object.freeze({displayName:"Sass",fileTypes:["sass"],foldingStartMarker:"/\\*|^#|^\\*|^\\b|*#?region|^\\.",foldingStopMarker:"\\*/|*#?endregion|^\\s*$",name:"sass",patterns:[{begin:"^(\\s*)(/\\*)",end:"(\\*/)|^(?!\\s\\1)",name:"comment.block.sass",patterns:[{include:"#comment-tag"},{include:"#comment-param"}]},{match:"^[\\t ]*/?//[\\t ]*[SRI][\\t ]*$",name:"keyword.other.sass.formatter.action"},{begin:"^[\\t ]*//[\\t ]*(import)[\\t ]*(css-variables)[\\t ]*(from)",captures:{1:{name:"keyword.control"},2:{name:"variable"},3:{name:"keyword.control"}},end:"$\\n?",name:"comment.import.css.variables",patterns:[{include:"#import-quotes"}]},{include:"#double-slash"},{include:"#double-quoted"},{include:"#single-quoted"},{include:"#interpolation"},{include:"#curly-brackets"},{include:"#placeholder-selector"},{begin:"\\$[a-zA-Z0-9_-]+(?=:)",captures:{0:{name:"variable.other.name"}},end:"$\\n?|(?=\\)\\s\\)|\\)\\n)",name:"sass.script.maps",patterns:[{include:"#double-slash"},{include:"#double-quoted"},{include:"#single-quoted"},{include:"#interpolation"},{include:"#variable"},{include:"#rgb-value"},{include:"#numeric"},{include:"#unit"},{include:"#flag"},{include:"#comma"},{include:"#function"},{include:"#function-content"},{include:"#operator"},{include:"#reserved-words"},{include:"#parent-selector"},{include:"#property-value"},{include:"#semicolon"},{include:"#dotdotdot"}]},{include:"#variable-root"},{include:"#numeric"},{include:"#unit"},{include:"#flag"},{include:"#comma"},{include:"#semicolon"},{include:"#dotdotdot"},{begin:"@include|\\+(?!\\W|\\d)",captures:{0:{name:"keyword.control.at-rule.css.sass"}},end:"(?=\\n|\\()",name:"support.function.name.sass.library"},{begin:"^(@use)",captures:{0:{name:"keyword.control.at-rule.css.sass.use"}},end:"(?=\\n)",name:"sass.use",patterns:[{match:"as|with",name:"support.type.css.sass"},{include:"#numeric"},{include:"#unit"},{include:"#variable-root"},{include:"#rgb-value"},{include:"#comma"},{include:"#parenthesis-open"},{include:"#parenthesis-close"},{include:"#colon"},{include:"#import-quotes"}]},{begin:"^@import(.*?)( as.*)?$",captures:{1:{name:"constant.character.css.sass"},2:{name:"invalid"}},end:"(?=\\n)",name:"keyword.control.at-rule.use"},{begin:"@mixin|^[\\t ]*=|@function",captures:{0:{name:"keyword.control.at-rule.css.sass"}},end:"$\\n?|(?=\\()",name:"support.function.name.sass",patterns:[{match:"[\\w-]+",name:"entity.name.function"}]},{begin:"@",end:"$\\n?|\\s(?!(all|braille|embossed|handheld|print|projection|screen|speech|tty|tv|if|only|not)(\\s|,))",name:"keyword.control.at-rule.css.sass"},{begin:"(?<!-|\\()\\b(a|abbr|acronym|address|applet|area|article|aside|audio|b|base|big|blockquote|body|br|button|canvas|caption|cite|code|col|colgroup|datalist|dd|del|details|dfn|dialog|div|dl|dt|em|embed|eventsource|fieldset|figure|figcaption|footer|form|frame|frameset|(h[1-6])|head|header|hgroup|hr|html|i|iframe|img|input|ins|kbd|label|legend|li|link|map|mark|menu|meta|meter|nav|noframes|noscript|object|ol|optgroup|option|output|p|param|picture|pre|progress|q|samp|script|section|select|small|source|span|strike|strong|style|sub|summary|sup|table|tbody|td|textarea|tfoot|th|thead|time|title|tr|tt|ul|var|video|main|svg|rect|ruby|center|circle|ellipse|line|polyline|polygon|path|text|u|slot)\\b(?!-|\\)|:\\s)|&",end:"$\\n?|(?=\\s|,|\\(|\\)|\\.|\\#|\\[|>|-|_)",name:"entity.name.tag.css.sass.symbol",patterns:[{include:"#interpolation"},{include:"#pseudo-class"}]},{begin:"#",end:"$\\n?|(?=\\s|,|\\(|\\)|\\.|\\[|>)",name:"entity.other.attribute-name.id.css.sass",patterns:[{include:"#interpolation"},{include:"#pseudo-class"}]},{begin:"\\.|(?<=&)(-|_)",end:"$\\n?|(?=\\s|,|\\(|\\)|\\[|>)",name:"entity.other.attribute-name.class.css.sass",patterns:[{include:"#interpolation"},{include:"#pseudo-class"}]},{begin:"\\[",end:"\\]",name:"entity.other.attribute-selector.sass",patterns:[{include:"#double-quoted"},{include:"#single-quoted"},{match:"\\^|\\$|\\*|~",name:"keyword.other.regex.sass"}]},{match:"^((?<=\\]|\\)|not\\(|\\*|>|>\\s)|\n*):[a-z:-]+|(::|:-)[a-z:-]+",name:"entity.other.attribute-name.pseudo-class.css.sass"},{include:"#module"},{match:"[\\w-]*\\(",name:"entity.name.function"},{match:"\\)",name:"entity.name.function.close"},{begin:":",end:"$\\n?|(?=\\s\\(|and\\(|\\),)",name:"meta.property-list.css.sass.prop",patterns:[{match:"(?<=:)[a-z-]+\\s",name:"support.type.property-name.css.sass.prop.name"},{include:"#double-slash"},{include:"#double-quoted"},{include:"#single-quoted"},{include:"#interpolation"},{include:"#curly-brackets"},{include:"#variable"},{include:"#rgb-value"},{include:"#numeric"},{include:"#unit"},{include:"#module"},{match:"--.+?(?=\\))",name:"variable.css"},{match:"[\\w-]*\\(",name:"entity.name.function"},{match:"\\)",name:"entity.name.function.close"},{include:"#flag"},{include:"#comma"},{include:"#semicolon"},{include:"#function"},{include:"#function-content"},{include:"#operator"},{include:"#parent-selector"},{include:"#property-value"}]},{include:"#rgb-value"},{include:"#function"},{include:"#function-content"},{begin:"(?<=})(?!\\n|\\(|\\)|[a-zA-Z0-9_-]+:)",end:"\\s|(?=,|\\.|\\[|\\)|\\n)",name:"entity.name.tag.css.sass",patterns:[{include:"#interpolation"},{include:"#pseudo-class"}]},{include:"#operator"},{match:"[a-z-]+((?=:|#{))",name:"support.type.property-name.css.sass.prop.name"},{include:"#reserved-words"},{include:"#property-value"}],repository:{colon:{match:":",name:"meta.property-list.css.sass.colon"},comma:{match:"\\band\\b|\\bor\\b|,",name:"comment.punctuation.comma.sass"},"comment-param":{match:"\\@(\\w+)",name:"storage.type.class.jsdoc"},"comment-tag":{begin:"(?<={{)",end:"(?=}})",name:"comment.tag.sass"},"curly-brackets":{match:"{|}",name:"invalid"},dotdotdot:{match:"\\.\\.\\.",name:"variable.other"},"double-quoted":{begin:'"',end:'"',name:"string.quoted.double.css.sass",patterns:[{include:"#quoted-interpolation"}]},"double-slash":{begin:"//",end:"$\\n?",name:"comment.line.sass",patterns:[{include:"#comment-tag"}]},flag:{match:"!(important|default|optional|global)",name:"keyword.other.important.css.sass"},function:{match:"(?<=[\\s|(|,|:])(?!url|format|attr)[a-zA-Z0-9_-][\\w-]*(?=\\()",name:"support.function.name.sass"},"function-content":{begin:"(?<=url\\(|format\\(|attr\\()",end:".(?=\\))",name:"string.quoted.double.css.sass"},"import-quotes":{match:"[\"']?\\.{0,2}[\\w/]+[\"']?",name:"constant.character.css.sass"},interpolation:{begin:"#{",end:"}",name:"support.function.interpolation.sass",patterns:[{include:"#variable"},{include:"#numeric"},{include:"#operator"},{include:"#unit"},{include:"#comma"},{include:"#double-quoted"},{include:"#single-quoted"}]},module:{captures:{1:{name:"constant.character.module.name"},2:{name:"constant.numeric.module.dot"}},match:"([\\w-]+?)(\\.)",name:"constant.character.module"},numeric:{match:"(-|\\.)?\\d+(\\.\\d+)?",name:"constant.numeric.css.sass"},operator:{match:"\\+|\\s-\\s|\\s-(?=\\$)|(?<=\\()-(?=\\$)|\\s-(?=\\()|\\*|/|%|=|!|<|>|~",name:"keyword.operator.sass"},"parent-selector":{match:"&",name:"entity.name.tag.css.sass"},"parenthesis-close":{match:"\\)",name:"entity.name.function.parenthesis.close"},"parenthesis-open":{match:"\\(",name:"entity.name.function.parenthesis.open"},"placeholder-selector":{begin:"(?<!\\d)%(?!\\d)",end:"$\\n?|\\s",name:"entity.other.inherited-class.placeholder-selector.css.sass"},"property-value":{match:"[a-zA-Z0-9_-]+",name:"meta.property-value.css.sass support.constant.property-value.css.sass"},"pseudo-class":{match:":[a-z:-]+",name:"entity.other.attribute-name.pseudo-class.css.sass"},"quoted-interpolation":{begin:"#{",end:"}",name:"support.function.interpolation.sass",patterns:[{include:"#variable"},{include:"#numeric"},{include:"#operator"},{include:"#unit"},{include:"#comma"}]},"reserved-words":{match:"\\b(false|from|in|not|null|through|to|true)\\b",name:"support.type.property-name.css.sass"},"rgb-value":{match:"(#)([0-9a-fA-F]{3,4}|[0-9a-fA-F]{6}|[0-9a-fA-F]{8})\\b",name:"constant.language.color.rgb-value.css.sass"},semicolon:{match:";",name:"invalid"},"single-quoted":{begin:"'",end:"'",name:"string.quoted.single.css.sass",patterns:[{include:"#quoted-interpolation"}]},unit:{match:"(?<=[\\d]|})(ch|cm|deg|dpcm|dpi|dppx|em|ex|grad|Hz|in|kHz|mm|ms|pc|pt|px|rad|rem|s|turn|vh|vmax|vmin|vw|fr|%)",name:"keyword.control.unit.css.sass"},variable:{match:"\\$[a-zA-Z0-9_-]+",name:"variable.other.value"},"variable-root":{match:"\\$[a-zA-Z0-9_-]+",name:"variable.other.root"}},scopeName:"source.sass"})]},99056:(e,n,t)=>{t.r(n),t.d(n,{default:()=>c});var a=t(76084);let i=Object.freeze({displayName:"Elm",fileTypes:["elm"],name:"elm",patterns:[{include:"#import"},{include:"#module"},{include:"#debug"},{include:"#comments"},{match:"\\b(_)\\b",name:"keyword.unused.elm"},{include:"#type-signature"},{include:"#type-declaration"},{include:"#type-alias-declaration"},{include:"#string-triple"},{include:"#string-quote"},{include:"#char"},{comment:"Floats are always decimal",match:"\\b(\\d+\\.\\d+([eE][+-]?\\d+)?|\\d+[eE][+-]?\\d+)\\b",name:"constant.numeric.float.elm"},{match:"\\b(\\d+)\\b",name:"constant.numeric.elm"},{match:"\\b(0x[0-9a-fA-F]+)\\b",name:"constant.numeric.elm"},{include:"#glsl"},{include:"#record-prefix"},{include:"#module-prefix"},{include:"#constructor"},{captures:{1:{name:"punctuation.bracket.elm"},2:{name:"record.name.elm"},3:{name:"keyword.pipe.elm"},4:{name:"entity.name.record.field.elm"}},match:"(\\{)\\s+([a-z]\\w*)\\s+(\\|)\\s+([a-z]\\w*)",name:"meta.record.field.update.elm"},{captures:{1:{name:"keyword.pipe.elm"},2:{name:"entity.name.record.field.elm"},3:{name:"keyword.operator.assignment.elm"}},match:"(\\|)\\s+([a-z]\\w*)\\s+(=)",name:"meta.record.field.update.elm"},{captures:{1:{name:"punctuation.bracket.elm"},2:{name:"record.name.elm"}},match:"(\\{)\\s+([a-z]\\w*)\\s+$",name:"meta.record.field.update.elm"},{captures:{1:{name:"punctuation.bracket.elm"},2:{name:"entity.name.record.field.elm"},3:{name:"keyword.operator.assignment.elm"}},match:"(\\{)\\s+([a-z]\\w*)\\s+(=)",name:"meta.record.field.elm"},{captures:{1:{name:"punctuation.separator.comma.elm"},2:{name:"entity.name.record.field.elm"},3:{name:"keyword.operator.assignment.elm"}},match:"(,)\\s+([a-z]\\w*)\\s+(=)",name:"meta.record.field.elm"},{match:"(\\}|\\{)",name:"punctuation.bracket.elm"},{include:"#unit"},{include:"#comma"},{include:"#parens"},{match:"(->)",name:"keyword.operator.arrow.elm"},{include:"#infix_op"},{match:"(=|:|\\||\\\\)",name:"keyword.other.elm"},{match:"\\b(type|as|port|exposing|alias|infixl|infixr|infix)\\s+",name:"keyword.other.elm"},{match:"\\b(if|then|else|case|of|let|in)\\s+",name:"keyword.control.elm"},{include:"#record-accessor"},{include:"#top_level_value"},{include:"#value"},{include:"#period"},{include:"#square_brackets"}],repository:{block_comment:{applyEndPatternLast:1,begin:"\\{-(?!#)",captures:{0:{name:"punctuation.definition.comment.elm"}},end:"-\\}",name:"comment.block.elm",patterns:[{include:"#block_comment"}]},char:{begin:"'",beginCaptures:{0:{name:"punctuation.definition.char.begin.elm"}},end:"'",endCaptures:{0:{name:"punctuation.definition.char.end.elm"}},name:"string.quoted.single.elm",patterns:[{match:"\\\\(NUL|SOH|STX|ETX|EOT|ENQ|ACK|BEL|BS|HT|LF|VT|FF|CR|SO|SI|DLE|DC1|DC2|DC3|DC4|NAK|SYN|ETB|CAN|EM|SUB|ESC|FS|GS|RS|US|SP|DEL|[abfnrtv\\\\\\\"'\\&]|x[0-9a-fA-F]{1,5})",name:"constant.character.escape.elm"},{match:"\\^[A-Z@\\[\\]\\\\\\^_]",name:"constant.character.escape.control.elm"}]},comma:{match:"(,)",name:"punctuation.separator.comma.elm"},comments:{patterns:[{begin:"--",captures:{1:{name:"punctuation.definition.comment.elm"}},end:"$",name:"comment.line.double-dash.elm"},{include:"#block_comment"}]},constructor:{match:"\\b[A-Z]\\w*\\b",name:"constant.type-constructor.elm"},debug:{match:"\\b(Debug)\\b",name:"invalid.illegal.debug.elm"},glsl:{begin:"(\\[)(glsl)(\\|)",beginCaptures:{1:{name:"entity.glsl.bracket.elm"},2:{name:"entity.glsl.name.elm"},3:{name:"entity.glsl.bracket.elm"}},end:"(\\|\\])",endCaptures:{1:{name:"entity.glsl.bracket.elm"}},name:"meta.embedded.block.glsl",patterns:[{include:"source.glsl"}]},import:{begin:"^\\b(import)\\s+",beginCaptures:{1:{name:"keyword.control.import.elm"}},end:"\\n(?!\\s)",name:"meta.import.elm",patterns:[{match:"(as|exposing)",name:"keyword.control.elm"},{include:"#module_chunk"},{include:"#period"},{match:"\\s+",name:"punctuation.spaces.elm"},{include:"#module-exports"}]},infix_op:{match:"(</>|<\\?>|<\\||<=|\\|\\||&&|>=|\\|>|\\|=|\\|\\.|\\+\\+|::|/=|==|//|>>|<<|<|>|\\^|\\+|-|/|\\*)",name:"keyword.operator.elm"},module:{begin:"^\\b((port |effect )?module)\\s+",beginCaptures:{1:{name:"keyword.other.elm"}},end:"\\n(?!\\s)",endCaptures:{1:{name:"keyword.other.elm"}},name:"meta.declaration.module.elm",patterns:[{include:"#module_chunk"},{include:"#period"},{match:"(exposing)",name:"keyword.other.elm"},{match:"\\s+",name:"punctuation.spaces.elm"},{include:"#module-exports"}]},"module-exports":{begin:"(\\()",beginCaptures:{1:{name:"punctuation.parens.module-export.elm"}},end:"(\\))",endCaptures:{1:{name:"punctuation.parens.module-export.elm"}},name:"meta.declaration.exports.elm",patterns:[{match:"\\b[a-z][a-zA-Z_'0-9]*",name:"entity.name.function.elm"},{match:"\\b[A-Z][A-Za-z_'0-9]*",name:"storage.type.elm"},{match:",",name:"punctuation.separator.comma.elm"},{match:"\\s+",name:"punctuation.spaces.elm"},{include:"#comma"},{match:"\\(\\.\\.\\)",name:"punctuation.parens.ellipses.elm"},{match:"\\.\\.",name:"punctuation.parens.ellipses.elm"},{include:"#infix_op"},{comment:"So named because I don't know what to call this.",match:"\\(.*?\\)",name:"meta.other.unknown.elm"}]},"module-prefix":{captures:{1:{name:"support.module.elm"},2:{name:"keyword.other.period.elm"}},match:"([A-Z]\\w*)(\\.)",name:"meta.module.name.elm"},module_chunk:{match:"[A-Z]\\w*",name:"support.module.elm"},parens:{match:"(\\(|\\))",name:"punctuation.parens.elm"},period:{match:"[.]",name:"keyword.other.period.elm"},"record-accessor":{captures:{1:{name:"keyword.other.period.elm"},2:{name:"entity.name.record.field.accessor.elm"}},match:"(\\.)([a-z]\\w*)",name:"meta.record.accessor"},"record-prefix":{captures:{1:{name:"record.name.elm"},2:{name:"keyword.other.period.elm"},3:{name:"entity.name.record.field.accessor.elm"}},match:"([a-z]\\w*)(\\.)([a-z]\\w*)",name:"record.accessor.elm"},square_brackets:{match:"[\\[\\]]",name:"punctuation.definition.list.elm"},"string-quote":{begin:'"',beginCaptures:{0:{name:"punctuation.definition.string.begin.elm"}},end:'"',endCaptures:{0:{name:"punctuation.definition.string.end.elm"}},name:"string.quoted.double.elm",patterns:[{match:"\\\\(NUL|SOH|STX|ETX|EOT|ENQ|ACK|BEL|BS|HT|LF|VT|FF|CR|SO|SI|DLE|DC1|DC2|DC3|DC4|NAK|SYN|ETB|CAN|EM|SUB|ESC|FS|GS|RS|US|SP|DEL|[abfnrtv\\\\\\\"'\\&]|x[0-9a-fA-F]{1,5})",name:"constant.character.escape.elm"},{match:"\\^[A-Z@\\[\\]\\\\\\^_]",name:"constant.character.escape.control.elm"}]},"string-triple":{begin:'"""',beginCaptures:{0:{name:"punctuation.definition.string.begin.elm"}},end:'"""',endCaptures:{0:{name:"punctuation.definition.string.end.elm"}},name:"string.quoted.triple.elm",patterns:[{match:"\\\\(NUL|SOH|STX|ETX|EOT|ENQ|ACK|BEL|BS|HT|LF|VT|FF|CR|SO|SI|DLE|DC1|DC2|DC3|DC4|NAK|SYN|ETB|CAN|EM|SUB|ESC|FS|GS|RS|US|SP|DEL|[abfnrtv\\\\\\\"'\\&]|x[0-9a-fA-F]{1,5})",name:"constant.character.escape.elm"},{match:"\\^[A-Z@\\[\\]\\\\\\^_]",name:"constant.character.escape.control.elm"}]},top_level_value:{match:"^[a-z]\\w*\\b",name:"entity.name.function.top_level.elm"},"type-alias-declaration":{begin:"^(type\\s+)(alias\\s+)([A-Z][a-zA-Z0-9_']*)\\s+",beginCaptures:{1:{name:"keyword.type.elm"},2:{name:"keyword.type-alias.elm"},3:{name:"storage.type.elm"}},end:"^(?=\\S)",name:"meta.function.type-declaration.elm",patterns:[{match:"\\n\\s+",name:"punctuation.spaces.elm"},{match:"=",name:"keyword.operator.assignment.elm"},{include:"#module-prefix"},{match:"\\b[A-Z]\\w*\\b",name:"storage.type.elm"},{match:"\\b[a-z]\\w*\\b",name:"variable.type.elm"},{include:"#comments"},{include:"#type-record"}]},"type-declaration":{begin:"^(type\\s+)([A-Z][a-zA-Z0-9_']*)\\s+",beginCaptures:{1:{name:"keyword.type.elm"},2:{name:"storage.type.elm"}},end:"^(?=\\S)",name:"meta.function.type-declaration.elm",patterns:[{captures:{1:{name:"constant.type-constructor.elm"}},match:"^\\s*([A-Z]\\w*)\\b",name:"meta.record.field.elm"},{match:"\\s+",name:"punctuation.spaces.elm"},{captures:{1:{name:"keyword.operator.assignment.elm"},2:{name:"constant.type-constructor.elm"}},match:"(=|\\|)\\s+([A-Z]\\w*)\\b",name:"meta.record.field.elm"},{match:"=",name:"keyword.operator.assignment.elm"},{match:"->",name:"keyword.operator.arrow.elm"},{include:"#module-prefix"},{match:"\\b[a-z]\\w*\\b",name:"variable.type.elm"},{match:"\\b[A-Z]\\w*\\b",name:"storage.type.elm"},{include:"#comments"},{include:"#type-record"}]},"type-record":{begin:"(\\{)",beginCaptures:{1:{name:"punctuation.section.braces.begin"}},end:"(\\})",endCaptures:{1:{name:"punctuation.section.braces.end"}},name:"meta.function.type-record.elm",patterns:[{match:"\\s+",name:"punctuation.spaces.elm"},{match:"->",name:"keyword.operator.arrow.elm"},{captures:{1:{name:"entity.name.record.field.elm"},2:{name:"keyword.other.elm"}},match:"([a-z]\\w*)\\s+(:)",name:"meta.record.field.elm"},{match:"\\,",name:"punctuation.separator.comma.elm"},{include:"#module-prefix"},{match:"\\b[a-z]\\w*\\b",name:"variable.type.elm"},{match:"\\b[A-Z]\\w*\\b",name:"storage.type.elm"},{include:"#comments"},{include:"#type-record"}]},"type-signature":{begin:"^(port\\s+)?([a-z_][a-zA-Z0-9_']*)\\s+(:)",beginCaptures:{1:{name:"keyword.other.port.elm"},2:{name:"entity.name.function.elm"},3:{name:"keyword.other.colon.elm"}},end:"((^(?=[a-z]))|^$)",name:"meta.function.type-declaration.elm",patterns:[{include:"#type-signature-chunk"}]},"type-signature-chunk":{patterns:[{match:"->",name:"keyword.operator.arrow.elm"},{match:"\\s+",name:"punctuation.spaces.elm"},{include:"#module-prefix"},{match:"\\b[a-z]\\w*\\b",name:"variable.type.elm"},{match:"\\b[A-Z]\\w*\\b",name:"storage.type.elm"},{match:"\\(\\)",name:"constant.unit.elm"},{include:"#comma"},{include:"#parens"},{include:"#comments"},{include:"#type-record"}]},unit:{match:"\\(\\)",name:"constant.unit.elm"},value:{match:"\\b[a-z]\\w*\\b",name:"meta.value.elm"}},scopeName:"source.elm",embeddedLangs:["glsl"]});var c=[...a.default,i]}};