(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[285],{24901:(e,n,r)=>{Promise.resolve().then(r.bind(r,43432)),Promise.resolve().then(r.t.bind(r,35685,23)),Promise.resolve().then(r.bind(r,13957)),Promise.resolve().then(r.bind(r,71497))},71497:(e,n,r)=>{"use strict";r.r(n),r.d(n,{ClientPump:()=>i}),r(82920);var t=r(50628);let l="__alias__";var u=!1,a=new Set,s=new Map,o=new Map,i=e=>{var n;let{children:i,rawQueries:c,pumpEndpoint:p,pumpToken:f,initialState:d,initialResolvedChildren:h,apiVersion:v,previewRef:m}=e,b=t.useRef(f),[w,_]=t.useState(d),y=t.useRef(d);y.current=d;let[g,k]=t.useState(m),P=t.useRef(g);P.current=g;let E=t.useCallback(async()=>{let e,n,r,t=await Promise.all(c.map(async(t,u)=>{var a,i;if(!b.current)return console.warn("No pump token found. Skipping query."),null;let c=JSON.stringify(t),f=c+g,d=o.get(c)||(null==(i=y.current)||null==(a=i.responseHashes)?void 0:a[u])||"";if(s.has(f)){let t=s.get(f);if(performance.now()-t.start<32){let l=await t.response;return l?(l.newPumpToken&&(e=l.newPumpToken),n=l.pusherData,r=l.spaceID,l):null}}let h=fetch(p,{cache:"no-store",method:"POST",headers:{"content-type":"application/json","x-basehub-api-version":v,"x-basehub-ref":g},body:JSON.stringify({...t,pumpToken:b.current,lastResponseHash:d})}).then(async e=>{let{data:n=null,errors:r=null,newPumpToken:t,spaceID:u,pusherData:a,responseHash:s}=await e.json();return o.set(c,s),{data:function e(n){if("object"!=typeof n||null===n)return n;if(Array.isArray(n))return n.map(n=>e(n));let r={};for(let[t,u]of Object.entries(n))if(t.includes(l)){let[n,...a]=t.split(l);r[a.join(l)]=e(u)}else r[t]=e(u);return r}(n),spaceID:u,pusherData:a,newPumpToken:t,errors:r,responseHash:s,changed:d!==s}}).catch(e=>{console.error("Error fetching data from the BaseHub Draft API:\n              \n".concat(JSON.stringify(e,null,2),"\n              \nContact <EMAIL> for help."))});s.set(f,{start:performance.now(),response:h});let m=await h;return m?(m.newPumpToken&&(e=m.newPumpToken),n=m.pusherData,r=m.spaceID,m):null}));if(t.some(e=>null==e?void 0:e.changed)){if(!n||!r)return;_(e=>n&&r?{data:t.map((n,r)=>{var t,l,u;return(null==n?void 0:n.changed)?null!=(u=null==n?void 0:n.data)?u:null:null!=(l=null==e||null==(t=e.data)?void 0:t[r])?l:null}),errors:t.map((n,r)=>{var t,l,u;return(null==n?void 0:n.changed)?null!=(u=null==n?void 0:n.errors)?u:null:null!=(l=null==e||null==(t=e.errors)?void 0:t[r])?l:null}),responseHashes:t.map(e=>{var n;return null!=(n=null==e?void 0:e.responseHash)?n:""}),pusherData:n,spaceID:r}:e)}e&&(b.current=e)},[p,c,v,g]);t.useRef(null),t.useEffect(()=>{var e;if(!(null==w?void 0:w.errors))return;let n=null==(e=w.errors[0])?void 0:e[0];n&&console.error("Error fetching data from the BaseHub Draft API: ".concat(n.message).concat(n.path?" at ".concat(n.path.join(".")):""))},[null==w?void 0:w.errors]),t.useEffect(()=>{function e(){E()}return e(),a.add(e),()=>{a.delete(e)}},[E]);let[D,S]=t.useState(null),T=null==w||null==(n=w.pusherData)?void 0:n.channel_key,O=null==w?void 0:w.pusherData.app_key,N=null==w?void 0:w.pusherData.cluster;t.useEffect(()=>{if(!u&&O&&N)return u=!0,r.e(71).then(r.bind(r,9071)).then(e=>{S(new e.default(O,{cluster:N}))}).catch(e=>{console.log("error importing pusher"),console.error(e)}),()=>{u=!1}},[O,N]),t.useEffect(()=>{if(!T||!D)return;let e=D.subscribe(T);return e.bind("poke",e=>{var n;(null==e||null==(n=e.mutatedEntryTypes)?void 0:n.includes("block"))&&e.branch===P.current&&a.forEach(e=>e())}),()=>{e.unsubscribe()}},[D,T]),t.useEffect(()=>{function e(){let e=window.__bshb_ref;e&&"string"==typeof e&&k(e)}return e(),window.addEventListener("__bshb_ref_changed",e),()=>{window.removeEventListener("__bshb_ref_changed",e)}},[]);let j=t.useMemo(()=>null==w?void 0:w.data.map((e,n)=>{var r,t;return null!=(t=null!=e?e:null==d||null==(r=d.data)?void 0:r[n])?t:null}),[null==d?void 0:d.data,null==w?void 0:w.data]),[I,x]=t.useState("function"==typeof i?h:i);return t.useEffect(()=>{if(j)if("function"==typeof i){let e=i(j);e instanceof Promise?e.then(x):x(e)}else x(i)},[i,j]),null!=I?I:h}},82920:(e,n,r)=>{"use strict";r.d(n,{P:()=>l});var t=Object.getOwnPropertyNames,l=(e,n)=>function(){return n||(0,e[t(e)[0]])((n={exports:{}}).exports,n),n.exports}}},e=>{var n=n=>e(e.s=n);e.O(0,[213,685,913,499,358],()=>n(24901)),_N_E=e.O()}]);